<?php

namespace App\Mail;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendMailSupport extends Mailable
{
    use Queueable, SerializesModels;

    public $title;
    public $description;
    public $content;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($title, $description, $content)
    {
        //
        $this->title = $title;
        $this->description = $description;
        $this->content = $content;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('email/mail_support', [
            'title' => $this->title,
            'description' => $this->description,
            'content' => $this->content,

        ])
            ->from('<EMAIL>', $this->title)
            ->subject($this->description);
    }
}
