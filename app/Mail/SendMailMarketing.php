<?php

namespace App\Mail;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendMailMarketing extends Mailable
{
    use Queueable, SerializesModels;

    public $name;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($name)
    {
        //
        $this->name = $name;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('marketing_email_web', ['VERIFICATION_CODE' => $this->name, 'body' => 'green', 'slot' => 'sds'])
             ->from('<EMAIL>', 'XIN CHÀO')
            ->subject("XIN CHÀO $this->name gửi từ DoPage.vn Thiế<PERSON>ế Website Landing Page chuyên nghiệp ");
    }
}

