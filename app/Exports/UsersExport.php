<?php

namespace App\Exports;

use App\Models\RenewalHistory;
use App\Models\User;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithEvents;

class UsersExport implements FromQuery, WithHeadings, WithMapping, WithColumnFormatting, WithColumnWidths, WithEvents
{

    protected $startDate;
    protected $endDate;
    protected $agentId;
    protected $type;

    function __construct($startDate = null, $endDate = null, $agentId, $type)
    {
        $this->startDate   = $startDate;
        $this->endDate     = $endDate;
        $this->agentId   = $agentId;
        $this->type     = $type;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Tô màu nền và định dạng cột A1 đến I1
                $event->sheet->getDelegate()->getStyle('A1:L1')->applyFromArray([
                    'fill' => [
                        'fillType'   => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => [
                            'argb' => 'FF103178', // Mã màu HEX
                        ],
                    ],
                    'font' => [
                        'color' => ['rgb' => 'FFFFFF'], // Màu chữ
                    ],
                ]);
            },
        ];
    }

    public function headings(): array
    {
        return [
            "Email",
            "Name",
            "Created at",
            "IP",
            "Expiry alldata",
            "Expiry autodata",
            "Expiry haynespro",
            "Expiry haynespro truck",
            "Expiry identifix",
            "Expiry mitchell prodemand",
            'Expiry partslink24',
            'Expiry KdsGds',
            'Expiry ETKA',
            'Expiry TecDoc',
            'Expiry FordTIS',
            'Expiry Toyota TIS',
        ];
    }

    public function columnFormats(): array
    {
        return [
            // 'D' => NumberFormat::FORMAT_TEXT,
            // 'E' => NumberFormat::FORMAT_TEXT,
            // 'F' => NumberFormat::FORMAT_TEXT,
            // 'H' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 30,
            'B' => 30,
            'C' => 30,
            'D' => 30,
            'E' => 30,
            'F' => 30,
            'G' => 30,
            'H' => 30,
            'I' => 30,
            'J' => 30,
            'K' => 30,
            'L' => 30,
            'M' => 30,
            'N' => 30,
            'O' => 30,
            'P' => 30,
            'Q' => 30,
        ];
    }

    public function map($user): array
    {
        return [
            $user->email ?? "",
            $user->name ?? "",
            $user->created_at ?? "",
            $user->ip_using ?? "",
            $user->expiry_alldata ?? "",
            $user->expiry_autodata ?? "",
            $user->expiry_haynespro ?? "",
            $user->expiry_haynespro_truck ?? "",
            $user->expiry_identifix ?? "",
            $user->expiry_mitchell_prodemand ?? "",
            $user->expiry_partslink24 ?? "",
            $user->expiry_kdsgds ?? "",
            $user->expiry_etka ?? "",
            $user->expiry_tecdoc ?? "",
            $user->expiry_ford_pts ?? "",
            $user->expiry_toyota_tis ?? "",
        ];
    }

    public function query()
    {


        $users = User::query();
        if ($this->startDate && $this->endDate) {
            return $users
                ->whereDate('created_at', '>=', $this->startDate)
                ->whereDate('created_at', '<=', $this->endDate)
                ->orderBy('id', 'desc')

                ->when($this->type == 0 && $this->agentId != null, function ($query) {
                    $query->where('of_agency_id', $this->agentId);
                });
        }


        return $users;
    }
}
