<?php

namespace App\Exports;

use App\Models\RenewalHistory;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithEvents;

class RenewalHistoryExport implements FromQuery, WithHeadings, WithMapping, WithColumnFormatting, WithColumnWidths, WithEvents
{

    protected $startDate;
    protected $endDate;
    protected $agentId;
    protected $type;

    function __construct($startDate = null, $endDate = null, $agentId, $type)
    {
        $this->startDate   = $startDate;
        $this->endDate     = $endDate;
        $this->agentId   = $agentId;
        $this->type     = $type;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Tô màu nền và định dạng cột A1 đến I1
                $event->sheet->getDelegate()->getStyle('A1:I1')->applyFromArray([
                    'fill' => [
                        'fillType'   => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => [
                            'argb' => 'FF103178', // Mã màu HEX
                        ],
                    ],
                    'font' => [
                        'color' => ['rgb' => 'FFFFFF'], // Màu chữ
                    ],
                ]);
            },
        ];
    }

    public function headings(): array
    {
        return ["Email", "Name", "Service",  "Month", "Before", "After", "Money", "Time", "Note"];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_TEXT,
            'E' => NumberFormat::FORMAT_TEXT,
            'F' => NumberFormat::FORMAT_TEXT,
            'H' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 35,
            'B' => 22,
            'C' => 22,
            'E' => 22,
            'F' => 22,
            'H' => 22,
            'I' => 50,
        ];
    }

    public function map($renewalHistory): array
    {
        return [
            $renewalHistory->user->email ?? "",
            $renewalHistory->user->name ?? "",
            $renewalHistory->service ?? "",
            $renewalHistory->month ?? "",
            $renewalHistory->before_expiration_date ?? "",
            $renewalHistory->after_expiration_date ?? "",
            $renewalHistory->paid ?? "",
            $renewalHistory->created_at ?? "",
            $renewalHistory->references_value ?? "",
        ];
    }

    public function query()
    {

        $renewalHistory = RenewalHistory::query();
        if ($this->startDate && $this->endDate) {
            return $renewalHistory
                ->join('users', function ($join) {
                    $join->on('users.id', '=', 'renewal_histories.user_id');
                })->whereDate('renewal_histories.created_at', '>=', $this->startDate)
                ->whereDate('renewal_histories.created_at', '<=', $this->endDate)
                ->orderBy('renewal_histories.id', 'desc')

                ->when($this->type == 0 && $this->agentId != null, function ($query) {
                    $query->where('users.of_agency_id', $this->agentId);
                })
                ->select('renewal_histories.*')
                ->with('user');
        }


        return $renewalHistory;
    }
}
