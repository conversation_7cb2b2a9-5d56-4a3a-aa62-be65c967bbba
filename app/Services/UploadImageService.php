<?php

namespace App\Services;

use App\Models\ImgurImage;
use App\Models\MsgCode;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Client as GuzzleClient;

class UploadImageService
{


    const END_POINT = 'https://data1.drcar.la/public/api/image-upload';
    const END_POINT_TRY = 'https://data1.drcar.la/public/api/image-upload';

    public static function uploadImage($imagePath, $type)
    {

        function getLink($url, $imagePath, $type )
        {
            if ($imagePath == null) {

                throw new Exception(MsgCode::UNABLE_TO_FIND_THE_UPLOAD_IMAGE[1]);
            }
            $client = new GuzzleClient();

            try {
                $response = $client->request(
                    'POST',
                    $url,
                    [
                        'multipart' => [
                            [
                                'name'     => 'image',
                                'contents' => @file_get_contents($imagePath),
                                'Content-type' => 'multipart/form-data',
                                'filename' => 'dsadsad.png',
                            ],
                            [
                                'name'     => 'type',
                                'contents' =>  $type
                            ]
                        ],
                    ]

                );


                if ($response->getStatusCode() != 200) {

                    throw new Exception(MsgCode::CANNOT_POST_PICTURES[1]);
                }


                $body = (string) $response->getBody();
                $jsonResponse = json_decode($body);


                return $jsonResponse->link;
            } catch (Exception $e) {

                throw new Exception(MsgCode::CANNOT_POST_PICTURES[1]);
            }
        }
       
        if($type == null) {
            $type = "drcar";
        }

        try {
            $link1 = getLink(UploadImageService::END_POINT, $imagePath, $type );
            return $link1;
        } catch (Exception $e) {

            try {
                $link2 = getLink(UploadImageService::END_POINT_TRY, $imagePath, $type );

                return  $link2;
            } catch (Exception $e) {

                throw new Exception($e->getMessage());
            }
        }
    }


    // const END_POINT = 'https://data2.karavui.com/public/api/image-upload';
    //const END_POINT = 'https://api.imgbb.com/1/upload?expiration=15552000&key=ba3f513080461f442e50df5e3991ce54';

    public static function uploadImage2($imagePath)
    {

        //     function save_record_image($image,$name = null){
        //         $API_KEY = 'ba3f513080461f442e50df5e3991ce54';
        //         $ch = curl_init();
        //         curl_setopt($ch, CURLOPT_URL, 'https://api.imgbb.com/1/upload?expiration=15552000&key='.$API_KEY);
        //         curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        //         curl_setopt($ch, CURLOPT_POST, 1);
        //        // curl_setopt($ch, CURLOPT_SAFE_UPLOAD, false);
        //         // $extension = pathinfo($image['name'],PATHINFO_EXTENSION);
        //         // $file_name = ($name)? $name.'.'.$extension : $image['name'] ;
        //         $data = array('image' => base64_encode($image));
        //         curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        //         $result = curl_exec($ch);
        //         if (curl_errno($ch)) {
        //             return 'Error:' . curl_error($ch);
        //         }else{
        //             return json_decode($result, true);
        //         }
        //         curl_close($ch);
        //     }



        //     if ($imagePath == null) {
        //         return response()->json([
        //             'code' => 400,
        //             'success' => false,
        //             'msg_code' => MsgCode::UNABLE_TO_FIND_THE_UPLOAD_IMAGE[0],
        //             'msg' => MsgCode::UNABLE_TO_FIND_THE_UPLOAD_IMAGE[1],
        //         ], 400);
        //     }

        //     $client = new GuzzleClient();


        //     try {
        //         $response = $client->request(
        //             'POST',
        //             UploadImageService::END_POINT,
        //             [
        //                 'multipart' => [
        //                     [
        //                         'name'     => 'image',
        //                         'contents' => @file_get_contents($imagePath),
        //                         'Content-type' => 'multipart/form-data',
        //                         'filename' => 'dsadsad.png',
        //                     ],
        //                 ],
        //             ]

        //         );
        //         // $response = $client->request(
        //         //     'POST',
        //         //     UploadImageService::END_POINT,
        //         //     [
        //         //         // 'multipart' => [
        //         //         //     [
        //         //         //         'name'     => 'image',
        //         //         //         'contents' => @file_get_contents($imagePath),
        //         //         //         'Content-type' => 'multipart/form-data',
        //         //         //         'filename' => 'dsadsad.png',
        //         //         //     ],
        //         //         // ],
        //         //         'form_params' => [
        //         //             'image' => base64_encode(file_get_contents($imagePath)),
        //         //             'expiration' => 15552000
        //         //         ]
        //         //     ]

        //         // );

        //         if ($response->getStatusCode() != 200) {
        //             return MsgCode::CANNOT_POST_PICTURES;
        //         }

        //         $body = (string) $response->getBody();


        //         $jsonResponse = json_decode($body);


        //         return $jsonResponse->data->url;


        // //   dd( save_record_image(file_get_contents($imagePath)));

        //     } catch (\GuzzleHttp\Exception\RequestException $e) {

        //         return MsgCode::CANNOT_POST_PICTURES;
        //     }
    }
}
