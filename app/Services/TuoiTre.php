<?php

namespace App\Services;

use App\Helper\Helper;
use App\Models\Post;
use Carbon\Carbon;
use KubAT\PhpSimple\HtmlDomParser;

class TuoiTre
{
    static public function get_and_save()
    {

   
        $pages = [
            "thời sự" => "https://tuoitre.vn/thoi-su.htm",
            "thế giới" => "https://tuoitre.vn/the-gioi.htm",
            "pháp luật" => "https://tuoitre.vn/phap-luat.htm",
            "kinh doanh" => "https://tuoitre.vn/kinh-doanh.htm",
            "công nghệ" => "https://congnghe.tuoitre.vn/",
            "du lịch" => "https://dulich.tuoitre.vn/di-choi.htm",
            "nhịp sống trẻ" => "https://tuoitre.vn/nhip-song-tre/trang-1.htm",
            "văn hóa" => "https://tuoitre.vn/van-hoa.htm",
            "giải trí" => "https://tuoitre.vn/giai-tri.htm",
            "thể thao" => "https://tuoitre.vn/the-thao/trang-1.htm",
            "giáo dục" => "https://tuoitre.vn/giao-duc.htm",
            "khoa học" => "https://tuoitre.vn/khoa-hoc.htm",
            "sức khỏe" => "https://tuoitre.vn/suc-khoe.htm",
            "giả thật" => "https://tuoitre.vn/gia-that.htm",
            "bạn đọc" => "https://tuoitre.vn/ban-doc-lam-bao.htm"
        ];

        foreach ($pages as $key => $value) {

            $data = TuoiTre::get_all_list_in_page($key, $value);
            if (count($data) > 0) {
                foreach ($data as $itemData) {
                    $postExists = Post::where('from_link', $itemData['from_link'])->first();
                    if ($postExists == null) {
                        Post::create([

                            "title" => $itemData['title'] ?? "",
                            "summary" =>     $itemData['summary'] ?? "",
                            "from_link"    => $itemData['from_link'] ?? "",
                            "thumbnai" => $itemData['thumbnai'] ?? "",
                            "time_get" =>     $itemData['time_get'] ?? "",
                            "time_write" => $itemData['time_write'] ?? "",
                            "category" => $itemData['category'] ?? "",
                            "from"    => $itemData['from'] ?? "",
                            "status" => 0,
                            "content" =>  $itemData['content'] ?? "",

                        ]);
                    }
                }
            }
        }
    }
    static public function get_all_list_in_page($category, $link)
    {
        $urlList = $link;
        $client = new \GuzzleHttp\Client();
        $response = $client->request('GET', $urlList);
        $html = $response->getBody();
        $html = HtmlDomParser::str_get_html($html);

        $data = [];

        $link = "";
        if ($category == "du lịch") {
            $link = "https://dulich.tuoitre.vn";
            $container_list = $html->find('.newest') ?? [];
            if (count($container_list) == 0) {
                return;
            }
            $container_list = $html->find('.newest')[0];
        } else {
            $link = "https://tuoitre.vn";
            $container_list = $html->find('.list-news-content') ?? [];
            if (count($container_list) == 0) {
                return;
            }
            $container_list = $html->find('.list-news-content')[0];
        }



        $item_news =  $container_list->find("li");

        foreach ($item_news  as $item_new) {
            $title = $item_new->find('h3 > a')[0]->innertext;



            $summary = $item_new->find('div > p') ? $item_new->find('div > p')[0]->innertext : "";
           // $thumbnai = $item_new->find('a > img') ? $item_new->find('a > img')[0]->src : "";

            $from_link = $item_new->find('h3 > a') ?  $item_new->find('h3 > a')[0]->href : "";

            $time_get = Helper::getTimeNowString();


            $from_full = $link . $from_link;
            $content =  TuoiTre::get_content_one_post($link . $from_link);

            if($content == null || count($content) < 3) continue;
            $time_write = Carbon::parse($content[1]);
            $thumbnai =  $content[2];

            array_push($data, [
                "title" =>  $title ?? "",
                "summary" => $summary ?? "",
                "thumbnai" => $thumbnai ?? "",
                "category" => $category ?? "",
                "from_link" => $from_full ?? "",
                "time_get" =>  $time_get,
                "from" => 'tuoitre.vn',
                "content" => base64_encode($content[0] ?? ""),
                "time_write" =>  $time_write->toDateTimeString(),
            ]);
        }

        return $data;
    }

    static public function get_content_one_post($link)
    {
        $url = $link;

        $client = new \GuzzleHttp\Client();
        $response = $client->request('GET',  $url);


        $html = $response->getBody();

        $html = HtmlDomParser::str_get_html($html);



        $content =  $html->find('#mainContentDetail > div > div.main-content-body');

        $thumbnai = $html->find('#mainContentDetail');

        $thumbnai = $thumbnai ? $thumbnai[0]->find('img')[0]->src : "";


        if ($content != null) {
            $content =  $content[0]->innertext;
        }

        $time =  $html->find('#main-detail > div.w980 > div');

        if ($time != null) {
            $time =  $time[0]->innertext;
            $split = explode(" ",  $time);
        }

        //
      

        if (!is_string($content)) return;
        return [
            $content, 
            str_replace("/", "-", ($split[0] . " " . $split[1])), 
            $thumbnai 
        ];
    }
}
