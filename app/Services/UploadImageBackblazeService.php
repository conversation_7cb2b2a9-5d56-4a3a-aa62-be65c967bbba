<?php

namespace App\Services;

use App\Models\MsgCode;
use obregonco\B2\Client;
use obregonco\B2\Bucket;

class UploadImageBackblazeService
{

    public static function uploadImage($image, $type)
    {
        if ($type == null) {
            $type = "multi";
        }

        $imagePath =  $image->getRealPath();
        $name =  $image->getClientOriginalName();
        try {
            $client = new Client('469c387d5469', [
                'keyId' => '469c387d5469', // optional if you want to use master key (account Id)
                'applicationKey' => '004dfd4ce00621141d73b6a19c014e7a4d3583d3ba',
            ]);

            $resource = fopen($image, "r") or die("File upload Problems");

            $file = $client->upload([
                'BucketName' => 'data3vay',
                'FileName' => "data/$type/$name",
                'Body' =>   $resource,

                // The file content can also be provided via a resource.
                // 'Body' => fopen('/path/to/input', 'r')
            ]);

            $array = (array) $file;

            $fileId = $array["\x00*\x00fileId"];
            $link =  "https://f004.backblazeb2.com/b2api/v1/b2_download_file_by_id?fileId=$fileId";

            return  $link;
        } catch (\GuzzleHttp\Exception\RequestException $e) {
     
            return MsgCode::CANNOT_POST_PICTURES;
        }
    }
}
