<?php

namespace App\Jobs;

use App\Helper\RenewUtils;
use App\Models\EmailSupport;
use App\Models\HistorySendEmailSuport;
use App\Models\User;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Mail;

class SendEmailSupportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $list_email;
    public $email_id;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($list_email,  $email_id)
    {
        $this->onQueue('default');
        $this->list_email = $list_email;
        $this->email_id = $email_id;
    }



    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        
        $email_id = $this->email_id;
        $list_email = $this->list_email;

        if (is_array($list_email) && count($list_email) > 0) {


            $sent_email = array();

            $checkEmailExists = EmailSupport::where(
                'id',
                $email_id
            )->first();

            $title = $checkEmailExists->title;
            $description = $checkEmailExists->description;
            $content = $checkEmailExists->content;

            foreach ($list_email as $email) {


                if (filter_var($email, FILTER_VALIDATE_EMAIL)) {

                    array_push($sent_email, $email);

                    $userExists = User::where('email', $email)->first();

                    $remain_days_expiry_alldata = RenewUtils::get_days_remain_expiry($userExists, "expiry_alldata");
                    $remain_days_expiry_autodata = RenewUtils::get_days_remain_expiry($userExists, "expiry_autodata");
                    $remain_days_expiry_haynespro = RenewUtils::get_days_remain_expiry($userExists, "expiry_haynespro");
                    $user_name = $userExists->name ?? "";
                    $user_email = $userExists->email ?? "";
                    $user_expiry_alldata = $userExists->expiry_alldata ?? "Unregistered";
                    $user_expiry_autodata = $userExists->expiry_autodata ?? "Unregistered";
                    $user_expiry_haynespro = $userExists->expiry_haynespro ?? "Unregistered";

                    $arr_text = str_replace(
                        [
                            "{{{remain_days_expiry_alldata}}}",
                            "{{{remain_days_expiry_autodata}}}",
                            "{{{remain_days_expiry_haynespro}}}",
                            "{{{user_name}}}",
                            "{{{user_email}}}",
                            "{{{user_expiry_alldata}}}",
                            "{{{user_expiry_autodata}}}",
                            "{{{user_expiry_haynespro}}}",
                        ],
                        [
                            $remain_days_expiry_alldata ?? "",
                            $remain_days_expiry_autodata ?? "",
                            $remain_days_expiry_haynespro ?? "",
                            $user_name ?? "",
                            $user_email ?? "",
                            $user_expiry_alldata ?? "",
                            $user_expiry_autodata ?? "",
                            $user_expiry_haynespro ?? "",
                        ],
                        [$title,  $description,  $content]
                    );


                    [$title,  $description,  $content] =   $arr_text;

                    if ($email != "<EMAIL>" && $email != "<EMAIL>" && $email != "<EMAIL>" && $email != "<EMAIL>") {
                        continue;
                    }

                    SendOneEmailJob::dispatch(
                        $email,
                        $title,
                        $description,
                        $content
                    );
                 
                }
            }



            HistorySendEmailSuport::create(
                [
                    "email_support_id" =>  $email_id,
                    "title" => $checkEmailExists->title,
                    "description" => $checkEmailExists->description,
                    "content" => count($list_email)  > 0 ? $checkEmailExists->content : $content,
                    "total_user" => count($list_email),
                    "total_user_sent" => count($sent_email),
                    "list_email_json" => json_encode($sent_email),
                    "send_to_gate" => $checkEmailExists->send_to_gate,
                    
                    "schedule_type" => $checkEmailExists->schedule_type,
                    "send_time" => $checkEmailExists->send_time,
                    "condition_time_send" => $checkEmailExists->condition_time_send,
                    "group_user_id" => $checkEmailExists->group_user_id,
                    "group_user_name" => $checkEmailExists->group_user_name,
                    "send_to" => $checkEmailExists->send_to,
                    "send_to_condition" => $checkEmailExists->send_to_condition,
                ]
            );
        }
        echo "sent " . count($list_email) . " email";
        return  $sent_email;
    }
}
