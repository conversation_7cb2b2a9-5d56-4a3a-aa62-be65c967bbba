<?php

namespace App\Jobs;

use App\Helper\DomainConfigHelper;
use App\Models\Admin;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use GuzzleHttp\Client;


class PushNotificationAdminJob implements ShouldQueue
{

    const TYPE_ERROR_COOKIE_SERVER = 1;
    const TYPE_SUPPORT_CONTACT = 2;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    protected $content;
    protected $title;
    protected $type;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        $title,
        $content,
        $type = 0 //0 dạng thông báo đa số, 1 dạng lỗi cookie
    ) {
        $this->onQueue('default');
        $this->title = $title;
        $this->content = $content;
        $this->type = $type;
    }



    private function sendToMessageChat($title, $content, $token, $id)
    {

        //         $data = [
        //             'chat_id' => $id,
        //             'text' =>  $this->title . "
        // " . $content
        //         ];
        //         $response = file_get_contents("https://api.telegram.org/bot$token/sendMessage?" . http_build_query($data));

        $client = new Client();

        $text  =  $title . "
" . $content;

        $response = $client->post("https://discord.com/api/webhooks/" . $token, [
            'multipart' => [
                [
                    'name' => 'content',
                    'contents' => $text
                ]
            ]
        ]);
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $domainConfig = DomainConfigHelper::getConfig();

        if ($this->type == 0) { //nhận tiền, change pass, gant, ...
            $apiToken = $domainConfig['api_token_boss'];
            $this->sendToMessageChat($this->title,  $this->content, $apiToken,  $domainConfig['chat_id_boss']);
        }

        if ($this->type == PushNotificationAdminJob::TYPE_SUPPORT_CONTACT) { // chat trong contact
            $apiToken = $domainConfig['api_token_contact'];
            $this->sendToMessageChat($this->title,  $this->content, $apiToken,  $domainConfig['chat_id_contact']);
        }

        if ($this->type == PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER) { //lỗi cookie server
            if (str_contains($this->title, 'Alldata US')) { // đối với alldata us
                $apiToken = $domainConfig['api_token_error_cookie_alldata'];
                $this->sendToMessageChat($this->title,  $this->content, $apiToken,  $domainConfig['chat_id_error_cookie_alldata']);
            } else {  // các chương trình khác
                $apiToken = $domainConfig['api_token_error_cookie_general'];
                $this->sendToMessageChat($this->title,  $this->content, $apiToken,  $domainConfig['chat_id_error_cookie_general']);
            }
        }
        //Tìm admin
        $admins =  User::where('is_admin', true)->get();
        foreach ($admins  as $admin) {
            if (
                $admin->admin_lv == Admin::ADMIN_LEVEL_BOSS ||
                $this->type  == 0 ||
                $this->type ==  PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER &&  $admin->admin_lv == Admin::ADMIN_LEVEL_MAINTENANCE ||
                $this->type ==  PushNotificationAdminJob::TYPE_SUPPORT_CONTACT && $admin->admin_lv == Admin::ADMIN_LEVEL_SUPPORT_CONTENT
            ) {
                // PushNotificationUserJob::dispatch(
                //     $admin->id,
                //     $this->title,
                //     $this->content,
                // );
            }

            // if (!str_contains('odemand')) {
            //     Mail::to([$admin->email])
            //         ->send(new \App\Mail\SendMailInfoServer($this->title, $this->content));
            // }
        }
    }
}
