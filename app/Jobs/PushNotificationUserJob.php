<?php

namespace App\Jobs;

use App\Helper\Helper;
use App\Helper\LogUtils;
use App\Helper\SendNotificationUtils;
use App\Models\NotificationUser;
use App\Models\UserDeviceToken;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use GuzzleHttp\Client;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class PushNotificationUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    protected $user_id;
    protected $content;
    protected $title;
    protected $type;
    protected $references_value;
    protected $branch_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        $user_id,
        $title,
        $content
        // $references_value,
        // $branch_id
    ) {
        $this->onQueue('default');
        $this->user_id = $user_id;
        $this->title = $title;
        $this->content = $content;
        // $this->type = $type;
        // $this->references_value = $references_value;
        // $this->branch_id = $branch_id;
    }



    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $deviceTokens = UserDeviceToken::where('user_id',  $this->user_id)
            ->pluck('device_token')
            ->toArray();

        $deviceTokens = array_unique($deviceTokens);

        $data = [
            'body' => $this->content,
            'title' =>  $this->title,
            'type' => $this->type,
            'references_value' => $this->references_value,
        ];

        $deviceTokens = array_unique($deviceTokens);
        $splitArray = array_chunk($deviceTokens, 500);
        $topicName = Helper::getRandomOrderString();
        foreach ($splitArray as $parentArray) {

            // $this->subscribeTopic($parentArray, $topicName);
            // $this->sendNotification($data, $topicName);
            // $this->unsubscribeTopic($parentArray, $topicName);

            $sendReport = SendNotificationUtils::send(
                $data,
                $parentArray,
            );
        }

        NotificationUser::create([
            'user_id' => $this->user_id  ?? null,
            "content" => $this->content,
            "title" => $this->title,
            "type" =>  $this->type,
            "unread" => true,
            'references_value' => $this->references_value,
        ]);
    }

    public function sendNotification($data, $topicName = null)
    {
        $url = 'https://fcm.googleapis.com/fcm/send';
        $data = [
            'to' => '/topics/' . $topicName,
            'notification' => [
                'body' => $data['body'] ?? 'Something',
                'title' => $data['title'] ?? 'Something',
                'image' => $data['image'] ?? null,

                'priority' => 'high',
                'android_channel_id' => 'noti_push_app_1',
                "content_available" => true,
            ],
            "apns" => [
                "payload" => [
                    "aps" =>  [
                        "sound" => "default"
                    ]
                ]
            ],
            "priority" =>  'high',
            "sound" =>  "default"

        ];

        $this->execute($url, $data);
    }

    /**
     * @param $deviceToken
     * @param $topicName
     * @throws GuzzleException
     */
    public function subscribeTopic($deviceTokens, $topicName = null)
    {
        $url = 'https://iid.googleapis.com/iid/v1:batchAdd';
        $data = [
            'to' => '/topics/' . $topicName,
            'registration_tokens' => $deviceTokens,
        ];

        $data =  $this->execute($url, $data);

        if ($data  == null) return;
        $arr_rt = json_decode($data);

        $arr_device_token_err = array();
        if (is_array($arr_rt) && count($arr_rt) > 0) {
            $index = 0;
            foreach ($arr_rt as $item_rt) {
                if (isset($item_rt->error)) {
                    array_push($arr_device_token_err, $deviceTokens[$index]);
                }
                $index++;
            }
        }
        //// //// //// //// //// ////
        if (is_array($arr_device_token_err) && count($arr_device_token_err) > 0) {
            UserDeviceToken::whereIn('device_token', $arr_device_token_err)->delete();
        }
    }

    /**
     * @param $deviceToken
     * @param $topicName
     * @throws GuzzleException
     */
    public function unsubscribeTopic($deviceTokens, $topicName = null)
    {
        $url = 'https://iid.googleapis.com/iid/v1:batchRemove';
        $data = [
            'to' => '/topics/' . $topicName,
            'registration_tokens' => $deviceTokens,
        ];

        $this->execute($url, $data);
    }

    /**
     * @param $url
     * @param array $dataPost
     * @param string $method
     * @return bool
     * @throws GuzzleException
     */
    private function execute($url, $dataPost = [], $method = 'POST')
    {
        $result = false;
        try {
            $client = new Client();
            $result = $client->request($method, $url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'key=' . "AAAAzkedv5c:APA91bEmGDEALArLYiDCmwhKDm54Mopqr52VSq5BAJRGVtWxXWJfSarEehx6cBOEimHFDydC_1hiUr3lLJuPud4AEpBU7ncQFPejIAL7mX5LaESkTluwJFcqtmVT2balm9O_Orjl9Gsa",
                ],
                'json' => $dataPost,
                'timeout' => 300,
            ]);

            $result = $result->getStatusCode() == Response::HTTP_OK;
        } catch (Exception $e) {
            LogUtils::debug($e);
        }

        return $result;
    }
}
