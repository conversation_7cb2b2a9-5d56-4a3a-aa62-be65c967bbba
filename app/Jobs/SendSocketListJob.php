<?php

namespace App\Jobs;

use App\Events\RedisChatEventList;
use App\Helper\ChatUtils;
use App\Models\GroupChat;
use App\Models\LoggerFail;
use App\Models\PersonChat;
use App\Models\UserFriend;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendSocketListJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user_id;
    public $type;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id, $type)
    {
        $this->onQueue('default');
        $this->user_id = $user_id;
        $this->type = $type;
    }



    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        LoggerFail::create([
            'log'=>"xxx"
        ]);


        if ($this->type == ChatUtils::LIST_USER_CHAT) {

            // $all = PersonChat::where('user_id', $this->user_id)
            //     ->orderBy('updated_at', 'desc')
            //     ->paginate(20);

            $all = PersonChat::where('user_id', $this->user_id)
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            $itemsTransformed = $all
                ->getCollection()
                ->map(function ($item) {
                    $item2 = $item->toArray();
                    $item2['to_user']['is_friend'] = UserFriend::where('user_id',  $this->user_id)->where('friend_user_id', $item->vs_user_id)->first() != null;
                    return  $item2;
                })->toArray();

            $itemsTransformedAndPaginated = new \Illuminate\Pagination\LengthAwarePaginator(
                $itemsTransformed,
                $all->total(),
                $all->perPage(),
                $all->currentPage(),
                [
                    'path' => \Request::url(),
                    'query' => [
                        'page' => $all->currentPage()
                    ]
                ]
            );

            event($e = new RedisChatEventList($itemsTransformedAndPaginated,  $this->user_id,  $this->type));
        }

        if ($this->type == ChatUtils::LIST_GROUP_CHAT) {

            $all = GroupChat::where('user_id', $this->user_id)
                ->orderBy('updated_at', 'desc')
                ->paginate(20);


            event($e = new RedisChatEventList($all,  $this->user_id,  $this->type));
        }
        return true;
    }
}
