<?php

namespace App\Jobs;

use App\Helper\WebDataUtils;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendPostRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $url;
    public $fields;

    public function __construct($url, $fields)
    {
        $this->onQueue('post_queue');
        $this->url = $url;
        $this->fields = $fields;
    }

 
    public function handle()
    {
        // Thực hiện POST request ở đây
        $this->post_async($this->url, $this->fields);
    }

    private function post_async($url, $fields)
    {

        $time_end = microtime(true);
        $client = new Client(
            [
                'timeout' => 120,
                'connect_timeout' =>  120,
            ]
        );
        $time_start = microtime(true);
        try {

            $service = $fields['service'];
            $content_type = $fields['content_type'];
            $method = $fields['method'];
            $tail_link = $fields['tail_link'];

            $detailData = "service $service tailLink $tail_link method $method content_type $content_type";
            $response = $client->post($this->url, [
                'headers' => [
                    //  'Content-Type' => 'application/json'
                ],
                'form_params' => $fields
            ]);


            $time_end = microtime(true);
            $time_handle = ($time_end - $time_start);
        } catch (ClientException $e) {
            $time_end = microtime(true);
            $time_handle = ($time_end - $time_start);
            if ($e->getResponse()->getStatusCode() != 200 && $e->getResponse()->getStatusCode() != 400) {

                WebDataUtils::sendNoti(
                    "Can't save WebData Error 500  " . $detailData . " time handle " .  $time_handle,
                    $e->getMessage()
                );
            }
        } catch (GuzzleException $e) {
            $time_end = microtime(true);
            $time_handle = ($time_end - $time_start);
            WebDataUtils::sendNoti(
                "Can't save WebData Error request " . $detailData . " time handle " .  $time_handle,
                $e->getMessage()
            );
        } catch (Exception $e) {
            $time_end = microtime(true);
            $time_handle = ($time_end - $time_start);
            WebDataUtils::sendNoti(
                "Can't save WebData Php Exception  Line: " . $e->getLine() . $detailData . " time handle " .  $time_handle,
                $e->getMessage()
            );
        }
    }
}
