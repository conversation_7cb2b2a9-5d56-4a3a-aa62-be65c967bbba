<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Mail;

class SendOneEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $email;
    public $title;
    public $description;
    public $content;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        $email,
        $title,
        $description,
        $content
    ) {
        $this->onQueue('default');
        $this->email = $email;
        $this->title = $title;
        $this->description = $description;
        $this->content = $content;
    }

    

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        Mail::to([$this->email])
            ->send(new \App\Mail\SendMailSupport(
                $this->title,
                $this->description,
                $this->content
            ));
        echo "sent " . $this->email . " - " . $this->title;
    }
}
