<?php

namespace App\Helper;

use App\Jobs\PushNotificationAdminJob;
use App\Models\DeviceLogin;
use App\Models\PlanService;
use App\Models\RenewalHistory;
use App\Models\User;
use Carbon\Carbon;
use Exception;

class RenewUtils
{


    const PAYMENT_STATUS_WAIT = 0;
    const PAYMENT_STATUS_CANCEL = 1;
    const PAYMENT_STATUS_SUCCESS = 2;

    const PAY_FROM_APPLE_PAY = "FROM_APPLE_PAYPAL1";
    const PAY_FROM_ADMIN = "PAY_FROM_ADMIN";
    const PAY_FROM_AGENCY = "PAY_FROM_AGENCY";
    const PAY_FROM_PAYPAL = "PAY_FROM_PAYPAL";
    const PAY_FROM_STRIPE = "PAY_FROM_STRIPE";
    const PAY_FROM_NOWPAYMENT = "PAY_FROM_NOWPAYMENT";
    const PAY_FROM_CASSO = "PAY_FROM_CASSO";
    const PAY_FROM_BUY_ME_A_COFFEE = "PAY_FROM_BUY_ME_A_COFFEE";
    const PAY_FROM_PAYEER = "PAY_FROM_PAYEER";
    const DEMO_2_HOUR = "DEMO_2_HOUR";

    static function add_sub_expiry($user_id, $month, $paid, $is_add, $base_service, $service, $references_id, $references_value, $product_id, $pay_from, $json_data = "", $net_price = 0)
    {
        $lastTime  = null; //Thời gian trước khi xử lý đó
        $lastTimeCacu  = null;
        $lastDateTime = null;
        $lastTimeSv = null;

        $user = User::where('id', $user_id)->first();
        $colum_user_expiry = PlanUtils::getColumnExpiryWithPlanName($service); // if ($service == PlanUtils::ALLDATA) {

        $lastDateTime =   $user->$colum_user_expiry;


        if ($lastDateTime != null) {
            $lastTimeCacu  = $lastDateTime;
            $lastTime = Carbon::parse($lastDateTime);

            $lastTimeSv = unserialize(serialize($lastTime));
        }

        $timeNow = Carbon::parse(Helper::getTimeNowString());

        if ($is_add  == true &&  $timeNow->greaterThan($lastTime)) {
            $lastTimeCacu  = $timeNow;
        }


        if ($lastTimeCacu  == null) {
            $lastTimeCacu  = $timeNow;
        }



        if ($is_add  == true) {

            if ($lastTimeCacu  == null) {
                $lastTimeCacu  = $timeNow;
            }

            $timeNext =  $lastTimeCacu->addMonths($month);
        } else {


            if ($lastDateTime  == null) {
                $timeNext =  $timeNow;
                $month = 0;
            } else
            if (Carbon::parse($lastDateTime)->greaterThan($timeNow)) {

                $max_day = $timeNow->diffInDays(Carbon::parse($user->$colum_user_expiry), false);

                if ($max_day > 15) {
                    $timeNext =  $lastDateTime->subMonths($month);

                    if ($month > 0 && $timeNow->greaterThan($timeNext)) {

                        if ($max_day  - (($month - 1) * 30) < 15) {

                            $month = $month - 1;
                            $timeNext =  $timeNow;
                        }
                    }
                } else {
                    $timeNext =  $timeNow;
                    $month = 0;
                }
            } else {
                $timeNext =  $timeNow;
                $month = 0;
            }
        }

        RenewalHistory::create([
            "user_id" => $user_id,
            "month" => $is_add  == true ? $month : -$month,
            "base_service" => $base_service,
            "service" => $service,
            "price" => floor($paid ?? 0),
            "paid" => floor($paid ?? 0),
            "before_expiration_date" =>    $lastTimeSv,
            "after_expiration_date" =>  $timeNext,
            "extender_user_id" => $user_id,
            "references_id" => $references_id,
            "references_value" => $references_value,
            'product_id' => $product_id,
            'pay_from' => $pay_from,
            'json_data' => $json_data,
            'net_price' => $net_price,
        ]);
        $user = User::where('id', $user_id)->first();

        $user->update([
            $colum_user_expiry => $timeNext
        ]);


        //Reset device
        $user->update([
            'platform' => null,
            'device_id' => null,
            'model' => null,
        ]);

        DeviceLogin::where('user_id',  $user->id)
            ->update([
                "ip_using" => null,
                "address" => null,
                "device_id" => null,
                "model_name" => null,
                "platform" => null,
                "last_visit_time" => null,
                "app_version" => null,
                "login_time" => null,
            ]);
        ///


        if ($lastTimeSv == null || $timeNext > $lastTimeSv) {
            SendEmailUtils::sendEmailRenewSuccess($user->email, $service);
        }

        if ($pay_from == RenewUtils::PAY_FROM_AGENCY || $pay_from == RenewUtils::PAY_FROM_ADMIN) {
            PushNotificationAdminJob::dispatch(
                "Renewed for user " . $user->username,
                "" . $service . " " . ($is_add  == true ? $month : -$month) . " month " . $references_value . " " . $pay_from . " - " . $_SERVER['HTTP_HOST'],
            );
        } else {
            PushNotificationAdminJob::dispatch(
                "You received " . floor($paid) . "$ from " . $user->username,
                "" . $service . " " . ($is_add  == true ? $month : -$month) . " month " . $references_value . " " . $pay_from . " - " . $_SERVER['HTTP_HOST'],
            );
        }
    }


    static function renew_service_for_user($user_id, $product_id, $pay_from, $references_id, $references_value, $json_data = "", $plan_id = null, $net_price = 0)
    {
        $plan = null;

        if ($plan_id  != null) {
            $plan =  PlanService::where('id',  $plan_id)->first();
        } else if ($product_id != null) {
            $plan =  PlanService::where('product_id',  $product_id)->first();
        }

        if ($plan  != null) {
            if ($plan->service == PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO) {
                RenewUtils::add_sub_expiry(
                    $user_id,
                    $plan->month,
                    round($plan->price / 3, 3),
                    true,
                    PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO,
                    PlanUtils::ALLDATA,
                    $references_id,
                    $references_value,
                    $product_id,
                    $pay_from,
                    $json_data,
                    round($net_price / 3, 5),
                );
                RenewUtils::add_sub_expiry(
                    $user_id,
                    $plan->month,
                    round($plan->price / 3, 3),
                    true,
                    PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO,
                    PlanUtils::AUTODATA,
                    $references_id,
                    $references_value,
                    $product_id,
                    $pay_from,
                    $json_data,
                    round($net_price / 3, 5),
                );
                RenewUtils::add_sub_expiry(
                    $user_id,
                    $plan->month,
                    round($plan->price / 3, 3),
                    true,
                    PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO,
                    PlanUtils::HAYNESPRO,
                    $references_id,
                    $references_value,
                    $product_id,
                    $pay_from,
                    $json_data,
                    round($net_price / 3, 5),
                );
            } else if ($plan->service == PlanUtils::ALLDATAVSAUTODATA) {
                RenewUtils::add_sub_expiry(
                    $user_id,
                    $plan->month,
                    round($plan->price / 2, 3),
                    true,
                    PlanUtils::ALLDATAVSAUTODATA,
                    PlanUtils::ALLDATA,
                    $references_id,
                    $references_value,
                    $product_id,
                    $pay_from,
                    $json_data,
                    round($net_price / 2, 5),
                );
                RenewUtils::add_sub_expiry(
                    $user_id,
                    $plan->month,
                    round($plan->price / 2, 3),
                    true,
                    PlanUtils::ALLDATAVSAUTODATA,
                    PlanUtils::AUTODATA,
                    $references_id,
                    $references_value,
                    $product_id,
                    $pay_from,
                    $json_data,
                    round($net_price / 2, 5),
                );
            } else {
                RenewUtils::add_sub_expiry(
                    $user_id,
                    $plan->month,
                    $plan->price,
                    true,
                    $plan->service,
                    $plan->service,
                    $references_id,
                    $references_value,
                    $product_id,
                    $pay_from,
                    $json_data,
                    $net_price
                );
            }
        }
    }

    static function check_has_expiry($userExists)
    {
        $has_expiry  = false;
        $timeNow = Carbon::parse(Helper::getTimeNowString());
        if ($userExists->expiry_alldata != null && Carbon::parse($userExists->expiry_alldata)->greaterThan($timeNow)) {
            $has_expiry = true;
        }

        if ($userExists->expiry_autodata != null && Carbon::parse($userExists->expiry_autodata)->greaterThan($timeNow)) {
            $has_expiry = true;
        }

        if ($userExists->expiry_autodata_italy != null && Carbon::parse($userExists->expiry_autodata_italy)->greaterThan($timeNow)) {
            $has_expiry = true;
        }

        if ($userExists->expiry_haynespro != null && Carbon::parse($userExists->expiry_haynespro)->greaterThan($timeNow)) {
            $has_expiry = true;
        }

        if ($userExists->expiry_haynespro_truck != null && Carbon::parse($userExists->expiry_haynespro_truck)->greaterThan($timeNow)) {
            $has_expiry = true;
        }

        if ($userExists->expiry_identifix != null && Carbon::parse($userExists->expiry_identifix)->greaterThan($timeNow)) {
            $has_expiry = true;
        }
        if ($userExists->expiry_mitchell_repair_center != null && Carbon::parse($userExists->expiry_mitchell_repair_center)->greaterThan($timeNow)) {
            $has_expiry = true;
        }
        if ($userExists->expiry_mitchell_prodemand != null && Carbon::parse($userExists->expiry_mitchell_prodemand)->greaterThan($timeNow)) {
            $has_expiry = true;
        }
        if ($userExists->expiry_partslink24 != null && Carbon::parse($userExists->expiry_partslink24)->greaterThan($timeNow)) {
            $has_expiry = true;
        }
        if ($userExists->expiry_kdsgds != null && Carbon::parse($userExists->expiry_kdsgds)->greaterThan($timeNow)) {
            $has_expiry = true;
        }
        if ($userExists->expiry_etka != null && Carbon::parse($userExists->expiry_etka)->greaterThan($timeNow)) {
            $has_expiry = true;
        }
        if ($userExists->expiry_tecdoc != null && Carbon::parse($userExists->expiry_tecdoc)->greaterThan($timeNow)) {
            $has_expiry = true;
        }
        if ($userExists->expriy_ford_pts != null && Carbon::parse($userExists->expriy_ford_pts)->greaterThan($timeNow)) {
            $has_expiry = true;
        }
        return  $has_expiry;
    }

    static function check_has_expiry_for_service($userExists, $service)
    {
        $has_expiry  = false;
        $timeNow = Carbon::parse(Helper::getTimeNowString());

        //($service == PlanUtils::ALLDATA) {

        $colum_user_expiry = PlanUtils::getColumnExpiryWithPlanName($service);


        if ($userExists->$colum_user_expiry != null && Carbon::parse($userExists->$colum_user_expiry)->greaterThan($timeNow)) {
            $has_expiry = true;
        }


        return  $has_expiry;
    }


    //expiry_alldata  expiry_autodata expiry_haynespro
    static function get_days_remain_expiry($userExists, $service, $email = null) //là column của user đó
    {

        if ($email != null) {
            $userExists = User::where('email', $email)->first();
        }

        try {

            $timeNow = Carbon::parse(Helper::getTimeNowString());
            if ($userExists->$service != null && Carbon::parse($userExists->$service)->greaterThan($timeNow)) {

                $diff = Carbon::parse($userExists->$service)->diffInDays($timeNow);
                return $diff;
            }
        } catch (Exception $e) {
        }

        return  0;
    }

    static function add_sub_expiry_device($user_id, $month, $paid, $is_add, $base_service, $service, $references_id, $references_value, $device_login_id, $pay_from, $json_data = "")
    {
        $lastTime  = null; //Thời gian trước khi xử lý đó
        $lastTimeCacu  = null;
        $lastDateTime = null;
        $lastTimeSv = null;

        $deviceLogin = DeviceLogin::where('id', $device_login_id)->first();

        if ($deviceLogin != null) {
            $lastDateTime =   $deviceLogin->expiry_use;
        }


        if ($lastDateTime != null) {
            $lastTimeCacu  = $lastDateTime;
            $lastTime = Carbon::parse($lastDateTime);

            $lastTimeSv = unserialize(serialize($lastTime));
        }

        $timeNow = Carbon::parse(Helper::getTimeNowString());

        if ($is_add  == true &&  $timeNow->greaterThan($lastTime)) {
            $lastTimeCacu  = $timeNow;
        }


        if ($lastTimeCacu  == null) {
            $lastTimeCacu  = $timeNow;
        }



        if ($is_add  == true) {

            if ($lastTimeCacu  == null) {
                $lastTimeCacu  = $timeNow;
            }

            $timeNext =  $lastTimeCacu->addMonths($month);
        } else {


            if ($lastDateTime  == null) {
                $timeNext =  $timeNow;
                $month = 0;
            } else
            if (Carbon::parse($lastDateTime)->greaterThan($timeNow)) {

                $timeNext =  $lastDateTime->subMonths($month);
            } else {
                $timeNext =  $timeNow;
                $month = 0;
            }
        }

        RenewalHistory::create([
            "user_id" => $user_id,
            "month" => $is_add  == true ? $month : -$month,
            "base_service" => $base_service,
            "service" => $service,
            "price" => floor($paid),
            "paid" => floor($paid),
            "before_expiration_date" =>    $lastTimeSv,
            "after_expiration_date" =>  $timeNext,
            "extender_user_id" => $user_id,
            "references_id" => $references_id,
            "references_value" => $references_value,
            'device_login_id' => $device_login_id,
            'pay_from' => $pay_from,
            'json_data' => $json_data
        ]);
        $user = User::where('id', $user_id)->first();

        if ($deviceLogin != null) {
            $deviceLogin->update([
                'expiry_use' => $timeNext
            ]);
        } else {
            DeviceLogin::create([
                'user_id' => $user_id,
                'is_main' => false,
                'expiry_use' => $timeNext
            ]);
        }


        // if ($lastTimeSv == null || $timeNext > $lastTimeSv) {
        //     SendEmailUtils::sendEmailRenewSuccess($user->email, $service);
        // }

        if ($pay_from == RenewUtils::PAY_FROM_AGENCY || $pay_from == RenewUtils::PAY_FROM_ADMIN) {
            PushNotificationAdminJob::dispatch(
                "More device " . $user->username,
                " " . $service . " " . ($is_add  == true ? $month : -$month) . " month " . $pay_from . " " . $references_value . " - " . $_SERVER['HTTP_HOST'],
            );
        } else {
            PushNotificationAdminJob::dispatch(
                "You received " . floor($paid) . "$ from " . $user->username,
                "Device login " . $service . " " . ($is_add  == true ? $month : -$month) . " month " . $pay_from . " " . $references_value . " - " . $_SERVER['HTTP_HOST'],
            );
        }
    }
}
