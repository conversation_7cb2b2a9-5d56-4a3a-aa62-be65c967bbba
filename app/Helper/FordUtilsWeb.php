<?php

namespace App\Helper;


use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use App\Models\ConfigAdminYear;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use PHPUnit\TextUI\Help;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;

class FordUtilsWeb
{

    static public function  remove_element_reponse($body, $contentType, $statusCode = null,  $request)
    {

        $ContentType = $contentType;

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];

        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            header("Content-Type:application/pdf");
        }

        if (strlen($tailLink) > 3) {
            if (substr_compare($tailLink, '.svg', -4) === 0) {
                $ContentType  = 'image/svg+xml';
            }

            if (substr_compare($tailLink, '.png', -4) === 0) {
                $ContentType  = 'image/png';
            }
        }

        //Save vao db
        if (
            $request != null
            && $body != null && $body != ""
            &&
            !str_contains($body, "password")
            && !str_contains($body, 'Service Unavailable')
            && $statusCode == 200
        ) {
            $tailLink = Helper::removeParamsFromUrl($tailLink, ['t']);
            $webClass = new WebClass();
            $webClass->tail_link = $tailLink;
            $webClass->service =  PlanUtils::FORD_PTS;
            $webClass->method = $request->method();
            $webClass->content_type =  $contentType;
            $webClass->body =  $body;
            $webClass->status_code =  $statusCode;

            WebDataUtils::saveWebData($webClass);
        }


        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        if (str_contains($body, 'www.fordservicecontent.dealerconnection.com')) {
            $body = str_replace("www.fordservicecontent.dealerconnection.com", $_SERVER['HTTP_HOST'] . "/www-fordservicecontent-dealerconnection-com1",  $body);
        }

        if (str_contains($body, 'www.fordservicecontent.com')) {
            $body = str_replace("www.fordservicecontent.com", $_SERVER['HTTP_HOST'] . "/www-fordservicecontent-com1",  $body);
        }

        if (str_contains($contentType, 'text/html') || str_contains($contentType, 'json')) {
            $body =  str_replace("www.fordtechservice.dealerconnection.com", $pathNoHTTP, $body);
        }

          if (str_contains($contentType, 'text/html') || str_contains($contentType, 'json')) {
            $body =  str_replace("Scripts/SessionManager.js", "Scripts/SessionManager111.js", $body);
        }

        if ($request->user != null) {
            if (str_contains($ContentType, 'text/html')) {
                $pattern = '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/';
                $body  = preg_replace($pattern, $request->user->username, $body);
            }
        }


        $body = str_replace("/vdirs/common/privacypolicycglobal.asp", "#",  $body);
        $body = str_replace("/vdirsnet/applicationservices/frequentlyaskedquestions/RETAIL/MEX/EN-US", "#",  $body);

        if (str_contains($ContentType, 'text/html')) {
            $dom = HtmlDomParser::str_get_html($body);
            if ($dom  != null) {
                //Logout button
                $logout = $dom->find('.logout', 0);

                if ($logout != null) {
                    $logout->remove();
                }

                //help button
                $help = $dom->find('.help', 0);

                if ($help != null) {
                    $help->remove();
                }

                //flag button
                $flag = $dom->find('.header-element .flag', 0);

                if ($flag != null) {
                    $flag->remove();
                }

                //flag button
                $flag = $dom->find('.header-element .flag', 0);

                if ($flag != null) {
                    $flag->remove();
                }

                $mobileVersion = $dom->find('#MobileVersion', 0);

                if ($mobileVersion != null) {
                    $mobileVersion->remove();
                }



                $body =  $dom->innertext;
            }
        }


        header('Content-type: ' .  $ContentType ?? "");

        echo   $body;
        die();
    }
    /**
     * Thông tin server
     */
    static public function  getResponse($link, $method = "GET", $body = [], Request $request = null)
    {

        $tailLink = $_SERVER["REQUEST_URI"];
        $body = [];

        if (str_contains($link, 'www-fordservicecontent-dealerconnection-com1')) {
            $link = str_replace('www.fordtechservice.dealerconnection.com/www-fordservicecontent-dealerconnection-com1', 'www.fordservicecontent.dealerconnection.com', $link);
        }

        if (str_contains($link, 'www-fordservicecontent-com1')) {
            $link = str_replace('www.fordtechservice.dealerconnection.com/www-fordservicecontent-com1', 'www.fordservicecontent.com', $link);
        }

        if ($request != null) {
            $body = array_diff_key($request->all(), $request->query());
            $body =  Helper::removeBodyExcessive($body);
        }


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        $cookies_str = null;
        $proxy = null;

        $configAdmin =      ConfigAdmin::first();
        if ($cookies_str == null) {
            $cookies_str = Cache::get(json_encode(['cookies_ford_pts_str']));

            if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_ford_pts_str)) {
                $cookies_str = $configAdmin->cookies_ford_pts_str;
            }
        }

        if ($proxy === null) {
            if ($configAdmin  != null && !empty($configAdmin->proxy_ford_pts)) {
                $proxy = $configAdmin->proxy_ford_pts;
            }
        }

        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);

        // unset($headers_requests['user-agent']);
        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['cf-ipcountry']);
        unset($headers_requests['x-forwarded-for']);
        unset($headers_requests['cf-ray']);
        unset($headers_requests['cdn-loop']);
        unset($headers_requests['cf-connecting-ip']);

        $headers_requests['user-agent'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
        $headers_requests['accept-language'] = 'en,en-US;q=0.9,vi-VN;q=0.8,vi;q=0.7,zh-CN;q=0.6,zh;q=0.5,ca;q=0.4,ko;q=0.3,de;q=0.2,ar;q=0.1,hi;q=0.1';

        if ($method ==  "POST" && str_contains($link, 'Ford_Content')) {
            $headers_requests = [];
        }

        if (str_contains($link, 'fordservicecontent')) {
            $cookies_str = null;
        }

        $cookies_str  = FordUtilsWeb::standCookie($cookies_str);

        ///////////Xóa vin tránh lỗi
        if (str_contains($link, 'wiring?vin=')) {
            $link = Helper::removeParamsFromLink($link, 'vin');
        }

        if (str_contains($link, 'publication/Book/workshop/')) {
            $link = preg_replace("#(/[^/]+)(\?.*)$#", "/$2", $link);
        }
        $res = CurlUtils::requestFordPts($method, $link, [
            'headers' =>  $headers_requests,
            'cookie' =>  $cookies_str,
            'proxy' => $proxy,
            'form_params' => $body,
        ]);

        $cookiesNew = null;

        // if (isset($res->headers['set-cookie']) && is_array($res->headers['set-cookie'])) {

        //     $cookieLast = CookiesUtils::cookieStrToArray($cookies_str);
        //     $set_cookies = $res->headers['set-cookie'];

        //     foreach ($set_cookies  as $cooki_item) {
        //         $splitItem = explode(";", $cooki_item);
        //         if (count($splitItem) > 0) {
        //             $splitKeyValue = explode("=", $splitItem[0], 2);
        //             if (
        //                 count($splitKeyValue) > 1
        //                 //&& $splitKeyValue[0] == "v3.tusc1.sessionTicket"
        //             ) {
        //                 $cookiesNew =  $cookiesNew . $splitKeyValue[0] . "=" . $splitKeyValue[1] . ";";
        //                 $key1 = $splitKeyValue[0];
        //                 $value1 =  $splitKeyValue[1];
        //                 $cookieLast[$key1] = $value1;
        //             }
        //         }
        //     }
        // }

        // if ($configAdmin  != null && !empty($cookiesNew)) {
        //     $cookiesNew = CookiesUtils::cookiesKeyValueToStr($cookieLast);
        //     $expiresAt = Carbon::now()->addSeconds(6);
        //     Cache::put(json_encode(['cookies_ford_pts_str']), $cookiesNew, $expiresAt);

        //     if (str_contains($cookiesNew, 'PERSISTENT')) {
        //         $configAdmin->update([
        //             'cookies_ford_pts_str' => $cookiesNew
        //         ]);
        //     }
        // }

        return $res;
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        $content = "";
        try {
            $time_start = microtime(true);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://www.fordtechservice.dealerconnection.com/');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
            curl_setopt($ch, CURLOPT_ENCODING, 'gzip');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language: en,en-US;q=0.9,vi-VN;q=0.8,vi;q=0.7,zh-CN;q=0.6,zh;q=0.5,ca;q=0.4,ko;q=0.3,de;q=0.2,ar;q=0.1,hi;q=0.1',
                'cache-control: max-age=0',
                'dnt: 1',
                'priority: u=0, i',
                'sec-ch-ua: "Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "macOS"',
                'upgrade-insecure-requests: 1',
                'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            ]);

            $configAdmin =      ConfigAdmin::first();
            if ($cookies_str == null) {
                $cookies_str = Cache::get(json_encode(['cookies_ford_pts_str']));

                if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_ford_pts_str)) {
                    $cookies_str = $configAdmin->cookies_ford_pts_str;
                }
            }
            $cookies_str  = FordUtilsWeb::standCookie($cookies_str);

            curl_setopt($ch, CURLOPT_COOKIE,    $cookies_str);

            $content =  $response = curl_exec($ch);

            if ($content != "") {
                $userId = "";

                $pattern = '/"UserId":"(.*?)"/m';

                if (preg_match($pattern, $content, $matches)) {
                    $userId = $matches[1];
                    $time_end = microtime(true);
                    $dataFinal = [
                        'time_handle' => ($time_end - $time_start),
                        'status' => true,
                        'mess' => "",
                        'user_id' =>  $userId
                    ];
                    return $dataFinal;
                }
            }
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage() . " khong xac dinh"
            ];
        }

        if (str_contains($content, 'Service Unavailable - Fail to connect')) {
            return [
                'status' => false,
                'mess' => "ERROR FROM FORD SERVER",
                'content' =>  'Service Unavailable - Fail to connect'
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES",
            'content' =>  $content
        ];
    }

    static public function standCookie($cookies_str)
    {


        $cookieLast = CookiesUtils::cookieStrToArray($cookies_str);
        if (isset($cookieLast['PERSISTENT'])) {
            $PERSISTENT =    $cookieLast['PERSISTENT'];


            $newHeader = "en%2Cen-US%2Cvi-VN%2Cvi%2Czh-CN%2Czh%2Cca%2Cko%2Cde%2Car%2Chi";
            $query =  $PERSISTENT;
            $headerPos = strpos($query, "header=");

            if ($headerPos === false) {
            } else {
                $endPos = strpos($query, "&", $headerPos);

                if ($endPos === false) {
                    $PERSISTENT =  substr($query, 0, $headerPos) . "header=" . $newHeader;
                } else {
                    $PERSISTENT =   substr($query, 0, $headerPos) . "header=" . $newHeader . substr($query, $endPos);
                }
            }

            $cookieLast['PERSISTENT']  =  $PERSISTENT;
        }


        if (str_contains($cookies_str, 'TPS-PERM')) {
            $cookieLast['PERSISTENT'] = str_replace("|", "%7C", $cookieLast['PERSISTENT'] ?? "");
            $cookieLast['PERSISTENT'] = str_replace("*", "%2A", $cookieLast['PERSISTENT'] ?? "");
            $cookieLast['PERSISTENT'] = str_replace(",", "%2C", $cookieLast['PERSISTENT'] ?? "");

            $cookieLast['TPS%2DPERM'] = $cookieLast['TPS-PERM'] ?? "";
            $cookieLast['TPS%2DMEMBERSHIP'] = $cookieLast['TPS-MEMBERSHIP'] ?? "";

            unset($cookieLast['TPS-PERM']);
            unset($cookieLast['TPS-MEMBERSHIP']);

            $cookieLast['TPS%2DMEMBERSHIP'] = str_replace("@", "%40", $cookieLast['TPS%2DMEMBERSHIP'] ?? "");
            $cookieLast['TPS%2DMEMBERSHIP'] = str_replace("/", "%2f",  $cookieLast['TPS%2DMEMBERSHIP'] ?? "");
            $cookieLast['TPS%2DMEMBERSHIP'] = str_replace("+", "%2b", $cookieLast['TPS%2DMEMBERSHIP'] ?? "");
            $cookieLast['TPS%2DMEMBERSHIP'] = str_replace("|", "%7c", $cookieLast['TPS%2DMEMBERSHIP'] ?? "");
            $cookieLast['TPS%2DMEMBERSHIP'] = str_replace("=&flavor=", "%3d&flavor=", $cookieLast['TPS%2DMEMBERSHIP'] ?? "");

            $cookieLast['TPS%2DPERM'] = str_replace("&region=%2A&", "&region=*&", $cookieLast['TPS%2DPERM'] ?? "");
        }


        $cookieNew = [];
        $cookieNew['dtCookie'] = $cookieLast['dtCookie'] ?? "";
        $cookieNew['Ford.TSO.PTSSuite'] = $cookieLast['Ford.TSO.PTSSuite'] ?? "";
        $cookieNew['TPS%2DPERM'] = $cookieLast['TPS%2DPERM'] ?? "";
        $cookieNew['PERSISTENT'] = $cookieLast['PERSISTENT'] ?? "";
        $cookieNew['PREFERENCES'] = $cookieLast['PREFERENCES'] ?? "";
        $cookieNew['TPS%2DMEMBERSHIP'] = $cookieLast['TPS%2DMEMBERSHIP'] ?? "";

        return CookiesUtils::cookiesKeyValueToStr($cookieNew);
    }


    static public function setNewDataBodyRequestFromRes($body)
    {

        $updatedFields  = [];
        try {
            $json = json_decode($body, true);
            if (isset($json['_UCT']['_TIMESTAMP'])) {
                $cachedFields = Cache::get('BODY_DATA_REQUEST_FORD', []);
                $updatedFields = array_merge($cachedFields, [
                    '_TIMESTAMP' => $json['_UCT']['_TIMESTAMP']
                ]);
                Cache::put('BODY_DATA_REQUEST_FORD', $updatedFields, now()->addHours(24 * 7));
            }
        } catch (Exception $e) {
        }

        $pattern = '/<input[^>]*type=["\']hidden["\'][^>]*name=["\']([^"\']+)["\'][^>]*value=["\']([^"\']+)["\'][^>]*>/i';
        preg_match_all($pattern, $body, $matches, PREG_SET_ORDER);

        $result = [];
        foreach ($matches as $match) {
            $key = $match[1];
            $value = $match[2];

            if ($key == '_A' || $key == '_TIMESTAMP' || $key == '_VERSION' || $key == '_CONVID' || $key == '_L' || $key == '_M' || $key == '_DS')
                $result[$key] =  $value;
        }

        $cachedFields = Cache::get('BODY_DATA_REQUEST_FORD', []);
        $updatedFields = array_merge($cachedFields, $result);
        Cache::put('BODY_DATA_REQUEST_FORD', $updatedFields, now()->addHours(24 * 7));
        return  $updatedFields;
    }
}
