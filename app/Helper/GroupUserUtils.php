<?php

namespace App\Helper;

use App\Models\Agency;
use App\Models\GroupUser;
use App\Models\GroupUserConditionItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class GroupUserUtils
{
    const GROUP_FIELD_TIME_REGISTER = "GROUP_FIELD_TIME_REGISTER";
    const GROUP_FIELD_REMAIN_DAYS_ALLDATA = "GROUP_FIELD_REMAIN_DAYS_ALLDATA";
    const GROUP_FIELD_REMAIN_DAYS_AUTODATA = "GROUP_FIELD_REMAIN_DAYS_AUTODATA";
    const GROUP_FIELD_REMAIN_DAYS_HAYNESPRO = "GROUP_FIELD_REMAIN_DAYS_HAYNESPRO";
    const GROUP_FIELD_AGENCY = "GROUP_FIELD_AGENCY";

    static function getEmailsWithMultiGroupId($arrGroupId)
    {
        $emails = array();
        foreach ($arrGroupId as $groupId) {

            $emails =   array_merge($emails, GroupUserUtils::getEmailsWithGroupId($groupId));
        }

        $emails = array_unique($emails);
        return  $emails;
    }

    static function getEmailsWithGroupId($groupId)
    {
        $is_first = true;
        $emails = array();

        $checkGroupUserExists = GroupUser::where(
            'id',
            $groupId
        )->first();

        if (!empty($checkGroupUserExists)) {
            $condition_items = GroupUserConditionItem::select('compare_field', 'compare_expression', 'compare_value', 'compare_merge_condition')
                ->where([
                    ['group_user_id', $checkGroupUserExists->id],

                ])
                ->get();

            foreach ($condition_items  as  $condition_item) {
                $ems = GroupUserUtils::getEmailsGroupUserWithCondition(
                    $condition_item->compare_field,
                    $condition_item->compare_expression,
                    $condition_item->compare_value
                );

                if ($is_first  == true) {
                    $emails = array_merge($emails,  $ems);
                } else {

                    if ($condition_item->compare_merge_condition == "AND") {

                        $emails =  array_intersect($emails, $ems);
                    }
                    if ($condition_item->compare_merge_condition == "OR") {
                        $emails = array_merge($emails,  $ems);
                    }
                }

                $is_first = false;
            }
        }

        $emails = array_unique($emails);
        return  $emails;
    }


    static function getEmailsGroupUserWithCondition($compare_field, $compare_expression, $compare_value)
    {
        $emails = array();
        if ($compare_field ==    GroupUserUtils::GROUP_FIELD_TIME_REGISTER) {
            $emails = DB::table("users")->where([
                ['created_at', $compare_expression, $compare_value]
            ])->pluck("email")->toArray();
        }
        if ($compare_field ==    GroupUserUtils::GROUP_FIELD_REMAIN_DAYS_ALLDATA) {
            $days = intval($compare_value);
            $carbon = Carbon::now();
            $carbon->addDays($days);
            $time = $carbon->format('Y-m-d H:i:s');

            $ems = array();
            if ($days  == 0 &&  ($compare_expression == ">=" || $compare_expression == "<=" || $compare_expression == "=" )) {
                $ems = DB::table("users")->where([
                    ['expiry_alldata', null]
                ])->pluck("email")->toArray();
            }
            $emails = DB::table("users")->where([
                ['expiry_alldata', $compare_expression, $time]
            ])->pluck("email")->toArray();

            $emails = array_unique(array_merge($emails,  $ems));
        }
        if ($compare_field ==    GroupUserUtils::GROUP_FIELD_REMAIN_DAYS_AUTODATA) {
            $days = intval($compare_value);
            $carbon = Carbon::now();
            $carbon->addDays($days);
            $time = $carbon->format('Y-m-d H:i:s');

            $ems = array();
            if ($days  == 0 &&  ($compare_expression == ">=" || $compare_expression == "<=" || $compare_expression == "=" )) {
                $ems = DB::table("users")->where([
                    ['expiry_autodata', null]
                ])->pluck("email")->toArray();
            }

            $emails = DB::table("users")->where([
                ['expiry_autodata', $compare_expression, $time]
            ])->pluck("email")->toArray();
            $emails = array_unique(array_merge($emails,  $ems));
        }
        if ($compare_field ==    GroupUserUtils::GROUP_FIELD_REMAIN_DAYS_HAYNESPRO) {
            $days = intval($compare_value);
            $carbon = Carbon::now();
            $carbon->addDays($days);
            $time = $carbon->format('Y-m-d H:i:s');

            $ems = array();
            if ($days  == 0 &&  ($compare_expression == ">=" || $compare_expression == "<=" || $compare_expression == "=" ) ) {
                $ems = DB::table("users")->where([
                    ['expiry_haynespro', null]
                ])->pluck("email")->toArray();
            }

            $emails = DB::table("users")->where([
                ['expiry_haynespro', $compare_expression, $time]
            ])->pluck("email")->toArray();
            $emails = array_unique(array_merge($emails,  $ems));
        }
        if ($compare_field ==    GroupUserUtils::GROUP_FIELD_AGENCY) {
            $usersId = Agency::pluck('user_id')->toArray();

            $emails = DB::table("users")->whereIn('id', $usersId)->pluck("email")->toArray();
        }

        return $emails;
    }
}
