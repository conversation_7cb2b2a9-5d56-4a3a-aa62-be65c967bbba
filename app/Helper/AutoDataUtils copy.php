<?php

namespace App\Helper;

use App\Models\ConfigAdmin;
use Exception;
use GuzzleHttp\TransferStats;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use Illuminate\Support\Facades\Log;

class AutoDataUtils2
{

    const PATH_AUTODATA = "https://workshop.autodata-group.com";


    static public function  remove_element_reponse($response1, $request)
    {


        $domain_request = $_SERVER['HTTP_HOST'];
        $tailLink = $_SERVER["REQUEST_URI"];
        $server = null;


        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $body = $response1->getBody();

        $ContentType = "";
        if ($response1->getHeader('Content-Type') != null) {
            $ContentType = $response1->getHeader('Content-Type')[0] ?? "";
            header('Content-type: ' .  $response1->getHeader('Content-type')[0] ?? "");
        }

        if (str_contains($tailLink, '/js/') && str_contains($tailLink, '.js')) {
            header('Content-Type: application/javascript');
        }


        if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) {

            $body =  str_replace("https://assets.autodata-group.com", $path, $body);

            $body =  str_replace("https://workshop.autodata-group.com", $path, $body);

            $body =  str_replace("//assets.autodata-group.com", "//" . $pathNoHTTP, $body);

            $body =  str_replace("workshop.autodata-group.com", $pathNoHTTP, $body);

            $body =  str_replace("assets.autodata-group.com", $pathNoHTTP, $body);
            $appName = DomainConfigHelper::getConfig('appName');
            $body =  str_replace("Your payment details need updating", 'Welcome to '.$appName, $body);
            $body =  str_replace("Please contact us now to maintain your Autodata access", 'Have a productive day', $body);
            $body =  str_replace("+44 (0)1628 688 111", '+86189 8087 6959', $body);
            
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null &&   $configAdmin->autodata_info != null && $request->user != null) {
                $body =  str_replace($configAdmin->autodata_info->user_name, $request->user->name, $body);
                $body =  str_replace($configAdmin->autodata_info->user_id, $request->user->username, $body);
            }
        }

        if (str_contains($ContentType, 'text/html')) {
            //Remove header web pc
            $dom = HtmlDomParser::str_get_html($body);



            if ($dom  != null && !str_contains($domain_request, 'workshop-autodata-group.')) {


                //check if select vehicle page
                $select_ele = $dom->find('#tabs > h2', 0);
                if ($select_ele != null && $select_ele->innertext == "Select vehicle" && !str_contains($domain_request, 'workshop-autodata-group.')) {
                    return response()->view('error_back_select_autodata');
                }

                $table = $dom->find('#header', 0);
                if ($table != null) {
                    $table->remove();
                }

                //xoa 1 footer man hinh select
                $table = $dom->find("#footer", 0);
                if ($table != null) {
                    $table->remove();
                }

                //xoa 1 footer man hinh data
                $table = $dom->find("#app > div > header", 0);
                if ($table != null) {
                    $table->remove();
                }
                //xoa 1 footer man hinh data
                $table = $dom->find("#app > div > footer", 0);
                if ($table != null) {
                    $table->remove();
                }

                //đổi xe
                $table = $dom->find("#app > div > header > div.sc-fzpjYC.gJohPa.row.col-12.col-print-6.p-0.m-0 > div.sc-fzoKki.igtovi.false.col-xl-3.col-md-4.col-12.col-print-6.current-vehicle.sc-fzoaKM.diYijl > div.sc-fzoYkl.kzkgsM.p-0.mx-0.my-auto.row > div", 0);
                if ($table != null) {
                    $table->remove();
                }


                echo   $dom->innertext;
            } else {
                echo   $body;
            }
        } else {
            echo   $body;
        }
    }
    /**
     * Thông tin server
     */
    static public function  getResponse($link, $method = "GET", $body = [], $cookies_str = null, $request = null, $proxy = null)
    {


        if ($cookies_str == null) {
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null && !empty($configAdmin->cookies_autodata_str)) {
                $cookies_str = $configAdmin->cookies_autodata_str;
            }
        }

        if ($proxy == null) {
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null && !empty($configAdmin->proxy_autodata)) {
                $proxy = $configAdmin->proxy_autodata;
            }
        }


        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $arr = CookiesUtils::cookieStrToArray($cookies_str);

        $headerArr  = [];

        $jar = new \GuzzleHttp\Cookie\CookieJar();
        $tailLink = $_SERVER["REQUEST_URI"];

        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {


                if ($key == "access_token") {
                } else {
                    $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                        'Name'     => $key,
                        'Value'    =>  $value,
                        'Domain'   => '.autodata-group.com',
                        'Path'     => '/',
                        // 'Max-Age'  => $item['name'],
                        'Expires'  => time() + (10 * 365 * 24 * 60 * 60),
                    ]));
                }
            }
        }



        $client = new \GuzzleHttp\Client();
        $response1 = $client->request(
            $method,
            $link,
            [
                'cookies' => $jar,
                'headers' =>   $headerArr,
                'timeout' => 15, // Response timeout
                'connect_timeout' => 15, // Connection timeout
                'form_params' => $body,
                'proxy' => $proxy,
                'on_stats' => function (\GuzzleHttp\TransferStats $stats) {

                    $url = $stats->getHandlerStats()['redirect_url'];
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";

                    $url =  str_replace("https://assets.autodata-group.com", $path, $url);

                    $url =  str_replace("https://workshop.autodata-group.com", $path,  $url);
                    $url =  str_replace(" ", "",  $url);

                    $tailLink = $_SERVER["REQUEST_URI"];


                    if ($url != null && $url != "" && str_contains($tailLink, '/w1/')) {

                        header("Location: $url");
                        exit;
                    }
                }
            ],

        );



        return  $response1;
    }


    static public function checkServer($cookies_str = null, $proxy = null)
    {
        $time_start = microtime(true);
        try {
          
            $response  = AutoDataUtilsWeb::getResponse(
                'https://workshop.autodata-group.com/w2/api/user',
                "GET",
                [],
                $cookies_str,
                null,
                $proxy

            );
          
            $content = ($response->getBody()->getContents());
            if (str_contains($content, "Login page")) {
                return [
                    'status' => false,
                    'mess' => "ERROR COOKIES"
                ];
            }
   
            $user_name = "";
            $user_id = "";
            $name = "";
            $full_name = "";
            $email = "";
            $account_number = "";
            $phone_number = "";

            //Lấy username
            $info_account_list = json_decode($content);
            $user_name = $info_account_list->username ?? "";
            $account_number = $info_account_list->garage->id ?? "";
            $full_name = $info_account_list->garage->name ?? "";
            $user_id = $info_account_list->garage->user_id ?? "";
            $email = $info_account_list->garage->email ?? "";
            $phone_number = $info_account_list->garage->address->phone_number ?? "";

            $time_end = microtime(true);

            return [
                'time_handle' => ($time_end - $time_start),
                'status' => true,
                'mess' => "",
                'name' => $name,
                'email' => $email,
                'user_id' => $user_id,
                'full_name' => $full_name,
                'user_name' => $user_name,
                'account_number' => $account_number,
                'phone_number' => $phone_number,

            ];
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage()
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
