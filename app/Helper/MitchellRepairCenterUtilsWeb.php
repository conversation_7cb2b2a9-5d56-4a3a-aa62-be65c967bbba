<?php

namespace App\Helper;

use App\Models\ConfigAdmin;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;

class MitchellRepairCenterUtilsWeb
{



    static public function  remove_element_reponse(CurlReponse $response1, $request)
    {
        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $domain_request = $_SERVER['HTTP_HOST'];


        $body = $response1->getResponseBody();
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);


        if (str_contains($domain_request, 'mitchell-repair-center.')) {
            $pathNoHTTP = str_replace("http://", "", $path);
            $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp(); 
            $body =  str_replace("https://crs.mymitchell.com", "https://mitchell-repair-center." . $domainMain, $body);
            $body =  str_replace("http://mitchell-repair-center." . $domainMain, "https://mitchell-repair-center." . $domainMain, $body);
        }



        //    \" id=\"13968816\" src=\"/ADAG/repa

        $body =  str_replace("https://crs.mymitchell.com", $path, $body);
        // $body =  str_replace("http://crs.mymitchell.com", $path, $body);

        if (!str_contains($domain_request, 'mitchell-repair-center.')) {
            $body =  str_replace('src=\"/', 'src=\"' . $path . '/', $body);
            $body =  str_replace('href=\"/', 'href=\"' . $path . '/', $body);
        }

        $contentType = $response1->getContentType();

        if ($request->user != null && str_contains($contentType, 'text/html')) {

            $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_mitchell_repair_center");
            if ($remain_days > 0 && $remain_days < 10) {
                $appName = DomainConfigHelper::getConfig('appName');
                $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                $body =  HtmlUtils::addTagHtmlOnHead('<script>
                 
                     siiimpleToast.alert("('.$appName.') Mitchell repair center expiry date is only ' . $remain_days . ' days, remember to renew!", {
                      position: "top|center",
                      margin: 15,
                      delay: 0,
                      duration: 5000,
                    });
                    </script>', $body);
            }
        }

        //remove nut
        $dom = HtmlDomParser::str_get_html($body);
        if (str_contains($contentType, 'text/html')) {
            //Welcome
            $GlobaLinks = $dom->find('.GlobaLinks', 0);
            if ($GlobaLinks != null) {
                $GlobaLinks->remove();
            }
            $body =  $dom->innertext;
        }





        $tailLink = $_SERVER["REQUEST_URI"];

        if (str_contains($tailLink, '_png')) {
            header('Content-Type: image/png');
        } else 
        if (str_contains($tailLink, '_jpg')) {
            header('Content-Type: image/jpeg');
        } else 
        if (str_contains($tailLink, 'svg')) {
            header('Content-Type: image/svg+xml');
        } else {
            header("Content-Type: $contentType");
        }

        echo   $body;
        die();
    }
    /**
     * Thông tin server
     */
    static public function  getResponse($link, $cookies_str = null, $method = "GET", $body = [], Request $request = null, $proxy = null)
    {
        $tailLink = $_SERVER["REQUEST_URI"];

        if (
            str_contains($tailLink, 'MyAccount.aspx') ||
            str_contains($tailLink, 'Logout.aspx')
        ) {
            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp(); 
            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }

        $headers_requests = [];
        $headers_requests_base = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                    $headers_requests_base[$key] = $value[0];
                }
            }
        }


        if ($cookies_str == null) {
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null && !empty($configAdmin->cookies_mitchell_repair_center_str)) {
                $cookies_str = $configAdmin->cookies_mitchell_repair_center_str;
            }
        }

        if ($proxy == null) {
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null && !empty($configAdmin->proxy_mitchell_repair_center)) {
                $proxy = $configAdmin->proxy_mitchell_repair_center;
            }
        }

        $domain_request = $_SERVER['HTTP_HOST'];


        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);

        unset($headers_requests['cdn-loop']);
        unset($headers_requests['cf-ipcountry']);
        unset($headers_requests['cf-connecting-ip']);
        unset($headers_requests['cookie']);
        unset($headers_requests['user-agent']);
        unset($headers_requests['x-forwarded-for']);
        unset($headers_requests['cf-ray']);

        if (substr($cookies_str, 0, 1) == "[") { //neu la array string
            $cookiesArray = json_decode($cookies_str, true);
            $cookies_str = CookiesUtils::cookiesArrayToStr($cookiesArray);
        }
     
        $res = CurlUtils::request($method, $link, [
            'headers' => [
                'accept' => $headers_requests_base['accept'] ??"",
                'user-agent' => $headers_requests_base['user-agent']??"",
                 'cookie' =>  $cookies_str ?? "",
            ],
            'form_params' => $body,
            'cookie' =>  $cookies_str ?? "",
            'proxy' => $proxy ?? ""
        ]);

        return $res;
    }

    static public function checkServer($cookies_str = null)
    {

        try {
            $time_start = microtime(true);

            $response  = MitchellRepairCenterUtilsWeb::getResponse('https://crs.mymitchell.com/', $cookies_str);
            $type = $response->getContentType();
            $content = $response->getResponseBody();

            if (str_contains($type, 'text/html')) {

                if (str_contains($content, 'Content/Login.css')) {
                    return [
                        'status' => false,
                        'mess' => "ERROR COOKIES"
                    ];
                }


                $email = "";
                $user_id = "";


                //Lấy username
                $dom = HtmlDomParser::str_get_html($content);
                if ($dom  != null) {
                    $emailspan = $dom->find('#ManageAccount_lblEmail', 0);
                    if ($emailspan != null) {
                        $email  = str_replace(" ", "", ($emailspan->text()));
                    }

                    $useridspan = $dom->find('#ManageAccount_lblUserID', 0);
                    if ($useridspan != null) {
                        $user_id  = str_replace(" ", "", ($useridspan->text()));
                    }
                }

                $time_end = microtime(true);
                $dataFinal = [
                    'email' => $email,
                    'user_id' => $user_id,
                    'time_handle' => ($time_end - $time_start),
                    'status' => true,
                    'mess' => "",
                ];

                return $dataFinal;
            }
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage()
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
