<?php

namespace App\Helper;

use Carbon\Carbon;
use DateTime;

class Helper
{

    static function getDomainCurrentWithoutSubAndNoHttp()
    {
        $host = $_SERVER['HTTP_HOST'];
        $host_names = explode(".", $host);
        $count = count($host_names);

        if (str_contains($host, 'localhost')) {
            return $host_names[count($host_names) - 1];
        }

        if ($count > 3) {
            return $host_names[$count - 3] . "." . $host_names[$count - 2] . "." . $host_names[$count - 1];
        } else {
            return $host_names[$count - 2] . "." . $host_names[$count - 1];
        }
    }

    static function getHttpOrHttps()
    {
        return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http");
    }

    static function getDomainCurrentWithoutSubAndHttp()
    {
        $host =  "$_SERVER[HTTP_HOST]";
        $host_names = explode(".", $host);

        if (str_contains($host, 'localhost')) {
            $bottom_host_name = $host_names[count($host_names) - 1];
        } else {
            $bottom_host_name = $host_names[count($host_names) - 2] . "." . $host_names[count($host_names) - 1];
        }
        // $domain = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$bottom_host_name";
    }

    static function getDomainCurrent()
    {
        $domain = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        return $domain;
    }

    static  function removeBodyExcessive($array)
    {
        unset($array['data3_server']);
        unset($array['data3_domain']);
        unset($array['agency_sub']);
        unset($array['user']);
        unset($array['isForCustomer']);
        return $array;
    }

    static  function removeNullValues(array &$array)
    {
        foreach ($array as $key => $value) {
            if ($value === null) {
                unset($array[$key]);
            }
        }
    }

    static function sahaRemoveItemArrayIfNullValue(array $array): array
    {

        $newArray = $array;

        foreach ($newArray as $key => $value) {

            if ($value === null) {
                unset($newArray[$key]);
            }
        }

        return $newArray;
    }

    static function getTimeNowDateTime()
    {
        $dt = Carbon::now('UTC');
        $dt =  new DateTime($dt->toDateTimeString());
        return $dt;
    }

    static function getTimeNowString()
    {
        $dt = Carbon::now('UTC');
        $dt = $dt->toDateTimeString();
        return $dt;
    }


    static function getTimeNowStringVietNam()
    {
        $dt = Carbon::now('Asia/Ho_Chi_Minh');
        $dt = $dt->toDateTimeString();
        return $dt;
    }

    static function getRandomOrderString()
    {

        $dt = Carbon::now('UTC');
        $dt1 = $dt->format('dm');
        $dt2 = substr($dt->format('Y'), 2, 3);

        $order_code = $dt1 . $dt2 . Helper::generateRandomString(8);
        return $order_code;
    }

    static function getRandomRevenueExpenditureString()
    {

        $dt = Carbon::now('UTC');
        $dt1 = $dt->format('dm');
        $dt2 = substr($dt->format('Y'), 2, 3);

        $order_code = "TC" . $dt1 . $dt2 . Helper::generateRandomString(6);
        return $order_code;
    }


    static function getRandomTallySheetString()
    {

        $dt = Carbon::now('UTC');
        $dt1 = $dt->format('dm');
        $dt2 = substr($dt->format('Y'), 2, 3);

        $order_code = "K" . $dt1 . $dt2 . Helper::generateRandomString(6);
        return $order_code;
    }

    static function getRandomImportStockString()
    {

        $dt = Carbon::now('UTC');
        $dt1 = $dt->format('dm');
        $dt2 = substr($dt->format('Y'), 2, 3);

        $order_code = "N" . $dt1 . $dt2 . Helper::generateRandomString(6);
        return $order_code;
    }

    static public function generateRandomString($length = 8)
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    static public function generateRandomChart($length = 2)
    {
        $characters = 'ABCDEFGHJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }


    static public function generateRandomNum($length = 6)
    {
        $characters = '0123456789';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    static public function validEmail($str)
    {
        return (!preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $str)) ? FALSE : TRUE;
    }

    static public function day_php_to_standard($day)
    {
        $day = (int)$day;
        if ($day == 0) return 8;
        if ($day == 1) return 2;
        if ($day == 2) return 3;
        if ($day == 3) return 4;
        if ($day == 4) return 5;
        if ($day == 5) return 6;
        if ($day == 6) return 7;
        return 8;
    }

    static public function removeParamsFromLink($url, $paramToRemove)
    {
        $parsedUrl = parse_url($url);
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);
            if (isset($queryParams[$paramToRemove])) {
                unset($queryParams[$paramToRemove]);
            }
            $parsedUrl['query'] = http_build_query($queryParams);
        }

        $newUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $parsedUrl['path'];
        if (!empty($parsedUrl['query'])) {
            $newUrl .= '?' . $parsedUrl['query'];
        }

        return $newUrl;
    }


    static public function removeParamsFromUrl($path, $params)
    {
        $parts = parse_url($path);

        // Tách tham số và giữ lại các tham số khác ngoài ROID
        if (isset($parts['query'])) {
            parse_str($parts['query'], $query);

            foreach ($params as $p) {
                unset($query[$p]);
            }

            $newQuery = http_build_query($query);

            if (!empty($newQuery)) {
                $newUrl = $parts['path'] . '?' . $newQuery;
            } else {
                $newUrl = $parts['path'];
            }

            return $newUrl;
        } else {
            return $path;
        }
    }

    static public function  capitalizeAfterDash($string)
    {
        $string = strtolower($string);
        $string = ucwords($string, '-');
        return $string;
    }

    static public function capitalizeArrayKeys($array)
    {
        $newArray = [];
        foreach ($array as $key => $value) {
            $newKey = Helper::capitalizeAfterDash($key);
            $newArray[$newKey] = $value;
        }
        return $newArray;
    }

    static public function removeParamsFromUrlAutodata($tailLink)
    {


        if (
            str_contains($tailLink, 'access-autodata1') ||
            str_contains($tailLink, 'utrkdata-pendo1') ||
            str_contains($tailLink, 'production-images-data1') ||
            str_contains($tailLink, 'utrkcontent-pendo1')
        ) {
            $tailLink = Helper::removeParamsFromUrl($tailLink, ['v', 'ct', 'jzb']);
        }

        $tailLink = Helper::removeParamsFromUrl($tailLink, ['v', 'language', 'Expires', 'Signature', 'Key-Pair-Id']);

        return $tailLink;
    }

    static public function removeJSessionIdToyota($url)
    {
        $index = strpos($url, ';jsessionid=');
        return $index !== false ? substr($url, 0, $index) : $url;
    }

    static public function removeParamsFromUrlToyota($tailLink)
    {
        $tailLink = Helper::removeJSessionIdToyota($tailLink);

        $params = [
            "time",
            "siid",
            "random"
        ];

        // if (strpos$tailLink, "jp.co.toyota.pzd1132.CCZD1138") !== false) {
        //     $params[] = '_PARAM';
        // }

        return Helper::removeParamsFromUrl($tailLink, $params);
    }

    static public function removeFieldFromBodyToyota($body)
    {
        unset($body['time']);
        return $body;
    }
}
