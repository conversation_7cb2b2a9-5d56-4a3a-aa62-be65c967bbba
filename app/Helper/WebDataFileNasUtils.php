<?php

namespace App\Helper;

use App\Classes\WebClass;
use Exception;
use Illuminate\Support\Facades\Storage;

class WebDataFileNasUtils
{

    static function sendNoti($title, $content) {}
    static function saveWebData(WebClass $webClass)
    {
        $tailLink = $webClass->tail_link;
        $language = $webClass->language;
        $service = $webClass->service;
        $method = $webClass->method;
        $content = $webClass->content;
        $body = $webClass->body;
        $content_type = $webClass->content_type;
        $updated_at = date('Y-m-d H:i:s');

        try {

            $parsed_url = parse_url($tailLink);
            $path = $parsed_url['path'];
            $pathWithFile = $path;
            $query_string = isset($parsed_url['query']) ? $parsed_url['query'] : '';
            parse_str($query_string, $query_array);

            $pathWithFile  = FileUtils::standLinkToPathFile($tailLink);

            $dataResponse =  [
                'tail_link' => $tailLink,
                'language' => $language,
                'service' => $service,
                'method' => $method,
                'content_type' => $content_type,
                'created_at' => $updated_at,
                'updated_at' => $updated_at,
                'query_array' => $query_array,
                'content' => base64_encode($body)
            ];


            $exists = Storage::disk('ftp-v1')->exists($pathWithFile);
            $lastDataResponseJson =  $exists ? Storage::disk('ftp-v1')->get($pathWithFile) : "";
            $lastDataResponse = json_decode($lastDataResponseJson, true);

            if (is_array($lastDataResponse) && isset($lastDataResponse[$tailLink]) && isset($lastDataResponse[$tailLink]['created_at'])) {
                $dataResponse['created_at'] = $lastDataResponse[$tailLink]['created_at'];
                $lastDataResponse[$tailLink] =  $dataResponse;
            } else if (is_array($lastDataResponse)) {
                $lastDataResponse[$tailLink] =  $dataResponse;
            } else {
                $lastDataResponse = array();
                $lastDataResponse[$tailLink] =  $dataResponse;
            }

            $jsonContent = json_encode(
                $lastDataResponse,
                JSON_PRETTY_PRINT
            );

            Storage::disk('ftp-v1')->put($pathWithFile,  $jsonContent);
        } catch (Exception $e) {
            dd($e);
        }
    }

    static function getWebData(WebClass $webClass)
    {
        try {
            $tailLink = $webClass->tail_link;

            $parsed_url = parse_url($tailLink);
            $path = $parsed_url['path'];
            $pathWithFile = $path;
            $query_string = isset($parsed_url['query']) ? $parsed_url['query'] : '';
            parse_str($query_string, $query_array);

            $pathWithFile  = FileUtils::standLinkToPathFile($tailLink);

            $exists = Storage::disk('ftp-v1')->exists($pathWithFile);

            if (!$exists) {
                return  null;
            }

            $lastDataResponseJson = Storage::disk('ftp-v1')->get($pathWithFile);
            $lastDataResponse = json_decode($lastDataResponseJson, true);

            if (is_array($lastDataResponse) && isset($lastDataResponse[$tailLink]) && isset($lastDataResponse[$tailLink]['content'])) {
                $dataResponse['created_at'] = $lastDataResponse[$tailLink]['created_at'];
                $webClass->content =  $lastDataResponse[$tailLink]['content'];
                $webClass->content_type =  $lastDataResponse[$tailLink]['content_type'];
                return $webClass;
            }
        } catch (Exception $e) {
            dd($e);
        }
        return  null;
    }
}
