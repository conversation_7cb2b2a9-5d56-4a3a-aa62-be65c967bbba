<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use App\Helper\HtmlUtils;
use App\Models\WebData;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class Partslink24UtilsWeb
{

    static public function base64url_encode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    static public function fakeTokenPartslink()
    {

        $header = [
            "alg" => "RS256",
            "typ" => "JWT",
            "kid" => "pr-pl24jwks-kid1"
        ];
        $payload = [
            "app" => "partslinq24",
            "type" => "pl24-authorization",
            "country" => "GT",
            "ulo" => "admin",
            "alo" => "gt-318989",
            // "gt-" . rand(100000, 999999),
            "uid" => 2223841,
            // rand(1000000, 9999999),
            "exp" => time() + 3600,
            "iat" => time()
        ];

        $encodedHeader = Partslink24UtilsWeb::base64url_encode(json_encode($header));
        $encodedPayload = Partslink24UtilsWeb::base64url_encode(json_encode($payload));
        $signature = Partslink24UtilsWeb::base64url_encode(bin2hex(random_bytes(32)));
        return $encodedHeader . '.' . $encodedPayload . '.' . $signature;
    }

    static public function setCache($request, $key, $value)
    {
        $expiresAt = Carbon::now()->addMinutes(60);
        Cache::put(json_encode([$request == null || $request->user == null ? null : $request->user->email,  $key]), $value, $expiresAt);
    }
    static public function getCache($request, $key)
    {
        return Cache::get(json_encode([$request == null || $request->user == null ? null : $request->user->email, $key]));
    }
    /**
     * Lấy dữ liệu
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $proxy = null)
    {

        $tailLink = $_SERVER["REQUEST_URI"];

        if (
            str_contains($tailLink, 'logout.action') ||
            str_contains($tailLink, 'portal.action') ||
            str_contains($tailLink, 'pl24-html/index.htm?se=oh')

        ) {
            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();

            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }

        // if (!str_contains($tailLink, "js") &&  !str_contains($tailLink, "css")) {
        //     //Chặn request thừa
        //     $key = 'throttle:1111par1'; // Key Cache dựa trên địa chỉ IP của client
        //     $allowedRequests = 60; // Số lượng yêu cầu tối đa trong cacheTime giây
        //     $cacheTime = 5; // Thời gian cache trong giây

        //     $currentTimestamp = time();
        //     $expireTimestamp = $currentTimestamp + $cacheTime;

        //     $count = Cache::get($key, 0);
        //     if ($count >= $allowedRequests) {
        //         return new Exception('Too many requests. Please try again later.');
        //     }
        //     if ($count === 0 || $currentTimestamp > $expireTimestamp) {
        //         // Reset count and set new expiration timestamp
        //         Cache::put($key, 1, $cacheTime);
        //     } else {
        //         // Increment count
        //         Cache::increment($key);
        //     }
        // }

        if (
            str_contains($tailLink, 'search-text-ajax.actio')
        ) {

            //Chặn request thừa
            $key = 'search-text-ajax.actio111'; // Key Cache dựa trên địa chỉ IP của client

            $allowedRequests = 60; // Số lượng yêu cầu tối đa trong 1 giây
            $cacheTime = 5; // Thời gian cache trong giây

            $currentTimestamp = time();
            $expireTimestamp = $currentTimestamp + $cacheTime;

            $count = Cache::get($key, 0);

            if ($count >= $allowedRequests) {
                return new Exception('Too many requests. Please try again later.');
            }

            if ($count === 0 || $currentTimestamp > $expireTimestamp) {
                // Reset count and set new expiration timestamp
                Cache::put($key, 1, $cacheTime);
            } else {
                // Increment count
                Cache::increment($key);
            }
        }


        if (
            str_contains($tailLink, 'p5vwag/extern/directAccess')
        ) {

            //Chặn request thừa
            $key = 'p5vwag/extern/directAccess111'; // Key Cache dựa trên địa chỉ IP của client

            $allowedRequests = 60; // Số lượng yêu cầu tối đa trong 1 giây
            $cacheTime = 5; // Thời gian cache trong giây

            $currentTimestamp = time();
            $expireTimestamp = $currentTimestamp + $cacheTime;

            $count = Cache::get($key, 0);

            if ($count >= $allowedRequests) {
                return new Exception('Too many requests. Please try again later.');
            }

            if ($count === 0 || $currentTimestamp > $expireTimestamp) {
                // Reset count and set new expiration timestamp
                Cache::put($key, 1, $cacheTime);
            } else {
                // Increment count
                Cache::increment($key);
            }
        }

        if ($request != null &&  $request->method() == "POST") {
            //Chặn request thừa
            $key = 'throttlepartlinkpost:111' . $proxy; // Key Cache dựa trên địa chỉ IP của client
            $allowedRequests = 60; // Số lượng yêu cầu tối đa trong cacheTime giây
            $cacheTime = 5; // Thời gian cache trong giây

            $currentTimestamp = time();
            $expireTimestamp = $currentTimestamp + $cacheTime;

            $count = Cache::get($key, 0);
            if ($count >= $allowedRequests) {
                return new Exception('Too many requests. Please try again later.');
            }
            if ($count === 0 || $currentTimestamp > $expireTimestamp) {
                // Reset count and set new expiration timestamp
                Cache::put($key, 1, $cacheTime);
            } else {
                // Increment count
                Cache::increment($key);
            }
        }


        if ($request->is_check != true) {

            if (
                str_contains($tailLink, 'user/logout.do') ||
                str_contains($tailLink, 'partslink24/user/adminMenu.do') ||
                str_contains($tailLink, '/partslink24/account') ||
                str_contains($tailLink, '/partslink24/edit-user') ||
                str_contains($tailLink, '/partslink24/change-password') ||
                str_contains($tailLink, '/partslink24/account-users')
            ) {
                $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();
                header("Location: https://$request->agency_sub." . $domainMain);
                exit;
            }
        }



        if (
            $tailLink == "/"
        ) {
            $domainMain = Helper::getDomainCurrent();
            header("Location: " . $domainMain . "/partslink24/startup.do");
            exit;
        }


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        $configAdmin =      ConfigAdmin::first();
        if ($proxy === null) {


            if ($configAdmin  != null && !empty($configAdmin->proxy_partslink24)) {
                $proxy = $configAdmin->proxy_partslink24;
            }
        }


        if ($cookies_str == null) {
            $cookies_str = Cache::get(json_encode(['access_token_partslink24']));
            if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_partslink24_str)) {
                $cookies_str = $configAdmin->cookies_partslink24_str;
            }
        }


        // $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        // $arr = CookiesUtils::cookieStrToArray($cookies_str);
        $arr = [];
        //unset($headers_requests['user-agent']);

        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);


        unset($headers_requests['upgrade-insecure-requests']);

        $headerArr  =  $headers_requests;;


        $headerArr['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';

        $jsonToken = json_decode($cookies_str, true);
        if (isset($jsonToken['access_token'])) {
            $access_token = $jsonToken['access_token'];
            $headerArr['authorization'] = "Bearer $access_token";
        }
        if (isset($jsonToken['cookie'])) {
            $headerArr['cookie']  = $jsonToken['cookie'];
        }


        $jar = new \GuzzleHttp\Cookie\CookieJar();

        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {

                $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name'     => $key,
                    'Value'    =>  $value,
                    'Domain'   => '.partslink24.com',
                    'Path'     => '/',
                    // 'Max-Age'  => $item['name'],
                    //'Expires'  => $item['name'],
                    // 'Secure'   => $item['name'],
                    // 'Discard'  => $item['name'],
                    //  'HttpOnly' => $item['name'],
                ]));
            }
        }

        $client = new \GuzzleHttp\Client(
            []
        );
        if (str_contains($link, 'pl24-appgtw/ext/api/1.0/session')) {
            $headerArr['accept'] = 'application/json';
        }

        foreach ($headerArr as $key => $value) {
            if (preg_match('/\b(ip|ipv4|ip address|ipaddress)\b/i', $value)) {
                unset($headerArr[$key]);
            }
        }

        $response1 = $client->request(
            $method,
            $link,
            [
                // 'cookies' => $jar,
                'headers' => $headerArr,
                'json' => $body,
                'form_params' => $body,
                'timeout' => 90, // Response timeout
                'connect_timeout' => 90, // Connection timeout,
                'on_stats' => (str_contains($tailLink, 'check_')) ? null : function (\GuzzleHttp\TransferStats $stats) {

                    $url = $stats->getHandlerStats()['redirect_url'];
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $pathNoHTTP = str_replace("http://", "", $path);
                    $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);


                    $url =  str_replace("https://www.partslink24.com", $path, $url);
                    $url =  str_replace("http://www.partslink24.com", $path, $url);
                    $url =  str_replace("https://partslink24.com", $path, $url);
                    $url =  str_replace("http://partslink24.com", $path, $url);
                    $url =  str_replace("partslink24.com", $pathNoHTTP, $url);


                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);


                    // if (str_contains($url, "logout.do")) {
                    //     throw new Exception("logout.");
                    // }

                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink) {

                        $firtUrl = substr($url, 0, 3);
                        if ($firtUrl == "www") {
                            $url =  str_replace("www.", "https://www.",  $url);
                        }


                        if (str_contains($tailLink, "changeLang")) {

                            parse_str(parse_url($tailLink)['query'], $params);
                            if (isset($params['changeLang'])) {
                                $changeLang = $params['changeLang'];

                                $url = StringUtils::addGetParamToUrl($url, 'changeLang', $changeLang);
                            }
                        }


                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );


        return  $response1;
    }

    static public function  remove_element_reponse($body, $contentType, $statusCode, $request)
    {

        $ContentType = $contentType;

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];

        $tailLink = $_SERVER["REQUEST_URI"];
        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            $ContentType = "application/pdf";
        } else 
        if (str_contains($tailLink, '.svg')) {
            $ContentType = "image/svg+xml";
        }


        if (str_contains($tailLink, '/authorize')) {
            if (isset(json_decode($body)->access_token)) {
                Partslink24UtilsWeb::setCache($request, 'access_token_partslink24', json_decode($body)->access_token);
            };
        }


        //Save vao db
        if ($request != null && $request->method() == "GET" && $statusCode == 200) {
            try {
                if (
                    $request != null
                    //  && !str_contains($contentType, "image")
                    && $body != null && $body != "" && !str_contains($tailLink, "user/adminMenu")
                    // && !str_contains($tailLink, "p5.html")
                ) {
                    $webClass = new WebClass();
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::PARTSLINK24;
                    $webClass->method = $request->method();
                    $webClass->content_type =  $contentType;
                    $webClass->body =  $body;
                    $webClass->status_code =  $statusCode;

                    WebDataUtils::saveWebData($webClass);
                }
            } catch (Exception $e) {
            }
        }

        if ($ContentType) {

            if (str_contains($ContentType, 'text/html')  && !$request->isMethod('post')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                    <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag("js", new Date());
                
                    gtag("config", "G-KXWTNY3G2J");
                    </script>', $body);
            }

            if (str_contains($contentType, 'text/html')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/service/partlink2.js?v=6"></script>', $body);
            }

            if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) {
                $body =  str_replace("https://www.partslink24.com", $path, $body);
                $body =  str_replace("http://www.partslink24.com", $path, $body);
                $body =  str_replace("https://partslink24.com", $path, $body);
                $body =  str_replace("http://partslink24.com", $path, $body);
                $body =  str_replace("partslink24.com", $pathNoHTTP, $body);

                $body =  str_replace("http://", "https://", $body);

                $cookies = $_COOKIE;
                $platform = $cookies['platform'] ?? request()->header('platform') ?? null;

                if ($request->isMethod('get') && str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);

                    if ($dom  != null) {


                        $languageLink = $dom->find('#languageLink', 0);

                        if ($languageLink != null) {
                            $languageLink->remove();
                        }


                        $tvBox = $dom->find('#tvBox', 0);

                        if ($tvBox != null) {
                            $tvBox->remove();
                        }
                        $otherInfoBox = $dom->find('#otherInfoBox', 0);

                        if ($otherInfoBox != null) {
                            $otherInfoBox->remove();
                        }



                        $portalMenu = $dom->find('#portalMenu', 0);

                        if ($portalMenu != null) {
                            $portalMenu->remove();
                        }

                        $logoutLink = $dom->find('#logoutLink', 0);

                        if ($logoutLink != null) {
                            $logoutLink->remove();
                        }

                        $catalog_logout = $dom->find('#catalog_logout', 0);

                        if ($catalog_logout != null) {
                            $catalog_logout->remove();
                        }

                        $HeaderMenus = $dom->find('.HeaderMenu');
                        $idxMenu = 0;
                        if (count($HeaderMenus) > 1) {
                            $lengthMenu = count($HeaderMenus);
                            foreach ($HeaderMenus as $menu) {
                                if ($idxMenu !=  $lengthMenu - 1) {
                                    $menu->remove();
                                }

                                $idxMenu++;
                            }
                        }


                        //replace html
                        $body2 =  $dom->innertext;
                        $body =  ($body2);



                        $pattern = '/<p[^>]*>[^<]*<img[^>]*>\s*<a[^>]*>Quickstart guidelines for partslink24 for independent workshops<\/a>\s*<\/p>/i';

                        $body = preg_replace($pattern, '', $body);
                    }


                    if ($request->user != null) {

                        $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_partslink24");
                        if ($remain_days > 0 && $remain_days < 10  && !$request->isMethod('post')) {
                            $appName = DomainConfigHelper::getConfig('appName');
                            $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script>

                                 siiimpleToast.alert("(' . $appName . ') Partslink24 expiry date is only ' . $remain_days . ' days, remember to renew!", {
                                  position: "top|center",
                                  margin: 15,
                                  delay: 0,
                                  duration: 5000,
                                });
                                </script>', $body);
                        }
                    }
                }
            }
        }

        header('Content-type: ' .  $ContentType ?? "");
        echo $body;
        die();
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        try {
            $time_start = microtime(true);

            $jayParsedAry = [
                "serviceNames" => [
                    "cart",
                    "pl24-full-vin-data",
                    "pl24-orderbridge",
                    "pl24-orderbridge-cart",
                    "pl24-sendbtmail",
                    "pl24-qparts",
                    "orderBook",
                    "pl24-tls-pilot",

                    "dealer-listing-pl24-bmw",
                    "dealer-listing-pl24-abarth",
                    "dealer-listing-pl24-alfa",
                    "dealer-listing-pl24-audi",
                    "dealer-listing-pl24-bentley",
                    "dealer-listing-pl24-bmwmotorrad",
                    "dealer-listing-pl24-citroen",
                    "dealer-listing-pl24-citroenDs",
                    "dealer-listing-pl24-dacia",
                    "dealer-listing-pl24-fiatp",
                    "dealer-listing-pl24-fordp",
                    "dealer-listing-pl24-hyundai",
                    "dealer-listing-pl24-infiniti",
                    "dealer-listing-pl24-jeep",
                    "dealer-listing-pl24-kia",
                    "dealer-listing-pl24-lancia",
                    "dealer-listing-pl24-man",
                    "dealer-listing-pl24-mercedes",
                    "dealer-listing-pl24-mini",
                    "dealer-listing-pl24-mmc",
                    "dealer-listing-pl24-nissan",
                    "dealer-listing-pl24-opel",
                    "dealer-listing-pl24-peugeot",
                    "dealer-listing-pl24-porsche",
                    "dealer-listing-pl24-seat",
                    "dealer-listing-pl24-skoda",
                    "dealer-listing-pl24-smart",
                    "dealer-listing-pl24-vauxhall",
                    "dealer-listing-pl24-volvo",
                    "dealer-listing-pl24-vw",


                    "cupra_parts",
                    "alpine_parts",
                    "vn_parts",
                    "mercedesvans_parts",
                    "miniclassic_parts",
                    "bmwmotorradclassic_parts",
                    "landrover_parts",
                    "vwclassic_parts",
                    "porscheclassic_parts",
                    "bmwclassic_parts",
                    "mercedesunimog_parts",
                    "jaguar_parts",
                    "mercedestrucks_parts",
                    "renault_parts",

                    "audi_parts",
                    "bmw_parts",
                    "abarth_parts",
                    "alfa_parts",
                    "bentley_parts",
                    "bmwmotorrad_parts",
                    "busandtrucks_parts",
                    "citroen_parts",
                    "citroenDs_parts",
                    "dacia_parts",
                    "fiatp_parts",
                    "fordp_parts",
                    "hyundai_parts",
                    "infiniti_parts",
                    "jeep_parts",
                    "kia_parts",
                    "lancia_parts",
                    "man_parts",
                    "mercedes_parts",
                    "mercedesclassic_parts",
                    "mini_parts",
                    "mmc_parts",
                    "nissan_parts",
                    "opel_parts",
                    "peugeot_parts",
                    "porsche_parts",
                    "seat_parts",
                    "skoda_parts",
                    "smart_parts",
                    "vauxhall_parts",
                    "volvo_parts",
                    "vw_parts"
                ],
                "serviceCategoryNames" => [
                    "pl24-shop-universal",
                    "pl24-shop-tools"
                ],
                "withLogin" => true
            ];


            $response  = Partslink24UtilsWeb::getResponse('https://www.partslink24.com/auth/ext/api/1.1/authorize', "POST", $cookies_str, $jayParsedAry, request());
            $type = $response->getHeader('Content-Type')[0] ?? "";

            $content = ($response->getBody()->getContents());
            if (str_contains($content, "Unauthorized")) {
                return [
                    'status' => false,
                    'mess' => "Unauthorized"
                ];
            }

            $c = ConfigAdmin::first();

            $data = json_decode($content);

            $access_token  =  $data->access_token;

            if (empty($access_token) || strlen($access_token) < 700) {
                return [
                    'status' => false,
                    'mess' => "access_token < 700 maybe cookie expired"
                ];
            }

            $expiresAt = Carbon::now()->addSeconds(6);


            $object =  json_decode($c->cookies_partslink24_str, true);

            if (isset($object['access_token'])) {
                $object["access_token"] = $access_token;
            }
            Cache::put(json_encode(['access_token_partslink24']), json_encode($object), $expiresAt);
            $c->update([
                "cookies_partslink24_str" => json_encode($object),
                'cookies_partslink24_json_info' => json_encode([
                    'status' => true,
                    'mess' => "OK updated access token",
                ])
            ]);

            $time_end = microtime(true);
            $dataFinal = [
                'time_handle' => ($time_end - $time_start),
                'status' => true,
                'mess' => "OK updated access token",
            ];
            return $dataFinal;
        } catch (Exception $e) {

            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage() . " khong xac dinh"
            ];
        }
        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
