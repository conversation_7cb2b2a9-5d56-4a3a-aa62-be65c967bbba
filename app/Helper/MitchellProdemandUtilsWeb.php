<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use App\Models\WebData;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use Illuminate\Support\Facades\Cache;
use GuzzleHttp\Client;


class MitchellProdemandUtilsWeb
{



    static public function  remove_element_reponse($body, $contentType, $statusCode = null,  $request)
    {

        $VERSION_CSS_JS = 10;

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $domain_request = $_SERVER['HTTP_HOST'];
        $tailLink = $_SERVER["REQUEST_URI"];


        if (str_contains($tailLink, '_png')) {
            $contentType = "image/png";
        } else 
        if (str_contains($tailLink, '_jpg')) {
            $contentType = "image/jpeg";
        } else 
        if (str_contains($tailLink, 'svg')) {
            $contentType = "image/svg+xml";
        }


        //Save vao db
        if ($request != null && $request->method() == "GET" && $statusCode == 200) {

            try {
                if (
                    $request != null  && !str_contains($tailLink, "ogin") && !str_contains($tailLink, "Information")
                    // && !str_contains($contentType, "image")
                    && !str_contains($body, "Server Error")
                    && $body != null && $body != "" && !str_contains($body, '<div id="login_menu_content">') &&  (!str_contains($body, 'rejected') || str_contains($tailLink, ".js"))
                ) {


                    $webClass = new WebClass();
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::MITCHELL_PRODEMAND;
                    $webClass->method = $request->method();
                    $webClass->content_type =  $contentType;
                    $webClass->body =  $body;
                    $webClass->status_code =  $statusCode;

                    WebDataUtils::saveWebData($webClass);
                }
            } catch (Exception $e) {
            }
        }


        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);


        if (str_contains($domain_request, 'mitchell-prodemand.')) {
            $pathNoHTTP = str_replace("http://", "", $path);
            $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();
            $body =  str_replace("https://crs.mymitchell.com", "https://mitchell-prodemand." . $domainMain, $body);
            $body =  str_replace("http://mitchell-prodemand." . $domainMain, "https://mitchell-prodemand." . $domainMain, $body);
        }


        if (str_contains($contentType, 'text/html') || str_contains($contentType, 'json')) {
            $body =  str_replace("https://www2.prodemand.com", $path, $body);
            $body =  str_replace("paulagara", "rpaulgara", $body);
        }


        if ($request->user != null && str_contains($contentType, 'text/html')) {

            if ($tailLink == "/Main/Index") {
                $body = HtmlUtils::addVersionToJsAndCsss($body, $VERSION_CSS_JS);
            }

            $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_mitchell_prodemand");
            if ($remain_days > 0 && $remain_days < 10) {
                $appName = DomainConfigHelper::getConfig('appName');
                $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                $body =  HtmlUtils::addTagHtmlOnHead('<script>
                 
                     siiimpleToast.alert(" Mitchell prodemand expiry date is only ' . $remain_days . ' days, remember to renew!", {
                      position: "top|center",
                      margin: 15,
                      delay: 0,
                      duration: 5000,
                    });
                    </script>', $body);
            }
        }

        if (str_contains($tailLink, 'UserInformation/Information')) {

            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null &&   $configAdmin->mitchell_prodemand_info != null && $request->user != null) {
                $body = json_encode([
                    "id" => 0,
                    "name" => null,
                    "username" => $request->user->name ?? "",
                    "firstName" => "",
                    "lastName" => "",
                    "address1" => $request->user->address_detail ?? "",
                    "address2" => "",
                    "city" => "",
                    "state" => "",
                    "zip" => "",
                    "phoneNumber" => null,
                    "emailAddress" => $request->user->email ?? "",
                    "profileType" => "Technicians",
                    "accountNumber" => $request->user->id ?? "",
                    "scopegroups" => [["scopegroup" => "Community"]],
                    "communityUserId" => null,
                    "isTechnician" => true,
                    "openQuestionCount" => 0,
                    "openQuestionWithoutActivityCount" => 0,
                ]);
            }
        }


        if (str_contains($contentType, 'text/html')) {
            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/service/mitchell_prodemand.js?v=10"></script>', $body);
            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/jssav/' . Helper::generateRandomString(10) . '.js"></script>', $body);

            $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
            <script>
              window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag("js", new Date());
          
            gtag("config", "G-KXWTNY3G2J");
            </script>', $body);

            $body =  str_replace("intents.mitchell1.com", "thisiscoin.com", $body);
        }

        if (str_contains($body, '404 - File or directory not found') && str_contains($body, '<h1>Server Error</h1>')) {
            header('HTTP/1.0 404 Not Found', true, 404);
        }

        header('Content-type: ' .  $contentType ?? "");
        echo   $body;
        die();
    }
    /**
     * Thông tin server
     */
    static public function  getResponse($link, $cookies_str = null, $method = "GET", $body = [], Request $request = null, $proxy = null)
    {
        $tailLink = $_SERVER["REQUEST_URI"];

        if ($method == "POST") {
            if (str_contains($tailLink, "UserInformation")) {
                echo "404";
                http_response_code(404);
                die();
            }
        }
        if (str_contains($tailLink, "password")) {
            echo "404";
            http_response_code(404);
            die();
        }

        if (
            $tailLink == "/"
        ) {
            header("Location: /Main/Index#|||||||||||||||||/Home");
            exit;
        }

        $headers_requests = [];
        $headers_requests_base = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                    $headers_requests_base[$key] = $value[0];
                }
            }
        }

        $configAdmin =      ConfigAdmin::first();
        if ($cookies_str == null) {
            $cookies_str = Cache::get(json_encode(['cookies_mitchell_prodemand_str']));

            if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_mitchell_prodemand_str)) {
                $cookies_str = $configAdmin->cookies_mitchell_prodemand_str;
            }
        }

        if ($proxy == null) {

            if ($configAdmin  != null && !empty($configAdmin->proxy_mitchell_prodemand)) {
                $proxy = $configAdmin->proxy_mitchell_prodemand;
            }
        }

        $domain_request = $_SERVER['HTTP_HOST'];



        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);

        unset($headers_requests['cdn-loop']);
        unset($headers_requests['cf-ipcountry']);
        unset($headers_requests['cf-connecting-ip']);
        // unset($headers_requests['cookie']);
        unset($headers_requests['user-agent']);
        unset($headers_requests['x-forwarded-for']);
        unset($headers_requests['cf-ray']);

        $headers_requests['Referer'] = 'https://www2.prodemand.com/';

        if (substr($cookies_str, 0, 1) == "[") { //neu la array string
            $cookiesArray = json_decode($cookies_str, true);
            $cookies_str = CookiesUtils::cookiesArrayToStr($cookiesArray);
        }

        $headerInfo = [];
        if (str_contains($link, 'Api/UserInformation/Information')) {
            $headerInfo = [
                'Accept'             => 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language'    => 'en-US,en;q=0.9',
                'Cookie' => $cookies_str ?? "",
                '_riuv'              => MitchellProdemandUtilsWeb::get_RIUV(),
                'Referer'            => 'https://www2.prodemand.com/',
                'User-Agent'         => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36',
                'sec-ch-ua-platform' => '"Windows"',
            ];
        } else {
            $headerInfo = [
                'Cookie' => $cookies_str ?? "",
                'accept' => $headers_requests['accept'] ?? null,
                'accept-language' => $headers_requests['accept-language'] ?? null,
                'Referer' => 'https://www2.prodemand.com/',
                '_riuv'              => MitchellProdemandUtilsWeb::get_RIUV(),
                'User-Agent'         => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36',
                'sec-ch-ua-platform' => '"Windows"',
            ];
        }

        if ((Cache::has('PRODEMAND_FIXING') && str_contains($link, 'Api/UserInformation/Information')) || !Cache::has('PRODEMAND_FIXING')) {

            unset($headerInfo['_riuv']);
            $res = CurlUtils::requestProdemand($method, $link, [
                'headers' => $headerInfo,
                'form_params' => $body,
                'cookie' => $cookies_str ?? "",
                'proxy' => $proxy ?? ""
            ]);
        }


        $cookiesNew = null;
        if ($tailLink != "/Main/Index") {
            if (isset($res->headers['set-cookie']) && is_array($res->headers['set-cookie'])) {

                $cookieLast = CookiesUtils::cookieStrToArray($cookies_str);
                $set_cookies = $res->headers['set-cookie'];

                foreach ($set_cookies  as $cooki_item) {
                    $splitItem = explode(";", $cooki_item);
                    if (count($splitItem) > 0) {
                        $splitKeyValue = explode("=", $splitItem[0],2);
                        if (
                            count($splitKeyValue) > 1
                            //&& $splitKeyValue[0] == "v3.tusc1.sessionTicket"
                        ) {
                            $cookiesNew =  $cookiesNew . $splitKeyValue[0] . "=" . $splitKeyValue[1] . ";";
                            $key1 = $splitKeyValue[0];
                            $value1 =  $splitKeyValue[1];
                            $cookieLast[$key1] = $value1;
                        }
                    }
                }
            }

            if ($configAdmin  != null && !empty($cookiesNew)) {
                $cookiesNew = CookiesUtils::cookiesKeyValueToStr($cookieLast);
                $expiresAt = Carbon::now()->addSeconds(6);
                Cache::put(json_encode(['cookies_mitchell_prodemand_str']), $cookiesNew, $expiresAt);
                $configAdmin->update([
                    'cookies_mitchell_prodemand_str' => $cookiesNew
                ]);
            }
        }



        return $res;
    }

    static public function checkServer($cookies_str = null)
    {

        try {

            $client = new Client();
            $proxy = null;
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null && !empty($configAdmin->proxy_mitchell_prodemand)) {
                $proxy = $configAdmin->proxy_mitchell_prodemand;
            }

            $client = new \GuzzleHttp\Client(
                [
                    'proxy' => $proxy,

                ]
            );

            $response = $client->request(
                'GET',
                'https://aui.mitchell1.com/Login?y=tusc1&exitUrl=https://www.prodemand.com&rememberPassword=True&autoLogin=True'
            );

            if (str_contains($response->getBody()->getContents(), '<title>No Access</title>')) {
                return [
                    'status' => false,
                    'mess' => "ERROR PROXY"
                ];
            }

            // $response  = MitchellProdemandUtilsWeb::getResponse('https://www2.prodemand.com/Main/Index#|||||||||||||||||/Home', $cookies_str);
            $time_start = microtime(true);

            $response  = MitchellProdemandUtilsWeb::getResponse('https://www2.prodemand.com/Api/UserInformation/Information', $cookies_str);
            $body = $response->getResponseBody();

            $time_end = microtime(true);
            if (str_contains($body, 'profileType')) {
                $info_account_list = json_decode($body, true);

                return array_merge(
                    [
                        'time_handle' => ($time_end - $time_start),
                        'status' => true,
                        'mess' => "",
                    ],
                    $info_account_list
                );
            }
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage()
            ];
        }


        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }

    static public function get_RIUV($indexHtml = null)
    {

        if ($indexHtml != null) {
            $html = $indexHtml;
            preg_match("/_RIUV',\s*'([^']+)'/", $html, $matches);
            if (isset($matches[1])) {
                $riuv_value = $matches[1];
                $RIUV = $riuv_value;
                Cache::forever(json_encode(['RIUVCache']), $RIUV);
            }
        }

        $RIUVCache = Cache::get(json_encode(['RIUVCache']));

        if (!empty($RIUVCache)) {
            return $RIUVCache;
        }

        $webClass = new WebClass();
        $webClass->hours_ago = 24 * 30;
        $webClass->tail_link = "/Main/Index";
        $webClass->service =  PlanUtils::MITCHELL_PRODEMAND;
        $webClass->method = "GET";

        $webDataExists =   WebDataUtils::getWebData($webClass);

        if ($webDataExists  != null && $webDataExists->content != null && $webDataExists->content != "") {
            $html = base64_decode($webDataExists->content);
            preg_match("/_RIUV',\s*'([^']+)'/", $html, $matches);

            if (isset($matches[1])) {
                $riuv_value = $matches[1];
                $RIUV = $riuv_value;
                Cache::forever(json_encode(['RIUVCache']), $RIUV);

                return $RIUV;
            }
        }


        return "";
    }
}
