<?php

namespace App\Services;

use App\Helper\GetWebData;
use App\Helper\Helper;
use App\Helper\TecDocUtils;

class TecDocService
{
    /**
     * Get Part Info
     * 
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function getPartInfo($request)
    {
        $lng_id = TecDocUtils::LNG_ID;
        $country_id = TecDocUtils::COUNTRY_ID;
        $article_number = $request->article_number ?? $request['ARTICLE_NUMBER'];
        $sup_id = $request->sup_id ?? $request['SUP_ID'];
        $domain = Helper::getDomainCurrent();

        $queryData = GetWebData::queryTecDoc("
                SELECT
                    ARTICLES.ART_ID,
                    ARTICLES.ART_SUP_ID AS SUP_ID,
                    ARTICLES.ART_ARTICLE_NR AS ARTICLE_NUMBER,
                    SUPPLIERS.SUP_LOGO_NAME AS SUP_BRAND_LOGO,
                    ARTICLES.ART_SUP_BRAND AS BRAND,
                    ART_COUNTRY_SPECIFICS.ACS_PACK_UNIT AS PACK_UNIT,
                    ART_COUNTRY_SPECIFICS.ACS_QUANTITY_PER_UNIT AS QUANTITY_PER_PACK_UNIT,
                    ART_COUNTRY_SPECIFICS.ACS_STATUS_DATE,
                    get_text(ART_COUNTRY_SPECIFICS.ACS_STATUS_DES_ID, $lng_id) AS ART_STATUS_TEXT,
                    CONCAT_WS(' ', get_text(ARTICLES.ART_COMPLETE_DES_ID, $lng_id),
                    get_text(ARTICLES.ART_DES_ID, $lng_id) ) AS PRODUCT_GROUP,
                    (SELECT
                    CONCAT_WS(0x0a0d2d, get_text(ARTICLE_INFO.AIN_TYPE_DES_ID, $lng_id),
                    GROUP_CONCAT(
                    IFNULL( TEXT_MODULE_TEXTS.TMT_TEXT,
                    TEXT_MODULE_TEXTS_UNI.TMT_TEXT )
                    SEPARATOR 0x0a0d2d ) )
                    FROM
                    ARTICLE_INFO
                    LEFT OUTER JOIN TEXT_MODULE_TEXTS ON TEXT_MODULE_TEXTS.TMT_ID =
                    ARTICLE_INFO.AIN_TMT_ID
                    AND TEXT_MODULE_TEXTS.TMT_LNG_ID = $lng_id
                    LEFT OUTER JOIN TEXT_MODULE_TEXTS AS TEXT_MODULE_TEXTS_UNI ON
                    TEXT_MODULE_TEXTS_UNI.TMT_ID = ARTICLE_INFO.AIN_TMT_ID
                    AND TEXT_MODULE_TEXTS_UNI.TMT_LNG_ID = 255
                    WHERE
                    ARTICLE_INFO.AIN_ART_ID = ARTICLES.ART_ID) AS ART_INFO,
                    (SELECT
                    GROUP_CONCAT(
                    CONCAT_WS(': ', get_text(CRITERIA.CRI_DES_ID, $lng_id),
                    IF( ARTICLE_CRITERIA.ACR_DES_ID IS NULL,
                    ARTICLE_CRITERIA.ACR_VALUE,
                    get_text(ARTICLE_CRITERIA.ACR_DES_ID, $lng_id)
                    ) )
                    SEPARATOR '; ')
                    FROM
                    ARTICLE_CRITERIA
                    INNER JOIN CRITERIA ON ARTICLE_CRITERIA.ACR_CRI_ID = CRITERIA.CRI_ID
                    WHERE
                    ARTICLE_CRITERIA.ACR_ART_ID = ARTICLES.ART_ID ) AS ARTICLE_CRITERIA,
                    GROUP_CONCAT(
                    DISTINCT IF(
                        ART_LOOKUP.ARL_TYPE = 'OENumber',
                        CONCAT_WS(': ', ART_LOOKUP.ARL_BRA_BRAND, ART_LOOKUP.ARL_DISPLAY_NR),
                        NULL
                    )
                        SEPARATOR 0x0a
                    ) AS OEM_NUMBERS,
                    GROUP_CONCAT(
                    IF( ART_LOOKUP.ARL_TYPE = 'EAN',
                    ART_LOOKUP.ARL_DISPLAY_NR,
                    NULL )
                    SEPARATOR 0x0a ) AS EAN_NUMBERS
                    FROM
                    ARTICLES
                    LEFT OUTER JOIN ART_COUNTRY_SPECIFICS ON ART_COUNTRY_SPECIFICS.ACS_ART_ID =
                    ARTICLES.ART_ID
                    AND (ART_COUNTRY_SPECIFICS.ACS_COU_ID = $country_id OR
                    ART_COUNTRY_SPECIFICS.ACS_COU_ID = 0)
                    INNER JOIN SUPPLIERS ON ARTICLES.ART_SUP_ID = SUP_ID
                    LEFT OUTER JOIN ART_LOOKUP ON ART_LOOKUP.ARL_ART_ID = ARTICLES.ART_ID
                    WHERE
                    ARTICLES.ART_ARTICLE_NR = '$article_number'
                    AND ARTICLES.ART_SUP_ID = $sup_id;
            ");
        foreach ($queryData as &$item) {
            $criteria = $item['ARTICLE_CRITERIA'];
            $criteria = $item['ARTICLE_CRITERIA'];

            if (!empty($criteria) && is_string($criteria)) {
                $formattedCriteria = [];

                $criteriaArray = explode(';', $criteria);

                foreach ($criteriaArray as $criteriaItem) {
                    list($key, $value) = array_map('trim', explode(':', $criteriaItem));

                    $formattedCriteria[] = [
                        'type' => $key,
                        'data' => $value
                    ];
                }

                $item['ARTICLE_CRITERIA'] = $formattedCriteria;
            } else {
                $item['ARTICLE_CRITERIA'] = [];
            }

            $oemNumbers = $item['OEM_NUMBERS'];
            if (!empty($oemNumbers)) {
                $oemNumbersArray = [];

                $oemItems = explode("\n", $oemNumbers);

                foreach ($oemItems as $oemItem) {
                    $oemData = explode(':', $oemItem);
                    if (count($oemData) == 2) {
                        $brand = trim($oemData[0]);
                        $number = trim($oemData[1]);

                        if (!isset($oemNumbersArray[$brand])) {
                            $oemNumbersArray[$brand] = [];
                        }

                        $oemNumbersArray[$brand][] = $number;
                    }
                }
                $formattedOemNumbers = [];
                foreach ($oemNumbersArray as $brand => $numbers) {
                    $formattedOemNumbers[] = [
                        'Brand' => $brand,
                        'Oem_Number' => $numbers
                    ];
                }

                $item['OEM_NUMBERS'] = $formattedOemNumbers;
            } else {
                $item['OEM_NUMBERS'] = [];
            }

            $eanNumbers = $item['EAN_NUMBERS'];
            if (!empty($eanNumbers)) {
                $eanNumbersArray = [];
                $eanItems = explode("\n", $eanNumbers);

                foreach ($eanItems as $eanItem) {
                    array_push($eanNumbersArray, $eanItem);
                }
                $item['EAN_NUMBERS'] = $eanNumbersArray;
            } else {
                $item['EAN_NUMBERS'] = [];
            }

            $item['SUP_BRAND_LOGO'] = $domain . '/api/tecdoc/access-data/media_files/suppliers_logo_uppercase/' . strtoupper($item['SUP_BRAND_LOGO']);
        }
        return $queryData;
    }

    public function getPartInfos($articleNumbersStr, $supIdsStr)
    {
        $lng_id = TecDocUtils::LNG_ID;
        $country_id = TecDocUtils::COUNTRY_ID;
        $domain = Helper::getDomainCurrent();
        $queryData = GetWebData::queryTecDoc("
                SELECT
                    ARTICLES.ART_ID,
                    ARTICLES.ART_SUP_ID AS SUP_ID,
                    ARTICLES.ART_ARTICLE_NR AS ARTICLE_NUMBER,
                    SUPPLIERS.SUP_LOGO_NAME AS SUP_BRAND_LOGO,
                    ARTICLES.ART_SUP_BRAND AS BRAND,
                    ART_COUNTRY_SPECIFICS.ACS_PACK_UNIT AS PACK_UNIT,
                    ART_COUNTRY_SPECIFICS.ACS_QUANTITY_PER_UNIT AS QUANTITY_PER_PACK_UNIT,
                    ART_COUNTRY_SPECIFICS.ACS_STATUS_DATE,
                    get_text(ART_COUNTRY_SPECIFICS.ACS_STATUS_DES_ID, $lng_id) AS ART_STATUS_TEXT,
                    CONCAT_WS(' ', get_text(ARTICLES.ART_COMPLETE_DES_ID, $lng_id),
                    get_text(ARTICLES.ART_DES_ID, $lng_id) ) AS PRODUCT_GROUP,
                    (SELECT
                    GROUP_CONCAT(
                    CONCAT_WS(': ', get_text(CRITERIA.CRI_DES_ID, $lng_id),
                    IF( ARTICLE_CRITERIA.ACR_DES_ID IS NULL,
                    ARTICLE_CRITERIA.ACR_VALUE,
                    get_text(ARTICLE_CRITERIA.ACR_DES_ID, $lng_id)
                    ) )
                    SEPARATOR '; ')
                    FROM
                    ARTICLE_CRITERIA
                    INNER JOIN CRITERIA ON ARTICLE_CRITERIA.ACR_CRI_ID = CRITERIA.CRI_ID
                    WHERE
                    ARTICLE_CRITERIA.ACR_ART_ID = ARTICLES.ART_ID ) AS ARTICLE_CRITERIA
                    FROM
                    ARTICLES
                    LEFT OUTER JOIN ART_COUNTRY_SPECIFICS ON ART_COUNTRY_SPECIFICS.ACS_ART_ID =
                    ARTICLES.ART_ID
                    AND (ART_COUNTRY_SPECIFICS.ACS_COU_ID = $country_id OR
                    ART_COUNTRY_SPECIFICS.ACS_COU_ID = 0)
                    INNER JOIN SUPPLIERS ON ARTICLES.ART_SUP_ID = SUP_ID
                    LEFT OUTER JOIN ART_LOOKUP ON ART_LOOKUP.ARL_ART_ID = ARTICLES.ART_ID
                    WHERE
                    ARTICLES.ART_ARTICLE_NR IN ($articleNumbersStr)
                    AND ARTICLES.ART_SUP_ID IN ($supIdsStr) GROUP BY ARTICLES.ART_ID;
            ");

        foreach ($queryData as &$item) {
            $criteria = $item['ARTICLE_CRITERIA'];
            $criteria = $item['ARTICLE_CRITERIA'];

            if (!empty($criteria) && is_string($criteria)) {
                $formattedCriteria = [];

                $criteriaArray = explode(';', $criteria);

                foreach ($criteriaArray as $criteriaItem) {
                    list($key, $value) = array_map('trim', explode(':', $criteriaItem));

                    $formattedCriteria[] = [
                        'type' => $key,
                        'data' => $value
                    ];
                }

                $item['ARTICLE_CRITERIA'] = $formattedCriteria;
            } else {
                $item['ARTICLE_CRITERIA'] = [];
            }

            $item['SUP_BRAND_LOGO'] = $domain . '/api/tecdoc/access-data/media_files/suppliers_logo_uppercase/' . strtoupper($item['SUP_BRAND_LOGO']);
        }
        return $queryData;
    }
}
