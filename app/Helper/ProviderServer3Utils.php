<?php

namespace App\Helper;

use App\Http\Controllers\Api\Admin\AdminProxyServerController;
use App\Models\AccountItem;
use App\Models\ConfigAdmin;
use App\Models\ProxyServer;
use Carbon\Carbon;
use Exception;
use Facebook\WebDriver\Exception\TimeoutException;
use GuzzleHttp\Client;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use Illuminate\Support\Facades\Cache;

class ProviderServer3Utils
{

    static public function getCookiesFromPreventive($service)
    {
        $proxyServers = ProxyServer::where('service', $service)->where('is_ok', true)->get();

        foreach ($proxyServers as  $proxyServer) {

            $data =  AdminProxyServerController::getDataServerProxy($proxyServer->id);

            // $k->update([
            //     'is_ok' =>  $data['status'] ?? false,
            //     'error_mess' =>  $data['mess'] ?? "",
            //     'user_name' =>  $data['user_name'] ?? "",
            //     'user_id' =>  $data['user_id'] ?? "",
            // ]);

            $is_ok = $data['status'] ?? false;

            if ($is_ok) {
                return [
                    'success' => true,
                    'link' => "",
                    'name' => $proxyServer->name,
                    'cookies' => $proxyServer->cookies_str
                ];
            }
        }

        return null;
    }

    ////////////////////////////////////////////////// EU
    static public function getCookiesAllDataSV1()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $res = CurlUtils::request('GET', 'http://onlinecatalog1.de/cookies', [
                'cookie' =>  'username=1207; password=ea84a2f9-c3d9-4110-a3fa-cb070e95af28',
            ]);
            $arr = json_decode($res->getResponseBody());

            if (isset($arr[0]->cookie) && $arr[0]->cookie == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => 'http://onlinecatalog1.de/cookies',
                'name' => 'Auto Car Diagnostic alldata eu',
                'cookies' => $arr[0]->cookie
            ];
        } catch (Exception $e) {

            return [
                'success' => false
            ];
        }
    }

    static public function getCookiesAlldataEUAutoCarSoftwate()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');

        try {
            $link = 'https://www.autocarsoftware.com/alldata/auto-login/give-away-new.php?username=anhhung&selection=eu1&region=usa&list=null&nic=93232430714';
            $client = new Client(
                [
                    'proxy' => "s87ZDs1N:xBRd2S9m@212.192.36.39:62742",
                    'timeout' => 8, // Response timeout
                    'connect_timeout' => 8, // Connection timeout
                ]
            );
            $response = $client->get($link);
            $body =  ($response->getBody()->getContents());

            $array = preg_split("/\r\n|\n|\r/", $body);
            $cok =   [];

            foreach ($array as $item) {
                $item_arr = explode("^", $item);


                if (count($item_arr) == 5) {

                    array_push($cok, [
                        "domain" => $item_arr[2],
                        "expirationDate" =>  $item_arr[4],
                        "name" =>  $item_arr[0],
                        "path" =>  $item_arr[3],
                        "value" =>  $item_arr[1],
                    ]);
                }
            }

            $cok = str_replace("\/", '/', json_encode($cok));


            $cokStr = CookiesUtils::cookiesArrayToStr($cok);
            $cokStr  = str_replace('RT="dm=', "dm=",  $cokStr);

            if ($cokStr == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'alldata eu autocarsoftware',
                'cookies' =>  $cokStr
            ];
        } catch (Exception $e) {

            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// US autosoftware
    static public function getCookiesAllDataUSAutocarsoftware()
    {
        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $link = 'http://www.autocarsoftware.com/alldata/auto-login/give-away-new.php?username=dat17&selection=usa1&region=usa&list=null&nic=18438911768';
            $client = new Client(
                [
                    'proxy' => "s87ZDs1N:xBRd2S9m@212.192.36.39:62742",
                    'timeout' => 8, // Response timeout
                    'connect_timeout' => 8, // Connection timeout
                ]
            );
            $response = $client->get($link);
            $body =  ($response->getBody()->getContents());

            $array = preg_split("/\r\n|\n|\r/", $body);
            $cokStr =   "";

            foreach ($array as $item) {
                $item_arr = explode("^", $item);

                if (count($item_arr) == 5) {
                    $cokStr =  $cokStr . (($item_arr[0]) . "=" . (urlencode($item_arr[1]))) . ";";
                }
            }



            if (empty($cokStr)) {
                return [
                    'success' => false
                ];
            }


            return [
                'success' => true,
                'link' => $link,
                'name' => 'alldata us autocarsoftware',
                'cookies' => $cokStr
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                "mess" => $e
            ];
        }
    }

    ////////////////////////////////////////////////// US Reyes
    static public function getCookiesAllDataUSAccountReyesNonVps()
    {
        $proxy = null;
        $configAdmin =      ConfigAdmin::first();

        if ($configAdmin  != null && !empty($configAdmin->proxy_alldata_us_v2)) {
            $proxy = $configAdmin->proxy_alldata_us_v2;
        }

        try {

            $link = 'https://my.alldata.com/ADAG/sso/login';
            $jar = new \GuzzleHttp\Cookie\CookieJar();
            $client = new Client(
                [
                    'proxy' => $proxy,
                    'timeout' => 8, // Response timeout
                    'connect_timeout' => 8, // Connection timeout
                    'cookies' => $jar
                ]
            );

            $response = $client->post('https://my.alldata.com/ADAG/sso/login', [
                'headers' => [
                    'Accept'             => 'application/json, text/plain, */*',
                    'Accept-Language'    => 'en-US,en;q=0.9',
                    'Connection'         => 'keep-alive',
                    'Content-Type'       => 'application/json;charset=UTF-8',
                    'Origin'             => 'https://my.alldata.com',
                    'Referer'            => 'https://my.alldata.com/',
                    'Sec-Fetch-Dest'     => 'empty',
                    'Sec-Fetch-Mode'     => 'cors',
                    'Sec-Fetch-Site'     => 'same-origin',
                    'User-Agent'         => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:134.0) Gecko/20100101 Firefox/134.0',
                    'sec-ch-ua'          => '"Not_A Brand";v="99", "Microsoft Edge";v="109", "Chromium";v="109"',
                    'sec-ch-ua-mobile'   => '?0',
                    'sec-ch-ua-platform' => '"Windows"'
                ],
                'json' => [
                    'username' => 'amirnazzal',
                    'password' => 'Anubis#13',
                    'devId' => 'web-**********-1omjc9p'
                ]
            ]);

            $cookieJar = $client->getConfig('cookies');

            $str_cookies = "";
            foreach ($cookieJar->toArray() as $item) {

                if ($item['Name'] == 'Access-Token') {
                    $str_cookies  =  $str_cookies . ($item['Name'] . "=" . $item['Value']);
                }
            }


            return [
                'success' => true,
                'link' => $link,
                'name' => 'AllDataUS Reyes account',
                'cookies' => $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }


    ////////////////////////////////////////////////// MHH AUTO ALLDATA US
    static public function getCookiesAllDataUSA_MHH_AUTO()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');
        $proxy = null;

        try {

            $link = 'https://webads3.com/';
            $jar = new \GuzzleHttp\Cookie\CookieJar();
            $client = new Client(
                [
                    'proxy' => 's87ZDs1N:xBRd2S9m@212.192.36.39:62742',
                    'timeout' => 8, // Response timeout
                    'connect_timeout' => 8, // Connection timeout
                    'cookies' => $jar
                ]
            );


            $response = $client->post('https://webads3.com/', [
                'headers' => [
                    'authority'                 => 'webads3.com',
                    'accept'                    => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'accept-language'           => 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5,zh-CN;q=0.4,zh;q=0.3,ca;q=0.2,ko;q=0.1,de;q=0.1,ar;q=0.1,hi;q=0.1',
                    'cache-control'             => 'max-age=0',
                    'content-type'              => 'application/x-www-form-urlencoded',
                    // 'cookie'                    => '.AspNetCore.Antiforgery.VyLW6ORzMgk=CfDJ8FzZ3_RhvnVLrzYYd-iXmevdUARrUNOO-ZdQIRRZhaP20po73vBN8b6IFy42t53w23QB3u3EeF581wP4sxAnK3sBUKdS3R9qfzzY7MFkV7EhsNlNXAF4EiFYDe7qVyZxiW-ym9M-XPyn14HkJoAv-Kw; CustomStore=f05a4db4-d35d-4b18-b81a-e3905dcc7d0a',
                    'dnt'                       => '1',
                    'origin'                    => 'https://webads3.com',
                    'referer'                   => 'https://webads3.com/',
                    'sec-ch-ua'                 => '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
                    'sec-ch-ua-mobile'          => '?0',
                    'sec-ch-ua-platform'        => '"macOS"',
                    'sec-fetch-dest'            => 'document',
                    'sec-fetch-mode'            => 'navigate',
                    'sec-fetch-site'            => 'same-origin',
                    'sec-fetch-user'            => '?1',
                    'upgrade-insecure-requests' => '1',
                    'user-agent'                => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
                ],
                'form_params' => [
                    'Username' => '3245',
                    'Password' => '3245',
                    'Hwid' => '28d271e3a49fa84528978a9748b30208',
                    'Browser' => 'Chrome',
                    //  '__RequestVerificationToken' => 'CfDJ8FzZ3_RhvnVLrzYYd-iXmev5Vjyhf1g4DZKBSpql_dbKCGPAFPTwHnWGucrqZULUvnN67owhh5Cz90gx7CFPHzGb9G8Ka0oPm-sTF_74pORmXap-t1ngaQU-tY0vR1PvJrxHpDOo2rfq8AxZWAYPGfF'
                ]
            ]);

            $cookieJar = $client->getConfig('cookies');

            $str_cookies = "DOMAIN_REQUEST_SERVER=https://www.project.webads3.com;";
            foreach ($cookieJar->toArray() as $item) {

                if ($item['Name'] == 'User_Cookie') {
                    $str_cookies  =  $str_cookies . ($item['Name'] . "=" . $item['Value']);
                }
            }


            return [
                'success' => true,
                'link' => $link,
                'name' => 'Haynespro Engin073_MHHAUTO',
                'cookies' => $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// IdentifixReyesAcount
    static public function getCookiesAllDataUSAccountReyesVpsScaper()
    {
        try {
            $client = new \GuzzleHttp\Client();

            $response3 = $client->get("http://**************/api/drcarcookies/cookies_alldata_us", [
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                ]
            ]);

            $str_cookies = "";

            $content1 = ($response3->getBody()->getContents());
            $json =  json_decode($content1, true);
            $json_data_decodes =  $json['json_data_decode'] ?? [];
            foreach ($json_data_decodes  as $itemCookie) {
                //  if ($itemCookie['name'] == 'ASP.NET_SessionId') {
                $str_cookies =    $str_cookies . $itemCookie['name'] . '=' . $itemCookie['value'] . ";";
                //     break;
                //  }
            }

            return [
                'success' => true,
                'link' => "ScaperVPS DrCar",
                'name' => 'Alldata US Reyes account - ScaperVPS',
                'cookies' => $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                "error" => $e->getMessage()
            ];
        }
    }

    ////////////////////////////////////////////////// US
    static public function getCookiesAllDataUSDiagnostic()
    {
        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $link = "http://185.81.152.38/New/index.php?password=User14&sysinfo";
            $body = file_get_contents($link);
            $content =   str_replace("\x00", "", $body);
            $index1 =  strpos($content, "cookies");
            $cok = substr($content,  $index1 + 9, strlen($content) -  $index1 - 10);

            return [
                'success' => true,
                'link' => $link,
                'name' => 'US diagnostic',
                'cookies' => CookiesUtils::cookiesArrayToStr($cok)
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// AUTODATA
    static public function getCookiesAutoDataSV1()
    {
        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $link = "http://84.54.14.167/index.php?pass=1207&mac=BHFKQQ35AAAG7W9SAXNERZ0M1NNGJ73CTSP02Z8Y21N2DZ48A4X0";
            $body = file_get_contents($link);


            $IV = "RpGWyTedxA4IAdJro4Y7Ow==";
            $KEY = "oUFlJVLJ9z2EkzJGk1qyWKVYihEAtv77NyKlNGatlUE=";

            $_IV = base64_decode($IV);
            $_KEY = base64_decode($KEY);

            $s =  $body;
            $decrypted = openssl_decrypt(base64_decode($s), 'aes-256-cbc', $_KEY, OPENSSL_RAW_DATA, $_IV);

            $cookies = json_decode($decrypted, true)['cookies'];

            $cok =   [];

            foreach ($cookies as $item) {
                array_push($cok, [
                    "domain" => $item['Domain'],
                    "expirationDate" => $item['Expires'],
                    "name" => $item['Name'],
                    "path" => $item['Path'],
                    "value" => $item['Value'],
                ]);
            }

            $cok = str_replace("\/", '/', json_encode($cok));

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Autodata diagnostic',
                'cookies' => CookiesUtils::cookiesArrayToStr($cok)
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }
    static public function getCookiesAutoDataAutoCarSoftwate()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');

        $proxy = null;
        $configAdmin =      ConfigAdmin::first();

        if ($configAdmin  != null && !empty($configAdmin->proxy_autodata)) {
            $proxy = $configAdmin->proxy_autodata;
        }


        try {
            $link = 'http://autocarsoftware.com/cookiesen.php';
            $client = new Client(
                [
                    'proxy' => "s87ZDs1N:xBRd2S9m@212.192.36.39:62742",
                    'timeout' => 8, // Response timeout
                    'connect_timeout' => 8, // Connection timeout
                ]
            );
            $response = $client->get($link);
            $body =  ($response->getBody()->getContents());

            $array = preg_split("/\r\n|\n|\r/", $body);
            $cokStr =   "";

            foreach ($array as $item) {
                $item_arr = explode("^", $item);

                if (count($item_arr) == 5) {
                    $cokStr =  $cokStr . (($item_arr[0]) . "=" . (urlencode($item_arr[1]))) . ";";
                }
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'autodata autocarsoftware',
                'cookies' =>  $cokStr
            ];
        } catch (Exception $e) {

            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES
    static public function getCookiesAutodataBaseAccount()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');

        $headerArr  =   [];

        unset($headerArr['host']);
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://workshop.autodata-group.com/login?destination=node";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );
            $response1 = $client->request(
                'POST',
                $link,
                [

                    'headers' =>  $headerArr,
                    'form_params' => [
                        "form_build_id" => "form-PTt4kx_0dyNHL5hddKU66GzUHRZ1gOUOxZhzANkTYAI",
                        "form_id" => "user_login",
                        "name" => "cbgraphiclit",
                        "pass" => "!@#Butlineking123456",
                    ],
                ]
            );

            $str_cookies = "";

            $it = $jar->getIterator();
            while ($it->valid()) {
                if ($it->current()->toArray()['Name'] == "SESSd965b47fdd2684807fd560c91c3e21b6") {
                    $str_cookies  = ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']);
                }
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Autodata Base Account',
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }



    ////////////////////////////////////////////////// Mitchell Prodemand Diagnostic
    static public function getCookiesHaynesProDiagnostic()
    {
        return [
            'success' => false
        ];
        //Error
        ////////////////////////////////////////////////// 
        ////////////////////////////////////////////////// 
        ////////////////////////////////////////////////// 
        ////////////////////////////////////////////////// 
        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $link = "http://141.98.115.185/index.php?pass=3649&mac=BHFKQQ35AAAG7W9SAXNERZ0M1NNGJ73CTSP02Z8Y21N2DZ48A4X0";
            $body = file_get_contents($link);


            $IV = "RpGWyTedxA4IAdJro4Y7Ow==";
            $KEY = "oUFlJVLJ9z2EkzJGk1qyWKVYihEAtv77NyKlNGatlUE=";

            $_IV = base64_decode($IV);
            $_KEY = base64_decode($KEY);

            $s =  $body;
            $decrypted = openssl_decrypt(base64_decode($s), 'aes-256-cbc', $_KEY, OPENSSL_RAW_DATA, $_IV);

            $cookies = json_decode($decrypted, true)['cookies'];

            $cok =   [];

            foreach ($cookies as $item) {
                array_push($cok, [
                    "domain" => $item['Domain'],
                    "expirationDate" => $item['Expires'],
                    "name" => $item['Name'],
                    "path" => $item['Path'],
                    "value" => $item['Value'],
                ]);
            }

            $cok = str_replace("\/", '/', json_encode($cok));

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Haynespro diagnostic',
                'cookies' => CookiesUtils::cookiesArrayToStr($cok)
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }
    ////////////////////////////////////////////////// HAYNES
    static public function getCookiesHaynesSV1()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $res = CurlUtils::request('GET', 'http://onlinecatalog2.de/cookies', [
                'cookie' =>  'username=1207; password=ea84a2f9-c3d9-4110-a3fa-cb070e95af28',
            ]);
            $arr = json_decode($res->getResponseBody());

            return [
                'success' => true,
                'link' => 'http://onlinecatalog2.de/cookies',
                'name' => 'Auto Car Diagnostic HayNesPro 1',
                'cookies' => $arr[0]->cookie
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES Autocarsoftware
    static public function getCookiesHaynesproAutocarsoftware()
    {


        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');
        $proxy = null;

        try {
            $link = "http://autocarsoftware.com/cookieshaynes.php";
            $client = new Client(
                [
                    'proxy' => 's87ZDs1N:xBRd2S9m@212.192.36.39:62742',
                    'timeout' => 8, // Response timeout
                    'connect_timeout' => 8, // Connection timeout
                ]
            );
            $response = $client->get($link);
            $body =  ($response->getBody()->getContents());

            $array = preg_split("/\r\n|\n|\r/", $body);
            $cokStr =   "";

            foreach ($array as $item) {
                $item_arr = explode("^", $item);

                if (count($item_arr) == 5) {
                    $cokStr =  $cokStr . (($item_arr[0]) . "=" . (urldecode($item_arr[1]))) . ";";
                }
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Haynespro Autocarsoftware',
                'cookies' => $cokStr
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// MHH AUTO
    static public function getCookiesHaynesproEngin073_MHH_AUTO()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');
        $proxy = null;

        try {

            $link = 'https://webads3.com/';
            $jar = new \GuzzleHttp\Cookie\CookieJar();
            $client = new Client(
                [
                    'proxy' => 's87ZDs1N:xBRd2S9m@212.192.36.39:62742',
                    'timeout' => 8, // Response timeout
                    'connect_timeout' => 8, // Connection timeout
                    'cookies' => $jar
                ]
            );


            $response = $client->post('https://webads3.com/', [
                'headers' => [
                    'authority'                 => 'webads3.com',
                    'accept'                    => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'accept-language'           => 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5,zh-CN;q=0.4,zh;q=0.3,ca;q=0.2,ko;q=0.1,de;q=0.1,ar;q=0.1,hi;q=0.1',
                    'cache-control'             => 'max-age=0',
                    'content-type'              => 'application/x-www-form-urlencoded',
                    // 'cookie'                    => '.AspNetCore.Antiforgery.VyLW6ORzMgk=CfDJ8FzZ3_RhvnVLrzYYd-iXmevdUARrUNOO-ZdQIRRZhaP20po73vBN8b6IFy42t53w23QB3u3EeF581wP4sxAnK3sBUKdS3R9qfzzY7MFkV7EhsNlNXAF4EiFYDe7qVyZxiW-ym9M-XPyn14HkJoAv-Kw; CustomStore=f05a4db4-d35d-4b18-b81a-e3905dcc7d0a',
                    'dnt'                       => '1',
                    'origin'                    => 'https://webads3.com',
                    'referer'                   => 'https://webads3.com/',
                    'sec-ch-ua'                 => '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
                    'sec-ch-ua-mobile'          => '?0',
                    'sec-ch-ua-platform'        => '"macOS"',
                    'sec-fetch-dest'            => 'document',
                    'sec-fetch-mode'            => 'navigate',
                    'sec-fetch-site'            => 'same-origin',
                    'sec-fetch-user'            => '?1',
                    'upgrade-insecure-requests' => '1',
                    'user-agent'                => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
                ],
                'form_params' => [
                    'Username' => '3245',
                    'Password' => '3245',
                    'Hwid' => '28d271e3a49fa84528978a9748b30208',
                    'Browser' => 'Chrome',
                    //  '__RequestVerificationToken' => 'CfDJ8FzZ3_RhvnVLrzYYd-iXmev5Vjyhf1g4DZKBSpql_dbKCGPAFPTwHnWGucrqZULUvnN67owhh5Cz90gx7CFPHzGb9G8Ka0oPm-sTF_74pORmXap-t1ngaQU-tY0vR1PvJrxHpDOo2rfq8AxZWAYPGfF'
                ]
            ]);

            $cookieJar = $client->getConfig('cookies');

            $str_cookies = "DOMAIN_REQUEST_SERVER=https://www.project.webads3.com;";
            foreach ($cookieJar->toArray() as $item) {

                if ($item['Name'] == 'User_Cookie') {
                    $str_cookies  =  $str_cookies . ($item['Name'] . "=" . $item['Value']);
                }
            }


            return [
                'success' => true,
                'link' => $link,
                'name' => 'Haynespro Engin073_MHHAUTO',
                'cookies' => $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES
    static public function getCookiesHaynesTruckSV1()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $link = "http://autocarsoftware.com/cookieshaynestruck.php";
            $body = file_get_contents($link);

            $array = preg_split("/\r\n|\n|\r/", $body);
            $cok =   [];

            foreach ($array as $item) {
                $item_arr = explode("^", $item);


                if (count($item_arr) == 5) {

                    array_push($cok, [
                        "domain" => $item_arr[2],
                        "expirationDate" =>  $item_arr[4],
                        "name" =>  $item_arr[0],
                        "path" =>  $item_arr[3],
                        "value" =>  $item_arr[1],
                    ]);
                }
            }

            $cok = str_replace("\/", '/', json_encode($cok));


            return [
                'success' => true,
                'link' => $link,
                'name' => 'haynespro truck autocarsoftware',
                'cookies' => CookiesUtils::cookiesArrayToStr($cok)
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }


    ////////////////////////////////////////////////// HAYNES
    static public function getCookiesHaynesXdiag3()
    {
        return [
            'success' => false
        ];
        //Error
        ////////////////////////////////////////////////// 
        ////////////////////////////////////////////////// 
        ////////////////////////////////////////////////// 
        ////////////////////////////////////////////////// 

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        $headerArr  =   [];

        unset($headerArr['host']);
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://workshop.x-diag.info/Login";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );
            $response1 = $client->request(
                'POST',
                $link,
                [

                    'headers' =>  $headerArr,
                    'form_params' => [
                        "login" => "983690003496",
                        "password" => "123456",
                        "submit" => "Login",
                    ],
                ]
            );

            $str_cookies = "";

            $it = $jar->getIterator();
            while ($it->valid()) {
                if ($it->current()->toArray()['Name'] == "JSESSIONID") {
                    $str_cookies  = ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']);
                }
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'haynespro xDia3',
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES
    static public function getCookiesHaynesDiagzone()
    {

        //Get token diagzone
        try {
            $client = new \GuzzleHttp\Client();
            $response = $client->post('https://diagboss.ch/api/v2/login', [
                'headers' => [
                    'Cookie' => 'psessid=U2JqUERBRmRxdVhEOHo5Vm0xT0s0QT09'
                ],
                'multipart' => [
                    [
                        'name' => 'app_id',
                        'contents' => '6021'
                    ],
                    [
                        'name' => 'ver',
                        'contents' => '5.3.0'
                    ],
                    [
                        'name' => 'login_key',
                        'contents' => 'H989149600903'
                    ],
                    [
                        'name' => 'password',
                        'contents' => 'H600903'
                    ],
                    [
                        'name' => 'time',
                        'contents' => '1710244563676'
                    ],
                    [
                        'name' => 'type',
                        'contents' => '0'
                    ],
                    [
                        'name' => 'device_token',
                        'contents' => ''
                    ]
                ]
            ]);
        } catch (Exception $e) {

            return [
                'success' => false
            ];
        }

        $jsonToken =  json_decode($response->getBody()->getContents(), true);
        $tokenDiagzone = $jsonToken['data']['token'];

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');
        $headerArr  =   [];

        unset($headerArr['host']);
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://diagboss.ch/api/v2/haynes');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ]);
        curl_setopt($ch, CURLOPT_COOKIE, 'psessid=U2JqUERBRmRxdVhEOHo5Vm0xT0s0QT09;jsessid=NzZBSnlXdk9mR2xrVW40QXJTRWtHZz09;$Version=1');
        curl_setopt($ch, CURLOPT_POSTFIELDS, [
            'app_id' => '3',
            'lang' => 'vi',
            'token' => $tokenDiagzone,
        ]);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $response = curl_exec($ch);
        curl_close($ch);

        $content1 = ($response);
        $json =  json_decode($content1, true);
        $link =  $json['url'];

        try {

            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );
            $response1 = $client->request(
                'GET',
                $link,
                [

                    'headers' =>  $headerArr,
                ]
            );

            $str_cookies = "DOMAIN_REQUEST_SERVER=https://hp.webdiag.name;";

            $it = $jar->getIterator();

            while ($it->valid()) {
                $str_cookies  =  $str_cookies . ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']);
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'haynespro Diagzone',
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES PRO5
    static public function getCookiesHaynesPro5()
    {

        // //Get token diagzone
        // try {
        //     $client = new \GuzzleHttp\Client();
        //     $response = $client->post('http://l.diagnosticpro.lv/?action=passport_service.login', [
        //         'headers' => [
        //             'Cookie' => 'psessid=U2JqUERBRmRxdVhEOHo5Vm0xT0s0QT09'
        //         ],
        //         'multipart' => [
        //             [
        //                 'name' => 'app_id',
        //                 'contents' => '6023'
        //             ],
        //             [
        //                 'name' => 'ver',
        //                 'contents' => '5.3.0'
        //             ],
        //             [
        //                 'name' => 'login_key',
        //                 'contents' => '986497002178'
        //             ],
        //             [
        //                 'name' => 'password',
        //                 'contents' => '111222'
        //             ],
        //             [
        //                 'name' => 'time',
        //                 'contents' => '1710244563676'
        //             ],
        //             [
        //                 'name' => 'type',
        //                 'contents' => '0'
        //             ],
        //             [
        //                 'name' => 'device_token',
        //                 'contents' => ''
        //             ]
        //         ]
        //     ]);
        // } catch (Exception $e) {

        //     return [
        //         'success' => false
        //     ];
        // }

        // $jsonToken =  json_decode($response->getBody()->getContents(), true);
        // $tokenDiagzone = $jsonToken['data']['token'];


        $jar = new \GuzzleHttp\Cookie\CookieJar();
        $link =  'https://hp-web.in/?vrid=986497002178edfe499df4a5a29c24f79600420626bf';
        try {
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );
            $response1 = $client->request(
                'GET',
                $link,
                []
            );

            $str_cookies = "DOMAIN_REQUEST_SERVER=https://hd.hp-web.in;";

            $it = $jar->getIterator();

            while ($it->valid()) {
                $str_cookies  =  $str_cookies . ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']);
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Haynespro Xpro5',
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES
    static public function getCookiesHaynesDXTSV1()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        $headerArr  =   [];

        unset($headerArr['host']);
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://www.workshopdata.com/touch/site/layout/wsdLogin";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );
            $response1 = $client->request(
                'POST',
                $link,
                [

                    'headers' =>  $headerArr,
                    'form_params' => [
                        "username" => "HB4545P7",
                        "password" => "dxt888",
                        "submit" => "Login",
                    ],
                ]
            );

            $str_cookies = "";

            $it = $jar->getIterator();
            while ($it->valid()) {
                if ($it->current()->toArray()['Name'] == "JSESSIONID") {
                    $str_cookies  = ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']);
                }
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'haynespro DXT',
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES CAR accout
    static public function getCookiesHaynesproDxdata()
    {

        return [
            'success' => false
        ];
        //Error
        ////////////////////////////////////////////////// 
        ////////////////////////////////////////////////// 
        ////////////////////////////////////////////////// 
        ////////////////////////////////////////////////// 

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        $headerArr  =   [];

        unset($headerArr['host']);
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://www.workshopdata.com/touch/site/layout/wsdLogin";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );
            $response1 = $client->request(
                'POST',
                $link,
                [

                    'headers' =>  $headerArr,
                    'form_params' => [
                        "username" => "carsdbdata",
                        "password" => "cars4455yh",
                        "submit" => "Login",
                    ],
                ]
            );

            $str_cookies = "";

            $it = $jar->getIterator();
            while ($it->valid()) {
                if ($it->current()->toArray()['Name'] == "JSESSIONID") {
                    $str_cookies  = ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']);
                }
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'haynespro DXT',
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES CAR accout CartuningSetup
    static public function getCookiesHaynesproDr()
    {



        function  getAccountHaynesproDr()
        {

            $accounts = DomainConfigHelper::getConfig("accounts_dr_fix_haynespro");

            $cacheKey = 'next_haynespro_index';
            $nextIndex = Cache::get($cacheKey, 0);
            if ($nextIndex >= count($accounts)) {
                $nextIndex = 0;
            }
            $nextAccount = $accounts[$nextIndex];
            $nextIndex = ($nextIndex + 1) % count($accounts);
            Cache::put($cacheKey, $nextIndex);

            return $nextAccount;
        }


        $accNext = getAccountHaynesproDr();
        $username = $accNext[0];
        $password = $accNext[1];


        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        $headerArr  =   [];

        unset($headerArr['host']);
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://www.workshopdata.com/touch/site/layout/wsdLogin";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );

            $proxy = null;
            $configAdmin =      ConfigAdmin::first();
            if ($proxy === null) {
                if ($configAdmin  != null && !empty($configAdmin->proxy_haynespro)) {
                    $proxy = $configAdmin->proxy_haynespro;
                }
            }

            $response1 = $client->request(
                'POST',
                $link,
                [
                    'proxy' => $proxy,
                    'headers' => [
                        'User-Agent'                => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
                        'Accept'                    => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language'           => 'en-US,en;q=0.5',
                        'Accept-Encoding'           => 'gzip, deflate, br, zstd',
                        'Content-Type'              => 'application/x-www-form-urlencoded',
                        'Origin'                    => 'https://www.workshopdata.com',
                        'Connection'                => 'keep-alive',
                        'Referer'                   => 'https://www.workshopdata.com/touch/site/layout/wsdLogin?urlDownloadSvgViewer=http%3A%2F%2Fdownload.adobe.com%2Fpub%2Fadobe%2Fmagic%2Fsvgviewer%2Fwin%2F3.x%2F3.03%2Fen%2FSVGView.exe',
                        'Upgrade-Insecure-Requests' => '1',
                        'Sec-Fetch-Dest'            => 'document',
                        'Sec-Fetch-Mode'            => 'navigate',
                        'Sec-Fetch-Site'            => 'same-origin',
                        'Sec-Fetch-User'            => '?1',
                        'Priority'                  => 'u=0, i',
                        'TE'                        => 'trailers'
                    ],
                    'form_params' => [
                        "username" => $username,
                        "password" => $password,
                        "submit" => "Login",
                    ],
                ]
            );

            $str_cookies = "";

            $it = $jar->getIterator();
            while ($it->valid()) {
                if ($it->current()->toArray()['Name'] == "JSESSIONID" || $it->current()->toArray()['Name'] == "productIdV2" || $it->current()->toArray()['Name'] == "styleIdV2") {
                    $str_cookies  =  $str_cookies . ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']) . ";";
                }
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }


            return [
                'success' => true,
                'link' => $link,
                'name' => 'Haynespro Dr ' . $username,
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES CAR accout CartuningSetup
    static public function getCookiesHaynesproTruckDr()
    {



        function  getAccountHaynesproTruckDr()
        {

            $accounts = DomainConfigHelper::getConfig("accounts_dr_fix_haynespro_truck");

            $cacheKey = 'next_haynespro_index';
            $nextIndex = Cache::get($cacheKey, 0);
            if ($nextIndex >= count($accounts)) {
                $nextIndex = 0;
            }
            $nextAccount = $accounts[$nextIndex];
            $nextIndex = ($nextIndex + 1) % count($accounts);
            Cache::put($cacheKey, $nextIndex);

            return $nextAccount;
        }


        $accNext = getAccountHaynesproTruckDr();
        $username = $accNext[0];
        $password = $accNext[1];


        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        $headerArr  =   [];

        unset($headerArr['host']);
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://www.workshopdata.com/touch/site/layout/wsdLogin";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );

            $proxy = null;
            $configAdmin =      ConfigAdmin::first();
            if ($proxy === null) {
                if ($configAdmin  != null && !empty($configAdmin->proxy_haynespro)) {
                    $proxy = $configAdmin->proxy_haynespro;
                }
            }

            $response1 = $client->request(
                'POST',
                $link,
                [
                    'proxy' => $proxy,
                    'headers' => [
                        'User-Agent'                => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
                        'Accept'                    => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language'           => 'en-US,en;q=0.5',
                        'Accept-Encoding'           => 'gzip, deflate, br, zstd',
                        'Content-Type'              => 'application/x-www-form-urlencoded',
                        'Origin'                    => 'https://www.workshopdata.com',
                        'Connection'                => 'keep-alive',
                        'Referer'                   => 'https://www.workshopdata.com/touch/site/layout/wsdLogin?urlDownloadSvgViewer=http%3A%2F%2Fdownload.adobe.com%2Fpub%2Fadobe%2Fmagic%2Fsvgviewer%2Fwin%2F3.x%2F3.03%2Fen%2FSVGView.exe',
                        'Upgrade-Insecure-Requests' => '1',
                        'Sec-Fetch-Dest'            => 'document',
                        'Sec-Fetch-Mode'            => 'navigate',
                        'Sec-Fetch-Site'            => 'same-origin',
                        'Sec-Fetch-User'            => '?1',
                        'Priority'                  => 'u=0, i',
                        'TE'                        => 'trailers'
                    ],
                    'form_params' => [
                        "username" => $username,
                        "password" => $password,
                        "submit" => "Login",
                    ],
                ]
            );

            $str_cookies = "";

            $it = $jar->getIterator();
            while ($it->valid()) {
                if ($it->current()->toArray()['Name'] == "JSESSIONID" || $it->current()->toArray()['Name'] == "productIdV2" || $it->current()->toArray()['Name'] == "styleIdV2") {
                    $str_cookies  =  $str_cookies . ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']) . ";";
                }
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }


            return [
                'success' => true,
                'link' => $link,
                'name' => 'Haynespro Truck Dr ' . $username,
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES CAR accout CartuningSetup
    static public function getCookiesHaynesproCartuning()
    {


        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        $headerArr  =   [];

        unset($headerArr['host']);
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://www.workshopdata.com/touch/site/layout/wsdLogin";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );
            $response1 = $client->request(
                'POST',
                $link,
                [

                    'headers' =>  $headerArr,
                    'form_params' => [
                        "username" => "<EMAIL>",
                        "password" => "AL2022TN",
                        "submit" => "Login",
                    ],
                ]
            );

            $str_cookies = "";

            $it = $jar->getIterator();
            while ($it->valid()) {
                if ($it->current()->toArray()['Name'] == "JSESSIONID") {
                    $str_cookies  = ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']);
                }
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }

            //Lấy _csrf
            $response  = HaynesTruckUtilsWeb::getResponse('https://www.workshopdata.com/touch/site/layout/settingsContent', "GET", $str_cookies, null);
            $type = $response->getHeader('Content-Type')[0] ?? "";
            $content = ($response->getBody()->getContents());
            if (str_contains($type, 'text/html')) {
                $_csrf = "";

                $dom = HtmlDomParser::str_get_html($content);
                if ($dom  != null) {
                    $inputElements = $dom->find('input');
                    foreach ($inputElements as $element) {
                        if ($element->getAttribute('name') === '_csrf') {
                            // Lấy giá trị của thuộc tính value
                            $csrfValue = $element->getAttribute('value');
                            $_csrf = $csrfValue;
                            break; // Dừng vòng lặp sau khi tìm thấy phần tử cần trích xuất giá trị
                        }
                    }
                }
            }

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://www.workshopdata.com/touch/site/layout/settingsContent');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'authority: www.workshopdata.com',
                'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language: vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5,zh-CN;q=0.4,zh;q=0.3,ca;q=0.2,ko;q=0.1,de;q=0.1,ar;q=0.1,hi;q=0.1',
                'cache-control: max-age=0',
                'content-type: application/x-www-form-urlencoded',
                'dnt: 1',
                'DNT: 1',
                'origin: https://www.workshopdata.com',
                'referer: https://www.workshopdata.com/touch/site/layout/settingsContent',
                'Referer: https://www.workshopdata.com/touch/site/layout/makesOverview?urlDownloadSvgViewer=http%3A%2F%2Fdownload.adobe.com%2Fpub%2Fadobe%2Fmagic%2Fsvgviewer%2Fwin%2F3.x%2F3.03%2Fen%2FSVGView.exe',
                'sec-ch-ua: "Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "macOS"',
                'sec-fetch-dest: document',
                'sec-fetch-mode: navigate',
                'sec-fetch-site: same-origin',
                'sec-fetch-user: ?1',
                'upgrade-insecure-requests: 1',
                'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
                'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
                'if-modified-since: Tue, 12 Mar 2024 16:36:30 GMT',
            ]);
            curl_setopt($ch, CURLOPT_COOKIE, $str_cookies);
            curl_setopt($ch, CURLOPT_POSTFIELDS, "includeWorkshopInfoOnPrints=true&_includeWorkshopInfoOnPrints=on&oldPassword=&newPassword=&confirmPassword=&selectLanguageCode=en&selectCountry=_GB&measurement=0&maintType=1&summarisedMode=true&_summarisedMode=on&manufactureCodesMode=true&_manufactureCodesMode=on&printServiceTimesMode=true&_printServiceTimesMode=on&skipTruckAxleSelection=true&_skipTruckAxleSelection=on&selectCurrency=GBP&_csrf=$_csrf");
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            $response = curl_exec($ch);
            curl_close($ch);

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Haynespro CartuningSetup',
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// HAYNES truck accout
    static public function getCookiesHaynesproTruckDxdata()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        $headerArr  =   [];

        unset($headerArr['host']);
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://www.workshopdata.com/touch/site/layout/wsdLogin";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]
            );
            $response1 = $client->request(
                'POST',
                $link,
                [

                    'headers' =>  $headerArr,
                    'form_params' => [
                        "username" => "trucksdata",
                        "password" => "trucks4455",
                        "submit" => "Login",
                    ],
                ]
            );

            $str_cookies = "";

            $it = $jar->getIterator();
            while ($it->valid()) {
                if ($it->current()->toArray()['Name'] == "JSESSIONID") {
                    $str_cookies  = ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']);
                }
                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'haynespro DXT',
                'cookies' =>  $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// US
    static public function getCookiesIdentifixAutocarsoftware()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');
        $proxy = null;
        $configAdmin =      ConfigAdmin::first();

        if ($configAdmin  != null && !empty($configAdmin->proxy_identifix)) {
            $proxy = $configAdmin->proxy_identifix;
        }

        try {
            $link = 'http://autocarsoftware.com/cookiesidentifix.php';
            $client = new Client(
                [
                    'proxy' => 's87ZDs1N:xBRd2S9m@212.192.36.39:62742',
                    'timeout' => 8, // Response timeout
                    'connect_timeout' => 8, // Connection timeout
                ]
            );
            $response = $client->get($link);
            $body =  ($response->getBody()->getContents());

            $array = preg_split("/\r\n|\n|\r/", $body);
            $cokStr =   "";

            foreach ($array as $item) {
                $item_arr = explode("@", $item);

                if (count($item_arr) == 5) {

                    if (($item_arr[0]) == 'ASP.NET_SessionId') {
                        $cokStr =  $cokStr . (($item_arr[0]) . "=" . (urlencode($item_arr[1]))) . ";";
                    }
                }
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'identifix autocarsoftware',
                'cookies' => $cokStr
            ];
        } catch (Exception $e) {

            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// Identifix
    static public function getCookiesIdentifixFromAccountEyes()
    {
        try {
            $chromeDriverPath = base_path('chromedriver');
            $client = \Symfony\Component\Panther\Client::createChromeClient($chromeDriverPath, null, [
                'port' => 4357,
            ]);
            $client->request('GET', 'https://dh.identifix.com/');

            $crawler = $client->waitFor('#UserName');

            $myInput = $crawler->filter('input[id=UserName]');
            $myInput->sendKeys('DrH92230');

            $myInput = $crawler->filter('input[id=Password]');
            $myInput->sendKeys('starfire');

            $client->executeScript('document.querySelector("#Login").click()');

            try {
                $crawler = $client->waitFor('.logout-link', 8);
            } catch (TimeoutException $e) {

                try {
                    $crawler = $client->waitFor('.license-headline', 8);
                    $client->executeScript('document.querySelector("#select-all-checkboxes").click()');
                    $client->executeScript('document.querySelector("#bt-update").click()');
                } catch (TimeoutException $e) {
                }
            }

            $cookies_str = "";

            $crawler = $client->waitFor('#new-recent-vehicle-tab');
            $SessionId = $client->getCookieJar()->get('ASP.NET_SessionId');




            if ($SessionId  != null)

                if ($SessionId->getName() == 'ASP.NET_SessionId') {
                    $cookies_str =   $cookies_str . $SessionId->getName() . "=" . $cookies_str . $SessionId->getValue() . ";";
                }

            return [
                'success' => true,
                'link' => "Panther php FirefoxClient",
                'name' => 'Identifix Reyes original account',
                'cookies' => $cookies_str
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                "error" => $e->getMessage()
            ];
        }
    }

    ////////////////////////////////////////////////// IdentifixReyesAcount
    static public function getCookiesIdentifixReyesAcountScaperVPS()
    {
        try {
            $client = new \GuzzleHttp\Client();
            $ipAccountScaper = DomainConfigHelper::getConfig('ip_vps_fix_account_identifix');

            $ipAccountScaper = "**************";
            $response3 = $client->get("http://$ipAccountScaper/api/drcarcookies/cookies_identifix", [
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                ]
            ]);

            $str_cookies = "";

            $content1 = ($response3->getBody()->getContents());
            $json =  json_decode($content1, true);
            $json_data_decodes =  $json['json_data_decode'] ?? [];
            foreach ($json_data_decodes  as $itemCookie) {
                if ($itemCookie['name'] == 'ASP.NET_SessionId') {
                    $str_cookies =    $str_cookies . $itemCookie['name'] . '=' . $itemCookie['value'] . ";";
                    break;
                }
            }

            return [
                'success' => true,
                'link' => "ScaperVPS DrCar",
                'name' => 'Identifix Reyes account - ScaperVPS',
                'cookies' => $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                "error" => $e->getMessage()
            ];
        }
    }

    ////////////////////////////////////////////////// Identifix
    static public function getCookiesIdentifixFromAccountFB1Chia()
    {
        try {
            $chromeDriverPath = base_path('chromedriver');
            $client = \Symfony\Component\Panther\Client::createChromeClient($chromeDriverPath, null, [
                'port' => 4357,
            ]);
            $client->request('GET', 'https://dh.identifix.com/');

            $crawler = $client->waitFor('#UserName');

            $myInput = $crawler->filter('input[id=UserName]');
            $myInput->sendKeys('ARTEMISA1');

            $myInput = $crawler->filter('input[id=Password]');
            $myInput->sendKeys('ARTETIRES11');

            $client->executeScript('document.querySelector("#Login").click()');

            try {
                $crawler = $client->waitFor('.logout-link', 8);
            } catch (TimeoutException $e) {

                try {
                    $crawler = $client->waitFor('.license-headline', 8);
                    $client->executeScript('document.querySelector("#select-all-checkboxes").click()');
                    $client->executeScript('document.querySelector("#bt-update").click()');
                } catch (TimeoutException $e) {
                }
            }

            $cookies_str = "";

            $crawler = $client->waitFor('#new-recent-vehicle-tab');
            $SessionId = $client->getCookieJar()->get('ASP.NET_SessionId');




            if ($SessionId  != null)

                if ($SessionId->getName() == 'ASP.NET_SessionId') {
                    $cookies_str =   $cookies_str . $SessionId->getName() . "=" . $cookies_str . $SessionId->getValue() . ";";
                }

            return [
                'success' => true,
                'link' => "Panther php FirefoxClient",
                'name' => 'Identifix FB1Chia account',
                'cookies' => $cookies_str
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                "error" => $e->getMessage()
            ];
        }
    }
    ////////////////////////////////////////////////// MitchellRepairCenter
    static public function getCookiesMitchellRepairCenterSV1()
    {
        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $link = "http://autocarsoftware.com/cookiesmitchell.php";
            $body = file_get_contents($link);

            $array = preg_split("/\r\n|\n|\r/", $body);
            $cok =   [];

            foreach ($array as $item) {
                $item_arr = explode("^", $item);

                if (count($item_arr) == 5) {

                    array_push($cok, [
                        "domain" => $item_arr[2],
                        "expirationDate" =>  $item_arr[4],
                        "name" =>  $item_arr[0],
                        "path" =>  $item_arr[3],
                        "value" =>  $item_arr[1],
                    ]);
                }
            }

            $cok = str_replace("\/", '/', json_encode($cok));


            return [
                'success' => true,
                'link' => $link,
                'name' => 'MitchellRepairCenter autocarsoftware',
                'cookies' => CookiesUtils::cookiesArrayToStr($cok)
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// Mitchell Prodemand SV autosoftware
    static public function getCookiesMitchellProdemandAutocarsoftware()
    {
        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');

        $proxy = null;
        $configAdmin =      ConfigAdmin::first();
        if ($proxy == null) {
            if ($configAdmin  != null && !empty($configAdmin->proxy_mitchell_prodemand)) {
                $proxy = 's87ZDs1N:xBRd2S9m@212.192.36.39:62742';
            }
        }

        try {

            $link = 'http://autocarsoftware.com/cookiesmitchellprod.php';
            $client = new Client(
                [
                    'proxy' => $proxy,
                ]
            );
            $response = $client->get($link);
            $body =  ($response->getBody()->getContents());

            $array = preg_split("/\r\n|\n|\r/", $body);
            $cokStr =   "";

            foreach ($array as $item) {
                $item_arr = explode("^", $item);

                if (count($item_arr) == 5) {
                    if ($item_arr[0]
                        //.  == 'v3.tusc1.sessionTicket'
                    ) {
                        $cokStr =  $cokStr . (($item_arr[0]) . "=" . (urlencode($item_arr[1]))) . ";";
                    }
                }
            }

            //  $cokStr = str_replace("/",'%2f',  $cokStr );

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Prodemand autocarsoftware',
                'cookies' =>  $cokStr
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// Mitchell Prodemand Diagnostic
    static public function getCookiesMitchellProdemandDiagnostic()
    {
        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $link = "https://zalo.me/index.php?pass=2994&mac=96R3TN7GVYSMYDNZJJRKT8RT06RKWM1RPYY2ZP8AZCWVMTQTD2B0";
            $body = file_get_contents($link);


            $IV = "RpGWyTedxA4IAdJro4Y7Ow==";
            $KEY = "oUFlJVLJ9z2EkzJGk1qyWKVYihEAtv77NyKlNGatlUE=";

            $_IV = base64_decode($IV);
            $_KEY = base64_decode($KEY);

            $s =  $body;
            $decrypted = openssl_decrypt(base64_decode($s), 'aes-256-cbc', $_KEY, OPENSSL_RAW_DATA, $_IV);

            $cookies = json_decode($decrypted, true)['cookies'];

            $cok =   [];

            foreach ($cookies as $item) {
                array_push($cok, [
                    "domain" => $item['Domain'],
                    "expirationDate" => $item['Expires'],
                    "name" => $item['Name'],
                    "path" => $item['Path'],
                    "value" => $item['Value'],
                ]);
            }

            $cok = str_replace("\/", '/', json_encode($cok));

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Prodemand diagnostic',
                'cookies' => CookiesUtils::cookiesArrayToStr($cok)
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// Ford PTS
    static public function getCookiesFordPTSScaperVPS()
    {
        try {

            $client = new \GuzzleHttp\Client();
            $ipAccountScaper = DomainConfigHelper::getConfig('ip_vps_fix_account_ford_pts');

            // $ipAccountScaper = "**************";

            $account = AccountItem::where('service', 'MITCHELL_PRODEMAND')->first();
            $username =  $account->username;
            $pass =  $account->password;

            $configAdmin =      ConfigAdmin::first();
            $proxy = "";

            if ($configAdmin  != null && !empty($configAdmin->proxy_mitchell_prodemand)) {
                $proxy = $configAdmin->proxy_mitchell_prodemand;
            }



            $response3 = $client->get("http://$ipAccountScaper/api/drcarcookies/cookies_ford_pts", [
                'headers' => [
                    'User-Agent'                => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                ],
                'timeout' => 200,
                'connect_timeout' => 200,
            ]);

            $str_cookies = "";

            $content1 = ($response3->getBody()->getContents());
            $json =  json_decode($content1, true);
            $cookies =  $json['json_data'] ?? [];
            foreach ($cookies  as $itemCookie) {
                // if ($itemCookie['name'] == 'v3.tusc1.sessionTicket') {
                $str_cookies =    $str_cookies . $itemCookie['name'] . '=' . $itemCookie['value'] . ";";
                // }
            }

            return [
                'success' => true,
                'link' => "ScaperVPS DrCar",
                'name' => 'Ford PTS account - ScaperVPS',
                'cookies' => $str_cookies,
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                "error" => $e->getMessage()
            ];
        }
    }

    ////////////////////////////////////////////////// ProdemandReyesAcount
    static public function getCookiessMitchellProdemandReyesAcount()
    {
        try {
            $chromeDriverPath = base_path('chromedriver');
            $firefoxDriverPath = base_path('geckodriver');
            $client = \Symfony\Component\Panther\Client::createFirefoxClient($firefoxDriverPath, null, [
                'port' => 4458,
            ]);
            $client->request('GET', 'https://aui.mitchell1.com/Login?y=tusc1&exitUrl=https://www.prodemand.com&rememberPassword=True&autoLogin=True');

            $crawler = $client->waitFor('#username');

            $myInput = $crawler->filter('input[id=username]');
            $myInput->sendKeys('Truckramos3');

            $myInput = $crawler->filter('input[id=password]');
            $myInput->sendKeys('Anubis13');

            $client->executeScript('document.querySelector("#loginButton").click()');

            try {
                $crawler = $client->waitFor('#vehicleSelector', 8);
            } catch (TimeoutException $e) {
                //chỗ này xử lý bỏ để checkox bỏ hết Sessions
                // try {
                //     $crawler = $client->waitFor('.license-headline', 8);
                //     $client->executeScript('document.querySelector("#select-all-checkboxes").click()');
                //     $client->executeScript('document.querySelector("#bt-update").click()');
                // } catch (TimeoutException $e) {
                // }
            }

            $cookies_str = "";

            $crawler = $client->waitFor('#vehicleSelector', 3);
            $SessionId = $client->getCookieJar()->get('v3.tusc1.sessionTicket');

            if ($SessionId  != null)

                if ($SessionId->getName() == 'v3.tusc1.sessionTicket') {
                    $cookies_str =   $cookies_str . $SessionId->getName() . "=" . $cookies_str . $SessionId->getValue() . ";";
                }
            return [
                'success' => true,
                'link' => "Panther php FirefoxClient",
                'name' => 'Prodemand Reyes original account',
                'cookies' => $cookies_str
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                "error" => $e->getMessage()
            ];
        }
    }


    ////////////////////////////////////////////////// ProdemandReyesAcount
    static public function getCookiessMitchellProdemandReyesAcountV2()
    {
        try {

            $jar = new \GuzzleHttp\Cookie\CookieJar();

            $proxy = 'zproxy.lum-superproxy.io:22225';
            $proxyauth = 'brd-customer-hl_70855a58-zone-zone1-ip-*************:wnft28fcc3y8';

            $proxy = "http://$proxyauth@$proxy";

            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar
                ]

            );
            $response1 = $client->get('https://aui.mitchell1.com/Login?y=tusc1&exitUrl=https://www.prodemand.com&rememberPassword=True&autoLogin=True', [
                'proxy' =>  $proxy,
                'headers' => [
                    'Accept'                    => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                    'Accept-Language'           => 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
                    'Cache-Control'             => 'max-age=0',
                    'Connection'                => 'keep-alive',
                    //  'Cookie'                    => '__m1auitoken=5FA798FDE4C755E86A2BE2613310DC2F918688F0EDE6D00FA3C64E0BD961208F12D096D67A6128DAABF0D13658F2B61623283E528E175C3193F491CC6AD33A6C7D11862370E0255ED85246EDC08FAA5BB093AE56AFB3E5E304ACA1922C192919747F596780F30D808C8F0D76C4F6091D9E8E058C1E518E746D85242D9EC1A8BAA35335D78FE6D5E0265F252ADA72E3EF; TS01dfb0ee=018de3ecdda2a63c0ae08ec1f17fb58c004d19a7a0e4ce3494747048ee3a3b22e762609dee2fdadb601f5e5b9a6039ab18a2a3b431b077e2c6c7b28a4a2b7b6997d3eafc1f',
                    'Sec-Fetch-Dest'            => 'document',
                    'Sec-Fetch-Mode'            => 'navigate',
                    'Sec-Fetch-Site'            => 'none',
                    'Sec-Fetch-User'            => '?1',
                    'Upgrade-Insecure-Requests' => '1',
                    'User-Agent'                => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'sec-ch-ua'                 => '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
                    'sec-ch-ua-mobile'          => '?0',
                    'sec-ch-ua-platform'        => '"Windows"'
                ]
            ]);

            $str_cookies = "";

            $it = $jar->getIterator();
            $i = 0;
            while ($it->valid()) {
                if ($it->current()->toArray()['Name'] != " ") {
                    $str_cookies  =  $str_cookies . (($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value'])) . ";";
                }
                $it->next();
            }


            $content1 = ($response1->getBody()->getContents());
            if (!str_contains($content1, "<title>Login</title>")) {
                return [
                    'success' => false,
                    "error" => "Can't access"
                ];
            }

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://aui.mitchell1.com/api/v1/authenticate/transfer');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json, text/javascript, */*; q=0.01',
                'Accept-Language: en-US,en;q=0.9',
                'Connection: keep-alive',
                'Content-Type: application/json; charset=UTF-8',
                'Origin: https://aui.mitchell1.com',
                'Referer: https://aui.mitchell1.com/Login?y=tusc1&exitUrl=https://www.prodemand.com&rememberPassword=True&autoLogin=False',
                'Sec-Fetch-Dest: empty',
                'Sec-Fetch-Mode: cors',
                'Sec-Fetch-Site: same-origin',
                'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With: XMLHttpRequest',
                'sec-ch-ua: "Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "Windows"',
                'x-M1AntiForgeryToken: 26d7d26f63474e32bbd176e820900bce',
                'Accept-Encoding: gzip',
            ]);
            curl_setopt($ch, CURLOPT_COOKIE, '__m1auitoken=5FC37D65F7B7B2A291C9E0D7B0F81AE6AED28A952ED169BF03C44F24E728D22D348FEFB76AB47044ABC3C265E8E2521A86B997365980CA3DD07494465BCD32A1EC957E481309FB705BCB082D20A9216BA11D60FA316DF28CA7BF646351DC8B121C42481E3A01247E98C14AFE839843D7C364BA94B1400D37D2DBADE17CF0348921EB0B9663AF4AAAAA968D09C26BE571; TS01dfb0ee=018de3ecdd6d2fcd9f803a9d67a764d07ac89fc7e8d970ac1c821b9c786d7a0611391335949a1084050392344f660ca76868302de47b9237e7d608a98a1b4fdfced5f6ec75');
            curl_setopt($ch, CURLOPT_POSTFIELDS, '{"ApplicationCode":"tusc1","Username":"Truckramos3","Password":"Anubis13","IpV4Address":"***************","RememberUsername":false,"RememberPassword":true}');

            $response2 = curl_exec($ch);

            curl_close($ch);

            //  $content2 = ($response2->getBody()->getContents());
            $token = json_decode($response2, true)['AuthenticateTransferResponse']['token'];


            $response3 = $client->get("https://www2.prodemand.com/Main/Authorize?transferTicket=$token", [
                'proxy' =>  $proxy,
                'headers' => [
                    'Accept'                    => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                    'Accept-Language'           => 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
                    'Cache-Control'             => 'max-age=0',
                    'Connection'                => 'keep-alive',
                    //  'Cookie'                    => '__m1auitoken=5FA798FDE4C755E86A2BE2613310DC2F918688F0EDE6D00FA3C64E0BD961208F12D096D67A6128DAABF0D13658F2B61623283E528E175C3193F491CC6AD33A6C7D11862370E0255ED85246EDC08FAA5BB093AE56AFB3E5E304ACA1922C192919747F596780F30D808C8F0D76C4F6091D9E8E058C1E518E746D85242D9EC1A8BAA35335D78FE6D5E0265F252ADA72E3EF; TS01dfb0ee=018de3ecdda2a63c0ae08ec1f17fb58c004d19a7a0e4ce3494747048ee3a3b22e762609dee2fdadb601f5e5b9a6039ab18a2a3b431b077e2c6c7b28a4a2b7b6997d3eafc1f',
                    'Sec-Fetch-Dest'            => 'document',
                    'Sec-Fetch-Mode'            => 'navigate',
                    'Sec-Fetch-Site'            => 'none',
                    'Sec-Fetch-User'            => '?1',
                    'Upgrade-Insecure-Requests' => '1',
                    'User-Agent'                => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'sec-ch-ua'                 => '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
                    'sec-ch-ua-mobile'          => '?0',
                    'sec-ch-ua-platform'        => '"Windows"'
                ]
            ]);
            $content1 = ($response3->getBody()->getContents());

            $str_cookies  = "";
            $it = $jar->getIterator();
            $i = 0;
            while ($it->valid()) {
                // if ($it->current()->toArray()['Name'] == "v3.tusc1.sessionTicket") {
                $str_cookies  =  $str_cookies . (($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value'])) . ";";
                //  }
                $it->next();
            }


            return [
                'success' => true,
                'link' => "Panther php FirefoxClient",
                'name' => 'Prodemand Reyes original account V2',
                'cookies' => $str_cookies
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                "error" => $e->getMessage()
            ];
        }
    }


    ////////////////////////////////////////////////// ProdemandReyesAcount
    static public function getCookiessMitchellProdemandReyesAcountScaperVPS()
    {
        try {

            $client = new \GuzzleHttp\Client();
            $ipAccountScaper = DomainConfigHelper::getConfig('ip_vps_fix_account_identifix');

            $ipAccountScaper = "**************";

            $account = AccountItem::where('service', 'MITCHELL_PRODEMAND')->first();
            $username =  $account->username;
            $pass =  $account->password;

            $configAdmin =      ConfigAdmin::first();
            $proxy = "";

            if ($configAdmin  != null && !empty($configAdmin->proxy_mitchell_prodemand)) {
                $proxy = $configAdmin->proxy_mitchell_prodemand;
            }



            $pass = urlencode($pass);
            $response3 = $client->get("http://$ipAccountScaper/api/drcarcookies/cookies_mitchell_prodemand?proxy=$proxy&username=$username&password=$pass", [
                'headers' => [
                    'User-Agent'                => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                ],
                'timeout' => 150,
                'connect_timeout' => 150,
            ]);

            $str_cookies = "";

            $content1 = ($response3->getBody()->getContents());
            $json =  json_decode($content1, true);
            $cookies =  $json['json_data_decode']['cookies'] ?? [];
            foreach ($cookies  as $itemCookie) {
                // if ($itemCookie['name'] == 'v3.tusc1.sessionTicket') {
                $str_cookies =    $str_cookies . $itemCookie['name'] . '=' . $itemCookie['value'] . ";";
                // }
            }
            $base64Html =  $json['json_data_decode']['base64Html'] ?? "";

            MitchellProdemandUtilsWeb::get_RIUV(base64_decode($base64Html));

            return [
                'success' => true,
                'link' => "ScaperVPS DrCar",
                'name' => 'Prodemand Reyes account - ScaperVPS',
                'cookies' => $str_cookies,
                'base64Html' => $base64Html
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                "error" => $e->getMessage()
            ];
        }
    }

    ////////////////////////////////////////////////// Sessions Tecdoc
    static public function getSessionsTecdocSV1()
    {
        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        try {
            $link = "http://autocarsoftware.com/sessionstecdoc.php";
            $body = file_get_contents($link);

            $array = preg_split("/\r\n|\n|\r/", $body);
            $cok =   [];

            $cok = [
                [
                    "name" =>  '-**********-XAGKUOTMFVIDESNWRPYZBJHCLQ',
                    "value" =>  $array[0],
                ],
                [
                    "name" =>  '-**********-TMRHZUYCFJGIKDOPNQVEWXLSBA',
                    "value" =>  $array[1],
                ],
                [
                    "name" =>  '-**********-AMXZCIPHLJOKVFBUQTYRNDGSWE',
                    "value" =>  $array[2],
                ],
                [
                    "name" =>  '-**********-TEHVOKARLQICXUSBGJMZPNDFWY',
                    "value" =>  $array[3],
                ],
                [
                    "name" =>  '-**********-MZTEIPFWKVCUBSXLGRJDQYOHAN',
                    "value" =>  $array[4],
                ],
            ];

            $cok = str_replace("\/", '/', json_encode($cok));

            return [
                'success' => true,
                'link' => $link,
                'name' => 'tecdoc session autocarsoftware',
                'cookies' => CookiesUtils::cookiesArrayToStr($cok)
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// Eyes
    static public function getCookiesTecDocFromAccountEyes()
    {
        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');


        $headerArr  =   [];


        try {
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://webservice.tecalliance.services/webcat30/v1/services/WebCat30WS.jsonEndpoint',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                "getAPIKeyForUser": {
                    "catalog": "tecdocsw",
                    "username": "224620u1",
                    "password": "0ST+Mla5"
                }
            }',
                CURLOPT_HTTPHEADER => array(
                    'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);

            $apiKey = json_decode($response)->apiKey;
            $appStorages = '-**********-XAGKUOTMFVIDESNWRPYZBJHCLQ=Fri Dec 30 2024 21:22:23 GMT 0300 (GMT 03:00);-**********-TMRHZUYCFJGIKDOPNQVEWXLSBA=[{"remember":false,"userName":""}];-**********-AMXZCIPHLJOKVFBUQTYRNDGSWE=' . $apiKey . ';-**********-TEHVOKARLQICXUSBGJMZPNDFWY={"username":"224620u1"};-**********-MZTEIPFWKVCUBSXLGRJDQYOHAN=' . $apiKey . ';';


            return [
                'success' => true,
                'link' => "",
                'name' => 'Tecdoc from account Reyes',
                'cookies' =>  $appStorages
            ];
        } catch (Exception $e) {
            return [
                'success' => false
            ];
        }
    }


    ////////////////////////////////////////////////// Partslink24 DrAcc
    static public function getCookiesPartslink24DrAccount()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');

        $configAdmin =      ConfigAdmin::first();

        $proxy = null;
        if ($proxy === null) {
            if ($configAdmin  != null && !empty($configAdmin->proxy_partslink24)) {
                $proxy = $configAdmin->proxy_partslink24;
            }
        }

        $request = request();
        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }



        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);
        unset($headers_requests['upgrade-insecure-requests']);

        $headerArr  =  [];


        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://www.partslink24.com/partslink24/login-ajax!login.action";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar,
                    'proxy' => $proxy,
                ]
            );

            $account = AccountItem::where('service', PlanUtils::PARTSLINK24)->first();
            $username = $account->username;
            $pass = $account->password;

            $response1 = $client->request(
                'POST',
                $link,
                [

                    //  gb-723380/admin/509607


                    'headers' =>  $headerArr,
                    // 'allow_redirects' => [
                    //     'max' => 2,
                    // ],
                    // 'form_params' => [  //reyes
                    //     'loginBean.accountLogin' => 'bo-745147',
                    //     'loginBean.password' => 'eZFsD3Ya5Vg@3GM',
                    // ],
                    'form_params' => [
                        'loginBean.accountLogin' => $username,
                        'loginBean.userLogin' =>  empty($account->username2) ? 'admin' : $account->username2,
                        'loginBean.sessionSqueezeOut' => 'true',
                        'loginBean.password' => $pass,
                        'loginBean.userOffsetSec' => '25200',
                        'loginBean.code2f' => ''
                    ],
                    // 'on_stats' =>  function (\GuzzleHttp\TransferStats $stats)  {

                    //     $url = $stats->getHandlerStats()['redirect_url'];
                    //     if( $url != "") {
                    //         dd( $url);
                    //     }

                    // }
                ]
            );

            $str_cookies = "";

            $it = $jar->getIterator();
            while ($it->valid()) {

                $str_cookies  =  $str_cookies . ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']) . ";";

                $it->next();
            }

            if ($str_cookies == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Partlink24 Dr',
                'cookies' => json_encode([
                    "access_token"  =>  "",
                    "cookie" => $str_cookies
                ])
            ];
        } catch (Exception $e) {

            return [
                'success' => false
            ];
        }
    }

    ////////////////////////////////////////////////// Partslink24 DrAcc
    static public function getCookiesPartslink24MobileDrAccount()
    {

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $time = $now->format('d/n/Y @ H:i:s');

        $configAdmin =      ConfigAdmin::first();

        $proxy = null;
        if ($proxy === null) {
            if ($configAdmin  != null && !empty($configAdmin->proxy_partslink24)) {
                $proxy = $configAdmin->proxy_partslink24;
            }
        }

        $request = request();
        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }




        $account = AccountItem::where('service', PlanUtils::PARTSLINK24)->first();
        $username = $account->username;
        $pass = $account->password;

        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);
        unset($headers_requests['upgrade-insecure-requests']);

        $headerArr  =  [];


        $jar = new \GuzzleHttp\Cookie\CookieJar();

        try {
            $link = "https://www.partslink24.com/pl24-appgtw/ext/api/1.0/login";
            $client = new \GuzzleHttp\Client(
                [
                    'cookies' => $jar,
                    'proxy' => $proxy,
                ]
            );
            $response1 = $client->request(
                'POST',
                $link,
                [
                    'headers' =>  $headerArr,
                    'json' => [
                        'authentication' => [
                            'account' => $username,
                            'user' => empty($account->username2) ? 'admin' : $account->username2,
                            'pwd' => $pass
                        ],
                        'device' => [
                            'id' => '0',
                            'os' => 'Win32',
                            'offset' => '0',
                            'lang' => 'en',
                            'os-version' => '0'
                        ],
                        'app-version' => '',
                        'squeezeOut' => true
                    ]
                ]
            );

            $cookie = "";
            $it = $jar->getIterator();
            while ($it->valid()) {

                // $str_cookies  =  $cookie . ($it->current()->toArray()['Name']) . '=' . ($it->current()->toArray()['Value']) . ";";

                if ($it->current()->toArray()['Name'] == "PL24TOKEN") {
                    $cookie =  ($it->current()->toArray()['Value']);
                }

                $it->next();
            }

            $body = $response1->getBody()->getContents();
            $data = json_decode($body);

            $access_token  =  $data->token->access_token;

            if ($access_token == "") {
                return [
                    'success' => false
                ];
            }

            return [
                'success' => true,
                'link' => $link,
                'name' => 'Partlink24 Mobile Dr',
                'cookies' =>  json_encode([
                    "access_token"  =>  $access_token,
                    "cookie" => $cookie
                ])
            ];
        } catch (Exception $e) {

            return [
                'success' => false
            ];
        }
    }
}
