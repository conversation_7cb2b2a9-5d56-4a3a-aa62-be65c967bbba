<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use App\Models\MsgCode;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use App\Helper\HtmlUtils;
use Exception;
use Illuminate\Support\Facades\Cache;

class HaynesTruckUtilsWeb
{


    /**
     * Lấy dữ liệu
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $proxy = null)
    {

        $request = $request ?? request();
        $tailLink = $_SERVER["REQUEST_URI"];

        if (
            str_contains($tailLink, 'wsdLogin') || str_contains($tailLink, '?forceLogout=true')
        ) {
            return ViewUtils::viewErrorPage([
                'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                'msg' => MsgCode::HAYNES_OF_MAINTENANCE[1],
            ]);
            exit;
        }

        if (
            str_contains($tailLink, 'wsdLogout') ||
            str_contains($tailLink, 'settingsContent') ||
            str_contains($tailLink, 'aboutContent') ||
            str_contains($tailLink, 'Logout') ||
            str_contains($tailLink, 'logout')
        ) {

            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();

            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }

        if (
            str_contains($tailLink, 'wsdLogin')
        ) {
            return ViewUtils::viewErrorPage([
                'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                'msg' => MsgCode::HAYNES_OF_MAINTENANCE[1],
            ]);
            exit;
        }


        if (
            str_contains($tailLink, 'login.do') || $tailLink == "/"
        ) {
            $domainMain = Helper::getDomainCurrent();
            header("Location: " . $domainMain . "/touch/site/layout/makesOverviewTrucks");
            exit;
        }


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }

        $configAdmin =      ConfigAdmin::first();
        if ($cookies_str == null) {
            if ($configAdmin  != null && !empty($configAdmin->cookies_haynes_truck_str)) {
                $cookies_str = $configAdmin->cookies_haynes_truck_str;
            }
        }


        if ($proxy === null) {
            if ($configAdmin  != null && !empty($configAdmin->proxy_haynespro_truck)) {
                $proxy = $configAdmin->proxy_haynespro_truck;
            }
        }

        $headerArr['accept'] = $headers_requests['accept'] ?? null;
        $headerArr['x-csrf-token'] = $headers_requests['x-csrf-token'] ?? null;
        $headerArr['user-agent'] = 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.141';
        $headerArr['accept-language'] = 'accept-language: en-US,en;q=0.9';

        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $cookieArr  = CookiesUtils::cookieStrToArray($cookies_str);

        $headerArr['cookie'] =  $cookies_str;

        if (isset($cookieArr['DOMAIN_REQUEST_SERVER'])) {
            $link = str_replace('https://www.workshopdata.com', $cookieArr['DOMAIN_REQUEST_SERVER'], $link);
            $request->merge(['data3_domain' => $cookieArr['DOMAIN_REQUEST_SERVER']]);
        }

        //kiểm tra là diagzone
        if (isset($cookieArr['DOMAIN_REQUEST_SERVER'])) {
            if (str_contains($cookieArr['DOMAIN_REQUEST_SERVER'], "webdiag.name") && str_contains($tailLink, "axlesTruckSelection")) {

                $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();

                $newTailLink = str_replace('axlesTruckSelection', 'modelDetail', $tailLink) . "&axleSelectionSkiped=true";

                header("Location: $newTailLink");
                exit;
            }
        }

        $client = new \GuzzleHttp\Client(
            [
                'headers' => $headerArr,
                'proxy' => $proxy,

            ]
        );

        $response1 = $client->request(
            $method,
            $link,
            [
                'headers' =>  $headerArr,
                'form_params' => $body,
                'timeout' => 15, // Response timeout
                'connect_timeout' => 15, // Connection timeout,
                'on_stats' => $request->is_check == true ? null : function (\GuzzleHttp\TransferStats $stats)  use ($request) {


                    $url = $stats->getHandlerStats()['redirect_url'];

                    if (str_contains($url, "timeout")) {
                        throw new Exception('timeout');
                    }

                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $pathNoHTTP = str_replace("http://", "", $path);
                    $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

                    $url =  str_replace("https://www.workshopdata.com:443", $path, $url);
                    $url =  str_replace("http://www.workshopdata.com:443", $path, $url);
                    $url =  str_replace("www.workshopdata.com:443", $pathNoHTTP, $url);

                    $url =  str_replace("https://www.workshopdata.com", $path, $url);
                    $url =  str_replace("http://www.workshopdata.com", $path, $url);
                    $url =  str_replace("www.workshopdata.com", $pathNoHTTP, $url);


                    if (str_contains($request->data3_domain, "hp.webdiag.name")) {
                        $url =  str_replace("https://hp.webdiag.name", $path, $url);
                        $url =  str_replace("http://hp.webdiag.name", $path, $url);
                        $url =  str_replace("hp.webdiag.name", $pathNoHTTP, $url);
                    }
                    if (str_contains($request->data3_domain, "hd.hp-web.in")) {
                        $url =  str_replace("https://hd.hp-web.in", $path, $url);
                        $url =  str_replace("http://hd.hp-web.in", $path, $url);
                        $url =  str_replace("hd.hp-web.in", $pathNoHTTP, $url);
                    }


                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);


                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink) {

                        $firtUrl = substr($url, 0, 3);
                        if ($firtUrl == "www") {
                            $url =  str_replace("www.", "https://www.",  $url);
                        }

                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );



        return  $response1;
    }

    static public function  remove_element_reponse($body, $contentType, $statusCode, $request)
    {

        $actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];
        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            header("Content-Type:application/pdf");
        }

        $body =  str_replace("#1c66ed", '#103178', $body);

        $ContentType = $contentType;

        //Save vao db
        if ($ContentType && $request != null && $request->method() == "GET" && $statusCode == 200) {
            try {
                if (
                    $request != null && !str_contains($tailLink, "ogin")
                    // && !str_contains($contentType, "image")
                    && (!str_contains($body, "error") || str_contains($body, "genericErrorMessage"))
                    && $body != null && $body != "" && !str_contains($tailLink, "settingsContent") && $body != null
                    && !str_contains($body, '<input type="text" name="username" id="username"')
                    && !str_contains($body, 'class="login"')
                ) {


                    if (str_contains($request->data3_domain, "workshopdata.com")) {
                        $webClass = new WebClass();
                        $webClass->tail_link = $tailLink;
                        $webClass->service =  PlanUtils::HAYNESPRO_TRUCK;
                        $webClass->method = $request->method();
                        $webClass->content_type =  $contentType;
                        $webClass->body =  $body;
                        $webClass->status_code =  $statusCode;

                        WebDataUtils::saveWebData($webClass);
                    }
                }
            } catch (Exception $e) {
            }
        }

        if ($contentType) {
            $appName = DomainConfigHelper::getConfig('appName');

            if (!str_contains($tailLink, "touch/getSubnetDiagram") && (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json'))) {
                //check is diagzone
                if (str_contains($request->data3_domain, "hp.webdiag.name")) {

                    $body =  str_replace("https://hp-en.webdiag.name", $path, $body);
                    $body =  str_replace("http://hp-en.webdiag.name", $path, $body);
                    $body =  str_replace("hp-en.webdiag.name", $pathNoHTTP, $body);

                    $body =  str_replace("https://hp.webdiag.name", $path, $body);
                    $body =  str_replace("http://hp.webdiag.name", $path, $body);
                    $body =  str_replace("hp.webdiag.name", $pathNoHTTP, $body);

                    $body =  str_replace("Diagzone", $appName, $body);
                    $body =  str_replace("https://diagzone.com/assets/img/fix-logo.png", "/assets/img/fix-logo.png", $body);
                }


                function replaceTitleTag($html)
                {
                    $appName = DomainConfigHelper::getConfig('appName');
                    $newText = preg_replace('/<title>[^<]*<\/title>/', '<title>' . $appName . ' Haynes Pro</title>', $html);
                    return $newText;
                }
                $body =  replaceTitleTag($body);


                //check is xpro5
                if (str_contains($request->data3_domain, "hd.hp-web.in")) {

                    $body =  str_replace("https://hd.hp-web.in", $path, $body);
                    $body =  str_replace("http://hd.hp-web.in", $path, $body);
                    $body =  str_replace("hd.hp-web.in", $pathNoHTTP, $body);

                    $body =  str_replace("X-PRO5", $appName, $body);
                    $body =  str_replace("/touch/branding/Launchwest/logo.gif", "/assets/img/fix-logo.png", $body);
                }


                $body =  str_replace("https://www.workshopdata.com:443", $path, $body);
                $body =  str_replace("http://www.workshopdata.com:443", $path, $body);
                $body =  str_replace("www.workshopdata.com:443", $pathNoHTTP, $body);

                $body =  str_replace("https://www.workshopdata.com", $path, $body);
                $body =  str_replace("http://www.workshopdata.com", $path, $body);
                $body =  str_replace("www.workshopdata.com", $pathNoHTTP, $body);
                $body =  str_replace("http://", "https://", $body);


                $_csrf = Cache::get('HAYNESPRO_TRUCK_CSRF', null);
                if ($_csrf != null) {
                    $body = preg_replace(
                        '/<meta\s+name="_csrf"\s+content="[^"]*"\s*\/?>/i',
                        '<meta name="_csrf" content="' . htmlspecialchars($_csrf, ENT_QUOTES) . '" />',
                        $body
                    );
                }

                $configAdmin =      ConfigAdmin::first();
                if ($configAdmin  != null &&   $configAdmin->alldata_info != null && $request->user != null) {
                    $first_name = $configAdmin->alldata_info->first_name ?? "";
                    $last_name = $configAdmin->alldata_info->last_name ?? "";
                    $username = $request->user->username;
                    $body =  str_replace("<span class=\"dp_label\">$first_name $last_name</span>", "<span class=\"dp_label\">$username</span>", $body);
                }

                $cookies = $_COOKIE;
                $platform = $cookies['platform'] ?? request()->header('platform') ?? null;

                $isDocumentTypeHtml = false;
                if (substr($body, 0, strlen("<!DOCTYPE html>")) == "<!DOCTYPE html>") {
                    $isDocumentTypeHtml  = true;
                }

                if (str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);

                    if ($dom  != null) {

                        //Remove search
                        $last = $dom->find('.select-recent-car', 0);

                        if ($last != null) {
                            $last->remove();
                        }

                        //LCV
                        $cars = $dom->find('.cars', 0);

                        if ($cars != null) {
                            $cars->remove();
                        }

                        //Remove Expires
                        $Expires = $dom->find('.licenseExpireAlert', 0);

                        if ($Expires != null) {
                            $Expires->remove();
                        }

                        $footer = $dom->find('.haynesproBar', 0);
                        if ($footer != null) {
                            $footer->remove();
                        }

                        //3 nust car truck va cost
                        $appMenu = $dom->find('.appMenu', 0);
                        if ($appMenu != null) {
                            $appMenu->remove();
                        }

                        $settingMenu = $dom->find('.settingMenu', 0);

                        if ($settingMenu != null) {
                            $settingMenu->remove();
                        }

                        $body =  $dom->innertext;
                    }

                    //Khong phai iframe thi them vao body
                    if ($isDocumentTypeHtml && isset($_SERVER['HTTP_SEC_FETCH_DEST']) && $_SERVER['HTTP_SEC_FETCH_DEST'] != 'iframe' && !$request->isMethod('post')) {
                        $body =  HtmlUtils::addTagHtmlOnHead(' <div id="google_translate_element" style="z-index: 410;position: inherit;"></div>
                        <script> 
                        function googleTranslateElementInit() {
                        new google.translate.TranslateElement({
                            pageLanguage: "en"
                        }, "google_translate_element");
                        }
                        </script>
                        <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script> ', $body);

                        $body =  HtmlUtils::addTagHtmlOnHead('<script src="/jssav/' . Helper::generateRandomString(10) . '.js"></script>', $body);
                    }

                    if ($isDocumentTypeHtml &&  $request->user != null) {

                        $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_haynespro_truck");
                        if ($remain_days > 0 && $remain_days < 10 && !$request->isMethod('post')) {
                            $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script>
                             
                                 siiimpleToast.alert(" Haynespro expiry date is only ' . $remain_days . ' days, remember to renew!", {
                                  position: "top|center",
                                  margin: 15,
                                  delay: 0,
                                  duration: 5000,
                                });
                                </script>', $body);
                        }
                    }
                }

                if ($isDocumentTypeHtml &&  str_contains($ContentType, 'text/html')  && !$request->isMethod('post')) {
                    $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                        <script>
                        window.dataLayer = window.dataLayer || [];
                        function gtag(){dataLayer.push(arguments);}
                        gtag("js", new Date());
                    
                        gtag("config", "G-KXWTNY3G2J");
                        </script>', $body);
                }

                if ($platform != 'web' && $platform != null && str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);
                    if ($dom  != null) {

                        //Remove last web pc
                        $top = $dom->find('header', 0);

                        if ($top != null) {
                            $top->remove();
                        }
                        $footer = $dom->find('footer', 0);

                        if ($footer != null) {
                            $footer->remove();
                        }
                    }

                    $body =  $dom->innertext;
                }
            }



            header('Content-type: ' .  $ContentType ?? "");

            echo $body;
            die();
        } else

        if (str_contains($tailLink, 'layout/electronicSystems?')) {

            header('Content-type: application/json;charset=UTF-8');
        }


        echo   $body;
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {


        $request = request();
        $request->merge(['is_check' => true]);
        try {
            $time_start = microtime(true);

            /// Diagzone
            $configAdmin =      ConfigAdmin::first();
            if ($cookies_str == null) {
                if ($configAdmin  != null && !empty($configAdmin->cookies_haynes_truck_str)) {
                    $cookies_str = $configAdmin->cookies_haynes_truck_str;
                }
            }
            $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
            $cookieArr  = CookiesUtils::cookieStrToArray($cookies_str);

            if (isset($cookieArr['DOMAIN_REQUEST_SERVER'])) {
                $data3_domain = $cookieArr['DOMAIN_REQUEST_SERVER'];
                if (str_contains($data3_domain, "hp.webdiag.name")) {
                    $response  = HaynesTruckUtilsWeb::getResponse('https://hp.webdiag.name/touch/site/layout/makesOverviewTrucks', "GET", $cookies_str, null);
                    $content = ($response->getBody()->getContents());
                    $time_end = microtime(true);

                    if (str_contains($content, 'logo-list make-logo-big')) {
                        return [
                            'time_handle' => ($time_end - $time_start),
                            'status' => true,
                            'mess' => "",
                            'name' => 'digzone',
                            'phone' => '',
                            'com' => "",
                        ];
                    }
                    return [
                        'status' => false,
                        'mess' => "ERROR COOKIES 1"
                    ];
                }
                if (str_contains($data3_domain, "hd.hp-web.in")) {
                    $response  = HaynesTruckUtilsWeb::getResponse('https://hd.hp-web.in/touch/site/layout/makesOverviewTrucks', "GET", $cookies_str, null);
                    $content = ($response->getBody()->getContents());
                    $time_end = microtime(true);

                    if (str_contains($content, 'logo-list make-logo-big')) {
                        return [
                            'time_handle' => ($time_end - $time_start),
                            'status' => true,
                            'mess' => "",
                            'name' => 'xpro5',
                            'phone' => '',
                            'com' => "",
                        ];
                    }
                    return [
                        'status' => false,
                        'mess' => "ERROR COOKIES 1"
                    ];
                }
            }
            ///

            $response  = HaynesTruckUtilsWeb::getResponse('https://www.workshopdata.com/touch/site/layout/settingsContent', "GET", $cookies_str, null);
            $type = $response->getHeader('Content-Type')[0] ?? "";

            $content = ($response->getBody()->getContents());

            if (str_contains($type, 'text/html')) {

                $name = "";
                $user_id = "";
                $phone = "";
                $com = "";
                //Lấy username
                $dom = HtmlDomParser::str_get_html($content);
                if ($dom  != null) {

                    $cars = $dom->find('.cars', 0);
                    $trucks = $dom->find('.trucks', 0);
                    $password = $dom->find('#password', 0);

                    if ($password != null) {
                        return [
                            'status' => false,
                            'mess' => "ERROR COOKIES LOGIN SCREEN"
                        ];
                    }
                    $active_trucks = $dom->find('.active.trucks', 0);
                    $disable_trucks = $dom->find('.disabled.trucks', 0); // == null thi co truck

                    if ($active_trucks == null &&  $disable_trucks  != null) {
                        return [
                            'status' => false,
                            'mess' => "ERROR NO ACTIVE TRUCK SCREEN"
                        ];
                    }

                    $_csrf = "";
                    $dom = HtmlDomParser::str_get_html($content);
                    if ($dom  != null) {
                        $inputElements = $dom->find('input');
                        foreach ($inputElements as $element) {
                            if ($element->getAttribute('name') === '_csrf') {
                                // Lấy giá trị của thuộc tính value
                                $csrfValue = $element->getAttribute('value');
                                $_csrf = $csrfValue;
                                break; // Dừng vòng lặp sau khi tìm thấy phần tử cần trích xuất giá trị
                            }
                        }
                    }


                    $namespan = $dom->find('#contactName', 0);
                    if ($namespan != null) {
                        $name  = str_replace(" ", "", ($namespan->value));
                    }

                    $phonespan = $dom->find('#telephone', 0);
                    if ($namespan != null) {
                        $phone  = str_replace(" ", "", ($phonespan->value));
                    }

                    $comspan = $dom->find('#companyName', 0);
                    if ($comspan != null) {
                        $com  = str_replace(" ", "", ($comspan->value));
                    }
                }
                $time_end = microtime(true);

                return [
                    'time_handle' => ($time_end - $time_start),
                    'status' => true,
                    'mess' => "",
                    'name' => $name,
                    'phone' => $phone,
                    'com' => $com,
                    '_csrf' => $_csrf
                ];
            }
        } catch (Exception $e) {

            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => 'Request ERRROR 1. ' . $e->getMessage()
            ];
        }
        return [
            'status' => false,
            'mess' => "Request ERRROR 2"
        ];
    }
}
