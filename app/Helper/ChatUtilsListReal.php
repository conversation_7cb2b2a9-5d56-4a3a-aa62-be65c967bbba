<?php

namespace App\Helper;

use App\Events\RedisChatEventList;
use App\Jobs\SendSocketListJob;
use App\Models\GroupChat;
use App\Models\PersonChat;
use M<PERSON><PERSON><PERSON>\LaravelQueueDebouncer\Facade\Debouncer;

class ChatUtils
{

    const LIST_USER_CHAT = 0;
    const LIST_GROUP_CHAT = 1;

    const TYPE_CHAT_0 = 0;
    const TYPE_CHAT_RECALL = 1;

    static function sendListSocket($user_id, $type)
    {
        SendSocketListJob::dispatch(
            $user_id,
            $type
        );
    }
}
