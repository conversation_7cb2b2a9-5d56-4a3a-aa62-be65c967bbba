<?php

namespace App\Helper;

use App\Models\ConfigAdmin;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;

class HaynesWebCurlUtils
{

    const cookie =  'll=en_GB; JSESSIONID=263E7E35D5638F223ECAFFD4338C543E-n1; _ga=GA1.2.951375793.1661419528; __utmz=69693760.1662560141.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); _gid=GA1.2.1493157109.1662442689; __utma=69693760.951375793.1661419528.1662560141.1662628670.2; ak_bmsc=5AE131AD99B154727CAF5A4A6FA768F1~000000000000000000000000000000~YAAQ26zbFxVNz/WCAQAA6Pj/IRGVfZKYjNxmldyw0d7KdLDfZeA1IJmF1GWMuc8nfMgPMVNuINctUqF0v1x5LTxMxmAVkOi+OhFFI0/vyZNwt62XCatkwSIoIIOLM3+umQeqo0Wy/ujo1Ghhn5DcyG1X4PMDJHHmm5rXjyajwfp8goT/Y+efeQtJbp1bnZzegQyMizj8YyAsPX9KCY79x8gZ4S36wrNr1t5j3q8UQcXlAR7WCPqrq0+EQW/u4s5syLuNoCjhUnAXG1BUro48L55DoiUDyoo5z7oXHvMNZ+VhVGvvB5PP3xLvccDrhXFE9hNlxuhX9BzotGzP5FZiqC8y+Z3lH439lKDp+G3vfp0TFghIi/HG9ZOkk6iW67bphSD4Xr/tZNjp0b6+NBm/QNwKlWFI1nJgcTj1; _gat=1; RT="sl=8&ss=1662722711552&tt=8248&obo=0&sh=1662722770789%3D8%3A0%3A8248%2C1662722767437%3D7%3A0%3A5669%2C1662722753821%3D6%3A0%3A5338%2C1662722744446%3D5%3A0%3A5020%2C1662722738723%3D4%3A0%3A4350&dm=my.alldata.com&si=7de1ffb0-2fb5-4d72-a34e-cda08b2da35c&bcn=%2F%2F684d0d49.akstat.io%2F"; bm_sv=E54B0A0882B6CA2E3BE58465FD3E8C82~YAAQ26zbF9JOz/WCAQAA60wAIhEUjqkrd+YmdBdBuWcnEhdcQWXRQnUvoVz3flKFvTQZeZB5JbsqgQqg0BcKjbsc34ZOdzNfNd3crq4h7S2H37pC6hUFVR2XGjguWXcGaMkgJD4tYqOIZUkvNkKFXdB+CAWMaxevfJ7yLOanSnAt0axzSX0dHRKaX02BNUMxccA5wqso54DIfKTMMcv3t40yxtUXL7eG92oMf7vOhiVUEDpZ7KZYIUaRnXy2VjdZYcs=~1';
    const PATH_ALLDATA = "https://my.alldata.com";

    static public function  remove_element_reponse(CurlReponse $response1, $request)
    {
        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $domain_request = $_SERVER['HTTP_HOST'];


        $body = $response1->getResponseBody();
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);


        if (str_contains($domain_request, 'my-alldata.')) {
            $pathNoHTTP = str_replace("http://", "", $path);
            $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp(); 
            $body =  str_replace("https://my.alldata.com", "https://my-alldata." . $domainMain, $body);
            $body =  str_replace("http://my-alldata." . $domainMain, "https://my-alldata." . $domainMain, $body);
        }



        //    \" id=\"13968816\" src=\"/ADAG/repa

        $body =  str_replace("https://my.alldata.com", $path, $body);
        // $body =  str_replace("http://my.alldata.com", $path, $body);

        if (!str_contains($domain_request, 'my-alldata.')) {
            $body =  str_replace('src=\"/', 'src=\"' . $path . '/', $body);
            $body =  str_replace('href=\"/', 'href=\"' . $path . '/', $body);
        }

        $contentType = $response1->getContentType();

        if (str_contains($contentType, 'text/html')) {
            $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                    <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag("js", new Date());
                
                    gtag("config", "G-KXWTNY3G2J");
                    </script>', $body);
        }

        if ($request->user != null && str_contains($contentType, 'text/html')) {

            $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_alldata");
            if ($remain_days > 0 && $remain_days < 10) {
                $appName = DomainConfigHelper::getConfig('appName');
                $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                $body =  HtmlUtils::addTagHtmlOnHead('<script>
                 
                     siiimpleToast.alert("('.$appName.') Alldata expiry date is only ' . $remain_days . ' days, remember to renew!", {
                      position: "top|center",
                      margin: 15,
                      delay: 0,
                      duration: 5000,
                    });
                    </script>', $body);
            }
        }

        if (str_contains($contentType, 'javascript')) {
            $body =  str_replace("800-859-3282", "+8618980876959", $body);
        }
        if (str_contains($contentType, 'json')) {
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null &&   $configAdmin->alldata_us_v2_info != null && $request->user != null) {

                $first_name = $configAdmin->alldata_us_v2_info->first_name ?? "";
                $last_name = $configAdmin->alldata_us_v2_info->last_name ?? "";
                $site_name = $configAdmin->alldata_us_v2_info->site_name ?? "";
                $user_name = $configAdmin->alldata_us_v2_info->user_name ?? "";

                $username = $request->user->username ?? "";
                $username2 =  "";
                $name = $request->user->name ?? "";



                $body =  str_replace("\"siteName\":\"$site_name\"", "\"siteName\":\"$name\"", $body);
                $body =  str_replace("\"userName\":\"$user_name\"", "\"userName\":\"$username\"", $body);

                $body =  str_replace("\"firstName\":\"$first_name\"", "\"firstName\":\"$username\"", $body);
                $body =  str_replace("\"lastName\":\"$last_name\"", "\"lastName\":\"$username2\"", $body);
            }
        }



        $tailLink = $_SERVER["REQUEST_URI"];

        if (str_contains($tailLink, '_png')) {
            //dd("hhh");
            header('Content-Type: image/png');
        } else 
        if (str_contains($tailLink, '_jpg')) {
            header('Content-Type: image/jpeg');
        } else 
        if (str_contains($tailLink, 'svg')) {
            header('Content-Type: image/svg+xml');
        } else {
            header("Content-Type: $contentType");
        }

        echo   $body;
        die();
    }
    /**
     * Thông tin server
     */
    static public function  getResponse($link, $cookies_str = null, $method = "GET", $body = [], Request $request = null, $proxy = null)
    {
        $tailLink = $_SERVER["REQUEST_URI"];

        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        if ($cookies_str == null) {
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null && !empty($configAdmin->cookies_haynes_str)) {
                $cookies_str = $configAdmin->cookies_haynes_str;
            }
        }

        if ($proxy == null) {
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null && !empty($configAdmin->proxy_haynespro)) {
                $proxy = $configAdmin->proxy_haynespro;
            }
        }


        $res = CurlUtils::requestAllEU($method, $link, [
            'headers' =>  $headers_requests,
            'cookie' =>  $cookies_str,
            'proxy' => $proxy
        ]);

        return $res;
    }

    static public function checkServer($cookies_str = null)
    {
        try {
            $response  = HaynesWebCurlUtils::getResponse('https://my.alldata.com/ADAG/sso/profile', $cookies_str);
            $type = $response->getContentType();
            $body = $response->getResponseBody();


            if (!str_contains($body, '1015')) {
                $info_account_list = json_decode($body);

                $user_name = $info_account_list->userName;
                $user_id = $info_account_list->userId;
                return [
                    'status' => true,
                    'mess' => "",
                    'user_name' => $user_name,
                    'user_id' => $user_id,
                ];
            }
        } catch (Exception $e) {
            LogUtils::error($e);

            return [
                'status' => false,
                'mess' => $e->getMessage()
            ];
        }
        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
