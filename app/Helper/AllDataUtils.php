<?php

namespace App\Helper;

use App\Models\ConfigAdmin;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;

class AllDataUtils
{

    /**
     * Thông tin server
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $proxy = null)
    {
        $link = str_replace('/locale/en_GB/','/locales/en_GB/',$link );

        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        if ($cookies_str == null) {
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null && !empty($configAdmin->cookies_alldata_str)) {
                $cookies_str = $configAdmin->cookies_alldata_str;
            }
        }

        if ($proxy == null) {
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null && !empty($configAdmin->proxy_alldata)) {
                $proxy = $configAdmin->proxy_alldata;
            }
        }

        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $arr = CookiesUtils::cookieStrToArray($cookies_str);

        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);

        unset($headers_requests['cf-connecting-ip']);
        unset($headers_requests['x-forwarded-for']);
        unset($headers_requests['cdn-loop']);
        unset($headers_requests['referer']);
        

        $headerArr = [
            'User-Agent'       =>  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:134.0) Gecko/20100101 Firefox/134.0',
            'Accept'           =>  $headers_requests['accept'] ?? $headers_requests['Accept'] ?? '*/*',
            'Accept-Language'  =>  'en-US,en;q=0.5', //$headers_requests['Accept-Language'] ?? $headers_requests['accept-language'] ?? 
            'Accept-Encoding'  =>  $headers_requests['Accept-Encoding'] ?? $headers_requests['accept-encoding'] ?? 'gzip, deflate, br, zstd',
            'Content-Type'     =>  $headers_requests['Content-Type'] ?? $headers_requests['content-type'] ?? 'application/json',
            'Cookie'           =>  $cookies_str,

        ];
        $jar = new \GuzzleHttp\Cookie\CookieJar();

        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {

                $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name'     => $key,
                    'Value'    =>  $value,
                    'Domain'   => '.alldata.com',
                    'Path'     => '/',
                    // 'Max-Age'  => $item['name'],
                    //'Expires'  => $item['name'],
                    // 'Secure'   => $item['name'],
                    // 'Discard'  => $item['name'],
                    //  'HttpOnly' => $item['name'],
                ]));
            }
        }

        $client = new \GuzzleHttp\Client();

        $response1 = $client->request(
            $method,
            $link,
            [
                'cookies' => $jar,
                'headers' => $headerArr,
                'form_params' => $body,
                'timeout' => 15, // Response timeout
                'connect_timeout' => 15, // Connection timeout,
                'proxy' => $proxy,
                'on_stats' => function (\GuzzleHttp\TransferStats $stats) {

                    $url = $stats->getHandlerStats()['redirect_url'];
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";

                    $url =  str_replace("https://app.alldata.com", $path, $url);
                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);

                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink && !str_contains($url, 'alldata/user/login',)) {

                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );


        return  $response1;
    }

    static public function  remove_element_reponse($response1)
    {
        $actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];
        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            header("Content-Type:application/pdf");
        }
        // if (str_contains($tailLink, '.css') || str_contains($tailLink, '.css')) {
        //     header("Content-Type:text/css");
        // }
        // if (str_contains($tailLink, '.js')) {

        //     header("Content-Type:application/javascript");
        // }

        $body = $response1->getBody();


        $ContentType = $response1->getHeader('Content-Type')[0] ?? "";


        if (str_contains($ContentType, 'text/html') && !str_contains($path, 'app-alldata.')) {  //Tách svg ra
            $body =  str_replace("app.alldata.com", $pathNoHTTP, $body);
            $dom = HtmlDomParser::str_get_html($body);

            if ($dom  != null) {
                $embed = $dom->find('embed', 0);
                if ($embed  != null &&  $embed->attr != null && isset($embed->attr['src'])) {
                    if (str_contains($embed->attr['src'], '_svg')) {


                        $body = str_replace(
                            'type="image/svg+xml"',
                            'type="image/svg+xml" style="
                       
                        width: 250vw !important;
                        height: 250vw !important;
                        
                        "

                        ',
                            $body
                        );



                        echo $body;
                        die();
                        $urlXXX = strtok($tailLink, '?');
                        $your_array = explode("/", $urlXXX);
                        $last =  $your_array[count($your_array) - 1];

                        $repPath =   str_replace($last, $embed->attr['src'], $actual_link);
                        header("location: $repPath");
                    }
                }
            }

            echo $body;
            die();
        }





        if ($response1->getHeader('Content-Type') != null) {
            $ContentType = $response1->getHeader('Content-Type')[0] ?? "";
            header('Content-type: ' .  $response1->getHeader('Content-type')[0] ?? "");

            echo $body;
            die();
        } else

        if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) {
            $body =  str_replace("app.alldata.com", $pathNoHTTP, $body);

            echo $body;
            die();
        }


        if (str_contains($ContentType, 'text/html')) {
            dd("hhh");
            echo $body;
            die();
        }


        if (str_contains($tailLink, 'makes')) {
            header("Content-Type: application/octet-stream;charset=UTF-8");
            echo $body;
            die();
        }


        echo   $body;
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {


        $time_start = microtime(true);
        try {


            $response  = AllDataUtils::getResponse('https://app.alldata.com/alldata/vehicle/ymme/years', "GET", $cookies_str, null, null, $proxy);
            $type = $response->getHeader('Content-Type')[0] ?? "";


            $time_end = microtime(true);


            if (!str_contains($response->getBody()->getContents(), '<title>Login</title>')) {

                try {
                    $response  = AllDataUtils::getResponse('https://app.alldata.com/alldata/user/listUser/C/settings', "GET", $cookies_str, null, null, $proxy);
                    $type = $response->getHeader('Content-Type')[0] ?? "";


                    $info_account_list = json_decode($response->getBody()->getContents());
                    $user_name =  $info_account_list[0]->userName;
                    $user_id =  $info_account_list[0]->id;

                    $last_name =  $info_account_list[0]->lastName;
                    $first_name =  $info_account_list[0]->firstName;

                    return [
                        'time_handle' => ($time_end - $time_start),
                        'status' => true,
                        'mess' => "",
                        'user_id' => $user_id,
                        'user_name' => $user_name,
                        'last_name' => $last_name,
                        'first_name' => $first_name,
                    ];
                } catch (Exception $e) {
                    dd($e);
                }
            }
        } catch (Exception $e) {

            return [
                'status' => false,
                'mess' => $e->getMessage()
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
