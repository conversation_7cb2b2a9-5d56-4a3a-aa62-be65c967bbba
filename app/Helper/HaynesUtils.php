<?php

namespace App\Helper;

use App\Jobs\PushNotificationAdminJob;
use App\Models\ConfigAdmin;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class HaynesUtils
{


    /**
     * Thông tin server
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $proxy = null)
    {
        $domain_request = $_SERVER['HTTP_HOST'];
        $request = $request ?? request();

        $tailLink = $_SERVER["REQUEST_URI"];
        if (
            str_contains($tailLink, 'wsdLogout') ||
            str_contains($tailLink, 'settingsContent') ||
            str_contains($tailLink, 'aboutContent') ||
            str_contains($tailLink, 'Logout') ||
            str_contains($tailLink, 'logout')
        ) {
            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();

            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }



        $configAdmin =      ConfigAdmin::first();
        if ($cookies_str == null) {
            $cookies_str = Cache::get(json_encode(['cookies_haynes_str']));

            if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_haynes_str)) {
                $cookies_str = $configAdmin->cookies_haynes_str;
            }
        }

        if ($proxy === null) {
            if ($configAdmin  != null && !empty($configAdmin->proxy_haynespro)) {
                $proxy = $configAdmin->proxy_haynespro;
            }
        }

        $headerArr['accept'] = $headers_requests['accept'] ?? "";
        $headerArr['user-agent'] = $headers_requests['user-agent'] ?? "";
        $headerArr['x-csrf-token'] = $headers_requests['x-csrf-token'] ?? "";
        $headerArr['accept-language'] = 'accept-language: en,en-US;q=0.9,vi-VN;q=0.8,vi;q=0.7,zh-CN;q=0.6,zh;q=0.5,ca;q=0.4,ko;q=0.3,de;q=0.2,ar;q=0.1,hi;q=0.1';


        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $headerArr['cookie'] =  $cookies_str;

        if (isset($cookieArr['DOMAIN_REQUEST_SERVER'])) {
            $link = str_replace('https://www.workshopdata.com', $cookieArr['DOMAIN_REQUEST_SERVER'], $link);
            $request->merge(['data3_domain' => $cookieArr['DOMAIN_REQUEST_SERVER']]);
            $headerArr['Cookie'] =  $cookies_str;
        }

        $client = new \GuzzleHttp\Client(
            [
                'headers' => $headerArr,
                'proxy' => $proxy,
            ]
        );

        $response1 = $client->request(
            $method,
            $link,
            [
                'headers' => $headerArr,
                'form_params' => $body,
                'timeout' => 15, // Response timeout
                'connect_timeout' => 15, // Connection timeout,
                'on_stats' => function (\GuzzleHttp\TransferStats $stats)   use ($request) {

                    $url = $stats->getHandlerStats()['redirect_url'];

                    if (str_contains($url, "timeout")) {
                        throw new Exception('timeout');
                    }

                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $pathNoHTTP = str_replace("http://", "", $path);
                    $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

                    $url =  str_replace("https://www.workshopdata.com:443", $path, $url);
                    $url =  str_replace("http://www.workshopdata.com:443", $path, $url);
                    $url =  str_replace("www.workshopdata.com:443", $pathNoHTTP, $url);

                    $url =  str_replace("https://www.workshopdata.com", $path, $url);
                    $url =  str_replace("http://www.workshopdata.com", $path, $url);
                    $url =  str_replace("www.workshopdata.com", $pathNoHTTP, $url);

                    if (str_contains($request->data3_domain ?? "", "hp.webdiag.name")) {

                        $url =  str_replace("https://www.hp.webdiag.name", $path, $url);
                        $url =  str_replace("https://hp.webdiag.name", $path, $url);
                        $url =  str_replace("http://hp.webdiag.name", $path, $url);
                        $url =  str_replace("hp.webdiag.name", $pathNoHTTP, $url);
                    }

                    if (str_contains($request->data3_domain  ?? "", "project.webads3.com")) {

                        $url =  str_replace("https://www.project.webads3.com", $path, $url);
                        $url =  str_replace("https://project.webads3.com", $path, $url);
                        $url =  str_replace("http://project.webads3.com", $path, $url);
                        $url =  str_replace("project.webads3.com", $pathNoHTTP, $url);
                    }

                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);


                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink && !str_contains($tailLink, 'check_and_fix_server') && !str_contains($tailLink,  "check_server_car")) {

                        $firtUrl = substr($url, 0, 3);
                        if ($firtUrl == "www") {
                            $url =  str_replace("www.", "https://www.",  $url);
                        }

                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );

        $headers = $response1->getHeaders();
        $cookiesNew = "";
        $setTokenHeaders = isset($headers['Set-Token']) ? $headers['Set-Token'] : [];
        foreach ($setTokenHeaders as $setTokenHeader) {
            $splitItem = explode(";", $setTokenHeader);
            if (count($splitItem) > 0) {
                $splitKeyValue = explode("=", $splitItem[0], 2);
                if (count($splitKeyValue) > 1 && ($splitKeyValue[0] == "JSESSIONID" || $splitKeyValue[0] == "productIdV2" || $splitKeyValue[0] == "styleIdV2")) {
                    $cookiesNew =  $cookiesNew  . ($splitKeyValue[0] . "=" . $splitKeyValue[1] . ";");
                }
            }
        }

        if ($configAdmin  != null && !empty($cookiesNew)) {
            $expiresAt = Carbon::now()->addSeconds(6);
            Cache::put(json_encode(['cookies_haynes_str']), $cookiesNew, $expiresAt);
            $configAdmin->update([
                'cookies_haynes_str' => $cookiesNew
            ]);
        }

        return  $response1;
    }

    static public function  remove_element_reponse($response1)
    {
        $body = "";
        $ContentType = "";
        if ($response1 instanceof CurlReponse) {
            $body = $response1->getResponseBody();
            $ContentType =  $response1->getContentType();
        } else {
            $body = $response1->getBody();
            $ContentType = $response1->getHeader('Content-Type')[0] ?? "";
        }

        $actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];
        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            header("Content-Type:application/pdf");
        }
        // if (str_contains($tailLink, '.css') || str_contains($tailLink, '.css')) {
        //     header("Content-Type:text/css");
        // }
        // if (str_contains($tailLink, '.js')) {

        //     header("Content-Type:application/javascript");
        // }


        if (str_contains($ContentType, 'text/html') && !str_contains($path, 'app-alldata.')) {  //Tách svg ra
            $body =  str_replace("app.alldata.com", $pathNoHTTP, $body);
            $dom = HtmlDomParser::str_get_html($body);

            if ($dom  != null) {
                $embed = $dom->find('embed', 0);
                if ($embed  != null &&  $embed->attr != null && isset($embed->attr['src'])) {
                    if (str_contains($embed->attr['src'], '_svg')) {


                        $body = str_replace(
                            'type="image/svg+xml"',
                            'type="image/svg+xml" style="
                       
                        width: 250vw !important;
                        height: 250vw !important;
                        
                        "

                        ',
                            $body
                        );



                        echo $body;
                        die();
                        $urlXXX = strtok($tailLink, '?');
                        $your_array = explode("/", $urlXXX);
                        $last =  $your_array[count($your_array) - 1];

                        $repPath =   str_replace($last, $embed->attr['src'], $actual_link);
                        header("location: $repPath");
                    }
                }
            }

            echo $body;
            die();
        }





        if ($ContentType != null) {
            $ContentType = $ContentType ?? "";
            header('Content-type: ' .  $ContentType ?? "");

            echo $body;
            die();
        } else

        if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) {
            $body =  str_replace("app.alldata.com", $pathNoHTTP, $body);

            echo $body;
            die();
        }


        if (str_contains($ContentType, 'text/html')) {
            echo $body;
            die();
        }


        if (str_contains($tailLink, 'makes')) {
            header("Content-Type: application/octet-stream;charset=UTF-8");
            echo $body;
            die();
        }


        echo   $body;
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {


        try {
            $time_start = microtime(true);

            /// Diagzone
            $configAdmin =      ConfigAdmin::first();
            if ($cookies_str == null) {
                if ($configAdmin  != null && !empty($configAdmin->cookies_haynes_str)) {
                    $cookies_str = $configAdmin->cookies_haynes_str;
                }
            }
            $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
            $cookieArr  = CookiesUtils::cookieStrToArray($cookies_str);

            if (isset($cookieArr['DOMAIN_REQUEST_SERVER'])) {

                $data3_domain = $cookieArr['DOMAIN_REQUEST_SERVER'];

                if (str_contains($data3_domain, "hp.webdiag.name")) {
                    $response  = HaynesUtilsWeb::getResponse('https://hp.webdiag.name/touch/site/layout/makesOverview', "GET", $cookies_str, null);
                    $content = ($response->getBody()->getContents());

                    $_csrf = "";

                    if (preg_match('/<meta\s+name="_csrf"\s+content="([^"]+)"\s*\/?>/i', $content, $matches)) {
                        $_csrf = $matches[1];
                    }

                    $time_end = microtime(true);
                    if (str_contains($content, 'makesSection')) {
                        return [
                            'time_handle' => ($time_end - $time_start),
                            'status' => true,
                            'mess' => "",
                            'name' => 'digzone',
                            'phone' => '',
                            'com' => "",
                            '_csrf' => $_csrf
                        ];
                    }

                    return [
                        'status' => false,
                        'mess' => "ERROR COOKIES"
                    ];
                }

                if (str_contains($data3_domain, "project.webads3.com")) {

                    $response  = HaynesUtilsWeb::getResponse('https://www.project.webads3.com/touch/site/layout/makesOverview', "GET", $cookies_str, null);
                    $content = ($response->getBody()->getContents());


                    $_csrf = "";

                    $_csrf = "";
                    if (preg_match('/<meta\s+name="_csrf"\s+content="([^"]+)"\s*\/?>/i', $content, $matches)) {
                        $_csrf = $matches[1];
                    }

                    $time_end = microtime(true);
                    if (str_contains($content, 'makesSection')) {
                        return [
                            'time_handle' => ($time_end - $time_start),
                            'status' => true,
                            'mess' => "",
                            'name' => 'webads3',
                            'phone' => '',
                            'com' => "",
                            '_csrf' => $_csrf
                        ];
                    }
                    return [
                        'status' => false,
                        'mess' => "ERROR COOKIES"
                    ];
                }
            }
            ///



            $response  = HaynesUtilsWeb::getResponse('https://www.workshopdata.com/touch/site/layout/settingsContent', "GET", $cookies_str, null);
            $type = $response->getHeader('Content-Type')[0] ?? "";

            $content = ($response->getBody()->getContents());

            if (str_contains($type, 'text/html')) {

                $name = "";
                $user_id = "";
                $phone = "";
                $com = "";
                //Lấy username
                $dom = HtmlDomParser::str_get_html($content);
                if ($dom  != null) {

                    $cars = $dom->find('.cars', 0);
                    $trucks = $dom->find('.trucks', 0);
                    $password = $dom->find('#password', 0);

                    if (str_contains($content, '<label>Login name:</label>')) {
                        return [
                            'status' => false,
                            'mess' => "ERROR COOKIES LOGIN SCREEN"
                        ];
                    }

                    $active_cars = $dom->find('.active.cars', 0);
                    $disable_cars = $dom->find('.disabled.cars', 0); // == null thi co truck

                    if ($active_cars == null &&  $disable_cars  != null) {
                        return [
                            'status' => false,
                            'mess' => "ERROR NO ACTIVE CAR SCREEN"
                        ];
                    }





                    $_csrf = "";
                    $dom = HtmlDomParser::str_get_html($content);
                    if ($dom  != null) {
                        $inputElements = $dom->find('input');
                        foreach ($inputElements as $element) {
                            if ($element->getAttribute('name') === '_csrf') {
                                // Lấy giá trị của thuộc tính value
                                $csrfValue = $element->getAttribute('value');
                                $_csrf = $csrfValue;
                                break; // Dừng vòng lặp sau khi tìm thấy phần tử cần trích xuất giá trị
                            }
                        }
                    }


                    //Cập nhật ngôn ngữ cho Haynespro
                    $titlePage = $dom->find('.heading h2', 0);
                    if ($titlePage->text() != "Preferences") {

                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, 'https://www.workshopdata.com/touch/site/layout/settingsContent');
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
                        curl_setopt($ch, CURLOPT_HTTPHEADER, [
                            'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                            'accept-language: en,en-US;q=0.9,vi-VN;q=0.8,vi;q=0.7,zh-CN;q=0.6,zh;q=0.5,ca;q=0.4,ko;q=0.3,de;q=0.2,ar;q=0.1,hi;q=0.1',
                            'cache-control: no-cache',
                            'content-type: application/x-www-form-urlencoded',
                            'dnt: 1',
                            'origin: https://www.workshopdata.com',
                            'pragma: no-cache',
                            'priority: u=0, i',
                            'referer: https://www.workshopdata.com/touch/site/layout/settingsContent',
                            'sec-ch-ua: "Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
                            'sec-ch-ua-mobile: ?0',
                            'sec-ch-ua-platform: "macOS"',
                            'sec-fetch-dest: document',
                            'sec-fetch-mode: navigate',
                            'sec-fetch-site: same-origin',
                            'sec-fetch-user: ?1',
                            'upgrade-insecure-requests: 1',
                            'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        ]);
                        curl_setopt($ch, CURLOPT_COOKIE, $cookies_str);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, "companyName=&contactName=&address=&postalCode=&city=&telephone=&fax=&email=&vatNumber=&companyRegistrationNumber=&includeWorkshopInfoOnPrints=true&_includeWorkshopInfoOnPrints=on&oldPassword=&newPassword=&confirmPassword=&selectLanguageCode=en&selectCountry=_GB&measurement=0&maintType=0&_summarisedMode=on&manufactureCodesMode=true&_manufactureCodesMode=on&printServiceTimesMode=true&_printServiceTimesMode=on&_skipTruckAxleSelection=on&selectCurrency=GBP&labourRate1Description=&labourRate1=0.0&labourRate2Description=&labourRate2=0.0&labourRate3Description=&labourRate3=0.0&vatRate1Description=&vatRate1=0.0&vatRate2Description=&vatRate2=0.0&vatRate3Description=&vatRate3=0.0&_csrf=$_csrf");

                        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                        $response = curl_exec($ch);
                        curl_close($ch);

                        PushNotificationAdminJob::dispatch(
                            "Haynespro language was changed",
                            "Haynespro changed to English",
                            PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                        );
                    }

                    $namespan = $dom->find('#contactName', 0);

                    if ($namespan != null) {
                        $name  = str_replace(" ", "", ($namespan->value));
                    }

                    $phonespan = $dom->find('#telephone', 0);
                    if ($namespan != null) {
                        $phone  = str_replace(" ", "", ($phonespan->value));
                    }

                    $comspan = $dom->find('#companyName', 0);
                    if ($comspan != null) {
                        $com  = str_replace(" ", "", ($comspan->value));
                    }

                    $email = $dom->find('#email', 0);
                    if ($email != null) {
                        $email  = str_replace(" ", "", ($email->value));
                    }
                }
                $time_end = microtime(true);


                return [
                    'time_handle' => ($time_end - $time_start),
                    'status' => true,
                    'mess' => "",
                    'name' => $name,
                    'phone' => $phone,
                    'com' => $com,
                    'email' => $email,
                    '_csrf' => $_csrf
                ];
            }
        } catch (Exception $e) {

            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => 'Request ERRROR 1. ' . $e->getMessage()
            ];
        }
        return [
            'status' => false,
            'mess' => "Request ERRROR 2"
        ];
    }
}
