<?php

namespace App\Helper;

use Kreait\Firebase\Messaging\Notification;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;


class SendNotificationUtils
{

    static function send($data, $deviceTokens)
    {
        $factory = (new Factory)->withServiceAccount(config('firebase.credentials.file'));
        $messaging = $factory->createMessaging();
        $deviceToken = 'cy-tLLsAp06pu7VHFqYmxP:APA91bHI15a8W_JZp43nmGriiPfD3C6PNuN8-gQanM0DyEXuariOR46GODB1tsPj7GZKm-ybijFb8_OH0fOR2vmJNXa1k1mRZNzOkpUZd4vexEIjYZT8pdX8HqGToL-3uj_9ew25RCpg';
        $message = CloudMessage::fromArray([
            'notification' =>
            Notification::create($data['title'] ?? "", $data['body'] ?? ""),
            'data' => $data,
        ]);
        $sendReport = $messaging->sendMulticast($message, $deviceTokens);
        return $sendReport;
    }
}
