<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use App\Helper\HtmlUtils;
use App\Models\WebData;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class Partslink24UtilsMobileWeb
{

    static public function setCache($request, $key, $value)
    {
        $expiresAt = Carbon::now()->addMinutes(60);
        Cache::put(json_encode([$request == null || $request->user == null ? null : $request->user->email,  $key]), $value, $expiresAt);
    }
    static public function getCache($request, $key)
    {
        return Cache::get(json_encode([$request == null || $request->user == null ? null : $request->user->email, $key]));
    }
    /**
     * Lấy dữ liệu
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $proxy = null)
    {


        $tailLink = $_SERVER["REQUEST_URI"];

        if (
            str_contains($tailLink, 'p5vwag/extern/directAccess')
        ) {

            //Chặn request thừa
            $key = 'p5vwag/extern/directAccess'; // Key Cache dựa trên địa chỉ IP của client

            $allowedRequests = 1; // Số lượng yêu cầu tối đa trong 1 giây
            $cacheTime = 60; // Thời gian cache trong giây

            $currentTimestamp = time();
            $expireTimestamp = $currentTimestamp + $cacheTime;

            $count = Cache::get($key, 0);

            if ($count >= $allowedRequests) {
                return new Exception('Too many requests. Please try again later.');
            }

            if ($count === 0 || $currentTimestamp > $expireTimestamp) {
                // Reset count and set new expiration timestamp
                Cache::put($key, 1, $cacheTime);
            } else {
                // Increment count
                Cache::increment($key);
            }
        }



        if ($request->is_check != true) {

            if (
                str_contains($tailLink, 'user/logout.do') ||
                str_contains($tailLink, 'pl24-app/menu') ||
                str_contains($tailLink, 'dealer-management') ||
                str_contains($tailLink, 'my-account') ||
                str_contains($tailLink, 'pl24-app/settings') ||
                str_contains($tailLink, '1.0/logout')
            ) {
                $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();
                header("Location: https://$request->agency_sub." . $domainMain);
                exit;
            }
        }



        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        $configAdmin =      ConfigAdmin::first();
        if ($proxy === null) {


            if ($configAdmin  != null && !empty($configAdmin->proxy_partslink24)) {
                $proxy = $configAdmin->proxy_partslink24;
            }
        }

        if ($cookies_str == null) {
            $cookies_str = Cache::get(json_encode(['access_token_partslink24']));
            if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_partslink24_str)) {
                $cookies_str = $configAdmin->cookies_partslink24_str;
            }
        }



        //unset($headers_requests['user-agent']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['authorization']);
        unset($headers_requests['Authorization']);

        unset($headers_requests['upgrade-insecure-requests']);

        $headerArr  =  $headers_requests;;


        $headerArr['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';

        $jsonToken = json_decode($cookies_str, true);
        if (isset($jsonToken['access_token'])) {
            $access_token = $jsonToken['access_token'];
            $headerArr['authorization'] = "Bearer $access_token";
        }
        if (isset($jsonToken['cookie'])) {
            $headerArr['cookie']  = "PL24TOKEN=" . ($jsonToken['cookie']);
        }

        $client = new \GuzzleHttp\Client(
            []
        );

        $headerArr =  [
            'accept'             => $headers_requests['accept'] ?? $headers_requests['Accept'] ?? '*/*',
            'accept-language'    => 'en-US,en;q=0.5',
            'authorization'      => $headerArr['authorization'],
            'cookie'             => $headerArr['cookie'],
            'user-agent'         => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36'
        ];
        if (str_contains($link, 'pl24-appgtw/ext/api/1.0/session')) {
            $headerArr['accept'] = 'application/json';
        }


        $response1 = $client->request(
            $method,
            $link,
            [
                // 'cookies' => $jar,
                'headers' => $headerArr,
                'json' => $body,
                'form_params' => $body,
                'timeout' => 90, // Response timeout
                'connect_timeout' => 90, // Connection timeout,
                'on_stats' => (str_contains($tailLink, 'check_')) ? null : function (\GuzzleHttp\TransferStats $stats) {

                    $url = $stats->getHandlerStats()['redirect_url'];
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $pathNoHTTP = str_replace("http://", "", $path);
                    $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);


                    $url =  str_replace("https://m.partslink24.com", $path, $url);
                    $url =  str_replace("http://m.partslink24.com", $path, $url);
                    $url =  str_replace("https://partslink24.com", $path, $url);
                    $url =  str_replace("http://partslink24.com", $path, $url);

                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);


                    // if (str_contains($url, "logout.do")) {
                    //     throw new Exception("logout.");
                    // }

                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink) {

                        $firtUrl = substr($url, 0, 3);
                        if ($firtUrl == "www") {
                            $url =  str_replace("www.", "https://www.",  $url);
                        }


                        if (str_contains($tailLink, "changeLang")) {

                            parse_str(parse_url($tailLink)['query'], $params);
                            if (isset($params['changeLang'])) {
                                $changeLang = $params['changeLang'];

                                $url = StringUtils::addGetParamToUrl($url, 'changeLang', $changeLang);
                            }
                        }


                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );


        return  $response1;
    }

    static public function  remove_element_reponse($body, $contentType, $statusCode, $request)
    {

        $ContentType = $contentType;

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];

        $tailLink = $_SERVER["REQUEST_URI"];
        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            $ContentType = "application/pdf";
        } else 
        if (str_contains($tailLink, '.svg')) {
            $ContentType = "image/svg+xml";
        }




        //Save vao db
        if ($request != null && $request->method() == "GET" && $statusCode == 200) {
            try {
                if (
                    $request != null
                    // && !str_contains($contentType, "image")
                    && $body != null && $body != ""
                ) {
                    $webClass = new WebClass();
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::PARTSLINK24;
                    $webClass->method = $request->method();
                    $webClass->content_type =  $contentType;
                    $webClass->body =  $body;
                    $webClass->status_code =  $statusCode;

                    WebDataUtils::saveWebData($webClass);
                }
            } catch (Exception $e) {
            }
        }

        if ($ContentType) {

            if (str_contains($ContentType, 'text/html')  && !$request->isMethod('post')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                    <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag("js", new Date());
                
                    gtag("config", "G-KXWTNY3G2J");
                    </script>', $body);
            }


            if (str_contains($contentType, 'text/html')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/service/partlink_mobile.js?v=6"></script>', $body);
            }

            if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) {
                $body =  str_replace("https://m.partslink24.com", $path, $body);
                $body =  str_replace("http://m.partslink24.com", $path, $body);
                $body =  str_replace("https://partslink24.com", $path, $body);
                $body =  str_replace("http://partslink24.com", $path, $body);
                $body =  str_replace("partslink24.com", $pathNoHTTP, $body);


                $body =  str_replace("http://", "https://", $body);

                $cookies = $_COOKIE;
                $platform = $cookies['platform'] ?? request()->header('platform') ?? null;

                if ($request->isMethod('get') && str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);

                    if ($dom  != null) {


                        $menu = $dom->find('#MuiBottomNavigationAction-root', 4);

                        if ($menu != null) {
                            $menu->remove();
                        }


                        //replace html
                        $body2 =  $dom->innertext;
                        $body =  ($body2);
                    }


                    if ($request->user != null) {

                        $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_partslink24");
                        if ($remain_days > 0 && $remain_days < 10  && !$request->isMethod('post')) {
                            $appName = DomainConfigHelper::getConfig('appName');
                            $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script>

                                 siiimpleToast.alert(" Partslink24 expiry date is only ' . $remain_days . ' days, remember to renew!", {
                                  position: "top|center",
                                  margin: 15,
                                  delay: 0,
                                  duration: 5000,
                                });
                                </script>', $body);
                        }
                    }
                }
            }
        }

        header('Content-type: ' .  $ContentType ?? "");
        echo $body;
        die();
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        try {
            $time_start = microtime(true);


            $response  = Partslink24UtilsMobileWeb::getResponse('https://www.partslink24.com/pl24-appgtw/ext/api/1.0/session', "GET", $cookies_str, null, request());
            $content = ($response->getBody()->getContents());

            if (str_contains($content, "Unauthorized")) {
                return [
                    'status' => false,
                    'mess' => "Unauthorized"
                ];
            }

            $c = ConfigAdmin::first();

            $data = json_decode($content);


            $access_token  =  $data->token->access_token;

            $expiresAt = Carbon::now()->addSeconds(1);

            $object =  json_decode($c->cookies_partslink24_str, true);

            if (isset($object['access_token'])) {
                $object["access_token"] = $access_token;
            }
            Cache::put(json_encode(['access_token_partslink24']), json_encode($object), $expiresAt);
            $c->update([
                "cookies_partslink24_str" => json_encode($object),
                'cookies_partslink24_json_info' => json_encode($data)
            ]);

            $time_end = microtime(true);
            $dataFinal = [
                'time_handle' => ($time_end - $time_start),
                'status' => true,
                'mess' => "OK updated access token",
            ];
            return $dataFinal;
        } catch (Exception $e) {

            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage() . " Exception"
            ];
        }
        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
