<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use App\Models\WebData;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\TransferStats;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AutoDataItalyUtilsWeb
{

    const PATH_AUTODATA = "https://workshop.autodata-group.com";


    static public function  remove_element_reponse($body, $contentType, $statusCode, $request)
    {
        $ContentType = $contentType;
        $tailLink = $_SERVER["REQUEST_URI"];


        //Save vao db
        if ($request != null && $request->method() == "GET" && $statusCode == 200) {
            try {
                if ($request != null && !str_contains($tailLink, "ogin") && $body != null && $body != "" && !str_contains($body, '<input type="hidden" name="form_id" value="user_login" />')) {
                 
                    $webClass = new WebClass();
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::AUTODATA_ITALY;
                    $webClass->method = $request->method();
                    $webClass->content_type =  $contentType;
                    $webClass->body =  $body;
                    $webClass->status_code =  $statusCode;
                    
                    WebDataUtils::saveWebData($webClass);

                }
            } catch (Exception $e) {
            }
        }

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);


        if ($statusCode  != 200) {
            if ($statusCode  == 404) {
                header('HTTP/1.0 404 Not Found', true, 404);
            }
        }

        function isJson($string)
        {
            json_decode($string);
            return json_last_error() === JSON_ERROR_NONE;
        }
        if (str_contains($body, '{"main-service":"')) {
        } else
        if (!str_contains($tailLink, 'engines/codes') && !isJson($body)) {

            if (str_contains($ContentType, 'text/html')) {

                $dom = HtmlDomParser::str_get_html($body);
                if ($dom  != null) {
                    //Remove last web pc
                    $lastCom = $dom->find('#jobsRecentMain', 0);

                    if ($lastCom != null) {
                        $lastCom->remove();
                    }

                    $searchSwitch = $dom->find('.search-switch', 0);
                    if ($searchSwitch  != null) {
                        $searchSwitch->remove();
                    }

                    if ($lastCom != null) {
                        $lastCom->remove();
                    }

                    // Training
                    $Training = $dom->find('.adt-full', 0);
                    if ($Training  != null) {
                        $Training->remove();
                    }

                    //thay doi link home vehicles
                    $vehicleInfo = $dom->find('.technical-info a', 0);
                    if ($vehicleInfo  != null) {
                        $linkTo =  $vehicleInfo->getAttribute('href');

                        $manufacturer_id =   AutoDataItalyUtilsWeb::getCache($request, 'manufacturer_id');
                        $model_id =  AutoDataItalyUtilsWeb::getCache($request, 'model_id');

                        if (
                            str_contains($linkTo, "/w1/vehicles") && count(explode("/",  $linkTo)) == 4 &&
                            $manufacturer_id != null && $model_id != null
                        ) {
                            $vehicleInfo->setAttribute('href', "/w1/vehicles/$manufacturer_id/$model_id");
                        }
                    }


                    $footerP = $dom->find('.footer > p', 0) ?? $dom->find('#footer > p', 0);

                    if ($footerP != null) {

                        $manufacturer_id =   AutoDataItalyUtilsWeb::getCache($request, 'manufacturer_id');
                        $model_id =  AutoDataItalyUtilsWeb::getCache($request, 'model_id');

                        $vehicle_id =   AutoDataItalyUtilsWeb::getCache($request, 'vehicle_id');
                        $engine_id =  AutoDataItalyUtilsWeb::getCache($request, 'engine_id');
                        $engine_name =   AutoDataItalyUtilsWeb::getCache($request, 'engine_name');
                        $route_name =  AutoDataItalyUtilsWeb::getCache($request, 'route_name');
                        $module_id =   AutoDataItalyUtilsWeb::getCache($request, 'module_id');
                        $back =  AutoDataItalyUtilsWeb::getCache($request, 'back');


                        $footerP->innertext = "Autodata Limited 1972-2023 " .
                            "|manufacturer_id:" . $manufacturer_id .
                            "|model_id:" . $model_id .

                            "|vehicle_id:<span id='vehicle_id'>" . $vehicle_id . "</span>|engine_id:" . $engine_id .
                            "|route_name:" . $route_name;
                    }



                    //Ô nhập biển số xe
                    $vrm_main_block = $dom->find('.vrm_main_block', 0);

                    if ($vrm_main_block != null) {
                        $vrm_main_block->remove();
                    }
                }

                $body =  $dom == null ? $body : $dom->innertext;




                if ($request->user != null) {

                    $manufacturer_id =   AutoDataItalyUtilsWeb::getCache($request, 'manufacturer_id');
                    $model_id =  AutoDataItalyUtilsWeb::getCache($request, 'model_id');

                    if ($manufacturer_id  != null &&  $model_id  != null) {
                        $body =  HtmlUtils::addTagHtmlOnHead('<meta name="manufacturer_id" content="' . $manufacturer_id . '" />', $body);
                        $body =  HtmlUtils::addTagHtmlOnHead('<meta name="model_id" content="' . $model_id . '" />', $body);
                    }

                    $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_autodata");
                    if ($remain_days > 0 && $remain_days < 10  && !$request->isMethod('post')) {
                        $appName = DomainConfigHelper::getConfig('appName');
                        $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                        $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);


                        $body =  HtmlUtils::addTagHtmlOnHead('<script>

                        setTimeout(
                            function() {

                                siiimpleToast.alert(".", {
                                    position: "top|center",
                                    margin: 15,
                                    delay: 1000,
                                    duration: 1,
                                    style: {
                                        height:1
                                    },
                                  });

                                  siiimpleToast.alert("('.$appName.') Autodata expiry date is only ' . $remain_days . ' days, remember to renew!", {
                                    position: "top|center",
                                    margin: 15,
                                    delay: 0,
                                    duration: 5000,
                                  });
                            }, 1000);


                            </script>', $body);
                    }
                }
            }

            if (str_contains($tailLink, '.js')) {
                $ContentType = 'application/javascript';
            }

            if (str_contains($ContentType, 'text/html') && str_contains($body, '</head>')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/service/autodata.js?v=6"></script>', $body);
            }


            if (str_contains($ContentType, 'text/html')  && !$request->isMethod('post')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                        <script>
                        window.dataLayer = window.dataLayer || [];
                        function gtag(){dataLayer.push(arguments);}
                        gtag("js", new Date());
                    
                        gtag("config", "G-KXWTNY3G2J");
                        </script>', $body);
            }

            //     //Khong phai iframe thi them vao body
            //     if (isset($_SERVER['HTTP_SEC_FETCH_DEST']) && $_SERVER['HTTP_SEC_FETCH_DEST'] != 'iframe') {
            //         $body =  HtmlUtils::addTagHtmlOnBody(' <div id="google_translate_element" style="z-index: 410;position: inherit;"></div>
            //             <script> 
            //             function googleTranslateElementInit() {
            //             new google.translate.TranslateElement({
            //                 pageLanguage: "en"
            //             }, "google_translate_element");
            //             }
            //             </script>
            //             <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script> ', $body);
            //     }

            // }


        }

        if (!str_contains($tailLink, 'engines/codes') && (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) && !str_contains($tailLink, '.js')) {
            $body =  str_replace("https://assets.autodata-group.com", $path, $body);
            $body =  str_replace("http://assets.autodata-group.com", $path, $body);
            $body =  str_replace("https://workshop.autodata-group.com", $path, $body);
            $body =  str_replace("bam.nr-data.net", "bam.nr-data3.net", $body);

            $body =  str_replace("//assets.autodata-group.com", "//" . $pathNoHTTP, $body);
            $body =  str_replace("workshop.autodata-group.com", $pathNoHTTP, $body);
            $body =  str_replace("assets.autodata-group.com", $pathNoHTTP, $body);
            $body =  str_replace("http://", "https://", $body);
            $appName = DomainConfigHelper::getConfig('appName');
            $body =  str_replace("Your payment details need updating", 'Welcome to '.$appName, $body);
            $body =  str_replace("Please contact us now to maintain your Autodata access", 'Have a productive day', $body);
            $body =  str_replace("+44 (0)1628 688 111", '+86189 8087 6959', $body);
            $body =  str_replace("+44 (0)330 0535 111", "+86189 8087 6959", $body);
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null &&   $configAdmin->autodata_italy_info != null && $request->user != null) {

                if (str_contains($ContentType, 'text/html')) {
                    $dom = HtmlDomParser::str_get_html($body);
                    if ($dom != null) {
                        $moreAccount = $dom->find('li.more a span', 0);
                        if ($moreAccount  != null) {
                            $moreAccount->innertext = '<span> ' . $request->user->name . ' <i class="fa fa-sort-desc" aria-hidden="true"></i> <i class="fa fa-sort-asc" aria-hidden="true"></i> </span>';
                        }

                        $accountInfo = $dom->find('li.more ul li.account-info', 0);
                        if ($accountInfo  != null) {
                            $accountInfo->innertext = '<span>' . $request->user->username . '</span>';
                        }


                        // Training
                        $Training = $dom->find('.adt-full', 0);
                        if ($Training  != null) {
                            $Training->remove();
                        }

                        $body =  $dom == null ? $body : $dom->innertext;
                    }
                }


                if (str_contains($tailLink, 'w2/api/user')) {
                    $ContentType = "application/json";
                    $arr = json_decode($body, true);
                    try {
                        $arr['name'] = $request->user->name;
                        $arr['username'] = $request->user->name;
                        $arr['email'] = $request->user->email;
                        $arr['invoice_email'] = $request->user->email;
                        $arr['id'] = 1234567;
                        $arr['garage']['email'] = $request->user->email;
                        $arr['garage']['account_number'] = $request->user->email;
                        $arr['garage']['name'] = $request->user->name;
                        $arr['garage']['address']['city'] = '';
                        $arr['garage']['address']['postcode'] = '';
                        $arr['garage']['address']['country'] = '';
                        $arr['garage']['address']['country_code'] = '';
                        $arr['garage']['address']['address1'] = '';
                        $arr['garage']['address']['address1'] = $request->user->address_detail;
                    } catch (Exception $e) {
                    }
                    $json_res = json_encode($arr, true);
                    $body = $json_res;
                }
            }
        }


        header('Content-type: ' .  $ContentType ?? "");
        echo $body;
        die();
    }


    static public function setCache($request, $key, $value)
    {
        $expiresAt = Carbon::now()->addMinutes(60);
        Cache::put(json_encode([$request == null || $request->user == null ? null : $request->user->id,  $key]), $value, $expiresAt);
    }
    static public function getCache($request, $key)
    {
        return Cache::get(json_encode([$request == null || $request->user == null ? null : $request->user->id, $key]));
    }

    /**
     * Thông tin server
     */
    static public function  getResponse($link, $method = "GET", $body = [], $cookies_str = null, $request = null, $proxy = null)
    {


        if ($proxy === null) {
            // $configAdmin =      ConfigAdmin::first();

            // if ($configAdmin  != null && !empty($configAdmin->proxy_autodata)) {
            //     $proxy = $configAdmin->proxy_autodata;
            // }

            $proxy  = ProxyUtils::chooseProxyForAutodataItaly();
        }

        $tailLink = $_SERVER["REQUEST_URI"];


        if (!str_contains($tailLink, "js") &&  !str_contains($tailLink, "css")) {
            //Chặn request thừa
            $key = 'throttleitaly:' . $proxy; // Key Cache dựa trên địa chỉ IP của client
            $allowedRequests = 15; // Số lượng yêu cầu tối đa trong cacheTime giây
            $cacheTime = 5; // Thời gian cache trong giây

            $currentTimestamp = time();
            $expireTimestamp = $currentTimestamp + $cacheTime;

            $count = Cache::get($key, 0);
            if ($count >= $allowedRequests) {
                return new Exception('Too many requests. Please try again later.');
            }
            if ($count === 0 || $currentTimestamp > $expireTimestamp) {
                // Reset count and set new expiration timestamp
                Cache::put($key, 1, $cacheTime);
            } else {
                // Increment count
                Cache::increment($key);
            }
        }
        ///


        if (
            str_contains($tailLink, 'reg-sess')
        ) {
            return null;
        }

        $w1 = [
            "service-schedules",
            "service-illustrations",
            "service-indicator",
            "service-brakes",
            "engine-oil",
            "service-transmission-variants",
            "service-summary-variants",
            "service-ac",
            "engine-management",
            "camshaft-drive-system",
            "auxiliary-drive-belts",
            "clutches",
            "anti-lock-brake-systems",
            "wheel-alignment",
            "tyre-pressures-variants",
            "tyre-pressure-monitoring-system",
            "tyres",
            "airbags",
            "key-programming",
            "air-conditioning",
            "battery-disconnection-and-reconnection",
            "diagnostic-trouble-codes",
            "control-module-pin-data",
            "electrical-component-locations",
            "bulbs",
            "fuses-and-relays",
            "technical-specifications",
            "repair-times-variants",
            "known-fixes",
            "vin-plate-location",
            "warning-lamp-and-symbols",
            "wiring-diagrams",
            "diesel-exhaust-gas-aftertreatment",
        ];

        $w2 = [
            'engine-oil',
            'tyres',
            "tyre-pressure-monitoring-system",
            "service-indicator",
            "key-programming",
            "wheel-alignment",
            "battery-disconnection-and-reconnection",
            "electric-parking-brake",
            "diesel-exhaust-gas-aftertreatment",
            "camshaft-drive-system",
            "service-advisor",
            "static-content",
            "diagnostic-trouble-codes",
        ];

        $vehicle_id =   AutoDataItalyUtilsWeb::getCache($request, 'vehicle_id');
        $engine_id =  AutoDataItalyUtilsWeb::getCache($request, 'engine_id');
        $route_name = $request == null ? null : ($request->input('route_name') ?? null);

        if (
            $method == "GET" &&

            str_contains($tailLink, '/w1/manufacturers') &&  str_contains($tailLink, 'engines')
            && in_array($route_name, $w2) &&  $vehicle_id != null &&  !str_contains($tailLink, 'change_engine=true')
        ) {


            $engine_name =   AutoDataItalyUtilsWeb::getCache($request, 'engine_name');

            $module_id =   AutoDataItalyUtilsWeb::getCache($request, 'module_id');
            $back =  AutoDataItalyUtilsWeb::getCache($request, 'back');

            header("Location: /w2/$route_name/$vehicle_id"); //?cacao=drcar
            exit;
        }

        if (
            $method == "GET" &&
            str_contains($tailLink, '/w1/manufacturers')  &&  str_contains($tailLink, 'change_engine=true')
        ) {


            header("Location: /w1/model-selection");
            exit;
        }


        if ($request != null && $request->manufacturer_id != null && $request->model_id != null) {
            AutoDataItalyUtilsWeb::setCache($request, 'manufacturer_id', $request->manufacturer_id);
            AutoDataItalyUtilsWeb::setCache($request, 'model_id', $request->model_id);
        }

        if (
            $request != null && $request->vehicle_id != null
            && $request->engine_id != null
            && $request->engine_name != null
            && $request->route_name != null
        ) {
            AutoDataItalyUtilsWeb::setCache($request, 'vehicle_id', $request->vehicle_id);
            AutoDataItalyUtilsWeb::setCache($request, 'engine_id', $request->engine_id);
            AutoDataItalyUtilsWeb::setCache($request, 'engine_name', $request->engine_name);
            AutoDataItalyUtilsWeb::setCache($request, 'route_name', $request->route_name);
            AutoDataItalyUtilsWeb::setCache($request, 'module_id', $request->module_id);
            AutoDataItalyUtilsWeb::setCache($request, 'back', $request->back);
        }

        $hasSelectModel = false;
        if ($request != null) {
            $manufacturer_id =   AutoDataItalyUtilsWeb::getCache($request, 'manufacturer_id');
            $model_id =  AutoDataItalyUtilsWeb::getCache($request, 'model_id');


            if (
                $method  == "GET" &&
                $manufacturer_id != null &&

                !str_contains($tailLink, '/w2/')  &&
                $model_id != null &&
                (str_contains($link, 'w1/vehicles') ||   str_contains($link, 'w1/diagram')  || str_contains($link, 'w1/manufacturers') ||  in_array(request('route_name'), $w1))
                &&
                !str_contains($link, 'vehicles/variants')
            ) {

                $hasSelectModel =  true;
                try {
                    AutoDataItalyUtilsWeb::getResponse(
                        'https://workshop.autodata-group.com/selection/save-in-jobfolder/0/undefined',
                        'POST',
                        [
                            "manufacturer_id" =>  $manufacturer_id,
                            "model_id" => $model_id,
                        ],
                        null,
                        $request,
                        null,
                        true
                    );
                } catch (Exception $e) {
                }
            }
        }

        if ($request != null) {
            $manufacturer_id =   AutoDataItalyUtilsWeb::getCache($request, 'manufacturer_id');
            $model_id =  AutoDataItalyUtilsWeb::getCache($request, 'model_id');

            $vehicle_id =   AutoDataItalyUtilsWeb::getCache($request, 'vehicle_id');
            $engine_id =  AutoDataItalyUtilsWeb::getCache($request, 'engine_id');
            $engine_name =   AutoDataItalyUtilsWeb::getCache($request, 'engine_name');
            $route_name =  AutoDataItalyUtilsWeb::getCache($request, 'route_name');
            $module_id =   AutoDataItalyUtilsWeb::getCache($request, 'module_id');
            $back =  AutoDataItalyUtilsWeb::getCache($request, 'back');


            if (
                $method  == "GET" &&
                !str_contains($tailLink, '/w2/')  &&
                (str_contains($link, 'vehicles/variants') || str_contains($link, 'change_engine=true')  ||
                    in_array(request('route_name'), $w1)   ||
                    !str_contains($link, 'w1/vehicles/'))

            ) {
                if ($vehicle_id == null && $engine_id == null) {

                    AutoDataItalyUtilsWeb::getResponse(
                        "https://workshop.autodata-group.com/w1/vehicle-selection/mid/AUDd082073",
                        'POST',
                        [],
                        null,
                        $request,
                        null,
                        true
                    );
                }
                if (
                    $manufacturer_id != null   && $model_id != "" &&  $hasSelectModel == false
                ) {

                    try {
                        AutoDataItalyUtilsWeb::getResponse(
                            'https://workshop.autodata-group.com/selection/save-in-jobfolder/0/undefined',
                            'POST',
                            [
                                "manufacturer_id" =>  $manufacturer_id,
                                "model_id" => $model_id,
                            ],
                            null,
                            $request,
                            null,
                            true
                        );
                    } catch (Exception $e) {
                    }
                }
                if (
                    $vehicle_id != null   && $vehicle_id != ""
                ) {


                    AutoDataItalyUtilsWeb::getResponse(
                        "https://workshop.autodata-group.com/w1/vehicle-selection/mid/$vehicle_id",
                        'POST',
                        [
                            "vehicle_id" =>  $vehicle_id,
                            "engine_id" => $engine_id,
                            "engine_name" =>  $engine_name,
                            "route_name" => $route_name,
                            "module_id" =>  $module_id,
                            "back" => $back,
                        ],
                        null,
                        $request,
                        null,
                        true
                    );
                }
            }



            $vehicle_id =   AutoDataItalyUtilsWeb::getCache($request, 'vehicle_id');
            if (
                $method  == "GET" &&
                (
                    ((str_contains($link, 'model-selection'))
                        && !str_contains($link, "model-selection/manufacturers") &&
                        $vehicle_id  != null)
                    ||
                    (str_contains($link, 'w1/vehicles/') && !str_contains($link, 'w1/vehicles/variants'))

                    ||
                    str_contains($link, 'change_engine=true')
                )
            ) {

                try {
                    AutoDataItalyUtilsWeb::setCache($request, 'vehicle_id', null);
                    AutoDataItalyUtilsWeb::setCache($request, 'engine_id',  null);
                    AutoDataItalyUtilsWeb::setCache($request, 'engine_name',  null);
                    AutoDataItalyUtilsWeb::setCache($request, 'route_name',  null);
                    AutoDataItalyUtilsWeb::setCache($request, 'module_id',  null);
                    AutoDataItalyUtilsWeb::setCache($request, 'back', null);

                    AutoDataItalyUtilsWeb::getResponse(
                        "https://workshop.autodata-group.com/w1/vehicle-selection/mid/$vehicle_id",
                        'POST',
                        [],
                        null,
                        $request,
                        null,
                        true
                    );
                } catch (Exception $e) {
                }
            }
        }



        if (
            str_contains($tailLink, '/w1/manage-account') ||
            str_contains($tailLink, '/settings/garage') ||
            str_contains($tailLink, '/support/page') ||
            str_contains($tailLink, '/user/logout')
        ) {
            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();
            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }


        if ($cookies_str == null) {
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null && !empty($configAdmin->cookies_autodata_italy_str)) {
                $cookies_str = $configAdmin->cookies_autodata_italy_str;
            }
        }


        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $arr = CookiesUtils::cookieStrToArray($cookies_str);


        $headerArr = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headerArr[$key] = $value[0];
                }
            }
        }

        unset($headerArr['cookie']);
        unset($headerArr['host']);
        unset($headerArr['connection']);
        unset($headerArr['dnt']);
        unset($headerArr['referer']);
        unset($headerArr['origin']);
        unset($headerArr['cf-ray']);
        unset($headerArr['content-length']);
        unset($headerArr['cf-connecting-ip']);

        unset($headerArr['user-agent']);
        unset($headerArr['User-Agent']);

        $headerArr['user-agent'] = "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.145";

        //  $arr = json_decode($cookies_autodata);

        $jar = new \GuzzleHttp\Cookie\CookieJar();


        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {


                if ($key == "access_token") {
                } else {
                    $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                        'Name'     => $key,
                        'Value'    =>  $value,
                        'Domain'   => '.autodata-group.com',
                        'Path'     => '/',
                        // 'Max-Age'  => $item['name'],
                        'Expires'  => time() + (10 * 365 * 24 * 60 * 60),
                    ]));
                }
            }
        }



        $client = new \GuzzleHttp\Client();

        try {
            $response1 = $client->request(
                $method,
                $link,
                [
                    'cookies' => $jar,
                    'headers' =>   $headerArr,
                    'timeout' => 25, // Response timeout
                    'connect_timeout' => 25, // Connection timeout
                    'form_params' => $body,
                    'proxy' => $proxy,
                    'on_stats' => !str_contains($tailLink, "w2") ? null : function (\GuzzleHttp\TransferStats $stats) {

                        $url = $stats->getHandlerStats()['redirect_url'];
                        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";

                        $url =  str_replace("https://assets.autodata-group.com", $path, $url);

                        $url =  str_replace("https://workshop.autodata-group.com", $path,  $url);
                        $url =  str_replace(" ", "",  $url);

                        $tailLink = $_SERVER["REQUEST_URI"];

                        if ($url != null && $url != "" && str_contains($tailLink, '/w1/')) {



                            $url =  str_replace("http://", "https://", $url);

                            // $parts = parse_url($url);
                            // parse_str($parts['query'], $query);
                            // if(str_contains($tailLink, 'w1/manufacturers/', ) && str_contains( $url ,'vehicles/variants/' )) {

                            // }

                            // vehicles/variants/camshaft-drive-system/TOY24901?route_name=camshaft-drive-system&module=DBM

                            header("Location: $url");
                            exit;
                        }
                    }
                ],

            );
        } catch (ClientException $e) {
            $status = $e->getResponse()->getStatusCode();

            if ($status == 404) {
                $response1 = $e->getResponse();
                return  $response1;
            }
            throw $e;
        }


        //Chọn model
        if (
            $method  == "GET" &&
            $tailLink != '/'
            &&
            ((str_contains($link, 'w1/vehicles')
                &&
                !str_contains($link, 'w1/vehicles/variants')
            ) ||
                str_contains($link, 'w1/manufacturers/')
                ||
                in_array(request('route_name'), $w1)
            )

        ) {

            $max_try = 5;
            $queries = array();
            parse_str($_SERVER['QUERY_STRING'] ?? "", $queries);
            $number_try = (int)($queries['number_try'] ?? 0) + 1;

            //Check vehicles home valid
            $body = $response1->getBody();
            $ContentType = $response1->getHeader('Content-Type')[0] ?? "";

            $valid = AutoDataItalyUtilsWeb::checkValidContent($request, $ContentType, $body, $tailLink, $link, $w1);

            if ($valid == false) {
                $tailLink = AutoDataItalyUtilsWeb::addToUrl2C($tailLink, "number_try", $number_try);
                header("Location: $tailLink");
                exit;
            }
        }

        //
        //Chọn engine
        $vehicle_id =   AutoDataItalyUtilsWeb::getCache($request, 'vehicle_id');

        if (
            $method  == "GET" &&
            $tailLink != '/'
            &&
            (
                (!str_contains($link, 'w2') &&
                    !str_contains($link, 'w1/vehicles') &&
                    $vehicle_id != null)
                ||
                str_contains($link, 'w1/vehicles/variants') && request("vehicle_id") != null
                ||
                str_contains($link, 'w1/diagram') && request("vehicle_id") != null
                ||
                in_array(request('route_name'), $w1)
            )
        ) {


            $max_try = 5;
            $queries = array();
            parse_str($_SERVER['QUERY_STRING' ?? ""], $queries);
            $number_try = (int)($queries['number_try'] ?? 0) + 1;

            //Check vehicles home valid
            $body = $response1->getBody();
            $ContentType = $response1->getHeader('Content-Type')[0] ?? "";


            if (str_contains($ContentType, 'text/html')) {  //Tách svg ra
                $dom = HtmlDomParser::str_get_html($body);

                if ($dom  != null) {

                    $manufacturer_id =   AutoDataItalyUtilsWeb::getCache($request, 'manufacturer_id');
                    $model_id =   AutoDataItalyUtilsWeb::getCache($request, 'model_id');
                    $changeEngine = $dom->find('.change-engine', 0);
                    if ($changeEngine != null &&  $manufacturer_id  != null &&   $model_id  != null) {

                        if (!str_contains($changeEngine->parent->innertext, $manufacturer_id) && !str_contains($changeEngine->parent->innertext, $model_id) && $number_try < $max_try) {

                            $tailLink = AutoDataItalyUtilsWeb::addToUrl2C($tailLink, "number_try", $number_try);
                            header("Location: $tailLink");
                            exit;
                        }
                    }
                    ////

                    $technicalInfo = $dom->find('.tech-info', 0);
                    if ($technicalInfo != null &&  $vehicle_id  != null) {

                        if (!str_contains($technicalInfo->innertext, $vehicle_id) &&  $number_try < $max_try) {

                            $tailLink = AutoDataItalyUtilsWeb::addToUrl2C($tailLink, "number_try", $number_try);
                            header("Location: $tailLink");
                            exit;
                        }
                    }
                }
            }
        }



        parse_str($_SERVER['QUERY_STRING'] ?? "", $queries);
        $timesTry = (int)($queries['number_try'] ?? 0);
        $response1->timesTry = $timesTry;

        $response1->timesTry = 0;
        return  $response1;
    }

    static public function addToUrl2C($url, $key, $value = null)
    {
        $query = parse_url($url, PHP_URL_QUERY);
        if ($query) {
            parse_str($query, $queryParams);
            $queryParams[$key] = $value;
            $url = str_replace("?$query", '?' . http_build_query($queryParams), $url);
        } else {
            $url .= '?' . urlencode($key) . '=' . urlencode($value);
        }
        return $url;
    }


    static public function checkValidContent($request, $ContentType, $body,  $tailLink, $link)
    {
        $w1 = [
            "service-schedules",
            "service-illustrations",
            "service-indicator",
            "service-brakes",
            "engine-oil",
            "service-transmission-variants",
            "service-summary-variants",
            "service-ac",
            "engine-management",
            "camshaft-drive-system",
            "auxiliary-drive-belts",
            "clutches",
            "anti-lock-brake-systems",
            "wheel-alignment",
            "tyre-pressures-variants",
            "tyre-pressure-monitoring-system",
            "tyres",
            "airbags",
            "key-programming",
            "air-conditioning",
            "battery-disconnection-and-reconnection",
            "diagnostic-trouble-codes",
            "control-module-pin-data",
            "electrical-component-locations",
            "bulbs",
            "fuses-and-relays",
            "technical-specifications",
            "repair-times-variants",
            "known-fixes",
            "vin-plate-location",
            "warning-lamp-and-symbols",
            "wiring-diagrams",
            "diesel-exhaust-gas-aftertreatment",
        ];
        $vehicle_id =   AutoDataItalyUtilsWeb::getCache($request, 'vehicle_id');
        $valid = true;

        if (
            $request->method() == "GET" &&
            $tailLink != '/'
            &&
            (
                (!str_contains($link, 'w2') &&
                    !str_contains($link, 'w1/vehicles') &&
                    $vehicle_id != null)
                ||
                str_contains($link, 'w1/vehicles/variants') && request("vehicle_id") != null
                ||
                str_contains($link, 'w1/diagram') && request("vehicle_id") != null
                ||
                in_array(request('route_name'), $w1)
            )
        ) {

            if (str_contains($ContentType, 'text/html')) {  //Tách svg ra
                $dom = HtmlDomParser::str_get_html($body);


                if ($dom  != null) {

                    $manufacturer_id =   AutoDataItalyUtilsWeb::getCache($request, 'manufacturer_id');
                    $model_id =   AutoDataItalyUtilsWeb::getCache($request, 'model_id');

                    $changeEngine = $dom->find('.change-engine', 0);
                    $technicalInfo = $dom->find('.technical-info', 0);
                    $estimateCal = $dom->find('.estimate-cal', 0);

                    if ($changeEngine != null &&  $manufacturer_id  != null &&   $model_id  != null) {
                        if (!str_contains($changeEngine->parent->innertext, $manufacturer_id) && !str_contains($changeEngine->parent->innertext, $model_id) && $number_try < $max_try) {
                            $valid = false;
                        }
                    } else 
                if ($technicalInfo != null &&  $manufacturer_id  != null &&   $model_id  != null) {
                        if (!str_contains($technicalInfo->innertext, $manufacturer_id) && !str_contains($technicalInfo->innertext, $model_id) && $number_try < $max_try) {
                            $valid = false;
                        }
                    } else 
                if ($estimateCal != null &&  $manufacturer_id  != null &&   $model_id  != null) {
                        if (!str_contains($estimateCal->innertext, $manufacturer_id) && !str_contains($estimateCal->innertext, $model_id) && $number_try < $max_try) {
                            $valid = false;
                        }
                    }
                }
            }
        }
        return $valid;
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        $time_start = microtime(true);
        try {

            $response  = AutoDataItalyUtilsWeb::getResponse(
                'https://workshop.autodata-group.com/w2/api/user',
                "GET",
                [],
                $cookies_str,
                null,
                $proxy,
            );

            $content = ($response->getBody()->getContents());
            if (str_contains($content, "Login page")) {
                return [
                    'status' => false,
                    'mess' => "ERROR COOKIES"
                ];
            }

            $user_name = "";
            $user_id = "";
            $name = "";
            $full_name = "";
            $email = "";
            $account_number = "";
            $phone_number = "";

            //Lấy username
            $info_account_list = json_decode($content);
            $user_name = $info_account_list->username ?? "";
            $account_number = $info_account_list->garage->id ?? "";
            $full_name = $info_account_list->garage->name ?? "";
            $user_id = $info_account_list->garage->user_id ?? "";
            $email = $info_account_list->garage->email ?? "";
            $phone_number = $info_account_list->garage->address->phone_number ?? "";

            $time_end = microtime(true);

            return [
                'time_handle' => ($time_end - $time_start),
                'status' => true,
                'mess' => "",
                'name' => $name,
                'email' => $email,
                'user_id' => $user_id,
                'full_name' => $full_name,
                'user_name' => $user_name,
                'account_number' => $account_number,
                'phone_number' => $phone_number,

            ];
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage()
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
