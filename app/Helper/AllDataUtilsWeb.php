<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use App\Models\WebData;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use Illuminate\Support\Facades\Cache;

class AllDataUtilsWeb
{

    /**
     * Thông tin server
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $proxy = null)
    {


        $tailLink = $_SERVER["REQUEST_URI"];

        $link = str_replace('/locale/en_GB/', '/locales/en_GB/', $link);
        $tailLink = str_replace('/locale/en_GB/', '/locales/en_GB/', $tailLink);

        if (
            str_contains($tailLink, '/user/profile') ||
            str_contains($tailLink, '/user/settings') ||
            str_contains($tailLink, 'logout.do')
        ) {
            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();
            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }




        if ($cookies_str == null) {
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null && !empty($configAdmin->cookies_alldata_str)) {
                $cookies_str = $configAdmin->cookies_alldata_str;
            }
        }


        if ($proxy == null) {
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null && !empty($configAdmin->proxy_alldata)) {
                $proxy = $configAdmin->proxy_alldata;
            }
        }

        function capitalizeArrayKeys($array)
        {
            $newArray = [];
            foreach ($array as $key => $value) {
                $newKey = Helper::capitalizeAfterDash($key);
                $newArray[$newKey] = $value;
            }
            return $newArray;
        }

        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $arr = CookiesUtils::cookieStrToArray($cookies_str);

        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);
        unset($headers_requests['accept-language']);
        unset($headers_requests['cf-connecting-ip']);
        unset($headers_requests['x-forwarded-for']);
        unset($headers_requests['cdn-loop']);
        unset($headers_requests['user-agent']);
        unset($headers_requests['referer']);

        $headerArr  =  $headers_requests;


        $headerArr = [
            'User-Agent'       =>  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:134.0) Gecko/20100101 Firefox/134.0',
            'Accept'           =>  $headers_requests['accept'] ?? $headers_requests['Accept'] ?? '*/*',
            'Accept-Language'  =>  'en-US,en;q=0.5', //$headers_requests['Accept-Language'] ?? $headers_requests['accept-language'] ?? 
            'Accept-Encoding'  =>  $headers_requests['Accept-Encoding'] ?? $headers_requests['accept-encoding'] ?? 'gzip, deflate, br, zstd',
            'Content-Type'     =>  $headers_requests['Content-Type'] ?? $headers_requests['content-type'] ?? 'application/json',
            'Cookie'           =>  $cookies_str,


        ];

        foreach ($headerArr as $key => $value) {
            if (strpos($key, 'Sec') === 0) {
                unset($headerArr[$key]);
            }
        }
        if ($method == "POST" && str_contains($tailLink, '/search')) {
            $headerArr['cookie'] = $cookies_str;
            $res = CurlUtils::requestAllEU($method, $link, [
                'headers' =>  $headerArr,
                'cookie' =>  $cookies_str,
                'form_params' => $body,
                // 'proxy' => $proxy
            ]);
            return $res;
        }

        $jar = new \GuzzleHttp\Cookie\CookieJar();

        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {

                $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name'     => $key,
                    'Value'    =>  $value,
                    'Domain'   => '.alldata.com',
                    'Path'     => '/',
                    // 'Max-Age'  => $item['name'],
                    //'Expires'  => $item['name'],
                    // 'Secure'   => $item['name'],
                    // 'Discard'  => $item['name'],
                    //  'HttpOnly' => $item['name'],
                ]));
            }
        }



        $client = new \GuzzleHttp\Client();

        $response1 = $client->request(
            $method,
            $link,
            [
                // 'cookies' => $jar,
                'headers' => $headerArr,
                'form_params' => $body,
                'timeout' => 15, // Response timeout
                'connect_timeout' => 15, // Connection timeout,
                'proxy' => $proxy,
                'on_stats' => function (\GuzzleHttp\TransferStats $stats) {

                    $url = $stats->getHandlerStats()['redirect_url'];
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";


                    $url =  str_replace("https://app.alldata.com", $path, $url);
                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);

                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink && !str_contains($url, 'alldata/user/login',)) {

                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );


        // $headers = $response1->getHeaders();
        // $cookiesNew = "";
        // $cookieLast = CookiesUtils::cookieStrToArray($cookies_str);

        // if (count($cookieLast) > 0) {
        //     $setTokenHeaders = isset($headers['Set-Token']) ? $headers['Set-Token'] : [];
        //     foreach ($setTokenHeaders as $setTokenHeader) {
        //         $splitItem = explode(";", $setTokenHeader);
        //         if (count($splitItem) > 0) {
        //             $splitKeyValue = explode("=", $splitItem[0], 2);

        //             $cook_key = $splitKeyValue[0];
        //             $cook_value = $splitKeyValue[1];
        //             $cookieLast[$cook_key] = $cook_value;
        //         }
        //     }

        //     $cookiesNew = CookiesUtils::cookiesKeyValueToStr($cookieLast);
        // }

        // if ($configAdmin  != null && !empty($cookiesNew)) {
        //     $expiresAt = Carbon::now()->addSeconds(6);
        //     Cache::put(json_encode(['cookies_alldata_str']), $cookiesNew, $expiresAt);
        //     $configAdmin->update([
        //         'cookies_alldata_str' => $cookiesNew
        //     ]);
        // }

        return  $response1;
    }

    static public function  remove_element_reponse($body, $contentType, $statusCode, $request)
    {

        $cookies_str = null;
        if ($cookies_str == null) {
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null && !empty($configAdmin->cookies_alldata_str)) {
                $cookies_str = $configAdmin->cookies_alldata_str;
            }
        }
        $cookieLast = CookiesUtils::cookieStrToArray($cookies_str);
        if (isset($cookieLast['JSESSIONID'])) {
            if (str_contains($body, $cookieLast['JSESSIONID'])) {
                echo "";
                die();
            }
        }

        $VERSION_CSS_JS = 2;
        $ContentType = $contentType;
        $actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];
        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            header("Content-Type:application/pdf");
        }
        if (str_contains($tailLink, '.js')) {
            $ContentType = "application/javascript";
        } else
        if (str_contains($tailLink, 'makes')) {
            if ($ContentType == null || $ContentType == "") {
                $ContentType = "application/octet-stream;charset=UTF-8";
            }
        }


        //Save vao db
        if ($request != null && $request->method() == "GET" && $statusCode == 200) {
            try {

                $referer = $request->headers->get('referer');
                $pathReferer = parse_url($referer, PHP_URL_PATH);

                if (
                    str_contains($tailLink, '.js') ||
                    str_contains($tailLink, '.css') ||
                    str_contains($tailLink, '.properties') ||
                    str_contains($tailLink, '.png')  ||
                    str_contains($tailLink, '.svg') ||
                    str_contains($tailLink, '.woff') ||
                    // $pathReferer == "/alldata/vehicle/"
                    str_contains($tailLink, "/alldata/vehicle/")
                ) {
                    if ($request != null && !str_contains($tailLink, "ogin") && $body != null && !str_contains($body, 'login')) {

                        $tailLink = Helper::removeParamsFromUrl($tailLink, ['_']);
                        //Thêm vào để xử lý 2 server speed và original
                        if (str_contains($referer, "alldata-eu")) {
                            if (strpos($tailLink, 'fr=eu') === false) {
                                if (strpos($tailLink, '?') !== false) {
                                    $tailLink .= '&fr=eu';
                                } else {
                                    $tailLink .= '?fr=eu';
                                }
                            }
                        }

                        $webClass = new WebClass();
                        $webClass->tail_link = $tailLink;
                        $webClass->service =  PlanUtils::ALLDATA;
                        $webClass->method = $request->method();
                        $webClass->content_type =  $ContentType;
                        $webClass->body =  $body;
                        $webClass->status_code =  $statusCode;

                        WebDataUtils::saveWebData($webClass);
                    }
                }
            } catch (Exception $e) {
            }
        }

        if ($ContentType) {

            $ref = request()->headers->get('referer');

            if (str_contains($ContentType, 'text/html') && str_contains($ref ?? "", 'alldata-eu.')) {

                $body =  str_replace("app.alldata.com", $pathNoHTTP, $body);
                $dom = HtmlDomParser::str_get_html($body);

                if ($dom  != null) {
                    $embed = $dom->find('embed', 0);
                    if ($embed  != null &&  $embed->attr != null && isset($embed->attr['src'])) {
                        if (str_contains($embed->attr['src'], '_svg')) {


                            $body = str_replace(
                                '800px',
                                '4500px;height: 3500px;',
                                $body
                            );

                            $body = str_replace(
                                'type="image/svg+xml"',
                                'type="image/svg+xml" width="100%" height="100%"
    
                        ',
                                $body
                            );



                            echo $body;
                            die();
                            $urlXXX = strtok($tailLink, '?');
                            $your_array = explode("/", $urlXXX);
                            $last =  $your_array[count($your_array) - 1];

                            $repPath =   str_replace($last, $embed->attr['src'], $actual_link);
                            header("location: $repPath");
                        }
                    }
                }
            } else
            if (str_contains($ContentType, 'text/html') && !str_contains($path, 'app-alldata.')) {  //Tách svg ra
                $body =  str_replace("app.alldata.com", $pathNoHTTP, $body);
                $dom = HtmlDomParser::str_get_html($body);

                if ($dom  != null) {
                    $embed = $dom->find('embed', 0);
                    if ($embed  != null &&  $embed->attr != null && isset($embed->attr['src'])) {
                        if (str_contains($embed->attr['src'], '_svg')) {


                            $body = str_replace(
                                'type="image/svg+xml"',
                                'type="image/svg+xml" width="100%" height="100%"
    
                            ',
                                $body
                            );



                            echo $body;
                            die();
                            $urlXXX = strtok($tailLink, '?');
                            $your_array = explode("/", $urlXXX);
                            $last =  $your_array[count($your_array) - 1];

                            $repPath =   str_replace($last, $embed->attr['src'], $actual_link);
                            header("location: $repPath");
                        }
                    }
                }

                if ($tailLink == "/alldata/vehicle") {
                    $body = HtmlUtils::addVersionToJsAndCsss($body, $VERSION_CSS_JS);
                }
            }

            //remove nut
            $dom = HtmlDomParser::str_get_html($body);

            if ($dom != null && $dom != false && str_contains($ContentType, 'text/html')) {
                //Info Center
                $InfoCenter = $dom->find('#liInfoCenter', 0);

                if ($InfoCenter != null) {
                    $InfoCenter->remove();
                }

                //Service Intervals	
                $liServiceIntervals = $dom->find('#liServiceIntervals', 0);

                if ($liServiceIntervals != null) {
                    $liServiceIntervals->remove();
                }
                // xóa phần tử userAcct feedback_bar language trialContainer
                $userAcct = $dom->find('#userAcct', 0);
                if ($userAcct != null) {
                    $userAcct->style = 'display:none;';
                }
                $feedback_bar = $dom->find('#feedback_bar', 0);
                if ($feedback_bar != null) {
                    $feedback_bar->remove();
                }
                $language = $dom->find('#language', 0);
                if ($language != null) {
                    $language->style = 'display:none;';
                }
                $trialContainer = $dom->find('#trialContainer', 0);
                if ($trialContainer != null) {
                    $trialContainer->style = 'display:none;';
                }

                $vehicleSelectorContainer = $dom->find('#vehicleSelectorContainer', 0);
                if ($vehicleSelectorContainer != null) {
                    $vehicleSelectorContainer->remove();
                }

                $body =  $dom->innertext;
            }

            if (str_contains($ContentType, 'text/html')) {


                $body =  str_replace('<a href="/alldata/">', '<a href="/">', $body);

                if (str_contains($body, "<html") && str_contains($body, "main-build-")) {
                    $body =  HtmlUtils::addTagHtmlOnHead('<script src="/jssav/' . Helper::generateRandomString(10) . '.js"></script>', $body);
                    $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/service/alldata_eu.js?v=4"></script>', $body);
                    $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                    <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag("js", new Date());

                    gtag("config", "G-KXWTNY3G2J");
                    </script>', $body);

                    if ($request->user != null) {

                        $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_alldata");
                        if ($remain_days > 0 && $remain_days < 10) {
                            $appName = DomainConfigHelper::getConfig('appName');
                            $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script>
                         
                             siiimpleToast.alert(" Alldata expiry date is only ' . $remain_days . ' days, remember to renew!", {
                              position: "top|center",
                              margin: 15,
                              delay: 0,
                              duration: 5000,
                            });
                            </script>', $body);
                        }
                    }
                }
            }

            if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) {
                $timestamp = time();
                $body = str_replace('.js', ".js?_={$timestamp}", $body);

                $body =  str_replace("+44(0)1213 681174", "+86189 8087 6959", $body);
                $body =  str_replace("+44 (0)330 0535 111", "+86189 8087 6959", $body);
                $body =  str_replace("app.alldata.com", $pathNoHTTP, $body);

                // if ($configAdmin  != null &&   $configAdmin->alldata_info != null && $request->user != null) {
                //     $first_name = $configAdmin->alldata_info->first_name ?? "";
                //     $last_name = $configAdmin->alldata_info->last_name ?? "";
                //     $username = $request->user->username;



                //     $index1 = strpos($body, '<span class="dp_label">');

                //     $bodyCut = substr($body, $index1);

                //     $index2 = strpos($bodyCut, '</span>');

                //     if ($index1  > 0 &&  $index2 > 0) {
                //         $body =  substr_replace($body, "<span class=\"dp_label\">$username</span>",    $index1, $index2);
                //     }
                // }


            }

            if (str_contains($tailLink, '.js')) {
                $body =  str_replace('$.i18n.prop("library.attachments.browse")', '"Choose File"', $body);
                $body =  str_replace('$.i18n.prop("navigation.information.title")', '"INFORMATION"', $body);
                $body =  str_replace('$.i18n.prop("vehicle.field.vehicle")', '"Vehicle"', $body);
                $body =  str_replace('$.i18n.prop(', '$.i18n?.prop(', $body);

                $ContentType = "application/javascript";
            }
        } else

        if (str_contains($tailLink, 'makes')) {
            $ContentType = "application/octet-stream;charset=UTF-8";
        }

        header('Content-type: ' .  $ContentType ?? "");
        echo   $body;
        die();
    }

    static public function checkServer($cookies_str = null)
    {
        try {
            $response  = AllDataUtilsWeb::getResponse('https://app.alldata.com/alldata/user/listUser/C/settings', "GET", $cookies_str, null);

            $type = $response->getHeader('Content-Type')[0] ?? "";

            if (str_contains($type, 'application/json')) {

                $info_account_list = json_decode($response->getBody()->getContents());
                $user_name =  $info_account_list[0]->userName;
                $user_id =  $info_account_list[0]->id;

                $last_name =  $info_account_list[0]->lastName;
                $first_name =  $info_account_list[0]->firstName;

                return [
                    'status' => true,
                    'mess' => "",
                    'user_id' => $user_id,
                    'user_name' => $user_name,
                    'last_name' => $last_name,
                    'first_name' => $first_name,
                ];
            }
        } catch (Exception $e) {


            LogUtils::error($e);

            return [
                'status' => false,
                'mess' => $e->getMessage()
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }

    static public function userDataReponse($email, $name, $address)
    {


        $jayParsedAry = [
            "id" => $email,
            "email" => $email,
            "firstName" => $name,
            "lastName" => "",
            "defaultLocale" => "fr_FR",
            "phoneNumber" => $email,
            "subscription" => [
                "account" => [
                    "id" => $email,
                    "primaryUserName" => $email,
                    "shop" => [
                        "shopName" => "MD Technologia Marcin Dobrzański",
                        "address" => [
                            "CITY_TOWN_LOCALITY" => "Łódź",
                            "STATE_PROVINCE" => "łódzkie",
                            "ADDRESS_1" => "Zawiszy Czarnego 22b",
                            "ALT_PHONE_NUMBER" => "",
                            "ADDRESS_2" => "",
                            "POSTAL_CODE" => "91-824",
                            "FAX_NUMBER" => "",
                            "PHONE_NUMBER" => $email
                        ],
                        "country" => [
                            "name" => "Poland",
                            "countryCode" => "PL"
                        ]
                    ],
                    "country" => null,
                    "type" => "C"
                ],
                "id" => $email,
                "startDate" => 1654157551000,
                "availableMakes" => [
                    [
                        "code" => "all",
                        "name" => "all"
                    ]
                ],
                "billType" => "B"
            ],
            "vehicleSelector" => "P",
            "wantsMarketingInfo" => false,
            "wantsProductUpdateInfo" => false,
            "trialUser" => false,
            "activeFeatures" => [
                [
                    "id" => "LT",
                    "name" => "Labour Times",
                    "active" => true
                ]
            ],
            "roles" => [
                [
                    "code" => "Gen3 Owner",
                    "name" => "Owner"
                ]
            ],
            "acceptedTerms" => true,
            "token" => "6c9cfe4d-6208-43ad-9dc5-98425dc51a88",
            "expired" => false,
            "remainingDays" => 0
        ];

        return   $jayParsedAry;
    }
}
