<?php

namespace App\Helper;

use App\Models\AdminSetting;

class PayPalDataUtils
{
    static function listLive()
    {
        $appName = DomainConfigHelper::getConfig('appName');

        $list_live = [];
        if ($appName == "DRCAR") {
            $list_live = [

                [
                    "name" => "Nguyễn <PERSON>u <PERSON>ng 10",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AbbhqqhuztfYCBS3d_veKVeG4BTwGIhGk5_-RWl9NvWarftOMN0zpe1OZ4jzCXnYFvSAHWZnOvr3A-hw'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EIopUGNYsvmCeObyo0dLGsRAl1su-S_4zT6HsXMZ6ggUvlToh9rZ_EfSapGkchNryCqF60h4k9fuDcJ-'),
                    'app_id'            => 'DuongNguyen',
                ],


                [
                    "name" => "Lê Thanh Hải 17",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AQyJP2To1J7HNktPDqFh7B6Cv--WIM2vaeLSukzfbGU6kt7_XLo31enKjgvkReG7OwI9hJvOA3-dEM3z'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EOHfCcHMEKqZv2uMBJmbcckonBRBIPtZw1tdwV-XF0IgPawX_2hnyvozr_dTiLHvWBqHgwkX4VXeh8b5'),
                    'app_id'            => 'ThanhHai',
                ],

                [
                    "name" => "Nguyễn Tiến Nam 23",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AZpZpZp0x0v_tRoVrqgfwsbfrU8iZ7bPGjGbwisJd5mjjrRXAfuN_Cg8BYInS5KeiP8OdbdKrYyJq7ga'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EMQrxOaSKbRlYleIhCaWXVAicDJoVATo06kLD5PlGuoTniUWGU66ojRKVh0cGYWk4oNjOkuFvEoqHjZU'),
                    'app_id'            => 'TienNam',
                ],


                [
                    "name" => "Nguyễn Lê Đức 41",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'ASUG1e1Veun4xk063dXhKQxNP0EA7ViV3S1DAXmWTlvD0ac4j3Xwo__F_hEc8nvVrhw1ofEO157DDR6u'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'ENDRi8iuUYCcBuGC9x91iLBrpNGo6VWOfbR-eS7RXVlzCZaENGe9pKG5Zo-wLpSOLgD4-aoFiV5Z4fi-'),
                    'app_id'            => 'LeDuc',
                ],
                [
                    "name" => "Vũ Thị Hà 42",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'ASx3P9YiXVdbH3djCNyUCbpWnI3S5T0c95bhQ94Xf77TN9fc2GZbMsQMcM2emPcsHpHxKytrGZuOeMW6'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EJNF9S_P_t6uQU1avEvRbscDKUd0vlEbobDDpLzRuxTG_Gh18YfZbzAkKLZFlidwmtl14JeiTrfGF9xP'),
                    'app_id'            => 'ThiHa',
                ],

                [
                    "name" => "Vũ Hoàng Nhật Nguyễn 44",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'ATz9A02teYHM9T60evqPaATtxc8a9HxCa2YnbhI-qlJYCBhtNXTgCTF7zk8tW_6b3LGGUmw09pXNZ77I'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EN5CeMRXAbci3PdH05oHPuz8kcKdZPULMalk41Z7A5gFwK7CfLKns8ifNj3zWXxNUdD4w4CrrQdkkWPy'),
                    'app_id'            => 'NhatNguyen',
                ],
                [
                    "name" => "NguyenAnhTuan",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AUsuFu7DQ0O97KMnSXciAbIBPTl0GgLvCEtCygZvE6QfBOi6XbN1UZntW-Sl5gxKaqbDphXX6CVphWG3'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EBDBBabENbAK7YUUVWzgxJLfBhkHLnS6DGAgdIQP022OXqzCpiBLeSXYZrB3h9DZpw9AorNC4IG3qUES'),
                    'app_id'            => 'AnhTuan',
                ],


            ];
        }
        if ($appName == "FHOST") {
            $list_live = [

                [
                    "name" => "Nguyễn Triệu Dương 10",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AbbhqqhuztfYCBS3d_veKVeG4BTwGIhGk5_-RWl9NvWarftOMN0zpe1OZ4jzCXnYFvSAHWZnOvr3A-hw'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EIopUGNYsvmCeObyo0dLGsRAl1su-S_4zT6HsXMZ6ggUvlToh9rZ_EfSapGkchNryCqF60h4k9fuDcJ-'),
                    'app_id'            => 'DuongNguyen',
                ],


                [
                    "name" => "Lê Thanh Hải 17",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AQyJP2To1J7HNktPDqFh7B6Cv--WIM2vaeLSukzfbGU6kt7_XLo31enKjgvkReG7OwI9hJvOA3-dEM3z'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EOHfCcHMEKqZv2uMBJmbcckonBRBIPtZw1tdwV-XF0IgPawX_2hnyvozr_dTiLHvWBqHgwkX4VXeh8b5'),
                    'app_id'            => 'ThanhHai',
                ],

                [
                    "name" => "Nguyễn Tiến Nam 23",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AZpZpZp0x0v_tRoVrqgfwsbfrU8iZ7bPGjGbwisJd5mjjrRXAfuN_Cg8BYInS5KeiP8OdbdKrYyJq7ga'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EMQrxOaSKbRlYleIhCaWXVAicDJoVATo06kLD5PlGuoTniUWGU66ojRKVh0cGYWk4oNjOkuFvEoqHjZU'),
                    'app_id'            => 'TienNam',
                ],


                [
                    "name" => "Nguyễn Lê Đức 41",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'ASUG1e1Veun4xk063dXhKQxNP0EA7ViV3S1DAXmWTlvD0ac4j3Xwo__F_hEc8nvVrhw1ofEO157DDR6u'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'ENDRi8iuUYCcBuGC9x91iLBrpNGo6VWOfbR-eS7RXVlzCZaENGe9pKG5Zo-wLpSOLgD4-aoFiV5Z4fi-'),
                    'app_id'            => 'LeDuc',
                ],
                [
                    "name" => "Vũ Thị Hà 42",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'ASx3P9YiXVdbH3djCNyUCbpWnI3S5T0c95bhQ94Xf77TN9fc2GZbMsQMcM2emPcsHpHxKytrGZuOeMW6'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EJNF9S_P_t6uQU1avEvRbscDKUd0vlEbobDDpLzRuxTG_Gh18YfZbzAkKLZFlidwmtl14JeiTrfGF9xP'),
                    'app_id'            => 'ThiHa',
                ],

                [
                    "name" => "Vũ Hoàng Nhật Nguyễn 44",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'ATz9A02teYHM9T60evqPaATtxc8a9HxCa2YnbhI-qlJYCBhtNXTgCTF7zk8tW_6b3LGGUmw09pXNZ77I'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EN5CeMRXAbci3PdH05oHPuz8kcKdZPULMalk41Z7A5gFwK7CfLKns8ifNj3zWXxNUdD4w4CrrQdkkWPy'),
                    'app_id'            => 'NhatNguyen',
                ],
                [
                    "name" => "NguyenAnhTuan",
                    'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AUsuFu7DQ0O97KMnSXciAbIBPTl0GgLvCEtCygZvE6QfBOi6XbN1UZntW-Sl5gxKaqbDphXX6CVphWG3'),
                    'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EBDBBabENbAK7YUUVWzgxJLfBhkHLnS6DGAgdIQP022OXqzCpiBLeSXYZrB3h9DZpw9AorNC4IG3qUES'),
                    'app_id'            => 'AnhTuan',
                ],


            ];
        }

        return $list_live;
        
    }
    static function getCredentials()
    {
        $setting =      AdminSetting::first();
        $app_id_paypal =  $setting  == null ? null :  $setting->app_id_paypal;

        $live = null;

        $list_live = PayPalDataUtils::listLive();
        foreach ($list_live as $l) {
            if ($l['app_id'] == $app_id_paypal) {
                $live = $l;
                break;
            }
        }

        if ($live  == null) {
            $live = $list_live[array_rand($list_live)];
        }


        return [
            'mode'    => env('PAYPAL_MODE', 'live'), // Can only be 'sandbox' Or 'live'. If empty or invalid, 'live' will be used.
            'sandbox' => [
                'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AdeukURjlNIf_4fzmMoODyx3u5fZpXwS07IgZ_psm0FccgbfGJYKfCdWkE1QD10jwqgdzEKu20mb22-N'),
                'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EI9WUXqOjNaV1JOcki7o77qzsa7Xp2llitFox88UbKHb0zMX2vsoa5rSRUHCB46d_yIJdu_JLXhOp7oL'),
                'app_id'            => 'Default Application',
            ],
            'live' => $live,
            'payment_action' => env('PAYPAL_PAYMENT_ACTION', 'Sale'), // Can only be 'Sale', 'Authorization' or 'Order'
            'currency'       => env('PAYPAL_CURRENCY', 'USD'),
            'notify_url'     => env('PAYPAL_NOTIFY_URL', ''), // Change this accordingly for your application.
            'locale'         => env('PAYPAL_LOCALE', 'en_US'), // force gateway language  i.e. it_IT, es_ES, en_US ... (for express checkout only)
            'validate_ssl'   => env('PAYPAL_VALIDATE_SSL', true), // Validate SSL when creating api client.
        ];
    }

    static function getDomainOnly($host)
    {
        $host = strtolower(trim($host));
        $host = ltrim(str_replace("http://", "", str_replace("https://", "", $host)), "www.");
        $count = substr_count($host, '.');
        if ($count === 2) {
            if (strlen(explode('.', $host)[1]) > 3) $host = explode('.', $host, 2)[1];
        } else if ($count > 2) {
            $host = PayPalDataUtils::getDomainOnly(explode('.', $host, 2)[1]);
        }
        $host = explode('/', $host);
        return $host[0];
    }

    static function getSuccessPath($appCredentials, $custom_path = "payment_drcar")
    {
        $ppData = ($appCredentials);
        $appId = $ppData['live']['app_id'];
        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $appId . "-payment." . PayPalDataUtils::getDomainOnly($_SERVER['HTTP_HOST']);
        $path =  $path . "/$custom_path/paypal-success";
        return $path;
    }

    static function getCreatePath($appCredentials, $custom_path = "payment_drcar")
    {
        $ppData = ($appCredentials);
        $appId = $ppData['live']['app_id'];
        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $appId . "-payment." . PayPalDataUtils::getDomainOnly($_SERVER['HTTP_HOST']);
        $path =  $path . "/$custom_path/paypal-create";
        return $path;
    }

    static function getCancelPath($appCredentials, $custom_path = "payment_drcar")
    {
        $ppData = ($appCredentials);
        $appId = $ppData['live']['app_id'];
        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $appId . "-payment." . PayPalDataUtils::getDomainOnly($_SERVER['HTTP_HOST']);
        $path =  $path . "/$custom_path/paypal-cancel";
        return $path;
    }
}
