<?php

namespace App\Helper;


use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\JsonResponse;

class ToyotaUtilsWeb
{

    static public function  remove_element_reponse($body, $contentType, $statusCode = null,  $request)
    {

        $ContentType = $contentType;

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];

        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            header("Content-Type:application/pdf");
        }

        if (strlen($tailLink) > 3) {
            if (substr_compare($tailLink, '.svg', -4) === 0) {
                $ContentType  = 'image/svg+xml';
            }

            if (substr_compare($tailLink, '.png', -4) === 0) {
                $ContentType  = 'image/png';
            }
        }

        //Save vao db
        if ($request != null && $statusCode == 200) {

            try {
                if (
                    $request != null
                    && $body != null && $body != ""
                    && !str_contains($body, "<title>Techinfo prelogin desktop</title>")

                ) {
                    parse_str(request()->getContent(), $outputArray);
                    $tailLinkSave = Helper::removeParamsFromUrlToyota($tailLink);

                    $webClass = new WebClass();
                    $webClass->tail_link = $tailLinkSave;
                    $webClass->service =  PlanUtils::TOYOTA_TIS;
                    $webClass->method = $request->method();
                    // dd($webClass->method); 
                    $webClass->content_type =  $contentType;
                    $webClass->body =  (string)$body;
                    $webClass->bodyRequest =   Helper::removeFieldFromBodyToyota($outputArray);
                    $rt = WebDataUtils::saveWebData($webClass);

                    if ($rt instanceof JsonResponse) {
                        return response()->json($rt->getData(), 500);
                    }
                }
            } catch (Exception $e) {
                http_response_code(500);
                echo "Error saving file";
                exit;
            }
        }


        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);
        if (str_contains($contentType, 'text/html') || str_contains($contentType, 'json')) {
            $body =  str_replace("techinfo.toyota.com", $pathNoHTTP, $body);
        }


        header('Content-type: ' .  $ContentType ?? "");

        echo   $body;
        die();
    }
    /**
     * Thông tin server
     */
    static public function  getResponse($link, $method = "GET", $body = [], Request $request = null)
    {

        $tailLink = $_SERVER["REQUEST_URI"];


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }

        $cookies_str = null;
        $proxy = null;

        $configAdmin =      ConfigAdmin::first();

        if ($proxy === null) {
            if ($configAdmin  != null && !empty($configAdmin->proxy_toyota_tis)) {
                $proxy = $configAdmin->proxy_toyota_tis;
            }
        }

        if ($cookies_str == null) {
            $cookies_str = Cache::get(json_encode(['cookies_toyota_tis_str']));

            if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_toyota_tis_str)) {
                $cookies_str = $configAdmin->cookies_toyota_tis_str;
            }
        }

        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $arr = CookiesUtils::cookieStrToArray($cookies_str);

        unset($headers_requests['user-agent']);

        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);

        unset($headers_requests['upgrade-insecure-requests']);

        $headerArr  =  $headers_requests;;

        $headerArr['Cookie'] = $cookies_str;
        $headerArr['User-Agent'] = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36";
        $headerArr['Accept'] =   $headers_requests['Accept'] ?? "text/html, */*; q=0.01";
        $headerArr['Accept-Language'] =   $headers_requests['Accept-Language'] ?? "en-US,en;q=0.05";
        $headerArr['Accept-Encoding'] =   $headers_requests['Accept-Encoding'] ?? "gzip, deflate, br";
        $headerArr['Origin'] =   $headers_requests['Origin'] ?? "https://techinfo.toyota.com";
        $headerArr['Connection'] =   $headers_requests['Connection'] ?? "keep-alive";
        $headerArr['Referer'] =   $headers_requests['Referer'] ?? "https://techinfo.toyota.com/";

        $headerArr['Sec-Fetch-Dest'] =   $headers_requests['Sec-Fetch-Dest'] ?? "empty";
        $headerArr['Sec-Fetch-Mode'] =   $headers_requests['Sec-Fetch-Mode'] ?? "cors";
        $headerArr['Sec-Fetch-Site'] =   $headers_requests['Sec-Fetch-Site'] ?? "cross-site";
        $headerArr['Sec-GPC'] =   $headers_requests['Sec-GPC'] ?? "1";

        $jar = new \GuzzleHttp\Cookie\CookieJar();

        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {

                $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name'     => $key,
                    'Value'    =>  $value,
                    'Domain'   => '.toyota.com',
                    'Path'     => '/',
                    // 'Max-Age'  => $item['name'],
                    //'Expires'  => $item['name'],
                    // 'Secure'   => $item['name'],
                    // 'Discard'  => $item['name'],
                    //  'HttpOnly' => $item['name'],
                ]));
            }
        }


        $client = new \GuzzleHttp\Client(
            [
                'headers' => $headerArr,
                'proxy' => $proxy,
                'verify' => false,
            ]
        );


        $response1 = $client->request(
            $method,
            $link,
            [
                'cookies' => $jar,
                'headers' => $headerArr,
                'form_params' => $body,
                'timeout' => 90, // Response timeout
                'connect_timeout' => 90, // Connection timeout,
                // 'on_stats' => (str_contains($tailLink, 'check_') || str_contains($tailLink, 'test_api')) ? null : function (\GuzzleHttp\TransferStats $stats) {

                //     $url = $stats->getHandlerStats()['redirect_url'];

                //     $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                //     $pathNoHTTP = str_replace("http://", "", $path);
                //     $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

                //     $url =  str_replace("https://techinfo.toyota.com", $path, $url);
                //     $url =  str_replace("http://techinfo.toyota.com", $path, $url);
                //     $url =  str_replace("techinfo.toyota.com", $pathNoHTTP, $url);


                //     $tailLink = $_SERVER["REQUEST_URI"];

                //     $tailLink =  rtrim($tailLink, "/");
                //     $url =  rtrim($url, "/");


                //     if ($url != null && $url != "" && $url != $tailLink) {

                //         $firtUrl = substr($url, 0, 3);
                //         if ($firtUrl == "www") {
                //             $url =  str_replace("www.", "https://www.",  $url);
                //         }

                //         header("Location: $url");
                //         exit;
                //     }
                // }
            ]

        );


        return  $response1;
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        $content = "";
        try {
            $time_start = microtime(true);

            $response  = ToyotaUtilsWeb::getResponse('https://global.service3s.toyota.co.jp/PrivacyPolicy/Index?LocationId=6', "GET", $cookies_str, null);
            $type = $response->getHeader('Content-Type')[0] ?? "";

            $content = ($response->getBody()->getContents());
            if (str_contains($type, 'text/html')) {

                $indexJsonProfile1 = strpos($content, 'window.inlineManualTracking = {');

                $subContentProfile = substr($content,    $indexJsonProfile1);
                $indexJsonProfile2 = strpos($subContentProfile, '};');

                $jsonProfile =  substr($subContentProfile,  30, $indexJsonProfile2 - 29);
                $jsonProfile = trim(preg_replace('/\s\s+/', ' ', $jsonProfile));

                $jsonProfile  = preg_replace('/([\w\d]+): /', '"$1": ', $jsonProfile);
                $jsonProfile  = str_replace("'", '"', $jsonProfile);

                $array = json_decode($jsonProfile, true);

                $time_end = microtime(true);
                $dataFinal = [
                    'time_handle' => ($time_end - $time_start),
                    'status' => true,
                    'mess' => "",
                ];
                if (str_contains($content, "Your Session has been Terminated")) {
                    return [
                        'status' => false,
                        'mess' => 'ERROR_COOKIES Your Session has been Terminated',
                        'content' =>  $content
                    ];
                }
                $dataFinal  = array_merge($dataFinal, $array);
                return $dataFinal;
            }
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage() . " khong xac dinh"
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES",
            'content' =>  $content
        ];
    }
}
