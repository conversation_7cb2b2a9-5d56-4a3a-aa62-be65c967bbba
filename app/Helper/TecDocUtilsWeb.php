<?php

namespace App\Helper;

use App\Models\ConfigAdmin;
use App\Models\MsgCode;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use App\Helper\HtmlUtils;
use App\Jobs\PushNotificationAdminJob;
use App\Models\HistoryStatusServer3;

class TecDocUtilsWeb
{

    /**
     * Lấy dữ liệu
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null)
    {

        $tailLink = $_SERVER["REQUEST_URI"];


        if (str_contains($tailLink, '/login')) {
            $c =      ConfigAdmin::first();


            $lastTecDoc =  HistoryStatusServer3::where('service', PlanUtils::TECDOC)->orderBy('id', 'desc')->first();
            if ($lastTecDoc  == null ||   $lastTecDoc->status !=  MsgCode::STATUS_ERR) {
                $c->update([
                    'cookies_tecdoc_json_info' => json_encode(["status" => false, 'begin_time_err' => Helper::getTimeNowStringVietNam()])
                ]);

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::TECDOC,
                    'status' => MsgCode::STATUS_ERR,
                ]);

                //Tìm admin

                $title =  "TecDoc";
                $content =  "TecDoc Stopped";
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
            }

            return ViewUtils::viewErrorPage([
                'msg_code' => MsgCode::TECDOC_OF_MAINTENANCE[0],
                'msg' => MsgCode::TECDOC_OF_MAINTENANCE[1],
            ]);
            exit;
        }


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        if ($cookies_str == null) {
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null && !empty($configAdmin->cookies_tecdoc_str)) {
                $cookies_str = $configAdmin->cookies_tecdoc_str;
            }
        }

        $arr = CookiesUtils::cookieStrToArray($cookies_str);

        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);

        $headerArr  =  $headers_requests;
        $headerArr['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36';
        $headerArr['sec-ch-ua'] = '"Chromium";v="106", "Google Chrome";v="106", "Not;A=Brand";v="99"';
        $headerArr['sec-ch-ua-mobile'] = '?0';
        $headerArr['sec-ch-ua-platform'] = '"Windows"';


        $jar = new \GuzzleHttp\Cookie\CookieJar();

        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {

                $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name'     => $key,
                    'Value'    =>  $value,
                    'Domain'   => 'web.tecalliance.net',
                    'Path'     => '/',
                    // 'Max-Age'  => $item['name'],
                    //'Expires'  => $item['name'],
                    // 'Secure'   => $item['name'],
                    // 'Discard'  => $item['name'],
                    //  'HttpOnly' => $item['name'],
                ]));
            }
        }




        $client = new \GuzzleHttp\Client(
            [
                'headers' => $headerArr,
                'cookies' => $jar,
            ]
        );

        $response1 = $client->request(
            $method,
            $link,
            [
                'cookies' => $jar,
                'headers' => $headerArr,
                'form_params' => $body,
                'timeout' => 15, // Response timeout
                'connect_timeout' => 15, // Connection timeout,
                'on_stats' => function (\GuzzleHttp\TransferStats $stats) {

                    $url = $stats->getHandlerStats()['redirect_url'];
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $pathNoHTTP = str_replace("http://", "", $path);
                    $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

                    $url =  str_replace("https://web.tecalliance.net", $path, $url);
                    $url =  str_replace("http://web.tecalliance.net", $path, $url);
                    $url =  str_replace("web.tecalliance.net", $pathNoHTTP, $url);

                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);


                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink) {

                        $firtUrl = substr($url, 0, 3);
                        if ($firtUrl == "www") {
                            $url =  str_replace("www.", "https://www.",  $url);
                        }

                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );


        return  $response1;
    }

    static public function  remove_element_reponse($response1, $request)
    {
        $actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];
        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            header("Content-Type:application/pdf");
        }

        $body = $response1->getBody();

        $ContentType = $response1->getHeader('Content-Type')[0] ?? "";

        if ($response1->getHeader('Content-Type') != null) {

            if (str_contains($ContentType, 'text/html')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                    <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag("js", new Date());
                
                    gtag("config", "G-KXWTNY3G2J");
                    </script>', $body);
            }
            
            if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')  || str_contains($ContentType, 'text/javascript')) {
                $body =  str_replace("https://web.tecalliance.net", $path, $body);
                $body =  str_replace("http://web.tecalliance.net", $path, $body);
                $body =  str_replace("web.tecalliance.net", $pathNoHTTP, $body);
                $body =  str_replace("https://www.tecalliance.net", $path, $body);


                $body =  str_replace("http://", "https://", $body);

                $cookies = $_COOKIE;
                $platform = $cookies['platform'] ?? request()->header('platform') ?? null;

                if (str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);
                    $body =  $dom->innertext;

                    $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/service/tecdoc.js?v=6"></script>', $body);
                    if ($tailLink = "/") {
                       
                        $cookies_str = null;
                        if ($cookies_str == null) {
                            $configAdmin =      ConfigAdmin::first();

                            if ($configAdmin  != null && !empty($configAdmin->cookies_tecdoc_str)) {
                                $cookies_str = $configAdmin->cookies_tecdoc_str;
                            }
                        }
                        $arr = CookiesUtils::cookieStrToArray($cookies_str);
                        $jar = new \GuzzleHttp\Cookie\CookieJar();

                        $str_javascript = "";
                        if ($arr  != null && is_array($arr)) {
                            foreach ($arr as $key => $value) {
                                $str_javascript =      $str_javascript .  "window.localStorage.setItem('$key', '$value');
                                ";
                            }
                            $body =  HtmlUtils::addTagHtmlOnHead("<script>
                        $str_javascript
                       </script>", $body);
                        }
                    }


                    if ($request->user != null) {

                        $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_tecdoc");
                        if ($remain_days > 0 && $remain_days < 10) {
                            $appName = DomainConfigHelper::getConfig('appName');
                            $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script>
                             
                                 siiimpleToast.alert("('.$appName.') TecDoc expiry date is only ' . $remain_days . ' days, remember to renew!", {
                                  position: "top|center",
                                  margin: 15,
                                  delay: 0,
                                  duration: 5000,
                                });
                                </script>', $body);
                        }
                    }
                }
            }



            header('Content-type: ' .  $ContentType ?? "");

            echo $body;
            die();
        }

        echo   $body;
    }

    static public function checkServer($cookies_str = null)
    {
        $apiKey = "";
        $time_start = microtime(true);
        try {
            $curl = curl_init();


            if ($cookies_str == null) {
                $configAdmin =      ConfigAdmin::first();

                if ($configAdmin  != null && !empty($configAdmin->cookies_tecdoc_str)) {
                    $cookies_str = $configAdmin->cookies_tecdoc_str;
                }
            }

            $arr = CookiesUtils::cookieStrToArray($cookies_str);

            foreach ($arr as $key => $value) {
                if ($key == '-1618829110-AMXZCIPHLJOKVFBUQTYRNDGSWE') {
                    $apiKey = $value;
                }
            }


            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://webservice.tecalliance.services/webcat30/v1/services/WebCat30WS.jsonEndpoint',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{"getCurrentAPIKeyInfo":{}}',
                CURLOPT_HTTPHEADER => array(
                    'authority: webservice.tecalliance.services',
                    'accept: application/json, text/plain, */*',
                    'accept-language: vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5,zh-CN;q=0.4,zh;q=0.3,ca;q=0.2,ko;q=0.1',
                    'content-type: application/json; charset=UTF-8',
                    'dnt: 1',
                    'origin: https://web.tecalliance.net',
                    'referer: https://web.tecalliance.net/',
                    'sec-ch-ua: "Google Chrome";v="111", "Not(A:Brand";v="8", "Chromium";v="111"',
                    'sec-ch-ua-mobile: ?0',
                    'sec-ch-ua-platform: "macOS"',
                    'sec-fetch-dest: empty',
                    'sec-fetch-mode: cors',
                    'sec-fetch-site: cross-site',
                    'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36',
                    'x-api-key: ' . $apiKey,
                ),
            ));

            $response = curl_exec($curl);
            curl_close($curl);

            $json = json_decode(  $response );
  
            if( $json  != null &&  $json->status != null && $json->status != 200) {
                return [
                    'status' => false,
                    'mess' =>  $json->statusText
                ];
            }

            $time_end = microtime(true);

            return [
                'time_handle' => ($time_end - $time_start),
                'status' => true,
                'mess' => $response,
            ];
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage()
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
