<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use App\Helper\HtmlUtils;


class IdentifixUtilsWeb
{

    /**
     * Lấy dữ liệu
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $proxy = null)
    {

        $body = [];

        if ($request != null) {
            $body = array_diff_key($request->all(), $request->query());
            $body =  Helper::removeBodyExcessive($body);
        }


        $tailLink = $_SERVER["REQUEST_URI"];

        if (
            str_contains($tailLink, 'CreditCardUpdate') ||
            str_contains($tailLink, 'LogOff') ||
            str_contains($tailLink, 'Referral') ||
            str_contains($tailLink, 'ContactUs') ||
            str_contains($tailLink, 'VehicleSummary/Index') ||
            str_contains($tailLink, 'FactoryMaintenanceConfiguration/Index') ||
            str_contains($tailLink, 'ChangePassword/Index') ||
            str_contains($tailLink, 'ShopInfo/Index') ||
            str_contains($tailLink, 'HotlineCallRequest/Index') ||
            str_contains($tailLink, 'PostFix/Index') ||
            str_contains($tailLink, 'TermsOfUse/Index')

        ) {
            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();

            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }


        if (
            $tailLink == "/"
        ) {
            $domainMain = Helper::getDomainCurrent();
            header("Location: " . $domainMain . "/CreateVehicle/Index");
            exit;
        }



        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        $configAdmin =      ConfigAdmin::first();
        if ($proxy === null) {

            //    $proxy  = ProxyUtils::chooseProxyForAutodata();
            if ($configAdmin  != null && !empty($configAdmin->proxy_identifix)) {
                $proxy = $configAdmin->proxy_identifix;
            }
        }

        if ($cookies_str == null) {


            if ($configAdmin  != null && !empty($configAdmin->cookies_identifix_str)) {
                $cookies_str = $configAdmin->cookies_identifix_str;
            }
        }

        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $arr = CookiesUtils::cookieStrToArray($cookies_str);

        unset($headers_requests['user-agent']);

        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);


        unset($headers_requests['upgrade-insecure-requests']);

        $headerArr  =  $headers_requests;;

        $headerArr['Cookie'] = $cookies_str;
        $headerArr['User-Agent'] = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";


        $jar = new \GuzzleHttp\Cookie\CookieJar();

        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {

                $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name'     => $key,
                    'Value'    =>  $value,
                    'Domain'   => '.identifix.com',
                    'Path'     => '/',
                    // 'Max-Age'  => $item['name'],
                    //'Expires'  => $item['name'],
                    // 'Secure'   => $item['name'],
                    // 'Discard'  => $item['name'],
                    //  'HttpOnly' => $item['name'],
                ]));
            }
        }

        $client = new \GuzzleHttp\Client(
            [
                'headers' => $headerArr,
                'proxy' => $proxy,
            ]
        );

        $response1 = $client->request(
            $method,
            $link,
            [
                // 'cookies' => $jar,
                'headers' => $headerArr,
                'form_params' => $body,
                'timeout' => 90, // Response timeout
                'connect_timeout' => 90, // Connection timeout,
                'on_stats' => (str_contains($tailLink, 'check_') || str_contains($tailLink, 'test_api')) ? null : function (\GuzzleHttp\TransferStats $stats) {

                    $url = $stats->getHandlerStats()['redirect_url'];
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $pathNoHTTP = str_replace("http://", "", $path);
                    $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

                    $url =  str_replace("https://dh.identifix.com", $path, $url);
                    $url =  str_replace("http://dh.identifix.com", $path, $url);
                    $url =  str_replace("dh.identifix.com", $pathNoHTTP, $url);

                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);


                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink) {

                        $firtUrl = substr($url, 0, 3);
                        if ($firtUrl == "www") {
                            $url =  str_replace("www.", "https://www.",  $url);
                        }

                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );


        return  $response1;
    }

    static public function  remove_element_reponse($body, $contentType, $statusCode, $request)
    {
        $ContentType = $contentType;

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];


        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            header("Content-Type:application/pdf");
        }

        if (strlen($tailLink) > 3) {
            if (substr_compare($tailLink, '.svg', -4) === 0) {
                $ContentType  = 'image/svg+xml';
            }

            if (substr_compare($tailLink, '.png', -4) === 0) {
                $ContentType  = 'image/png';
            }
        }

        //Save vao db
        if ($request != null && $request->method() == "GET" && $statusCode == 200) {

            $tailLink2 = Helper::removeParamsFromUrl($tailLink, ['CacheId', '_', 'LastRefresh']);
            try {
                if (
                    $request != null &&
                    !str_contains($tailLink, "ogin") &&
                    !str_contains($tailLink, "log") &&
                    !str_contains($tailLink, "User") &&
                    !str_contains($tailLink, "CacheId")  &&
                    !str_contains($tailLink, "log")  &&
                    !str_contains($tailLink, "RenderNewRecentVehiclePartial")    &&
                    $body != null && $body != "" &&
                    !str_contains($body, 'error-panel')
                ) {
                    $tailLink = Helper::removeParamsFromUrl($tailLink, ['ROID']);
                    $webClass = new WebClass();
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::IDENTIFIX;
                    $webClass->method = $request->method();
                    $webClass->content_type =  $contentType;
                    $webClass->body =  $body;
                    $webClass->status_code =  $statusCode;

                    WebDataUtils::saveWebData($webClass);
                }
            } catch (Exception $e) {
            }
        }

        if ($ContentType) {

            if (str_contains($ContentType, 'text/html')  && !$request->isMethod('post')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                    <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag("js", new Date());
                
                    gtag("config", "G-KXWTNY3G2J");
                    </script>', $body);

                $body =  HtmlUtils::addTagHtmlOnHead('<script src="/jssav/' . Helper::generateRandomString(10) . '.js"></script>', $body);
            }

            if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) {
                // $body =  str_replace("https://dh.identifix.com", $path, $body);
                // $body =  str_replace("http://dh.identifix.com", $path, $body);
                // $body =  str_replace("dh.identifix.com", $pathNoHTTP, $body);

                // $body =  str_replace("http://", "https://", $body);

                $body =  str_replace("bam.nr-data.net", "bam.nr-data3.net", $body);

                // $configAdmin =      ConfigAdmin::first();
                // if ($configAdmin  != null &&   $configAdmin->identifix_info != null && $request->user != null) {
                //     $uid = $configAdmin->identifix_info->uid ?? "";
                //     $name = $configAdmin->identifix_info->name ?? "";
                //     $username = $configAdmin->identifix_info->username ?? "";
                //     $body =  str_replace($uid, "123456", $body);
                // }

                $cookies = $_COOKIE;
                $platform = $cookies['platform'] ?? request()->header('platform') ?? null;

                if ($request->isMethod('get') && str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);

                    if ($dom  != null) {

                        //Logout button
                        $last = $dom->find('.select-recent-car', 0);

                        if ($last != null) {
                            $last->remove();
                        }

                        //my shop
                        $myshop1 = $dom->find('#main-menu .menu-item', 6);

                        if ($myshop1 != null) {
                            $myshop1->remove();
                        }

                        $myshop2 = $dom->find('#main-menu .menu-item', 5);

                        if ($myshop2 != null) {
                            $myshop2->remove();
                        }


                        $footer = $dom->find('#footer', 0);

                        if ($footer != null) {
                            $footer->remove();
                        }



                        $cantfind = $dom->find('#lnkCantFindOemLink', 0);

                        if ($cantfind != null) {
                            $cantfind->remove();
                        }

                        //replace html
                        $body2 =  $dom->innertext;

                        //Logout button
                        $logo = $dom->find('.branch-logo', 0);

                        if ($logo != null) {
                            $body2 = str_replace(
                                $logo->outertext,
                                '<a href="/CreateVehicle/Index"><img alt="Direct-Hit -Find and Fix Faster" height="40px" width="376px" src="/Themes/DIRECTHIT5/Content/Images/dh-solera-logo-cvp.png"></a>',
                                $body2
                            );
                        }

                        $user_info1 = $dom->find('.user-shop-info', 0);
                        if ($user_info1 != null) {
                            $body2 = str_replace(
                                $user_info1->outertext,
                                '<td class="user-shop-info">' . ($request->user->name ?? "") . '</td>',
                                $body2
                            );
                        }
                        $user_info2 = $dom->find('.user_shop_display', 0);
                        if ($user_info2 != null) {
                            $body2 = str_replace(
                                $user_info2->outertext,
                                '<td class="user_shop_display">' . ($request->user->name ?? "") . '</td>',
                                $body2
                            );
                        }


                        $body2 = str_replace(
                            'https://slc.api.solera.com/smr/srv/hit/document-search-mfe-prd/main.js',
                            "/smr/srv/hit/document-search-mfe-prd/main.js",
                            $body2
                        );

                        $body =  ($body2);
                    }


                    //Khong phai iframe thi them vao body
                    if (isset($_SERVER['HTTP_SEC_FETCH_DEST']) && !str_contains($tailLink, 'FileManager') && $_SERVER['HTTP_SEC_FETCH_DEST'] != 'iframe'  && !$request->isMethod('post')) {
                        $body =  HtmlUtils::addTagHtmlOnBody(' <div id="google_translate_element" style="z-index: 410;position: inherit;"></div>
                        <script> 
                        function googleTranslateElementInit() {
                          new google.translate.TranslateElement({
                            pageLanguage: "en"
                          }, "google_translate_element");
                        }
                        </script>
                        <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script> ', $body);
                    }

                    if ($request->user != null) {

                        $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_identifix");
                        if ($remain_days > 0 && $remain_days < 10  && !$request->isMethod('post')) {
                            $appName = DomainConfigHelper::getConfig('appName');
                            $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script>
                             
                                 siiimpleToast.alert(" Identifix expiry date is only ' . $remain_days . ' days, remember to renew!", {
                                  position: "top|center",
                                  margin: 15,
                                  delay: 0,
                                  duration: 5000,
                                });
                                </script>', $body);
                        }
                    }
                }


                if ($request->isMethod('get') && $platform != 'web' && $platform != null && str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);
                    if ($dom  != null) {

                        //Remove last web pc
                        $top = $dom->find('header', 0);

                        if ($top != null) {
                            $top->remove();
                        }
                        $footer = $dom->find('footer', 0);

                        if ($footer != null) {
                            $footer->remove();
                        }
                    }

                    $body =  $dom->innertext;
                }
            }
        }
        header('Content-type: ' .  $ContentType ?? "");
        echo   $body;
        die();
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        $content = "";
        try {
            $time_start = microtime(true);

            $response  = IdentifixUtilsWeb::getResponse('https://dh.identifix.com/PrivacyPolicy/Index?LocationId=6', "GET", $cookies_str, null);
            $type = $response->getHeader('Content-Type')[0] ?? "";

            $content = ($response->getBody()->getContents());
            if (str_contains($type, 'text/html')) {

                $indexJsonProfile1 = strpos($content, 'window.inlineManualTracking = {');

                $subContentProfile = substr($content,    $indexJsonProfile1);
                $indexJsonProfile2 = strpos($subContentProfile, '};');

                $jsonProfile =  substr($subContentProfile,  30, $indexJsonProfile2 - 29);
                $jsonProfile = trim(preg_replace('/\s\s+/', ' ', $jsonProfile));

                $jsonProfile  = preg_replace('/([\w\d]+): /', '"$1": ', $jsonProfile);
                $jsonProfile  = str_replace("'", '"', $jsonProfile);

                $array = json_decode($jsonProfile, true);

                $time_end = microtime(true);
                $dataFinal = [
                    'time_handle' => ($time_end - $time_start),
                    'status' => true,
                    'mess' => "",
                ];
                if (str_contains($content, "Your Session has been Terminated") || $array == null) {
                    return [
                        'status' => false,
                        'mess' => 'ERROR_COOKIES Your Session has been Terminated',
                        'content' =>  $content
                    ];
                }
                $dataFinal  = array_merge($dataFinal, $array);
                return $dataFinal;
            }
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage() . " khong xac dinh"
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES",
            'content' =>  $content
        ];
    }
}
