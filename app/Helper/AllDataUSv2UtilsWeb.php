<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use Exception;
use Illuminate\Http\Request;
use App\Models\WebData;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class AllDataUSv2UtilsWeb
{

    const cookie =  'll=en_GB; JSESSIONID=263E7E35D5638F223ECAFFD4338C543E-n1; _ga=GA1.2.951375793.1661419528; __utmz=69693760.1662560141.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none); _gid=GA1.2.1493157109.1662442689; __utma=69693760.951375793.1661419528.1662560141.1662628670.2; ak_bmsc=5AE131AD99B154727CAF5A4A6FA768F1~000000000000000000000000000000~YAAQ26zbFxVNz/WCAQAA6Pj/IRGVfZKYjNxmldyw0d7KdLDfZeA1IJmF1GWMuc8nfMgPMVNuINctUqF0v1x5LTxMxmAVkOi+OhFFI0/vyZNwt62XCatkwSIoIIOLM3+umQeqo0Wy/ujo1Ghhn5DcyG1X4PMDJHHmm5rXjyajwfp8goT/Y+efeQtJbp1bnZzegQyMizj8YyAsPX9KCY79x8gZ4S36wrNr1t5j3q8UQcXlAR7WCPqrq0+EQW/u4s5syLuNoCjhUnAXG1BUro48L55DoiUDyoo5z7oXHvMNZ+VhVGvvB5PP3xLvccDrhXFE9hNlxuhX9BzotGzP5FZiqC8y+Z3lH439lKDp+G3vfp0TFghIi/HG9ZOkk6iW67bphSD4Xr/tZNjp0b6+NBm/QNwKlWFI1nJgcTj1; _gat=1; RT="sl=8&ss=1662722711552&tt=8248&obo=0&sh=1662722770789%3D8%3A0%3A8248%2C1662722767437%3D7%3A0%3A5669%2C1662722753821%3D6%3A0%3A5338%2C1662722744446%3D5%3A0%3A5020%2C1662722738723%3D4%3A0%3A4350&dm=my.alldata.com&si=7de1ffb0-2fb5-4d72-a34e-cda08b2da35c&bcn=%2F%2F684d0d49.akstat.io%2F"; bm_sv=E54B0A0882B6CA2E3BE58465FD3E8C82~YAAQ26zbF9JOz/WCAQAA60wAIhEUjqkrd+YmdBdBuWcnEhdcQWXRQnUvoVz3flKFvTQZeZB5JbsqgQqg0BcKjbsc34ZOdzNfNd3crq4h7S2H37pC6hUFVR2XGjguWXcGaMkgJD4tYqOIZUkvNkKFXdB+CAWMaxevfJ7yLOanSnAt0axzSX0dHRKaX02BNUMxccA5wqso54DIfKTMMcv3t40yxtUXL7eG92oMf7vOhiVUEDpZ7KZYIUaRnXy2VjdZYcs=~1';
    const PATH_ALLDATA = "https://my.alldata.com";

    static public function  remove_element_reponse($body, $contentType, $statusCode = null,  $request, $needSave = false)
    {

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $domain_request = $_SERVER['HTTP_HOST'];


        $tailLink = $_SERVER["REQUEST_URI"];

        if (str_contains($tailLink, '_png')) {
            $contentType = "image/png";
        } else 
        if (str_contains($tailLink, '_jpg')) {
            $contentType = "image/jpeg";
        } else 
        if (str_contains($tailLink, 'svg')) {
            $contentType = "image/svg+xml";
        }


        //Save vao db
        if ($request != null && $request->method() == "GET" && ($statusCode == 200 || $statusCode == 404) && $needSave == true) {

            try {
                // $isRepaidHtml = str_contains($contentType, 'text/html') && str_contains($tailLink, '/repair/');
                // $isRepaidHtml = false;

                // if (
                //     $request != null
                //     && !$isRepaidHtml
                //     && !str_contains($contentType, "image")
                //     && !str_contains($body, "errorCode")
                //     && !str_contains($body, "Access Denied")
                //     && $body != null && $body != ""
                //     && !str_contains($tailLink, "ogin")
                //     && !str_contains($body, 'Login')
                // ) {

                $isFileJavascriptNotError = str_contains($contentType, 'javascript') && (
                    str_contains($body, "errorCode")
                    || str_contains($body, "Access Denied")
                    || str_contains($body, 'Login')
                );


                if (
                    $request != null
                    && $body != null && $body != ""
                    // && !str_contains($contentType, "image")
                    &&  ($isFileJavascriptNotError  ||
                        (!str_contains($body, "errorCode") &&
                            !str_contains($body, "Access Denied") &&
                            !str_contains($body, 'Login')))
                ) {

                    $tailLink = Helper::removeParamsFromUrl($tailLink, ['t']);
                    $webClass = new WebClass();
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::ALLDATA_US;
                    $webClass->method = $request->method();
                    $webClass->content_type =  $contentType;
                    $webClass->body =  $body;
                    $webClass->status_code =  $statusCode;

                    WebDataUtils::saveWebData($webClass);
                }
            } catch (Exception $e) {
            }
        }

        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);


        if (str_contains($domain_request, 'my-alldata.')) {
            $pathNoHTTP = str_replace("http://", "", $path);
            $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();
            $body =  str_replace("my.alldata.com", "my-alldata." . $domainMain, $body);
            $body =  str_replace("https://my.alldata.com", "https://my-alldata." . $domainMain, $body);
            $body =  str_replace("http://my-alldata." . $domainMain, "https://my-alldata." . $domainMain, $body);
        }

        $body =  str_replace("https://my.alldata.com", $path, $body);
        $body =  str_replace("cdn.walkme.com", "thisiscoin.com", $body);
        $body =  str_replace("siteintercept.qualtrics.com", "siteintercept.qualtrics1.com", $body);
        $body =  str_replace("G-34GFS6WVL2", "G-34123456X", $body);

        if (!str_contains($domain_request, 'my-alldata.')) {
            $body =  str_replace('src=\"/', 'src=\"' . $path . '/', $body);
            $body =  str_replace('href=\"/', 'href=\"' . $path . '/', $body);
        }


        if (str_contains($request->data3_domain, "webads1.com")) {
            $body =  str_replace("https://webads1.com", $path, $body);
            $body =  str_replace("http://webads1.com", $path, $body);
            $body =  str_replace("webads1.com", $pathNoHTTP, $body);
        }

        if (str_contains($contentType, 'text/html')) {
            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/service/alldata_us.js?v=45"></script>', $body);
            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/jssav/' . Helper::generateRandomString(10) . '.js"></script>', $body);
            $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                    <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag("js", new Date());
                
                    gtag("config", "G-KXWTNY3G2J");
                    </script>', $body);


            //KHU VỰC OVERRIDE
            //   $body =  str_replace('main.0d4df57cc4e69162.js?v=2.51.2296', "https://my-alldata." . $domainMain . "/alldata_override/js_override/" . "main.0d4df57cc4e69162.js?V=4", $body);

        }
        //KHÔNG THỂ XÓA BẰNG DOM CHỈ XÓA BẰNG FILEJS

        if ($request->user != null && str_contains($contentType, 'text/html')) {

            $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_alldata");
            if ($remain_days > 0 && $remain_days < 10) {
                $appName = DomainConfigHelper::getConfig('appName');
                $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);

                $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                $body =  HtmlUtils::addTagHtmlOnHead('<script>
                 
                     siiimpleToast.alert(" Alldata expiry date is only ' . $remain_days . ' days, remember to renew!", {
                      position: "top|center",
                      margin: 15,
                      delay: 0,
                      duration: 5000,
                    });
                    </script>', $body);
            }
        }

        if ($tailLink == '/repair/' && str_contains($contentType, 'text/html')) {

            function replaceScriptSrc($inputText)
            {
                $pattern = '/<script\s+type="text\/javascript"\s+src="\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+"><\/script>/';



                $replacement = '<script type="text/javascript" src="/ADAG/repair/ADConnect/v5/account/recent_cars"></script>';
                $outputText = preg_replace($pattern, $replacement, $inputText);

                return $outputText;
            }

            if (str_contains($body, 'Your membership will expire in') || str_contains($body, 'All Data Usa - Login')) {
                $body = "";
            }

            $body = replaceScriptSrc($body);
        }


        if ($request->user != null && str_contains($tailLink, 'profile') && !str_contains($tailLink, 'test')) {
            function isJson($string)
            {
                return ((is_string($string) &&
                    (is_object(json_decode($string)) ||
                        is_array(json_decode($string))))) ? true : false;
            }

            if (isJson($body)) {
                $dataBody = json_decode($body, true);

                if (isset($dataBody['userId'])) {
                    $dataBody['userId'] = $request->user->id;
                }
                if (isset($dataBody['brandRefName'])) {
                    $dataBody['brandRefName'] = "";
                }
                if (isset($dataBody['platformName'])) {
                    $dataBody['platformName'] = "";
                }
                if (isset($dataBody['userName'])) {
                    $dataBody['userName'] = $request->user->username;
                }
                if (isset($dataBody['platformName'])) {
                    $dataBody['platformName'] = "";
                }

                if (isset($dataBody['_links']['site']['href'])) {
                    $dataBody['_links']['site']['href'] = "";
                }
                if (isset($dataBody['_links']['self']['href'])) {
                    $dataBody['_links']['self']['href'] = "";
                }
                if (isset($dataBody['_links']['products']['href'])) {
                    $dataBody['_links']['products']['href'] = "";
                }

                ///
                if (isset($dataBody['_embedded']['site']['siteId'])) {
                    $dataBody['_embedded']['site']['siteId'] = "";
                }

                if (isset($dataBody['_embedded']['site']['siteName'])) {
                    $dataBody['_embedded']['site']['siteName'] = "";
                }
                if (isset($dataBody['_embedded']['site']['addressLine1'])) {
                    $dataBody['_embedded']['site']['addressLine1'] = "";
                }
                if (isset($dataBody['_embedded']['site']['phoneNumber'])) {
                    $dataBody['_embedded']['site']['phoneNumber'] = "";
                }
                if (isset($dataBody['_embedded']['site']['contactName'])) {
                    $dataBody['_embedded']['site']['contactName'] = "";
                }
                if (isset($dataBody['_embedded']['site']['state'])) {
                    $dataBody['_embedded']['site']['state'] = "";
                }
                if (isset($dataBody['_embedded']['site']['stateCd'])) {
                    $dataBody['_embedded']['site']['stateCd'] = "";
                }
                if (isset($dataBody['_embedded']['site']['city'])) {
                    $dataBody['_embedded']['site']['city'] = "";
                }
                if (isset($dataBody['_embedded']['site']['country'])) {
                    $dataBody['_embedded']['site']['country'] = "";
                }
                if (isset($dataBody['_embedded']['site']['countryCd'])) {
                    $dataBody['_embedded']['site']['countryCd'] = "";
                }
                if (isset($dataBody['_embedded']['site']['typeCd'])) {
                    $dataBody['_embedded']['site']['typeCd'] = "";
                }

                ///

                if (isset($dataBody['_embedded']['site']['_links']['self']['href'])) {
                    $dataBody['_embedded']['site']['_links']['self']['href'] = "";
                }
                if (isset($dataBody['_embedded']['site']['_links']['accessPoints']['href'])) {
                    $dataBody['_embedded']['site']['_links']['accessPoints']['href'] = "";
                }

                if (isset($dataBody['_embedded']['profile']['personProfileId'])) {
                    $dataBody['_embedded']['profile']['personProfileId'] = $request->user->id;
                }
                if (isset($dataBody['_embedded']['profile']['firstName'])) {
                    $dataBody['_embedded']['profile']['firstName'] = $request->user->username;
                }
                if (isset($dataBody['_embedded']['profile']['lastName'])) {
                    $dataBody['_embedded']['profile']['lastName'] = "";
                }
                if (isset($dataBody['_embedded']['profile']['emailAddress'])) {
                    $dataBody['_embedded']['profile']['emailAddress'] = $request->user->username;
                }
                if (isset($dataBody['_embedded']['site']['emailAddress'])) {
                    $dataBody['_embedded']['site']['emailAddress'] = $request->user->username;
                }

                $body = json_encode($dataBody);
            }

            function replaceText($inputText)
            {
                $pattern = '/Please call .*? regarding your ALLDATA account\./';
                $replacement = 'Sorry! ALLData US of maintenance';
                $outputText = preg_replace($pattern, $replacement, $inputText);
                return $outputText;
            }
            $body = replaceText($body);

            function replaceText2($inputText)
            {
                $pattern = '/access limit reached\. Please call .*? for assistance/';
                $replacement = 'Sorry! ALLData US of maintenance';
                $outputText = preg_replace($pattern, $replacement, $inputText);
                return $outputText;
            }

            $body = replaceText2($body);
        }


        if (str_contains($contentType, 'javascript')) {
            $body =  str_replace("800-859-3282", "+*************", $body);


            if ($body == "") {
                abort(404);
            }
        }
        if (str_contains($contentType, 'json')) {
            $configAdmin =      ConfigAdmin::first();
            if ($configAdmin  != null &&   $configAdmin->alldata_us_v2_info != null && $request->user != null) {

                $first_name = $configAdmin->alldata_us_v2_info->first_name ?? "";
                $last_name = $configAdmin->alldata_us_v2_info->last_name ?? "";
                $site_name = $configAdmin->alldata_us_v2_info->site_name ?? "";
                $user_name = $configAdmin->alldata_us_v2_info->user_name ?? "";

                $username = $request->user->username ?? "";
                $username2 =  "";
                $name = $request->user->name ?? "";



                $body =  str_replace("\"siteName\":\"$site_name\"", "\"siteName\":\"$name\"", $body);
                $body =  str_replace("\"userName\":\"$user_name\"", "\"userName\":\"$username\"", $body);

                $body =  str_replace("\"firstName\":\"$first_name\"", "\"firstName\":\"$username\"", $body);
                $body =  str_replace("\"lastName\":\"$last_name\"", "\"lastName\":\"$username2\"", $body);
            }
        }

        if ($body == "" && str_contains(".js",  $tailLink)  && str_contains(".css",  $tailLink)) {
            abort(404);
        }
        if (str_contains($body, '<TITLE>Access Denied</TITLE>')) {
            abort(404);
        }

        header('Content-type: ' .  $contentType ?? "");
        return   $body;
    }
    /**
     * Thông tin server
     */
    static public function  getResponse($link, $cookies_str = null, $method = "GET", $body = [], Request $request = null, $proxy = null)
    {


        $configAdmin =  ConfigAdmin::first();
        $tailLink = $_SERVER["REQUEST_URI"];


        if (!str_contains($tailLink, 'check_')) {
            //  usleep(1000000);
        }


        if (
            $tailLink == "/"
        ) {
            $domainMain = Helper::getDomainCurrent();
            header("Location: " . $domainMain . "/repair/#/landingPage");
            exit;
        }

        if (
            str_contains($tailLink, 'changePassword') ||
            str_contains($tailLink, 'my-profile')
        ) {
            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();
            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }

        /////
        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }
        $headers_requests = Helper::capitalizeArrayKeys($headers_requests);


        if ($cookies_str == null) {

            $cookies_str = Cache::get(json_encode(['cookies_alldata_us_v2_str']));
            $cookies_str = null;

            if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_alldata_us_v2_str)) {
                $cookies_str = $configAdmin->cookies_alldata_us_v2_str;
            }
        }

        if ($proxy == null) {

            if ($configAdmin  != null && !empty($configAdmin->proxy_alldata_us_v2)) {
                $proxy = $configAdmin->proxy_alldata_us_v2;
            }

            //  $proxy  = ProxyUtils::chooseProxyForAlldataUS();
        }

        $domain_request = $_SERVER['HTTP_HOST'];
        if (!str_contains($domain_request, 'my-alldata.')) {
            $headers_requests['Accept'] = "*/*";

            if (str_contains($link, '/ADConnect/v5/carids/')) {
                $headers_requests['Accept'] = 'application/hal+json';
            }
            if (str_contains($link, '/ADAG/car-lookup/v3/')) {
                $headers_requests['Accept'] = 'application/json,application/hal+json ,text/plain';
            }
            if (str_contains($link, 'html_article') || (str_contains($link, 'guid') && str_contains($link, 'html'))) {
                //$headerArr['Accept'] = 'application/json, text/plain, */*';
                $headers_requests['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9';
            }
            if (str_contains($link, 'png')  || str_contains($link, 'jpg')  || str_contains($link, 'gif') || str_contains($link, 'jpeg') || str_contains($link, 'svg')) {
                //$headerArr['Accept'] = 'application/json, text/plain, */*';
                $headers_requests['Accept'] = 'image/svg+xml,text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9';
            }
        }



        unset($headers_requests['Cookie']);
        unset($headers_requests['Host']);
        unset($headers_requests['Connection']);
        unset($headers_requests['Dnt']);
        // unset($headers_requests['accept-language']);
        unset($headers_requests['Cf-Connecting-Ip']);
        unset($headers_requests['X-Forwarded-For']);
        unset($headers_requests['REMOTE_ADDR']);

        unset($headers_requests['Cdn-Loop']);
        unset($headers_requests['User-Agent']);

        unset($headers_requests['Cf-Ipcountry"']);

        if (isset($headers_requests['Referer'])) {
            $headers_requests['Referer'] = str_replace($domain_request, 'my.alldata.com', $headers_requests['Referer']);
        }

        if (isset($headers_requests['Origin'])) {
            $headers_requests['Origin'] = str_replace($domain_request, 'my.alldata.com', $headers_requests['Origin']);
        }


        $headerArr  =  $headers_requests;
        $headerArr  = array();
        $headerArr['User-Agent'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:134.0) Gecko/20100101 Firefox/134.0';
        $headerArr['Sec-Ch-Ua'] = '"Chromium";v="106", "Google Chrome";v="106", "Not;A=Brand";v="99"';
        $headerArr['Sec-Ch-Ua-Mobile'] = '?0';
        $headerArr['Sec-Ch-Ua-Platform'] = '"Windows"';
        $headerArr['Accept'] = $headers_requests['Accept'] ?? $headers_requests['accept'] ?? "application/json, text/plain, */*";
        $headerArr['Upgrade-Insecure-Requests'] = 1;
        $headerArr['Accept-Language'] = 'en-US,en;q=0.9';
        $headerArr['Accept-Language'] = 'gzip, deflate, br, zstd';
        $headerArr['Country'] = 'United States (US)';
        $headerArr['Referer'] = 'https://my.alldata.com/';



        // foreach ($headerArr as $key => $value) {
        //     $headerArr[$key] = str_replace($domain_request, 'my.alldata.com', $headerArr[$key]);
        //     if (strpos($key, 'Sec') === 0) {
        //         unset($headerArr[$key]);
        //     }
        //     if (strpos($key, 'CF') === 0) {
        //         unset($headerArr[$key]);
        //     }
        //     if (strpos($key, 'IP') === 0) {
        //         unset($headerArr[$key]);
        //     }
        //     if (strpos($key, 'Real') === 0) {
        //         unset($headerArr[$key]);
        //     }
        //     if (strpos($key, 'Cf') === 0) {
        //         unset($headerArr[$key]);
        //     }
        //     if (strpos(strtolower($key), 'ip') === 0) {
        //         unset($headerArr[$key]);
        //     }
        //     if (strpos(strtolower($key), 'Forwarded') === 0) {
        //         unset($headerArr[$key]);
        //     }

        //     //value
        //     if (strpos($value, 'VN') === 0 || strpos($value, 'vn') === 0 || strpos($value, 'vi') === 0 || strpos($value, 'VI') === 0) {
        //         unset($headerArr[$key]);
        //     }
        // }


        if (str_contains($cookies_str, "DOMAIN_REQUEST_SERVER")) {
            $cookiesArray = CookiesUtils::cookieStrToArray($cookies_str);
            $link = str_replace('https://my.alldata.com', $cookiesArray['DOMAIN_REQUEST_SERVER'], $link);
            $request->merge(['data3_domain' => $cookiesArray['DOMAIN_REQUEST_SERVER']]);
        }


        $cookieLast = CookiesUtils::cookieStrToArray($cookies_str);
        // $cookiesNewArray = [];
        // $cookiesNewArray['Access-Token'] =  $cookieLast['Access-Token'];
        // if (isset($cookieLast['Access-Token-Refresh'])) {
        //     $cookiesNewArray['Access-Token-Refresh'] =  $cookieLast['Access-Token-Refresh'];
        // }
        //$cookies_str = CookiesUtils::cookiesKeyValueToStr($cookiesNewArray);



        //WAY 2
        $cookieLast['OptanonConsent'] = "isIABGlobal=false&datestamp=Fri+Jun+28+2024+15%3A51%3A00+GMT-0700+(Pacific+Daylight+Time)&version=5.9.0&landingPath=NotLandingPage&groups=1%3A1%2C2%3A0%2C3%3A0%2C4%3A0%2C0_243830%3A0%2C0_243831%3A0%2C0_243832%3A0%2C0_243826%3A0%2C0_243827%3A0%2C0_243828%3A0%2C0_243829%3A0&AwaitingReconsent=false&isGpcEnabled=0&browserGpcFlag=0&geolocation=US%3B&hosts=";
        $cookies_str = CookiesUtils::cookiesKeyValueToStr($cookieLast);

        $res = CurlUtils::request($method, $link, [
            'headers' =>  $headerArr,
            'cookie' =>  $cookies_str,
            'proxy' => $proxy
        ]);


        // $cookiesNew = null;
        // if (isset($res->headers['set-cookie']) && is_array($res->headers['set-cookie'])) {
        //     $set_cookies = $res->headers['set-cookie'];

        //     $cookieLast = CookiesUtils::cookieStrToArray($cookies_str);
        //     $cookiesNewArray = [];
        //     $cookiesNewArray['Access-Token'] =  $cookieLast['Access-Token'];
        //     $cookiesNewArray['Access-Token-Refresh'] =  $cookieLast['Access-Token-Refresh'];

        //     if (count($cookieLast) > 0) {
        //         foreach ($set_cookies  as $cooki_item) {
        //             $splitItem = explode(";", $cooki_item);
        //             if (count($splitItem) > 0) {
        //                 $splitKeyValue = explode("=", $splitItem[0],2);
        //                 if (count($splitKeyValue) > 1) {
        //                     if ($key == 'Access-Token' || $key == 'Access-Token-Refresh') {
        //                         $key = $splitKeyValue[0];
        //                         $value = $splitKeyValue[1];
        //                         $cookiesNewArray[$key] = $value;
        //                     }
        //                 }
        //             }
        //         }
        //     }

        //     $cookiesNew = CookiesUtils::cookiesKeyValueToStr($cookiesNewArray);

        //     if ($configAdmin  != null && !empty($cookiesNew)) {
        //         $expiresAt = Carbon::now()->addSeconds(6);
        //         Cache::put(json_encode(['cookies_alldata_us_v2_str']), $cookiesNew, $expiresAt);
        //         $configAdmin->update([
        //             'cookies_alldata_us_v2_str' => $cookiesNew
        //         ]);
        //     }
        // }


        return $res;
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        try {

            $response1  = AllDataUSv2UtilsWeb::getResponse('https://my.alldata.com/ADAG/product/access?product=repair', $cookies_str, 'GET', null, null, $proxy);
            $body1 = $response1->getResponseBody();
            if (str_contains($body1, '<TITLE>Access Denied</TITLE>')) {
                return [
                    'status' => false,
                    'mess' => "ERROR from PROXY"
                ];
            }

            if (str_contains($body1, 'errorCode')) {
                return [
                    'status' => false,
                    'mess' => "ERROR from COOKIE"
                ];
            }

            $time_start = microtime(true);
            $response  = AllDataUSv2UtilsWeb::getResponse('https://my.alldata.com/ADAG/sso/profile', $cookies_str, 'GET', null, null, $proxy);
            $type = $response->getContentType();
            $body = $response->getResponseBody();

            if (!str_contains($body, '1015')) {
                $info_account_list = json_decode($body);

                $user_name = $info_account_list->userName;
                $user_id = $info_account_list->userId;



                $email = $info_account_list->_embedded->profile->emailAddress;

                $firstName = $info_account_list->_embedded->profile->firstName;
                $lastName = $info_account_list->_embedded->profile->lastName;

                $siteName = $info_account_list->_embedded->site->siteName;

                $time_end = microtime(true);
                return [
                    'time_handle' => ($time_end - $time_start),
                    'status' => true,
                    'mess' => "",
                    'user_name' => $user_name,
                    'user_id' => $user_id,
                    'email' => $email,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'site_name' => $siteName,
                ];
            }
        } catch (Exception $e) {

            LogUtils::error($e);

            return [
                'status' => false,
                'mess' => $e->getMessage() . "ERROR COOKIES 1"
            ];
        }
        return [
            'status' => false,
            'mess' => "ERROR from COOKIE 2"
        ];
    }
}
