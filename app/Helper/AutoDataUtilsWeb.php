<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;

use Carbon\Carbon;
use Exception;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\TransferStats;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use Illuminate\Support\Facades\Cache;


class AutoDataUtilsWeb
{

    const PATH_AUTODATA = "https://workshop.autodata-group.com";


    static public function  remove_element_reponse($body, $contentType, $statusCode, $request, $needSave = false)
    {
        $currentDomain = $_SERVER['HTTP_HOST'];
        $host =  "$_SERVER[HTTP_HOST]";
        $ContentType = $contentType;
        $tailLink = $_SERVER["REQUEST_URI"];

        //Save vao db

        // if ($request != null && $needSave  == true) {
        //     try {

        //         if ($request != null && !str_contains($tailLink, "ogin") && $body != null && !str_contains($body, '<input type="hidden" name="form_id" value="user_login" />')) {


        //             parse_str(request()->getContent(), $outputArray);
        //             $tailLinkSave = Helper::removeParamsFromUrlAutodata($tailLink);

        //             $webClass = new WebClass();
        //             $webClass->tail_link = $tailLinkSave;
        //             $webClass->service =  PlanUtils::AUTODATA;
        //             $webClass->method = $request->method();
        //             // dd($webClass->method); 
        //             $webClass->content_type =  $contentType;
        //             $webClass->body =  (string)$body;
        //             $webClass->bodyRequest =  $outputArray;
        //             $webClass->status_code =  $statusCode ?? 200;

        //             WebDataUtils::saveWebData($webClass);
        //         }
        //     } catch (Exception $e) {
        //         dd($e);
        //     }
        // }

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        // dd($tailLink);
        // $body = str_replace("w1/vehicles/", "ww/vehicles2/",  $body);
        // $body = str_replace("w1/manufacturers", "w10/manufacturers2",  $body);
        // $body = str_replace("w1/manufacturers/", "w10/manufacturers2/",  $body);

        if ($statusCode  != 200) {

            if ($statusCode  == 404) {
                header('HTTP/1.0 404 Not Found', true, 404);
            }
        }

        function isJson($string)
        {
            json_decode($string);
            return json_last_error() === JSON_ERROR_NONE;
        }
        if (str_contains($body, '{"main-service":"')) {
        } else
        if (!str_contains($tailLink, 'engines/codes') && !isJson($body)) {

            if (str_contains($ContentType, 'text/html')) {

                $dom = HtmlDomParser::str_get_html($body);
                if ($dom  != null) {
                    //Remove last web pc
                    $lastCom = $dom->find('#jobsRecentMain', 0);

                    if ($lastCom != null) {
                        $lastCom->remove();
                    }

                    $searchSwitch = $dom->find('.search-switch', 0);
                    if ($searchSwitch  != null) {
                        $searchSwitch->remove();
                    }

                    if ($lastCom != null) {
                        $lastCom->remove();
                    }

                    // Training
                    $Training = $dom->find('.adt-full', 0);
                    if ($Training  != null) {
                        $Training->remove();
                    }
                }

                $body =  $dom == null ? $body : $dom->innertext;
            }

            if (str_contains($tailLink, '.js')) {
                $ContentType = 'application/javascript';
            }
        }

        if (str_contains($body, 'production-images.autodata-group.com')) {
            $body = str_replace("production-images.autodata-group.com", $_SERVER['HTTP_HOST'] . "/production-images-data1",  $body);
        }
        if (str_contains($body, 'assets.autodata-group.com')) {
            $body = str_replace("assets.autodata-group.com", $_SERVER['HTTP_HOST'] . "/access-autodata1",  $body);
        }
        if (str_contains($body, 'utrkdata.pendo.workshop.autodata-group.com')) {
            $body = str_replace("utrkdata.pendo.workshop.autodata-group.com", $_SERVER['HTTP_HOST'] . "/utrkdata-pendo1",  $body);
        }
        if (str_contains($body, 'utrkcontent.pendo.workshop.autodata-group.com')) {
            $body = str_replace("utrkcontent.pendo.workshop.autodata-group.com", $_SERVER['HTTP_HOST'] . "/utrkcontent-pendo1",  $body);
        }



        $body = str_replace("enginecode-api.js", "enginecode-api-override.js?V=6",  $body);


        if (!str_contains($tailLink, 'engines/codes') && (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json'))) {



            $body =  str_replace("https://assets.autodata-group.com", $path, $body);
            $body =  str_replace("http://assets.autodata-group.com", $path, $body);

            $body =  str_replace("https://workshop.autodata-group.com", $path, $body);

            $body =  str_replace("//assets.autodata-group.com", "//" . $pathNoHTTP, $body);

            $body =  str_replace("workshop.autodata-group.com", $pathNoHTTP, $body);

            $body =  str_replace("assets.autodata-group.com", $pathNoHTTP, $body);


            $body =  str_replace("http://", 'https://', $body);

            $body =  str_replace("Your payment details need updating", 'Welcome to FHOST', $body);
            $body =  str_replace("Please contact us now to maintain your Autodata access", 'Have a productive day', $body);
            $body =  str_replace("+44 (0)1628 688 111", '+86189 8087 6959', $body);

            $host =  "$_SERVER[HTTP_HOST]";

            // $configAdmin =      ConfigAdmin::first();
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null &&   $request != null &&  $request->user != null) {

                if (str_contains($ContentType, 'text/html')) {
                    $dom = HtmlDomParser::str_get_html($body);
                    if ($dom != null) {
                        $moreAccount = $dom->find('li.more a span', 0);
                        if ($moreAccount  != null) {
                            $moreAccount->innertext = '<span> ' . $request->user->name . ' <i class="fa fa-sort-desc" aria-hidden="true"></i> <i class="fa fa-sort-asc" aria-hidden="true"></i> </span>';
                        }

                        $accountInfo = $dom->find('li.more ul li.account-info', 0);
                        if ($accountInfo  != null) {
                            $accountInfo->innertext = '<span>' . $request->user->username . '</span>';
                        }

                        $body =  $dom == null ? $body : $dom->innertext;
                    }
                }

                if (str_contains($tailLink, 'w2/api/user')) {

                    header('Content-Type: application/json');
                    $arr = json_decode($body, true);
                    try {
                        $arr['name'] = $request->user->name;
                        $arr['username'] = $request->user->name;
                        $arr['email'] = $request->user->email;
                        $arr['invoice_email'] = $request->user->email;
                        $arr['id'] = 1234567;
                        $arr['garage']['email'] = $request->user->email;
                        $arr['garage']['account_number'] = $request->user->email;
                        $arr['garage']['name'] = $request->user->name;
                        $arr['garage']['address']['city'] = '';
                        $arr['garage']['address']['postcode'] = '';
                        $arr['garage']['address']['country'] = '';
                        $arr['garage']['address']['country_code'] = '';
                        $arr['garage']['address']['address1'] = '';
                        $arr['garage']['address']['address1'] = $request->user->address_detail;
                    } catch (Exception $e) {
                    }
                    $json_res = json_encode($arr, true);
                    echo $json_res;
                    die();
                }
            }
        }

        $body = str_replace("/user/logout", "/w1/model-selection",  $body);
        $body = str_replace("https://audata.la/rlogout", "/w1/model-selection",  $body);
        header('Content-type: ' .  $ContentType ?? "");
        echo $body;
        die();
    }


    static public function setCache($request, $key, $value)
    {
        $expiresAt = Carbon::now()->addMinutes(60);
        Cache::put(json_encode([$request == null || $request->user == null ? null : $request->user->id,  $key]), $value, $expiresAt);
    }
    static public function getCache($request, $key)
    {
        return Cache::get(json_encode([$request == null || $request->user == null ? null : $request->user->id, $key]));
    }

    /**
     * Thông tin server
     */
    static public function  getResponse($link, $method = "GET", $body = [], $cookies_str = null, $request = null, $proxy = null)
    {


        if ($proxy === null) {
            // $configAdmin =      ConfigAdmin::first();

            // if ($configAdmin  != null && !empty($configAdmin->proxy_autodata)) {
            //     $proxy = $configAdmin->proxy_autodata;
            // }

            $proxy  = ProxyUtils::chooseProxyForAutodata();
        }

        $tailLink = $_SERVER["REQUEST_URI"];


        if (!str_contains($tailLink, "js") &&  !str_contains($tailLink, "css")) {
            //Chặn request thừa
            $key = 'throttleautodata:' . $proxy; // Key Cache dựa trên địa chỉ IP của client
            $allowedRequests = 15; // Số lượng yêu cầu tối đa trong cacheTime giây
            $cacheTime = 5; // Thời gian cache trong giây

            $currentTimestamp = time();
            $expireTimestamp = $currentTimestamp + $cacheTime;

            $count = Cache::get($key, 0);
            if ($count >= $allowedRequests) {
                return new Exception('Too many requests. Please try again later.');
            }
            if ($count === 0 || $currentTimestamp > $expireTimestamp) {
                // Reset count and set new expiration timestamp
                Cache::put($key, 1, $cacheTime);
            } else {
                // Increment count
                Cache::increment($key);
            }
        }
        ///


        if (
            str_contains($tailLink, 'reg-sess')
        ) {
            return null;
        }

        $w1 = [
            "service-schedules",
            "service-illustrations",
            "service-indicator",
            "service-brakes",
            "engine-oil",
            "service-transmission-variants",
            "service-summary-variants",
            "service-ac",
            "engine-management",
            "camshaft-drive-system",
            "auxiliary-drive-belts",
            "clutches",
            "anti-lock-brake-systems",
            "wheel-alignment",
            "tyre-pressures-variants",
            "tyre-pressure-monitoring-system",
            "tyres",
            "airbags",
            "key-programming",
            "air-conditioning",
            "battery-disconnection-and-reconnection",
            "diagnostic-trouble-codes",
            "control-module-pin-data",
            "electrical-component-locations",
            "bulbs",
            "fuses-and-relays",
            "technical-specifications",
            "repair-times-variants",
            "known-fixes",
            "vin-plate-location",
            "warning-lamp-and-symbols",
            "wiring-diagrams",
            "diesel-exhaust-gas-aftertreatment",
        ];

        $w2 = [
            'engine-oil',
            'tyres',
            "tyre-pressure-monitoring-system",
            "service-indicator",
            "key-programming",
            "wheel-alignment",
            "battery-disconnection-and-reconnection",
            "electric-parking-brake",
            "diesel-exhaust-gas-aftertreatment",
            "camshaft-drive-system",
            "service-advisor",
            "static-content",
            "diagnostic-trouble-codes",
        ];

        $vehicle_id =   AutoDataUtilsWeb::getCache($request, 'vehicle_id');
        $engine_id =  AutoDataUtilsWeb::getCache($request, 'engine_id');
        $route_name = $request == null ? null : ($request->input('route_name') ?? null);

        if (
            $method == "GET" &&

            str_contains($tailLink, '/w1/manufacturers') &&  str_contains($tailLink, 'engines')
            && in_array($route_name, $w2) &&  $vehicle_id != null &&  !str_contains($tailLink, 'change_engine=true')
        ) {


            $engine_name =   AutoDataUtilsWeb::getCache($request, 'engine_name');

            $module_id =   AutoDataUtilsWeb::getCache($request, 'module_id');
            $back =  AutoDataUtilsWeb::getCache($request, 'back');

            header("Location: /w2/$route_name/$vehicle_id"); //?cacao=drcar
            exit;
        }

        if (
            $method == "GET" &&
            str_contains($tailLink, '/w1/manufacturers')  &&  str_contains($tailLink, 'change_engine=true')
        ) {


            header("Location: /w1/model-selection");
            exit;
        }


        if ($request != null && $request->manufacturer_id != null && $request->model_id != null) {
            AutoDataUtilsWeb::setCache($request, 'manufacturer_id', $request->manufacturer_id);
            AutoDataUtilsWeb::setCache($request, 'model_id', $request->model_id);
        }

        if (
            $request != null && $request->vehicle_id != null
            && $request->engine_id != null
            && $request->engine_name != null
            && $request->route_name != null
        ) {
            AutoDataUtilsWeb::setCache($request, 'vehicle_id', $request->vehicle_id);
            AutoDataUtilsWeb::setCache($request, 'engine_id', $request->engine_id);
            AutoDataUtilsWeb::setCache($request, 'engine_name', $request->engine_name);
            AutoDataUtilsWeb::setCache($request, 'route_name', $request->route_name);
            AutoDataUtilsWeb::setCache($request, 'module_id', $request->module_id);
            AutoDataUtilsWeb::setCache($request, 'back', $request->back);
        }

        $hasSelectModel = false;
        if ($request != null) {
            $manufacturer_id =   AutoDataUtilsWeb::getCache($request, 'manufacturer_id');
            $model_id =  AutoDataUtilsWeb::getCache($request, 'model_id');


            if (
                $method  == "GET" &&
                $manufacturer_id != null &&

                !str_contains($tailLink, '/w2/')  &&
                $model_id != null &&
                (str_contains($link, 'w1/vehicles') ||   str_contains($link, 'w1/diagram')  || str_contains($link, 'w1/manufacturers') ||  in_array(request('route_name'), $w1))
                &&
                !str_contains($link, 'vehicles/variants')
            ) {

                $hasSelectModel =  true;
                try {
                    AutoDataUtilsWeb::getResponse(
                        'https://workshop.autodata-group.com/selection/save-in-jobfolder/0/undefined',
                        'POST',
                        [
                            "manufacturer_id" =>  $manufacturer_id,
                            "model_id" => $model_id,
                        ],
                        null,
                        $request,
                        null,
                        true
                    );
                } catch (Exception $e) {
                }
            }
        }

        if ($request != null) {
            $manufacturer_id =   AutoDataUtilsWeb::getCache($request, 'manufacturer_id');
            $model_id =  AutoDataUtilsWeb::getCache($request, 'model_id');

            $vehicle_id =   AutoDataUtilsWeb::getCache($request, 'vehicle_id');
            $engine_id =  AutoDataUtilsWeb::getCache($request, 'engine_id');
            $engine_name =   AutoDataUtilsWeb::getCache($request, 'engine_name');
            $route_name =  AutoDataUtilsWeb::getCache($request, 'route_name');
            $module_id =   AutoDataUtilsWeb::getCache($request, 'module_id');
            $back =  AutoDataUtilsWeb::getCache($request, 'back');


            if (
                $method  == "GET" &&
                !str_contains($tailLink, '/w2/')  &&
                (str_contains($link, 'vehicles/variants') || str_contains($link, 'change_engine=true')  ||
                    in_array(request('route_name'), $w1)   ||
                    !str_contains($link, 'w1/vehicles/'))

            ) {
                if ($vehicle_id == null && $engine_id == null) {

                    AutoDataUtilsWeb::getResponse(
                        "https://workshop.autodata-group.com/w1/vehicle-selection/mid/AUDd082073",
                        'POST',
                        [],
                        null,
                        $request,
                        null,
                        true
                    );
                }
                if (
                    $manufacturer_id != null   && $model_id != "" &&  $hasSelectModel == false
                ) {

                    try {
                        AutoDataUtilsWeb::getResponse(
                            'https://workshop.autodata-group.com/selection/save-in-jobfolder/0/undefined',
                            'POST',
                            [
                                "manufacturer_id" =>  $manufacturer_id,
                                "model_id" => $model_id,
                            ],
                            null,
                            $request,
                            null,
                            true
                        );
                    } catch (Exception $e) {
                    }
                }
                if (
                    $vehicle_id != null   && $vehicle_id != ""
                ) {


                    AutoDataUtilsWeb::getResponse(
                        "https://workshop.autodata-group.com/w1/vehicle-selection/mid/$vehicle_id",
                        'POST',
                        [
                            "vehicle_id" =>  $vehicle_id,
                            "engine_id" => $engine_id,
                            "engine_name" =>  $engine_name,
                            "route_name" => $route_name,
                            "module_id" =>  $module_id,
                            "back" => $back,
                        ],
                        null,
                        $request,
                        null,
                        true
                    );
                }
            }



            $vehicle_id =   AutoDataUtilsWeb::getCache($request, 'vehicle_id');
            if (
                $method  == "GET" &&
                (
                    ((str_contains($link, 'model-selection'))
                        && !str_contains($link, "model-selection/manufacturers") &&
                        $vehicle_id  != null)
                    ||
                    (str_contains($link, 'w1/vehicles/') && !str_contains($link, 'w1/vehicles/variants'))

                    ||
                    str_contains($link, 'change_engine=true')
                )
            ) {

                try {
                    AutoDataUtilsWeb::setCache($request, 'vehicle_id', null);
                    AutoDataUtilsWeb::setCache($request, 'engine_id',  null);
                    AutoDataUtilsWeb::setCache($request, 'engine_name',  null);
                    AutoDataUtilsWeb::setCache($request, 'route_name',  null);
                    AutoDataUtilsWeb::setCache($request, 'module_id',  null);
                    AutoDataUtilsWeb::setCache($request, 'back', null);

                    AutoDataUtilsWeb::getResponse(
                        "https://workshop.autodata-group.com/w1/vehicle-selection/mid/$vehicle_id",
                        'POST',
                        [],
                        null,
                        $request,
                        null,
                        true
                    );
                } catch (Exception $e) {
                }
            }
        }



        if (
            str_contains($tailLink, '/w1/manage-account') ||
            str_contains($tailLink, '/settings/garage') ||
            str_contains($tailLink, '/support/page') ||
            str_contains($tailLink, '/user/logout')
        ) {
            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();
            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }


        if ($cookies_str == null) {
            $configAdmin =      ConfigAdmin::first();

            if ($configAdmin  != null && !empty($configAdmin->cookies_autodata_str)) {
                $cookies_str = $configAdmin->cookies_autodata_str;
            }
        }


        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $arr = CookiesUtils::cookieStrToArray($cookies_str);


        $headerArr = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headerArr[$key] = $value[0];
                }
            }
        }

        unset($headerArr['cookie']);
        unset($headerArr['host']);
        unset($headerArr['connection']);
        unset($headerArr['dnt']);
        unset($headerArr['referer']);
        unset($headerArr['origin']);
        unset($headerArr['cf-ray']);
        unset($headerArr['content-length']);
        unset($headerArr['cf-connecting-ip']);

        unset($headerArr['user-agent']);
        unset($headerArr['User-Agent']);

        $headerArr['user-agent'] = "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.145";

        //  $arr = json_decode($cookies_autodata);

        $jar = new \GuzzleHttp\Cookie\CookieJar();


        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {


                if ($key == "access_token") {
                } else {
                    $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                        'Name'     => $key,
                        'Value'    =>  $value,
                        'Domain'   => '.autodata-group.com',
                        'Path'     => '/',
                        // 'Max-Age'  => $item['name'],
                        'Expires'  => time() + (10 * 365 * 24 * 60 * 60),
                    ]));
                }
            }
        }



        $client = new \GuzzleHttp\Client();

        try {
            $response1 = $client->request(
                $method,
                $link,
                [
                    'cookies' => $jar,
                    'headers' =>   $headerArr,
                    'timeout' => 25, // Response timeout
                    'connect_timeout' => 25, // Connection timeout
                    'form_params' => $body,
                    'proxy' => $proxy,
                    'on_stats' => !str_contains($tailLink, "w2") ? null : function (\GuzzleHttp\TransferStats $stats) {

                        $url = $stats->getHandlerStats()['redirect_url'];
                        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";

                        $url =  str_replace("https://assets.autodata-group.com", $path, $url);

                        $url =  str_replace("https://workshop.autodata-group.com", $path,  $url);
                        $url =  str_replace(" ", "",  $url);

                        $tailLink = $_SERVER["REQUEST_URI"];

                        if ($url != null && $url != "" && str_contains($tailLink, '/w1/')) {



                            $url =  str_replace("http://", "https://", $url);

                            // $parts = parse_url($url);
                            // parse_str($parts['query'], $query);
                            // if(str_contains($tailLink, 'w1/manufacturers/', ) && str_contains( $url ,'vehicles/variants/' )) {

                            // }

                            // vehicles/variants/camshaft-drive-system/TOY24901?route_name=camshaft-drive-system&module=DBM

                            header("Location: $url");
                            exit;
                        }
                    }
                ],

            );
        } catch (ClientException $e) {
            $status = $e->getResponse()->getStatusCode();

            if ($status == 404) {
                $response1 = $e->getResponse();
                return  $response1;
            }
            throw $e;
        }


        //Chọn model
        if (
            $method  == "GET" &&
            $tailLink != '/'
            &&
            ((str_contains($link, 'w1/vehicles')
                &&
                !str_contains($link, 'w1/vehicles/variants')
            ) ||
                str_contains($link, 'w1/manufacturers/')
                ||
                in_array(request('route_name'), $w1)
            )

        ) {

            $max_try = 5;
            $queries = array();
            parse_str($_SERVER['QUERY_STRING'] ?? "", $queries);
            $number_try = (int)($queries['number_try'] ?? 0) + 1;

            //Check vehicles home valid
            $body = $response1->getBody();
            $ContentType = $response1->getHeader('Content-Type')[0] ?? "";

            $valid = AutoDataUtilsWeb::checkValidContent($request, $ContentType, $body, $tailLink, $link, $w1);

            if ($valid == false) {
                $tailLink = AutoDataUtilsWeb::addToUrl2C($tailLink, "number_try", $number_try);
                header("Location: $tailLink");
                exit;
            }
        }

        //
        //Chọn engine
        $vehicle_id =   AutoDataUtilsWeb::getCache($request, 'vehicle_id');

        if (
            $method  == "GET" &&
            $tailLink != '/'
            &&
            (
                (!str_contains($link, 'w2') &&
                    !str_contains($link, 'w1/vehicles') &&
                    $vehicle_id != null)
                ||
                str_contains($link, 'w1/vehicles/variants') && request("vehicle_id") != null
                ||
                str_contains($link, 'w1/diagram') && request("vehicle_id") != null
                ||
                in_array(request('route_name'), $w1)
            )
        ) {


            $max_try = 5;
            $queries = array();
            parse_str($_SERVER['QUERY_STRING' ?? ""], $queries);
            $number_try = (int)($queries['number_try'] ?? 0) + 1;

            //Check vehicles home valid
            $body = $response1->getBody();
            $ContentType = $response1->getHeader('Content-Type')[0] ?? "";


            if (str_contains($ContentType, 'text/html')) {  //Tách svg ra
                $dom = HtmlDomParser::str_get_html($body);

                if ($dom  != null) {

                    $manufacturer_id =   AutoDataUtilsWeb::getCache($request, 'manufacturer_id');
                    $model_id =   AutoDataUtilsWeb::getCache($request, 'model_id');
                    $changeEngine = $dom->find('.change-engine', 0);
                    if ($changeEngine != null &&  $manufacturer_id  != null &&   $model_id  != null) {

                        if (!str_contains($changeEngine->parent->innertext, $manufacturer_id) && !str_contains($changeEngine->parent->innertext, $model_id) && $number_try < $max_try) {

                            $tailLink = AutoDataUtilsWeb::addToUrl2C($tailLink, "number_try", $number_try);
                            header("Location: $tailLink");
                            exit;
                        }
                    }
                    ////

                    $technicalInfo = $dom->find('.tech-info', 0);
                    if ($technicalInfo != null &&  $vehicle_id  != null) {

                        if (!str_contains($technicalInfo->innertext, $vehicle_id) &&  $number_try < $max_try) {

                            $tailLink = AutoDataUtilsWeb::addToUrl2C($tailLink, "number_try", $number_try);
                            header("Location: $tailLink");
                            exit;
                        }
                    }
                }
            }
        }



        parse_str($_SERVER['QUERY_STRING'] ?? "", $queries);
        $timesTry = (int)($queries['number_try'] ?? 0);
        $response1->timesTry = $timesTry;

        $response1->timesTry = 0;
        return  $response1;
    }

    static public function addToUrl2C($url, $key, $value = null)
    {
        $query = parse_url($url, PHP_URL_QUERY);
        if ($query) {
            parse_str($query, $queryParams);
            $queryParams[$key] = $value;
            $url = str_replace("?$query", '?' . http_build_query($queryParams), $url);
        } else {
            $url .= '?' . urlencode($key) . '=' . urlencode($value);
        }
        return $url;
    }


    static public function checkValidContent($request, $ContentType, $body,  $tailLink, $link)
    {
        $w1 = [
            "service-schedules",
            "service-illustrations",
            "service-indicator",
            "service-brakes",
            "engine-oil",
            "service-transmission-variants",
            "service-summary-variants",
            "service-ac",
            "engine-management",
            "camshaft-drive-system",
            "auxiliary-drive-belts",
            "clutches",
            "anti-lock-brake-systems",
            "wheel-alignment",
            "tyre-pressures-variants",
            "tyre-pressure-monitoring-system",
            "tyres",
            "airbags",
            "key-programming",
            "air-conditioning",
            "battery-disconnection-and-reconnection",
            "diagnostic-trouble-codes",
            "control-module-pin-data",
            "electrical-component-locations",
            "bulbs",
            "fuses-and-relays",
            "technical-specifications",
            "repair-times-variants",
            "known-fixes",
            "vin-plate-location",
            "warning-lamp-and-symbols",
            "wiring-diagrams",
            "diesel-exhaust-gas-aftertreatment",
        ];
        $vehicle_id =   AutoDataUtilsWeb::getCache($request, 'vehicle_id');
        $valid = true;

        if (
            $request->method() == "GET" &&
            $tailLink != '/'
            &&
            (
                (!str_contains($link, 'w2') &&
                    !str_contains($link, 'w1/vehicles') &&
                    $vehicle_id != null)
                ||
                str_contains($link, 'w1/vehicles/variants') && request("vehicle_id") != null
                ||
                str_contains($link, 'w1/diagram') && request("vehicle_id") != null
                ||
                in_array(request('route_name'), $w1)
            )
        ) {

            if (str_contains($ContentType, 'text/html')) {  //Tách svg ra
                $dom = HtmlDomParser::str_get_html($body);


                if ($dom  != null) {

                    $manufacturer_id =   AutoDataUtilsWeb::getCache($request, 'manufacturer_id');
                    $model_id =   AutoDataUtilsWeb::getCache($request, 'model_id');

                    $changeEngine = $dom->find('.change-engine', 0);
                    $technicalInfo = $dom->find('.technical-info', 0);
                    $estimateCal = $dom->find('.estimate-cal', 0);

                    if ($changeEngine != null &&  $manufacturer_id  != null &&   $model_id  != null) {
                        if (!str_contains($changeEngine->parent->innertext, $manufacturer_id) && !str_contains($changeEngine->parent->innertext, $model_id) && $number_try < $max_try) {
                            $valid = false;
                        }
                    } else 
                if ($technicalInfo != null &&  $manufacturer_id  != null &&   $model_id  != null) {
                        if (!str_contains($technicalInfo->innertext, $manufacturer_id) && !str_contains($technicalInfo->innertext, $model_id) && $number_try < $max_try) {
                            $valid = false;
                        }
                    } else 
                if ($estimateCal != null &&  $manufacturer_id  != null &&   $model_id  != null) {
                        if (!str_contains($estimateCal->innertext, $manufacturer_id) && !str_contains($estimateCal->innertext, $model_id) && $number_try < $max_try) {
                            $valid = false;
                        }
                    }
                }
            }
        }
        return $valid;
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        $time_start = microtime(true);
        try {

            $response  = AutoDataUtilsWeb::getResponse(
                'https://workshop.autodata-group.com/w2/api/user',
                "GET",
                [],
                $cookies_str,
                null,
                $proxy,
            );

            $content = ($response->getBody()->getContents());
            if (str_contains($content, "Login page")) {
                return [
                    'status' => false,
                    'mess' => "ERROR COOKIES"
                ];
            }

            $user_name = "";
            $user_id = "";
            $name = "";
            $full_name = "";
            $email = "";
            $account_number = "";
            $phone_number = "";

            //Lấy username
            $info_account_list = json_decode($content);
            $user_name = $info_account_list->username ?? "";
            $account_number = $info_account_list->garage->id ?? "";
            $full_name = $info_account_list->garage->name ?? "";
            $user_id = $info_account_list->garage->user_id ?? "";
            $email = $info_account_list->garage->email ?? "";
            $phone_number = $info_account_list->garage->address->phone_number ?? "";

            $time_end = microtime(true);

            return [
                'time_handle' => ($time_end - $time_start),
                'status' => true,
                'mess' => "",
                'name' => $name,
                'email' => $email,
                'user_id' => $user_id,
                'full_name' => $full_name,
                'user_name' => $user_name,
                'account_number' => $account_number,
                'phone_number' => $phone_number,

            ];
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage()
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
