<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use App\Helper\HtmlUtils;


class ETKAUtilsWeb
{

    /**
     * L<PERSON>y dữ liệu
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $proxy = null)
    {

        $body = [];

        if ($request != null) {
            $body = array_diff_key($request->all(), $request->query());
            $body =  Helper::removeBodyExcessive($body);
        }


        $tailLink = $_SERVER["REQUEST_URI"];

        // if (
        //     str_contains($tailLink, 'CreditCardUpdate') ||
        //     str_contains($tailLink, 'LogOff') ||
        //     str_contains($tailLink, 'Referral') ||
        //     str_contains($tailLink, 'ContactUs') ||
        //     str_contains($tailLink, 'VehicleSummary/Index') ||
        //     str_contains($tailLink, 'FactoryMaintenanceConfiguration/Index') ||
        //     str_contains($tailLink, 'ChangePassword/Index') ||
        //     str_contains($tailLink, 'ShopInfo/Index') ||
        //     str_contains($tailLink, 'HotlineCallRequest/Index') ||
        //     str_contains($tailLink, 'PostFix/Index') ||
        //     str_contains($tailLink, 'TermsOfUse/Index')

        // ) {
        //     $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();

        //     header("Location: https://$request->agency_sub." . $domainMain);
        //     exit;
        // }


        if (
            $tailLink == "/"
        ) {
            $domainMain = Helper::getDomainCurrent();
            header("Location: " . $domainMain . "/etka");
            exit;
        }



        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        $configAdmin =      ConfigAdmin::first();
        if ($proxy === null) {

            //    $proxy  = ProxyUtils::chooseProxyForAutodata();
            if ($configAdmin  != null && !empty($configAdmin->proxy_etka)) {
                $proxy = $configAdmin->proxy_etka;
            }
        }

        if ($cookies_str == null) {


            if ($configAdmin  != null && !empty($configAdmin->cookies_etka_str)) {
                $cookies_str = $configAdmin->cookies_etka_str;
            }
        }

        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $arr = CookiesUtils::cookieStrToArray($cookies_str);

        unset($headers_requests['user-agent']);

        unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        unset($headers_requests['connection']);
        unset($headers_requests['dnt']);


        unset($headers_requests['upgrade-insecure-requests']);

        $headerArr  =  $headers_requests;;

        $headerArr['Cookie'] = $cookies_str;
        $headerArr['User-Agent'] = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";


        $jar = new \GuzzleHttp\Cookie\CookieJar();

        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {

                $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name'     => $key,
                    'Value'    =>  $value,
                    'Domain'   => '.superetka.com',
                    'Path'     => '/',
                    // 'Max-Age'  => $item['name'],
                    //'Expires'  => $item['name'],
                    // 'Secure'   => $item['name'],
                    // 'Discard'  => $item['name'],
                    //  'HttpOnly' => $item['name'],
                ]));
            }
        }

        unset($headerArr['referer']);
        unset($headerArr['priority']);
        unset($headerArr['cf-ray']);
        unset($headerArr['x-forwarded-for']);
        unset($headerArr['postman-token']);
        unset($headerArr['cdn-loop']);


        $client = new \GuzzleHttp\Client(
            [
                'headers' => $headerArr,
                'proxy' => $proxy,
            ]
        );

        $response1 = $client->request(
            $method,
            $link,
            [
                //  'cookies' => $jar,
                'headers' => $headerArr,
                'form_params' => $body,
                'timeout' => 90, // Response timeout
                'connect_timeout' => 90, // Connection timeout,
                'on_stats' => (str_contains($tailLink, 'check_') || str_contains($tailLink, 'test_api')) ? null : function (\GuzzleHttp\TransferStats $stats) {

                    $url = $stats->getHandlerStats()['redirect_url'];
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $pathNoHTTP = str_replace("http://", "", $path);
                    $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

                    $url =  str_replace("https://superetka.com", $path, $url);
                    $url =  str_replace("http://superetka.com", $path, $url);
                    $url =  str_replace("superetka.com", $pathNoHTTP, $url);

                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);


                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink) {

                        $firtUrl = substr($url, 0, 3);
                        if ($firtUrl == "www") {
                            $url =  str_replace("www.", "https://www.",  $url);
                        }

                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );


        return  $response1;
    }

    static public function  remove_element_reponse($body, $contentType, $statusCode, $request)
    {
        $ContentType = $contentType;

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];


        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            header("Content-Type:application/pdf");
        }

        if (strlen($tailLink) > 3) {
            if (substr_compare($tailLink, '.svg', -4) === 0) {
                $ContentType  = 'image/svg+xml';
            }

            if (substr_compare($tailLink, '.png', -4) === 0) {
                $ContentType  = 'image/png';
            }
        }

        // //Save vao db
        // if ($request != null && $request->method() == "GET" && $statusCode == 200) {

        //     try {
        //         if (
        //             $request != null &&
        //             $body != null && $body != "" &&
        //             !str_contains($body, 'New user') && !str_contains($body, 'The account is blocked')
        //         ) {
        //             $tailLink = Helper::removeParamsFromUrl($tailLink, ['_']);
        //             $webClass = new WebClass();
        //             $webClass->tail_link = $tailLink;
        //             $webClass->service =  PlanUtils::ETKA;
        //             $webClass->method = $request->method();
        //             $webClass->content_type =  $contentType;
        //             $webClass->body =  $body;
        //             $webClass->status_code =  $statusCode;

        //             WebDataUtils::saveWebData($webClass);
        //         }
        //     } catch (Exception $e) {
        //     }
        // }

        if ($ContentType) {
            $isDocumentTypeHtml = false;
            if (substr($body, 0, strlen("<html><head>")) == "<html><head>") {
                $isDocumentTypeHtml  = true;
            }

            if ($isDocumentTypeHtml  && str_contains($ContentType, 'text/html')  && !$request->isMethod('post')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                    <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag("js", new Date());
                
                    gtag("config", "G-KXWTNY3G2J");
                    </script>', $body);

                $body =  HtmlUtils::addTagHtmlOnHead('<script src="/jssav/' . Helper::generateRandomString(10) . '.js"></script>', $body);
            }

            if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) {
                $body =  str_replace("https://superetka.com", $path, $body);
                $body =  str_replace("http://supere
                
                
                tka.com", $path, $body);
                $body =  str_replace("superetka.com", $pathNoHTTP, $body);
                $body =  str_replace('<div class="adInfoExit" id="logout" title="logout"></div>', "", $body);
                $body =  str_replace('<div class="adInfoHelp" title="help" onclick="showHelp()"></div>', "", $body);
                $body =  str_replace('<button  class="nav-link" onclick=\'showInfoLine()\'>INFOLINE</button>', "", $body);



                $body =  str_replace("bam.nr-data.net", "bam.nr-data3.net", $body);

                $body =  str_replace("You have reached your daily limit.", "Cannot find", $body);

                $cookies = $_COOKIE;
                $platform = $cookies['platform'] ?? request()->header('platform') ?? null;

                if ($request->isMethod('get') && str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);

                    if ($dom  != null) {

                         //Logout button
                        $adInfoFunctions = $dom->find('.adInfoFunctions', 0);

                        if ($adInfoFunctions != null) {
                            $adInfoFunctions->remove();
                        }

                        //Logout button
                        $login = $dom->find('.adInfoExit', 0);

                        if ($login != null) {
                            $login->remove();
                        }

                        //my shop
                        $help = $dom->find('.adInfoHelp', 0);

                        if ($help != null) {
                            $help->remove();
                        }

                        $notestab = $dom->find('#nav-notes-tab', 0);

                        if ($notestab != null) {
                            $notestab->remove();
                        }


                        $footer = $dom->find('.fixed-bottom', 0);

                        if ($footer != null) {
                            $footer->remove();
                        }

                        //replace html
                        $body2 =  $dom->innertext;
                        $body =  ($body2);
                    }


                    if ($request->user != null) {

                        $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_etka");
                        if ($remain_days > 0 && $remain_days < 10  && !$request->isMethod('post')) {
                            $appName = DomainConfigHelper::getConfig('appName');
                            $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script>
                             
                                 siiimpleToast.alert(" ETKA expiry date is only ' . $remain_days . ' days, remember to renew!", {
                                  position: "top|center",
                                  margin: 15,
                                  delay: 0,
                                  duration: 5000,
                                });
                                </script>', $body);
                        }
                    }
                }
            }
        }
        header('Content-type: ' .  $ContentType ?? "");
        echo   $body;
        die();
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        $content = "";
        try {
            $time_start = microtime(true);

            $response  = ETKAUtilsWeb::getResponse('https://superetka.com/etka/?lang=EN&marke=VW', "GET", $cookies_str, null);
            $type = $response->getHeader('Content-Type')[0] ?? "";

            $content = ($response->getBody()->getContents());

            if (str_contains($type, 'text/html')) {

                $pattern = '/(\S+@\S+\.\S+)(?=<br>).*?(\d{4}-\d{2}-\d{2})/';

                preg_match_all($pattern, $content, $matches);

                $email = null;
                $expiry = null;

                if (count($matches[0]) > 0) {
                    for ($i = 0; $i < count($matches[0]); $i++) {
                        $email =  $matches[1][$i];
                        $expiry =  $matches[2][$i];
                    }
                }

                if ($email == null ||  $expiry  == null) {
                    return [
                        'status' => false,
                        'mess' => 'ERROR COOKIES LOGIN',
                        'content' =>  $content
                    ];
                }


                $time_end = microtime(true);
                $dataFinal = [
                    'email' =>  $email,
                    'expiry' =>  $expiry,
                    'time_handle' => ($time_end - $time_start),
                    'status' => true,
                    'mess' => "",
                ];

                return $dataFinal;
            }
        } catch (Exception $e) {
            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage() . " khong xac dinh"
            ];
        }

        return [
            'status' => false,
            'mess' => "ERROR COOKIES",
            'content' =>  $content
        ];
    }
}
