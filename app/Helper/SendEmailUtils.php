<?php

namespace App\Helper;

use App\Jobs\PushNotificationUserJob;
use App\Jobs\PushNotificationUsersJob;
use App\Jobs\SendEmailSupportJob;
use App\Jobs\SendOneEmailJob;
use App\Jobs\UseFunctionWithQueueJob;
use App\Models\EmailSupport;
use App\Models\HistorySendEmailSuport;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Mail;

class SendEmailUtils
{
    const EMAIL_TEST = 0;
    const EMAIL_FOR_ALL = 1;
    const EMAIL_FOR_GROUP = 2;

    const EMAIL_AUTO_SUCCESS_RENEW_ALLDATA = 31;
    const EMAIL_AUTO_SUCCESS_RENEW_AUTODATA = 32;
    const EMAIL_AUTO_SUCCESS_RENEW_HAYNESPRO = 33;
    const EMAIL_AUTO_SUCCESS_RENEW_HAYNESPRO_TRUCK = 34;
    const EMAIL_AUTO_SUCCESS_RENEW_MITCHELL_PRODEMAND = 35;
    const EMAIL_AUTO_SUCCESS_RENEW_IDENTIFIX  = 36;
    const EMAIL_AUTO_SUCCESS_RENEW_PARTSLINK24 = 37;
    const EMAIL_AUTO_SUCCESS_RENEW_TECDOC = 38;
    const EMAIL_AUTO_SUCCESS_RENEW_FORD_PTS = 39;

    const EMAIL_AUTO_NOTI_RENEW_ALLDATA = 41;
    const EMAIL_AUTO_NOTI_RENEW_AUTODATA = 42;
    const EMAIL_AUTO_NOTI_RENEW_HAYNESPRO = 43;
    const EMAIL_AUTO_NOTI_RENEW_HAYNESPRO_TRUCK  = 44;
    const EMAIL_AUTO_NOTI_RENEW_MITCHELL_PRODEMAND = 45;
    const EMAIL_AUTO_NOTI_RENEW_IDENTIFIX = 46;
    const EMAIL_AUTO_NOTI_RENEW_PARTSLINK24 = 47;
    const EMAIL_AUTO_NOTI_RENEW_TECDOC = 48;
    const EMAIL_AUTO_NOTI_RENEW_FORD_PTS = 49;

    //Kiểu gửi:
    const SCHEDULE_TYPE_MANUAL = 0; //gửi thủ công
    const SCHEDULE_TYPE_ONE_TIME = 1; //gửi 1 lần
    const SCHEDULE_TYPE_CONDITION = 2; //gửi tự động theo điều kiện

    //Điều kiện gửi
    const CONDITION_TIME_SEND_REMAMING_3_DAY_ALLDATA = 11; //tròn 3 ngày hết alldata
    const CONDITION_TIME_SEND_REMAMING_3_DAY_AUTODATA = 12; //tròn 3 ngày hết autodata
    const CONDITION_TIME_SEND_REMAMING_3_DAY_HAYNESPRO = 13; //tròn 3 ngày hết haynespro
    const CONDITION_TIME_SEND_REMAMING_3_DAY_HAYNESPRO_TRUCK = 14;
    const CONDITION_TIME_SEND_REMAMING_3_DAY_MITCHELL_PRODEMAND = 15;
    const CONDITION_TIME_SEND_REMAMING_3_DAY_IDENTIFIX = 16;
    const CONDITION_TIME_SEND_REMAMING_3_DAY_PARTSLINK24 = 17;
    const CONDITION_TIME_SEND_REMAMING_3_DAY_KDSGDS = 18;
    const CONDITION_TIME_SEND_REMAMING_3_DAY_ETKA = 19;
    const CONDITION_TIME_SEND_REMAMING_3_DAY_TECDOC = 20;
    const CONDITION_TIME_SEND_REMAMING_3_DAY_FORD_PTS = 21;

    //gửi tới
    const SEND_TO_NO_BODY =  0;
    const SEND_TO_GROUP = 1;
    const SEND_TO_ALL = 2;
    const SEND_TO_USER_ACTION = 3;

    //gửi tới
    const SEND_TO_CONDITION_NO =  0;
    const SEND_TO_CONDITION_RENEW_SUCCESS_ALLDATA = 21;
    const SEND_TO_CONDITION_RENEW_SUCCESS_AUTODATA = 22;
    const SEND_TO_CONDITION_RENEW_SUCCESS_HAYNESPRO = 23;
    const SEND_TO_CONDITION_RENEW_SUCCESS_HAYNESPRO_TRUCK = 24;
    const SEND_TO_CONDITION_RENEW_SUCCESS_MITCHELL_PRODEMAND = 25;
    const SEND_TO_CONDITION_RENEW_SUCCESS_IDENTIFIX = 26;
    const SEND_TO_CONDITION_RENEW_SUCCESS_PARTSLINK24 = 27;
    const SEND_TO_CONDITION_RENEW_SUCCESS_KDSGDS = 28;
    const SEND_TO_CONDITION_RENEW_SUCCESS_ETKA = 29;
    const SEND_TO_CONDITION_RENEW_SUCCESS_TECDOC = 30;
    const SEND_TO_CONDITION_RENEW_SUCCESS_FORD_PTS = 31;
    const SEND_TO_CONDITION_RENEW_SUCCESS_TOYOTA_TIS = 32;

    const SEND_TO_CONDITION_REGISTER_SUCCESS = 31;

    static function send($list_email,  $email_id)
    {
        //  SendEmailSupportJob::dispatch($list_email, $email_id);

        $sent_email = array();
        if (is_array($list_email) && count($list_email) > 0) {

            $checkEmailExists = EmailSupport::where(
                'id',
                $email_id
            )->first();

            $title = $checkEmailExists->title;
            $description = $checkEmailExists->description;
            $content = $checkEmailExists->content;

            $arr_list_rep =  [
                "{{{remain_days_expiry_alldata}}}",
                "{{{remain_days_expiry_autodata}}}",
                "{{{remain_days_expiry_haynespro}}}",
                "{{{remain_days_expiry_haynespro_truck}}}",
                "{{{remain_days_expiry_mitchell_prodemand}}}",
                "{{{remain_days_expiry_identifix}}}",
                "{{{remain_days_expiry_partslink24}}}",
                "{{{remain_days_expiry_kdsgds}}}",
                "{{{remain_days_expiry_etka}}}",
                "{{{remain_days_expiry_tecdoc}}}",
                "{{{remain_days_expiry_ford_pts}}}",
                "{{{remain_days_expiry_toyota_tis}}}",
                "{{{domain_withou_sub}}}",
                "{{{user_name}}}",
                "{{{user_email}}}",
                "{{{contact_link}}}",
                "{{{user_expiry_alldata}}}",
                "{{{user_expiry_autodata}}}",
                "{{{user_expiry_haynespro}}}",
                "{{{user_expiry_haynespro_truck}}}",
                "{{{user_expiry_mitchell_prodemand}}}",
                "{{{user_expiry_identifix}}}",
                "{{{user_expiry_partslink24}}}",
                "{{{user_expiry_kdsgds}}}",
                "{{{user_expiry_etka}}}",
                "{{{user_expiry_tecdoc}}}",
                "{{{user_expiry_ford_pts}}}",
                "{{{user_expiry_toyota_tis}}}",
            ];

            $has_contai_rep = false;
            foreach ($arr_list_rep  as $rep) {
                if (str_contains($title,  $rep)) {
                    $has_contai_rep = true;
                }
                if (str_contains($description,  $rep)) {
                    $has_contai_rep = true;
                }
                if (str_contains($content,  $rep)) {
                    $has_contai_rep = true;
                }
            }

            if ($has_contai_rep == false) {
                $user_ids = User::whereIn('email',  $list_email)
                    ->pluck('id')
                    ->toArray();

                PushNotificationUsersJob::dispatch(
                    $user_ids,
                    $title,
                    $description,
                );
            } else {
                foreach ($list_email as $email) {


                    if (filter_var($email, FILTER_VALIDATE_EMAIL)) {

                        array_push($sent_email, $email);

                        $userExists = User::where('email', $email)->first();

                        $remain_days_expiry_alldata = RenewUtils::get_days_remain_expiry($userExists, "expiry_alldata");
                        $remain_days_expiry_autodata = RenewUtils::get_days_remain_expiry($userExists, "expiry_autodata");
                        $remain_days_expiry_haynespro = RenewUtils::get_days_remain_expiry($userExists, "expiry_haynespro");
                        $remain_days_expiry_haynespro_truck = RenewUtils::get_days_remain_expiry($userExists, "expiry_haynespro_truck");
                        $remain_days_expiry_mitchell_prodemand = RenewUtils::get_days_remain_expiry($userExists, "expiry_mitchell_prodemand");
                        $remain_days_expiry_identifix = RenewUtils::get_days_remain_expiry($userExists, "expiry_identifix");
                        $remain_days_expiry_partslink24 = RenewUtils::get_days_remain_expiry($userExists, "expiry_partslink24");
                        $remain_days_expiry_kdsgds = RenewUtils::get_days_remain_expiry($userExists, "expiry_kdsgds");
                        $remain_days_expiry_etka = RenewUtils::get_days_remain_expiry($userExists, "expiry_etka");
                        $remain_days_expiry_tecdoc = RenewUtils::get_days_remain_expiry($userExists, "expiry_tecdoc");
                        $remain_days_expiry_ford_pts = RenewUtils::get_days_remain_expiry($userExists, "expiry_ford_pts");
                        $remain_days_expiry_toyota_tis = RenewUtils::get_days_remain_expiry($userExists, "expiry_toyota_tis");

                        $user_name = $userExists->name ?? "";
                        $user_email = $email ?? "";
                        $contact_link = UserUtils::getContactLink($userExists->id);
                        $user_expiry_alldata = $userExists->expiry_alldata ?? "Unregistered";
                        $user_expiry_autodata = $userExists->expiry_autodata ?? "Unregistered";
                        $user_expiry_haynespro = $userExists->expiry_haynespro ?? "Unregistered";
                        $user_expiry_haynespro_truck = $userExists->expiry_haynespro_truck ?? "Unregistered";
                        $user_expiry_mitchell_prodemand = $userExists->expiry_mitchell_prodemand ?? "Unregistered";
                        $user_expiry_identifix = $userExists->expiry_identifix ?? "Unregistered";
                        $user_expiry_partslink24 = $userExists->expiry_partslink24 ?? "Unregistered";
                        $user_expiry_kdsgds = $userExists->expiry_kdsgds ?? "Unregistered";
                        $user_expiry_etka = $userExists->expiry_etka ?? "Unregistered";
                        $user_expiry_tecdoc = $userExists->expiry_tecdoc ?? "Unregistered";
                        $user_expiry_ford_pts = $userExists->expiry_ford_pts ?? "Unregistered";
                        $user_expiry_toyota_tis = $userExists->expiry_toyota_tis ?? "Unregistered";

                        $arr_text = str_replace(
                            $arr_list_rep,
                            [
                                $remain_days_expiry_alldata ?? "",
                                $remain_days_expiry_autodata ?? "",
                                $remain_days_expiry_haynespro ?? "",
                                $remain_days_expiry_haynespro_truck ?? "",
                                $remain_days_expiry_mitchell_prodemand ?? "",
                                $remain_days_expiry_identifix ?? "",
                                $remain_days_expiry_partslink24 ?? "",
                                $remain_days_expiry_kdsgds,
                                $remain_days_expiry_etka,
                                $remain_days_expiry_tecdoc,
                                $remain_days_expiry_ford_pts,
                                $remain_days_expiry_toyota_tis,
                                Helper::getDomainCurrentWithoutSubAndNoHttp() ?? "",
                                $user_name ?? "",
                                $user_email ?? "",
                                $contact_link ?? "https://wa.me/+84945238603",
                                $user_expiry_alldata ?? "",
                                $user_expiry_autodata ?? "",
                                $user_expiry_haynespro ?? "",
                                $user_expiry_haynespro_truck ?? "",
                                $user_expiry_mitchell_prodemand ?? "",
                                $user_expiry_identifix ?? "",
                                $user_expiry_partslink24 ?? "",
                                $user_expiry_kdsgds ?? "",
                                $user_expiry_etka ?? "",
                                $user_expiry_tecdoc ?? "",
                                $user_expiry_ford_pts ?? "",
                                $user_expiry_toyota_tis ?? "",
                            ],
                            [$title,  $description,  $content]
                        );


                        [$title,  $description,  $content] =   $arr_text;

                        // if ($email != "<EMAIL>"  && $email != "<EMAIL>" && $email != "<EMAIL>") {
                        //     continue;
                        // }

                        //Kiểm tra cổng gửi
                        if ($checkEmailExists->send_to_gate == 0) { //email
                            SendOneEmailJob::dispatch(
                                $email,
                                $title,
                                $description,
                                $content
                            );
                        }
                        if ($checkEmailExists->send_to_gate == 1) { //notification
                            PushNotificationUserJob::dispatch(
                                $userExists->id,
                                $title,
                                $description,
                            );
                        }
                        if ($checkEmailExists->send_to_gate == 2) { //both

                            SendOneEmailJob::dispatch(
                                $email,
                                $title,
                                $description,
                                $content
                            );
                            PushNotificationUserJob::dispatch(
                                $userExists->id,
                                $title,
                                $description,
                            );
                        }

                        // Mail::to([$email])
                        //     ->send(new \App\Mail\SendMailSupport(
                        //         $title,
                        //         $description,
                        //         $content
                        //     ));
                    }
                }
            }

            HistorySendEmailSuport::create(
                [
                    "email_support_id" =>  $email_id,
                    "title" => $checkEmailExists->title,
                    "description" => $checkEmailExists->description,
                    "content" => count($list_email)  > 0 ? $checkEmailExists->content : $content,
                    "total_user" => count($list_email),
                    "total_user_sent" => count($sent_email),
                    "list_email_json" => json_encode($sent_email),
                    "send_to_gate" => $checkEmailExists->send_to_gate,
                    "schedule_type" => $checkEmailExists->schedule_type,
                    "send_time" => $checkEmailExists->send_time,
                    "condition_time_send" => $checkEmailExists->condition_time_send,
                    "group_user_id" => $checkEmailExists->group_user_id,
                    "group_user_name" => $checkEmailExists->group_user_name,
                    "send_to" => $checkEmailExists->send_to,
                    "send_to_condition" => $checkEmailExists->send_to_condition,
                ]
            );
        }
        //ko dc echo
        // echo "sent " . count($list_email) . " email";
        return  $sent_email;
    }

    static function sendEmailRegisterSuccess($email)
    {
        //tìm email gửi đăng ký
        $allEmails = EmailSupport::where('running', true)->where('send_to', SendEmailUtils::SEND_TO_USER_ACTION)
            ->where('send_to_condition', SendEmailUtils::SEND_TO_CONDITION_REGISTER_SUCCESS)
            ->get();
        foreach ($allEmails  as $emailSupport) {
            SendEmailUtils::send([$email], $emailSupport->id);
        }
    }

    static function sendEmailRenewSuccess($email, $service)
    {
        $send_to_condition = "x";
        if ($service == PlanUtils::ALLDATA) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_ALLDATA;
        }
        if ($service == PlanUtils::AUTODATA) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_AUTODATA;
        }
        if ($service == PlanUtils::HAYNESPRO) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_HAYNESPRO;
        }
        if ($service == PlanUtils::HAYNESPRO_TRUCK) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_HAYNESPRO_TRUCK;
        }
        if ($service == PlanUtils::MITCHELL_PRODEMAND) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_MITCHELL_PRODEMAND;
        }
        if ($service == PlanUtils::IDENTIFIX) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_IDENTIFIX;
        }
        if ($service == PlanUtils::PARTSLINK24) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_PARTSLINK24;
        }
        if ($service == PlanUtils::KDSGDS) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_KDSGDS;
        }
        if ($service == PlanUtils::ETKA) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_ETKA;
        }
        if ($service == PlanUtils::TECDOC) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_TECDOC;
        }
        if ($service == PlanUtils::FORD_PTS) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_FORD_PTS;
        }
        if ($service == PlanUtils::TOYOTA_TIS) {
            $send_to_condition = SendEmailUtils::SEND_TO_CONDITION_RENEW_SUCCESS_TOYOTA_TIS;
        }

        //tìm email gửi gia hạn
        $allEmails = EmailSupport::where('running', true)->where('send_to', SendEmailUtils::SEND_TO_USER_ACTION)
            ->where('send_to_condition', $send_to_condition)
            ->get();
        foreach ($allEmails  as $emailSupport) {
            SendEmailUtils::send([$email], $emailSupport->id);
        }
    }

    static function sendEmailAutoOneTime()
    {

        $carbon = Carbon::now();
        $date1 = $carbon->format('Y-m-d H:i:00');
        $date2 = $carbon->format('Y-m-d H:i:59');


        $allEmails = EmailSupport::where('running', true)->where('schedule_type', SendEmailUtils::SCHEDULE_TYPE_ONE_TIME)
            ->where([
                ['send_time', '>=',  $date1],
                ['send_time', '<', $date2]
            ])
            ->get();

        foreach ($allEmails  as $emailSupport) {
            if ($emailSupport->send_to == SendEmailUtils::SEND_TO_ALL) {
                $emails = User::pluck('email')->toArray();
                SendEmailUtils::send($emails, $emailSupport->id);
            }
            if ($emailSupport->send_to == SendEmailUtils::SEND_TO_GROUP) {
                $emails = GroupUserUtils::getEmailsWithMultiGroupId([$emailSupport->group_user_id]);
                SendEmailUtils::send($emails, $emailSupport->id);
            }
        }
    }

    static function sendEmailWithService($service)
    {
        $condition_time_send = "x";
        if ($service == "expiry_alldata") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_ALLDATA;
        }
        if ($service == "expiry_autodata") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_AUTODATA;
        }
        if ($service == "expiry_haynespro") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_HAYNESPRO;
        }
        if ($service == "expiry_haynespro_truck") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_HAYNESPRO_TRUCK;
        }
        if ($service == "expiry_mitchell_prodemand") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_MITCHELL_PRODEMAND;
        }
        if ($service == "expiry_identifix") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_IDENTIFIX;
        }
        if ($service == "expiry_partslink24") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_PARTSLINK24;
        }
        if ($service == "expiry_kdsgds") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_KDSGDS;
        }
        if ($service == "expiry_etka") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_ETKA;
        }
        if ($service == "expiry_tecdoc") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_TECDOC;
        }
        if ($service == "expiry_ford_pts") {
            $condition_time_send =  SendEmailUtils::CONDITION_TIME_SEND_REMAMING_3_DAY_FORD_PTS;
        }


        //Config
        $carbon = Carbon::now();
        $carbon->addMinutes(4320);
        $date1 = $carbon->format('Y-m-d H:i:00');
        $date2 = $carbon->format('Y-m-d H:i:59');


        $emails = User::where([
            [$service, '>=',  $date1],
            [$service, '<', $date2]
        ])->pluck('email')->toArray();

        $allEmails = EmailSupport::where('running', true)->where('send_to', SendEmailUtils::SEND_TO_ALL)
            ->where('condition_time_send', $condition_time_send)
            ->get();
        // dd($emails , $date1 ,  $date2, count(   $allEmails ));
        foreach ($allEmails  as $emailSupport) {
            SendEmailUtils::send($emails, $emailSupport->id);
        }
    }

    static function sendEmailReaming3Day()
    {
        //expiry_alldata expiry_autodata expiry_haynespro
        SendEmailUtils::sendEmailWithService('expiry_alldata');
        SendEmailUtils::sendEmailWithService('expiry_autodata');
        SendEmailUtils::sendEmailWithService('expiry_haynespro');
        SendEmailUtils::sendEmailWithService('expiry_haynespro_truck');
        SendEmailUtils::sendEmailWithService('expiry_mitchell_prodemand');
        SendEmailUtils::sendEmailWithService('expiry_identifix');
        SendEmailUtils::sendEmailWithService('expiry_partslink24');
        SendEmailUtils::sendEmailWithService('expiry_kdsgds');
        SendEmailUtils::sendEmailWithService('expiry_etka');
        SendEmailUtils::sendEmailWithService('expiry_tecdoc');
        SendEmailUtils::sendEmailWithService('expiry_ford_pts');
        SendEmailUtils::sendEmailWithService('expiry_toyota_tis');
    }
}
