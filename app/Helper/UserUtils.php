<?php

namespace App\Helper;

use Illuminate\Support\Facades\DB;

class UserUtils
{
    //0 chưa gửi thông tin xác thực, 1 hủy xác thực, 2 đã xác thực
    const STATUS_WAITING  = 0;
    const STATUS_CANEL  = 1;
    const STATUS_AUTHENTICATED  = 2;

    static function getContactLinkWithAgency($agency, $getPanelPhone = true)
    {
        if($agency == null) return null;
        $contactLink  = null;
        $agency_web_theme = DB::table('agency_web_themes')->where('agency_id',  $agency->id)->first();

        if ($agency_web_theme != null && !empty($agency_web_theme->contact_link)) {
            $contactLink = $agency_web_theme->contact_link;
            $contactLink = str_replace("\u{202A}", '', $contactLink);

        } else 
        if ($getPanelPhone  == true && $agency != null && StringUtils::extractPhoneNumber($agency->name) != null) {
            $contactLink = "https://wa.me/" . StringUtils::extractPhoneNumber($agency->name);
        }
        return   $contactLink;
    }

    static function getContactLink($user_id, $getPanelPhone = true)
    {
        $contactLink  = null;
        $user = DB::table('users')->select('of_agency_id')->where('id',  $user_id)->first();
        if ($user == null)  return   $contactLink;

        $agency = DB::table('agencies')->where('id', $user->of_agency_id)->first();
        if ($agency == null)  return   $contactLink;

        $agency_web_theme = DB::table('agency_web_themes')->where('agency_id',  $agency->id)->first();

        if ($agency_web_theme != null && !empty($agency_web_theme->contact_link)) {
            $contactLink = $agency_web_theme->contact_link;
        } else 
        if ($getPanelPhone  == true && $agency != null && StringUtils::extractPhoneNumber($agency->name) != null) {
            $contactLink = "https://wa.me/" . StringUtils::extractPhoneNumber($agency->name);
        }
        return   $contactLink;
    }
}
