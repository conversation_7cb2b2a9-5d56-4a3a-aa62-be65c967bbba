<?php

namespace App\Helper;

use Illuminate\Support\Facades\Cache;

class PlanUtils
{

    //Device
    const MORE_DEVICE = "MORE_DEVICE";
    //Program

    const ALLDATAVSAUTODATAVSHAYNESPRO  = "ALLDATAVSAUTODATAVSHAYNESPRO";
    const ALLDATAVSAUTODATA  = "ALLDATAVSAUTODATA";
    const AUTODATA  = "AUTODATA";
    const AUTODATA_ITALY  = "AUTODATA_ITALY";
    const ALLDATA  = "ALLDATA";
    const ALLDATA_US  = "ALLDATA_US";
    const AUTOATAW2  = "AUTODATAW2";
    const HAYNESPRO  = "HAYNESPRO";
    const HAYNESPRO_TRUCK  = "HAYNESPRO_TRUCK";
    const MITCHELL_REPAIR_CENTER  = "MITCHELL_REPAIR_CENTER";
    const MITCHELL_PRODEMAND  = "MITCHELL_PRODEMAND";
    const TECDOC  = "TECDOC";
    const FORD_PTS  = "FORD_PTS";
    const IDENTIFIX  = "IDENTIFIX";
    const ETKA  = "ETKA";
    const PARTSLINK24  = "PARTSLINK24";
    const KDSGDS  = "KDSGDS";
    const ATSG  = "ATSG";
    const TOYOTA_TIS  = "TOYOTA_TIS";


    static function getColumnExpiryWithPlanName($type)
    {
        if ($type == PlanUtils::ALLDATA) {
            return "expiry_alldata";
        }
        if ($type == PlanUtils::AUTODATA) {
            return "expiry_autodata";
        }
        if ($type == PlanUtils::AUTODATA_ITALY) {
            return "expiry_autodata_italy";
        }
        if ($type == PlanUtils::HAYNESPRO) {
            return "expiry_haynespro";
        }
        if ($type == PlanUtils::HAYNESPRO_TRUCK) {
            return "expiry_haynespro_truck";
        }
        if ($type == PlanUtils::PARTSLINK24) {
            return "expiry_partslink24";
        }
        if ($type == PlanUtils::IDENTIFIX) {
            return "expiry_identifix";
        }
        if ($type == PlanUtils::MITCHELL_REPAIR_CENTER) {
            return "expiry_mitchell_repair_center";
        }
        if ($type == PlanUtils::MITCHELL_PRODEMAND) {
            return "expiry_mitchell_prodemand";
        }
        if ($type == PlanUtils::KDSGDS) {
            return "expiry_kdsgds";
        }
        if ($type == PlanUtils::ETKA) {
            return "expiry_etka";
        }
        if ($type == PlanUtils::TECDOC) {
            return "expiry_tecdoc";
        }
        if ($type == PlanUtils::FORD_PTS) {
            return "expiry_ford_pts";
        }
        if ($type == PlanUtils::TOYOTA_TIS) {
            return "expiry_toyota_tis";
        }

        return "";
    }

    static function getNameService($type)
    {
        if ($type == PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO) {
            return "ALLData VS AutoData VS Haynes";
        }
        if ($type == PlanUtils::ALLDATAVSAUTODATA) {
            return "Welcome to OBD DATA";
        }
        if ($type == PlanUtils::ALLDATA) {
            return "ALLData";
        }

        if ($type == PlanUtils::HAYNESPRO) {
            return "Haynes";
        }
        if ($type == PlanUtils::HAYNESPRO_TRUCK) {
            return "Haynes Truck";
        }

        if ($type == PlanUtils::PARTSLINK24) {
            return "Partslink24";
        }
        if ($type == PlanUtils::IDENTIFIX) {
            return "Identifix";
        }
        if ($type == PlanUtils::MITCHELL_REPAIR_CENTER) {
            return "Mitchell repair center";
        }
        if ($type == PlanUtils::MITCHELL_PRODEMAND) {
            return "Mitchell prodemand";
        }
        if ($type == PlanUtils::AUTODATA) {
            return "AutoData";
        }
        if ($type == PlanUtils::AUTODATA_ITALY) {
            return "AutoData Italy";
        }
        if ($type == PlanUtils::KDSGDS) {
            return "Kds Gds";
        }
        if ($type == PlanUtils::ETKA) {
            return "ETKA";
        }
        if ($type == PlanUtils::TECDOC) {
            return "TecDoc";
        }
        if ($type == PlanUtils::FORD_PTS) {
            return "FORD TIS";
        }
        if ($type == PlanUtils::TOYOTA_TIS) {
            return "TOYOTA TIS";
        }
        return "";
    }



    static  public function setCacheStatus($service, $status = true)
    {

        $key  = 'server_status' . $service;
        $keyTime  = 'server_status_last_updated' . $service;
        $status =  filter_var($status, FILTER_VALIDATE_BOOLEAN);
        $cachedStatus = Cache::get($key, true);
        $lastUpdated = Cache::get($keyTime);

        $time = 15;
        if ($service == PlanUtils::ALLDATA) {
            $time = 2;
        }

        if ($status == false && now()->diffInMinutes($lastUpdated) >= $time) {
            Cache::put($key, $status);
            Cache::put($keyTime, now());
        }

        if ($status == true) {
            Cache::put($key, $status);
            Cache::put($keyTime, now());
        }

        if ($lastUpdated  == null) {
            Cache::put($keyTime, now());
        }
    }

    static public function getCacheStatus($service)
    {
        $key  = 'server_status' . $service;
        $cachedStatus = Cache::get($key, true);
        return  $cachedStatus;
    }
}
