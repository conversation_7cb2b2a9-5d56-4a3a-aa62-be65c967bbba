<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Jobs\PushNotificationAdminJob;
use App\Jobs\SendPostRequest;
use App\Models\WebData;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Cache;

class WebDataUtils
{

    const WAY = 2;
    const DOMAIN = "http://webdata.drcar.la";

    static function  checkDateWithinRange($dateToCheck, $daysBefore)
    {
        if (!$dateToCheck) return false;
        $date = Carbon::parse($dateToCheck);
        $limitDate = Carbon::now()->subDays($daysBefore);
        return $date->greaterThanOrEqualTo($limitDate) && $date->lessThanOrEqualTo(Carbon::now()->addDays(2));
    }


    static function sendNoti($title, $content)
    {
        $lockTime = 15;
        $lockKey = 'lockSendNoti';

        // Kiểm tra xem có khóa không
        if (Cache::has($lockKey)) {
            return;
        }

        Cache::put($lockKey, true, 15);
        PushNotificationAdminJob::dispatch(
            $title,
            $content
        );
    }
    static function saveWebData(WebClass $webClass)
    {
        $tailLink = $webClass->tail_link;
        $language = $webClass->language;
        $service = $webClass->service;
        $method = $webClass->method;
        $content = $webClass->content;
        $body = $webClass->body;
        $content_type = $webClass->content_type;
        $updated_at = $webClass->updated_at;
        $status_code = $webClass->status_code;

        if (WebDataUtils::WAY == 0) {
            $webDataExists = WebData::where('tail_link', $tailLink)
                ->where('service', $service)
                ->where('method',  $method)
                ->where('content_type',  $content_type)
                ->orderBy('id', 'desc')
                ->first();

            $dataSave = [
                "tail_link" => $tailLink,
                "content" =>  base64_encode($body),
                "content_type" => $content_type,
                "service" =>  $service,
                "method" => $method,
                "status_code" => $status_code,
                "updated_at" => now()
            ];
            if ($webDataExists == null) {
                WebData::create(
                    $dataSave
                );
            } else {
                $webDataExists->update(
                    $dataSave
                );
            }
        }

        //Lưu server 2


        if (WebDataUtils::WAY == 1) {
            $time_end = microtime(true);
            $client = new Client(
                [
                    'timeout' => 120,
                    'connect_timeout' =>  120,
                ]
            );
            $time_start = microtime(true);
            try {
                $detailData = "service $service tailLink $tailLink method $method content_type $content_type";
                $response = $client->post(WebDataUtils::DOMAIN . '/api/webdata/create', [
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ],
                    'json' => [
                        "tail_link" => $tailLink,
                        "content" =>  base64_encode($body),
                        "method" => $method,
                        "content_type" => $content_type,
                        "status_code" => $status_code,
                        "service" =>  $service,
                    ]
                ]);
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
            } catch (ClientException $e) {
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
                if ($e->getResponse()->getStatusCode() != 200 && $e->getResponse()->getStatusCode() != 400) {

                    WebDataUtils::sendNoti(
                        "Can't save WebData " . $detailData . " time handle " .  $time_handle,
                        "Error 500  " . $e->getMessage()
                    );
                }
            } catch (GuzzleException $e) {
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
                WebDataUtils::sendNoti(
                    "Can't save WebData " . $detailData . " time handle " .  $time_handle,
                    "Error request  " . $e->getMessage()
                );
            } catch (Exception $e) {
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
                WebDataUtils::sendNoti(
                    "Can't save WebData " . $detailData . " time handle " .  $time_handle,
                    "Php Exception  Line: " . $e->getLine() . " " . $e->getMessage()
                );
            }
        }

        //Lưu server NAS
        if (WebDataUtils::WAY == 2) {
            $detailData = "service $webClass->service tailLink $webClass->tail_link method $webClass->method content_type $webClass->content_type";
            Cache::forget($detailData);
            
            $cacheKey = "webdata_{$tailLink}_{$method}_{$content_type}_{$service}";
            Cache::forget($cacheKey);
                 
            Cache::forget($tailLink);
            $domain_web_data = DomainConfigHelper::getConfig('domain_web_data');
            dispatch(new SendPostRequest("http://$domain_web_data/WebDataHandle/create-data.php",  [
                "tail_link" => $tailLink,
                "content" =>  base64_encode($body),
                "method" => $method,
                "content_type" => $content_type,
                "status_code" => $status_code,
                "service" =>  $service,
            ]))->onQueue('post_queue');
        }
    }

    static function getWebData(WebClass $webClass)
    {


        if (WebDataUtils::WAY == 0) {
            $now = Carbon::now();

            $webDataExists = WebData::where('updated_at', '>=', $now->subHours($webClass->hours_ago))
                ->where('tail_link', $webClass->tail_link)
                ->where('service', $webClass->service)
                ->where('method',  $webClass->method)
                ->first();


            if ($webDataExists  != null && $webDataExists->content != null) {
                $webClass->content = $webDataExists->content;
                $webClass->content_type = $webDataExists->content_type;
                $webClass->status_code = $webDataExists->status_code;


                return $webClass;
            }
        }

        if (WebDataUtils::WAY == 1) {

            $client = new Client(
                [
                    'timeout' => 120,
                    'connect_timeout' =>  120,
                ]
            );

            try {
                $time_start = microtime(true);
                $detailData = "service $webClass->service tailLink $webClass->tail_link method $webClass->method content_type $webClass->content_type";


                $tail_link  = $webClass->tail_link;
                $method = $webClass->method;
                $content_type = $webClass->content_type;
                $service = $webClass->service;
                $hours_ago = $webClass->hours_ago;

                $cacheKey = "webdata_{$tail_link}_{$method}_{$content_type}_{$service}";
                $cacheDuration = 60 * 60 * 1;
               

                $jsonBody = Cache::remember($cacheKey, $cacheDuration, function () use ($tail_link, $method, $content_type, $service, $hours_ago, $client) {
                    $response = $client->post(WebDataUtils::DOMAIN . '/api/webdata/get-one', [
                        'headers' => [
                            'Content-Type' => 'application/json'
                        ],

                        'json' => [
                            "tail_link" => $tail_link,
                            'method' => $method,
                            'content_type' => $content_type,
                            'service' => $service,
                            'hours_ago' => $hours_ago
                        ]
                    ]);
                    $body = $response->getBody()->getContents();

                    $jsonBody = json_decode($body, true);
                    return $jsonBody;
                });



                if ($jsonBody['success'] == true) {
                    $webClass->content = $jsonBody['data']['content'];
                    $webClass->content_type = $jsonBody['data']['content_type'];
                    $webClass->status_code = $jsonBody['data']['status_code'] ?? 200;
                    return $webClass;
                } else {
                    Cache::forget($cacheKey);
                }
            } catch (ClientException $e) {
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
                if ($e->getResponse()->getStatusCode() != 200 && $e->getResponse()->getStatusCode() != 400) {

                    WebDataUtils::sendNoti(
                        "Can't get WebData " . $detailData . " time handle " .  $time_handle,
                        "Error 500  " . $e->getMessage()
                    );
                }
            } catch (GuzzleException $e) {
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
                WebDataUtils::sendNoti(
                    "Can't get WebData " . $detailData . " time handle " .  $time_handle,
                    "Error request  " . $e->getMessage()
                );
            } catch (Exception $e) {
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
                WebDataUtils::sendNoti(
                    "Can't get WebData " . $detailData . " time handle " .  $time_handle,
                    "Php Exception  " . $e->getMessage()
                );
            }
        }

        if (WebDataUtils::WAY == 2) {


            try {
                $time_start = microtime(true);
                $detailData = "service $webClass->service tailLink $webClass->tail_link method $webClass->method content_type $webClass->content_type";


                $tail_link  = $webClass->tail_link;
                $method = $webClass->method;
                $content_type = $webClass->content_type;
                $service = $webClass->service;
                $hours_ago = $webClass->hours_ago;

                $cacheKey = "webdata_{$tail_link}_{$method}_{$content_type}_{$service}";
                $cacheDuration = 60 * 30 * 1;
                // $cacheDuration = 1;

                $jsonBody = Cache::remember($cacheKey, $cacheDuration, function () use ($tail_link, $method, $content_type, $service, $hours_ago) {
                    
                    $client = new Client(
                        [
                            'timeout' => 120,
                            'connect_timeout' =>  20,
                        ]
                    );
                    $domain_web_data = DomainConfigHelper::getConfig('domain_web_data');
                    $response = $client->post("http://$domain_web_data/WebDataHandle/post-get-data.php", [
                        'headers' => [
                            // 'Content-Type' => 'application/json'
                        ],

                        'form_params' => [
                            "tail_link" => $tail_link,
                            'method' => $method,
                            'content_type' => $content_type,
                            'service' => $service,
                            'hours_ago' => $hours_ago
                        ]
                    ]);
                    $body = $response->getBody()->getContents();

                    $jsonBody = json_decode($body, true);
                    return $jsonBody;
                });



                if ($jsonBody['success'] == true) {
                    $webClass->content = $jsonBody['data']['content'];
                    $webClass->content_type = $jsonBody['data']['content_type'];
                    $webClass->status_code = $jsonBody['data']['status_code'] ?? 200;
                    $webClass->updated_at = $jsonBody['data']['updated_at'] ?? null;

                    return $webClass;
                } else {
                    Cache::forget($cacheKey);
                }
            } catch (ClientException $e) {
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
                if ($e->getResponse()->getStatusCode() != 200 && $e->getResponse()->getStatusCode() != 400) {

                    WebDataUtils::sendNoti(
                        "Can't get WebData " . $detailData . " time handle " .  $time_handle,
                        "Error 500  " . $e->getMessage()
                    );
                }
            } catch (GuzzleException $e) {
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
                WebDataUtils::sendNoti(
                    "Can't get WebData " . $detailData . " time handle " .  $time_handle,
                    "Error request  " . $e->getMessage()
                );
            } catch (Exception $e) {
                $time_end = microtime(true);
                $time_handle = ($time_end - $time_start);
                WebDataUtils::sendNoti(
                    "Can't get WebData " . $detailData . " time handle " .  $time_handle,
                    "Php Exception  Line: " . $e->getLine() . " " . $e->getMessage()
                );
            }
        }
        return null;
    }
}
