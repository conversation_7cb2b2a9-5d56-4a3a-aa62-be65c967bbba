<?php

namespace App\Helper;

use Carbon\Carbon;

class TecDocUtils
{
    const LNG_ID  = 4;
    const COUNTRY_ID = 62;
    const PASSENGER_CAR = 'PC';

    public static function formatDate($date)
    {
        if (!$date) {
            return '';
        }
        return Carbon::parse($date)->format('m.Y');
    }

    public static function formatPerformance(string $kw, string $hp): string
    {
        return number_format((float)$kw, 0) . " kW / " . number_format((float)$hp, 0) . " HP";
    }

    public static function formatCapacity(string $cc, string $liters): string
    {
        return number_format((float)$cc, 0) . " cc / " . number_format((float)$liters, 1) . " l";
    }
}
