<?php

namespace App\Helper;


class HtmlUtils
{

    static public function addVersionToJsAndCsss($html_content, $param_value)
    {

        // Pattern để tìm các liên kết
        $pattern = '/(href|src)=["\']([^"\']*\.(css|js))["\']/i';

        // Callback function để thêm tham số vào cuối của các liên kết
        $callback = function ($matches) use ($param_value) {
            $url = $matches[2];
            return $matches[1] . '="' . $url . '?vep=' . $param_value . '"';
        };

        // Thay thế các liên kết trong nội dung HTML bằng cách sử dụng preg_replace_callback
        $modified_html = preg_replace_callback($pattern, $callback, $html_content);

        return $modified_html;
    }

    static public function addTagHtmlOnHead($htmlTag, $body)
    {
        $index = strpos($body, "</head>");

        $body =  substr_replace($body, $htmlTag, $index, 0);

        return $body;
    }

    static public function addTagHtmlOnBody($htmlTag, $body)
    {
        $index1 = strpos($body, "<body>");
        if ($index1 > 0) {
            $body =  substr_replace($body, $htmlTag, $index1 + 6, 0);
            return  $body;
        }

        $index1 = strpos($body, "<body");
        $sub1 = substr($body, $index1);

        if ($index1  > 5) {
            $index2 = strpos($sub1, ">");
            $body =  substr_replace($body, $htmlTag, $index1 + $index2 + 1, 0);
        }


        return $body;
    }
}
