<?php

namespace App\Helper;

use App\Events\RedisChatEventList;
use App\Events\RedisChatEventListAdmin;
use App\Jobs\SendSocketListJob;
use App\Models\AdminChat;
use App\Models\GroupChat;
use App\Models\PersonChat;
use App\Models\UserFriend;

class ChatUtils
{

    const LIST_USER_CHAT = 0;
    const LIST_GROUP_CHAT = 1;
    const LIST_ADMIN_PERSON_CHAT = 2;

    const TYPE_CHAT_0 = 0;
    const TYPE_CHAT_RECALL = 1;



    static function sendListSocket($user_id, $type)
    {
        // SendSocketListJob::dispatch(
        //     $user_id,
        //     $type
        // );

        if ($type == ChatUtils::LIST_USER_CHAT) {

            // $all = PersonChat::where('user_id', $user_id)
            //     ->orderBy('updated_at', 'desc')
            //     ->paginate(20);

            $all = PersonChat::where('user_id', $user_id)
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            $itemsTransformed = $all
                ->getCollection()
                ->map(function ($item) use ($user_id) {
                    $item2 = $item->toArray();
                    $item2['to_user']['is_friend'] = UserFriend::where('user_id',  $user_id)->where('friend_user_id', $item->vs_user_id)->first() != null;
                    return  $item2;
                })->toArray();

            $itemsTransformedAndPaginated = new \Illuminate\Pagination\LengthAwarePaginator(
                $itemsTransformed,
                $all->total(),
                $all->perPage(),
                $all->currentPage(),
                [
                    'path' => \Request::url(),
                    'query' => [
                        'page' => $all->currentPage()
                    ]
                ]
            );

            event($e = new RedisChatEventList($itemsTransformedAndPaginated,  $user_id,  $type));
        }

        if ($type == ChatUtils::LIST_GROUP_CHAT) {

            $all = GroupChat::where('user_id', $user_id)
                ->orderBy('updated_at', 'desc')
                ->paginate(20);


            event($e = new RedisChatEventList($all,  $user_id,  $type));
        }

        if ($type == ChatUtils::LIST_ADMIN_PERSON_CHAT) {

            $all = AdminChat::orderBy('updated_at', 'desc')
                ->paginate(20);


            event($e = new RedisChatEventListAdmin($all));
        }
    }
}
