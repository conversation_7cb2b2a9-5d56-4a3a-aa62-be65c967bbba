<?php

namespace App\Helper;

use Exception;



class CurlUtils
{

    static function request(string $method, $uri = '', array $options = [])
    {
        //form_params là body data
        //cookies
        //headers

        //for alldata
        $CURLOPT_CONNECTTIMEOUT = 90;
        $CURLOPT_TIMEOUT = 90;
        if (
            str_contains($uri, 'ADAG/repair/ADConnect/v5/account/recent_cars')
        ) {
            $CURLOPT_CONNECTTIMEOUT = 5;
            $CURLOPT_TIMEOUT = 5;
        }

        $res =  new CurlReponse();

        $ch = curl_init();
        $user_agent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:134.0) Gecko/******** Firefox/134.0';
        curl_setopt($ch, CURLOPT_URL, $uri);
        curl_setopt($ch, CURLOPT_USERAGENT, $user_agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $CURLOPT_CONNECTTIMEOUT);
        curl_setopt($ch, CURLOPT_TIMEOUT, $CURLOPT_TIMEOUT);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_ENCODING, "");
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        // curl_setopt($ch, CURLOPT_COOKIEFILE, "cookie2.txt");
        //curl_setopt($ch, CURLOPT_COOKIEJAR, "cookie.txt");

        if ($method == "POST" && isset($options['form_params'])) {
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $options['form_params']);
        }

        if (isset($options['headers']) && is_array($options['headers'])) {
            unset($options['headers']['cookie']);
            unset($options['headers']['host']);
            if (!isset($options['headers']['Accept']) && !isset($options['headers']['accept'])) {
                $options['headers']['Accept'] = 'application/hal+json';
            }
            $header_str_list = [];
            foreach ($options['headers'] as $key => $value) {

                if (
                    !str_contains($key, "cf-connecting-ip") && !str_contains($key, "cdn-loop")
                    && !str_contains($key, "x-forwarded-for")
                    && !str_contains($key, "x-forwarded-proto")
                    && !str_contains($key, "cf-visitor")
                    && !str_contains($key, "X-Forwarded-For")

                ) {
                    array_push($header_str_list, "$key: $value");
                }
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, $header_str_list);
        }

        if (isset($options['cookie'])) {
            curl_setopt($ch, CURLOPT_COOKIE, $options['cookie']);
        }


        if (isset($options['proxy'])) {
            curl_setopt($ch, CURLOPT_PROXY, ProxyUtils::getIPPort($options['proxy'] ?? ""));
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, ProxyUtils::getUserPass($options['proxy'] ?? ""));
            //curl_setopt($ch, CURLOPT_PROXY, "http://lum-customer-hl_70855a58-zone-data_center:<EMAIL>:22225"); 
        }
        //"proxy" => "http://lum-customer-hl_70855a58-zone-data_center:<EMAIL>:22225",

        $headers = [];
        curl_setopt(
            $ch,
            CURLOPT_HEADERFUNCTION,
            function ($curl, $header) use (&$headers) {
                $len = strlen($header);
                $header = explode(':', $header, 2);
                if (count($header) < 2) // ignore invalid headers
                    return $len;
                $headers[strtolower(trim($header[0]))][] = trim($header[1]);
                return $len;
            }
        );
        $content = curl_exec($ch);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $httpStatusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new Exception(curl_error($ch));
        }
        $res->responseBody =   $content;
        $res->contentType =  $contentType;
        $res->headers =  $headers;
        $res->statusCode = $httpStatusCode;
        return  $res;
    }

    static function requestFordPts(string $method, $uri = '', array $options = [])
    {


        //form_params là body data
        //cookies
        //headers

        //for alldata
        $CURLOPT_CONNECTTIMEOUT = 90;
        $CURLOPT_TIMEOUT = 90;


        $res =  new CurlReponse();

        $ch = curl_init();
        $user_agent = 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36';
        curl_setopt($ch, CURLOPT_URL, $uri);
        curl_setopt($ch, CURLOPT_USERAGENT, $user_agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);



        if (str_contains($uri, 'Book') || str_contains($uri, 'wiring') || str_contains($uri, 'applicationservices')) {
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        } else {
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        }

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $CURLOPT_CONNECTTIMEOUT);
        curl_setopt($ch, CURLOPT_TIMEOUT, $CURLOPT_TIMEOUT);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_ENCODING, "");
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);

        if ($method == "POST" && isset($options['form_params'])) {
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $options['form_params']);
        }


        if (isset($options['headers']) && is_array($options['headers'])) {
            unset($options['headers']['cookie']);
            unset($options['headers']['host']);
            if (!isset($options['headers']['Accept']) && !isset($options['headers']['accept'])) {
                $options['headers']['Accept'] = 'application/hal+json';
            }
            $header_str_list = [];
            foreach ($options['headers'] as $key => $value) {

                // if (
                //     !str_contains($key, "cf-connecting-ip") && !str_contains($key, "cdn-loop")
                //     && !str_contains($key, "x-forwarded-for")
                //     && !str_contains($key, "x-forwarded-proto")
                //     && !str_contains($key, "cf-visitor")
                //     && !str_contains($key, "dnt")
                // ) {
                array_push($header_str_list, "$key: $value");
                // }
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, $header_str_list);
        }


        if (isset($options['cookie'])) {
            curl_setopt($ch, CURLOPT_COOKIE, $options['cookie']);
        }

        if (isset($options['proxy'])) {
            curl_setopt($ch, CURLOPT_PROXY, ProxyUtils::getIPPort($options['proxy'] ?? ""));
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, ProxyUtils::getUserPass($options['proxy'] ?? ""));
        }

        $headers = [];
        curl_setopt(
            $ch,
            CURLOPT_HEADERFUNCTION,
            function ($curl, $header) use (&$headers) {
                $len = strlen($header);
                $header = explode(':', $header, 2);
                if (count($header) < 2) // ignore invalid headers
                    return $len;
                $headers[strtolower(trim($header[0]))][] = trim($header[1]);
                return $len;
            }
        );
        $content = curl_exec($ch);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $httpStatusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new Exception(curl_error($ch));
        }
        $res->responseBody =   $content;
        $res->contentType =  $contentType;
        $res->headers =  $headers;
        $res->statusCode = $httpStatusCode;
        return  $res;
    }

    static function requestProdemand(string $method, $uri = '', array $options = [])
    {
        //form_params là body data
        //cookies
        //headers

        //for alldata
        $CURLOPT_CONNECTTIMEOUT = 90;
        $CURLOPT_TIMEOUT = 90;
        if (
            str_contains($uri, 'ADAG/repair/ADConnect/v5/account/recent_cars')
        ) {
            $CURLOPT_CONNECTTIMEOUT = 5;
            $CURLOPT_TIMEOUT = 5;
        }

        $res =  new CurlReponse();

        $ch = curl_init();
        $user_agent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:134.0) Gecko/******** Firefox/134.0';
        curl_setopt($ch, CURLOPT_URL, $uri);
        curl_setopt($ch, CURLOPT_USERAGENT, $user_agent);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $CURLOPT_CONNECTTIMEOUT);
        curl_setopt($ch, CURLOPT_TIMEOUT, $CURLOPT_TIMEOUT);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_ENCODING, "");
        // curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        // curl_setopt($ch, CURLOPT_COOKIEFILE, "cookie2.txt");
        //curl_setopt($ch, CURLOPT_COOKIEJAR, "cookie.txt");

        if ($method == "POST" && isset($options['form_params'])) {

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $options['form_params']);
        }

        if (isset($options['headers']) && is_array($options['headers'])) {
            unset($options['headers']['cookie']);
            unset($options['headers']['host']);
            if (!isset($options['headers']['Accept']) && !isset($options['headers']['accept'])) {
                $options['headers']['Accept'] = 'application/hal+json';
            }
            $header_str_list = [];
            foreach ($options['headers'] as $key => $value) {

                if (
                    !str_contains($key, "cf-connecting-ip") && !str_contains($key, "cdn-loop")
                    && !str_contains($key, "x-forwarded-for")
                    && !str_contains($key, "x-forwarded-proto")
                    && !str_contains($key, "cf-visitor")
                ) {
                    array_push($header_str_list, "$key: $value");
                }
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, $header_str_list);
        }

        if (isset($options['cookie'])) {
            curl_setopt($ch, CURLOPT_COOKIE, $options['cookie']);
        }


        if (isset($options['proxy'])) {
            curl_setopt($ch, CURLOPT_PROXY, ProxyUtils::getIPPort($options['proxy'] ?? ""));
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, ProxyUtils::getUserPass($options['proxy'] ?? ""));
            //curl_setopt($ch, CURLOPT_PROXY, "http://lum-customer-hl_70855a58-zone-data_center:<EMAIL>:22225"); 
        }
        //"proxy" => "http://lum-customer-hl_70855a58-zone-data_center:<EMAIL>:22225",

        $headers = [];
        curl_setopt(
            $ch,
            CURLOPT_HEADERFUNCTION,
            function ($curl, $header) use (&$headers) {
                $len = strlen($header);
                $header = explode(':', $header, 2);
                if (count($header) < 2) // ignore invalid headers
                    return $len;
                $headers[strtolower(trim($header[0]))][] = trim($header[1]);
                return $len;
            }
        );
        $content = curl_exec($ch);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $httpStatusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new Exception(curl_error($ch));
        }
        $res->responseBody =   $content;
        $res->contentType =  $contentType;
        $res->headers =  $headers;
        $res->statusCode = $httpStatusCode;
        return  $res;
    }

    static function requestAllEU(string $method, $uri = '', array $options = [])
    {
        $res =  new CurlReponse();

        $curl = curl_init();
        $cookie = $options['cookie'] ?? "";

        $header_str_list = [];
        if (isset($options['headers']) && is_array($options['headers'])) {
            unset($options['headers']['cookie']);
            unset($options['headers']['host']);
            if (!isset($options['headers']['Accept']) && !isset($options['headers']['accept'])) {
                $options['headers']['Accept'] = 'application/hal+json';
            }
            foreach ($options['headers'] as $key => $value) {
                array_push($header_str_list, "$key: $value");
            }
        }

        curl_setopt_array($curl, array(
            CURLOPT_URL => $uri,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method ?? 'GET',
            CURLOPT_POSTFIELDS => json_encode($options['form_params']),
            CURLOPT_HTTPHEADER => array(
                'content-type: application/json',
                "cookie: $cookie",
                'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:134.0) Gecko/******** Firefox/134.0',
                'accept: ' . $options['headers']['Accept'],
                'accept-language: ' . $options['headers']['Accept-Language'],
                'accept-encoding: ' . $options['headers']['Accept-Encoding'],
            ),
        ));

        if ($method == "POST" && isset($options['form_params'])) {

            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($options['form_params']));
        }

        $headers = [];
        curl_setopt(
            $curl,
            CURLOPT_HEADERFUNCTION,
            function ($curl, $header) use (&$headers) {
                $len = strlen($header);
                $header = explode(':', $header, 2);
                if (count($header) < 2) // ignore invalid headers
                    return $len;
                $headers[strtolower(trim($header[0]))][] = trim($header[1]);
                return $len;
            }
        );
        $content = curl_exec($curl);
        $contentType = curl_getinfo($curl, CURLINFO_CONTENT_TYPE);
        if (curl_errno($curl)) {
            throw new Exception(curl_error($curl));
        }
        $res->responseBody =   $content;
        $res->contentType =  $contentType;
        $res->headers =  $headers;

        curl_close($curl);
        return  $res;
    }
}
