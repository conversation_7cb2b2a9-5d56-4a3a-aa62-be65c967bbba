<?php

namespace App\Helper;

use Exception;

class StringUtils
{

    static function description_contains_image_base64($des)
    {
        if (strpos($des, 'data:image') !== false && strpos($des, 'base64') !== false) {
            return true;
        }
        return false;
    }

    static function convert_name($str)
    {
        if ($str == null) return "";
        $str = preg_replace("/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/", 'a', $str);
        $str = preg_replace("/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/", 'e', $str);
        $str = preg_replace("/(ì|í|ị|ỉ|ĩ)/", 'i', $str);
        $str = preg_replace("/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/", 'o', $str);
        $str = preg_replace("/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/", 'u', $str);
        $str = preg_replace("/(ỳ|ý|ỵ|ỷ|ỹ)/", 'y', $str);
        $str = preg_replace("/(đ)/", 'd', $str);
        $str = preg_replace("/(À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ)/", 'A', $str);
        $str = preg_replace("/(È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ)/", 'E', $str);
        $str = preg_replace("/(Ì|Í|Ị|Ỉ|Ĩ)/", 'I', $str);
        $str = preg_replace("/(Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ)/", 'O', $str);
        $str = preg_replace("/(Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ)/", 'U', $str);
        $str = preg_replace("/(Ỳ|Ý|Ỵ|Ỷ|Ỹ)/", 'Y', $str);
        $str = preg_replace("/(Đ)/", 'D', $str);
        $str = preg_replace("/(\“|\”|\‘|\’|\,|\!|\&|\;|\@|\#|\%|\~|\`|\=|\_|\'|\]|\[|\}|\{|\)|\(|\+|\^)/", '-', $str);
        return $str;
    }

    static function convert_name_lowcase($str)
    {
        return strtolower(StringUtils::convert_name($str ?? "") ?? "");
    }

    static function splitBy2($str)
    {

        $indexSP = strpos($str, ':');
        $str1  =  substr($str, 0,  $indexSP);
        $str2  =  substr($str, $indexSP + 1);

        return [
            "str1" => $str1,
            "str2" => $str2,
        ];
    }

    static function headerToKeyValue($header)
    {



        $data = [];

        $array = preg_split("/\r\n|\n|\r/", $header);
        foreach ($array as $item) {

            $splitSpit =   StringUtils::splitBy2($item);


            if (!empty($splitSpit['str1']) && !empty($splitSpit['str2'])) {
                $data[trim($splitSpit['str1'])] = trim($splitSpit['str2'], ' ');
            }
        }

        return ($data);
    }

    static function addGetParamToUrl($url, $varName, $value)
    {
        // is there already an ?
        if (strpos($url, "?")) {
            $url .= "&" . $varName . "=" . $value;
        } else {
            $url .= "?" . $varName . "=" . $value;
        }

        return $url;
    }

    static function normalizeProxy($proxyString)
    {
        $patterns = [
            '/^(?<ip>[\d.]+):(?<port>\d+):(?<user>[^:]+):(?<pass>.+)$/',
            '/^(?<user>[^:]+):(?<pass>.+)@(?<ip>[\d.]+):(?<port>\d+)$/',
            '/^(?<ip>[\d.]+):(?<port>\d+)$/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $proxyString, $matches)) {
                $ip = $matches['ip'] ?? null;
                $port = $matches['port'] ?? null;
                $user = $matches['user'] ?? null;
                $pass = $matches['pass'] ?? null;

                if ($user && $pass) {
                    return "$user:$pass@$ip:$port";
                } elseif ($ip && $port) {
                    return "$ip:$port";
                }
            }
        }

        return null;
    }

    static function extractPhoneNumber($text)
    {
        preg_match('/\+\d{5,}/', $text, $matches);
        if (!empty($matches)) {
            return $matches[0];
        }
        return null;
    }
}
