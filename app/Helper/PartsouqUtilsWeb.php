<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use App\Helper\HtmlUtils;
use App\Models\WebData;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use GuzzleHttp\Exception\RequestException;

class PartsouqUtilsWeb
{

    static public function setCache($request, $key, $value)
    {
        $expiresAt = Carbon::now()->addMinutes(60);
        Cache::put(json_encode([$request == null || $request->user == null ? null : $request->user->email,  $key]), $value, $expiresAt);
    }
    static public function getCache($request, $key)
    {
        return Cache::get(json_encode([$request == null || $request->user == null ? null : $request->user->email, $key]));
    }
    /**
     * Lấy dữ liệu
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $proxy = null)
    {

        $tailLink = $_SERVER["REQUEST_URI"];



        // if (
        //     $tailLink == "/"
        // ) {
        //     $domainMain = Helper::getDomainCurrent();
        //     header("Location: " . $domainMain . "/partslink24/startup.do");
        //     exit;
        // }


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        $configAdmin =      ConfigAdmin::first();
        if ($proxy === null) {
            if ($configAdmin  != null && !empty($configAdmin->proxy_partslink24)) {
                $proxy = $configAdmin->proxy_partslink24;
            }
        }

        if ($cookies_str == null) {
            if ($configAdmin  != null && !empty($configAdmin->cookies_partslink24_str)) {
                $cookies_str = $configAdmin->cookies_partslink24_str;
            }
        }

        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $arr = CookiesUtils::cookieStrToArray($cookies_str);

        //unset($headers_requests['user-agent']);

        //  unset($headers_requests['cookie']);
        unset($headers_requests['host']);
        // unset($headers_requests['connection']);
        // unset($headers_requests['dnt']);
        //unset($headers_requests['upgrade-insecure-requests']);


        $headerArr  =  $headers_requests;;



        // $headerArr['Cookie'] = $cookies_str;
        // $headerArr['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';


        if (
            str_contains($tailLink, 'partslink24/startup.do') ||     str_contains($tailLink, '.html')

        ) {
            try {
                foreach ($arr as $key => $value) {
                    $domain = Helper::getDomainCurrentWithoutSubAndNoHttp();
                    setcookie($key, $value, time() + 24 * 3600, "/", ".$domain");
                }
            } catch (Exception $e) {
            };
        }

        $jar = new \GuzzleHttp\Cookie\CookieJar();

        if ($arr  != null && is_array($arr)) {
            foreach ($arr as $key => $value) {

                $jar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name'     => $key,
                    'Value'    =>  $value,
                    'Domain'   => '.partsouq.com',
                    'Path'     => '/',
                    // 'Max-Age'  => $item['name'],
                    //'Expires'  => $item['name'],
                    // 'Secure'   => $item['name'],
                    // 'Discard'  => $item['name'],
                    //  'HttpOnly' => $item['name'],
                ]));
            }
        }

        $client = new \GuzzleHttp\Client(
            []
        );

        $access_token_partslink24 =   PartsouqUtilsWeb::getCache($request, 'access_token_partslink24');
        if ($access_token_partslink24  != null) {
            $headerArr['authorization'] = "Bearer " . $access_token_partslink24;
        }

        try {
            $response1 = $client->request(
                $method,
                $link,
                [
                    // 'cookies' => $jar,
                    'headers' => $headerArr,
                    'json' => $body,
                    'form_params' => $body,
                    'timeout' => 90, // Response timeout
                    'connect_timeout' => 90, // Connection timeout,
                    'on_stats' => (str_contains($tailLink, 'check_')) ? null : function (\GuzzleHttp\TransferStats $stats) {

                        $url = $stats->getHandlerStats()['redirect_url'];
                        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                        $pathNoHTTP = str_replace("http://", "", $path);
                        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);


                        $url =  str_replace("https://www.partsouq.com", $path, $url);
                        $url =  str_replace("http://www.partsouq.com", $path, $url);
                        $url =  str_replace("https://partsouq.com", $path, $url);
                        $url =  str_replace("http://partsouq.com", $path, $url);
                        $url =  str_replace("partsouq.com", $pathNoHTTP, $url);


                        $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                        $url =  str_replace("https://", "", $url);
                        $url =  str_replace("http://", "", $url);
                        $url =  str_replace(" ", "",  $url);


                        // if (str_contains($url, "logout.do")) {
                        //     throw new Exception("logout.");
                        // }

                        $tailLink = $_SERVER["REQUEST_URI"];

                        $tailLink =  rtrim($tailLink, "/");
                        $url =  rtrim($url, "/");


                        if ($url != null && $url != "" && $url != $tailLink) {

                            $firtUrl = substr($url, 0, 3);
                            if ($firtUrl == "www") {
                                $url =  str_replace("www.", "https://www.",  $url);
                            }


                            if (str_contains($tailLink, "changeLang")) {

                                parse_str(parse_url($tailLink)['query'], $params);
                                if (isset($params['changeLang'])) {
                                    $changeLang = $params['changeLang'];

                                    $url = StringUtils::addGetParamToUrl($url, 'changeLang', $changeLang);
                                }
                            }


                            header("Location: $url");
                            exit;
                        }
                    }
                ]
            );
        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response1 = $e->getResponse();
            } else {
                // Xử lý khi không có response
                echo "Request failed: " . $e->getMessage() . "\n";
            }
        }




        return  $response1;
    }

    static public function  remove_element_reponse($body, $contentType, $statusCode, $request)
    {

        $ContentType = $contentType;

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];

        $tailLink = $_SERVER["REQUEST_URI"];
        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            $ContentType = "application/pdf";
        } else 
        if (str_contains($tailLink, '.svg')) {
            $ContentType = "image/svg+xml";
        }


        if (str_contains($tailLink, '/authorize')) {
            if (isset(json_decode($body)->access_token)) {
                PartsouqUtilsWeb::setCache($request, 'access_token_partslink24', json_decode($body)->access_token);
            };
        }


        //Save vao db
        if ($request != null && $request->method() == "GET" && $statusCode == 200) {
            try {
                if (
                    $request != null && !str_contains($contentType, "image")
                    && $body != null && $body != "" && !str_contains($tailLink, "user/adminMenu")
                    && !str_contains($tailLink, "p5.html")
                ) {
                    $webClass = new WebClass();
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::PARTSLINK24;
                    $webClass->method = $request->method();
                    $webClass->content_type =  $contentType;
                    $webClass->body =  $body;
                    $webClass->status_code =  $statusCode;
                    
                    WebDataUtils::saveWebData($webClass);
                }
            } catch (Exception $e) {
            }
        }

        if ($ContentType) {

            if (str_contains($ContentType, 'text/html')  && !$request->isMethod('post')) {
                $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                    <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag("js", new Date());
                
                    gtag("config", "G-KXWTNY3G2J");
                    </script>', $body);
            }

            if (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json')) {
                // $body =  str_replace("https://www.partsouq.com", $path, $body);
                // $body =  str_replace("http://www.partsouq.com", $path, $body);
                // $body =  str_replace("https://partsouq.com", $path, $body);
                // $body =  str_replace("http://partsouq.com", $path, $body);
                // $body =  str_replace("partsouq.com", $pathNoHTTP, $body);

                $body =  str_replace("http://", "https://", $body);

                $cookies = $_COOKIE;
                $platform = $cookies['platform'] ?? request()->header('platform') ?? null;

                if ($request->isMethod('get') && str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);

                    if ($dom  != null) {


                        $portalMenu = $dom->find('#portalMenu', 0);

                        if ($portalMenu != null) {
                            $portalMenu->remove();
                        }

                        $logoutLink = $dom->find('#logoutLink', 0);

                        if ($logoutLink != null) {
                            $logoutLink->remove();
                        }

                        $catalog_logout = $dom->find('#catalog_logout', 0);

                        if ($catalog_logout != null) {
                            $catalog_logout->remove();
                        }

                        $HeaderMenus = $dom->find('.HeaderMenu');
                        $idxMenu = 0;
                        if (count($HeaderMenus) > 1) {
                            $lengthMenu = count($HeaderMenus);
                            foreach ($HeaderMenus as $menu) {
                                if ($idxMenu !=  $lengthMenu - 1) {
                                    $menu->remove();
                                }

                                $idxMenu++;
                            }
                        }


                        //replace html
                        $body2 =  $dom->innertext;
                        $body =  ($body2);
                    }


                    if ($request->user != null) {

                        $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_partslink24");
                        if ($remain_days > 0 && $remain_days < 10  && !$request->isMethod('post')) {
                            $appName = DomainConfigHelper::getConfig('appName');
                            $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script>

                                 siiimpleToast.alert("('.$appName.') Partslink24 expiry date is only ' . $remain_days . ' days, remember to renew!", {
                                  position: "top|center",
                                  margin: 15,
                                  delay: 0,
                                  duration: 5000,
                                });
                                </script>', $body);
                        }
                    }
                }
            }
        }

        header('Content-type: ' .  $ContentType ?? "");
        echo $body;
        die();
    }

    static public function checkServer($cookies_str = null, $proxy = null)
    {
        try {
            $time_start = microtime(true);


            $response  = PartsouqUtilsWeb::getResponse('https://www.partsouq.com/partslink24/partslink24/account-users.action', "GET", $cookies_str, null, request());
            $type = $response->getHeader('Content-Type')[0] ?? "";

            $content = ($response->getBody()->getContents());

            if (str_contains($type, 'text/html')) {
                $dom = HtmlDomParser::str_get_html($content);

                $accountEntry = $dom->find(".list-entry", 0);
                if ($accountEntry  == null) {
                    return [
                        'status' => false,
                        'mess' => "null entry account"
                    ];
                };
                $trs = $accountEntry->find("tr");

                $username = $trs[0]->children()[1]->plaintext;
                $name = $trs[1]->children()[1]->plaintext;
                $email = $trs[2]->children()[1]->plaintext;

                $time_end = microtime(true);
                $dataFinal = [
                    'time_handle' => ($time_end - $time_start),
                    'status' => true,
                    'username' => $username,
                    'name' => $name,
                    'email' => $email,
                    'mess' => "",
                ];
                return $dataFinal;
            }
        } catch (Exception $e) {

            LogUtils::error($e);
            return [
                'status' => false,
                'mess' => $e->getMessage() . " khong xac dinh"
            ];
        }
        return [
            'status' => false,
            'mess' => "ERROR COOKIES"
        ];
    }
}
