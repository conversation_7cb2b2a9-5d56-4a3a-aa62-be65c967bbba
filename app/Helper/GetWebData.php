<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Exceptions\NasException;
use Exception;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;

class GetWebData
{
    static function getWebDataAutoData($method, $tai_link)
    {
        $webClass = new WebClass();
        $webClass->hours_ago = 24 * 30;
        $webClass->tail_link = $tai_link;
        $webClass->method = $method;
        $webClass->service = PlanUtils::AUTODATA;

        $webDataExists = WebDataUtils::getWebData($webClass);

        $data = null;

        if ($webDataExists !== null && isset($webDataExists->content) && !empty($webDataExists->content)) {
            $data = base64_decode($webDataExists->content);
        }

        return $data;
    }

    static function getWebDataAutoDataImg($method, $tai_link)
    {
        $webClass = new WebClass();
        $webClass->hours_ago = 24 * 30;
        $webClass->tail_link = $tai_link;
        $webClass->method = $method;
        $webClass->service = PlanUtils::AUTODATA;
        $webDataExists = WebDataUtils::getWebData($webClass);
        return $webDataExists;
    }

    static function getWebDataAutoDataPost(Request $request, $method, $tai_link, $body)
    {

        $webClass = new WebClass();
        $webClass->hours_ago = 24 * 30;
        $webClass->tail_link = $tai_link;
        $webClass->method = $method;
        $webClass->service = PlanUtils::AUTODATA;
        $webClass->bodyRequest =  $body;
        $webDataExists = WebDataUtils::getWebData($webClass);
        $data = null;
        if ($webDataExists !== null && isset($webDataExists->content) && !empty($webDataExists->content)) {
            $data = base64_decode($webDataExists->content);
        }
        return $data;
    }

    static function getWebDataProdemand($method, $tail_link)
    {
        $method = $method;
        $webClass = new WebClass();
        $webClass->tail_link = $tail_link;
        $webClass->method = $method;
        $webClass->service = PlanUtils::MITCHELL_PRODEMAND;

        $webDataExists = WebDataUtils::getWebData($webClass);

        return $webDataExists;
    }

    static function queryTecDoc($sql)
    {
        try {
            $client = new Client();
            $response = $client->post('http://nas.fhost.ca/WebDataHandle/sql-data/db-tecdoc-2024q3.php', [
                'headers' => [
                    'Authorization' => 'Bearer nhaemcoconga1',
                    'Content-Type'  => 'text/plain'
                ],
                'body' => $sql
            ]);
            $responseBodyAsString = $response->getBody()->getContents();
         
            $jsonRes = json_decode($responseBodyAsString, true);
            if (isset($jsonRes['data']['error'])) {
                throw new NasException($jsonRes['data']['error']);
            }
            return  $jsonRes['data'];
        } catch (ClientException $e) {
            $response = $e->getResponse();
            $responseBodyAsString = $response->getBody()->getContents();
            $jsonRes = json_decode($responseBodyAsString, true);
            throw  new NasException($jsonRes['msg']);
        } catch (ServerException $e) {
            $response = $e->getResponse();
            $responseBodyAsString = $response->getBody()->getContents();
            $jsonRes = json_decode($responseBodyAsString, true);
            throw new NasException($jsonRes['msg']);
        } catch (Exception $e) {
            throw new NasException($e->getMessage());
        }
    }
}
