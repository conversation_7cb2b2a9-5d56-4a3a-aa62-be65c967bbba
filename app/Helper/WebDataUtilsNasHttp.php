<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Jobs\PushNotificationAdminJob;
use App\Jobs\SendPostRequest;
use App\Models\WebData;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Cache;

class WebDataUtilsNasHttp
{

    const WAY = 2;


    static function sendNoti($title, $content) {}

    static function saveWebData(WebClass $webClass)
    {
        $tailLink = $webClass->tail_link;
        $language = $webClass->language;
        $service = $webClass->service;
        $method = $webClass->method;
        $content = $webClass->content;
        $body = $webClass->body;
        $content_type = $webClass->content_type;
        $updated_at = $webClass->updated_at;
        $status_code = $webClass->status_code;

        // //Lưu server NAS
        // if (WebDataUtilsNasHttp::WAY == 2) {
        //     Cache::forget($tailLink);
        //     dispatch(new SendPostRequest('http://nas.obdoc.net/WebDataHandle/create-data.php',  [
        //         "tail_link" => $tailLink,
        //         "content" =>  base64_encode($body),
        //         "method" => $method,
        //         "content_type" => $content_type,
        //         "status_code" => $status_code,
        //         "service" =>  $service,
        //     ]))->onQueue('post_queue');
        // }
    }

    static function getWebData(WebClass $webClass)
    {

        try {
            $time_start = microtime(true);
            $detailData = "service $webClass->service tailLink $webClass->tail_link method $webClass->method content_type $webClass->content_type";


            $tail_link  = $webClass->tail_link;
            $method = $webClass->method;
            $content_type = $webClass->content_type;
            $service = $webClass->service;
            $hours_ago = $webClass->hours_ago;
            $bodyRequest = $webClass->bodyRequest;

            $bodyText = json_encode($bodyRequest);
            $cacheKey = "webdata_{$tail_link}_{$method}_{$content_type}_{$service}_{$bodyText}";
            $cacheKey = md5($cacheKey);
            $cacheDuration = 60; // 60 * 30 * 1; //thời gian cache file


            $jsonBody = Cache::remember($cacheKey, $cacheDuration, function () use ($tail_link, $method, $content_type, $service, $hours_ago, $bodyRequest) {
                $client = new Client(
                    [
                        'timeout' => 120,
                        'connect_timeout' =>  120,
                    ]
                );

                $response = $client->post('http://nas.obdoc.net/WebDataHandle/saved-data/pull-data.php', [
                    'headers' => [
                        // 'Content-Type' => 'application/json'
                    ],

                    'form_params' => [
                        "tail_link" => $tail_link,
                        'method' => $method,
                        'content_type' => $content_type,
                        'service' => $service,
                        'hours_ago' => $hours_ago,
                        'body_request' => json_encode($bodyRequest)
                    ]
                ]);
                $body = $response->getBody()->getContents();

                $jsonBody = json_decode($body, true);
                return $jsonBody;
            });



            if ($jsonBody['success'] == true) {
                $webClass->content = $jsonBody['data']['content'];
                $webClass->content_type = $jsonBody['data']['content_type'];
                $webClass->status_code = $jsonBody['data']['status_code'] ?? 200;
                return $webClass;
            } else {
                Cache::forget($cacheKey);
                return $jsonBody;
            }
        } catch (ClientException $e) {
            dd($e);
            $time_end = microtime(true);
            $time_handle = ($time_end - $time_start);
            if ($e->getResponse()->getStatusCode() != 200 && $e->getResponse()->getStatusCode() != 400) {

                WebDataUtilsNasHttp::sendNoti(
                    "Can't get WebData " . $detailData . " time handle " .  $time_handle,
                    "Error 500  " . $e->getMessage()
                );
            }
        } catch (GuzzleException $e) {
            $time_end = microtime(true);
            $time_handle = ($time_end - $time_start);
            WebDataUtilsNasHttp::sendNoti(
                "Can't get WebData " . $detailData . " time handle " .  $time_handle,
                "Error request  " . $e->getMessage()
            );
        } catch (Exception $e) {
            $time_end = microtime(true);
            $time_handle = ($time_end - $time_start);
            WebDataUtilsNasHttp::sendNoti(
                "Can't get WebData " . $detailData . " time handle " .  $time_handle,
                "Php Exception  Line: " . $e->getLine() . " " . $e->getMessage()
            );
        }

        return null;
    }
}
