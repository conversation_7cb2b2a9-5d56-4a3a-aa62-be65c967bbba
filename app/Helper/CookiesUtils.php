<?php

namespace App\Helper;

use Exception;

class CookiesUtils
{
    static function standardCookieStr($cookies_str) {
        if (substr($cookies_str, 0, 1) == "[") { //neu la array string
            $cookiesArray = json_decode($cookies_str, true);
            $cookies_str = CookiesUtils::cookiesArrayToStr($cookiesArray);
        }
        return $cookies_str;
    }

    static function splitBy2($str)
    {

        $indexSP = strpos($str, ':');
        $str1  =  substr($str, 0,  $indexSP);
        $str2  =  substr($str, $indexSP + 1);

        return [
            "str1" => $str1,
            "str2" => $str2,
        ];
    }

    static function cookieStrToArray($cookieStr)
    {

        if($cookieStr === null) return [];

        $cookieStr = str_replace(" ", "", $cookieStr);
        $cookieStr = str_replace("\r", "", $cookieStr);
        $cookieStr = str_replace("\n", "", $cookieStr);

        $data = [];

        try {
            $array = explode(";", $cookieStr);
            foreach ($array as $item) {
                try {
                    $spa1 = explode("=", $item,2)[0];
                    $spa2 = explode("=", $item,2)[1];

                    $data[$spa1] = $spa2;
                } catch (Exception $k) {
                }
            }
        } catch (Exception $e) {
            dd($e);
        }


        return ($data);
    }

    static function cookiesArrayToStr($cookies_autodata)
    {
        if (is_array($cookies_autodata)) {
            $arr = $cookies_autodata;

            $arr = json_decode(json_encode($arr), FALSE);
         
        } else {
            $arr = json_decode($cookies_autodata);

            $arr = json_decode(json_encode($arr), FALSE);
        }

        $strListCookies = "";
     
     
        if ($arr  != null && is_array($arr)) {
          
            foreach ($arr as $item) {


                if ($item->name == "access_token") {
                } else {
                    $strListCookies =  $strListCookies . ($item->name . "=" . $item->value) . ";";
                }
            }
        }
     
        return  $strListCookies;
    }

    static function cookiesKeyValueToStr($arrKeyValue)
    {
        $strListCookies = "";
        foreach ($arrKeyValue as $key => $value) {
            $strListCookies =  $strListCookies . ($key . "=" . $value) . ";";
        }

        return  $strListCookies;
    }
}
