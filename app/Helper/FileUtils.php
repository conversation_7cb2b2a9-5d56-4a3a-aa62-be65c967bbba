<?php
namespace App\Helper;


class FileUtils
{
    static public function isFile($path)
    {
        $filename = basename($path);
        return strpos($filename, '.') !== false;
    }

    static public function replaceSpecialChars($string)
    {
        $specialChars = [
            '*' => 'rep-asterisk',
            '?' => 'rep-question',
            ':' => 'rep-colon',
            '"' => 'rep-doublequote',
            '&' => 'rep-ampersand',
            ';' => 'rep-semicolon',
            '|' => 'rep-pipe',
            '>' => 'rep-greaterthan',
            '<' => 'rep-lessthan',
            '%' => 'rep-percent',
            '+' => 'rep-plus',
            '\\' => 'rep-xoat',
            '#' => 'th',
        ];

        return str_replace(array_keys($specialChars), array_values($specialChars), $string);
    }

    //hàm chuẩn hóa đường dẫn 
    static public function standPathFile($path)
    {

        $pathWithFile = $path;
        if (substr($path, -1) === '/') {
            $pathWithFile  = $path . "drindex.txt";
        } else {

            if (!FileUtils::isFile($path)) {
                $pathWithFile  = $path . ".txt";
            }
        }
        return  FileUtils::replaceSpecialChars($pathWithFile);
    }
    static public function splitAndJoinString($input, $maxLength = 50)
    {
        $chunks = str_split($input, $maxLength);
        $result = implode('/', $chunks);
        return $result;
    }
    static public function replaceDoubleSlashes($input)
    {
        $result = preg_replace('/\/\/+/', '/', $input);
        return $result;
    }
    static public function isAssociativeArray($array)
    {
        return array_keys($array) !== range(0, count($array) - 1);
    }
    static public function standLinkToPathFile($tailLink, $body = null)
    {

        $tailLink = str_replace("#", "4", $tailLink);
        $parsed_url = parse_url($tailLink);
        $path = $parsed_url['path'];
        $pathWithFile = $path;
        $query_string = isset($parsed_url['query']) ? $parsed_url['query'] : '';
        parse_str($query_string, $query_array);

        $resultParams = [];


        foreach ($query_array as $key => $value) {
            if (empty($value)) $value = "drnull";


            if (is_array($value)) {

                $newValue = '';
                foreach ($value as $key1 => $value1) {

                    if (is_array($value1)) {

                        if (isset($value1[0]) && is_array($value1[0]) && isset($value1[0][0]) && is_string($value1[0][0])) {
                            foreach ($value1[0] as $key2 => $value2) {
                                $newValue = $newValue . $key2 . $value2;
                            }
                        }
                        if (isset($value1[0])  && is_array($value1[0]) && FileUtils::isAssociativeArray($value1[0])) {

                            foreach ($value1[0] as $key2 => $value2) {
                                $newValue = $newValue . $key2 . $value2;
                            }
                        } else {

                            foreach ($value1 as $key2 => $value2) {
                                $newValue = $newValue . $key2 . $value2;
                            }
                        }
                    } else {
                        $newValue = $newValue . $key1 . "=" . $value1;
                    }
                }


                $value = $newValue;
                $value = str_replace(" ", "", $value);
            }
            if (strlen($value) > 15) $value = substr($value, -15);
            $value = str_replace("/", "-", $value);
            $resultParams[] = $key . '=' . $value;
        }

        $stringParamsPath = implode('/', $resultParams);
        $stringParamsPath = FileUtils::replaceSpecialChars($stringParamsPath);

        $bodyDataParams = "";
        if ($body != null) {
            $bodyDataParams = "/bodyparams/";
            if (is_array($body) && count($body) > 0) {
                foreach ($body as $key => $value) {
                    if ($key !== null && $value !== null) {
                        $bodyDataParams = $bodyDataParams . $key . "=" . trim($value) . "/";
                    }
                }
            }
            if (is_string($body) && strlen($body) > 0) {
                if (strlen($value) > 15) $bodyDataParams = substr($value, -15);
            }
        }


        //Có param
        if (count($query_array) > 0) {
            if (substr($path, -1) === '/') {
                $pathWithFile  = $path . 'drparams/' . $stringParamsPath . $bodyDataParams .  "/drindex.json";
            } else {
                $pathWithFile  = $path . '/drparams/' . $stringParamsPath . $bodyDataParams .  "/drindex.json";
            }
        } else {
            if (substr($path, -1) === '/') {
                $pathWithFile  = $path . $bodyDataParams  . "drindex.json";
            } else {
                $pathWithFile  = $path . $bodyDataParams  . "/drindex.json";
            }
        }

        $filePathFull = FileUtils::replaceDoubleSlashes(FileUtils::replaceSpecialChars($pathWithFile));

        return $filePathFull;
    }
}
