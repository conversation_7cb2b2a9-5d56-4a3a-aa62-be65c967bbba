<?php

namespace App\Helper;

use App\Classes\WebClass;
use App\Models\ConfigAdmin;
use App\Models\MsgCode;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use App\Helper\HtmlUtils;
use Illuminate\Support\Facades\Cache;

class HaynesUtilsWeb
{


    /**
     * Lấy dữ liệu
     */
    static public function  getResponse($link, $method = "GET",  $cookies_str = null, $body = [], Request $request = null, $disableRedirect = false, $proxy = null)
    {

        $body = [];

        $request = $request ?? request();

        if ($request != null) {
            $body = array_diff_key($request->all(), $request->query());
            $body =  Helper::removeBodyExcessive($body);
        }


        $tailLink = $_SERVER["REQUEST_URI"];

        if (
            str_contains($tailLink, 'wsdLogout') ||
            str_contains($tailLink, 'settingsContent') ||
            str_contains($tailLink, 'aboutContent') ||
            str_contains($tailLink, 'Logout') ||
            str_contains($tailLink, 'logout')
        ) {
            $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();

            header("Location: https://$request->agency_sub." . $domainMain);
            exit;
        }

        if (
            str_contains($tailLink, 'wsdLogin')
        ) {
            return ViewUtils::viewErrorPage([
                'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                'msg' => MsgCode::HAYNES_OF_MAINTENANCE[1],
            ]);
            exit;
        }


        if (
            str_contains($tailLink, 'login.do') ||
            $tailLink == "/"
        ) {
            $domainMain = Helper::getDomainCurrent();
            header("Location: " . $domainMain . "/touch/site/layout/makesOverview");
            exit;
        }


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }

        $configAdmin =      ConfigAdmin::first();
        if ($cookies_str == null) {
            $cookies_str = Cache::get(json_encode(['cookies_haynes_str']));

            if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_haynes_str)) {
                $cookies_str = $configAdmin->cookies_haynes_str;
            }
        }

        if ($proxy === null) {
            if ($configAdmin  != null && !empty($configAdmin->proxy_haynespro)) {
                $proxy = $configAdmin->proxy_haynespro;
            }
        }

        $headerArr['accept'] = $headers_requests['accept'] ?? null;
        $headerArr['x-csrf-token'] = $headers_requests['x-csrf-token'] ?? null;
        $headerArr['user-agent'] = 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.140';
        $headerArr['accept-language'] = 'accept-language: en-US,en;q=0.9';


        $cookies_str = CookiesUtils::standardCookieStr($cookies_str);
        $cookieArr  = CookiesUtils::cookieStrToArray($cookies_str);

        $headerArr['cookie'] =  $cookies_str;

        if (isset($cookieArr['DOMAIN_REQUEST_SERVER'])) {
            $link = str_replace('https://www.workshopdata.com', $cookieArr['DOMAIN_REQUEST_SERVER'], $link);
            $request->merge(['data3_domain' => $cookieArr['DOMAIN_REQUEST_SERVER']]);
        }

        $client = new \GuzzleHttp\Client(
            [
                'headers' => $headerArr,
                'proxy' => $proxy,

            ]
        );

        $response1 = $client->request(
            $method,
            $link,
            [
                'headers' => $headerArr,
                'form_params' => $body,
                'timeout' => 15, // Response timeout
                'proxy' => $proxy,
                'connect_timeout' => 15, // Connection timeout,
                'on_stats' => $disableRedirect  == true ? null : function (\GuzzleHttp\TransferStats $stats)   use ($request) {

                    $url = $stats->getHandlerStats()['redirect_url'];
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $pathNoHTTP = str_replace("http://", "", $path);
                    $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

                    $url =  str_replace("https://www.workshopdata.com:443", $path, $url);
                    $url =  str_replace("http://www.workshopdata.com:443", $path, $url);
                    $url =  str_replace("www.workshopdata.com:443", $pathNoHTTP, $url);

                    $url =  str_replace("https://www.workshopdata.com", $path, $url);
                    $url =  str_replace("http://www.workshopdata.com", $path, $url);
                    $url =  str_replace("www.workshopdata.com", $pathNoHTTP, $url);

                    if (str_contains($request->data3_domain, "hp.webdiag.name")) {

                        $url =  str_replace("https://www.hp.webdiag.name", $path, $url);
                        $url =  str_replace("https://hp.webdiag.name", $path, $url);
                        $url =  str_replace("http://hp.webdiag.name", $path, $url);
                        $url =  str_replace("hp.webdiag.name", $pathNoHTTP, $url);
                    }

                    if (str_contains($request->data3_domain ?? "", "project.webads3.com")) {

                        $url =  str_replace("https://www.project.webads3.com", $path, $url);
                        $url =  str_replace("https://project.webads3.com", $path, $url);
                        $url =  str_replace("http://project.webads3.com", $path, $url);
                        $url =  str_replace("project.webads3.com", $pathNoHTTP, $url);
                    }

                    $url =  str_replace($_SERVER["HTTP_HOST"], "", $url);
                    $url =  str_replace("https://", "", $url);
                    $url =  str_replace("http://", "", $url);
                    $url =  str_replace(" ", "",  $url);


                    $tailLink = $_SERVER["REQUEST_URI"];

                    $tailLink =  rtrim($tailLink, "/");
                    $url =  rtrim($url, "/");


                    if ($url != null && $url != "" && $url != $tailLink && str_contains($tailLink, 'suspended')) {

                        $firtUrl = substr($url, 0, 3);
                        if ($firtUrl == "www") {
                            $url =  str_replace("www.", "https://www.",  $url);
                        }


                        header("Location: $url");
                        exit;
                    }
                }
            ]
        );


        // $headers = $response1->getHeaders();
        // $setTokenHeaders = isset($headers['Set-Token']) ? $headers['Set-Token'] : [];
        // foreach ($setTokenHeaders as $setTokenHeader) {
        //     $splitItem = explode(";", $setTokenHeader);
        //     if (count($splitItem) > 0) {
        //         $splitKeyValue = explode("=", $splitItem[0],2);
        //         if (count($splitKeyValue) > 1 && $splitKeyValue[0] == "JSESSIONID") {
        //             $cookiesNew = $splitKeyValue[0] . "=" . $splitKeyValue[1];
        //         }
        //     }
        // }


        // if ($configAdmin  != null && !empty($cookiesNew)) {
        //     $expiresAt = Carbon::now()->addSeconds(6);
        //     Cache::put(json_encode(['cookies_haynes_str']), $cookiesNew, $expiresAt);
        //     $configAdmin->update([
        //         'cookies_haynes_str' => $cookiesNew
        //     ]);
        // }

        return  $response1;
    }

    static public function  remove_element_reponse($body, $contentType, $statusCode, $request)
    {

        $ContentType = $contentType;
        $actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);

        $tailLink = $_SERVER["REQUEST_URI"];
        if (str_contains($tailLink, '.pdf') || str_contains($tailLink, '_pdf')) {
            $ContentType = "application/pdf";
        } else 
        if ($contentType == null ||  $contentType == "") {
            if (str_contains($tailLink, 'layout/electronicSystems?')) {
                $ContentType = "application/json;charset=UTF-8";
            }
        }

        //Save vao db
        if ($request != null && $request->method() == "GET" && $statusCode == 200) {
            try {
                if (
                    $request != null && !str_contains($tailLink, "ogin")
                    // && !str_contains($contentType, "image")
                    && (!str_contains($body, "error") || str_contains($body, "genericErrorMessage"))
                    && $body != null && $body != "" && !str_contains($tailLink, "settingsContent") && $body != null
                    && !str_contains($body, '<input type="text" name="username" id="username"')
                    && !str_contains($body, 'class="login"')
                    && !str_contains($tailLink, 'electronicSystems')
                ) {


                    if (str_contains($request->data3_domain, "workshopdata.com")) {
                        $webClass = new WebClass();
                        $webClass->tail_link = $tailLink;
                        $webClass->service =  PlanUtils::HAYNESPRO;
                        $webClass->method = $request->method();
                        $webClass->content_type =  $contentType;
                        $webClass->body =  $body;
                        $webClass->status_code =  $statusCode;

                        WebDataUtils::saveWebData($webClass);
                    }
                }
            } catch (Exception $e) {
            }
        }

        if ($ContentType) {

            $appName = DomainConfigHelper::getConfig('appName');
            if (!str_contains($tailLink, "touch/getSubnetDiagram") && (str_contains($ContentType, 'text/html') || str_contains($ContentType, 'json'))) {

                //check is diagzone
                if (str_contains($request->data3_domain, "hp.webdiag.name")) {

                    $body =  str_replace("https://hp-en.webdiag.name", $path, $body);
                    $body =  str_replace("http://hp-en.webdiag.name", $path, $body);
                    $body =  str_replace("hp-en.webdiag.name", $pathNoHTTP, $body);

                    $body =  str_replace("https://hp.webdiag.name", $path, $body);
                    $body =  str_replace("http://hp.webdiag.name", $path, $body);
                    $body =  str_replace("hp.webdiag.name", $pathNoHTTP, $body);

                    $body =  str_replace("Diagzone", $appName, $body);
                    $body =  str_replace("https://diagzone.com/assets/img/fix-logo.png", "/assets/img/fix-logo.png", $body);
                }


                function replaceTitleTag($html)
                {
                    $appName = DomainConfigHelper::getConfig('appName');
                    $newText = preg_replace('/<title>[^<]*<\/title>/', '<title>' . $appName . ' Haynes Pro</title>', $html);
                    return $newText;
                }
                $body =  replaceTitleTag($body);

                //check if webads3
                if (str_contains($request->data3_domain, "webads3.com")) {
                    $body =  str_replace("https://www.project.webads3.com", $path, $body);
                    $body =  str_replace("https://project.webads3.com", $path, $body);
                    $body =  str_replace("http://project.webads3.com", $path, $body);
                    $body =  str_replace("project.webads3.com", $pathNoHTTP, $body);
                }

                $body =  str_replace("https://www.workshopdata.com:443", $path, $body);
                $body =  str_replace("http://www.workshopdata.com:443", $path, $body);
                $body =  str_replace("www.workshopdata.com:443", $pathNoHTTP, $body);

                $body =  str_replace("https://www.workshopdata.com", $path, $body);
                $body =  str_replace("http://www.workshopdata.com", $path, $body);
                $body =  str_replace("www.workshopdata.com", $pathNoHTTP, $body);
                $body =  str_replace("http://", "https://", $body);
                $body =  str_replace("https://diagzone.com/assets/img/fix-logo.png", "/assets/img/fix-logo.png", $body);

                $_csrf = Cache::get('HAYNESPRO_CAR_CSRF', null);
                if ($_csrf != null) {
                    $body = preg_replace(
                        '/<meta\s+name="_csrf"\s+content="[^"]*"\s*\/?>/i',
                        '<meta name="_csrf" content="' . htmlspecialchars($_csrf, ENT_QUOTES) . '" />',
                        $body
                    );
                }

                $configAdmin =      ConfigAdmin::first();
                if ($configAdmin  != null &&   $configAdmin->alldata_info != null && $request->user != null) {
                    $first_name = $configAdmin->alldata_info->first_name ?? "";
                    $last_name = $configAdmin->alldata_info->last_name ?? "";
                    $username = $request->user->username;
                    $body =  str_replace("<span class=\"dp_label\">$first_name $last_name</span>", "<span class=\"dp_label\">$username</span>", $body);
                }

                $cookies = $_COOKIE;
                $platform = $cookies['platform'] ?? request()->header('platform') ?? null;

                $isDocumentTypeHtml = false;
                if (substr($body, 0, strlen("<!DOCTYPE html>")) == "<!DOCTYPE html>") {
                    $isDocumentTypeHtml  = true;
                }

                if (str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);

                    if ($dom  != null) {

                        //Remove search
                        $last = $dom->find('.select-recent-car', 0);

                        if ($last != null) {
                            $last->remove();
                        }

                        //Remove Expires
                        $Expires = $dom->find('.licenseExpireAlert', 0);

                        if ($Expires != null) {
                            $Expires->remove();
                        }

                        $footer = $dom->find('.haynesproBar', 0);
                        if ($footer != null) {
                            $footer->remove();
                        }

                        //3 nust car truck va cost
                        $appMenu = $dom->find('.appMenu', 0);
                        if ($appMenu != null) {
                            $appMenu->remove();
                        }

                        $settingMenu = $dom->find('.settingMenu', 0);

                        if ($settingMenu != null) {
                            $settingMenu->remove();
                        }




                        $body =  $dom->innertext;
                    }


                    //Khong phai iframe thi them vao body


                    if ($isDocumentTypeHtml && !$request->isMethod('post')) {

                        $body =  HtmlUtils::addTagHtmlOnHead(' <div id="google_translate_element" style="z-index: 410;position: inherit;"></div>
                        <script> 
                        function googleTranslateElementInit() {
                        new google.translate.TranslateElement({
                            pageLanguage: "en"
                        }, "google_translate_element");
                        }
                        </script>
                        <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script> ', $body);
                    }


                    if ($isDocumentTypeHtml && $request->user != null && !$request->isMethod('post')) {

                        $remain_days = RenewUtils::get_days_remain_expiry($request->user, "expiry_haynespro");
                        if ($remain_days > 0 && $remain_days < 10) {
                            $appName = DomainConfigHelper::getConfig('appName');
                            $body =  HtmlUtils::addTagHtmlOnHead("<link rel='stylesheet'href='/css/css_toast.css'>", $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script src="/js/jquery_toast.js"></script>', $body);
                            $body =  HtmlUtils::addTagHtmlOnHead('<script>
                             
                                 siiimpleToast.alert("(' . $appName . ') Haynespro expiry date is only ' . $remain_days . ' days, remember to renew!", {
                                  position: "top|center",
                                  margin: 15,
                                  delay: 0,
                                  duration: 5000,
                                });
                                </script>', $body);
                        }
                    }
                }

                if ($isDocumentTypeHtml && str_contains($ContentType, 'text/html') && !$request->isMethod('post')) {
                    $body =  HtmlUtils::addTagHtmlOnHead('<script src="/jssav/' . Helper::generateRandomString(10) . '.js"></script>', $body);

                    $body =  HtmlUtils::addTagHtmlOnHead('<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
                        <script>
                        window.dataLayer = window.dataLayer || [];
                        function gtag(){dataLayer.push(arguments);}
                        gtag("js", new Date());
                    
                        gtag("config", "G-KXWTNY3G2J");
                        </script>', $body);
                }

                if ($platform != 'web' && $platform != null && str_contains($ContentType, 'text/html')) {

                    $dom = HtmlDomParser::str_get_html($body);
                    if ($dom  != null) {

                        //Remove last web pc
                        $top = $dom->find('header', 0);

                        if ($top != null) {
                            $top->remove();
                        }
                        $footer = $dom->find('footer', 0);

                        if ($footer != null) {
                            $footer->remove();
                        }
                    }

                    $body =  $dom->innertext;
                }
            }
        }


        header('Content-type: ' .  $ContentType ?? "");

        echo $body;
        die();
    }
}
