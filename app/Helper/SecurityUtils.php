<?php

namespace App\Helper;

use App\Models\DeviceLogin;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\Cache;

class SecurityUtils
{

    const SERVER_ALL_DATA = "SERVER_ALL_DATA";

    const SERVER_ALL_DATA_WEB = "SERVER_ALL_DATA_WEB";
    const SERVER_ALL_DATA_EU_WEB_CUSTOM = "SERVER_ALL_DATA_EU_WEB_CUSTOM";

    const SERVER_AUTO_DATA = "SERVER_AUTO_DATA";
    const SERVER_AUTO_DATA_WEB = "SERVER_AUTO_DATA_WEB";
    const SERVER_AUTO_DATA_WEB_SAVED = "SERVER_AUTO_DATA_WEB_SAVED";

    const SERVER_AUTO_DATA_ITALY_WEB = "SERVER_AUTO_DATA_ITALY_WEB";

    const SERVER_ALL_DATA_US_V2 = "SERVER_ALL_DATA_US_V2";

    const SERVER_ALL_DATA_US_V2_WEB = "SERVER_ALL_DATA_US_V2_WEB";

    const SERVER_HAYNES_PRO = "SERVER_HAYNES_PRO";

    const SERVER_HAYNES_PRO_WEB = "SERVER_HAYNES_PRO_WEB";

    const SERVER_HAYNES_PRO_TRUCK_WEB = "SERVER_HAYNES_PRO_TRUCK_WEB";

    const SERVER_MITCHELL_REPAIR_CENTER_WEB = "SERVER_MITCHELL_REPAIR_CENTER_WEB";

    const SERVER_MITCHELL_PRODEMAND_WEB = "SERVER_MITCHELL_PRODEMAND_WEB";

    const SERVER_TECDOC_WEB = "SERVER_TECDOC_WEB";

    const SERVER_IDENTIFIX_WEB = "SERVER_IDENTIFIX_WEB";

    const SERVER_PARTSLINK24_WEB = "SERVER_PARTSLINK24_WEB";

    const SERVER_KDS_GDS = "SERVER_KDS_GDS";

    const SERVER_KDS_GDS_WEB = "SERVER_KDS_GDS_WEB";

    const SERVER_PARTSOUQ_WEB = "SERVER_PARTSOUQ_WEB";

    const SERVER_ETKA_WEB = "SERVER_ETKA_WEB";

    const SERVER_FORD_PTS_WEB = "SERVER_FORD_PTS_WEB";

    const SERVER_TOYOTA_TIS_WEB = "SERVER_TOYOTA_TIS_WEB";

    static public function getSplitDevice()
    {
        $device_id = request()->header('dr-device-id') ?? $cookies['dr-device-id'] ?? request()->header('device-id')  ?? $cookies['device-id'] ?? "device-id-default";
        $len_device_id = strlen($device_id);
        $len05 = (int) ($len_device_id / 2);

        $device_id_split = substr($device_id, 0, $len05);
        return  $device_id_split;
    }

    static public function useDevice($user)
    {
        $user_id = $user->id;

        $deviceData = SecurityUtils::getDataDevice();
        $dataDevice = [
            'user_id' => $user_id,
            'ip_using' =>   $deviceData->ip_using,
            "platform" => $deviceData->platform,
            "device_id" => $deviceData->device_id,
            "app_version" => $deviceData->app_version,
            "model_name" => $deviceData->model,
            'last_visit_time' => Carbon::now('UTC')
        ];

        if ($user->is_user_ios == true) {
            $haveDevice = DeviceLogin::where('user_id', $user_id)
                ->first();

            if ($haveDevice != null) {
                $haveDevice->update($dataDevice);
            } else {
                $dataDevice['is_main'] = true;
                $dataDevice['login_time'] = Carbon::now('UTC');
                DeviceLogin::create($dataDevice);
            }

            return true;
        }

        return Cache::remember(json_encode(["devices",  $user_id,  $deviceData->device_id]), 3, function () use ($dataDevice, $deviceData, $user_id) {
            $maxDevice = 1;
            $device_id_split  =  SecurityUtils::getSplitDevice();

            //Kiểm tra slot đó có trong list device
            $carbon = new Carbon();
            $time = $carbon->format('Y-m-d H:i:s');

            $haveDevice = DeviceLogin::where('user_id', $user_id)
                ->where('device_id', 'like', '%' . $device_id_split  . '%')
                ->where(function ($query) use ($time) {
                    $query->where('expiry_use', '>=', $time)
                        ->orWhere('is_main', true);
                })
                ->first();

            if ($haveDevice  != null) {
                $haveDevice->update($dataDevice);
                return true;
            }

            //Kiểm tra còn slot
            //Chưa hề login và đc 1 slot
            $count = DeviceLogin::where('user_id', $user_id)->count();
            if ($count < $maxDevice) {
                if ($deviceData->device_id != null) {
                    if ($count < $maxDevice) {
                        $dataDevice['is_main'] = true;
                        $dataDevice['login_time'] = Carbon::now('UTC');
                        DeviceLogin::create($dataDevice);
                    }
                }
                return true;
            }


            //Còn slot mới mua hoặc main
            $carbon = Carbon::now();
            $time = $carbon->format('Y-m-d H:i:s');
            $haveSlot = DeviceLogin::where('user_id', $user_id)
                ->where('device_id', null)
                ->where(function ($query) use ($time) {
                    $query->where('expiry_use', '>=', $time)
                        ->orWhere('is_main', true);
                })
                ->first();


            if ($haveSlot != null) {
                if ($haveSlot != null) {
                    $haveSlot->update($dataDevice);
                }
                return true;
            }

            return false;
        });
    }


    static public function getDataDevice()
    {
        $cookies = $_COOKIE;

        $token = request()->header('dr-token') ?? $cookies['dr-token'] ?? request()->header('token') ?? $cookies['token'] ?? null;
        $device_id = request()->header('dr-device-id') ?? $cookies['dr-device-id'] ?? request()->header('device-id')  ?? $cookies['device-id'] ?? "device-id-default";
        $platform = request()->header('dr-platform') ?? $cookies['dr-platform'] ?? request()->header('platform') ?? $cookies['platform'] ?? null;
        $model = request()->header('dr-modelx')  ?? $cookies['dr-modelx'] ?? request()->header('model') ?? $cookies['model'] ?? null;
        $security_code = request()->header('dr-security-code') ?? $cookies['dr-security-code'] ?? request()->header('security-code') ?? $cookies['security-code'] ?? null;
        $app_version = request()->header('dr-version-app-local') ?? $cookies['dr-version-app-local']  ?? null;

        $ip_using = IPUtils::getIP();
        $dataDevice = new DataDevice();
        $dataDevice->token =  $token;
        $dataDevice->device_id =  $device_id;
        $dataDevice->platform =  $platform;
        $dataDevice->model =  $model;
        $dataDevice->security_code =  $security_code;
        $dataDevice->app_version =  $app_version;
        $dataDevice->ip_using =  $ip_using;

        return  $dataDevice;
    }
}

class DataDevice

{
    public $ip_using;
    public $token;
    public $device_id;
    public $platform;
    public $model;
    public $security_code;
    public $app_version;
}
