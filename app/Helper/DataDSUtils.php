<?php

namespace App\Helper;

use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Exception;
use GuzzleHttp\Client;


class DataDSUtils
{
    const HYUNDAI_URL = "https://service.hyundai-motor.com";
    const KIA_URL = "https://www.kia-hotline.com";

    const USER_ID_HYUNDAI = "<EMAIL>";
    const USER_ID_KIA = "TEST3"; //A26VAVAISOI

    const Aes256key1 = "nfactor!planemo!nfactor!planemo!";
    const Aes256key2 = "cnqrhqorhvkdusqhddhffuwnj18.!!!!";
    const Aes256key3 = "sdjfie!sdfijlvi!sdjfie!sdfijlvi!";
    const Aes256IV = "nfactor!planemo!";
    const Aes256IVKey3 = "sdjfie!sdfijlvi!";


    static function getUserID()
    {
        if (request()->carprovidertype == "hyundai") {
            return DataDSUtils::USER_ID_HYUNDAI;
        }
        return DataDSUtils::USER_ID_KIA;
    }

    static function GetUTCTimeStamp2($str)
    {
        $current_time_utc = new DateTime('now', new DateTimeZone('UTC'));
        $utc_plus_2_time = $current_time_utc->format($str);
        return $utc_plus_2_time;
    }

    static function  GetUTCTimeStamp()
    {
        return DataDSUtils::GetUTCTimeStamp2("YmdHis");
    }

    static function AESEncrypt($bArr)
    {
        $secretKey = "nfactor!planemo!";
        $cipher = "AES-128-ECB"; // ECB mode với AES-128
        $length = strlen($bArr);
        $i2 = (16 - ($length % 16)) + $length;
        $i3 = $i2 / 16;
        if ($i2 < 1 || $i3 == 0 || $length > $i2) {
            return null;
        }
        $bArr2 = str_pad($bArr, $i2, "\0"); // Điền thêm null bytes cho đủ độ dài
        // Thực hiện mã hóa AES
        $encrypted = openssl_encrypt($bArr2, $cipher, $secretKey, OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING);
        return $encrypted;
    }

    function AESDecrypt($bArr)
    {
        $secretKey = "nfactor!planemo!";
        $cipher = "AES-128-ECB"; // ECB mode với AES-128

        $length = strlen($bArr);
        if ($length / 16 < 1) {
            return null;
        }
        $decrypted = openssl_decrypt($bArr, $cipher, $secretKey, OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING);
        $i2 = 0;
        for ($i3 = 0; $i3 < $length && $decrypted[$i3] != "\0"; $i3++) {
            $i2++;
        }
        $bArr2 = substr($decrypted, 0, $i2);
        return $bArr2;
    }


    static function AES256Encode($text)
    {
        $key = DataDSUtils::Aes256key1;
        $iv = DataDSUtils::Aes256IV;
        $encrypted = openssl_encrypt($text, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
        return base64_encode($encrypted);
    }

    static function AES256Decode($text)
    {
        $key = base64_decode(DataDSUtils::Aes256key1);
        $iv = base64_decode(DataDSUtils::Aes256IV);
        $decrypted = openssl_decrypt($text, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
        return $decrypted;
    }

    static function getTokenByInterface()
    {

        $client = new Client();

        try {
            $TIMESTAMP = DataDSUtils::GetUTCTimeStamp();

            $url = request()->carprovidertype == "hyundai" ?
                DataDSUtils::HYUNDAI_URL . ':443/na/getTokenByInterface.action' :
                DataDSUtils::KIA_URL . ':443/na/getTokenByInterface.action';
            $str = 'USER_ID=' . DataDSUtils::getUserID() . '&TIMESTAMP=' . $TIMESTAMP . '&FROM=GDSM';

            $AES256Encodev = DataDSUtils::AES256Encode($str);

            // echo $AES256Encodev;
            $response = $client->post($url, [
                'headers' => [
                    'Content-Type' => 'application/json;charset=utf-8',
                ],
                'body' => $AES256Encodev
            ]);

            $responseJson = json_decode($response->getBody()->getContents(), true);
            $token = $responseJson['token'];
            // echo "Token By Interface is: " . $token;
            return $token;
        } catch (Exception $err) {
            //echo $err->getMessage();
            throw $err->getMessage();
        }
        return "";
    }
}
