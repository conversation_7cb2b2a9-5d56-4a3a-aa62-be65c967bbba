<?php

namespace App\Helper;

use App\Models\ProxyItem;
use Exception;
use Illuminate\Support\Facades\Cache;

class ProxyUtils
{

    static function getUserPass($proxyFull) //chuẩn hóa
    {
        try {
            $proxyFull = str_replace("http://", "", $proxyFull);
            $proxyFull = str_replace("https://", "", $proxyFull);
            $arr =  explode("@", $proxyFull);
            if (count($arr) == 1) {
                return "";
            }
            $userPas = $arr[0];
            return $userPas;
        } catch (Exception $e) {
            return "";
        }
    }

    static function getIPPort($proxyFull) //chuẩn hóa
    {
        try {
            $proxyFull = str_replace("http://", "", $proxyFull);
            $proxyFull = str_replace("https://", "", $proxyFull);
            $arr =  explode("@", $proxyFull);

            if (count($arr) == 1) {
                return $arr[0];
            }

            $IPPort = $arr[1];
            return $IPPort;
        } catch (Exception $e) {
            return "";
        }
    }

    static function chooseProxyForAlldataUS()
    {
        $lastProxyAlldataUS = Cache::get('lastProxyAlldataUS');
        $rtProxy = null;
        $proxys = ProxyItem::orderBy('position', 'ASC')->where('note', 'like', '%' . 'alldata_us' . '%')->get();
        $listProxyText = [];

        if (count($proxys) > 0) {
            $rtProxy = $proxys[1]->proxy;

            foreach ($proxys as $itemProxy) {
                array_push($listProxyText, $itemProxy->proxy);
            }

            $indexLast = array_search($lastProxyAlldataUS, $listProxyText);
            if ($indexLast === false || $indexLast + 1 >= count($listProxyText)) {
                $rtProxy = $listProxyText[0];
            } else {
                $rtProxy = $listProxyText[$indexLast + 1];
            }

            Cache::put('lastProxyAlldataUS',  $rtProxy);
        }

        return $rtProxy;
    }

    static function chooseProxyForAutodata()
    {
        $lastProxyAutodata = Cache::get('lastProxyAutodata');
        $rtProxy = null;
        $proxys = ProxyItem::orderBy('position', 'ASC')->where('note', 'like', '%' . 'autodata' . '%')->get();
        $listProxyText = [];

        if (count($proxys) > 0) {
            $rtProxy = $proxys[1]->proxy;

            foreach ($proxys as $itemProxy) {
                array_push($listProxyText, $itemProxy->proxy);
            }

            $indexLast = array_search($lastProxyAutodata, $listProxyText);
            if ($indexLast === false || $indexLast + 1 >= count($listProxyText)) {
                $rtProxy = $listProxyText[0];
            } else {
                $rtProxy = $listProxyText[$indexLast + 1];
            }

            Cache::put('lastProxyAutodata',  $rtProxy);
        }

        return $rtProxy;
    }

    static function chooseProxyForAutodataItaly()
    {
        $lastProxyAutodata = Cache::get('lastProxyAutodata');
        $rtProxy = null;
        $proxys = ProxyItem::orderBy('position', 'ASC')->where('note', 'like', '%' . 'autoitaly' . '%')->get();
        $listProxyText = [];

        if (count($proxys) > 0) {
            $rtProxy = $proxys[1]->proxy;

            foreach ($proxys as $itemProxy) {
                array_push($listProxyText, $itemProxy->proxy);
            }

            $indexLast = array_search($lastProxyAutodata, $listProxyText);
            if ($indexLast === false || $indexLast + 1 >= count($listProxyText)) {
                $rtProxy = $listProxyText[0];
            } else {
                $rtProxy = $listProxyText[$indexLast + 1];
            }

            Cache::put('lastProxyAutodata',  $rtProxy);
        }

        return $rtProxy;
    }
}
