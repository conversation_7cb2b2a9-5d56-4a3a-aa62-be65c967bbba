<?php

namespace App\Http\Controllers\Web;

use App\Helper\SecurityUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\SessionUser;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class LoginController extends Controller
{
    /**
     * create transaction.
     *
     * process transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
        return response()->view('agent/auth/login', []);
    }

    public function perform(Request $request)
    {

        $username = $request->username ?? $request->email;


        if (empty($username)) {

            return response()->view('agent/auth/login', [
                "error_mess" =>  MsgCode::NO_USERNAME_EXISTS[1],
            ]);
        }

        $username =  str_replace(" ", "", $username);


        $dataCheckLogin = [
            'username' =>   $username,
            'password' => $request->password,
        ];


        //B1 xác thực tồn tại
        if (Auth::attempt($dataCheckLogin)) {
            $checkTokenExists = SessionUser::where(
                'user_id',
                Auth::id()
            )->first();
            //B2 tạo token


            $user = User::where('id',  Auth::id())->first();

            if ($user  != null && $user->is_block) {
                return response()->view('agent/auth/login', [
                    "error_mess" =>  MsgCode::YOU_HAVE_BEEN_BLOCKED[1],
                ]);
            }

            SecurityUtils::useDevice($user);

            if (empty($checkTokenExists)) {
                $userSession = SessionUser::create([
                    'token' => Str::random(40),
                    'refresh_token' => Str::random(40),
                    'token_expried' => date('Y-m-d H:i:s',  strtotime('+100 day')),
                    'refresh_token_expried' => date('Y-m-d H:i:s',  strtotime('+365 day')),
                    'user_id' => Auth()->id()
                ]);
            } else {
                $userSession =  $checkTokenExists;
            }


            $host = $_SERVER['HTTP_HOST'];
            $hostWithoutPort = preg_replace('/:\d+$/', '', $host);

            $agenct = urldecode($_SERVER['HTTP_USER_AGENT']);
            $agenct = str_replace(" ", "", $agenct);

            setcookie("ad-web-token", $userSession->token, time() + (86400 * 360), "/", "." . $hostWithoutPort);
            setcookie("ad-web-platform", 'web', time() + (86400 * 360), "/", "." . $hostWithoutPort);
            setcookie("ad-web-device-id", $agenct, time() + (86400 * 360), "/", "." . $hostWithoutPort);

            return redirect('home.index');
            exit;

            return response()->view('agent/auth/login', [
                "error_mess" =>  MsgCode::SUCCESS[1],
            ]);
        }


        return response()->view('agent/auth/login', [
            "error_mess" =>  MsgCode::WRONG_ACCOUNT_OR_PASSWORD[1],
        ]);

        return response()->view('agent/auth/login', []);
    }
}
