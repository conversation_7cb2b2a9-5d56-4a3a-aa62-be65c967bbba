<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\DeviceLogin;
use App\Models\User;

class LogoutController extends Controller
{
    /**
     * Log out account user.
     *
     * @return \Illuminate\Routing\Redirector
     */
    public function perform()
    {

        $host = $_SERVER['HTTP_HOST'];
        $hostWithoutPort = preg_replace('/:\d+$/', '', $host);

        if (app('request')->user != null) {
            $user = app('request')->user;


            $user = User::where('id', $user->id)->first();
            $user->update([
                'platform' => null,
                'device_id' => null,
                'model' => null,
            ]);


            DeviceLogin::where('user_id', $user->id)->update([
                "ip_using" => null,
                "address" => null,
                "device_id" => null,
                "model_name" => null,
                "platform" => null,
                "last_visit_time" => null,
                "app_version" => null,
                "login_time" => null,
            ]);
        }

        setcookie("ad-web-token",  "", time() - 3600, "/", "." . $hostWithoutPort);
        setcookie("ad-web-platform",  "", time() - 3600, "/", "." . $hostWithoutPort);
        setcookie("ad-web-device-id",  "", time() - 3600, "/", "." . $hostWithoutPort);

        return redirect('login');
    }
}
