<?php

namespace App\Http\Controllers\Api\TecDoc;

use App\Exceptions\NasException;
use App\Helper\GetWebData;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Services\TecDocService;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;
use GuzzleHttp\Client;

class TecDocSearchController extends Controller
{
    protected $tecDocService;
    public function __construct(TecDocService $tecDocService)
    {
        $this->tecDocService = $tecDocService;
    }

    /**
     * Search by oem number
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchByOEMNumber(Request $request)
    {
        try {
            $oem_number = $request->oem_number;
            $queryData = GetWebData::queryTecDoc("
                SELECT DISTINCT
                    ARTICLES.ART_ID,
                    ARTICLES.ART_ARTICLE_NR AS ARTICLE_NUMBER,
                    ART_LOOKUP.ARL_TYPE AS TYPE,
                    SUPPLIERS.SUP_ID
                FROM
                    ART_LOOKUP
                INNER JOIN ARTICLES ON ARTICLES.ART_ID = ART_LOOKUP.ARL_ART_ID
                INNER JOIN SUPPLIERS ON SUPPLIERS.SUP_BRAND = ARTICLES.ART_SUP_BRAND
                WHERE
                    ART_LOOKUP.ARL_SEARCH_NUMBER = '$oem_number'
                    AND ART_LOOKUP.ARL_TYPE = 'OENumber'
                ORDER BY
                    ARTICLES.ART_SUP_BRAND
            ");

            $articleNumbers = [];
            $supIds = [];
            foreach ($queryData as $key => $item) {
                $articleNumbers[] = $item['ARTICLE_NUMBER'];
                $supIds[] = $item['SUP_ID'];
            }
            $articleNumbersStr = "'" . implode("', '", $articleNumbers) . "'";
            $supIdsStr = implode(", ", $supIds);

            $newData = $this->tecDocService->getPartInfos($articleNumbersStr, $supIdsStr);
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $newData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }

    /**
     * Get vehicle by vin
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVehiclesByVin(Request $request)
    {
        try {
            $vin = $request->vin;
            $client = new Client();
            // $data = cache()->remember($vin, 60, function () use ($vin, $client) {
            //     $response = $client->get('https://partsapi.ru/api.php', [
            //         'query' => [
            //             'method' => 'VINdecode',
            //             'key' => '39db90a79904d41094e59f9d24db0598',
            //             'vin' => $vin,
            //             'lang' => 'en'
            //         ],
            //     ]);
            //     $data = json_decode($response->getBody()->getContents(), true);
            //     return $data;
            // });
            // $newData = [];
            // if (empty($data)) {
            //     cache()->forget($vin);
            //     $data = [];
            // } else {
            //     foreach ($data['result'] as $key => $value) {
            //         if (is_numeric($key)) {
            //             $newData[] = $value;
            //         }
            //     }
            // }

            $response = $client->post('https://webservice.tecalliance.services/pegasus-3-0/services/TecdocToCatDLB.jsonEndpoint', [
                'headers' => [
                    'accept'             => 'application/json, text/plain, */*',
                    'accept-language'    => 'en-US,en;q=0.9,vi;q=0.8',
                    'cache-control'      => 'no-cache',
                    'content-type'       => 'application/json',
                    'origin'             => 'https://web.tecalliance.net',
                    'pragma'             => 'no-cache',
                    'priority'           => 'u=1, i',
                    'referer'            => 'https://web.tecalliance.net/',
                    'sec-ch-ua'          => '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                    'sec-ch-ua-mobile'   => '?0',
                    'sec-ch-ua-platform' => '"Linux"',
                    'sec-fetch-dest'     => 'empty',
                    'sec-fetch-mode'     => 'cors',
                    'sec-fetch-site'     => 'cross-site',
                    'user-agent'         => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
                    'x-api-key'          => '2BeBXg6KgXfwZr8i2P2L7H5W6ujr4XXd1L4yWFTMuhwamxCTFA3J',
                    'x-catalog'          => 'BvuP4vGac75ecTSwkjqYf',
                    'x-log-tracking-id'  => '34436919-c912-4120-b7bf-bffb972aa7e0'
                ],
                'json' => [
                    'getVehiclesByVIN' => [
                        'country' => 'DE',
                        'lang' => 'en',
                        'vin' => $vin,
                        'provider' => 23365,
                        'manuId' => null,
                        'modelId' => null,
                        'maxVehiclesToReturn' => -1
                    ]
                ]
            ]);
            $data = json_decode($response->getBody()->getContents(), true);
            $newData = $data['data']['matchingVehicles']['array'];
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $newData
            ], 200);
        } catch (ClientException $e) {
            cache()->forget($vin);
            $errorMessage = json_decode($e->getResponse()->getBody()->getContents(), true);
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => $errorMessage['message'],
            ], 500);
        }
    }
}
