<?php

namespace App\Http\Controllers\Api\TecDoc;

use App\Exceptions\NasException;
use App\Helper\GetWebData;
use App\Helper\Helper;
use App\Helper\TecDocUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Services\TecDocService;
use Exception;
use Illuminate\Http\Request;

class TecDocMenuController extends Controller
{
    protected $tecDocService;
    public function __construct(TecDocService $tecDocService)
    {
        $this->tecDocService = $tecDocService;
    }

    /**
     * Get Year
     */
    public function getMakes(Request $request)
    {

        try {
            $queryData = GetWebData::queryTecDoc("
           SELECT
            MFA_ID,
            MFA_BRAND
            FROM
            MANUFACTURERS
            WHERE
            FIND_IN_SET('PC', MFA_TYPE)>0
            AND MFA_MODELS_COUNT > 0 ORDER BY MFA_BRAND");
            $domain = Helper::getDomainCurrent();
            foreach ($queryData as &$item) {
                $item['path'] = $domain . '/api/tecdoc/access-data/media_files/manufacturers_logo/' . $item['MFA_ID'] . '.png';
            }

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $queryData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }

    /**
     * Get Models
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getModels(Request $request)
    {
        try {
            $lng_id = TecDocUtils::LNG_ID;
            $country_id = TecDocUtils::COUNTRY_ID;
            $passenger_car = TecDocUtils::PASSENGER_CAR;
            $mfa_brand_id = $request->mfa_brand_id;
            $queryData = GetWebData::queryTecDoc("
           SELECT
            MS_ID,
            get_text(IFNULL(MS_COUNTRY_SPECIFICS.MSCS_NAME_DES,MODELS_SERIES.MS_NAME_DES), $lng_id)
            AS 'MS_NAME',
            MODELS_SERIES.MS_CI_FROM,
            MODELS_SERIES.MS_CI_TO
            FROM
            MODELS_SERIES
            LEFT OUTER JOIN MS_COUNTRY_SPECIFICS
            ON MS_COUNTRY_SPECIFICS.MSCS_ID = MODELS_SERIES.MS_ID
            AND MS_COUNTRY_SPECIFICS.MSCS_COU_ID = $country_id
            WHERE
            MODELS_SERIES.MS_MFA_ID = $mfa_brand_id
            AND FIND_IN_SET('$passenger_car', MS_TYPE)>0");

            usort($queryData, function ($a, $b) {
                return strcasecmp($a['MS_NAME'], $b['MS_NAME']);
            });

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $queryData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }

    /**
     * Get Model Types 
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getModelTypes(Request $request)
    {
        try {
            $lng_id = TecDocUtils::LNG_ID;
            $country_id = TecDocUtils::COUNTRY_ID;
            $ms_id = $request->ms_id;
            $passenger_car = TecDocUtils::PASSENGER_CAR;
            $queryData = GetWebData::queryTecDoc("
            SELECT
                PASSENGER_CARS.PC_ID,
                CONCAT_WS(' ', MFA_BRAND,
                get_text(IFNULL(MS_COUNTRY_SPECIFICS.MSCS_NAME_DES,MODELS_SERIES.MS_NAME_DES), $lng_id),
                get_text(PASSENGER_CARS.PC_MODEL_DES, $lng_id) ) AS TYPEL,
                PC_COUNTRY_SPECIFICS.PCS_CI_FROM,
                PC_COUNTRY_SPECIFICS.PCS_CI_TO,
                PC_COUNTRY_SPECIFICS.PCS_POWER_KW,
                PC_COUNTRY_SPECIFICS.PCS_POWER_PS,
                PC_COUNTRY_SPECIFICS.PCS_CAPACITY_TAX,
                get_text(PC_COUNTRY_SPECIFICS.PCS_BODY_TYPE, $lng_id) AS BODY_STYLE
                FROM
                PASSENGER_CARS
                INNER JOIN PC_COUNTRY_SPECIFICS ON PC_COUNTRY_SPECIFICS.PCS_PC_ID =
                PASSENGER_CARS.PC_ID
                AND (PC_COUNTRY_SPECIFICS.PCS_COU_ID = $country_id OR
                PC_COUNTRY_SPECIFICS.PCS_COU_ID = 0)
                INNER JOIN MODELS_SERIES ON MODELS_SERIES.MS_ID = $ms_id
                INNER JOIN MANUFACTURERS ON MANUFACTURERS.MFA_ID = PASSENGER_CARS.PC_MFA_ID
                LEFT OUTER JOIN MS_COUNTRY_SPECIFICS
                ON MS_COUNTRY_SPECIFICS.MSCS_ID = MODELS_SERIES.MS_ID
                AND MS_COUNTRY_SPECIFICS.MSCS_COU_ID = $country_id
                WHERE
                PASSENGER_CARS.PC_MS_ID = $ms_id
                AND FIND_IN_SET('$passenger_car', MODELS_SERIES.MS_TYPE)>0");

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $queryData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }

    /**
     * Get Product Categories
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductCategories(Request $request)
    {
        try {
            $lng_id = TecDocUtils::LNG_ID;
            $passenger_car = TecDocUtils::PASSENGER_CAR;
            $pc_id = $request->pc_id;
            $queryData = GetWebData::queryTecDoc("
                WITH RECURSIVE category_path (STR_ID, STR_ID_PARENT, STR_LEVEL, PT_ID, STR_NODE_NAME, STR_TYPE, STR_PATH) AS (
                    SELECT 
                        SEARCH_TREE.STR_ID AS STR_ID,
                        SEARCH_TREE.STR_ID_PARENT AS STR_ID_PARENT,
                        SEARCH_TREE.STR_LEVEL,
                        LINK_PT_STR.PT_ID AS PT_ID,
                        get_text(SEARCH_TREE.STR_DES_ID, $lng_id) AS STR_NODE_NAME,
                        SEARCH_TREE.STR_TYPE AS STR_TYPE,
                        get_text(SEARCH_TREE.STR_DES_ID, $lng_id) AS STR_PATH
                    FROM 
                        SEARCH_TREE
                    INNER JOIN LINK_PT_STR 
                        ON FIND_IN_SET('$passenger_car', LINK_PT_STR.STR_TYPE) > 0
                        AND LINK_PT_STR.STR_ID = SEARCH_TREE.STR_ID
                        AND LINK_PT_STR.PT_ID IN (
                            SELECT DISTINCT LINK_LA_TYP.LAT_PT_ID
                            FROM LINK_LA_TYP
                            WHERE LINK_LA_TYP.LAT_TYP_ID = $pc_id 
                            AND LINK_LA_TYP.LAT_TYPE = '$passenger_car'
                        ) 
                    WHERE 
                        FIND_IN_SET('$passenger_car', SEARCH_TREE.STR_TYPE) > 0 
                        AND SEARCH_TREE.STR_ID_PARENT IS NULL
                    UNION 
                    SELECT 
                        SEARCH_TREE.STR_ID AS STR_ID,
                        SEARCH_TREE.STR_ID_PARENT AS STR_ID_PARENT,
                        SEARCH_TREE.STR_LEVEL,
                        LINK_PT_STR.PT_ID AS PT_ID,
                        get_text(SEARCH_TREE.STR_DES_ID, $lng_id) AS STR_NODE_NAME,
                        SEARCH_TREE.STR_TYPE AS STR_TYPE,
                        CONCAT(cp.STR_PATH, ' > ', get_text(SEARCH_TREE.STR_DES_ID, $lng_id)) AS STR_PATH
                    FROM 
                        category_path AS cp 
                    INNER JOIN SEARCH_TREE
                        ON SEARCH_TREE.STR_ID_PARENT = cp.STR_ID
                        AND FIND_IN_SET('$passenger_car', SEARCH_TREE.STR_TYPE) > 0 
                    INNER JOIN LINK_PT_STR 
                        ON FIND_IN_SET('$passenger_car', LINK_PT_STR.STR_TYPE) > 0
                        AND LINK_PT_STR.STR_ID = SEARCH_TREE.STR_ID
                        AND LINK_PT_STR.PT_ID = cp.PT_ID
                )
                    SELECT DISTINCT
                        category_path.STR_ID,  
                        category_path.STR_ID_PARENT,
                        category_path.STR_LEVEL,
                        category_path.STR_NODE_NAME,
                        category_path.STR_PATH,
                        PASSENGER_CARS.PC_ID AS PC_ID
                    FROM 
                        category_path
                    JOIN PASSENGER_CARS 
                        ON PASSENGER_CARS.PC_ID = $pc_id
                    ORDER BY  
                        category_path.STR_PATH;
            ");

            $data = [];
            $idMap = [];

            foreach ($queryData as $item) {
                unset($item['STR_PATH']);
                $idMap[$item['STR_ID']] = $item;
            }

            foreach ($queryData as $key => $item) {
                if ($item['STR_ID_PARENT']) {
                    if (isset($idMap[$item['STR_ID_PARENT']])) {
                        $parent = &$idMap[$item['STR_ID_PARENT']];
                        $levelKey = 'LEVEL_' . $item['STR_LEVEL'];

                        if (!isset($parent[$levelKey])) {
                            $parent[$levelKey] = [];
                        }

                        $parent[$levelKey][] = &$idMap[$item['STR_ID']];
                    }
                } else {
                    $data[] = &$idMap[$item['STR_ID']];
                }
            }

            foreach ($data as &$item) {
                if (isset($item['LEVEL_2'])) {
                    foreach ($item['LEVEL_2'] as &$subItem) {
                        if (!isset($subItem['LEVEL_3'])) {
                            $subItem['LEVEL_3'] = [];
                        }
                        if (isset($subItem['LEVEL_3'])) {
                            foreach ($subItem['LEVEL_3'] as &$subSubItem) {
                                if (!isset($subSubItem['LEVEL_4'])) {
                                    $subSubItem['LEVEL_4'] = [];
                                }
                            }
                        }
                    }
                }

                if (isset($item['LEVEL_3'])) {
                    foreach ($item['LEVEL_3'] as &$subItem) {
                        if (!isset($subItem['LEVEL_4'])) {
                            $subItem['LEVEL_4'] = [];
                        }
                    }
                }
            }

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $data
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }

    /**
     * Get Brands
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBrands(Request $request)
    {
        try {
            $lng_id = TecDocUtils::LNG_ID;
            $passenger_car = TecDocUtils::PASSENGER_CAR;
            $pc_id = $request->pc_id;
            $str_id = $request->str_id;
            $queryData = GetWebData::queryTecDoc("
                SELECT DISTINCT
                    PRODUCTS.PT_ID,
                    get_text(PRODUCTS.PT_DES_ID, $lng_id) AS 'PRODUCT GROUP',
                    SUPPLIERS.SUP_ID,
                    ARTICLES.ART_SUP_BRAND AS 'BRAND',
                    ARTICLES.ART_ARTICLE_NR AS 'ARTICLE_NUMBER'
                    FROM
                    SEARCH_TREE
                    INNER JOIN LINK_PT_STR ON LINK_PT_STR.STR_ID = $str_id
                    AND FIND_IN_SET('$passenger_car', LINK_PT_STR.STR_TYPE)>0
                    INNER JOIN LINK_LA_TYP ON LINK_LA_TYP.LAT_TYP_ID = $pc_id
                    AND LINK_LA_TYP.LAT_TYPE = '$passenger_car'
                    AND LINK_LA_TYP.LAT_PT_ID = LINK_PT_STR.PT_ID
                    INNER JOIN LINK_ART ON LINK_ART.LA_ID = LINK_LA_TYP.LAT_LA_ID
                    INNER JOIN ARTICLES ON ARTICLES.ART_ID = LINK_ART.LA_ART_ID
                    INNER JOIN SUPPLIERS ON SUPPLIERS.SUP_ID = LINK_LA_TYP.LAT_SUP_ID
                    INNER JOIN PRODUCTS ON PRODUCTS.PT_ID = LINK_LA_TYP.LAT_PT_ID
                    WHERE
                    SEARCH_TREE.STR_ID = $str_id
                    AND FIND_IN_SET('$passenger_car', SEARCH_TREE.STR_TYPE)>0
                    ORDER BY SUP_BRAND
            ");
            $newData = [];
            foreach ($queryData as $key => $item) {
                $data = $this->tecDocService->getPartInfo($item);
                foreach ($data as $value) {
                    $newData[] = $value;
                }
            }

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $newData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }

    /**
     * Get Vehicle Information
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVehicleInformation(Request $request)
    {
        try {
            $lng_id = TecDocUtils::LNG_ID;
            $passenger_car = TecDocUtils::PASSENGER_CAR;
            $country_id = TecDocUtils::COUNTRY_ID;
            $pc_id = $request->pc_id;
            $queryData = GetWebData::queryTecDoc("
                SELECT
                    CONCAT( MFA_BRAND, ' ',
                    get_text(IFNULL(MS_COUNTRY_SPECIFICS.MSCS_NAME_DES,MODELS_SERIES.MS_NAME_DES), $lng_id),
                    ' ',
                    get_text(PASSENGER_CARS.PC_MODEL_DES, $lng_id) ) AS 'Vehicle type',
                    PC_COUNTRY_SPECIFICS.PCS_CI_FROM,
                    PC_COUNTRY_SPECIFICS.PCS_CI_TO,
                    PC_COUNTRY_SPECIFICS.PCS_POWER_KW,
                    PC_COUNTRY_SPECIFICS.PCS_POWER_PS,
                    PC_COUNTRY_SPECIFICS.PCS_CAPACITY_LT,
                    PC_COUNTRY_SPECIFICS.PCS_CAPACITY_TECH,
                    PC_COUNTRY_SPECIFICS.PCS_ABS,
                    PC_COUNTRY_SPECIFICS.PCS_ASR,
                    PC_COUNTRY_SPECIFICS.PCS_NUMBER_OF_CYLINDERS AS Cylinders,
                    PC_COUNTRY_SPECIFICS.PCS_NUMBER_OF_VALVES AS Valves,
                    get_text(PC_COUNTRY_SPECIFICS.PCS_BODY_TYPE, $lng_id) AS 'Body style',
                    get_text(PC_COUNTRY_SPECIFICS.PCS_ENGINE_TYPE, $lng_id) AS
                    'Engine type',
                    get_text(PC_COUNTRY_SPECIFICS.PCS_GEAR_TYPE, $lng_id) AS PC_GEAR_TYPE,
                    get_text(PC_COUNTRY_SPECIFICS.PCS_DRIVE_TYPE, $lng_id) AS 'Drive type',
                    get_text(PC_COUNTRY_SPECIFICS.PCS_BRAKE_TYPE, $lng_id) AS PC_BRAKE_TYPE,
                    get_text(PC_COUNTRY_SPECIFICS.PCS_FUEL_TYPE, $lng_id) AS 'Fuel type',
                    get_text(PC_COUNTRY_SPECIFICS.PCS_CATALYSATOR_TYPE, $lng_id) AS
                    PC_CATALYSATOR_TYPE,
                    get_text(PC_COUNTRY_SPECIFICS.PCS_FUEL_MIXTURE, $lng_id)
                    AS 'Fuel preparation',
                    (SELECT
                    GROUP_CONCAT(ENGINES.ENG_CODE)
                    FROM
                    ENGINES
                    JOIN LINK_ENGINE_TYPE ON ENGINES.ENG_ID=LINK_ENGINE_TYPE.LET_ENG_ID
                    WHERE
                    LINK_ENGINE_TYPE.LET_TYPE_ID = PASSENGER_CARS.PC_ID
                    AND LINK_ENGINE_TYPE.LET_TYPE = '$passenger_car' ) AS 'Engine codes'
                    FROM
                    PASSENGER_CARS
                    INNER JOIN PC_COUNTRY_SPECIFICS ON PC_COUNTRY_SPECIFICS.PCS_PC_ID =
                    PASSENGER_CARS.PC_ID
                    AND (PC_COUNTRY_SPECIFICS.PCS_COU_ID = $country_id OR
                    PC_COUNTRY_SPECIFICS.PCS_COU_ID = 0)
                    INNER JOIN MODELS_SERIES ON MODELS_SERIES.MS_ID = PASSENGER_CARS.PC_MS_ID
                    INNER JOIN MANUFACTURERS ON MANUFACTURERS.MFA_ID = PASSENGER_CARS.PC_MFA_ID
                    LEFT OUTER JOIN MS_COUNTRY_SPECIFICS
                    ON MS_COUNTRY_SPECIFICS.MSCS_ID = MODELS_SERIES.MS_ID
                    AND MS_COUNTRY_SPECIFICS.MSCS_COU_ID = $country_id
                    WHERE
                    PASSENGER_CARS.PC_ID = $pc_id
            ");
            $vehicle = $queryData[0];

            $transformedData = [
                [
                    'name' => 'Vehicle type',
                    'value' => $vehicle['Vehicle type'],
                ],
                [
                    'name' => 'Model year',
                    'value' => TecDocUtils::formatDate($vehicle['PCS_CI_FROM']) . ' - ' . TecDocUtils::formatDate($vehicle['PCS_CI_TO']),
                ],
                [
                    'name' => 'Performance',
                    'value' => TecDocUtils::formatPerformance($vehicle['PCS_POWER_KW'], $vehicle['PCS_POWER_PS']),
                ],
                [
                    'name' => 'Capacity',
                    'value' => TecDocUtils::formatCapacity($vehicle['PCS_CAPACITY_TECH'], $vehicle['PCS_CAPACITY_LT']),
                ],
                [
                    'name' => 'Cylinders',
                    'value' => $vehicle['Cylinders'],
                ],
                [
                    'name' => 'Valves',
                    'value' => $vehicle['Valves'],
                ],
                [
                    'name' => 'Body style',
                    'value' => $vehicle['Body style'],
                ],
                [
                    'name' => 'Engine type',
                    'value' => $vehicle['Engine type'],
                ],
                [
                    'name' => 'Drive type',
                    'value' => $vehicle['Drive type'],
                ],
                [
                    'name' => 'Fuel type',
                    'value' => $vehicle['Fuel type'],
                ],
                [
                    'name' => 'Engine codes',
                    'value' => $vehicle['Engine codes'],
                ],
                [
                    'name' => 'Fuel preparation',
                    'value' => $vehicle['Fuel preparation'],
                ],
            ];

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $transformedData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }
}
