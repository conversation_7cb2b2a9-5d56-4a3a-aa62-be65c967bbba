<?php

namespace App\Http\Controllers\Api\TecDoc;

use App\Http\Controllers\Controller;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;


class TecDocFileController extends Controller
{
    /**
     * Get file
     * 
     * @param $path
     * @return void
     */
    public function getFile($path)
    {
        try {
            $client = new Client();
            $response = $client->get('http://nas.fhost.ca/WebDataHandle/get-file-data.php', [
                'query' => [
                    'service' => 'TECDOC',
                    'file' => '/'.$path
                ],
            ]);
            $contentType = $response->getHeaderLine('Content-Type');

            header('Content-Type: ' . $contentType);
            while (!$response->getBody()->eof()) {
                echo $response->getBody()->read(1024);
                flush();
            }
        } catch (ClientException $e) {
            echo "File Not Found";
        }
    }
}
