<?php

namespace App\Http\Controllers\Api\TecDoc;

use App\Exceptions\NasException;
use App\Helper\GetWebData;
use App\Helper\Helper;
use App\Helper\TecDocUtils;
use App\Helper\WebDataUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Services\TecDocService;
use Illuminate\Http\Request;

class TecDocInformationController extends Controller
{
    protected $tecDocService;
    public function __construct(TecDocService $tecDocService)
    {
        $this->tecDocService = $tecDocService;
    }

    /**
     * Get Part Info
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPartInfo(Request $request)
    {
        try {
            $queryData = $this->tecDocService->getPartInfo($request);

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $queryData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }

    /**
     * Get Manufacter Address
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getManufacterAddress(Request $request)
    {
        try {
            $sup_id = $request->sup_id;
            $queryData = GetWebData::queryTecDoc("
                SELECT SUP_ID,SUPPLIER_ADDRESSES.COU_ID,CITY1,CITY2,STREET1,STREET2,EMAIL,HOMEPAGE AS INTERNET,NAME1,NAME2,POST_OFFICE_BOX,POSTAL_CODE_CITY,POSTAL_CODE_POB,POSTAL_CODE_WHOLESALER AS POSTAL_CODE_CLIENT,POSTAL_COUNTRY_CODE,FAX,TELEPHONE,
                get_text(`TYPE_OF_ADDRESS_DES_ID`, 4)
                AS 'ADDRESS_NAME', IFNULL(COUNTRIES.COU_ISOCODE2, 'DE') AS 'COUNTRY'
                    FROM SUPPLIER_ADDRESSES
                    LEFT OUTER JOIN COUNTRIES ON COUNTRIES.COU_CODE = SUPPLIER_ADDRESSES.POSTAL_COUNTRY_CODE
                    WHERE SUP_ID = $sup_id 
            ");
            $newData = [];
            if (count($queryData) > 1) {
                foreach ($queryData as $item) {
                    if ($item['COUNTRY'] === 'DE') {
                        $addressName = $item['ADDRESS_NAME'];
                        if (!isset($newData[$addressName]) || ($newData[$addressName]['COU_ID'] !== '63' && $item['COU_ID'] === '63')) {
                            $newData[$addressName] = $item;
                        }
                    }
                }

                if (empty($newData)) {
                    $newData = $queryData;
                }
            } else {
                $newData = $queryData;
            }
            $newData = array_values($newData);
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $newData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }

    /**
     * Get trade number
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTradeAndReplacements(Request $request)
    {
        try {
            $art_id = $request->art_id;
            $queryData = GetWebData::queryTecDoc("
                SELECT
                    (SELECT GROUP_CONCAT(SUPERSEDED_ARTICLES.SUA_NUMBER)
                    FROM SUPERSEDED_ARTICLES
                    INNER JOIN ARTICLES ON ARTICLES.ART_ID = SUPERSEDED_ARTICLES.SUA_ART_ID
                    WHERE SUPERSEDED_ARTICLES.SUA_ART_ID = $art_id) AS REPLACES,

                    (SELECT GROUP_CONCAT(ARTICLES.ART_ARTICLE_NR)
                    FROM SUPERSEDED_ARTICLES
                    INNER JOIN ARTICLES ON ARTICLES.ART_ID = SUPERSEDED_ARTICLES.SUA_ART_ID
                    WHERE SUPERSEDED_ARTICLES.SUA_NEW_ART_ID = $art_id) AS REPLACES_BY,

                    (SELECT GROUP_CONCAT(ARL_DISPLAY_NR)
                    FROM ART_LOOKUP
                    INNER JOIN ARTICLES ON ARTICLES.ART_ID = ART_LOOKUP.ARL_ART_ID
                    WHERE ART_LOOKUP.ARL_ART_ID = $art_id 
                    AND ART_LOOKUP.ARL_BRA_ID = ARTICLES.ART_SUP_ID 
                    AND ART_LOOKUP.ARL_TYPE = 'TradeNumber') AS TRADE_NUMBER;
            ");

            foreach ($queryData as &$item) {
                if ($item["REPLACES"]) {
                    $item["REPLACES"] = explode(",", $item["REPLACES"]);
                } else {
                    $item["REPLACES"] = [];
                }

                if ($item["REPLACES_BY"]) {
                    $item["REPLACES_BY"] = explode(",", $item["REPLACES_BY"]);
                } else {
                    $item["REPLACES_BY"] = [];
                }

                if ($item["TRADE_NUMBER"]) {
                    $item["TRADE_NUMBER"] = explode(",", $item["TRADE_NUMBER"]);
                } else {
                    $item["TRADE_NUMBER"] = [];
                }
            }

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $queryData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }

    /**
     * Get file by article id
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFileByArticleId(Request $request)
    {
        try {
            $art_id = $request->art_id;
            $domain = Helper::getDomainCurrent();
            $queryData = GetWebData::queryTecDoc("
                SELECT DISTINCT
                    ART_MEDIA_INFO.ART_MEDIA_TYPE AS TYPE,
                    (CASE
                    WHEN ART_MEDIA_INFO.ART_MEDIA_TYPE = 'URL'
                    THEN ART_MEDIA_INFO.ART_MEDIA_HIPPERLINK
                    WHEN ART_MEDIA_INFO.ART_MEDIA_TYPE = 'PDF'
                    THEN CONCAT_WS('/', 'PDF', ART_MEDIA_INFO.ART_MEDIA_SUP_ID,
                    ART_MEDIA_INFO.ART_MEDIA_FILE_NAME)
                    ELSE
                    CONCAT_WS('/', 'IMAGE', ART_MEDIA_INFO.ART_MEDIA_SUP_ID,
                    ART_MEDIA_INFO.ART_MEDIA_FILE_NAME )
                    END) AS PATH
                    FROM
                    ART_MEDIA_INFO
                    WHERE
                    ART_MEDIA_INFO.ART_MEDIA_ART_ID = $art_id
            ");

            foreach ($queryData as &$item) {
                if ($item['TYPE'] != 'URL') {
                    $item['PATH'] = $domain . '/api/tecdoc/access-data/media_files/' . $item['PATH'];

                    if (strpos($item['PATH'], 'IMAGE') !== false) {
                        $item['PATH'] = str_replace('IMAGE', 'IMAGES', $item['PATH']);
                    }
                }
            }

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $queryData
            ], 200);
        } catch (NasException $e) {
            return response()->json([
                'code' => 500,
                'success' => false,
                'msg_code' => MsgCode::SERVER_ERROR[0],
                'msg' => MsgCode::SERVER_ERROR[1],
            ], 500);
        }
    }
}
