<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Services\UploadImageBackblazeService;
use App\Services\UploadImageService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * @group  Upload ảnh
 */
class UploadImageController extends Controller
{


    /**
     * Upload 1 ảnh
     * @bodyParam image file required File ảnh
     */
    public function uploadV2(Request $request)
    {

        if (empty($request->image)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::UNABLE_TO_FIND_THE_UPLOAD_IMAGE[0],
                'msg' => MsgCode::UNABLE_TO_FIND_THE_UPLOAD_IMAGE[1],
            ], 400);
        }

        // if(!exif_imagetype($request->image)) {
        //     return response()->json([
        //         'code' => 400,
        //         'success' => false,
        //         'msg_code' => MsgCode::INVALID_PHOTO[0],
        //         'msg' => MsgCode::INVALID_PHOTO[1],
        //     ], 400);
        // }
        $imageUrl =   UploadImageBackblazeService::uploadImage($request->image, $request->type);
        // $imageUrl = UploadImageService::uploadImage($request->image->getRealPath());

        return response()->json([
            'code' => 201,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $imageUrl
        ], 201);
    }
    /**
     * Upload 1 ảnh
     * @bodyParam image file required File ảnh
     */
    public function upload(Request $request)
    {



        if (empty($request->image)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::UNABLE_TO_FIND_THE_UPLOAD_IMAGE[0],
                'msg' => MsgCode::UNABLE_TO_FIND_THE_UPLOAD_IMAGE[1],
            ], 400);
        }
        // if(!exif_imagetype($request->image)) {
        //     return response()->json([
        //         'code' => 400,
        //         'success' => false,
        //         'msg_code' => MsgCode::INVALID_PHOTO[0],
        //         'msg' => MsgCode::INVALID_PHOTO[1],
        //     ], 400);
        // }
        // $imageUrl =   UploadImageBackblazeService::uploadImage($request->image, $request->type);
        try {
            $imageUrl = UploadImageService::uploadImage($request->image->getRealPath(), $request->type);

            return response()->json([
                'code' => 201,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $imageUrl
            ], 201);
        } catch (Exception $e) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' =>  $e->getMessage(),
            ], 400);
        }
    }
}
