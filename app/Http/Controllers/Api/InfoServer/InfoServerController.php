<?php

namespace App\Http\Controllers\Api\InfoServer;

use App\Helper\Helper;
use App\Helper\IPUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Illuminate\Http\Request;


/**
 * @group  InfoServer/Đại lý
 */

class InfoServerController extends Controller
{


    /**
     * getMAcAddressExec
     * 
     * 
     */
    public function getMAcAddressExec(Request $request)
    {
     

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  IPUtils::getIP()
        ], 200);
    }


  
}
