<?php

namespace App\Http\Controllers\Api\HaynesPro;


use App\Helper\HaynesUtilsWeb;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;


/**
 * @group  Haynespro/Haynespro
 */
class HaynesProController extends Controller
{


    /**
     * Menu
     */
    public function makes(Request $request)
    {


        if ( $request->user != null &&  $request->user->allow_mobile == false ) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_MOBILE[0],
                'msg' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_MOBILE[1],
            ], 400);
        }

        try {
            $response1 = HaynesUtilsWeb::getResponse("https://www.workshopdata.com/touch/site/layout/makesOverview", "GET", null,null, $request, true);
        } catch (Exception $e) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                'msg' => MsgCode::HAYNES_OF_MAINTENANCE[1],
            ], 400);
        }

        try {


            $listMakes = [];

            $dom = HtmlDomParser::str_get_html($response1->getBody());
            $makesEle = $dom->find("#makes", 0)->children();

            foreach ($makesEle  as $make) {
                $a =  $make->getElementByTagName('a');

                $img = $make->find('img');

                $name = str_replace('   t', '', $a->text());
                $name = str_replace(' ', '', $name);
                $name =  str_replace(array("\r", "\n", "\t", "\v"), '',  $name);



                $link =  $a->getAttribute('href');
                $linkImage =  $a->children(0)->children(0)->getAttribute('src');

                $indexMake = strpos($link, 'makeId');
                $makeId = substr($link, $indexMake + 7);

                array_push($listMakes, [
                    'id' => $makeId,
                    'name' => $name,
                    'link' => $link,
                    'image_url' => str_replace(['.svgz','.svg'], ".png", $linkImage),
                ]);
            }
        } catch (Exception $e) {
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $listMakes
        ], 200);
    }

    /**
     * Models
     */
    public function models(Request $request, $makeId)
    {

        try {
            $response1 = HaynesUtilsWeb::getResponse("https://www.workshopdata.com/touch/site/layout/modelOverview?makeId=" . $makeId);
        } catch (Exception $e) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                'msg' => MsgCode::HAYNES_OF_MAINTENANCE[1],
            ], 400);
        }

        try {


            $listMakes = [];

            $dom = HtmlDomParser::str_get_html($response1->getBody());
            $modelsEle = $dom->find("#models", 0)->children();

            foreach ($modelsEle  as $make) {
               

                $img = $make->find('img');

                $p =  $make->getElementByTagName('p');
                $name = str_replace('   t', '', $p->text());
                $name = str_replace(' ', '', $name);
                $name =  str_replace(array("\r", "\n", "\t", "\v"), '',  $name);

                $span =  $make->getElementByTagName('span');
                $year = str_replace('   t', '', $span->text());
                $year = str_replace(' ', '', $year);
                $year =  str_replace(array("\r", "\n", "\t", "\v"), '',  $year);


                $a =  $make->getElementByTagName('a');
                $link =  $a->getAttribute('href');
                $linkImage =  $a->children(0)->children(0)->getAttribute('src');

                $indexMake = strpos($link, 'makeId');
                $makeId = substr($link, $indexMake + 7);

                $indexModel = strpos($link, 'modelGroupId');
                $modelId = substr($link, $indexModel + 13);
                $modelId = substr($modelId,0 , strpos($modelId, '&'));

                array_push($listMakes, [
                    'makeId' => $makeId,
                    'modelId' => $modelId,
                    'name' => $name,
                    'year' => $year,
                    'link' => $link,
                    'image_url' => str_replace(['.svgz','.svg'], ".png", $linkImage),
                ]);
            }
        } catch (Exception $e) {
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $listMakes
        ], 200);
    }
}
