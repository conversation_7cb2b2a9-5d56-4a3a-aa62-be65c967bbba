<?php

namespace App\Http\Controllers\Api\AutoData;

use App\Helper\AllDataUtils;
use App\Helper\AutoDataUtilsWeb;
use App\Helper\Helper;
use App\Helper\IPUtils;
use App\Http\Controllers\Api\DataLeechControllerForm3Controller;
use App\Http\Controllers\Controller;
use App\Jobs\LoggerFailJob;
use App\Models\MsgCode;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;


/**
 * @group  Admin/Log
 */
class AutoDataController extends Controller
{

    /**
     * Thông tin server
     */
    public function indexGet(Request $request, $slug)
    {

        try {
            $response0 = AutoDataUtilsWeb::getResponse(
                "https://workshop.autodata-group.com/selection/save-in-jobfolder/0/undefined",
                'POST',
                [
                    "manufacturer_id" => 'AUD0',
                    "model_id" =>   '7000029'
                ]
            );
        } catch (Exception $e) {
        }

        $tailLink = $_SERVER["REQUEST_URI"];
        $link = AutoDataUtilsWeb::PATH_AUTODATA .  $tailLink;

        $response1 = AutoDataUtilsWeb::getResponse($link);

        echo AutoDataUtilsWeb::remove_element_reponse($response1, $request);
    }

    /**
     * Thông tin server
     */
    public function indexPost(Request $request)
    {
        
        $tailLink = $_SERVER["REQUEST_URI"];
        $link = AutoDataUtilsWeb::PATH_AUTODATA .  $tailLink;


        $body = $request->all();
        $response1 = AutoDataUtilsWeb::getResponse($link, 'POST', $body);

        echo AutoDataUtilsWeb::remove_element_reponse($response1, $request);
        return;
    }

    /**
     * Thông tin server
     */
    public function index2(Request $request, $slug)
    {

        $tailLink = $_SERVER["REQUEST_URI"];
        $link = AutoDataUtilsWeb::PATH_AUTODATA .  $tailLink;

        $ch = curl_init($link);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_exec($ch);
        $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        header('Content-type: ' .  $content_type);

        //    echo $output = curl_exec($ch);
        return response(curl_exec($ch))->header('Content-type',  $content_type);
        curl_close($ch);
    }


    /**
     * Menu
     */
    public function menu_main(Request $request, $manufacturerId, $modelId)
    {
       
        try {
            $response0 = AutoDataUtilsWeb::getResponse(
                "https://workshop.autodata-group.com/selection/save-in-jobfolder/0/undefined",
                'POST',
                [
                    "manufacturer_id" => $manufacturerId,
                    "model_id" =>   $modelId
                ]
            );
        } catch (Exception $e) {
        }
      
        try {
            $response1 = AutoDataUtilsWeb::getResponse("https://workshop.autodata-group.com/w1/vehicles/$manufacturerId/$modelId");
        } catch (Exception $e) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::AUTODATA_OF_MAINTENANCE[0],
                'msg' => MsgCode::AUTODATA_OF_MAINTENANCE[1],
            ], 400);
        }
        header('Content-type: ' .  $response1->getHeader('Content-type')[0] ?? "");

        try {


            $dom = HtmlDomParser::str_get_html($response1->getBody());
            $extra_information_eles = $dom->find("#vueApp > category-page > div.extra-information > ul > li ");


            $extra_informations = [];
            foreach ($extra_information_eles  as $extra_information) {
                $h2 =  $extra_information->getElementByTagName('h2');

                $li_in_extras = $extra_information->find('li');


                $item_extra_informations = [];
                foreach ($li_in_extras  as   $li_in_extra) {

                    $ale =  $li_in_extra->getElementByTagName('a');
                    $link =  $ale == null ? "" : $ale->getAttribute('href');

                    $query_str = parse_url($link, PHP_URL_QUERY);
                    parse_str($query_str, $query_params);

                    array_push($item_extra_informations, [
                        'title' =>   $ale == null ? "" : $ale->innertext,
                        'link' =>  $link,
                        'class' => $li_in_extra == null ? "" : $li_in_extra->getAttribute('class'),
                        "route_name" => $query_params['route_name'] ?? "",
                        "module" => $query_params['module'] ?? "",
                    ]);
                }

                $image = null;
                $imageE =   $dom->find("#vueApp > category-page > div.extra-information > ul > li.vehicle-details.matchHeight > div > div");
                if ($imageE != null && count($imageE) > 0) {
                    $image = $imageE[0]->outertext;
                }


                array_push($extra_informations, [
                    'title' =>  $h2->innertext,
                    'items' => $item_extra_informations
                ]);
            }

            ///////TECHNICAL INFORMATION
            $techical_informations = [];

            $techical_information_eles = $dom->find("#vehicleSelection_expertview > ul > li");


            foreach ($techical_information_eles  as     $techical_information_ele) {
                $h2 =  $techical_information_ele->getElementByTagName('h2');
                $class =  $h2->getAttribute('class');
                $ale =  $techical_information_ele->getElementByTagName('a');


                $item_ts = [];

                $items_techinfos  = $techical_information_ele->find('li');



                foreach ($items_techinfos  as     $items_techinfo) {

                    $ele =  $items_techinfo->getElementByTagName('a');

                    $link = $ele == null ? "" : $ele->getAttribute('href');
                    $query_str = parse_url($link, PHP_URL_QUERY);
                    parse_str($query_str, $query_params);

                    if (!str_contains($link, "autodata-training.com/",)) {
                        array_push($item_ts, [
                            'title' => $ele == null ? "" : trim($ele->innertext),
                            'link' =>   $link,
                            "route_name" => $query_params['route_name'] ?? "",
                            "module" => $query_params['module'] ?? "",
                        ]);
                    }
                }

                array_push($techical_informations, [
                    'title' =>  $h2->innertext,
                    'class' => $class,
                    'items' => $item_ts
                ]);
            }


            if ($image != null && strlen($image) > 100) {
                $arrimage = (explode("url('", $image))[1];
                $indexTai = strpos($arrimage, "');");

                $image = substr($arrimage, 0,  $indexTai);
            }

            // echo   $response1->getBody();
            $data = [
                'image' => $image,
                'extra_information_eles' =>  $extra_informations,
                'techical_informations' => $techical_informations

            ];
        } catch (Exception $e) {
            array_push($extra_informations, [
                'title' =>  "ERROR",
                'items' => []
            ]);

            $data = [
                'image' => $image ?? "",
                'extra_information_eles' =>  $extra_informations,
                'techical_informations' => []
            ];
        }


        return response()->json($data, 200);
    }


    public function engines(Request $request, $manufacturerId, $modelId)
    {

        try {
            $response0 = AutoDataUtilsWeb::getResponse(
                "https://workshop.autodata-group.com/selection/save-in-jobfolder/0/undefined",
                'POST',
                [
                    "manufacturer_id" => $manufacturerId,
                    "model_id" =>   $modelId
                ]
            );
        } catch (Exception $e) {
        }

        $module = request('module');
        $route_name = request('route_name');

        $response1 = AutoDataUtilsWeb::getResponse("https://workshop.autodata-group.com/w1/manufacturers/$manufacturerId/$modelId/engines?route_name=$route_name&module=$module");

        header('Content-type: ' .  $response1->getHeader('Content-type')[0] ?? "");



        $dom = HtmlDomParser::str_get_html($response1->getBody());
        $ecLeft = $dom->find('div[class=ecLeft]', 0);
        $lu = $ecLeft->find('ul', 0);
        $lis = $lu->children;

        $listEngines = array();
        foreach ($lis as $eleLi) {

            $ale =  $eleLi->getElementByTagName('a');

            array_push($listEngines, [
                "title" => trim($ale->innertext),
                "manufacturer" => $ale->getAttribute('manufacturer'),
                "litres" => $ale->getAttribute('litres'),
                "fuel" => $ale->getAttribute('fuel'),
                "body" => $ale->getAttribute('body'),
                "freetext" => $ale->getAttribute('freetext'),
                "module" => $ale->getAttribute('module'),
                "vehicletype" => $ale->getAttribute('vehicletype'),
            ]);
        }
        return response()->json($listEngines, 200);
    }


    public function engine_codes(Request $request, $manufacturerId, $modelId)
    {


        $module = request('module');
        $route_name = request('route_name');


        if (empty($module) || empty($route_name)) {
            echo "query param not found";
            return;
        }

        try {
            $response0 = AutoDataUtilsWeb::getResponse(
                "https://workshop.autodata-group.com/selection/save-in-jobfolder/0/undefined",
                'POST',
                [
                    "manufacturer_id" => $manufacturerId,
                    "model_id" =>   $modelId
                ]
            );
        } catch (Exception $e) {
        }

        $manufacturer = $request->manufacturer;
        $body = $request->body;
        $module = $request->module;
        $litres = $request->litres;
        $fuel = $request->fuel;
        $freetext = $request->freetext;
        $vehicletype = $request->vehicletype;

        $response1 = AutoDataUtilsWeb::getResponse(
            "https://workshop.autodata-group.com/w1/manufacturers/$manufacturerId/$modelId/engines/codes",
            'POST',
            [
                "manufacturer" =>  $manufacturer,
                "body" => $body,
                "module" => $module,
                "litres" => $litres,
                "fuel" => $fuel,
                "freetext" => $freetext,
                "vehicletype" => $vehicletype,
            ]
        );

        header('Content-type: ' .  $response1->getHeader('Content-type')[0] ?? "");

        $dom = HtmlDomParser::str_get_html($response1->getBody());

        $table = $dom->find('table', 0);

        $tbody = $table->find('tbody', 0);

        $trs = $tbody->children;

        function handleStringTd($str)
        {
            $str =  str_replace("\\\"", "", $str);
            $str =  str_replace("<\\/td>\\n", "", $str);
            $str = str_replace("\\n", "", $str);
            $str = trim($str);
            $str = preg_replace('!\s+!', ' ', $str);
            return $str;
        }

        $listEngineCodes = array();
        foreach ($trs as $engineTd) {

            $t0 =  $engineTd->find('td')[0];
            $t1 =  $engineTd->find('td')[1];
            $t2 =  $engineTd->find('td')[2];
            $t3 =  $engineTd->find('td')[3];


            array_push($listEngineCodes, [
                "enabled" => str_contains($engineTd->getAttribute('class'), "disabled") ? false : true,
                "engine_code_nid" => handleStringTd($engineTd->getAttribute('engine-code-nid'),),
                "vehicle_id" => handleStringTd($engineTd->getAttribute('mecnid'),),
                "engine_name" => handleStringTd($engineTd->getAttribute('data-vechicle-nid'),),
                "engine_id" => handleStringTd($t0->innertext),
                "eng_code_2" => handleStringTd($t1->innertext),
                "eng_code_3" => handleStringTd($t2->innertext),
                "year" => handleStringTd($t3->innertext),

                "route_name" => $route_name,
                "module_id" =>    $module

            ]);
        }
        return response()->json($listEngineCodes, 200);
    }


    // /w1/vehicle-selection/mid/AUD00277
    // vehicle_id: AUD00277
    // engine_id: NP
    // engine_name: NP
    // route_name: engine-oil
    // module_id: TD
    // back: 

    // /w1/vehicle-selection/mid/AUD08181
    // vehicle_id: AUD08181
    // engine_id: 
    // engine_name: 
    // route_name: service-illustrations
    // module_id: SILL
    // back: 

    // vehicle_id: BMW30790
    // engine_id: B38 K15A
    // engine_name: B38 K15A
    // route_name: engine-oil
    // module_id: TD
    // back: 

    // /w1/vehicle-selection/mid/BMW00729

    public function mid_end(Request $request, $vehicleId)
    {


        $domain = Helper::getDomainCurrentWithoutSubAndNoHttp();
        $response1 = AutoDataUtilsWeb::getResponse(
            "https://main.$domain/w1/vehicle-selection/mid/$vehicleId",
            'POST',
            [
                "vehicle_id" => $request->vehicle_id,
                "engine_id" => $request->engine_id,
                "engine_name" => $request->engine_name,
                "route_name" => $request->route_name,
                "module_id" => $request->module_id,
                "back" => null,
            ]
        );

        echo AutoDataUtilsWeb::remove_element_reponse($response1, $request);
    }


    public function data_web_view(Request $request)
    {

        $vehicle_id = request('vehicle_id');
        $engine_id = request('engine_id');
        $engine_name = request('engine_name');
        $route_name = request('route_name');
        $module_id = request('module_id');
        $manufacturerId = request('manufacturerId');
        $modelId = request('modelId');
        $back = request('module_id');

        try {
            $response0 = AutoDataUtilsWeb::getResponse(
                "https://workshop.autodata-group.com/selection/save-in-jobfolder/0/undefined",
                'POST',
                [
                    "manufacturer_id" => $manufacturerId,
                    "model_id" =>   $modelId
                ]
            );
        } catch (Exception $e) {
        }

        try {
            $response1 = AutoDataUtilsWeb::getResponse(
                "https://workshop.autodata-group.com/w1/vehicle-selection/mid/$vehicle_id",
                'POST',
                [
                    "vehicle_id" => $vehicle_id,
                    "engine_id" => $engine_id,
                    "engine_name" => $engine_name,
                    "route_name" => $route_name,
                    "module_id" => $module_id,
                    "back" => null,
                ]
            );
        } catch (Exception $e) {
        }

        // $response1 = AutoDataUtilsWeb::getResponse(
        //     "https://workshop.autodata-group.com/w1/vehicles/variants/service-brakes/$vehicle_id?route_name=$route_name&module=$module_id",
        // );
        $domain = Helper::getDomainCurrent();
        $newURL =  "$domain/w1/vehicles/variants/service-brakes/$vehicle_id?route_name=$route_name&module=$module_id";
        header('Location: ' . $newURL);
        exit();
        //  echo AutoDataUtilsWeb::remove_element_reponse($response1);
    }

    ///w1/vehicle-selection/mid/AUD08181

    // vehicle_id: AUD08181
    // engine_id: 
    // engine_name: 
    // route_name: service-brakes
    // module_id: TD
    // back: 
}
