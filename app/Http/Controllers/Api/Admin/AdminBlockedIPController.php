<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlockedIp;
use App\Models\Category;
use App\Models\HistoryAccess;
use App\Models\HistoryBlockIP;
use App\Models\MsgCode;
use App\Models\IPItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

/**
 * @group  User/IP
 */
class AdminBlockedIPController extends Controller
{

    /**
     * Lịch sử block
     */
    public function getAllHistoryBlockIp(Request $request)
    {

        $blocks = HistoryBlockIP::orderBy('id', 'DESC')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $blocks,
        ], 200);
    }

    /**
     * Lịch sử access
     */
    public function getAllHistoryAccess(Request $request)
    {

        $blocks = HistoryAccess::orderBy('id', 'DESC')->paginate(20);;

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $blocks,
        ], 200);
    }

    /**
     * Danh sách ip đã bị block
     */
    public function getAll(Request $request)
    {

        $blocks = BlockedIp::orderBy('id', 'DESC')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $blocks,
        ], 200);
    }

    /**
     * Thêm block IP
     */
    public function create(Request $request)
    {

        $hasIP = BlockedIp::where('ip', $request->ip)->first();

        if ($hasIP  != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Đã có IP này",
            ], 400);
        }
        $created = BlockedIp::create([
            'ip' => $request->ip,
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $created,
        ], 200);
    }

    /**
     * Cập nhật IP
     */
    public function update(Request $request, $id)
    {


        $hasIP = BlockedIp::where('id', $id)->first();

        if ($hasIP  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không tồn tại",
            ], 400);
        }
        $hasIP->update([
            'ip' => $request->ip,
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $hasIP,
        ], 200);
    }


    /**
     * Remove IP
     */
    public function delete(Request $request, $id)
    {


        $hasIP = BlockedIp::where('id', $id)->first();

        if ($hasIP  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không tồn tại",
            ], 400);
        }
        Cache::forget("blocked_ip" .  $hasIP->ip);
        $hasIP->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
