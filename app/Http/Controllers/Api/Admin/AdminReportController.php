<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\PlanUtils;
use App\Helper\RenewUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\OrderPlan;
use App\Models\RenewalHistory;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * @group  Admin/báo cáo
 */

class AdminReportController extends Controller
{
    /**
     * Báo cáo tổng quan
     * 
     * @queryParam  date_from
     * @queryParam  date_to
     * @queryParam  date_from_compare
     * @queryParam  date_to_compare
     * 
     */
    public function overview(Request $request)
    {
        function handle_data(Request $request, $dateFrom,  $dateTo)
        {

            $MONEY_ALLDATA  = 0;
            $MONEY_AUTODATA  = 0;
            $MONEY_HAYNESPRO  = 0;
            $MONEY_IDENTIFIX  = 0;
            $MONEY_MITCHELL_REPAIR_CENTER  = 0;
            $MONEY_MITCHELL_PRODEMAND  = 0;
            $MONEY_HAYNESPRO_TRUCK  = 0;
            $MONEY_PARTSLINK24  = 0;
            $MONEY_KDSGDS  = 0;
            $MONEY_ETKA  = 0;
            $MONEY_TECDOC  = 0;
            $MONEY_FORD_PTS  = 0;

            $COUNT_ALLDATA  = 0;
            $COUNT_AUTODATA  = 0;
            $COUNT_HAYNESPRO  = 0;
            $COUNT_IDENTIFIX  = 0;
            $COUNT_MITCHELL_REPAIR_CENTER  = 0;
            $COUNT_MITCHELL_PRODEMAND = 0;
            $COUNT_HAYNESPRO_TRUCK  = 0;
            $COUNT_PARTSLINK24  = 0;
            $COUNT_KDSGDS  = 0;
            $COUNT_TECDOC  = 0;
            $COUNT_ETKA  = 0;
            $COUNT_FORD_PTS  = 0;

            $COUNT_NEW_USER  = 0;
            $COUNT_USER_VERIFY  = 0;

            $COUNT_MORE_DEVICE  = 0;
            $MONEY_MORE_DEVICE  = 0;

            $charts = [];

            //Config
            $carbon = Carbon::now();
            $dateFrom = Carbon::parse(Carbon::parse($dateFrom)->format('Y-m-d 00:00:00'))->timezone('UTC')->toDateTimeString();
            $dateTo = Carbon::parse(Carbon::parse($dateTo)->format('Y-m-d 23:59:59'))->timezone('UTC')->toDateTimeString();

            $date1 = $carbon->parse($dateFrom);
            $date2 = $carbon->parse($dateTo);

            //check loại charts
            $type = 'month';
            $date2Compare = clone $date2;

            if ($date2Compare->subDays(2) <= $date1) {

                $type = 'hour';
            } else 
            if ($date2Compare->subMonths(2) < $date1) {
                $type = 'day';
            } else 
            if ($date2Compare->subMonths(24) < $date1) {
                $type = 'month';
            }
            if ($date2->year - $date1->year > 2) {
                return new Exception(MsgCode::GREAT_TIME[1]);;
            }

            $allHistoryRenew = null;

            $allHistoryRenew = RenewalHistory::where('created_at', '>=',  $dateFrom)
                ->where('created_at', '<', $dateTo)
                ->get();


            //Đặt time charts
            if ($type == 'hour') {
                for ($i = $date1; $i <= $date2; $i->addHours(1)) {
                    $charts[$i->format('Y-m-d H:00:00')] = [
                        'time' => $i->format('Y-m-d H:00:00'),
                        "MONEY_ALLDATA"  => 0,
                        "MONEY_AUTODATA"  => 0,
                        "MONEY_HAYNESPRO"  => 0,
                        "MONEY_IDENTIFIX"  => 0,
                        "MONEY_MITCHELL_REPAIR_CENTER"  => 0,
                        "MONEY_MITCHELL_PRODEMAND"  => 0,
                        "MONEY_HAYNESPRO_TRUCK"  => 0,
                        "MONEY_PARTSLINK24"  => 0,
                        "MONEY_KDSGDS"  => 0,
                        "MONEY_ETKA"  => 0,
                        "MONEY_TECDOC"  => 0,
                        "MONEY_FORD_PTS"  => 0,

                        "COUNT_ALLDATA"  => 0,
                        "COUNT_AUTODATA"  => 0,
                        "COUNT_HAYNESPRO" => 0,
                        "COUNT_IDENTIFIX" => 0,
                        "COUNT_MITCHELL_REPAIR_CENTER" => 0,
                        "COUNT_MITCHELL_PRODEMAND" => 0,
                        "COUNT_HAYNESPRO_TRUCK" => 0,
                        "COUNT_PARTSLINK24" => 0,
                        "COUNT_KDSGDS" => 0,
                        "COUNT_ETKA" => 0,
                        "COUNT_TECDOC" => 0,
                        "COUNT_FORD_PTS" => 0,

                        "COUNT_NEW_USER"  => 0,
                        "COUNT_USER_VERIFY"  => 0,

                        "COUNT_MORE_DEVICE" => 0,
                        "MONEY_MORE_DEVICE" => 0,
                    ];
                }
            }

            if ($type == 'day') {
                for ($i = $date1; $i <= $date2; $i->addDays(1)) {
                    $charts[$i->format('Y-m-d')] = [
                        'time' => $i->format('Y-m-d'),
                        "MONEY_ALLDATA"  => 0,
                        "MONEY_AUTODATA"  => 0,
                        "MONEY_HAYNESPRO"  => 0,
                        "MONEY_IDENTIFIX"  => 0,
                        "MONEY_MITCHELL_REPAIR_CENTER"  => 0,
                        "MONEY_MITCHELL_PRODEMAND"  => 0,
                        "MONEY_HAYNESPRO_TRUCK"  => 0,
                        "MONEY_PARTSLINK24"  => 0,
                        "MONEY_KDSGDS"  => 0,
                        "MONEY_ETKA"  => 0,
                        "MONEY_TECDOC"  => 0,
                        "MONEY_FORD_PTS"  => 0,

                        "COUNT_ALLDATA"  => 0,
                        "COUNT_AUTODATA"  => 0,
                        "COUNT_HAYNESPRO" => 0,
                        "COUNT_IDENTIFIX" => 0,
                        "COUNT_MITCHELL_REPAIR_CENTER" => 0,
                        "COUNT_MITCHELL_PRODEMAND" => 0,
                        "COUNT_HAYNESPRO_TRUCK" => 0,
                        "COUNT_PARTSLINK24" => 0,
                        "COUNT_KDSGDS" => 0,
                        "COUNT_ETKA" => 0,
                        "COUNT_TECDOC" => 0,
                        "COUNT_FORD_PTS" => 0,

                        "COUNT_NEW_USER"  => 0,
                        "COUNT_USER_VERIFY"  => 0,

                        "COUNT_MORE_DEVICE" => 0,
                        "MONEY_MORE_DEVICE" => 0,
                    ];
                }
            }

            if ($type == 'month') {
                for ($i = $date1; $i <= $date2; $i->addMonths(1)) {
                    $charts[$i->format('Y-m')] = [
                        'time' => $i->format('Y-m'),
                        "MONEY_ALLDATA"  => 0,
                        "MONEY_AUTODATA"  => 0,
                        "MONEY_HAYNESPRO"  => 0,
                        "MONEY_IDENTIFIX"  => 0,
                        "MONEY_MITCHELL_REPAIR_CENTER"  => 0,
                        "MONEY_MITCHELL_PRODEMAND"  => 0,
                        "MONEY_HAYNESPRO_TRUCK"  => 0,
                        "MONEY_PARTSLINK24"  => 0,
                        "MONEY_KDSGDS"  => 0,
                        "MONEY_ETKA"  => 0,
                        "MONEY_TECDOC"  => 0,
                        "MONEY_FORD_PTS"  => 0,

                        "COUNT_ALLDATA"  => 0,
                        "COUNT_AUTODATA"  => 0,
                        "COUNT_HAYNESPRO" => 0,
                        "COUNT_IDENTIFIX" => 0,
                        "COUNT_MITCHELL_REPAIR_CENTER" => 0,
                        "COUNT_MITCHELL_PRODEMAND" => 0,
                        "COUNT_HAYNESPRO_TRUCK" => 0,
                        "COUNT_PARTSLINK24" => 0,
                        "COUNT_KDSGDS" => 0,
                        "COUNT_ETKA" => 0,
                        "COUNT_TECDOC" => 0,
                        "COUNT_FORD_PTS" => 0,

                        "COUNT_NEW_USER"  => 0,
                        "COUNT_USER_VERIFY"  => 0,

                        "COUNT_MORE_DEVICE" => 0,
                        "MONEY_MORE_DEVICE" => 0,
                    ];
                }
            }


            foreach ($allHistoryRenew as $itemRow) {

                if (
                    $itemRow->service ==  PlanUtils::ALLDATA
                ) {
                    $MONEY_ALLDATA += $itemRow->paid;
                    $COUNT_ALLDATA += 1;
                }

                if (
                    $itemRow->service ==  PlanUtils::AUTODATA
                ) {
                    $MONEY_AUTODATA += $itemRow->paid;
                    $COUNT_AUTODATA += 1;
                }
                if (
                    $itemRow->service ==  PlanUtils::HAYNESPRO
                ) {
                    $MONEY_HAYNESPRO += $itemRow->paid;
                    $COUNT_HAYNESPRO += 1;
                }

                if (
                    $itemRow->service ==  PlanUtils::IDENTIFIX
                ) {
                    $MONEY_IDENTIFIX += $itemRow->paid;
                    $COUNT_IDENTIFIX += 1;
                }

                if (
                    $itemRow->service ==  PlanUtils::MITCHELL_REPAIR_CENTER
                ) {
                    $MONEY_MITCHELL_REPAIR_CENTER += $itemRow->paid;
                    $COUNT_MITCHELL_REPAIR_CENTER += 1;
                }
                if (
                    $itemRow->service ==  PlanUtils::MITCHELL_PRODEMAND
                ) {
                    $MONEY_MITCHELL_PRODEMAND += $itemRow->paid;
                    $COUNT_MITCHELL_PRODEMAND += 1;
                }

                if (
                    $itemRow->service ==  PlanUtils::HAYNESPRO_TRUCK
                ) {
                    $MONEY_HAYNESPRO_TRUCK += $itemRow->paid;
                    $COUNT_HAYNESPRO_TRUCK += 1;
                }

                if (
                    $itemRow->service ==  PlanUtils::PARTSLINK24
                ) {
                    $MONEY_PARTSLINK24 += $itemRow->paid;
                    $COUNT_PARTSLINK24 += 1;
                }
                if (
                    $itemRow->service ==  PlanUtils::KDSGDS
                ) {
                    $MONEY_KDSGDS += $itemRow->paid;
                    $COUNT_KDSGDS += 1;
                }
                if (
                    $itemRow->service ==  PlanUtils::ETKA
                ) {
                    $MONEY_ETKA += $itemRow->paid;
                    $COUNT_ETKA += 1;
                }
                if (
                    $itemRow->service ==  PlanUtils::TECDOC
                ) {
                    $MONEY_TECDOC += $itemRow->paid;
                    $COUNT_TECDOC += 1;
                }
                if (
                    $itemRow->service ==  PlanUtils::FORD_PTS
                ) {
                    $MONEY_FORD_PTS += $itemRow->paid;
                    $COUNT_FORD_PTS += 1;
                }

                if (
                    $itemRow->service ==  PlanUtils::MORE_DEVICE
                ) {
                    $MONEY_MORE_DEVICE += $itemRow->paid;
                    $COUNT_MORE_DEVICE += 1;
                }


                //Định hình charts
                $time = $carbon->parse($itemRow->created_at);

                $time_compare = "xxx";
                if ($type == 'hour') {
                    $time_compare = $time->year . '-' . $time->month . '-' . $time->day . ' ' . $time->hour . ':00:00';
                    $time_compare = $carbon->parse($time_compare);
                    $time_compare = $time_compare->format('Y-m-d H:00:00');
                }
                if ($type == 'day') {
                    $time_compare = $time->year . '-' . $time->month . '-' . $time->day;
                    $time_compare = $carbon->parse($time_compare);
                    $time_compare = $time_compare->format('Y-m-d');
                }
                if ($type == 'month') {
                    $time_compare = $time->year . '-' . $time->month;
                    $time_compare = $carbon->parse($time_compare);
                    $time_compare = $time_compare->format('Y-m');
                }




                //Thêm ddataa vào thời gian của chart
                if (isset($charts[$time_compare])) {

                    if (
                        $itemRow->service ==  PlanUtils::ALLDATA
                    ) {
                        $charts[$time_compare]["MONEY_ALLDATA"] = ($charts[$time_compare]["MONEY_ALLDATA"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_ALLDATA"] = ($charts[$time_compare]["COUNT_ALLDATA"] ?? 0) + 1;
                    }


                    if (
                        $itemRow->service ==  PlanUtils::AUTODATA
                    ) {
                        $charts[$time_compare]["MONEY_AUTODATA"] = ($charts[$time_compare]["MONEY_AUTODATA"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_AUTODATA"] = ($charts[$time_compare]["COUNT_AUTODATA"] ?? 0) + 1;
                    }

                    if (
                        $itemRow->service ==  PlanUtils::HAYNESPRO
                    ) {
                        $charts[$time_compare]["MONEY_HAYNESPRO"] = ($charts[$time_compare]["MONEY_HAYNESPRO"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_HAYNESPRO"] = ($charts[$time_compare]["COUNT_HAYNESPRO"] ?? 0) + 1;
                    }


                    if (
                        $itemRow->service ==  PlanUtils::IDENTIFIX
                    ) {
                        $charts[$time_compare]["MONEY_IDENTIFIX"] = ($charts[$time_compare]["MONEY_IDENTIFIX"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_IDENTIFIX"] = ($charts[$time_compare]["COUNT_IDENTIFIX"] ?? 0) + 1;
                    }

                    if (
                        $itemRow->service ==  PlanUtils::MITCHELL_REPAIR_CENTER
                    ) {
                        $charts[$time_compare]["MONEY_MITCHELL_REPAIR_CENTER"] = ($charts[$time_compare]["MONEY_MITCHELL_REPAIR_CENTER"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_MITCHELL_REPAIR_CENTER"] = ($charts[$time_compare]["COUNT_MITCHELL_REPAIR_CENTER"] ?? 0) + 1;
                    }

                    if (
                        $itemRow->service ==  PlanUtils::MITCHELL_PRODEMAND
                    ) {
                        $charts[$time_compare]["MONEY_MITCHELL_PRODEMAND"] = ($charts[$time_compare]["MONEY_MITCHELL_PRODEMAND"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_MITCHELL_PRODEMAND"] = ($charts[$time_compare]["COUNT_MITCHELL_PRODEMAND"] ?? 0) + 1;
                    }
                    if (
                        $itemRow->service ==  PlanUtils::HAYNESPRO_TRUCK
                    ) {
                        $charts[$time_compare]["MONEY_HAYNESPRO_TRUCK"] = ($charts[$time_compare]["MONEY_HAYNESPRO_TRUCK"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_HAYNESPRO_TRUCK"] = ($charts[$time_compare]["COUNT_HAYNESPRO_TRUCK"] ?? 0) + 1;
                    }

                    if (
                        $itemRow->service ==  PlanUtils::PARTSLINK24
                    ) {
                        $charts[$time_compare]["MONEY_PARTSLINK24"] = ($charts[$time_compare]["MONEY_PARTSLINK24"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_PARTSLINK24"] = ($charts[$time_compare]["COUNT_PARTSLINK24"] ?? 0) + 1;
                    }
                    if (
                        $itemRow->service ==  PlanUtils::KDSGDS
                    ) {
                        $charts[$time_compare]["MONEY_KDSGDS"] = ($charts[$time_compare]["MONEY_KDSGDS"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_KDSGDS"] = ($charts[$time_compare]["COUNT_KDSGDS"] ?? 0) + 1;
                    }
                    if (
                        $itemRow->service ==  PlanUtils::ETKA
                    ) {
                        $charts[$time_compare]["MONEY_ETKA"] = ($charts[$time_compare]["MONEY_ETKA"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_ETKA"] = ($charts[$time_compare]["COUNT_ETKA"] ?? 0) + 1;
                    }
                    if (
                        $itemRow->service ==  PlanUtils::TECDOC
                    ) {
                        $charts[$time_compare]["MONEY_TECDOC"] = ($charts[$time_compare]["MONEY_TECDOC"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_TECDOC"] = ($charts[$time_compare]["COUNT_TECDOC"] ?? 0) + 1;
                    }
                    if (
                        $itemRow->service ==  PlanUtils::FORD_PTS
                    ) {
                        $charts[$time_compare]["MONEY_FORD_PTS"] = ($charts[$time_compare]["MONEY_FORD_PTS"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_FORD_PTS"] = ($charts[$time_compare]["COUNT_FORD_PTS"] ?? 0) + 1;
                    }
                    if (
                        $itemRow->service ==  PlanUtils::MORE_DEVICE
                    ) {
                        $charts[$time_compare]["MONEY_MORE_DEVICE"] = ($charts[$time_compare]["MONEY_MORE_DEVICE"] ?? 0) + $itemRow->paid;
                        $charts[$time_compare]["COUNT_MORE_DEVICE"] = ($charts[$time_compare]["COUNT_MORE_DEVICE"] ?? 0) + 1;
                    }
                }
            }

            $allUser = DB::table('users')->select('id', 'email_verified_at', 'created_at')->where('created_at', '>=',  $dateFrom)
                ->where('created_at', '<', $dateTo)
                ->get();


            //Tính khách hàng
            foreach ($allUser as $itemRow) {


                $COUNT_NEW_USER += 1;

                if (
                    $itemRow->email_verified_at != null
                ) {
                    $COUNT_USER_VERIFY  += 1;
                }



                //Định hình charts
                $time = $carbon->parse($itemRow->created_at);

                $time_compare = "xxx";
                if ($type == 'hour') {
                    $time_compare = $time->year . '-' . $time->month . '-' . $time->day . ' ' . $time->hour . ':00:00';
                    $time_compare = $carbon->parse($time_compare);
                    $time_compare = $time_compare->format('Y-m-d H:00:00');
                }
                if ($type == 'day') {
                    $time_compare = $time->year . '-' . $time->month . '-' . $time->day;
                    $time_compare = $carbon->parse($time_compare);
                    $time_compare = $time_compare->format('Y-m-d');
                }
                if ($type == 'month') {
                    $time_compare = $time->year . '-' . $time->month;
                    $time_compare = $carbon->parse($time_compare);
                    $time_compare = $time_compare->format('Y-m');
                }



                //Thêm ddataa vào thời gian của chart
                if (isset($charts[$time_compare])) {


                    $charts[$time_compare]["COUNT_NEW_USER"] = ($charts[$time_compare]["COUNT_NEW_USER"] ?? 0) + 1;

                    if (
                        $itemRow->email_verified_at != null
                    ) {
                        $charts[$time_compare]["COUNT_USER_VERIFY"] = ($charts[$time_compare]["COUNT_USER_VERIFY"] ?? 0) + 1;
                    }
                }
            }


            $charts = array_values($charts);

            $data = [
                "MONEY_ALLDATA"  => $MONEY_ALLDATA,
                "MONEY_AUTODATA"  => $MONEY_AUTODATA,
                "MONEY_HAYNESPRO"  => $MONEY_HAYNESPRO,
                "MONEY_IDENTIFIX"  => $MONEY_IDENTIFIX,
                "MONEY_MITCHELL_REPAIR_CENTER"  => $MONEY_MITCHELL_REPAIR_CENTER,
                "MONEY_MITCHELL_PRODEMAND"  => $MONEY_MITCHELL_PRODEMAND,
                "MONEY_HAYNESPRO_TRUCK"  => $MONEY_HAYNESPRO_TRUCK,
                "MONEY_PARTSLINK24"  => $MONEY_PARTSLINK24,
                "MONEY_KDSGDS"  => $MONEY_KDSGDS,
                "MONEY_ETKA"  => $MONEY_ETKA,
                "MONEY_TECDOC"  => $MONEY_TECDOC,
                "MONEY_FORD_PTS"  => $MONEY_FORD_PTS,

                "COUNT_ALLDATA"  => $COUNT_ALLDATA,
                "COUNT_AUTODATA"  => $COUNT_AUTODATA,
                "COUNT_HAYNESPRO"  => $COUNT_HAYNESPRO,
                "COUNT_IDENTIFIX"  => $COUNT_IDENTIFIX,
                "COUNT_MITCHELL_REPAIR_CENTER"  => $COUNT_MITCHELL_REPAIR_CENTER,
                "COUNT_MITCHELL_PRODEMAND"  => $COUNT_MITCHELL_PRODEMAND,
                "COUNT_HAYNESPRO_TRUCK"  => $COUNT_HAYNESPRO_TRUCK,
                "COUNT_PARTSLINK24"  => $COUNT_PARTSLINK24,
                "COUNT_KDSGDS"  => $COUNT_KDSGDS,
                "COUNT_ETKA"  => $COUNT_ETKA,
                "COUNT_TECDOC"  => $COUNT_TECDOC,
                "COUNT_FORD_PTS"  => $COUNT_FORD_PTS,

                "COUNT_NEW_USER"  => $COUNT_NEW_USER,
                "COUNT_USER_VERIFY"  => $COUNT_USER_VERIFY,

                "MONEY_MORE_DEVICE"  => $MONEY_MORE_DEVICE,
                "COUNT_MORE_DEVICE"  => $COUNT_MORE_DEVICE,

                "count_visit_alldata"  => User::where('last_visit_time_alldata', '>=',  $dateFrom)
                    ->where('last_visit_time_alldata', '<', $dateTo)->count(),

                "count_visit_autodata"  => User::where('last_visit_time_autodata', '>=',  $dateFrom)
                    ->where('last_visit_time_autodata', '<', $dateTo)->count(),

                "count_visit_haynespro"  => User::where('last_visit_time_haynespro', '>=',  $dateFrom)
                    ->where('last_visit_time_haynespro', '<', $dateTo)->count(),

                "count_visit_identifix"  => User::where('last_visit_time_identifix', '>=',  $dateFrom)
                    ->where('last_visit_time_identifix', '<', $dateTo)->count(),

                "count_visit_mitchell_repair_center"  => User::where('last_visit_time_mitchell_repair_center', '>=',  $dateFrom)
                    ->where('last_visit_time_mitchell_repair_center', '<', $dateTo)->count(),

                "count_visit_mitchell_prodemand"  => User::where('last_visit_time_mitchell_prodemand', '>=',  $dateFrom)
                    ->where('last_visit_time_mitchell_prodemand', '<', $dateTo)->count(),

                "count_visit_haynespro_truck"  => User::where('last_visit_time_haynespro_truck', '>=',  $dateFrom)
                    ->where('last_visit_time_haynespro_truck', '<', $dateTo)->count(),

                "count_visit_partslink24"  => User::where('last_visit_time_partslink24', '>=',  $dateFrom)
                    ->where('last_visit_time_partslink24', '<', $dateTo)->count(),


                "count_visit_kdsgds"  => User::where('last_visit_time_kdsgds', '>=',  $dateFrom)
                    ->where('last_visit_time_kdsgds', '<', $dateTo)->count(),


                "count_visit_etka"  => User::where('last_visit_time_etka', '>=',  $dateFrom)
                    ->where('last_visit_time_etka', '<', $dateTo)->count(),


                "count_visit_tecdoc"  => User::where('last_visit_time_tecdoc', '>=',  $dateFrom)
                    ->where('last_visit_time_tecdoc', '<', $dateTo)->count(),

                "count_visit_ford_pts"  => User::where('last_visit_time_ford_pts', '>=',  $dateFrom)
                    ->where('last_visit_time_ford_pts', '<', $dateTo)->count(),


                'type_chart' => $type,
                'charts' => $charts

            ];

            return $data;
        }


        $dateFrom = request('date_from');
        $dateTo = request('date_to');

        $dateFrom = Carbon::parse(Carbon::parse($dateFrom)->format('Y-m-d 00:00:00'))->timezone('UTC')->toDateTimeString();
        $dateTo = Carbon::parse(Carbon::parse($dateTo)->format('Y-m-d 23:59:59'))->timezone('UTC')->toDateTimeString();



        $data_prime_time = handle_data($request, $dateFrom, $dateTo);
        if ($data_prime_time instanceof Exception) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => $data_prime_time->getMessage(),
            ], 400);
        }

        $dateFromCompare = request('date_from_compare');
        $dateToCompare = request('date_to_compare');

        $data_compare_time = null;
        if ($dateFromCompare != null && $dateToCompare != null) {
            $data_compare_time = handle_data($request, $dateFromCompare, $dateToCompare);

            if ($data_compare_time instanceof Exception) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => $data_compare_time->getMessage(),
                ], 400);
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => [
                'net_money_paypal' => OrderPlan::where('created_at', '>=',  $dateFrom)
                    ->where('created_at', '<', $dateTo)
                    ->where('status', RenewUtils::PAYMENT_STATUS_SUCCESS)
                    ->sum('net_price'),
                'data_prime_time' => $data_prime_time,
                'data_compare_time' => $data_compare_time
            ]
        ], 200);
    }
}
