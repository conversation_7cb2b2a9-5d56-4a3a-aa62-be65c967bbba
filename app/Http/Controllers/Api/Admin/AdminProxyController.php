<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\Helper;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\MsgCode;
use App\Models\ProxyItem;
use Illuminate\Http\Request;

/**
 * @group  User/Proxy
 */
class AdminProxyController extends Controller
{

    /**
     * Danh sách proxy
     */
    public function getAll(Request $request)
    {

        $proxys = ProxyItem::orderBy('position', 'ASC')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $proxys,
        ], 200);
    }

    /**
     * Thêm proxy
     */
    public function create(Request $request)
    {
        $proxy = StringUtils::normalizeProxy($request->proxy);

        $hasProxy = ProxyItem::where('proxy', $proxy )->first();

        if ($hasProxy  != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Đã có proxy này",
            ], 400);
        }
        $created = ProxyItem::create([
            'proxy' => $proxy ,
            'note' => $request->note
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $created,
        ], 200);
    }

    /**
     * Cập nhật proxy
     */
    public function update(Request $request, $proxy_id)
    {


        $hasProxy = ProxyItem::where('id', $proxy_id)->first();
        $proxy = StringUtils::normalizeProxy($request->proxy);

        if ($hasProxy  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không tồn tại",
            ], 400);
        }
        $hasProxy->update([
            'proxy' => $proxy,
            'note' => $request->note
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $hasProxy,
        ], 200);
    }

        /**
     * Cập nhật updateSortProxy
     */
    public function updateSortProxy(Request $request)
    {
        if($request->list != null) {
            foreach($request->list as $item) {
                $hasProxy = ProxyItem::where('id',$item['id'])->first();

                if(  $hasProxy  != null) {
                    $hasProxy ->update([
                        'position' =>  $item['position']
                    ]);
                }
            }
        }
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
    

      /**
     * Remove proxy
     */
    public function delete(Request $request, $proxy_id)
    {


        $hasProxy = ProxyItem::where('id', $proxy_id)->first();

        if ($hasProxy  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không tồn tại",
            ], 400);
        }
        $hasProxy->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
