<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\PlanUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\PlanService;
use App\Models\Voucher;
use Illuminate\Http\Request;


/**
 * @group  Admin/Voucher
 */

class AdminVoucherController extends Controller
{


    /**
     * Thêm voucher
     * 
     */
    public function create(Request $request)
    {

        // {
        //   "code_voucher"
        //     "on_start"
        //     "start_time"
        //     "end_time"
        //     "voucher_items":{ "service":"ALLDATA", "value":10 }
        // }


        if (empty($request->code_voucher)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Voucher Code không hợp lệ',
            ], 400);
        }

        $voucherExists = Voucher::where('code_voucher', strtoupper($request->code_voucher))
            ->first();

        if ($voucherExists  != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Code đã tồn tại',
            ], 400);
        }


        if ($request->voucher_items == null && !is_array($request->voucher_items)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Không có dịch vụ nào',
            ], 400);
        }

        if ($request->start_time == null || $request->end_time == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Thời gian không hợp lệ',
            ], 400);
        }

        $voucherCreate = Voucher::create([
            'code_voucher' => strtoupper($request->code_voucher),
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'on_start' => filter_var($request->on_start, FILTER_VALIDATE_BOOLEAN),
            "json_voucher_items" => json_encode($request->voucher_items)
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $voucherCreate
        ], 200);
    }

    /**
     * Danh sách voucher
     * 
     */
    public function getAll(Request $request)
    {

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => Voucher::get()
        ], 200);
    }

    /**
     * Get 1 voucher
     * 
     */
    public function getOne(Request $request)
    {

        $voucherExists = Voucher::where('id', $request->voucher_id)->first();

        if ($voucherExists  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Voucher không tồn tại',
            ], 400);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $voucherExists
        ], 200);
    }

    /**
     * delete 1 voucher
     * 
     */
    public function delete(Request $request)
    {

        $voucherExists = Voucher::where('id', $request->voucher_id)->first();

        if ($voucherExists  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Voucher không tồn tại',
            ], 400);
        }

        $voucherExists->delete();
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * update 1 voucher
     * 
     */
    public function update(Request $request)
    {

        $voucherExists = Voucher::where('id', $request->voucher_id)->first();

        if (empty($request->code_voucher)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Voucher Code không hợp lệ',
            ], 400);
        }

        if ($voucherExists  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Voucher không tồn tại',
            ], 400);
        }

        $voucherExistsCode = Voucher::where('id', '!=', $request->voucher_id)
            ->where('code_voucher', strtoupper($request->code_voucher))
            ->first();

        if ($voucherExistsCode  != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Code đã tồn tại',
            ], 400);
        }

        if ($request->voucher_items == null && !is_array($request->voucher_items)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Không có dịch vụ nào',
            ], 400);
        }

        if ($request->start_time == null || $request->end_time == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Thời gian không hợp lệ',
            ], 400);
        }

        $voucherExists->update(
            [
                'code_voucher' => strtoupper($request->code_voucher),
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'on_start' => filter_var($request->on_start, FILTER_VALIDATE_BOOLEAN),
                "json_voucher_items" => json_encode($request->voucher_items)
            ]
        );
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $voucherExists
        ], 200);
    }
}
