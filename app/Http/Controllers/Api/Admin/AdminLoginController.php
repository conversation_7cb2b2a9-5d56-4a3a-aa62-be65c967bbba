<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\PhoneUtils;
use App\Http\Controllers\Api\HandleReceiverSmsController;
use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\Employee;
use App\Models\MsgCode;
use App\Models\OtpCodePhone;
use App\Models\SessionAdmin;
use App\Models\SessionEmployee;
use App\Models\SessionUser;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

/**
 * @group  Admin/Đăng nhập
 */
class AdminLoginController extends Controller
{
    /**
     * Login
     * @bodyParam email string required Số điện thoại
     * @bodyParam password string required Password
     */
    public function login(Request $request)
    {

        $checkAdminExists = User::where('email', $request->email)->first();


        //B1 xác thực tồn tại
        if ($checkAdminExists != null && Hash::check($request->password, $checkAdminExists->password)) {

            $checkTokenExists = SessionUser::where(
                'user_id',
                $checkAdminExists->id
            )->first();

            //B2 tạo token
            if (empty($checkTokenExists)) {
                $adminSession = SessionUser::create([
                    'token' => Str::random(40),
                    'refresh_token' => Str::random(40),
                    'token_expried' => date('Y-m-d H:i:s',  strtotime('+100 day')),
                    'refresh_token_expried' => date('Y-m-d H:i:s',  strtotime('+365 day')),
                    'user_id' => $checkAdminExists->id
                ]);
            } else {
                $adminSession =  $checkTokenExists;
            }

            return response()->json([
                'code' => 200,
                'success' => true,
                'data' => $adminSession,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
            ], 200);
        }

        //Nếu không phải admin thì là employee


        return response()->json([
            'code' => 401,
            'success' => false,
            'msg_code' => MsgCode::WRONG_ACCOUNT_OR_PASSWORD[0],
            'msg' => MsgCode::WRONG_ACCOUNT_OR_PASSWORD[1],
        ], 401);
    }



    /**
     * Thay đổi mật khẩu
     * @bodyParam password string required Mật khẩu mới
     */
    public function change_password(Request $request)
    {


        $oldPassword = $request->old_password;
        $newPassword = $request->new_password;


        $admin = $request->admin;
        if (!Hash::check($oldPassword, $admin->password)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_OLD_PASSWORD[0],
                'msg' => MsgCode::INVALID_OLD_PASSWORD[1],
            ], 400);
        }

        if (
            strlen($newPassword) < 6
        ) {

            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[0],
                'msg' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[1],
            ], 400);
        }

        $admin->update(
            [
                'password' => bcrypt($newPassword)
            ]
        );


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Kiểm tra email,phone_number đã tồn tại
     * Sẽ ưu tiên kiểm tra phone_number (kết quả true tồn tại, false không tồn tại)
     * @bodyParam phone_number required phone_number
     * @bodyParam email string required email
     */
    public function check_exists(Request $request)
    {
        $phone = PhoneUtils::convert($request->phone_number);
        $email = $request->email;

        $list_check = [];
        $admin = Admin::where('store_id', $request->store->id)->where('phone_number', $phone)->first();
        if ($admin != null) {

            array_push($list_check, [
                "name" => "phone_number",
                "value" => true
            ]);
        } else {
            array_push($list_check, [
                "name" => "phone_number",
                "value" => false
            ]);
        }

        $admin2 = Admin::where('store_id', $request->store->id)->where('email', $email)->first();
        if ($admin2 != null) {


            array_push($list_check, [
                "name" => "email",
                "value" => true
            ]);
        } else {
            array_push($list_check, [
                "name" => "email",
                "value" => false
            ]);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $list_check
        ], 200);
    }
}
