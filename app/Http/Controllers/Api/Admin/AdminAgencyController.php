<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\Helper;
use App\Helper\PlanUtils;
use App\Helper\RenewUtils;
use App\Helper\SendEmailUtils;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationAdminJob;
use App\Models\Agency;
use App\Models\MsgCode;
use App\Models\ProxyServer;
use App\Models\RenewalHistory;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;


/**
 * @group  Admin/Đại lý
 */

class AdminAgencyController extends Controller
{


    /**
     * Thêm Đại lý
     * 
     * @bodyParam user_id 
     * @bodyParam agency_code
     * @bodyParam name
     * @bodyParam note
     * 
     */
    public function create(Request $request)
    {

        $agencyCode = trim($request->agency_code ?? '');
        if ($agencyCode === '' || !preg_match('/^[a-zA-Z0-9_-]+$/', $agencyCode) || strlen($agencyCode) < 2) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_AGENCY_CODE[0],
                'msg' => MsgCode::INVALID_AGENCY_CODE[1],
            ], 400);
        }

        if (Agency::where('agency_code', $agencyCode)->first()) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::AGENCY_CODE_ALREADY_EXISTS[0],
                'msg' => MsgCode::AGENCY_CODE_ALREADY_EXISTS[1],
            ], 400);
        };

        $user = User::where('id', $request->user_id)->first();
        if ($user  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        };

        if (Agency::where('user_id', $request->user_id)->first()) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::AGENT_WITH_THIS_USER_ALREADY_EXISTS[0],
                'msg' => MsgCode::AGENT_WITH_THIS_USER_ALREADY_EXISTS[1],
            ], 400);
        };


        $agency_create = Agency::create([
            'user_id' => $request->user_id,
            'agency_code' => $agencyCode,
            'sub_domain' => $agencyCode,
            'note' => $request->note,
            'name' => $request->name,
        ]);

        PushNotificationAdminJob::dispatch(
            ("Grant agency for ") . $user->username . ' with agency code ' . $agencyCode,
            ("Grant agency for ") . $user->username . " from " . $request->user->username,
        );


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $agency_create
        ], 200);
    }


    /**
     * Danh sách agency
     * 
     * 
     */
    public function getAll(Request $request)
    {

        $all = Agency::orderBy('id', 'desc')->paginate(request('limit') ?? 40);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Xóa 1 agency
     * 
     * @urlParam agency_id
     * 
     */
    public function delete(Request $request, $agency_id)
    {

        //  Agency::where('id', $agency_id)->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Cập nhật 1 agency
     * 
     * @urlParam agency_id
     * @bodyParam agency_code
     * @bodyParam name
     * @bodyParam note
     * 
     */
    public function update(Request $request, $agency_id)
    {

        $agencyExists = Agency::where('id', $agency_id)->first();
        if ($agencyExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_AGENCY_EXISTS[0],
                'msg' => MsgCode::NO_AGENCY_EXISTS[1],
            ], 400);
        }

        if ($request->agency_code === '' || !preg_match('/^[a-zA-Z0-9_-]+$/',$request->agency_code) || strlen($request->agency_code) < 2) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_AGENCY_CODE[0],
                'msg' => MsgCode::INVALID_AGENCY_CODE[1],
            ], 400);
        }

        if (Agency::where('agency_code', $request->agency_code)->where('id', '!=', $agency_id)->first()) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::AGENCY_CODE_ALREADY_EXISTS[0],
                'msg' => MsgCode::AGENCY_CODE_ALREADY_EXISTS[1],
            ], 400);
        };


        $agencyExists->update(
            [
                'allow_renew' =>  $request->allow_renew,
                'agency_code' => $request->agency_code,
                'sub_domain' => $request->sub_domain,
                'note' => $request->note,
                'name' => $request->name,

                'hidden_price' => $request->hidden_price,
                'enable_customer_add_device' => $request->enable_customer_add_device,

                'allow_alldata' => $request->allow_alldata,
                'allow_autodata' => $request->allow_autodata,
                'allow_haynespro' => $request->allow_haynespro,
                'allow_haynespro_truck' => $request->allow_haynespro_truck,
                'allow_identifix' => $request->allow_identifix,
                'allow_mitchell_repair_center' => $request->allow_mitchell_repair_center,
                'allow_mitchell_prodemand' => $request->allow_mitchell_prodemand,
                'not_back_home_web_agency' => $request->not_back_home_web_agency,
                'allow_partslink24' => $request->allow_partslink24,
                'allow_kdsgds' => $request->allow_kdsgds,
                'allow_etka' => $request->allow_etka,
                'allow_tecdoc' => $request->allow_tecdoc,
                'allow_ford_pts' => $request->allow_ford_pts,

                'autodata_language' => $request->autodata_language,
                'json_list_cookie_manager' =>  json_encode($request->list_cookie_manager)

            ]
        );

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>    $agencyExists
        ], 200);
    }

    /**
     * Thêm 1 user
     * 
     * @bodyParam name 
     * @bodyParam username 
     * @bodyParam password 
     * 
     */
    public function create_user(Request $request)
    {

        $username = $request->username;
        $userExists =  User::where('username',  $username)->first();

        if ($userExists  == null && !filter_var($request->username, FILTER_VALIDATE_EMAIL)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Username must be email",
            ], 400);
        }

        if ($userExists  != null && $userExists->is_admin == true) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Can't create this user",
            ], 400);
        }


        if (empty($request->username)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::USERNAME_IS_REQUIRED[0],
                'msg' => MsgCode::USERNAME_IS_REQUIRED[1],
            ], 400);
        }




        if (empty($username)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_USERNAME[0],
                'msg' => MsgCode::INVALID_USERNAME[1],
            ], 400);
        }



        // if (
        //     !empty($username) &&
        //     $username != null &&
        //     strlen($username) > 0 &&   $userExists != null &&   $userExists->of_agency_id != null
        // ) {

        //     return response()->json([
        //         'code' => 409,
        //         'success' => false,
        //         'msg_code' => MsgCode::USERNAME_ALREADY_EXISTS[0],
        //         'msg' => MsgCode::USERNAME_ALREADY_EXISTS[1],
        //     ], 409);
        // }

        if (
            strlen($request->password) < 6
        ) {

            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[0],
                'msg' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[1],
            ], 400);
        }


        $agency_id = $request->agency_id ?? $request->agency->id;

        if ($userExists == null) {
            $userCreate = User::create(
                [
                    'username' => $username,
                    'name' => $request->name,
                    'password' => bcrypt($request->password),
                    'of_agency_id' =>    $agency_id,
                    'email_verified_at' => Helper::getTimeNowString(),
                    'email' => filter_var($username, FILTER_VALIDATE_EMAIL)  == true  ? $username :  null
                    // 'expiry_alldata' =>  $timeNext,
                    // 'expiry_autodata' =>  $timeNext,

                ]
            );

            SendEmailUtils::sendEmailRegisterSuccess($username);

            $toAgencyCode = $request->agency->agency_code;
        } else {

            $isAgency = Agency::where('user_id', $userExists->id)->first() != null;

            if ($isAgency == true && $userExists->id != $request->user->id) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => "Can't create this user",
                ], 400);
            }

            $userExists->update([
                'of_agency_id' =>   $agency_id,
                'email' => filter_var($username, FILTER_VALIDATE_EMAIL)  == true  ? $username :   $userExists->email,
                'email_verified_at' => Helper::getTimeNowString(),
            ]);

            $toAgencyCode = $request->agency->agency_code;
            PushNotificationAdminJob::dispatch(
                "Change agent manager",
                "Change agent manager for " . $username . " to agent " . $toAgencyCode,
            );

            $userCreate =   $userExists;
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $userCreate
        ], 200);
    }


    /**
     * Xóa 1 khách hàng trong ds của đại lý
     * 
     * @urlParam user_id 
     * 
     */
    public function delete_user(Request $request, $user_id)
    {

        $userExists = User::where('id', $user_id)->where('of_agency_id', $request->agency->id)->first();

        if ($userExists  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $has_expiry = RenewUtils::check_has_expiry($userExists);

        if ($has_expiry == true) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Has expiry date cannot deleted',
            ], 400);
        }

        $userExists->update(
            [
                'of_agency_id' => null
            ]
        );

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }


    /**
     * Thực hiện thanh toán cho dịch vụ 1 agency
     * 
     * @bodyParam agency_id
     * @bodyParam service
     * 
     */
    public function paidForAgency(Request $request)
    {
        $agencyExists = Agency::where('id', $request->agency_id)->first();
        if ($agencyExists != null) {

            RenewalHistory::where('references_id', $agencyExists->user_id)
                ->where('service', $request->service)->update([
                    'paid_for_admin' => true
                ]);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }


    /**
     * Thực hiện thanh toán cho tất cả dịch vụ của đại lý đó
     * 
     * @bodyParam agency_id
     * @bodyParam service
     * 
     */
    public function paidForAgencyAllService(Request $request)
    {
        $agencyExists = Agency::where('id', $request->agency_id)->first();
        if ($agencyExists != null) {

            $need_pay = 0;
            if ($agencyExists->need_pay != null) {
                $need_pay = $agencyExists->need_pay['need_pay'];
            }

            RenewalHistory::where('references_id', $agencyExists->user_id)
                ->update([
                    'paid_for_admin' => true
                ]);

            PushNotificationAdminJob::dispatch(
                'Agency code: ' . $agencyExists->agency_code . " paid and reset " .  $need_pay . "$",
                'Agency code: ' . $agencyExists->agency_code . " paid and reset " .  $need_pay . "$",
            );
        }
        sleep(3);
        $agencyExists = Agency::where('id', $request->agency_id)->first();


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>   $agencyExists
        ], 200);
    }

    /**
     * Xem 1 agency
     * 
     * @bodyParam agency_id
     * @bodyParam service
     * 
     */
    public function getOne(Request $request)
    {
        $agencyExists = Agency::where('id', $request->agency_id)->first();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $agencyExists
        ], 200);
    }
}
