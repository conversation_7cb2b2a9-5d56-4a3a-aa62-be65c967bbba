<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\MoneyBalanceUtils;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\HistoryRecharge;
use App\Models\MsgCode;
use App\Models\User;
use Illuminate\Http\Request;
use Prophecy\Util\StringUtil;

/**
 * @group  Admin/Nạp tiền
 */
class AdminRechargeController extends Controller
{

    /**
     * Lịch sử yêu cầu N<PERSON>p tiền
     * 
     * @queryParam user_id string user id
     * @queryParam user_id string user id
     * 
     * 
     */
    public function histories(Request $request)
    {

        $search = StringUtils::convert_name_lowcase(request('search'));


        $historis = HistoryRecharge::when(request('user_id') != null, function ($query) {
            $query->where('history_recharges.user_id', request('user_id'));
        })->leftJoin('users', function ($join) {
            $join->on('users.id', '=', 'history_recharges.user_id');
        })
            ->when(request('of_agency_id') != null, function ($query) {
                $query->where('users.of_agency_id',  request('of_agency_id'));
            })
            ->when(request('status') !== null, function ($query) {
                $query->where('history_recharges.status', request('status'));
            })->orderBy("history_recharges.created_at", 'desc')
            ->search($search)
            ->paginate(request('limit') ?? 20);


        foreach ($historis  as  $history) {
            $history->user  = User::where('id', $history->user_id)->first();
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>   $historis,
        ], 200);
    }

    /**
     * Xử lý lệnh Nạp tiền
     * 
     * @bodyParam status só tiền 1 hủy, 2 đồng ý
     * 
     */
    public function updateStatus(Request $request)
    {

        $recharge_id = $request->route()->parameter('recharge_id');
        $checkRechargeExists = HistoryRecharge::where(
            'id',
            $recharge_id
        )->first();


        if ($checkRechargeExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_RECHARGE_EXISTS[1],
                'msg' => MsgCode::NO_RECHARGE_EXISTS[1],
            ], 400);
        }

        if ((int)$checkRechargeExists->status == (int)$request->status) {

            if ($request->status == MoneyBalanceUtils::MONEY_BALANCE_WITH_DRAW_ACCEPT) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::SUCCESS[0],
                    'msg' => "Yêu cầu này đã được duyệt từ trước",
                ], 400);
            }

            if ($request->status == MoneyBalanceUtils::MONEY_BALANCE_WITH_DRAW_CANCEL) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::SUCCESS[0],
                    'msg' => "Yêu cầu đã hủy duyệt từ trước",
                ], 400);
            }
        }

        if ($request->status == MoneyBalanceUtils::MONEY_BALANCE_WITH_DRAW_CANCEL || $request->status == MoneyBalanceUtils::MONEY_BALANCE_WITH_DRAW_ACCEPT) {

            if ($checkRechargeExists->status != MoneyBalanceUtils::MONEY_BALANCE_WITH_DRAW_WAITING) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::SUCCESS[0],
                    'msg' => "Yêu cầu đã được xử lý không thể xử lý lại",
                ], 400);
            }
        }


        if ($request->status == MoneyBalanceUtils::MONEY_BALANCE_WITH_DRAW_ACCEPT) {
            MoneyBalanceUtils::add_sub_money(
                $checkRechargeExists->user_id,
                MoneyBalanceUtils::MONEY_RECHARGE,
                $checkRechargeExists->money,
                $checkRechargeExists->code,
                $checkRechargeExists->id,
            );
        }

        $checkRechargeExists->update([
            'status' => $request->status
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  HistoryRecharge::where('id',  $recharge_id)->first()
        ], 200);
    }
    /**
     * Xem 1 lệnh rút tiền
     * 
     * @bodyParam status só tiền 1 hủy, 2 đồng ý
     * 
     */
    public function getOne(Request $request)
    {

        $recharge_id = $request->route()->parameter('recharge_id');
        $checkRechargeExists = HistoryRecharge::where(
            'id',
            $recharge_id
        )->first();


        if ($checkRechargeExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_RECHARGE_EXISTS[1],
                'msg' => MsgCode::NO_RECHARGE_EXISTS[1],
            ], 400);
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  HistoryRecharge::where('id',  $recharge_id)->first()
        ], 200);
    }
}
