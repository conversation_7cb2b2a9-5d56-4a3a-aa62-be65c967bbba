<?php

namespace App\Http\Controllers\Api\Admin;

use App\Exports\RenewalHistoryExport;
use App\Helper\Helper;
use App\Helper\PlanUtils;
use App\Helper\RenewUtils;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationAdminJob;
use App\Models\Agency;
use App\Models\DeviceLogin;
use App\Models\HistoryExportExcel;
use App\Models\MsgCode;
use App\Models\RenewalHistory;
use App\Models\User;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

/**
 * @group  Admin/Hạn sử dụng
 */

class AdminRenewalController extends Controller
{


    /**
     * Thêm gói
     * 
     * @bodyParam month số tháng
     * @bodyParam is_add trừ hay cộng
     * 
     */
    public function sub_add_expiry(Request $request)
    {

        $month = $request->month;
        $is_add = $request->is_add;
        $paid = $request->paid;
        $service = $request->service;

        $user =  User::where('id', $request->user_id)->first();

        if ($user  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        if ($month < 0) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_VALUE[0],
                'msg' => MsgCode::INVALID_VALUE[1],
            ], 400);
        }


        $timeNow =   Carbon::parse(Helper::getTimeNowString());
        $colum_user_expiry = PlanUtils::getColumnExpiryWithPlanName($service);

        if ($is_add == false) {
            $max_month = $timeNow->diffInMonths(Carbon::parse($user->$colum_user_expiry), false) + 1;

            if ($month > $max_month) {
                $month  = $max_month;
            }
            if ($month  < 0) $month  = 0;
        }

        if ($is_add == false && $request->user->is_admin == false) {
            $history = RenewalHistory::where('user_id',   $request->user_id)->where('references_id', $request->user->id)
                ->where('month', '>', 0)->first();

            if ($history == null) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => "NO_RENEWAL_HISTORY_OF_YOURS",
                    'msg' => "No renewal history of yours",
                ], 400);
            }
        }


        RenewUtils::add_sub_expiry(
            $request->user_id,
            $month,
            $paid,
            $is_add,
            $service,
            $service,
            $request->user->id,
            $request->agency != null ?  ("AGENCY_HANDLE" . $request->agency->agency_code . '_' . Helper::generateRandomString(5)) : ("ADMIN_HANDLER " . Helper::generateRandomString(5)),
            null,
            RenewUtils::PAY_FROM_ADMIN
        );


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Thêm thiết bị
     * 
     * @bodyParam month số tháng
     * @bodyParam is_add trừ hay cộng
     * 
     */
    public function sub_add_device(Request $request)
    {

        $month = $request->month;
        $is_add = $request->is_add;
        $paid = $request->paid;
        $service = $request->service;

        $user =  User::where('id', $request->user_id)->first();

        if ($user  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        if ($month != 6 && $month != 12) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_VALUE[0],
                'msg' => MsgCode::INVALID_VALUE[1],
            ], 400);
        }


        // $timeNow =   Carbon::parse(Helper::getTimeNowString());
        // $colum_user_expiry = PlanUtils::getColumnExpiryWithPlanName($service);

        // if ($is_add == false) {
        //     $max_month = $timeNow->diffInMonths(Carbon::parse($user->$colum_user_expiry), false) + 1;

        //     if ($month > $max_month) {
        //         $month  = $max_month;
        //     }
        //     if ($month  < 0) $month  = 0;
        // }

        $mainDevice = DeviceLogin::where('user_id', $request->user_id)->where('is_main', true)->first();
        if ($mainDevice  == null) {
            DeviceLogin::create([
                'user_id' =>  $request->user_id,
                'is_main' => true,
            ]);
        }


        RenewUtils::add_sub_expiry_device(
            $request->user_id,
            $request->month,
            $request->price ?? 0,
            true,
            PlanUtils::MORE_DEVICE,
            PlanUtils::MORE_DEVICE,
            $request->user->id,
            $request->agency != null ?  ("AGENCY_DEVICE" . $request->agency->agency_code . '_' . Helper::generateRandomString(5)) : ("ADMIN_DEVICE" . Helper::generateRandomString(5)),
            null, // $orderExists->device_login_id,
            RenewUtils::PAY_FROM_ADMIN
        );

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Thêm thiết bị
     * 
     * @bodyParam month số tháng
     * @bodyParam is_add trừ hay cộng
     * 
     */
    public function cancelDevice(Request $request)
    {
        $device_login_id = $request->device_login_id;

        if ($device_login_id  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_DEVICE_LOGIN[0],
                'msg' => MsgCode::NO_DEVICE_LOGIN[1],
            ], 400);
        }

        $deviceLoginExists = DeviceLogin::where('id', $device_login_id)->first();

        if ($deviceLoginExists  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_DEVICE_LOGIN[0],
                'msg' => MsgCode::NO_DEVICE_LOGIN[1],
            ], 400);
        }

        $checkUserExists = User::where('id',  $deviceLoginExists->user_id)->first();

        if (
            $request->user->is_admin == false && $request->agency != null &&
            $checkUserExists->of_agency_id != $request->agency->id
        ) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => "NOT_ENOUGH_AUTHORITY",
                'msg' => "not enough authority",
            ], 400);
        }

        if ($deviceLoginExists->is_main) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => "IS_MAIN_CANT CANCEL",
                'msg' => "is main can't cancel",
            ], 400);
        }

        $daysDiff = Carbon::parse(now())->diffInDays($deviceLoginExists->expiry_use, false);
        if ($daysDiff <= 350) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => "OUT_DATE_CANT_CANCEL",
                'msg' => "Out date can't cancel",
            ], 400);
        }

        $history = RenewalHistory::where('user_id',  $deviceLoginExists->user_id)->where('references_id', $request->user->id)
            ->where('service', 'MORE_DEVICE')->where('month', 12)
            ->where('paid_for_admin', 0)->first();

        if ($history == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => "NO_RENEWAL_HISTORY_OF_YOURS",
                'msg' => "No renewal history of yours",
            ], 400);
        }

        $history->update([
            'paid_for_admin' => true
        ]);
        $deviceLoginExists->delete();

        $references_value =  $history->references_value;

        PushNotificationAdminJob::dispatch(
            "Cancel 12 month device " .  $checkUserExists->username . " from " . $references_value,
            "Cancel 12 month device " .  $checkUserExists->username . " from " . $references_value,
        );

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }



    /**
     * Cho dùng thử 2 giờ
     * 
     * @bodyParam user_id
     * @bodyParam is_add trừ hay cộng
     * 
     */
    public function try_demo(Request $request)
    {

        $service = $request->service;

        $user =  User::where('id', $request->user_id)->first();

        if ($user  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        if ($service == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'No service',
            ], 400);
        }

        $hasExpiry = RenewUtils::check_has_expiry_for_service($user, $service);

        if ($hasExpiry == true) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "User has expiration date, can't try demo",
            ], 400);
        }

        $timeBefore = Carbon::parse(Helper::getTimeNowString());
        $timeNow = Carbon::parse(Helper::getTimeNowString());



        $timeNext =  $timeNow->addHours(2);
        $colum_user_expiry = PlanUtils::getColumnExpiryWithPlanName($service);


        $timeBefore = $user->$colum_user_expiry;

        $user->update([
            $colum_user_expiry =>  $timeNext
        ]);

        $references_value = $request->agency != null ?  ("DEMO 2 HOUR " . $request->agency->agency_code . '_' . Helper::generateRandomString(5)) : ("DEMO 2 HOUR ADMIN " . Helper::generateRandomString(5));

        RenewalHistory::create([
            "user_id" => $request->user_id,
            "month" => 0,
            "base_service" => $service,
            "service" => $service,
            "price" => floor(0),
            "paid" => floor(0),
            "before_expiration_date" =>  $timeBefore,
            "after_expiration_date" =>  $timeNext,
            "extender_user_id" =>    $request->user->id,
            "references_id" =>     $request->user->id,
            "references_value" =>  $references_value,
            'product_id' => null,
            'pay_from' =>  $request->agency != null ? RenewUtils::PAY_FROM_AGENCY :  RenewUtils::PAY_FROM_ADMIN,
            'json_data' => null
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Lịch sử gia hạn của khách
     * 
     */
    public function getAll(Request $request)
    {
        $search = request('search') ?? "";
        $all = RenewalHistory::orderBy('renewal_histories.id', 'desc')
            ->join('users', function ($join) {
                $join->on('users.id', '=', 'renewal_histories.user_id');
            })
            ->when(request('of_agency_id') != null, function ($query) {
                $query->where('users.of_agency_id',  request('of_agency_id'));
            })
            ->select('renewal_histories.*')
            ->with('user')
            ->where(function ($query) use ($search) {
                $query->where('users.username', 'like', '%' . $search . '%')
                    ->orWhere('renewal_histories.references_value', 'like', '%' . $search . '%');
            })
            ->paginate(20);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * 
     * Xuất excel
     * 
     */
    public function getLinkExportExcel(Request $request)
    {
        $actual_link = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]";


        $carbon = Carbon::now();
        $dateFrom = Carbon::parse(Carbon::parse($carbon)->format('Y-m-d 00:00:00'))->timezone('UTC')->toDateTimeString();
        $dateTo = Carbon::parse(Carbon::parse($carbon)->format('Y-m-d 23:59:59'))->timezone('UTC')->toDateTimeString();

        $startDate   = $request->start_date ?? $dateFrom;
        $endDate     = $request->end_date ??  $dateTo;

        $key_get =  Helper::generateRandomString(20);
        $his = HistoryExportExcel::create([
            "user_id" => $request->user->id,
            "key_get" => $key_get,
            "start_date" =>  $startDate,
            "end_date" => $endDate,
            "type" =>  $request->type
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => [
                "link" => $actual_link . "/api/admin/download_export_excel?key_get=$key_get"
            ]
        ], 200);
    }


    /**
     * 
     * Xuất excel
     * 
     */
    public function downloadExportExcel(Request $request)
    {
        $now = Carbon::now();
        $twentyFourHoursAgo = $now->subHours(1);
        $his = HistoryExportExcel::where("key_get", request('key_get'))->where('updated_at', '>=', $twentyFourHoursAgo)->first();

        if ($his == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NOT_FOUND[0],
                'msg' => MsgCode::NOT_FOUND[1],
            ], 400);
        }

        $carbon = Carbon::now();
        $dateFrom = Carbon::parse(Carbon::parse($carbon)->format('Y-m-d 00:00:00'))->timezone('UTC')->toDateTimeString();
        $dateTo = Carbon::parse(Carbon::parse($carbon)->format('Y-m-d 23:59:59'))->timezone('UTC')->toDateTimeString();

        $startDate   = $his->start_date ?? $dateFrom;
        $endDate     = $his->end_date ??  $dateTo;


        $agentId = null;
        if ($his->type == 0) {
            $agent = Agency::where('user_id', $his->user_id)->first();
            if ($agent == null) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::NOT_FOUND[0],
                    'msg' => MsgCode::NOT_FOUND[1],
                ], 400);
            }
            $agentId  = $agent->id;
        }


        $export = new RenewalHistoryExport($startDate, $endDate,  $agentId, $his->type);
        $currentTime = new DateTime();

        $start = DateTime::createFromFormat('Y-m-d H:i:s', $startDate);
        $end = DateTime::createFromFormat('Y-m-d H:i:s', $endDate);

        $fileName = "DrCar-History-" . $start->format('Y-m-d') . "-" . $end->format('Y-m-d')  . ".xls";

        return Excel::download($export, $fileName, \Maatwebsite\Excel\Excel::XLS);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $his
        ], 200);

        $fileName = "DrCar-History-" . $start->format('Y-m-d') . "-" . $end->format('Y-m-d')  . ".xls";

        return Excel::download($export, $fileName, \Maatwebsite\Excel\Excel::XLS);
    }
}
