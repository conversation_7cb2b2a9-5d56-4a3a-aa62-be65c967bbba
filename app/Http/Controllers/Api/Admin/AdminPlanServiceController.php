<?php

namespace App\Http\Controllers\Api\Admin;


use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\PlanService;
use Illuminate\Http\Request;


/**
 * @group  Admin/Chỉnh giá
 */

class AdminPlanServiceController extends Controller
{


    /**
     * Thêm gói giá
     * 
     * @bodyParam service Proxy cho dịch vụ AUTODATA ALLDATA HAYNESPRO
     * @bodyParam month
     * @bodyParam price
     * @bodyParam product_id
     * @bodyParam public ẩn hiện
     * @bodyParam apply Áp dụng cho APP,WEB
     * 
     */
    public function create(Request $request)
    {


        PlanService::create([
            'pay_in_link' => $request->pay_in_link,
            'service' => $request->service,
            'month' => $request->month,
            'price' => $request->price,
            'product_id' => $request->product_id,
            'public' => $request->public,
            'apply' => $request->apply,
        ]);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }


    /**
     * Danh sách gói
     * 
     * @queyParam service Dịch vụ
     * 
     */
    public function getAll(Request $request)
    {

        $all = PlanService::when(request('service') != null, function ($query) {
            $query->where('service', request('service'));
        })->orderBy('price', 'asc')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Xóa 1 gói giá
     * 
     * @urlParam plan_service_id
     * 
     */
    public function delete(Request $request, $plan_service_id)
    {


        PlanService::where('id', $plan_service_id)->delete();


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Cập nhật 1 gói giá
     * 
     * @urlParam plan_service_id
     * @bodyParam service Proxy cho dịch vụ AUTODATA ALLDATA HAYNESPRO
     * @bodyParam month
     * @bodyParam price
     * @bodyParam product_id
     * @bodyParam public ẩn hiện
     * @bodyParam apply Áp dụng cho APP,WEB
     * 
     * 
     */
    public function update(Request $request, $plan_service_id)
    {
        $k =   PlanService::where('id', $plan_service_id)->first();

        $k->update([
            'pay_in_link' => $request->pay_in_link,
            'service' => $request->service,
            'month' => $request->month,
            'price' => $request->price,
            'product_id' => $request->product_id,
            'public' => $request->public,
            'apply' => $request->apply,
        ]);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>   $k
        ], 200);
    }
}
