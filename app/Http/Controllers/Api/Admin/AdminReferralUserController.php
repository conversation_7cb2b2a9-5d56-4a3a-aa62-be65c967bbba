<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Illuminate\Http\Request;

/**
 * @group  Admin/Quản lý user gi<PERSON><PERSON> thiệu
 */

class AdminReferralUserController extends Controller
{
    /**
     * Danh sách staff
     * 
     * @bodyParam staff_id required staff_id
     * 
     */
    public function getAll(Request $request)
    {



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            // 'data' =>  []
        ], 200);
    }
}
