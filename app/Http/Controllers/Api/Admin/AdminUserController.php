<?php

namespace App\Http\Controllers\Api\Admin;

use App\Exports\UsersExport;
use App\Helper\GroupUserUtils;
use App\Helper\Helper;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationAdminJob;
use App\Models\Agency;
use App\Models\DeviceLogin;
use App\Models\HistoryExportExcel;
use App\Models\MsgCode;
use App\Models\SessionUser;
use App\Models\User;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

/**
 * @group  Admin/Quản lý user
 */

class AdminUserController extends Controller
{
    /**
     * Danh sách user
     * 
     * @bodyParam search required Tìm theo tên hoặc sdt
     * 
     */
    public function getAll(Request $request)
    {


        $search = strtolower(request('search') ?? "");

        $all = User::when(request('status') !== null, function ($query) {
            $query->where('status', request('status'));
        })->when(request('type') !== null, function ($query) {
            $query->where('type', request('type'));
        })
            ->when(request('is_block') != null, function ($query) {
                $query->where('is_block',  filter_var(request('is_block'), FILTER_VALIDATE_BOOLEAN));
            })
            ->when(request('is_admin') != null, function ($query) {
                $query->where('is_admin',  filter_var(request('is_admin'), FILTER_VALIDATE_BOOLEAN));
            })
            ->when(request('is_user_ios') != null, function ($query) {
                $query->where('is_user_ios',  filter_var(request('is_user_ios'), FILTER_VALIDATE_BOOLEAN));
            })
            ->when(request('of_agency_id') != null, function ($query) {
                $query->where('of_agency_id',  request('of_agency_id'));
            })
            ->when($request->admin != 1 && $request->user != null && $request->user->is_admin == false && $request->agency != null, function ($query) use ($request) {
                $query->where('of_agency_id',  $request->agency->id);
            })

            ->when(request('group_user_ids') != null, function ($query) {
                $group_user_ids =  explode(",", request('group_user_ids'));
                $emails = GroupUserUtils::getEmailsWithMultiGroupId($group_user_ids);
                $query->whereIn('email',  $emails);
            })
            ->where(function ($query) use ($search) {
                $query->where('username', 'like', '%' . $search . '%')
                    ->orWhere('email', 'like', '%' . $search . '%')
                    ->orWhere('name', 'like', '%' . $search . '%')
                    ->orWhere('ip_using', 'like', '%' . $search . '%');
            })


            ->orderBy('id', 'desc')
            // ->search($search)
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Xóa user
     * 
     * @bodyParam search required Tìm theo tên hoặc sdt
     * 
     */
    public function delete(Request $request)
    {

        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        if ($request->admin != null) {
            // $checkUserExists->delete();
        }
        if ($request->agency != null && $request->agency->id ==  $checkUserExists->of_agency_id) {
            $checkUserExists->update([
                'of_agency_id' => null
            ]);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * 
     * 
     * Thay đổi block
     * 
     * 
     */
    public function updateIsBlock(Request $request)
    {
        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $checkUserExists->update([
            'is_block' =>  $request->is_block ?? 0,
        ]);

        PushNotificationAdminJob::dispatch(
            ($request->is_block == true ? "Blocked " : "UnBlock ") . $checkUserExists->username,
            ($request->is_block  == true ? "Blocked " : "UnBlock ") . $checkUserExists->username . " from " . $request->user->username,
        );

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkUserExists->id)->first(),
        ], 200);
    }



    /**
     * 
     * 
     * Thay đổi giá trị cột
     * 
     * @bodyParam column string tên cột
     * @bodyParam value giá trị cột
     * 
     */
    public function updateFieldStatus(Request $request)
    {
        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $checkUserExists->update([
            $request->column =>  $request->value ?? 0,
        ]);

        PushNotificationAdminJob::dispatch(
            "Change " .  $request->column  . " to " . ($request->value == true ? "allow " : "non") . " for " . $checkUserExists->username,
            "Change " .  $request->column  . " to " . ($request->value == true ? "allow " : "non") . " for " . $checkUserExists->username . " from " . $request->user->username,
        );


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkUserExists->id)->first(),
        ], 200);
    }

    /**
     * 
     * 
     * Thay đổi is ios
     * 
     * 
     */
    public function updateIsUserIos(Request $request)
    {
        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        if ($request->user->is_admin == false && $request->agency != null && $checkUserExists->of_agency_id != $request->agency->id) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }


        $checkUserExists->update([
            'is_user_ios' =>  $request->is_user_ios ?? 0,
        ]);

        PushNotificationAdminJob::dispatch(
            ($request->is_user_ios == true ? "Set ios " : "Un ios ") . $checkUserExists->username,
            ($request->is_user_ios  == true ? "Set ios " : "Un ios ") . $checkUserExists->username . " from " . $request->user->username,
        );

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkUserExists->id)->first(),
        ], 200);
    }

    /**
     * Thông tin 1 staff bằng email
     * 
     */
    public function getOneByEmail(Request $request)
    {
        $email = $request->route()->parameter('email');
        $checkStaffExists = User::where(
            'email',
            $email
        )->first();

        if ($checkStaffExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[1],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }


        if ($request->user->is_admin == false && $checkStaffExists->of_agency_id != $request->agency->id) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkStaffExists->id)->first(),
        ], 200);
    }

    /**
     * Thông tin 1 user renew bằng email
     * 
     */
    public function getRenewInfoByEmail(Request $request)
    {
        $email = $request->route()->parameter('email');
        $checkUserExists = User::where(
            'email',
            $email
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[1],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::select('expiry_alldata', 'expiry_autodata', 'expiry_haynespro')->where('id',  $checkUserExists->id)->first(),
        ], 200);
    }

    /**
     * Thông tin 1 staff bằng username
     * 
     */
    public function getOneByUsername(Request $request)
    {
        $username = $request->route()->parameter('username');
        $checkUserExists = User::where(
            'username',
            $username
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[1],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }
        if ($request->user->is_admin == false && $request->agency != null && $checkUserExists->of_agency_id != $request->agency->id) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $agency = Agency::where('id', $checkUserExists->of_agency_id)->first();
        $user = User::where('id',  $checkUserExists->id)->first();
        $user->agency = $agency;

        $devices = DeviceLogin::where('user_id',  $checkUserExists->id)->get();
        $user->devices = $devices;


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $user,
        ], 200);
    }

    /**
     * editNoteByUsername
     * 
     */
    public function editNoteByUsername(Request $request)
    {
        $username = $request->route()->parameter('username');
        $checkUserExists = User::where(
            'username',
            $username
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[1],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }
        if ($request->user->is_admin == false && $request->agency != null && $checkUserExists->of_agency_id != $request->agency->id) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $checkUserExists->update([
            'note' => $request->note
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkUserExists->id)->first(),
        ], 200);
    }


    /**
     * 
     * 
     * Thay đổi is admin
     * 
     * 
     */
    public function updateIsAdmin(Request $request)
    {
        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        // $checkUserExists->update([
        //     'is_admin' =>  $request->is_admin ?? 0,
        // ]);

        PushNotificationAdminJob::dispatch(
            ($request->setis_admin == true ? "Grant admin " : "Un admin ") . $checkUserExists->username,
            ($request->setis_admin  == true ? "Grant admin " : "Un admin ") . $checkUserExists->username . " from " . $request->user->username,
        );

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkUserExists->id)->first(),
        ], 200);
    }

    /**
     * 
     * 
     * Thay đổi vip
     * 
     * 
     */
    public function updateIsVip(Request $request)
    {
        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $checkUserExists->update([
            'is_vip' =>  $request->is_vip ?? 0,
        ]);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkUserExists->id)->first(),
        ], 200);
    }


    /**
     * Reset resetDevice
     * 
     * 
     * @bodyParam resetDevice
     */
    public function resetDevice(Request $request)
    {

        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }



        $checkUserExists->update([
            'platform' => null,
            'device_id' => null,
            'model' => null,
        ]);
        DeviceLogin::where('user_id',   $user_id)
            ->update([
                "ip_using" => null,
                "address" => null,
                "device_id" => null,
                "model_name" => null,
                "platform" => null,
                "last_visit_time" => null,
                "app_version" => null,
                "login_time" => null,
            ]);

        $devices = DeviceLogin::where('user_id',  $checkUserExists->id)->get();

        $checkUserExists = User::where('id',  $checkUserExists->id)->first();
        $checkUserExists->devices = $devices;

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $checkUserExists,
        ], 200);
    }

    /**
     * Reset password
     * 
     * 
     * @bodyParam password String pass
     */
    public function resetPassword(Request $request)
    {

        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($request->user->is_admin == false && $request->agency != null && $checkUserExists->of_agency_id != $request->agency->id) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $checkUserExists->update([
            'password' => bcrypt($request->password),
        ]);

        $textAgent = "";
        if ($request->agency != null) {
            $agency_code = $request->agency->agency_code;
            $textAgent = "agency $agency_code ";
        }

        PushNotificationAdminJob::dispatch(
            ("Change password for ") . $checkUserExists->username,
            ("Change password for ") . $checkUserExists->username . " from " . $textAgent . $request->user->username,
        );

        $checkTokenExists = SessionUser::where(
            'user_id',
            $checkUserExists->id
        )->first();

        if ($checkTokenExists) {
            $checkTokenExists->delete();
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkUserExists->id)->first(),
        ], 200);
    }

    /**
     * Cập nhật thông tin profile
     * @bodyParam name String   Họ và tên
     */
    public function update(Request $request)
    {

        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }



        $checkUserExists->update([
            'name' =>  $request->name,
        ]);



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkUserExists->id)->first(),
        ], 200);
    }

    /**
     * Thông tin 1 user
     * 
     */
    public function getOne(Request $request)
    {
        $user_id = $request->route()->parameter('user_id');
        $checkUserExists = User::where(
            'id',
            $user_id
        )->first();

        if ($checkUserExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }
        if ($request->user->is_admin == false && $request->agency != null && $checkUserExists->of_agency_id != $request->agency->id) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::where('id',  $checkUserExists->id)->first(),
        ], 200);
    }

    /**
     * 
     * Xuất excel
     * 
     */
    public function getLinkExportExcelUser(Request $request)
    {
        $actual_link = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]";


        $carbon = Carbon::now();
        $dateFrom = Carbon::parse(Carbon::parse($carbon)->format('Y-m-d 00:00:00'))->timezone('UTC')->toDateTimeString();
        $dateTo = Carbon::parse(Carbon::parse($carbon)->format('Y-m-d 23:59:59'))->timezone('UTC')->toDateTimeString();

        $startDate   = $request->start_date ?? $dateFrom;
        $endDate     = $request->end_date ??  $dateTo;

        $key_get =  Helper::generateRandomString(20);
        $his = HistoryExportExcel::create([
            "user_id" => $request->user->id,
            "key_get" => $key_get,
            "start_date" =>  $startDate,
            "end_date" => $endDate,
            "type" =>  $request->type
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => [
                "link" => $actual_link . "/api/admin/download_export_excel_users?key_get=$key_get"
            ]
        ], 200);
    }


    /**
     * 
     * Xuất excel
     * 
     */
    public function downloadExportExcelUser(Request $request)
    {
        $now = Carbon::now();
        $twentyFourHoursAgo = $now->subHours(1);
        $his = HistoryExportExcel::where("key_get", request('key_get'))->where('updated_at', '>=', $twentyFourHoursAgo)->first();

        if ($his == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NOT_FOUND[0],
                'msg' => MsgCode::NOT_FOUND[1],
            ], 400);
        }

        $carbon = Carbon::now();
        $dateFrom = Carbon::parse(Carbon::parse($carbon)->format('Y-m-d 00:00:00'))->timezone('UTC')->toDateTimeString();
        $dateTo = Carbon::parse(Carbon::parse($carbon)->format('Y-m-d 23:59:59'))->timezone('UTC')->toDateTimeString();

        $startDate   = $his->start_date ?? $dateFrom;
        $endDate     = $his->end_date ??  $dateTo;


        $agentId = null;
        if ($his->type == 0) {
            $agent = Agency::where('user_id', $his->user_id)->first();
            if ($agent == null) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::NOT_FOUND[0],
                    'msg' => MsgCode::NOT_FOUND[1],
                ], 400);
            }
            $agentId  = $agent->id;
        }



        $export = new UsersExport($startDate, $endDate,  $agentId, $his->type);
        $currentTime = new DateTime();

        $start = DateTime::createFromFormat('Y-m-d H:i:s', $startDate);
        $end = DateTime::createFromFormat('Y-m-d H:i:s', $endDate);

        $fileName = "DrCar-Users-" . $start->format('Y-m-d') . "-" . $end->format('Y-m-d')  . ".xls";

        return Excel::download($export, $fileName, \Maatwebsite\Excel\Excel::XLS);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $his
        ], 200);
    }
}
