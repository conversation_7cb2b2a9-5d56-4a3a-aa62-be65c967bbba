<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\PlanUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\PlanService;
use Illuminate\Http\Request;


/**
 * @group  Admin/Gói
 */

class AdminPlanController extends Controller
{


    /**
     * Thêm gói
     * 
     */
    public function create(Request $request)
    {

        if ($request->service != PlanUtils::AUTODATA && $request->service != PlanUtils::ALLDATA) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_VALUE[0],
                'msg' => MsgCode::INVALID_VALUE[1],
            ], 400);
        }

        if ($request->price < 0 || $request->month < 0) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_VALUE[0],
                'msg' => MsgCode::INVALID_VALUE[1],
            ], 400);
        }

        PlanService::create([
         
            'service' => $request->service,
            'price' => $request->price,
            'month' => $request->month,
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
