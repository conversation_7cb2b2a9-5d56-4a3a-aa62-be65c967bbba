<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\AllDataUSv2Utils;
use App\Helper\AllDataUtils;
use App\Helper\AutoDataUtils;
use App\Helper\PayPalDataUtils;
use App\Http\Controllers\Controller;
use App\Models\AccountItem;
use App\Models\AdminSetting;
use App\Models\ConfigAdmin;
use App\Models\MsgCode;
use Illuminate\Http\Request;

/**
 * @group  User/cài đặt
 */
class AdminConfigController extends Controller
{


    /**
     * Cài đặt cấu hình
     * 
     * 
     */
    public function updateSetting(Request $request)
    {


        $c =      AdminSetting::first();
        if ($c == null) {
            $c =  AdminSetting::create(
                [
                    "allow_renewal_extension" => $request->allow_renewal_extension,
                    "version_ios" => $request->version_ios,
                    "version_android" => $request->version_android,
                    "version_window" => $request->version_window,
                    "app_id_paypal" => $request->app_id_paypal,
                    "noti_new" => $request->noti_new,
                ]
            );
        } else {
            $c->update(
                [
                    "allow_renewal_extension" => $request->allow_renewal_extension,
                    "version_ios" => $request->version_ios,
                    "version_android" => $request->version_android,
                    "version_window" => $request->version_window,
                    "app_id_paypal" => $request->app_id_paypal,
                    "noti_new" => $request->noti_new,
                ]
            );
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $c
        ], 200);
    }

    /**
     * Lấy cài đặt cấu hình badges
     * 
     * 
     */
    public function getSetting(Request $request)
    {

        $c =      AdminSetting::first()->toArray();
        $listLives = [];

        foreach (PayPalDataUtils::listLive() as $item) {

            array_push(
                $listLives,
                [
                    "name" =>  $item['name'],
                    "app_id" =>  $item['app_id'],
                ]
            );
        }

        $c['paypals'] =    $listLives;
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $c
        ], 200);
    }

    /**
     * Cập nhật cookie từ xa
     * 
     * 
     * @bodyParam field Tên cột dữ liệu cần cập nhật
     * @bodyParam value giá trị cần cập nhật cho cột
     * 
     */
    public function update_one_field_config_remote(Request $request)
    {

        $c =      ConfigAdmin::first();
        $field = ($request->field);
        if ($request->value != null && strlen($request->value) > 0) {

            $c->update(
                [
                    $field => $request->value
                ]
            );
        }



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' =>  $c->$field,
        ], 200);
    }

     /**
     * Cập nhật account items
     * 
     * 
     * @bodyParam service Tên service cần cập nhật
     * @bodyParam password 
     * @bodyParam username 
     * 
     */
    public function update_account_items_remote(Request $request)
    {

        if ($request->service == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Need service",
            ], 400);
        }
        $c =   AccountItem::where('service', $request->service)->first();

        $c->update(
            [
                "username" => $request->username,
                "password" => $request->password,
            ]
        );

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }


    /**
     * Cài đặt cấu hình
     * 
     * 
     */
    public function updateConfig(Request $request)
    {

        $dataOkAllData = null;
        $dataOkAllUSData = null;
        $dataOkAutoData = null;

        $c =      ConfigAdmin::first();



        if ($c == null) {
            $c =  ConfigAdmin::create(
                [
                    "cookies_alldata_str" => $request->cookies_alldata_str,
                    "proxy_alldata" => $request->proxy_alldata,
                    "temp_alldata_eu" => $request->temp_alldata_eu,

                    "cookies_autodata_str" => $request->cookies_autodata_str,
                    "proxy_autodata" => $request->proxy_autodata,
                    "temp_autodata" => $request->temp_autodata,

                    "cookies_autodata_italy_str" => $request->cookies_autodata_italy_str,
                    "proxy_autodata_italy" => $request->proxy_autodata_italy,
                    "temp_autodata_italy" => $request->temp_autodata_italy,
                    

                    "cookies_alldata_us_v2_str" => $request->cookies_alldata_us_v2_str,
                    "proxy_alldata_us_v2" => $request->proxy_alldata_us_v2,
                    "temp_alldata_us" => $request->temp_alldata_us,

                    "cookies_haynes_str" => $request->cookies_haynes_str,
                    "proxy_haynespro" => $request->proxy_haynespro,

                    "cookies_mitchell_repair_center_str" => $request->cookies_mitchell_repair_center_str,
                    "proxy_mitchell_repair_center" => $request->proxy_mitchell_repair_center,

                    "cookies_mitchell_prodemand_str" => $request->cookies_mitchell_prodemand_str,
                    "proxy_mitchell_prodemand" => $request->proxy_mitchell_prodemand,
                    "temp_mitchell_prodemand" => $request->temp_mitchell_prodemand,
                    
                    "cookies_haynes_truck_str" => $request->cookies_haynes_str_truck,
                    "proxy_haynespro_truck" => $request->proxy_haynespro_truck,

                    "cookies_tecdoc_str" => $request->cookies_tecdoc_str,
                    "proxy_tecdoc" => $request->proxy_tecdoc,

                    "cookies_etka_str" => $request->cookies_etka_str,
                    "proxy_etka" => $request->proxy_etka,
                    
                    "cookies_identifix_str" => $request->cookies_identifix_str,
                    "proxy_identifix" => $request->proxy_identifix,

                    "cookies_partslink24_str" => $request->cookies_partslink24_str,
                    "proxy_partslink24" => $request->proxy_partslink24,

                    "cookies_ford_pts_str" => $request->cookies_ford_pts_str,
                    "proxy_ford_pts" => $request->proxy_ford_pts,
                ]
            );
        } else {
            $c->update(
                [
                    "cookies_alldata_str" => empty($request->cookies_alldata_str)  ? $c->cookies_alldata_str : $request->cookies_alldata_str,
                    "proxy_alldata" => $request->proxy_alldata,
                    "temp_alldata_eu" => $request->temp_alldata_eu,
                    
                    // "cookies_alldata_json_info" => empty($dataOkAllData)  ? $c->cookies_alldata_json_info : json_encode($dataOkAllData),
                    "cookies_autodata_str" => empty($request->cookies_autodata_str) ? $c->cookies_autodata_str :  $request->cookies_autodata_str,
                    "proxy_autodata" =>  $request->proxy_autodata,
                    "temp_autodata" => $request->temp_autodata,

                    "cookies_autodata_italy_str" => $request->cookies_autodata_italy_str,
                    "proxy_autodata_italy" => $request->proxy_autodata_italy,
                    "temp_autodata_italy" => $request->temp_autodata_italy,

                    // "cookies_autodata_json_info" => empty($dataOkAutoData) ? $c->cookies_autodata_json_info :  json_encode($dataOkAutoData),
                    "cookies_alldata_us_v2_str" => empty($request->cookies_alldata_us_v2_str) ? $c->cookies_alldata_us_v2_str :  $request->cookies_alldata_us_v2_str,
                    "proxy_alldata_us_v2" => $request->proxy_alldata_us_v2,
                    "temp_alldata_us" => $request->temp_alldata_us,

                    //"cookies_alldata_us_v2_json_info" => empty($dataOkAllUSData) ? $c->cookies_alldata_us_v2_json_info : json_encode($dataOkAllUSData),
                    "cookies_haynes_str" => empty($request->cookies_haynes_str) ? $c->cookies_haynes_str : $request->cookies_haynes_str,
                    "proxy_haynespro" =>  $request->proxy_haynespro,

                    "cookies_haynes_truck_str" => $request->cookies_haynes_truck_str,
                    "proxy_haynespro_truck" => $request->proxy_haynespro_truck,

                    "cookies_mitchell_repair_center_str" => $request->cookies_mitchell_repair_center_str,
                    "proxy_mitchell_repair_center" => $request->proxy_mitchell_repair_center,

                    "cookies_mitchell_prodemand_str" => $request->cookies_mitchell_prodemand_str,
                    "proxy_mitchell_prodemand" => $request->proxy_mitchell_prodemand,
                    "temp_mitchell_prodemand" => $request->temp_mitchell_prodemand,

                    "cookies_tecdoc_str" => $request->cookies_tecdoc_str,
                    "proxy_tecdoc" => $request->proxy_tecdoc,

                    "cookies_etka_str" => $request->cookies_etka_str,
                    "proxy_etka" => $request->proxy_etka,

                    "cookies_identifix_str" => $request->cookies_identifix_str,
                    "proxy_identifix" => $request->proxy_identifix,

                    "cookies_partslink24_str" => $request->cookies_partslink24_str,
                    "proxy_partslink24" => $request->proxy_partslink24,

                    "cookies_ford_pts_str" => $request->cookies_ford_pts_str,
                    "proxy_ford_pts" => $request->proxy_ford_pts,
                ]
            );
        }
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $c
        ], 200);
    }

    /**
     * Cài đặt cấu hình
     * 
     * 
     */
    public function getConfig(Request $request)
    {

        $c =      ConfigAdmin::first();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $c
        ], 200);
    }

}
