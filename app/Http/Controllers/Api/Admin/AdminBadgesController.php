<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\Place;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Illuminate\Http\Request;

/**
 * @group  Admin/badges
 */
class AdminBadgesController extends Controller
{

    /**
     * Tạo <PERSON> thông tin bagdes
     */
    public function getBadges(Request $request)
    {
        return response()->json([
            'code' => 200,
            'success' => true,
            'data' => [
                "user" => $request->user,
                "agency" => $request->agency,
            ],
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
