<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\MoneyBalanceUtils;
use App\Helper\Place;
use App\Helper\SendEmailUtils;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\EmailSupport;
use App\Models\MsgCode;
use App\Models\Email;
use App\Models\HistorySendEmailSuport;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON><PERSON>n\Type\NullType;
use Mail;

/**
 * @group  Admin/Quản lý email
 */

class AdminEmailSupportController extends Controller
{
    /**
     * Danh sách emails
     * 
     * @bodyParam search required Tìm theo tiêu đề
     * 
     */
    public function getAll(Request $request)
    {

        $search = StringUtils::convert_name_lowcase(request('search'));

        $all = EmailSupport::orderBy('created_at', 'desc')
            ->search($search)
            ->paginate(20);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Danh sách lịch sử emails
     * 
     * @bodyParam search required Tìm theo tiêu đề
     * 
     */
    public function getAllHistorySendEmail(Request $request)
    {

        $search = StringUtils::convert_name_lowcase(request('search'));

        $all = HistorySendEmailSuport::when(request('email_support_id') !== null, function ($query) {
            $query->where('email_support_id', request('email_support_id'));
        })->orderBy('created_at', 'desc')
            // ->search($search)
            ->paginate(20);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Xóa email
     * 
     * @bodyParam search required Tìm theo tên hoặc sdt
     * 
     */
    public function delete(Request $request)
    {

        $email_id = $request->route()->parameter('email_id');
        $checkEmailExists = EmailSupport::where(
            'id',
            $email_id
        )->first();

        if ($checkEmailExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::EMAIL_ALREADY_EXISTS[1],
                'msg' => MsgCode::EMAIL_ALREADY_EXISTS[1],
            ], 400);
        }

        $checkEmailExists->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }


    /**
     * Thêm 1 email

     * @bodyParam name String  Tên gợi nhớ
     * @bodyParam title String  Tiêu đề
     * @bodyParam description string required Mô tả ngắn
     * @bodyParam content string required content nội dung email
     * 
     * @bodyParam send_to 0 Không có 1 gửi theo nhóm, 2 gửi tất cả, 3 khách hàng hành động
     * @bodyParam send_to_condition (khi khách hàng hành động send_to == 3)
     * 
     * @bodyParam group_user_id nếu send_to == 1 thì truyền thêm id nhóm
     * 
     * @bodyParam schedule_type 0 gửi thủ công, 1 gửi 1 lần, 2 gửi tự động theo điều kiện

     * @bodyParam send_time nếu            schedule_type == 1  truyền time này lên
     * @bodyParam condition_time_send nếu  schedule_type == 2  định nghĩa dưới
     * 
     */
    public function create(Request $request)
    {


        //condition_time_send
        // 0 Không có
        // (11 -tròn 3 ngày hết alldata,  )
        // (12 -tròn 3 ngày hết autodata,  )
        // (13 -tròn 3 ngày hết haynespro,  )

        //send_to_condition
        // 0 Không có
        // (21 -đăng ký gia hạn thành công alldata)
        // (22 -đăng ký gia hạn thành công autodata)
        // (23 -đăng ký gia hạn thành công haynespro)
        // (31 -đăng ký tài khoản thành công)

        if (empty($request->title)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NAME_IS_REQUIRED[0],
                'msg' => MsgCode::NAME_IS_REQUIRED[1],
            ], 400);
        }


        $emailCreated = EmailSupport::create([
            'name' =>  $request->name,
            'title' =>  $request->title,
            'description' => $request->description,
            'content' => $request->content,
            
            "send_to_gate" => $request->send_to_gate,
            "send_to" => $request->send_to,
            "send_to_condition" => $request->send_to_condition,
            "group_user_id" => $request->group_user_id,
            "schedule_type" => $request->schedule_type,
            "send_time" => $request->send_time,
            "condition_time_send" => $request->condition_time_send,

        ]);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => EmailSupport::where('id', $emailCreated->id)->first(),
        ], 200);
    }



    /**
     * Cập nhật email
     * 
     * @bodyParam title String  Tiêu đề
     * @bodyParam description string required Mô tả ngắn
     * @bodyParam content string required content nội dung email

     */
    public function update(Request $request)
    {
        $email_id = $request->route()->parameter('email_id');
        $checkEmailExists = EmailSupport::where(
            'id',
            $email_id
        )->first();

        if ($checkEmailExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_EMAIL_EXISTS[1],
                'msg' => MsgCode::NO_EMAIL_EXISTS[1],
            ], 400);
        }




        $checkEmailExists->update([
            'name' =>  $request->name,
            'title' =>  $request->title,
            'description' => $request->description,
            'content' => $request->content,
            "send_to" => $request->send_to,
            "send_to_gate" => $request->send_to_gate,
            
            "send_to_condition" => $request->send_to_condition,
            "group_user_id" => $request->group_user_id,
            "schedule_type" => $request->schedule_type,
            "send_time" => $request->send_time,
            "condition_time_send" => $request->condition_time_send,
        ]);



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => EmailSupport::where('id',  $checkEmailExists->id)->first(),
        ], 200);
    }

     /**
     * Cập nhật running email
     * 
     * @bodyParam running String  Chạy hay không

     */
    public function updateRunning(Request $request)
    {
        $email_id = $request->route()->parameter('email_id');
        $checkEmailExists = EmailSupport::where(
            'id',
            $email_id
        )->first();

        if ($checkEmailExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_EMAIL_EXISTS[1],
                'msg' => MsgCode::NO_EMAIL_EXISTS[1],
            ], 400);
        }




        $checkEmailExists->update([
            'running' =>  $request->running,
        ]);



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => EmailSupport::where('id',  $checkEmailExists->id)->first(),
        ], 200);
    }
    

    /**
     * Thông tin 1 email
     * 
     */
    public function getOne(Request $request)
    {
        $email_id = $request->route()->parameter('email_id');
        $checkEmailExists = EmailSupport::where(
            'id',
            $email_id
        )->first();

        if ($checkEmailExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_EMAIL_EXISTS[1],
                'msg' => MsgCode::NO_EMAIL_EXISTS[1],
            ], 400);
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => EmailSupport::where('id',  $checkEmailExists->id)->first(),
        ], 200);
    }

    /**
     * Send thử 1 email
     * 
     */
    public function send(Request $request)
    {
        $email_send_to = $request->email;

        if ($email_send_to  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_EMAIL_EXISTS[0],
                'msg' => MsgCode::NO_EMAIL_EXISTS[1],
            ], 400);
        }


        $email_id = $request->route()->parameter('email_id');
        $checkEmailExists = EmailSupport::where(
            'id',
            $email_id
        )->first();

        if ($checkEmailExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_EMAIL_EXISTS[1],
                'msg' => MsgCode::NO_EMAIL_EXISTS[1],
            ], 400);
        }

        SendEmailUtils::send([$email_send_to],   $email_id, SendEmailUtils::EMAIL_TEST);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
