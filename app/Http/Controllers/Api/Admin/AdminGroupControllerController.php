<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\Helper;
use App\Http\Controllers\Controller;
use App\Models\GroupUser;
use App\Models\GroupUserConditionItem;

use App\Models\MsgCode;
use Illuminate\Http\Request;


/**
 * @group User/Nhóm khách hàng
 * 
 */
class AdminGroupControllerController extends Controller
{
    /**
     * Tạo nhóm khách hàng
     * 
     * @bodyParam name string required Tên nhóm khách hàng
     * @bodyParam note file required <PERSON><PERSON> chú
     * @bodyParam list {compare_field,compare_expression,compare_value)
     * 
     * @bodyParam compare_field Kiểu so sánh //0 Tổng mua (Chỉ đơn hoàn thành), 1 tổng bán, 2 Xu hiện tại, 3 Số lần mua hàng 4, tháng sinh nhật 5, tuổi 6, giới t<PERSON>h, 7 tỉnh, 8 ngày đăng ký
     * @bodyParam compare_expression <PERSON><PERSON><PERSON><PERSON> thức so sánh  (>,>=,=,<,<=)
     * @bodyParam compare_value Gi<PERSON> trị so sánh so sánh
     * 
     */
    public function create(Request $request)
    {



        $checkGroupExists = GroupUser::where(
            'name',
            $request->name
        )->first();

        if ($checkGroupExists != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NAME_ALREADY_EXISTS[0],
                'msg' => MsgCode::NAME_ALREADY_EXISTS[1],
            ], 400);
        }

        $categoryCreate = GroupUser::create(
            [

                'name' => $request->name,
                'note' => $request->note,

            ]
        );

        if ($request->condition_items != null && is_array($request->condition_items)) {
            foreach ($request->condition_items as $item) {
                GroupUserConditionItem::create([
                    'group_user_id' =>    $categoryCreate->id,
                    'compare_field' => $item['compare_field'] ?? "",
                    'compare_expression' => $item['compare_expression'] ?? "",
                    'compare_value' => $item['compare_value'] ?? "",
                    'compare_merge_condition' => $item['compare_merge_condition'] ?? "",
                ]);
            }
        }



        return response()->json([
            'code' => 201,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $categoryCreate
        ], 201);
    }


    /**
     * Danh sách nhóm khách hàng
     * @urlParam  store_code required Store code
     */
    public function getAll(Request $request)
    {

        $categories = GroupUser::orderBy('id', 'desc')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $categories,
        ], 200);
        
    }




    /**
     * update một GroupUser
     * 
     * @bodyParam name string required Tên nhóm khách hàng
     * @bodyParam note file required Ghi chú
     * @bodyParam list {compare_field,compare_expression,compare_value)
     * 
     * @bodyParam compare_field Kiểu so sánh //0 Tổng mua (Chỉ đơn hoàn thành), 1 tổng bán, 2 Xu hiện tại, 3 Số lần mua hàng 4, tháng sinh nhật 5, tuổi 6, giới tính, 7 tỉnh, 8 ngày đăng ký
     * @bodyParam compare_expression Biểu thức so sánh  (>,>=,=,<,<=)
     * @bodyParam compare_value Giá trị so sánh so sánh
     */
    public function update(Request $request)
    {


        $id = $request->route()->parameter('group_user_id');
        $checkGroupUserExists = GroupUser::where(
            'id',
            $id
        )->first();

        if (empty($checkGroupUserExists)) {
            return response()->json([
                'code' => 404,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_USER[0],
                'msg' => MsgCode::NO_GROUP_USER[1],
            ], 404);
        } else {
            $checkGroupUserExists2 = GroupUser::where(
                'name',
                $request->name
            )->where(
                'id',
                '<>',
                $id
            )->first();
            if ($checkGroupUserExists2 != null) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::NAME_ALREADY_EXISTS[0],
                    'msg' => MsgCode::NO_GROUP_USER[1],
                ], 400);
            }

            $checkGroupUserExists->update(Helper::sahaRemoveItemArrayIfNullValue([
                'name' => $request->name,

                'note' => $request->note,
                'group_type' => $request->group_type,

            ]));


            GroupUserConditionItem::where([

                'group_user_id' =>    $checkGroupUserExists->id,
            ])->delete();

            if ($request->condition_items != null && is_array($request->condition_items)) {
                foreach ($request->condition_items as $item) {
                    GroupUserConditionItem::create([

                        'group_user_id' =>    $checkGroupUserExists->id,
                        'compare_field' => $item['compare_field'] ?? "",
                        'compare_expression' => $item['compare_expression'] ?? "",
                        'compare_value' => $item['compare_value'] ?? "",
                        'compare_merge_condition' => $item['compare_merge_condition'] ?? "",
                        
                    ]);
                }
            }


            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => GroupUser::where('id', $id)->first(),
            ], 200);
        }
    }


    /**
     * remove một GroupUser
     * 
     */
    public function delete(Request $request)
    {


        $id = $request->route()->parameter('group_user_id');
        $checkGroupUserExists = GroupUser::where(
            'id',
            $id
        )->first();

        if (empty($checkGroupUserExists)) {
            return response()->json([
                'code' => 404,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_USER[0],
                'msg' => MsgCode::NO_GROUP_USER[1],
            ], 404);
        }
        $checkGroupUserExists->delete();
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }


    /**
     * thông tin 1 nhóm khách hàng
     * 
     */
    public function getOne(Request $request)
    {

        $id = $request->route()->parameter('group_user_id');
        $checkGroupUserExists = GroupUser::where(
            'id',
            $id
        )->first();

        if (empty($checkGroupUserExists)) {
            return response()->json([
                'code' => 404,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_USER[0],
                'msg' => MsgCode::NO_GROUP_USER[1],
            ], 404);
        }


        $checkGroupUserExists->condition_items = GroupUserConditionItem::select('compare_field', 'compare_expression', 'compare_value','compare_merge_condition')
            ->where([
                ['group_user_id', $checkGroupUserExists->id],

            ])
            ->get();



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $checkGroupUserExists
        ], 200);
    }
}
