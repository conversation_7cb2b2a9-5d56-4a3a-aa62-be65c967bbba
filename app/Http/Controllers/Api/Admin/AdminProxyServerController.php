<?php

namespace App\Http\Controllers\Api\Admin;


use App\Helper\AllDataUSv2UtilsWeb;
use App\Helper\AllDataUtils;

use App\Helper\AutoDataUtils;
use App\Helper\FordUtilsWeb;
use App\Helper\HaynesTruckUtilsWeb;
use App\Helper\HaynesUtils;
use App\Helper\IdentifixUtilsWeb;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\ProxyServer;
use Illuminate\Http\Request;


/**
 * @group  Admin/Proxy server dự phòng
 */

class AdminProxyServerController extends Controller
{


    /**
     * Thêm proxy server
     * 
     * @bodyParam service Proxy cho dịch vụ AUTODATA ALLDATA
     * @bodyParam proxy Chuỗi proxy
     * 
     */
    public function create(Request $request)
    {


        ProxyServer::create([
            'name' => $request->name,
            'service' => $request->service,
            'proxy' => $request->proxy,
            'cookies_str' => $request->cookies_str,
        ]);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }


    /**
     * Danh sách proxy
     * 
     * @queyParam service Dịch vụ
     * 
     */
    public function getAll(Request $request)
    {

        $all = ProxyServer::when(request('service') != null, function ($query) {
            $query->where('service', request('service'));
        })->orderBy('id', 'desc')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Xóa 1 proxy server
     * 
     * @urlParam proxy_server_id
     * 
     */
    public function delete(Request $request, $proxy_server_id)
    {


        ProxyServer::where('id', $proxy_server_id)->delete();


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Cập nhật 1 proxy server
     * 
     * @urlParam proxy_server_id
     * 
     */
    public function update(Request $request, $proxy_server_id)
    {


        $k =   ProxyServer::where('id', $proxy_server_id)->first();

        $k->update([
            'name' => $request->name,
            'service' => $request->service,
            'proxy' => $request->proxy,

            'is_use' => $request->is_use,
            'cookies_str' => $request->cookies_str,

            'note' => $request->note,
        ]);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>   $k
        ], 200);
    }


    /**
     * Get data check server proxy
     * 
     * @urlParam proxy_server_id
     * 
     */
    public static function getDataServerProxy($proxy_server_id)
    {


        $data = null;
        $k =   ProxyServer::where('id', $proxy_server_id)->first();

        if ($k->service == "ALLDATAEU") {
            $data = AllDataUtils::checkServer($k->cookies_str, $k->proxy);
        }
        if ($k->service == "ALLDATAUS") {
            $data = AllDataUSv2UtilsWeb::checkServer($k->cookies_str, $k->proxy);
        }
        if ($k->service == "AUTODATA") {
            $data = AutoDataUtils::checkServer($k->cookies_str, $k->proxy);
        }
        if ($k->service == "AUTODATAITALY") {
            $data = AutoDataUtils::checkServer($k->cookies_italy_str, $k->proxy);
        }
        if ($k->service == "HAYNESPRO") {
            $data = HaynesUtils::checkServer($k->cookies_str, $k->proxy);
        }
        if ($k->service == "HAYNESPROTRUCK") {
            $data = HaynesTruckUtilsWeb::checkServer($k->cookies_str, $k->proxy);
        }
        if ($k->service == "IDENTIFIX") {
            $data = IdentifixUtilsWeb::checkServer($k->cookies_str, $k->proxy);
        }
        if ($k->service == "FORD_PTS") {
            $data = FordUtilsWeb::checkServer($k->cookies_str, $k->proxy);
        }

        if ($data  != null) {
            $k->update([
                'is_ok' =>  $data['status'] ?? false,
                'error_mess' =>  $data['mess'] ?? "",
                'user_name' =>  $data['user_name'] ?? "",
                'user_id' =>  $data['user_id'] ?? "",

                'email' =>  $data['email'] ?? "",
                'first_name' =>  $data['first_name'] ?? "",
                'last_name' =>  $data['last_name'] ?? "",

                'site_name' =>  $data['site_name'] ?? "",
            ]);
        }

        return  $data;
    }

    /**
     * Kiểm tra 1 server proxy
     * 
     * @urlParam proxy_server_id
     * 
     */
    public static function checkServerProxy(Request $request, $proxy_server_id)
    {


        $data  = AdminProxyServerController::getDataServerProxy($proxy_server_id);

        $k =   ProxyServer::where('id', $proxy_server_id)->first();


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>   $k
        ], 200);
    }
}
