<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\MsgCode;
use App\Models\AccountItem;
use Illuminate\Http\Request;

/**
 * @group  User/Account
 */
class AdminAccountController extends Controller
{

    /**
     * Danh sách account
     */
    public function getAll(Request $request)
    {

        $accounts = AccountItem::orderBy('position', 'ASC')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $accounts,
        ], 200);
    }

    /**
     * Thêm account
     */
    public function create(Request $request)
    {

        $hasAccount = AccountItem::where('account', $request->account)->first();

        if ($hasAccount  != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Đã có account này",
            ], 400);
        }
        $created = AccountItem::create([
            'account' => $request->account,
            'note' => $request->note
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $created,
        ], 200);
    }

    /**
     * Cập nhật account
     */
    public function update(Request $request, $account_id)
    {


        $hasAccount = AccountItem::where('id', $account_id)->first();

        if ($hasAccount  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không tồn tại",
            ], 400);
        }
        $hasAccount->update([
            'account' => $request->account,
            'note' => $request->note
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $hasAccount,
        ], 200);
    }

        /**
     * Cập nhật updateSortAccount
     */
    public function updateSortAccount(Request $request)
    {
        if($request->list != null) {
            foreach($request->list as $item) {
                $hasAccount = AccountItem::where('id',$item['id'])->first();

                if(  $hasAccount  != null) {
                    $hasAccount ->update([
                        'position' =>  $item['position']
                    ]);
                }
            }
        }
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
    

      /**
     * Remove account
     */
    public function delete(Request $request, $account_id)
    {


        $hasAccount = AccountItem::where('id', $account_id)->first();

        if ($hasAccount  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không tồn tại",
            ], 400);
        }
        $hasAccount->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
