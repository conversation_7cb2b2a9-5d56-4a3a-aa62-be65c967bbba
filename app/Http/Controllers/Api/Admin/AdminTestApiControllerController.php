<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\MoneyBalanceUtils;
use App\Helper\Place;
use App\Helper\SendEmailUtils;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\Staff;
use Exception;
use Illuminate\Http\Request;
use <PERSON><PERSON>ergmann\Type\NullType;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\Cache;
use Mail;

/**
 * @group  Admin/Test api
 */

class AdminTestApiControllerController extends Controller
{


    /**
     * Kiểm tra api
     * 
     */
    public function testApi(Request $request)
    {

        $ip = $request->ip(); // Lấy địa chỉ IP của client
        $key = 'throttle:' . $ip; // Key Cache dựa trên địa chỉ IP của client

        $allowedRequests = 50; // <PERSON><PERSON> lượng yêu cầu tối đa trong 1 giây
        $cacheTime = 5; // Thời gian cache trong giây

        $currentTimestamp = time();
        $expireTimestamp = $currentTimestamp + $cacheTime;

        $count = Cache::get($key, 0);

        if ($count >= $allowedRequests) {
            return response()->json(['error' => 'Too many requests. Please try again later.'], 429);
        }

        if ($count === 0 || $currentTimestamp > $expireTimestamp) {
            // Reset count and set new expiration timestamp
            Cache::put($key, 1, $cacheTime);
        } else {
            // Increment count
            Cache::increment($key);
        }

        return response()->json(['OK' => 'OK'], 200);
        die();

        setcookie("TestCookie", "hh");
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    public function switchingAppStatus(Request $request)
    {
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => false,
            'data2' => false
        ], 200);
    }
}
