<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\GroupUser;

use App\Models\MsgCode;
use App\Models\Statistic;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * @group Admin/Quán lý truy cập
 * 
 */
class AdminHistoryAccessController extends Controller
{




    public function statistic(Request $request)
    {

        $statistic = Statistic::where('day_statistic', $request->day_statistic)->first();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $statistic,
        ], 200);
    }
    /**
     * Danh sách truy cập top
     * 
     * @queryParam  date_from
     * @queryParam  date_to
     */
    public function getAll(Request $request)
    {
        $order_with = request('order_with') ?? 'ip_count';
        $user_id = request('user_id') ?? null;

        $dateFrom = request('date_from');
        $dateTo = request('date_to');
        $dateFrom = Carbon::parse(Carbon::parse($dateFrom)->format('Y-m-d 00:00:00'))->timezone('UTC')->toDateTimeString();
        $dateTo = Carbon::parse(Carbon::parse($dateTo)->format('Y-m-d 23:59:59'))->timezone('UTC')->toDateTimeString();


        $topUsers = DB::connection('mysql_history')->table('history_accesses')
            ->where('created_at', '>=', $dateFrom)
            ->where('created_at', '<', $dateTo)
            ->select(
                'user_id',
                DB::raw('COUNT(DISTINCT ip) as ip_count'),
                DB::raw('COUNT(*) as request_count'),
                DB::raw('COUNT(DISTINCT service) as service_count'),
                DB::raw('COUNT(DISTINCT use_agent) as user_agent_count'),
                DB::raw('COUNT(DISTINCT DATE_FORMAT(created_at, "%Y-%m-%d %H")) AS total_access_hours')

            )
            ->groupBy('user_id')
            ->when($user_id != null, function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            })
            ->orderByDesc($order_with)
            ->paginate(20);

        foreach ($topUsers as $log) {
            $userIps = DB::table('history_accesses')
                ->where('user_id', $log->user_id)
                ->select('ip')
                ->where('created_at', '>=',  $dateFrom)
                ->where('created_at', '<', $dateTo)
                ->distinct()
                ->get();
            $userAgents = DB::table('history_accesses')
                ->where('user_id', $log->user_id)
                ->select('use_agent')
                ->where('created_at', '>=',  $dateFrom)
                ->where('created_at', '<', $dateTo)
                ->distinct()
                ->get();

            $log->user = User::where('id', $log->user_id)->first();
            $log->ips =  $userIps;
            $log->user_agents =  $userAgents;
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $topUsers,
        ], 200);
    }

    public function detailAccess(Request $request)
    {
        $user_id = request('user_id') ?? null;
        $dateFrom = request('date_from');
        $dateTo = request('date_to');

        $dateFrom = Carbon::parse($dateFrom)->startOfDay();
        $dateTo = Carbon::parse($dateTo)->endOfDay();

        $query = DB::connection('mysql_history')->table('history_accesses')
            ->select(
                'user_id',
                DB::raw('COUNT(DISTINCT ip) as ip_count'),
                DB::raw('COUNT(*) as request_count'),
                DB::raw('COUNT(DISTINCT service) as service_count'),
                DB::raw('COUNT(DISTINCT use_agent) as user_agent_count'),
                DB::raw('COUNT(DISTINCT DATE_FORMAT(created_at, "%Y-%m-%d %H")) as total_access_hours'),
                DB::raw('GROUP_CONCAT(DISTINCT ip) as list_ip'),
                DB::raw('GROUP_CONCAT(DISTINCT service) as list_service'),
                DB::raw('GROUP_CONCAT(DISTINCT use_agent) as list_user_agent'),
                DB::raw('GROUP_CONCAT(DISTINCT DATE_FORMAT(created_at, "%Y-%m-%d %H")) as list_hour')
            )
            ->where('user_id', $user_id)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->groupBy('user_id')
            ->first();

        $user = User::where('id', $user_id)->first();

        // Transform the query result to match the expected structure
        $accessInfo = [
            '_id' => $query->user_id,
            'ip_count' => $query->ip_count,
            'request_count' => $query->request_count,
            'service_count' => $query->service_count,
            'user_agent_count' => $query->user_agent_count,
            'total_access_hours' => $query->total_access_hours,
            'list_ip' => explode(',', $query->list_ip),
            'list_service' => explode(',', $query->list_service),
            'list_user_agent' => explode(',', $query->list_user_agent),
            'list_hour' => explode(',', $query->list_hour),
        ];


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $accessInfo,
        ], 200);
    }
}
