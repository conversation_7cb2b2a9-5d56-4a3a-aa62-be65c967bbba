<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\AllDataUSv2UtilsWeb;
use App\Helper\AllDataUtils;
use App\Helper\AutoDataItalyUtilsWeb;
use App\Helper\AutoDataUtils;
use App\Helper\AutoDataUtilsWeb;
use App\Helper\DomainConfigHelper;
use App\Helper\ETKAUtilsWeb;
use App\Helper\FordUtilsWeb;
use App\Helper\HaynesTruckUtilsWeb;
use App\Helper\HaynesUtils;
use App\Helper\IdentifixUtilsWeb;
use App\Helper\MitchellProdemandUtilsWeb;
use App\Helper\MitchellRepairCenterUtilsWeb;
use App\Helper\Partslink24UtilsWeb;
use App\Helper\TecDocUtilsWeb;
use App\Http\Controllers\Controller;
use App\Models\ConfigAdmin;
use App\Models\MsgCode;
use Exception;
use App\Helper\FileUtils;
use App\Helper\UserUtils;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Str;


/**
 * @group  Admin/Check server car
 */

class AdminTestServerCarController extends Controller
{

    public function test_api(Request $request)
    {

           $pathWithFile = Str::ascii("Đỗ Tâm – Café & Bánh mì Hidd©©©en??Ascii ©©®®®Charac££ter Hidd©©©en??Ascii ©©®®®Charac££ter List of Korean Text Slang · ㅋㅋ (sound of laughter) · ㅎㅎㅎ (hahaha) · ㅍㅎㅎ (puhaha) · ㅉㅉ (tsk tsk) ///// bbb \\\\\· ㅇㅇ (yes) · ㅇㅋ (OK) · ㅇㅈ (agreed) · ㅇㅋㄷㅋ (okey-dokey).");
      
           dd($pathWithFile );

        $client = new Client();

        $response = $client->get('https://superetka.com/etka/index.php', [
            'query' => [
                'cat' => 'ajaxShowMainGr',
                'lang' => 'EN',
                'marke' => 'VW',
                'market' => '',
                'model' => '2402',
                'year' => '2407'
            ],
            'headers' => [
                'accept'             => '*/*',
                'accept-language'    => 'en,en-US;q=0.9,vi-VN;q=0.8,vi;q=0.7,zh-CN;q=0.6,zh;q=0.5,ca;q=0.4,ko;q=0.3,de;q=0.2,ar;q=0.1,hi;q=0.1',
                'cache-control'      => 'no-cache',
                'dnt'                => '1',
                'pragma'             => 'no-cache',
                'priority'           => 'u=1, i',
                'referer'            => 'https://superetka.com/etka/?lang=EN&marke=VW&model=2402&year=2407',
                'sec-ch-ua'          => '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                'sec-ch-ua-mobile'   => '?0',
                'sec-ch-ua-platform' => '"macOS"',
                'sec-fetch-dest'     => 'empty',
                'sec-fetch-mode'     => 'cors',
                'sec-fetch-site'     => 'same-origin',
                'user-agent'         => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-requested-with'   => 'XMLHttpRequest',
                'cookie'             => '_ym_uid=1735880704682078310; _ym_d=1745811129; _ym_isad=2; _ym_visorc=w; PHPSESSID=531f3084fdb13b40e34930647d8308db'
            ]
        ]);

        dd( $response->getBody()->getContents());
        $client = new Client();

        $response = $client->post('http://nas.fhost.ca/WebDataHandle/post-get-data.php', [
            'multipart' => [
                [
                    'name' => 'tail_link',
                    'contents' => '/api/Qualifier/GetQualifiers?year=2022&make=Nissan&model=Altima&engine=2.5L%20Eng%20VIN%20B&subModel=SR&fuelType=Gas&engineCode=Eng%20CD%20PR25DD&bodyStyle=4D%20Sedan&driveType=FWD&transferCaseType=Not%20Applicable%20T%2FCase%20Control&transmissionControlType=Automatic%20CVT%20Trans&transmissionCode=&odometer=&odometerUnit=&m1VehicleId=&license=&state=&vin=&type=transmissionCode'
                ],
                [
                    'name' => 'service',
                    'contents' => 'MITCHELL_PRODEMAND'
                ]
            ]
        ]);
        dd($response->getBody()->getContents());

        $s = UserUtils::getContactLink(133874);
        dd($s);
        return response()->json([
            'code' => 200,
            'success' => true,
            // 'time_handle' => ($time_end - $time_start),
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => "",
        ], 200);
    }
    /**
     * Check
     * 
     */
    public function check(Request $request)
    {

        $cols = [
            "cookies_alldata_str",
            "cookies_autodata_str",
            "cookies_autodata_italy_str",
            "cookies_alldata_us_v2_str",
            "cookies_haynes_str",
            "cookies_haynes_truck_str",
            'cookies_identifix_str',
            'cookies_partslink24_str',
            'cookies_tecdoc_str',
            'cookies_mitchell_repair_center_str',
            'cookies_mitchell_prodemand_str',
            'cookies_ford_pts_str',
            'cookies_etka_str',
        ];

        if (!in_array(request('column'),   $cols)) {
            return response()->json([
                'code' => 400,
                'success' => true,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Column check không hợp lệ',
            ], 400);
        }

        $column = request('column');
        $configAdmin =      ConfigAdmin::first();

        if ($configAdmin  == null) {
            $configAdmin =  ConfigAdmin::create([]);
        }

        if ($column == "cookies_alldata_str") {
            $data = AllDataUtils::checkServer();


            $configAdmin->update([
                'cookies_alldata_json_info' => json_encode($data)
            ]);
        }
        if ($column == "cookies_alldata_us_v2_str") {
            $data = AllDataUSv2UtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_alldata_us_v2_json_info' => json_encode($data)
            ]);
        }
        if ($column == "cookies_autodata_str") {

            $data = AutoDataUtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_autodata_json_info' => json_encode($data)
            ]);
        }

        if ($column == "cookies_autodata_italy_str") {
            $data = AutoDataItalyUtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_autodata_italy_json_info' => json_encode($data)
            ]);
        }

        if ($column == "cookies_haynes_str") {

            $data = HaynesUtils::checkServer();

            $configAdmin->update([
                'cookies_haynes_json_info' => json_encode($data)
            ]);
        }
        if ($column == "cookies_haynes_truck_str") {

            $data = HaynesTruckUtilsWeb::checkServer();
            $configAdmin->update([
                'cookies_haynes_truck_json_info' => json_encode($data)
            ]);
        }


        if ($column == "cookies_mitchell_repair_center_str") {

            $data = MitchellRepairCenterUtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_mitchell_repair_center_json_info' => json_encode($data)
            ]);
        }

        if ($column == "cookies_mitchell_prodemand_str") {

            $data = MitchellProdemandUtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_mitchell_prodemand_json_info' => json_encode($data)
            ]);
        }

        if ($column == "cookies_identifix_str") {

            $data = IdentifixUtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_identifix_json_info' => json_encode($data)
            ]);
        }
        if ($column == "cookies_tecdoc_str") {

            $data = TecDocUtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_tecdoc_json_info' => json_encode($data)
            ]);
        }

        if ($column == "cookies_partslink24_str") {

            $data = Partslink24UtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_partslink24_json_info' => json_encode($data)
            ]);
        }
        if ($column == "cookies_ford_pts_str") {

            $data = FordUtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_ford_pts_json_info' => json_encode($data)
            ]);
        }
        if ($column == "cookies_etka_str") {

            $data = ETKAUtilsWeb::checkServer();

            $configAdmin->update([
                'cookies_etka_json_info' => json_encode($data)
            ]);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $configAdmin
        ], 200);
    }

    /**
     * Check
     * 
     */
    public function web_check(Request $request)
    {

        $user_agent = $_SERVER['HTTP_USER_AGENT'];

        $ch = curl_init();    // initialize curl handle
        // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_URL, 'http://workshop.autodata-group.com/');
        curl_setopt($ch, CURLOPT_FAILONERROR, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 50);
        // curl_setopt($ch, CURLOPT_POSTFIELDS, $post_xml); 
        curl_setopt($ch, CURLOPT_USERAGENT, $user_agent);
        //  curl_setopt($ch, CURLOPT_PORT, $port);          

        //     curl_setopt($ch, CURLOPT_PROXY, "https://workshop.autodata-group.com/"); //your proxy url
        // curl_setopt($ch, CURLOPT_PROXYPORT, "443"); // your proxy port number

        $data = curl_exec($ch);
        $curl_errno = curl_errno($ch);
        $curl_error = curl_error($ch);
        if ($curl_errno > 0) {
            echo "cURL Error ($curl_errno): $curl_error\n";
        } else {
            echo "Data received\n";
        }
        curl_close($ch);

        echo $data;
    }

    /**
     * Check
     * 
     */
    public function check_autodata(Request $request)
    {

        dd(AutoDataUtils::checkServer()['mess']);
    }

    /**
     * Check
     * 
     */
    public function check_autodata_proxy(Request $request)
    {
        $time_start = microtime(true);
        $proxy = request('proxy') ?? "";
        try {
            $response  = AutoDataUtilsWeb::getResponse(
                'https://workshop.autodata-group.com/w2/api/user',
                "GET",
                [],
                "xxxx:xxx;",
                null,
                $proxy,
            );
            $time_end = microtime(true);

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => "",
                'data' => [
                    "status" => true,
                    'time_handle' => ($time_end - $time_start),
                    'mess' => "Status code:  " . ($response == null ? "" : $response->getStatusCode())
                ]
            ], 200);
        } catch (Exception $e) {

            $time_end = microtime(true);
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => "",
                'data' => [
                    "status" => false,
                    'time_handle' => ($time_end - $time_start),
                    'mess' =>  "Status code:  " . "" . " - " . $e->getMessage()
                ]
            ], 200);
        }
    }
}
