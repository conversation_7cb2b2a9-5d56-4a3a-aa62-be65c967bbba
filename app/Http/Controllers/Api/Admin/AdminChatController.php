<?php

namespace App\Http\Controllers\Api\Admin;

use App\Events\RedisChatEventAdminToUser;
use App\Helper\ChatUtils;
use App\Helper\Helper;
use App\Helper\IPUtils;
use App\Helper\SecurityUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationUserJob;
use App\Models\AdminChat;
use App\Models\UToUMessage;
use App\Models\MsgCode;
use App\Models\User;
use App\Models\UToAdminMessage;


/**
 * @group  User/Chat
 */

class AdminChatController extends Controller
{

    /**
     * Danh sách người chat với admin
     * 
     */
    public function getAllPerson(Request $request)
    {

        $all = AdminChat::orderBy('updated_at', 'desc')
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }


    /**
     * Xóa chat với 1 người
     * 
     */
    public function removeChat(Request $request)
    {

        $vs_user_id = $request->route()->parameter('vs_user_id');

        AdminChat::where('user_id', $request->user->id)->where('vs_user_id', $vs_user_id)->delete();
        AdminChat::where('user_id', $vs_user_id)->where('vs_user_id', $request->user->id)->delete();

        ChatUtils::sendListSocket($request->user->id, ChatUtils::LIST_USER_CHAT);
        ChatUtils::sendListSocket($vs_user_id, ChatUtils::LIST_USER_CHAT);

        UToUMessage::where('user_id', $request->user->id)->where('vs_user_id', $vs_user_id)->update([
            'is_remove' => true
        ]);
        UToUMessage::where('user_id', $vs_user_id)->where('vs_user_id', $request->user->id)->update([
            'is_remove' => true
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Danh sách tin nhắn với 1 người
     * @bodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * 
     * 
     */
    public function getAllMessage(Request $request)
    {


        $device_id =  $request->input('device-id');
        AdminChat::where('device_id', $device_id)->update([
            'seen' => true,
        ]);


        $all = UToAdminMessage::where('device_id', $device_id)->orderBy('created_at', 'desc')
            ->where('is_remove', false)
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Thu hồi tin nhắn
     * @bodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * 
     * 
     */
    public function recallMessage(Request $request)
    {
        $mess_id = $request->route()->parameter('mess_id');
        $device_id = request()->header('device-id');

        $oMax = UToAdminMessage::where('device_id',   $device_id)->orderBy('id', 'desc')->first();

        $mess =    UToAdminMessage::where('device_id',  $device_id)
            ->where('id',  $mess_id)
            ->first();


        if ($mess  != null) {
            // event($e = new RedisChatEventUserToUser($mess, ChatUtils::TYPE_CHAT_RECALL));
            $mess->delete();
            if ($oMax  != null &&   $oMax->id == $mess_id) {
                $messLass = UToAdminMessage::where('device_id', $device_id)->orderBy('id', 'desc')->first();
                if ($messLass  !== null) {


                    $lass = AdminChat::where('device_id', $device_id)->first();

                    if ($lass  != null) {
                        $lass->update([
                            "last_mess" => $messLass->content,
                        ]);
                    }

                    //   ChatUtils::sendListSocket($request->user->id, ChatUtils::LIST_USER_CHAT);
                    //  ChatUtils::sendListSocket($vs_user_id, ChatUtils::LIST_USER_CHAT);
                }
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Gửi tin nhắn
     * 
     * @bodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * Khách nhận tin nhắn reatime khai báo io socket port 6441 nhận 
     * var socket = io("http://localhost:6441")
     * socket.on("chat:message2_from_admin_to_user:{device_id}", function(data) {   (1:2   1 là từ user nào gửi tới cusotmer nào nếu đang cần nhận thì 1 là người cần nhận 2 là id của bạn)
     *   console.log(data)
     *   })
     * chat:message:1   với 1 là user_id
     * 
     * 
     * 
     */
    public function sendMessage(Request $request)
    {
        $user_id = $request->user_id;
        $device_id = $request->device_id;

        if ($request->images == null && empty($request->content)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::CONTENT_IS_REQUIRED[0],
                'msg' => MsgCode::CONTENT_IS_REQUIRED[1],
            ], 400);
        }

        if (empty($request->id) || UToAdminMessage::where('id', $request->id)->first()  != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Id không hợp lệ",
            ], 400);
        }

        $ip_using =  IPUtils::getIP();

        //data nguoi gui
        $mess = new UToAdminMessage([
            "id" => ($request->id),
            "user_id" => $user_id,
            "content" => $request->content,
            'is_admin' => true,
            'images_json' => json_encode($request->images),
            "ip_using"    =>  $ip_using,
            // "platform"  =>  $platform,
            "device_id"   =>   $device_id,
            //"model"  =>  $model,
        ]);

        //socket này là của người nhận chứ người gửi ko cần nhận socket
        event($e = new RedisChatEventAdminToUser($mess->toArray(), ChatUtils::TYPE_CHAT_0));

        //thêm mess cho người gửi
        $mess->save();


        $user_by_device_id = User::where('device_id', $device_id)->first();

        if ($user_id != null) {
            PushNotificationUserJob::dispatch(
                $user_id,
                "Admin",
                substr($request->content, 0, 80),
            );
        } else
        if ($device_id != "device-id-default" && $user_by_device_id != null) {
            PushNotificationUserJob::dispatch(
                $user_by_device_id->id,
                "Admin",
                substr($request->content, 0, 80),
            );
        }




        $adminChat =  AdminChat::where('device_id',  $device_id)
            ->first();

        if (
            $adminChat != null
        ) {
            $adminChat->update([
                "user_id" =>  $user_id,
                "is_admin" => false,
                "last_mess" => $request->content,
                'seen' => true,
                "device_id"   =>   $device_id,
                "created_at" => Helper::getTimeNowString(),
                'updated_at' => Helper::getTimeNowString(),
            ]);
        } else {
            AdminChat::create([
                "user_id" =>   $user_id,
                "is_admin" => false,
                "last_mess" => $request->content,
                'seen' => true,
                "device_id"   =>   $device_id,
                "created_at" => Helper::getTimeNowString(),
                'updated_at' => Helper::getTimeNowString(),
            ]);
        }

        if ($user_id != null) {
            $userExists = User::where('id', $user_id)->first();

            $adminChat =  AdminChat::where('user_id',  $userExists->id)
                ->first();

            if ($userExists != null && $adminChat  != null) {
                $adminChat->update([
                    "user_id" =>  $user_id,
                    "is_admin" => false,
                    "last_mess" => $request->content,
                    'seen' => true,
                    "device_id"   =>   $device_id,
                    "created_at" => Helper::getTimeNowString(),
                    'updated_at' => Helper::getTimeNowString(),
                ]);
            }
        }

        ChatUtils::sendListSocket($user_id, ChatUtils::LIST_USER_CHAT);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>   $mess
        ], 200);
    }
}
