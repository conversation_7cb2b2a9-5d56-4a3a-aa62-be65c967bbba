<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\CookieRotate;
use Illuminate\Http\Request;

/**
 * @group  User/Cookie
 */
class AdminCookieRotateController extends Controller
{

    /**
     * Danh sách cookies
     */
    public function getAll(Request $request)
    {

        $cookies = CookieRotate::orderBy('position', 'ASC')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $cookies,
        ], 200);
    }

    /**
     * Thêm Cookie
     */
    public function create(Request $request)
    {


        if ($request->text_cookies  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "<PERSON><PERSON><PERSON> bổ sung cookies",
            ], 400);
        }

        if ($request->note  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Thêm note để ghi nhớ",
            ], 400);
        }

        $created = CookieRotate::create([
            'service' => $request->service,
            'run_times' => $request->run_times,
            'enable' => $request->enable,
            'proxy' => $request->proxy,
            'note' => $request->note,
            'position' => $request->position,
            'text_cookies' => $request->text_cookies,
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $created,
        ], 200);
    }

    /**
     * Cập nhật cookie
     */
    public function update(Request $request, $cookie_id)
    {

        $hasCookie = CookieRotate::where('id', $cookie_id)->first();

        if ($hasCookie  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không tồn tại",
            ], 400);
        }
        $hasCookie->update([
            'service' => $request->service,
            'run_times' => $request->run_times,
            'enable' => $request->enable,
            'proxy' => $request->proxy,
            'note' => $request->note,
            'position' => $request->position,
            'text_cookies' => $request->text_cookies,
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $hasCookie,
        ], 200);
    }

    /**
     * Cập nhật updateSortCookie
     */
    public function updateSortCookie(Request $request)
    {
        if ($request->list != null) {
            foreach ($request->list as $item) {
                $hasCookie = CookieRotate::where('id', $item['id'])->first();

                if ($hasCookie  != null) {
                    $hasCookie->update([
                        'position' =>  $item['position']
                    ]);
                }
            }
        }
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => CookieRotate::orderBy('position', 'ASC')->get()
        ], 200);
    }


    /**
     * Remove cookie
     */
    public function delete(Request $request, $cookie_id)
    {


        $hasCookie = CookieRotate::where('id', $cookie_id)->first();

        if ($hasCookie  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không tồn tại",
            ], 400);
        }
        $hasCookie->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
