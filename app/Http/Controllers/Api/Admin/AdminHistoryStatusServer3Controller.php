<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\Place;
use App\Helper\PlanUtils;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\HistoryStatusServer3;
use App\Models\MsgCode;
use Illuminate\Http\Request;

/**
 * @group  Admin/Lịch sử thay đổi trạng thái server 3
 */
class AdminHistoryStatusServer3Controller extends Controller
{

    /**
     * Lịch sử thay đổi trạng thái server 3
     */
    public function getAll(Request $request)
    {
        $search = StringUtils::convert_name_lowcase(request('search'));

        $custom = collect(
            [
                'last_status_alldata_eu' => HistoryStatusServer3::where('service', PlanUtils::ALLDATA)->orderBy('id', 'desc')->first(),
                'last_status_alldata_us' => HistoryStatusServer3::where('service', PlanUtils::ALLDATA_US)->orderBy('id', 'desc')->first(),
                'last_status_autodata_italy' => HistoryStatusServer3::where('service', PlanUtils::AUTODATA_ITALY)->orderBy('id', 'desc')->first(),
                'last_status_autodata' => HistoryStatusServer3::where('service', PlanUtils::AUTODATA)->orderBy('id', 'desc')->first(),
                'last_status_haynespro' => HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO)->orderBy('id', 'desc')->first(),
                'last_status_haynespro_truck' => HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO_TRUCK)->orderBy('id', 'desc')->first(),
                'last_status_mitchell_prodemand' => HistoryStatusServer3::where('service', PlanUtils::MITCHELL_PRODEMAND)->orderBy('id', 'desc')->first(),
                'last_status_tecdoc' => HistoryStatusServer3::where('service', PlanUtils::TECDOC)->orderBy('id', 'desc')->first(),
                'last_status_identifix' => HistoryStatusServer3::where('service', PlanUtils::IDENTIFIX)->orderBy('id', 'desc')->first(),
                'last_status_partslink24' => HistoryStatusServer3::where('service', PlanUtils::PARTSLINK24)->orderBy('id', 'desc')->first(),
                'last_status_ford_pts' => HistoryStatusServer3::where('service', PlanUtils::FORD_PTS)->orderBy('id', 'desc')->first(),
            ]
        );



        $all = HistoryStatusServer3::orderBy('id', 'desc')
            ->paginate(20);

        $all = $custom->merge($all);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }
}
