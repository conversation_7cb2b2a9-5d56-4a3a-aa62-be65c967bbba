<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\AgencyWebTheme;
use App\Models\Category;
use App\Models\MsgCode;
use App\Models\ProxyItem;
use Illuminate\Http\Request;

/**
 * @group  User/Thay đổi giao diện
 */
class AgencyWebThemeController extends Controller
{

    /**
     * Thông tin giao diện
     */
    public function getOne(Request $request)
    {

        $theme = AgencyWebTheme::where('agency_id', $request->agency->id)->first();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $theme,
        ], 200);
    }

    /**
     * Cập nhật giao diện
     */
    public function update(Request $request)
    {
        $theme = AgencyWebTheme::where('agency_id', $request->agency->id)->first();



        if ($request->sub_domain != null) {

            if (!preg_match('/^[a-zA-Z0-9-_]+$/',  $request->sub_domain)  || strlen($request->sub_domain) < 2) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => 'Sub domain invalid',
                ], 400);
            }


            $has = Agency::where('sub_domain', $request->sub_domain)->where('id', "!=", $request->agency->id)->first();

            if (
                $request->sub_domain == "admin" ||
                $request->sub_domain == "web" ||
                $request->sub_domain == "king" ||
                $request->sub_domain == "boss" ||
                $request->sub_domain == "top" ||
                $request->sub_domain == "car" ||
                $request->sub_domain == "drcar" ||
                $request->sub_domain == "dr" ||
                $request->sub_domain == "main" ||
                $request->sub_domain == "api" ||
                $request->sub_domain == "app" ||
                $request->sub_domain == "app-alldata" ||
                $request->sub_domain == "kdsgds" ||
                $request->sub_domain == "autodata-sv" ||
                $request->sub_domain == "workshop-autodata-group" ||
                $request->sub_domain == "autodata" ||
                $request->sub_domain == "my-alldata" ||
                $request->sub_domain == "atsg" ||
                $request->sub_domain == "haynespro" ||
                $request->sub_domain == "haynespro-truck" ||
                $request->sub_domain == "identifix" ||
                $request->sub_domain == "fordtis" ||
                $request->sub_domain == "ford-pts" ||
                $request->sub_domain == "partslink24" ||
                $request->sub_domain == "tecdoc" ||
                $request->sub_domain == "etka" ||
                $request->sub_domain == "toyota" ||
                $request->sub_domain == "main1" ||
                $request->sub_domain == "nas" ||
                $request->sub_domain == "my"
            )
                if ($has) {
                    return response()->json([
                        'code' => 400,
                        'success' => false,
                        'msg_code' => MsgCode::ERROR[0],
                        'msg' => ' Please use other domain',
                        'data' => $theme,
                    ], 400);
                }
        }
        $data = [
            'agency_id' =>  $request->agency->id,
            "title" =>  $request->title,
            "description" =>  $request->description,
            "color_main_1" =>  $request->color_main_1,
            'theme_style' => $request->theme_style,
            "background_color" =>  $request->background_color,
            "logo_url" =>  $request->logo_url,
            "background_url" =>  $request->background_url,
            "favicon_url" =>  $request->favicon_url,
            "contact_link" =>  $request->contact_link,

        ];
        if ($theme == null) {
            AgencyWebTheme::create($data);
        }
        if ($theme != null) {
            $theme->update($data);
        }

        if ($request->sub_domain != null) {
            $request->agency->update([
                'sub_domain' => $request->sub_domain
            ]);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $theme,
        ], 200);
    }
}
