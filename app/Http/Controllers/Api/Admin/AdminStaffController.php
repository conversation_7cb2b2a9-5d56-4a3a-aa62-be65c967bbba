<?php

namespace App\Http\Controllers\Api\Admin;

use App\Helper\MoneyBalanceUtils;
use App\Helper\Place;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\Staff;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Type\NullType;

/**
 * @group  Admin/Quản lý staff
 */

class AdminStaffController extends Controller
{
    /**
     * Danh sách staff
     * 
     * @bodyParam search required Tìm theo tên hoặc sdt
     * 
     */
    public function getAll(Request $request)
    {

        $search = StringUtils::convert_name_lowcase(request('search'));

        $all = Staff::when(request('status') !== null, function ($query) {
                $query->where('status', request('status'));
            })->orderBy('created_at', 'desc')
            ->search($search)
            ->paginate(20);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Xóa staff
     * 
     * @bodyParam search required Tìm theo tên hoặc sdt
     * 
     */
    public function delete(Request $request)
    {

        $staff_id = $request->route()->parameter('staff_id');
        $checkStaffExists = Staff::where(
            'id',
            $staff_id
        )->first();

        if ($checkStaffExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_STAFF_EXISTS[1],
                'msg' => MsgCode::NO_STAFF_EXISTS[1],
            ], 400);
        }

        $checkStaffExists->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Thay đổi trạng thái xác minh
     * @bodyParam status string required status  //2 đang kích hoạt,1 hủy kích hoạt
     */
    public function updateStatus(Request $request)
    {
        $staff_id = $request->route()->parameter('staff_id');
        $checkStaffExists = Staff::where(
            'id',
            $staff_id
        )->first();

        if ($checkStaffExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_STAFF_EXISTS[1],
                'msg' => MsgCode::NO_STAFF_EXISTS[1],
            ], 400);
        }



        $checkStaffExists->update([
            'status' =>  $request->status,
        ]);



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => Staff::where('id',  $checkStaffExists->id)->first(),
        ], 200);
    }

    /**
     * Thêm 1 nhân viên
     * 
     * @bodyParam name String   Họ và tên
     * @bodyParam phone_number string required Số điện thoại
     * @bodyParam facebook_id string required Facebook id

     */
    public function create(Request $request)
    {


        if (empty($request->name)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NAME_IS_REQUIRED[0],
                'msg' => MsgCode::NAME_IS_REQUIRED[1],
            ], 400);
        }

        if (!empty($request->facebook_id)) {
            $staffExists =   Staff::where('facebook_id', $request->facebook_id)->first();
            if ($staffExists  != null) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::FACEBOOK_ID_EXISTS[0],
                    'msg' => MsgCode::FACEBOOK_ID_EXISTS[1],
                ], 400);
            }
        }
        $staffCreated = Staff::create([
            'name' =>  $request->name,
            'phone_number' => $request->phone_number,
            'facebook_id' => $request->facebook_id,
            'max_in_day' => $request->max_in_day ?? 10000,
        ]);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => Staff::where('id', $staffCreated->id)->first(),
        ], 200);
    }



    /**
     * Cập nhật thông tin nhân viên
     * @bodyParam name String   Họ và tên
     * @bodyParam phone_number string required Số điện thoại
     * @bodyParam facebook_id string required Facebook id

     */
    public function update(Request $request)
    {
        $staff_id = $request->route()->parameter('staff_id');
        $checkStaffExists = Staff::where(
            'id',
            $staff_id
        )->first();

        if ($checkStaffExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_STAFF_EXISTS[1],
                'msg' => MsgCode::NO_STAFF_EXISTS[1],
            ], 400);
        }


        if (!empty($request->facebook_id)) {
            $staffExists =   Staff::where('facebook_id', $request->facebook_id)->where('id', '!=',  $staff_id)->first();
            if ($staffExists  != null) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::FACEBOOK_ID_EXISTS[0],
                    'msg' => MsgCode::FACEBOOK_ID_EXISTS[1],
                ], 400);
            }
        }

        $checkStaffExists->update([
            'name' =>  $request->name,
            'phone_number' => $request->phone_number,
            'facebook_id' => $request->facebook_id,
            'max_in_day' => $request->max_in_day,
        ]);



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => Staff::where('id',  $checkStaffExists->id)->first(),
        ], 200);
    }


    /**
     * Thông tin 1 staff
     * 
     */
    public function getOne(Request $request)
    {
        $staff_id = $request->route()->parameter('staff_id');
        $checkStaffExists = Staff::where(
            'id',
            $staff_id
        )->first();

        if ($checkStaffExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_STAFF_EXISTS[1],
                'msg' => MsgCode::NO_STAFF_EXISTS[1],
            ], 400);
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => Staff::where('id',  $checkStaffExists->id)->first(),
        ], 200);
    }

    /**
     * Thông tin 1 staff bằng email
     * 
     */
    public function getOneByEmail(Request $request)
    {
        $email = $request->route()->parameter('email');
        $checkStaffExists = Staff::where(
            'email',
            $email
        )->first();

        if ($checkStaffExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_STAFF_EXISTS[1],
                'msg' => MsgCode::NO_STAFF_EXISTS[1],
            ], 400);
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => Staff::where('id',  $checkStaffExists->id)->first(),
        ], 200);
    }

}
