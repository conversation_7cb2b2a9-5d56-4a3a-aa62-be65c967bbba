<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\ToyotaModel;
use Illuminate\Http\Request;

/**
 * @group  User/Model
 */
class AdminServiceModelSetController extends Controller
{

    /**
     * Cập nhật account
     */
    public function addOrUpdateToyotaModel(Request $request)
    {

        if ($request->year == null || $request->model_id == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Loss data",
            ], 400);
        }

        $modelHas = ToyotaModel::where('division', $request->division)->where('year', $request->year)->where('model_id', $request->model_id)->first();

        $data = [
            'division' => $request->division,
            'year' => $request->year,
            'model_id' => $request->model_id,
            'model_name' => $request->model_name,
            'rm_id' => $request->rm_id,
            'wd_id' => $request->wd_id,
        ];
        if ($modelHas  == null) {
            $modelHas =  ToyotaModel::create($data);
        } else {
            $modelHas->update($data);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $modelHas,
        ], 200);
    }
}
