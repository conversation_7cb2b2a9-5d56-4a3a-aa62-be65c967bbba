<?php

namespace App\Http\Controllers\Api\User;

use App\Classes\WebClass;
use App\Helper\AllDataUSv2UtilsWeb;
use App\Helper\AllDataUtils;
use App\Helper\AutoDataItalyUtilsWeb;
use App\Helper\AutoDataUtils;
use App\Helper\DomainConfigHelper;
use App\Helper\HaynesTruckUtilsWeb;
use App\Helper\HaynesUtils;
use App\Helper\Helper;
use App\Helper\IdentifixUtilsWeb;
use App\Helper\MitchellProdemandUtilsWeb;
use App\Helper\MitchellRepairCenterUtilsWeb;
use App\Helper\Partslink24UtilsMobileWeb;
use App\Helper\Partslink24UtilsWeb;
use App\Helper\PlanUtils;
use App\Helper\ProviderServer3Utils;
use App\Helper\SendEmailUtils;
use App\Helper\TecDocUtilsWeb;
use App\Helper\WebDataUtils;
use App\Helper\FordUtilsWeb;
use App\Http\Controllers\Api\Admin\AdminProxyServerController;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationAdminJob;
use App\Models\ConfigAdmin;
use App\Models\Group;
use App\Models\HistoryStatusServer3;
use App\Models\MsgCode;
use App\Models\ProxyItem;
use App\Models\ProxyServer;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use GuzzleHttp\Client;

/**
 * @group  User/ProviderServer3
 */

class CheckServerProviderServer3Controller extends Controller
{




    public function auto_check_preventive(Request $request)
    {
        $allServer = ProxyServer::where('is_ok', true)->get();

        foreach ($allServer as $proxyServer) {
            AdminProxyServerController::getDataServerProxy($proxyServer->id);

            $pro =  ProxyServer::where('id', $proxyServer->id)->first();


            if ($pro->is_ok == false) {
                $title =   $pro->service . " dự phòng đứt";
                PushNotificationAdminJob::dispatch(
                    $title,
                    $pro->name . " - " . $pro->error_mess,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    public function auto_fix_from_other_server_and_cookies()
    {

        $alldataEU  = AllDataUtils::checkServer()['status'];
        $statusAllDataEU =   $alldataEU  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $alldataUS  = AllDataUSv2UtilsWeb::checkServer()['status'];
        $statusAllDataUS =   $alldataUS  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $autodata_data_check = AutoDataUtils::checkServer();
        $autodata =  $autodata_data_check['status'];
        $statusAutodata =  $autodata  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $haynesPro  = HaynesUtils::checkServer()['status'];
        $statusHaynesPro =   $haynesPro  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        //Kiểm tra trạng thái hiện tại
        $lastAllDataEU =  HistoryStatusServer3::where('service', PlanUtils::ALLDATA)->orderBy('id', 'desc')->first();
        if ($lastAllDataEU  == null ||   $lastAllDataEU->status !=  $statusAllDataEU) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::ALLDATA,
                'status' => $statusAllDataEU,
            ]);

            //Tìm admin
            $title =  $alldataEU ? "Alldata EU  Available" : "Alldata EU Stopped";
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        $lastAllDataUS =  HistoryStatusServer3::where('service', PlanUtils::ALLDATA_US)->orderBy('id', 'desc')->first();
        if ($lastAllDataUS  == null ||   $lastAllDataUS->status !=  $statusAllDataUS) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::ALLDATA_US,
                'status' => $statusAllDataUS,
            ]);

            //Tìm admin
            $title =  $alldataUS ? "Alldata US  Available" : "Alldata US Stopped";
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        $lastAutoData =  HistoryStatusServer3::where('service', PlanUtils::AUTODATA)->orderBy('id', 'desc')->first();
        if ($lastAutoData  == null ||   $lastAutoData->status !=  $statusAutodata) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::AUTODATA,
                'status' => $statusAutodata,
                'note' =>  $autodata_data_check['mess'] ?? ""
            ]);

            //Tìm admin
            $title =  $autodata ? "Autodata Available" : "Autodata Stopped";
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO)->orderBy('id', 'desc')->first();
        if ($lastHaynesPro  == null ||   $lastHaynesPro->status !=  $statusHaynesPro) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::HAYNESPRO,
                'status' => $statusHaynesPro,
            ]);

            //Tìm admin
            $title =  $haynesPro  ? "HaynesPro  Available" : "HaynesPro Stopped";
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastAllDataEU =  HistoryStatusServer3::where('service', PlanUtils::ALLDATA)->orderBy('id', 'desc')->first();
        $lastAllDataUS =  HistoryStatusServer3::where('service', PlanUtils::ALLDATA_US)->orderBy('id', 'desc')->first();
        $lastAutoData =  HistoryStatusServer3::where('service', PlanUtils::AUTODATA)->orderBy('id', 'desc')->first();
        $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO)->orderBy('id', 'desc')->first();

        $now = Carbon::parse(Helper::getTimeNowStringVietNam());
        $minute = (int)($now->format("i"));
        $time = $now->format('d/n/Y @ H:i:s');


        if ($lastAllDataEU == null ||   $lastAllDataEU->status == 1) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesAlldataEUAutoCarSoftwate();
            $cookies = "";
            $dataCheck = null;

            $dataCheck = AllDataUtils::checkServer($dataSv1['cookies'] ?? "NON COOKIE");

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                $dataSv2 = ProviderServer3Utils::getCookiesAllDataSV1();
                $dataCheck = AllDataUtils::checkServer($dataSv2['cookies'] ?? "");

                if ($dataSv2['success'] == true && $dataCheck['status']) {
                    $dataOk =  $dataSv2;
                }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("ALLDATAEU");
            }

            if ($dataOk != null) {
                $c =      ConfigAdmin::first();
                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_alldata_str" => $dataOk['cookies'],
                        'cookies_alldata_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_alldata_str" => $dataOk['cookies'],
                        'cookies_alldata_json_info' => json_encode($dataCheck)
                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::ALLDATA,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title = "Alldata EU";
                $content =  "Alldata EU Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
            }
        }
        if ($lastAllDataUS == null ||   $lastAllDataUS->status == MsgCode::STATUS_ERR) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesAllDataUSDiagnostic();
            $cookies = "";
            $dataCheck = null;

            $dataCheck = AllDataUSv2UtilsWeb::checkServer($dataSv1['cookies'] ?? "NON COOKIE");

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                // $dataSv2 = ProviderServer3Utils::getCookiesAllDataSV1();

                // if ($dataSv2['success'] == true && AllDataUSv2UtilsWeb::checkServer($dataSv2['cookies'])) {
                //     $dataOk =  $dataSv2;
                // }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("ALLDATAUS");
            }

            if ($dataOk != null) {
                $c =      ConfigAdmin::first();
                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_alldata_us_v2_str" => $dataOk['cookies'],
                        'cookies_alldata_us_v2_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_alldata_us_v2_str" => $dataOk['cookies'],
                        'cookies_alldata_us_v2_json_info' => json_encode($dataCheck)

                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::ALLDATA_US,
                    'status' => 2,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title = "Alldata US";
                $content =  "Alldata US Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
            }
        }
        if ($lastAutoData == null ||   $lastAutoData->status == MsgCode::STATUS_ERR) {


            $dataCheck = null;
            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesAutoDataAutoCarSoftwate();


            if ($dataSv1['success'] == true && $dataSv1['status']) {
                $dataCheck = AutoDataUtils::checkServer($dataSv1['cookies'] ?? "NON COOKIE");
                $dataOk =  $dataSv1;
            } else {


                $dataSv2 = ProviderServer3Utils::getCookiesAutoDataSV1();

                if ($dataSv2['success'] == true && $dataSv2['status']) {
                    $dataCheck = AutoDataUtils::checkServer($dataSv2['cookies']);
                    $dataOk =  $dataSv2;
                }
            }

            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("AUTODATA");
            }

            if ($dataOk != null) {
                $c =      ConfigAdmin::first();
                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_autodata_str" => $dataOk['cookies'],
                        'cookies_autodata_json_info' => json_encode($dataCheck)

                    ]);
                } else {
                    $c->update([
                        "cookies_autodata_str" =>  $dataOk['cookies'],
                        'cookies_autodata_json_info' => json_encode($dataCheck)
                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::AUTODATA,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title = "Autodata";
                $content =  "Autodata Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
            }
        }
        if ($lastHaynesPro == null ||   $lastHaynesPro->status == MsgCode::STATUS_ERR) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesHaynesSV1();
            $cookies = "";
            $dataCheck = null;

            $dataCheck = HaynesUtils::checkServer($dataSv1['cookies'] ?? "NON COOKIE");

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                // $dataSv2 = ProviderServer3Utils::getCookiesAllDataSV1();

                // if ($dataSv2['success'] == true && AllDataUSv2UtilsWeb::checkServer($dataSv2['cookies'])) {
                //     $dataOk =  $dataSv2;
                // }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("HAYNESPRO");
            }

            if ($dataOk != null) {
                $c =      ConfigAdmin::first();
                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_haynes_str" => $dataOk['cookies'] ?? "",
                        'cookies_haynes_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_haynes_str" => $dataOk['cookies'] ?? "",
                        'cookies_haynes_json_info' => json_encode($dataCheck)
                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::HAYNESPRO,
                    'status' => 2,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title =  "Haynespro";
                $content =  "Haynespro Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
            }
        }
    }

    public function run_every_minute(Request $request)  //mỗi phút 1 lần
    {
        SendEmailUtils::sendEmailReaming3Day();
        SendEmailUtils::sendEmailAutoOneTime();
    }


    public function check_and_fix_server(Request $request)  //mỗi phút 2 lần
    {

        $this->auto_fix_from_other_server_and_cookies();
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    public function check_and_fix_server_alldata_eu(Request $request)  //mỗi phút 2 lần
    {

        $c =      ConfigAdmin::first();

        $alldataCheck  = AllDataUtils::checkServer();
        $alldataEU  = $alldataCheck['status'];
        $statusAllDataEU =   $alldataEU  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        PlanUtils::setCacheStatus(PlanUtils::ALLDATA, $alldataEU);
        $c->update([
            'cookies_alldata_json_info' => json_encode($alldataCheck)
        ]);

        //Kiểm tra trạng thái hiện tại
        $lastAllDataEU =  HistoryStatusServer3::where('service', PlanUtils::ALLDATA)->orderBy('id', 'desc')->first();
        if ($lastAllDataEU  == null ||   $lastAllDataEU->status !=  $statusAllDataEU) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::ALLDATA,
                'status' => $statusAllDataEU,
            ]);

            //Tìm admin
            $title =  $alldataEU ? "Alldata EU  Available" : "Alldata EU Stopped";
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastAllDataEU =  HistoryStatusServer3::where('service', PlanUtils::ALLDATA)->orderBy('id', 'desc')->first();

        if ($lastAllDataEU == null ||   $lastAllDataEU->status == MsgCode::STATUS_ERR) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesAlldataEUAutoCarSoftwate();
            $dataCheck = null;

            $dataCheck = AllDataUtils::checkServer($dataSv1['cookies'] ?? "NON COOKIE");

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                $dataSv2 = ProviderServer3Utils::getCookiesAllDataSV1();
                $dataCheck = AllDataUtils::checkServer($dataSv2['cookies'] ?? "");

                if ($dataSv2['success'] == true && $dataCheck['status']) {
                    $dataOk =  $dataSv2;
                }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("ALLDATAEU");
            }

            if ($dataOk != null) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_alldata_str" => $dataOk['cookies'],
                        'cookies_alldata_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_alldata_str" => $dataOk['cookies'],
                        'cookies_alldata_json_info' => json_encode($dataCheck)
                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::ALLDATA,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title =  "Alldata EU";
                $content =  "Alldata EU Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
                PlanUtils::setCacheStatus(PlanUtils::ALLDATA, true);
            }
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' =>  $alldataCheck,
        ], 200);
    }

    public function check_and_fix_server_alldata_us(Request $request)
    {

        $c =      ConfigAdmin::first();

        $proxys = ProxyItem::orderBy('position', 'ASC')->where('note', 'like', '%' . 'alldata_us' . '%')->get();
        $proxyList = [];
        foreach ($proxys as $itemProxy) {
            array_push($proxyList, $itemProxy->proxy);
        }

        $alldataCheck  = AllDataUSv2UtilsWeb::checkServer();
        $alldataUS  = $alldataCheck['status'];

        PlanUtils::setCacheStatus(PlanUtils::ALLDATA_US, $alldataUS);

        $alldataUSMess  = $alldataCheck['mess'] ?? "";
        $statusAllDataUS =   $alldataUS  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $lastProxy = $c->proxy_alldata_us_v2;
        if ($statusAllDataUS == MsgCode::STATUS_ERR) {
            $hasPro = false;
            foreach ($proxyList as $proxyItem) {
                if ($c->proxy_alldata_us_v2 != $proxyItem) {
                    $alldataCheck = AllDataUSv2UtilsWeb::checkServer(null, $proxyItem);
                    $alldataUS =  $alldataCheck['status'];
                    $statusAllDataUS =  $alldataUS  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

                    if ($statusAllDataUS ==  MsgCode::STATUS_OK) {
                        $title =  "Alldata US Available with new proxy " . $proxyItem;
                        PushNotificationAdminJob::dispatch(
                            $title,
                            $title,
                            PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                        );

                        $hasPro = true;
                        $c->update([
                            "proxy_alldata_us_v2" =>  $proxyItem,
                        ]);
                        break;
                    }
                }
            }
            if ($hasPro == false) {
                $c->update([
                    "proxy_alldata_us_v2" => $lastProxy,
                ]);
            }
        }

        $c->update([
            'cookies_alldata_us_v2_json_info' => json_encode($alldataCheck)
        ]);

        $lastAllDataUS =  HistoryStatusServer3::where('service', PlanUtils::ALLDATA_US)->orderBy('id', 'desc')->first();
        if ($lastAllDataUS  == null ||   $lastAllDataUS->status !=  $statusAllDataUS) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::ALLDATA_US,
                'status' => $statusAllDataUS,
            ]);

            //Tìm admin

            $title =  "Alldata US";
            $content =  $alldataUS ? "Alldata US  Available" : "Alldata US Stopped " . $alldataUSMess;
            PushNotificationAdminJob::dispatch(
                $title,
                $content,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastAllDataUS =  HistoryStatusServer3::where('service', PlanUtils::ALLDATA_US)->orderBy('id', 'desc')->first();

        if ($lastAllDataUS == null ||   $lastAllDataUS->status == MsgCode::STATUS_ERR) {

            $dataOk = null;

            $dataSv1 = ProviderServer3Utils::getCookiesAllDataUSAutocarsoftware();
            $dataCheck = AllDataUSv2UtilsWeb::checkServer($dataSv1['cookies'] ?? "NON COOKIE");

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                // $dataSv2 = ProviderServer3Utils::getCookiesAllDataUSAccountReyesVpsScaper();
                // $dataCheck = AllDataUSv2UtilsWeb::checkServer($dataSv2['cookies'] ?? "NON COOKIE");

                // if ($dataSv2['success'] == true && $dataCheck['status']) {
                //     $dataOk =  $dataSv2;
                // }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("ALLDATAUS");
            }

            if ($dataOk != null) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_alldata_us_v2_str" => $dataOk['cookies'],
                        'cookies_alldata_us_v2_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_alldata_us_v2_str" => $dataOk['cookies'],
                        'cookies_alldata_us_v2_json_info' => json_encode($dataCheck)

                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::ALLDATA_US,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title = "Alldata US";
                $content =  "Alldata US Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );

                PlanUtils::setCacheStatus(PlanUtils::ALLDATA_US, true);
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => $alldataCheck,
        ], 200);
    }

    public function check_and_fix_server_autodata(Request $request)
    {

        $proxys = ProxyItem::orderBy('position', 'ASC')->get();
        $proxyList = [""];
        foreach ($proxys as $itemProxy) {
            array_push($proxyList, $itemProxy->proxy);
        }

        $c =      ConfigAdmin::first();

        $autodata_data_check = AutoDataUtils::checkServer();
        $autodata =  $autodata_data_check['status'];
        $statusAutodata =  $autodata  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        if (str_contains($autodata_data_check['mess'] ?? "", 'cURL error')) {
            // $hasPro = false;
            // foreach ($proxyList as $proxyItem) {
            //     if ($c->proxy_autodata != $proxyItem) {
            //         $autodata_data_check = AutoDataUtils::checkServer(null, $proxyItem);
            //         $autodata =  $autodata_data_check['status'];
            //         $statusAutodata =  $autodata  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

            //         if ($statusAutodata ==  MsgCode::STATUS_OK) {
            //             $hasPro = true;
            //             $c->update([
            //                 "proxy_autodata" =>  $proxyItem,
            //                 "proxy_autodata_italy" =>  $proxyItem,
            //             ]);
            //             break;
            //         }
            //     }
            // }
            // if ($hasPro == false) {
            //     $c->update([
            //         "proxy_autodata" => "",
            //         "proxy_autodata_italy" => ""
            //     ]);
            // }
        } else {

            $c->update([
                'cookies_autodata_json_info' => json_encode($autodata_data_check)
            ]);

            $lastAutoData =  HistoryStatusServer3::where('service', PlanUtils::AUTODATA)->orderBy('id', 'desc')->first();
            if ($lastAutoData  == null ||   $lastAutoData->status !=  $statusAutodata) {
                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::AUTODATA,
                    'status' => $statusAutodata,
                    'note' =>  $autodata_data_check['mess'] ?? ""
                ]);

                //Tìm admin
                $title = "Autodata";
                $content =  $autodata ? "Autodata Available" : "Autodata Stopped" . ($autodata_data_check['mess'] ?? "");
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
            }



            //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
            $lastAutoData =  HistoryStatusServer3::where('service', PlanUtils::AUTODATA)->orderBy('id', 'desc')->first();
            if ($lastAutoData == null ||   $lastAutoData->status == MsgCode::STATUS_ERR) {


                $dataCheck = null;
                $dataOk = null;
                $dataSv1 = ProviderServer3Utils::getCookiesAutoDataAutoCarSoftwate();

                $dataCheck = AutoDataUtils::checkServer($dataSv1['cookies'] ?? "");
                if ($dataSv1['success'] == true && $dataCheck['status']) {
                    $dataOk =  $dataSv1;
                } else {


                    $dataSv2 = ProviderServer3Utils::getCookiesAutoDataSV1();
                    $dataCheck = AutoDataUtils::checkServer($dataSv2['cookies'] ?? "");
                    if ($dataSv2['success'] == true && $dataCheck['status']) {
                        $dataOk =  $dataSv2;
                    }
                }

                if ($dataOk == null) {
                    $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("AUTODATA");
                }

                if ($dataOk != null) {

                    if ($c == null) {
                        $c =  ConfigAdmin::create([
                            "cookies_autodata_str" => $dataOk['cookies'],
                            'cookies_autodata_json_info' => json_encode($dataCheck)

                        ]);
                    } else {
                        $c->update([
                            "cookies_autodata_str" =>  $dataOk['cookies'],
                            'cookies_autodata_json_info' => json_encode($dataCheck)
                        ]);
                    }

                    HistoryStatusServer3::create([
                        'service' =>  PlanUtils::AUTODATA,
                        'status' => MsgCode::STATUS_OK,
                        "fixer" =>  $dataOk['name'],
                        // "note" =>   $dataOk['link'],
                        'json_info' => json_encode($dataCheck)
                    ]);

                    $title =  "Autodata Available Fix By " . $dataOk['name'];
                    PushNotificationAdminJob::dispatch(
                        $title,
                        $title,
                        PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                    );
                }
            }
        }
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => $autodata_data_check,
        ], 200);
    }

    public function check_and_fix_server_autodata_italy(Request $request)
    {

        $c =      ConfigAdmin::first();

        $autodata_data_check = AutoDataItalyUtilsWeb::checkServer();
        $autodata =  $autodata_data_check['status'];
        $statusAutodata =  $autodata  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $c->update([
            'cookies_autodata_italy_json_info' => json_encode($autodata_data_check)
        ]);

        $lastAutoData =  HistoryStatusServer3::where('service', PlanUtils::AUTODATA_ITALY)->orderBy('id', 'desc')->first();
        if ($lastAutoData  == null ||   $lastAutoData->status !=  $statusAutodata) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::AUTODATA_ITALY,
                'status' => $statusAutodata,
                'note' =>  $autodata_data_check['mess'] ?? ""
            ]);

            //Tìm admin
            $title =  $autodata ? "Autodata Italy Available" : "Autodata Italy Stopped";
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => $autodata_data_check,
        ], 200);
    }

    public function check_and_fix_server_haynespro(Request $request)
    {

        $c =      ConfigAdmin::first();

        $proxys = ProxyItem::orderBy('id', 'ASC')->get();
        $proxyList = [""];
        foreach ($proxys as $itemProxy) {
            array_push($proxyList, $itemProxy->proxy);
        }


        $haynesProCheck = HaynesUtils::checkServer();
        $haynespro =  $haynesProCheck['status'];
        $_csrf  =  $haynesProCheck['_csrf'] ?? null;
        if (!empty($_csrf)) {
            Cache::put('HAYNESPRO_CAR_CSRF',  $_csrf);
        }

        PlanUtils::setCacheStatus(PlanUtils::HAYNESPRO, $haynespro);

        $statusHaynesPro =  $haynespro  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        if (str_contains($haynesProCheck['mess'] ?? "", 'cURL error')) {
            $hasPro = false;
            foreach ($proxyList as $proxyItem) {
                if ($c->proxy_haynespro != $proxyItem) {
                    $haynesProCheck = HaynesUtils::checkServer(null, $proxyItem);
                    $haynespro =  $haynesProCheck['status'];
                    $statusHaynespro =  $haynespro  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

                    if ($statusHaynespro ==  MsgCode::STATUS_OK) {
                        $hasPro = true;
                        $c->update([
                            "proxy_haynespro" =>  $proxyItem,
                        ]);
                        break;
                    }
                }
            }
            if ($hasPro == false) {
                $c->update([
                    "proxy_haynespro" => "",
                ]);
            }
        }


        $c->update([
            'cookies_haynes_json_info' => json_encode($haynesProCheck)
        ]);

        //Bỏ qua lỗi
        $mess  = $haynesProCheck['mess'] ?? "";
        if (!str_contains($mess, 'ERROR COOKIES')) {

            $_csrfNew = $haynesProCheck['_csrf'] ?? "";
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'Not error cookie haynes, _csrf new:' . $_csrfNew,
            ], 200);
        }


        $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO)->orderBy('id', 'desc')->first();
        if ($lastHaynesPro  == null ||   $lastHaynesPro->status !=  $statusHaynesPro) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::HAYNESPRO,
                'status' => $statusHaynesPro,
            ]);

            //Tìm admin
            $title =  $haynespro  ? "HaynesPro  Available" : "HaynesPro Stopped" . ($haynesProCheck['mess'] ?? "");
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO)->orderBy('id', 'desc')->first();

        if ($lastHaynesPro == null ||   $lastHaynesPro->status == MsgCode::STATUS_ERR) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesHaynesproDr();
            $dataCheck = HaynesUtils::checkServer($dataSv1['cookies'] ?? "NON COOKIE");

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                $dataSv0 = ProviderServer3Utils::getCookiesHaynesproAutocarsoftware();
                $dataCheck = HaynesUtils::checkServer($dataSv0['cookies'] ?? "");
                if ($dataSv0['success'] == true && $dataCheck['status']) {
                    $dataOk =  $dataSv0;
                }
                // else {
                //     $dataSv11 = ProviderServer3Utils::getCookiesHaynesDiagzone();
                //     $dataCheck = HaynesUtils::checkServer($dataSv11['cookies'] ?? "");

                //     if ($dataSv11['success'] == true && $dataCheck['status']) {
                //         $dataOk =  $dataSv11;
                //     } else {
                //         $dataSv2 = ProviderServer3Utils::getCookiesHaynesproCartuning();
                //         $dataCheck = HaynesUtils::checkServer($dataSv2['cookies'] ?? "");

                //         if ($dataSv2['success'] == true && $dataCheck['status']) {

                //             $dataOk =  $dataSv2;
                //         } else {

                //             $dataSv33 = ProviderServer3Utils::getCookiesHaynesproEngin073_MHH_AUTO();
                //             $dataCheck = HaynesUtils::checkServer($dataSv33['cookies'] ?? "");

                //             if ($dataSv33['success'] == true && $dataCheck['status']) {

                //                 $dataOk =  $dataSv33;
                //             } else {
                //                 $dataSv3 = ProviderServer3Utils::getCookiesHaynesproDxdata();
                //                 $dataCheck = HaynesUtils::checkServer($dataSv3['cookies'] ?? "");

                //                 if ($dataSv3['success'] == true && $dataCheck['status']) {
                //                     $dataOk =  $dataSv3;
                //                 } else {
                //                     $dataSv4 = ProviderServer3Utils::getCookiesHaynesXdiag3();
                //                     $dataCheck = HaynesUtils::checkServer($dataSv4['cookies']  ?? "");

                //                     if ($dataSv4['success'] == true && $dataCheck['status']) {
                //                         $dataOk =  $dataSv4;
                //                     } else {
                //                         $dataSv5 = ProviderServer3Utils::getCookiesHaynesProDiagnostic();
                //                         $dataCheck = HaynesUtils::checkServer($dataSv5['cookies']  ?? "");

                //                         if ($dataSv5['success'] == true && $dataCheck['status']) {
                //                             $dataOk =  $dataSv4;
                //                         }
                //                     }
                //                 }
                //             }
                //         }
                //     }
                // }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("HAYNESPRO");
            }

            if ($dataOk != null) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_haynes_str" => $dataOk['cookies'] ?? "",
                        'cookies_haynes_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_haynes_str" => $dataOk['cookies'] ?? "",
                        'cookies_haynes_json_info' => json_encode($dataCheck)
                    ]);

                    // if (str_contains($dataOk['name'] ?? "", "Making.Work.Easier") || str_contains($dataOk['name'] ?? "", "xDia3") || str_contains($dataOk['name'] ?? "", "Diagzone")) {

                    //     $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO_TRUCK)->orderBy('id', 'desc')->first();
                    //     if ($lastHaynesPro == null ||   $lastHaynesPro->status == MsgCode::STATUS_ERR) {
                    //         $c->update([
                    //             "cookies_haynes_truck_str" => $dataOk['cookies'] ?? "",
                    //             'cookies_haynes_truck_json_info' => json_encode($dataCheck)
                    //         ]);
                    //     }
                    // }

                    $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO_TRUCK)->orderBy('id', 'desc')->first();
                    if (str_contains($dataOk['name'] ?? "", "CartuningSetup") && str_contains($lastHaynesPro->fixer  ?? "", "CartuningSetup")) {
                        $c->update([
                            "cookies_haynes_str" => $dataOk['cookies'] ?? "",
                            'cookies_haynes_json_info' => json_encode($dataCheck)
                        ]);
                    }

                    // if (str_contains($dataOk['name'] ?? "", "Dr")) {

                    //     $appName = DomainConfigHelper::getConfig('appName');
                    //     $allow_send_haynes_truck_to_fhost = DomainConfigHelper::getConfig('allow_send_haynes_truck_to_fhost');
                    //     if ($appName  == "DRCAR" && $allow_send_haynes_truck_to_fhost) {
                    //         $c->update([
                    //             "cookies_haynes_truck_str" => $dataOk['cookies'] ?? "",
                    //             'cookies_haynes_truck_json_info' => json_encode($dataCheck)
                    //         ]);

                    //         $client = new Client();
                    //         $response = $client->post('https://main.fhost.ca/api/admin/update_one_field_config_remote', [
                    //             'json' => [
                    //                 'field' => 'cookies_haynes_truck_str',
                    //                 'value' => $dataOk['cookies'] ?? ""
                    //             ]
                    //         ]);
                    //     }
                    // }
                }

                $_csrfNew = $dataCheck['_csrf'] ?? "";

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::HAYNESPRO,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title =  "Haynespro";
                $content =  "Haynespro Available Fix By " . $dataOk['name'] . "  _csrf new " . $_csrfNew;
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
                PlanUtils::setCacheStatus(PlanUtils::HAYNESPRO, true);
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' =>  $haynesProCheck,
        ], 200);
    }

    public function check_and_fix_server_haynespro_truck(Request $request)
    {

        $c =      ConfigAdmin::first();
        $haynesProCheck  = HaynesTruckUtilsWeb::checkServer();
        $haynesPro  = $haynesProCheck['status'];
        $_csrf  =  $haynesProCheck['_csrf'] ?? null;
        if (!empty($_csrf)) {
            Cache::put('HAYNESPRO_TRUCK_CSRF',  $_csrf);
        }

        PlanUtils::setCacheStatus(PlanUtils::HAYNESPRO_TRUCK, $haynesPro);

        $statusHaynesPro =   $haynesPro  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $c->update([
            'cookies_haynes_truck_json_info' => json_encode($haynesProCheck)
        ]);

        //Bỏ qua lỗi
        //Bỏ qua lỗi
        $mess  = $haynesProCheck['mess'] ?? "";
        if (str_contains($mess, 'cURL error')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'cURL error',
            ], 200);
        }
        if (str_contains($mess, 'Request ERRROR')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'Request ERRROR',
            ], 200);
        }


        $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO_TRUCK)->orderBy('id', 'desc')->first();
        if ($lastHaynesPro  == null ||   $lastHaynesPro->status !=  $statusHaynesPro) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::HAYNESPRO_TRUCK,
                'status' => $statusHaynesPro,
            ]);

            //Tìm admin
            $title =  $haynesPro  ? "HaynesPro Truck Available" : "HaynesPro Truck Stopped" . ($haynesProCheck['mess'] ?? "");
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO_TRUCK)->orderBy('id', 'desc')->first();

        if ($lastHaynesPro == null ||   $lastHaynesPro->status == MsgCode::STATUS_ERR) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesHaynesproTruckDr();
            $dataCheck = null;

            $dataCheck = HaynesTruckUtilsWeb::checkServer($dataSv1['cookies'] ?? "NON COOKIE");
            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                $dataSv11 = ProviderServer3Utils::getCookiesHaynesDiagzone();
                $dataCheck = HaynesTruckUtilsWeb::checkServer($dataSv11['cookies'] ?? "NON COOKIE");

                if ($dataSv11['success'] == true && $dataCheck['status']) {
                    $dataOk =  $dataSv11;
                } else {
                    $dataSv2 = ProviderServer3Utils::getCookiesHaynesproCartuning();
                    $dataCheck = HaynesTruckUtilsWeb::checkServer($dataSv2['cookies'] ?? "NON COOKIE");

                    if ($dataSv2['success'] == true && $dataCheck['status']) {
                        $dataOk =  $dataSv2;
                    } else {
                        $dataSv3 = ProviderServer3Utils::getCookiesHaynesproTruckDxdata();
                        $dataCheck = HaynesTruckUtilsWeb::checkServer($dataSv3['cookies'] ?? "NON COOKIE");

                        if ($dataSv3['success'] == true && $dataCheck['status']) {
                            $dataOk =  $dataSv3;
                        }
                    }
                }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("HAYNESPROTRUCK");
            }

            if ($dataOk != null) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_haynes_truck_str" => $dataOk['cookies'] ?? "",
                        'cookies_haynes_truck_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_haynes_truck_str" => $dataOk['cookies'] ?? "",
                        'cookies_haynes_truck_json_info' => json_encode($dataCheck)
                    ]);

                    // $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO)->orderBy('id', 'desc')->first();
                    // if ($lastHaynesPro == null ||   $lastHaynesPro->status == MsgCode::STATUS_ERR) {
                    //     if (str_contains($dataOk['name'] ?? "", "Making.Work.Easier") || str_contains($dataOk['name'] ?? "", "xDia3")) {
                    //         $c->update([
                    //             "cookies_haynes_str" => $dataOk['cookies'] ?? "",
                    //             'cookies_haynes_json_info' => json_encode($dataCheck)
                    //         ]);
                    //     }
                    // }

                    // $lastHaynesPro =  HistoryStatusServer3::where('service', PlanUtils::HAYNESPRO)->orderBy('id', 'desc')->first();
                    // if (str_contains($dataOk['name'] ?? "", "CartuningSetup") && str_contains($lastHaynesPro->fixer  ?? "", "CartuningSetup")) {
                    //     $c->update([
                    //         "cookies_haynes_str" => $dataOk['cookies'] ?? "",
                    //         'cookies_haynes_json_info' => json_encode($dataCheck)
                    //     ]);
                    // }
                }

                // if (str_contains($dataOk['name'] ?? "", "Diagzone")) {

                //     $appName = DomainConfigHelper::getConfig('appName');
                //     $allow_send_haynes_truck_to_fhost = DomainConfigHelper::getConfig('allow_send_haynes_truck_to_fhost');
                //     if ($appName  == "DRCAR") {
                //         $c->update([
                //             "cookies_haynes_truck_str" => $dataOk['cookies'] ?? "",
                //             'cookies_haynes_truck_json_info' => json_encode($dataCheck)
                //         ]);

                //         $client = new Client();
                //         $response = $client->post('https://main.fhost.ca/api/admin/update_one_field_config_remote', [
                //             'json' => [
                //                 'field' => 'cookies_haynes_truck_str',
                //                 'value' => $dataOk['cookies'] ?? ""
                //             ]
                //         ]);
                //     }
                // }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::HAYNESPRO_TRUCK,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title =  "Haynespro Truck";
                $content =  "Haynespro Truck Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
                PlanUtils::setCacheStatus(PlanUtils::HAYNESPRO_TRUCK, true);
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => $haynesProCheck,
        ], 200);
    }

    public function check_and_fix_server_identifix(Request $request)
    {

        $c =      ConfigAdmin::first();

        $identifixCheck  = IdentifixUtilsWeb::checkServer();
        $identifix  = $identifixCheck['status'];
        PlanUtils::setCacheStatus(PlanUtils::IDENTIFIX, $identifix);

        $statusIdentifix =   $identifix  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $c->update([
            'cookies_identifix_json_info' => json_encode($identifixCheck)
        ]);


        $mess  = $identifixCheck['mess'] ?? "";
        if (str_contains($mess, 'cURL error')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'cURL error',
                // 'msg' => $mitchellCheck,
            ], 200);
        }


        if (!str_contains($mess, 'Your Session has been Terminated')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'Not cookies error',
                // 'msg' => $mitchellCheck,
            ], 200);
        }


        $lastIdentifix =  HistoryStatusServer3::where('service', PlanUtils::IDENTIFIX)->orderBy('id', 'desc')->first();
        if ($lastIdentifix  == null ||   $lastIdentifix->status !=  $statusIdentifix) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::IDENTIFIX,
                'status' => $statusIdentifix,
                'json_info' => json_encode([
                    "mess" => $identifixCheck['mess'] ?? "",
                    "cookies_identifix_str" => $c->cookies_identifix_str,
                ])
            ]);

            //Tìm admin
            $title =  $identifix  ? "Identifix Available" : "Identifix Stopped" . ($identifixCheck['mess'] ?? "");;
            PushNotificationAdminJob::dispatch(
                $title,
                $title . ($identifixCheck['content'] ?? ""),
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }
        if ($statusIdentifix == MsgCode::STATUS_ERR &&  $lastIdentifix->status == MsgCode::STATUS_OK) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'first time',
            ], 200);
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastIdentifix =  HistoryStatusServer3::where('service', PlanUtils::IDENTIFIX)->orderBy('id', 'desc')->first();

        if ($lastIdentifix == null ||   $lastIdentifix->status == MsgCode::STATUS_ERR) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesIdentifixAutocarsoftware();
            $dataCheck = null;

            $dataCheck = IdentifixUtilsWeb::checkServer($dataSv1['cookies'] ?? null);

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                $dataSv2 = ProviderServer3Utils::getCookiesIdentifixReyesAcountScaperVPS();
                $dataCheck = IdentifixUtilsWeb::checkServer($dataSv2['cookies'] ?? "x");

                if ($dataSv2['success'] == true && $dataCheck['status']) {
                    $dataOk =  $dataSv2;
                }
                // else {
                //     $dataSv3 = ProviderServer3Utils::getCookiesIdentifixFromAccountFB1Chia();
                //     $dataCheck = IdentifixUtilsWeb::checkServer($dataSv3['cookies'] ?? "x");

                //     if ($dataSv3['success'] == true && $dataCheck['status']) {
                //         $dataOk =  $dataSv3;
                //     }
                // }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("IDENTIFIX");
            }

            if ($dataOk != null && ($dataOk['cookies'] ?? "" != "")) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_identifix_str" => $dataOk['cookies'] ?? "",
                        'cookies_identifix_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_identifix_str" => $dataOk['cookies'] ?? "",
                        'cookies_identifix_json_info' => json_encode($dataCheck)
                    ]);
                }

                $appName = DomainConfigHelper::getConfig('appName');
                $allow_send_identifix_to_drcar = DomainConfigHelper::getConfig('allow_send_identifix_to_drcar');
                if ($appName  == "FHOST" && $allow_send_identifix_to_drcar) {
                    $c->update([
                        "cookies_identifix_str" => $dataOk['cookies'] ?? "",
                        'cookies_identifix_json_info' => json_encode($dataCheck)
                    ]);

                    $client = new Client();
                    $response = $client->post('https://main.docfix.ca/api/admin/update_one_field_config_remote', [
                        'json' => [
                            'field' => 'cookies_identifix_str',
                            'value' => $dataOk['cookies'] ?? ""
                        ]
                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::IDENTIFIX,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title =  "Identifix";
                $content =  "Identifix Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
                PlanUtils::setCacheStatus(PlanUtils::IDENTIFIX, true);
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => $identifixCheck,
        ], 200);
    }


    public function check_and_fix_server_mitchell_repair_center(Request $request)
    {

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => "",
        ], 200);

        $c =      ConfigAdmin::first();
        $mitchellCheck  = MitchellRepairCenterUtilsWeb::checkServer();
        $mitchell  = $mitchellCheck['status'];
        $statusMitchell =   $mitchell  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $c->update([
            'cookies_mitchell_repair_center_json_info' => json_encode($mitchellCheck)
        ]);

        $lastMitchell =  HistoryStatusServer3::where('service', PlanUtils::MITCHELL_REPAIR_CENTER)->orderBy('id', 'desc')->first();
        if ($lastMitchell  == null ||   $lastMitchell->status !=  $statusMitchell) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::MITCHELL_REPAIR_CENTER,
                'status' => $statusMitchell,
            ]);

            // //Tìm admin
            // $title =  $mitchell  ? "Mitchell repair center Available" : "Mitchell repair center Stopped";
            // PushNotificationAdminJob::dispatch(
            //     $title,
            //     $title,
            // );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastMitchell =  HistoryStatusServer3::where('service', PlanUtils::MITCHELL_REPAIR_CENTER)->orderBy('id', 'desc')->first();

        if ($lastMitchell == null ||   $lastMitchell->status == MsgCode::STATUS_ERR) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesMitchellRepairCenterSV1();
            $dataCheck = null;

            $dataCheck = MitchellRepairCenterUtilsWeb::checkServer($dataSv1['cookies'] ?? "NON COOKIE");

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                // $dataSv2 = ProviderServer3Utils::getCookiesAllDataSV1();

                // if ($dataSv2['success'] == true && AllDataUSv2UtilsWeb::checkServer($dataSv2['cookies'])) {
                //     $dataOk =  $dataSv2;
                // }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("MITCHELL_REPAIR_CENTER");
            }

            if ($dataOk != null) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_mitchell_repair_center_str" => $dataOk['cookies'] ?? "",
                        'cookies_mitchell_repair_center_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_mitchell_repair_center_str" => $dataOk['cookies'] ?? "",
                        'cookies_mitchell_repair_center_json_info' => json_encode($dataCheck)
                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::MITCHELL_REPAIR_CENTER,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                //     $title =  "Mitchell repair center Available Fix By " . $dataOk['name'];
                //     PushNotificationAdminJob::dispatch(
                //         $title,
                //         $title,
                //     );
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => $mitchellCheck,
        ], 200);
    }

    public function check_and_fix_server_mitchell_prodemand(Request $request)
    {

        $c =      ConfigAdmin::first();
        // $dataSv1 = ProviderServer3Utils::getCookiesMitchellProdemandDiagnostic();
        // $dataCheck = null;
        // $dataCheck = MitchellProdemandUtilsWeb::checkServer($dataSv1['cookies'] ?? "NON COOKIE");
        // $dataOk =  $dataSv1;
        // if ($dataSv1['success'] == true && $dataCheck['status']) {
        //     $c->update([
        //         "cookies_mitchell_prodemand_str" => $dataOk['cookies'] ?? "",
        //         'cookies_mitchell_prodemand_json_info' => json_encode($dataCheck)
        //     ]);
        // } else {

        //     $dataSv1 = ProviderServer3Utils::getCookiesMitchellProdemandSV1();
        //     $dataCheck = null;
        //     $dataCheck = MitchellProdemandUtilsWeb::checkServer($dataSv1['cookies'] ?? "NON COOKIE");
        //     $dataOk =  $dataSv1;
        //     if ($dataSv1['success'] == true && $dataCheck['status']) {
        //         $c->update([
        //             "cookies_mitchell_prodemand_str" => $dataOk['cookies'] ?? "",
        //             'cookies_mitchell_prodemand_json_info' => json_encode($dataCheck)
        //         ]);
        //     }

        // }

        // return response()->json([
        //     'code' => 200,
        //     'success' => true,
        //     'msg_code' => MsgCode::SUCCESS[0],
        // ], 200);
        // return;



        $mitchellCheck  = MitchellProdemandUtilsWeb::checkServer();
        $mess  = $mitchellCheck['mess'] ?? "";


        $mitchell  = $mitchellCheck['status'];
        PlanUtils::setCacheStatus(PlanUtils::MITCHELL_PRODEMAND, $mitchell);

        if (str_contains($mess, 'cURL error')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'cURL error',
                // 'msg' => $mitchellCheck,
            ], 200);
        }
        if (str_contains($mess, 'from proxy')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'Proxy error',
                // 'msg' => $mitchellCheck,
            ], 200);
        }


        $statusMitchell =   $mitchell  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $c->update([
            'cookies_mitchell_prodemand_json_info' => json_encode($mitchellCheck)
        ]);

        $lastMitchell =  HistoryStatusServer3::where('service', PlanUtils::MITCHELL_PRODEMAND)->orderBy('id', 'desc')->first();
        if ($lastMitchell  == null ||   $lastMitchell->status !=  $statusMitchell) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::MITCHELL_PRODEMAND,
                'status' => $statusMitchell,
            ]);

            //Tìm admin
            $title = "Mitchell prodemand";
            $content =  $mitchell  ? "Mitchell prodemand Available" : "Mitchell prodemand Stopped" . ($mitchellCheck['mess'] ?? "");
            PushNotificationAdminJob::dispatch(
                $title,
                $content,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastMitchell =  HistoryStatusServer3::where('service', PlanUtils::MITCHELL_PRODEMAND)->orderBy('id', 'desc')->first();

        if (($lastMitchell == null ||   $lastMitchell->status == MsgCode::STATUS_ERR) && $mess == 'ERROR COOKIES') {
            Cache::put('PRODEMAND_FIXING', 'value', now()->addMinutes(5));

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiessMitchellProdemandReyesAcountScaperVPS();
            $dataCheck = null;

            if (!empty($dataSv1['cookies'])) {
                $dataCheck = MitchellProdemandUtilsWeb::checkServer($dataSv1['cookies'] ?? "NON COOKIE");
            }

            if ($dataCheck != null && $dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;

                //Update lại html Index Main
                if (isset($dataSv1['base64Html']) && strlen($dataSv1['base64Html']) > 100) {

                    $webClass = new WebClass();
                    $webClass->tail_link = '/Main/Index';
                    $webClass->service =  PlanUtils::MITCHELL_PRODEMAND;
                    $webClass->method = 'GET';
                    $webClass->content_type =   'text/html; charset=utf-8';
                    $webClass->body =  base64_decode($dataSv1['base64Html']);

                    WebDataUtils::saveWebData($webClass);
                }
            } else {

                $dataSv2 = ProviderServer3Utils::getCookiesMitchellProdemandAutocarsoftware();
                $dataCheck = MitchellProdemandUtilsWeb::checkServer($dataSv2['cookies'] ?? "");

                if ($dataSv2['success'] == true && $dataCheck['status']) {
                    $dataOk =  $dataSv2;
                } else {
                    $dataSv3 = ProviderServer3Utils::getCookiesMitchellProdemandDiagnostic();
                    $dataCheck = MitchellProdemandUtilsWeb::checkServer($dataSv3['cookies'] ?? "");

                    if ($dataSv3['success'] == true && $dataCheck['status']) {
                        $dataOk =  $dataSv3;
                    }
                }
            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("MITCHELL_PRODEMAND");
            }

            if ($dataOk != null) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_mitchell_prodemand_str" => $dataOk['cookies'] ?? "",
                        'cookies_mitchell_prodemand_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_mitchell_prodemand_str" => $dataOk['cookies'] ?? "",
                        'cookies_mitchell_prodemand_json_info' => json_encode($dataCheck)
                    ]);
                }

                $expiresAt = Carbon::now()->addSeconds(6);
                Cache::put(json_encode(['cookies_mitchell_prodemand_str']), $dataOk['cookies'] ?? "", $expiresAt);

                // $appName = DomainConfigHelper::getConfig('appName');
                // $allow_send_mitchell_prodemand_to_drcar = DomainConfigHelper::getConfig('allow_send_mitchell_prodemand_to_drcar');
                // if ($appName  == "FHOST" && $allow_send_mitchell_prodemand_to_drcar) {
                //     $c->update([
                //         "cookies_mitchell_prodemand_str" => $dataOk['cookies'] ?? "",
                //         'cookies_mitchell_prodemand_json_info' => json_encode($dataCheck)
                //     ]);

                //     $client = new Client();
                //     $response = $client->post('https://main.docfix.ca/api/admin/update_one_field_config_remote', [
                //         'json' => [
                //             'field' => 'cookies_mitchell_prodemand_str',
                //             'value' => $dataOk['cookies'] ?? ""
                //         ]
                //     ]);
                // }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::MITCHELL_PRODEMAND,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title =  "Mitchell prodemand";
                $content =  "Mitchell prodemand Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
                PlanUtils::setCacheStatus(PlanUtils::MITCHELL_PRODEMAND, true);
            }
        }
        Cache::forget('PRODEMAND_FIXING');

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            // 'msg' => $mitchellCheck,
        ], 200);
    }

    public function check_and_fix_server_ford_pts(Request $request)
    {

        $c =      ConfigAdmin::first();


        $fordPtsCheck  = FordUtilsWeb::checkServer();
        $mess  = $fordPtsCheck['mess'] ?? "";


        $fordPts  = $fordPtsCheck['status'];
        PlanUtils::setCacheStatus(PlanUtils::FORD_PTS, $fordPts);

        if (str_contains($mess, 'cURL error')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'cURL error',
                // 'msg' => $fordPtsCheck,
            ], 200);
        }
        if (str_contains($mess, 'from proxy')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'Proxy error',
                // 'msg' => $fordPtsCheck,
            ], 200);
        }

        if (str_contains($mess, 'ERROR FROM FORD SERVER')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'ERROR FROM FORD SERVER',
                // 'msg' => $fordPtsCheck,
            ], 200);
        }


        $statusFordPTS =   $fordPts  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $c->update([
            'cookies_ford_pts_json_info' => json_encode($fordPtsCheck)
        ]);

        $lastFordPTS =  HistoryStatusServer3::where('service', PlanUtils::FORD_PTS)->orderBy('id', 'desc')->first();
        if ($lastFordPTS  == null ||   $lastFordPTS->status !=  $statusFordPTS) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::FORD_PTS,
                'status' => $statusFordPTS,
            ]);

            //Tìm admin
            $title =  $fordPts  ? "Ford PTS Available" : "Ford PTS Stopped" . ($fordPtsCheck['mess'] ?? "");
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastFordPTS =  HistoryStatusServer3::where('service', PlanUtils::FORD_PTS)->orderBy('id', 'desc')->first();

        if (($lastFordPTS == null ||   $lastFordPTS->status == MsgCode::STATUS_ERR) && $mess == 'ERROR COOKIES') {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesFordPTSScaperVPS();
            $dataCheck = null;

            if (!empty($dataSv1['cookies'])) {
                $dataCheck = FordUtilsWeb::checkServer($dataSv1['cookies'] ?? "NON COOKIE");
            }

            if ($dataOk != null) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_ford_pts_str" => $dataOk['cookies'] ?? "",
                        'cookies_ford_pts_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_ford_pts_str" => $dataOk['cookies'] ?? "",
                        'cookies_ford_pts_json_info' => json_encode($dataCheck)
                    ]);
                }


                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::FORD_PTS,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);


                $title =  "Ford PTS";
                $content =  "Ford PTS Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
                PlanUtils::setCacheStatus(PlanUtils::FORD_PTS, true);
            }
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            // 'msg' => $fordPtsCheck,
        ], 200);
    }


    public function check_and_fix_server_tecdoc(Request $request)
    {

        $c =      ConfigAdmin::first();
        $tecdocCheck  = TecDocUtilsWeb::checkServer();
        $tecdoc  = $tecdocCheck['status'];
        $statusTecdoc =   $tecdoc  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $c->update([
            'cookies_tecdoc_json_info' => json_encode($tecdocCheck)
        ]);


        $lastTecdoc =  HistoryStatusServer3::where('service', PlanUtils::TECDOC)->orderBy('id', 'desc')->first();
        if ($lastTecdoc  == null ||   $lastTecdoc->status !=  $statusTecdoc) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::TECDOC,
                'status' => $statusTecdoc,
                'json_info' => json_encode($tecdocCheck)
            ]);

            //Tìm admin
            // $title =  $tecdoc  ? "Tecdoc Available" : "Tecdoc Stopped";
            // PushNotificationAdminJob::dispatch(
            //     $title,
            //     $title,
            // );
        }

        $lastTecdoc =  HistoryStatusServer3::where('service', PlanUtils::TECDOC)->orderBy('id', 'desc')->first();
        if ($lastTecdoc == null ||   $lastTecdoc->status == MsgCode::STATUS_ERR) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getSessionsTecdocSV1();
            $dataCheck = null;

            $dataCheck = TecDocUtilsWeb::checkServer($dataSv1['cookies'] ?? "NON COOKIE");

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                $dataSv2 = ProviderServer3Utils::getCookiesTecDocFromAccountEyes();
                $dataCheck = TecDocUtilsWeb::checkServer($dataSv2['cookies']);

                if ($dataSv2['success'] == true && $dataCheck['status']) {
                    $dataOk =  $dataSv2;
                }
            }

            if ($dataOk != null) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_tecdoc_str" => $dataOk['cookies'] ?? "",
                        'cookies_tecdoc_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_tecdoc_str" => $dataOk['cookies'] ?? "",
                        'cookies_tecdoc_json_info' => json_encode($dataCheck)
                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::TECDOC,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                // $title =  "TecDoc Available Fix By " . $dataOk['name'];
                // PushNotificationAdminJob::dispatch(
                //     $title,
                //     $title,
                // );
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    public function check_and_fix_server_partslink24(Request $request)
    {

        $c =      ConfigAdmin::first();

        $partslink24Check  = Partslink24UtilsWeb::checkServer();
        $partslink24  = $partslink24Check['status'];
        PlanUtils::setCacheStatus(PlanUtils::PARTSLINK24, $partslink24);
        $statusPartslink24 =   $partslink24  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $c->update([
            'cookies_partslink24_json_info' => json_encode($partslink24Check)
        ]);

        $title = "";

        $mess  = $partslink24Check['mess'] ?? "";
        if (str_contains($mess, 'cURL error')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'cURL error',

            ], 200);
        }


        $lastPartslink24 =  HistoryStatusServer3::where('service', PlanUtils::PARTSLINK24)->orderBy('id', 'desc')->first();
        if ($lastPartslink24  == null ||   $lastPartslink24->status !=  $statusPartslink24) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::PARTSLINK24,
                'status' => $statusPartslink24,
            ]);

            //Tìm admin
            $title =  $partslink24  ? "Partslink24 Available" : "Partslink24 Stopped" . ($partslink24Check['mess'] ?? "");;
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastPartslink24 =  HistoryStatusServer3::where('service', PlanUtils::PARTSLINK24)->orderBy('id', 'desc')->first();

        if (($lastPartslink24 == null ||   $lastPartslink24->status == MsgCode::STATUS_ERR) && (str_contains($mess, "400 Bad Request") || str_contains($mess, "maybe cookie expired"))) {

            $dataOk = null;
            $dataSv1 = ProviderServer3Utils::getCookiesPartslink24DrAccount();
            $dataCheck = null;

            $dataCheck = Partslink24UtilsWeb::checkServer($dataSv1['cookies'] ?? null);

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                // $dataSv2 = ProviderServer3Utils::getCookiesPartslink24ReyesAcountScaperVPS();
                // $dataCheck = Partslink24UtilsWeb::checkServer($dataSv2['cookies'] ?? "x");

                // if ($dataSv2['success'] == true && $dataCheck['status']) {
                //     $dataOk =  $dataSv2;
                // }

            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("PARTSLINK24");
            }


            if ($dataOk != null) {

                if ($c == null) {

                    $c =  ConfigAdmin::create([
                        "cookies_partslink24_str" => $dataOk['cookies'] ?? "",
                        'cookies_partslink24_json_info' => json_encode($dataCheck)
                    ]);
                } else {
                    $objectNew =  json_decode($dataOk['cookies'] ?? "", true);
                    $object =  json_decode($c->cookies_partslink24_str, true);

                    if (isset($object['cookie'])) {
                        $object["cookie"] = $objectNew['cookie'] ?? "";
                    }
                    $expiresAt = Carbon::now()->addSeconds(6);
                    Cache::put(json_encode(['access_token_partslink24']), json_encode($object), $expiresAt);

                    $c->update([
                        "cookies_partslink24_str" => $dataOk['cookies'] ?? "",
                        'cookies_partslink24_json_info' => json_encode($dataCheck)
                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::PARTSLINK24,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);


                //check lại lần nữa

                Partslink24UtilsWeb::checkServer();
                PlanUtils::setCacheStatus(PlanUtils::PARTSLINK24, true);

                $title =  "Partslink24";
                $content =  "Partslink24 Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => $partslink24Check,
        ], 200);
    }

    public function check_and_fix_server_partslink24_mobile(Request $request)
    {

        $c =      ConfigAdmin::first();

        $partslink24Check  = Partslink24UtilsMobileWeb::checkServer();

        $partslink24  = $partslink24Check['status'];
        PlanUtils::setCacheStatus(PlanUtils::PARTSLINK24, $partslink24);
        $statusPartslink24 =   $partslink24  == true ? MsgCode::STATUS_OK : MsgCode::STATUS_ERR;

        $c->update([
            'cookies_partslink24_json_info' => $partslink24Check
        ]);

        $title = "";

        $mess  = $partslink24Check['mess'] ?? "";
        if (str_contains($mess, 'cURL error')) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => 'cURL error',

            ], 200);
        }


        $lastPartslink24 =  HistoryStatusServer3::where('service', PlanUtils::PARTSLINK24)->orderBy('id', 'desc')->first();
        if ($lastPartslink24  == null ||   $lastPartslink24->status !=  $statusPartslink24) {
            HistoryStatusServer3::create([
                'service' =>  PlanUtils::PARTSLINK24,
                'status' => $statusPartslink24,
            ]);

            //Tìm admin
            $title =  $partslink24  ? "Partslink24 Available" : "Partslink24 Stopped" . ($partslink24Check['mess'] ?? "");;
            PushNotificationAdminJob::dispatch(
                $title,
                $title,
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }

        //Kiem tra tiep trang thai cu, và xử lý nếu lỗi
        $lastPartslink24 =  HistoryStatusServer3::where('service', PlanUtils::PARTSLINK24)->orderBy('id', 'desc')->first();

        if (($lastPartslink24 == null ||   $lastPartslink24->status == MsgCode::STATUS_ERR) &&  str_contains($mess, "Undefined array key") || (str_contains($mess, "Unauthorized") || str_contains($mess, "unauthorized"))) {

            $dataOk = null;

            $dataSv1 = ProviderServer3Utils::getCookiesPartslink24MobileDrAccount();
            $dataCheck = null;

            $dataCheck = Partslink24UtilsMobileWeb::checkServer($dataSv1['cookies'] ?? null);

            if ($dataSv1['success'] == true && $dataCheck['status']) {
                $dataOk =  $dataSv1;
            } else {

                // $dataSv2 = ProviderServer3Utils::getCookiesPartslink24ReyesAcountScaperVPS();
                // $dataCheck = Partslink24UtilsWeb::checkServer($dataSv2['cookies'] ?? "x");

                // if ($dataSv2['success'] == true && $dataCheck['status']) {
                //     $dataOk =  $dataSv2;
                // }

            }


            if ($dataOk == null) {
                $dataOk =  ProviderServer3Utils::getCookiesFromPreventive("PARTSLINK24");
            }

            if ($dataOk != null) {

                if ($c == null) {
                    $c =  ConfigAdmin::create([
                        "cookies_partslink24_str" => $dataOk['cookies'] ?? "",
                        'cookies_partslink24_json_info' => json_encode($dataCheck)
                    ]);
                } else {

                    $c->update([
                        "cookies_partslink24_str" => $dataOk['cookies'] ?? "",
                        'cookies_partslink24_json_info' => json_encode($dataCheck)
                    ]);
                }

                HistoryStatusServer3::create([
                    'service' =>  PlanUtils::PARTSLINK24,
                    'status' => MsgCode::STATUS_OK,
                    "fixer" =>  $dataOk['name'],
                    // "note" =>   $dataOk['link'],
                    'json_info' => json_encode($dataCheck)
                ]);

                $title =  "Partslink24";
                $content =  "Partslink24 Available Fix By " . $dataOk['name'];
                PushNotificationAdminJob::dispatch(
                    $title,
                    $content,
                    PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
                );


                PlanUtils::setCacheStatus(PlanUtils::PARTSLINK24, true);
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => $partslink24Check,
        ], 200);
    }

    function getAll(Request $request)
    {

        dd(
            ProviderServer3Utils::getCookiesAllDataSV1(),
            ProviderServer3Utils::getCookiesAlldataEUAutoCarSoftwate(),

            ProviderServer3Utils::getCookiesAllDataUSAutocarsoftware(),

            ProviderServer3Utils::getCookiesAutoDataSV1(),
            ProviderServer3Utils::getCookiesAutoDataAutoCarSoftwate(),

            ProviderServer3Utils::getCookiesHaynesSV1(),

        );
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => [
                ProviderServer3Utils::getCookiesAllDataSV1(),
                ProviderServer3Utils::getCookiesAutoDataSV1(),
                ProviderServer3Utils::getCookiesAutoDataAutoCarSoftwate(),
            ]
        ], 200);
    }
}
