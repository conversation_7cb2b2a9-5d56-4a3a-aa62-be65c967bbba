<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\Helper;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationAdminJob;
use App\Models\DeviceLogin;
use App\Models\HistorySentEmail;
use App\Models\MailReset;
use App\Models\MsgCode;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Mail;

/**
 * @group  User/Send Email
 */
class SendEmailController extends Controller
{


    /**
     * Gửi mail reset device
     * 
     * @bodyParam email
     * @bodyParam type (DEVICE, PASSWORD)
     */
    public function sendEmailReset(Request $request)
    {

        $user = User::where('email', $request->email)->first();
        if ($user == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        if ($request->type != "DEVICE" && $request->type != "PASSWORD" && $request->type != "VERIFY_EMAIL") {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_TYPE[0],
                'msg' => MsgCode::INVALID_TYPE[1],
            ], 400);
        }

        $now = Carbon::parse(Helper::getTimeNowString());
        $now->subMinutes(1);

        $hasOneMinute =  MailReset::where('user_id',   $user->id)
            ->where('created_at', ">",  $now)
            ->first();

        if ($hasOneMinute != null) {
            $now = Carbon::parse(Helper::getTimeNowString());
            $last = Carbon::parse($hasOneMinute->created_at);

            $totalDuration = 60 - $now->diffInSeconds($last);

            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Try again in " . $totalDuration . "s"
            ], 400);
        }

        $mailReset =  MailReset::create([
            'user_id' =>  $user->id,
            'code' => Helper::generateRandomNum() . Helper::generateRandomString(10),
            'status' => 0,
            'type' => $request->type
        ]);

        try {

            $actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
            $link = $actual_link . "/api/user/email_reset?code=" . $mailReset->code . "&type=" . $request->type;

            if ($request->type == "DEVICE") {
                Mail::to([$user->email])
                    ->send(new \App\Mail\SendMailResetDevice($link));
            } else if ($request->type == "PASSWORD") {
                Mail::to([$user->email])
                    ->send(new \App\Mail\SendMailResetPassword($link));
            } else {
                Mail::to([$user->email])
                    ->send(new \App\Mail\SendMailVerifyEmail($link));
            }
        } catch (Exception $e) {

            PushNotificationAdminJob::dispatch(
                "Error email server " . $user->email . " $user->username",
                $e->getMessage(),
                PushNotificationAdminJob::TYPE_ERROR_COOKIE_SERVER
            );
        }




        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[1],
            'msg' => MsgCode::SUCCESS[1],
            // 'data' =>  $total
        ], 200);
    }

    /**
     * Nhấn link mail
     * 
     * 
     */
    public function clickEmailLink(Request $request)
    {

        $code = request('code');
        $type = request('type');

        if ($type  != "DEVICE" &&  $type != "PASSWORD" &&  $type != "VERIFY_EMAIL") {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_TYPE[0],
                'msg' => MsgCode::INVALID_TYPE[1],
            ], 400);
        }

        $mail =  MailReset::where('code', $code)->first();

        if ($mail  == null) {
            return response()->view(
                'success_reset_error',
                [
                    'mess' => "AN ERROR OCCURRED"
                ]
            );
        }


        $now = Carbon::parse(Helper::getTimeNowString());
        $last = Carbon::parse($mail->created_at);

        $totalDuration =  $now->diffInMinutes($last);

        if ($totalDuration  > 5) {
            return response()->view(
                'success_reset_error',
                [
                    'mess' => "The request has expired"
                ]
            );
        }

        if ($mail->type == "VERIFY_EMAIL") {
            $user = User::where('id', $mail->user_id)->first();
            $user->update([
                'password' => bcrypt($request->password),
            ]);

            $user->update([
                'email_verified_at' => Helper::getTimeNowString()
            ]);

            return response()->view('success_verify_email');
        }

        if ($mail->type == "PASSWORD") {

            $data =  [
                'is_error' => false,
                'is_success' => false,
                'mess' =>  ''
            ];

            if ($request->isMethod('post')) {

                if (empty($request->password) || strlen($request->password) < 6) {
                    $data['is_error'] = true;
                    $data['mess'] =  MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[1];
                    return response()->view('reset_password_form', $data);
                }

                if ($request->password != $request->password_confirm) {
                    $data['is_error'] = true;
                    $data['mess'] =  MsgCode::TWO_PASSWORDS_ARE_NOT_THE_SAME[1];
                    return response()->view('reset_password_form', $data);
                }

                $mail->update([
                    'status' => 2
                ]);
                $user = User::where('id', $mail->user_id)->first();
                $user->update([
                    'password' => bcrypt($request->password),
                ]);

                $data['is_error'] = false;
                $data['is_success'] = true;
                $data['mess'] =  'Password has been updated, please login again';

                return response()->view('reset_password_form', $data);
            }



            return response()->view('reset_password_form', $data);
        }


        if ($mail->status != 0) {
            return response()->view(
                'success_reset_error',
                [
                    'mess' => "The request has been processed before"
                ]
            );
        }

        $mail->update([
            'status' => 2
        ]);

        $user = User::where('id', $mail->user_id)->first();
        $user->update([
            'platform' => null,
            'device_id' => null,
            'model' => null,
        ]);


        DeviceLogin::where('user_id', $mail->user_id)->update([
            "ip_using" => null,
            "address" => null,
            "device_id" => null,
            "model_name" => null,
            "platform" => null,
            "last_visit_time" => null,
            "app_version" => null,
            "login_time" => null,
        ]);

        return response()->view('success_reset_device');
    }
}
