<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\Helper;
use App\Helper\PlanUtils;
use App\Helper\SecurityUtils;
use App\Http\Controllers\Controller;
use App\Models\AdminSetting;
use App\Models\Agency;
use App\Models\ConfigAdmin;
use App\Models\MsgCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * @group  Usser/badges
 */
class BadgesController extends Controller
{

    /**
     * Tạo Lấy thông tin bagdes
     */
    public function getBadges(Request $request)
    {


        $setting =      AdminSetting::first();
        $allow_renewal_extension =  $setting  == null ? true :  $setting->allow_renewal_extension;

        $agency_sub = null;
        $enable_customer_add_device = true;
        $user = $request->user;
        if ($user != null && $user->of_agency_id != null) {
            $agency = Cache::remember(json_encode(["agencyuser", $user->of_agency_id]), 30, function ()  use ($user) {
                return DB::table('agencies')->where('id', $user->of_agency_id)->first();
            });
            if ($agency != null &&  $agency->not_back_home_web_agency == false) {
                $domainWithoutSub = Helper::getDomainCurrentWithoutSubAndNoHttp();
                $agency_sub = $agency->agency_code;
                $enable_customer_add_device = filter_var($agency->enable_customer_add_device, FILTER_VALIDATE_BOOLEAN);
            }
            if ($agency != null) {
                $enable_customer_add_device = filter_var($agency->enable_customer_add_device, FILTER_VALIDATE_BOOLEAN);
            }
        }

        $status_server = null;
        if ($user != null) {
            SecurityUtils::useDevice($user);
        }

        $status_server = [
            'status_alldata_eu' => PlanUtils::getCacheStatus(PlanUtils::ALLDATA),
            'status_alldata_us' => PlanUtils::getCacheStatus(PlanUtils::ALLDATA_US),
            'status_kds_gds' => PlanUtils::getCacheStatus(PlanUtils::KDSGDS),
            'status_mitchell_prodemand' => PlanUtils::getCacheStatus(PlanUtils::MITCHELL_PRODEMAND),
            'status_atsg' => PlanUtils::getCacheStatus(PlanUtils::ATSG),
            'status_haynespro' => PlanUtils::getCacheStatus(PlanUtils::HAYNESPRO),
            'status_haynespro_truck' => PlanUtils::getCacheStatus(PlanUtils::HAYNESPRO_TRUCK),
            'status_identifix' => PlanUtils::getCacheStatus(PlanUtils::IDENTIFIX),
            'status_partslink24' => PlanUtils::getCacheStatus(PlanUtils::PARTSLINK24),
            'status_ford_pts' => PlanUtils::getCacheStatus(PlanUtils::FORD_PTS),
            'status_toyota_tis' => PlanUtils::getCacheStatus(PlanUtils::TOYOTA_TIS),
        ];



        return response()->json([
            'code' => 200,
            'success' => true,
            'data' => [
                'version_ios' =>  $setting  == null ? "" :  $setting->version_ios,
                'version_android' =>  $setting  == null ? "" :  $setting->version_android,
                'version_window' =>  $setting  == null ? "" :  $setting->version_window,
                "noti_new" => $setting->noti_new,
                'allow_renewal_extension' => $allow_renewal_extension,
                'agency_sub' =>  $agency_sub,
                'enable_customer_add_device' =>  $enable_customer_add_device,
                "user" => $request->user ?? null,
                "status_server" => $status_server ?? null,
            ],
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
