<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Helper\RenewUtils;
use App\Models\OrderPlan;
use Illuminate\Http\Request;
use Srmklive\PayPal\Services\PayPal as PayPalClient;
use App\Http\Controllers\Controller;


//http://localhost:8001/payment_drcar/paypal-manual-create?plan_id=30&email=<EMAIL>
class PayPalManualController extends Controller
{
    /**
     * create transaction.
     *
     * process transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function paypalCreate(Request $request)
    {
       
          
        return response()->view('payment/paypal_manual', [
            'email' => "<EMAIL> ",
            'money' => $request->price,
        ]);

    }

}
