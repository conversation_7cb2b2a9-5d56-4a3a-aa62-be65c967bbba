<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Helper\Helper;
use App\Helper\RenewUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\OrderPlan;
use Exception;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Payon\PaymentGateway\PayonHelper;

//http://localhost:8001/payment_drcar/payon-create?plan_id=30&email=<EMAIL>


class PayOnController extends Controller
{

    public function payonCreate(Request $request)
    {$mc_id = 
        $payon = new PayonHelper($mc_id, $app_id, $secret_key, $url, $http_auth, $http_auth_pass);
        $data = [
            "merchant_request_id" => $merchant_request_id,  //Type String: Mã đơn hàng Merchant được tạo từ yêu cầu thanh toán
            "amount" => 10000, //Type Int: <PERSON>i<PERSON> trị đơn hàng. Đơn vị: VNĐ
            "description" => 'Thanh toán đơn hàng KH Tran Van A', //Type String: Mô tả thông tin đơn hàng
            "url_redirect" => 'https://payon.vn/', //Type String: Đường link chuyển tiếp sau khi thực hiện thanh toán thành công
            "url_notify" => 'https://payon.vn/notify', //Type String: Đường link thông báo kết quả đơn hàng
            "url_cancel" => 'https://payon.vn/cancel', //Type String: Đường link chuyển tiếp khi khách hàng hủy thanh toán
            "customer_fullname" => 'Tran Van A', //Type String: Họ và tên khách hàng
            "customer_email" => '<EMAIL>', //Type String: Địa chỉ email khách hàng
            "customer_mobile" => '0123456789', //Type String: Số điện thoại khách hàng
        ];
        $response = $payon->CreateOrderPaynow($data);
        if($response['error_code'] = "00"){
            // Call API thành công, tiếp tục xử lý
        } else {
            //Có lỗi xảy ra check lỗi trả về
        }


        $client = new \GuzzleHttp\Client();

        try {
            $response1 = $client->request(
                'POST',
                'https://api.payon.io/v1/invoice',
                [
                    'headers' =>   [
                        'x-api-key' => 'JWK04TX-W11M2XJ-NKXCYAC-9J1XAFN'
                    ],
                    'timeout' => 10000, // Response timeout
                    'connect_timeout' => 10000, // Connection timeout
                    'form_params' => [

                        "price_amount" => $request->plan->price,
                        "price_currency" => "usd",
                        "order_id" => Helper::getRandomOrderString(),
                        "order_description" => $request->plan->service . " " . $request->plan->month . " month",
                        "ipn_callback_url" => "https://main.$domain/payment_drcar/payon-success",
                        "success_url" => "https://main.$domain/payment_drcar/payon-success",
                        "cancel_url" => "https://main.$domain/payment_drcar/payon-cancel"

                    ]
                ],

            );
            $res = json_decode($response1->getBody()->getContents());

            OrderPlan::create([
                'order_code' =>   $res->order_id,
                'user_id' => $request->user->id,
                'month' => $request->plan->month,
                'product_id' => $request->plan->product_id,
                'plan_id' => $request->plan->id,
                'service' => $request->plan->service,
                'price' => $request->plan->price,
                'status' => RenewUtils::PAYMENT_STATUS_WAIT,
                'payment_partner' => 'payon'
            ]);

            return redirect($res->invoice_url);
        } catch (Exception $e) {

            return response()->view('payment/error_payment', [
                'msg_code' => 'CANCEL ORDER',
                'msg' => $e->getMessage(),
            ]);
        }



        //  return redirect("https://payon.com/merchant/?m_shop=$m_shop&m_orderid=$m_orderid&m_amount=$m_amount&m_curr=$m_curr&m_desc=$m_desc&m_sign=$sign");
    }

    public function payonSuccess(Request $request)
    {

        $request_json = file_get_contents('php://input');
        $res = json_decode($request_json);

        $orderExists = OrderPlan::where('order_code',$res==null? "@3%" : $res->order_id)->first();
        if ($orderExists  != null &&   $orderExists->status != RenewUtils::PAYMENT_STATUS_SUCCESS && $request->payment_status ==  'finished') {

            RenewUtils::renew_service_for_user(
                $orderExists->user_id,
                $orderExists->product_id,
                RenewUtils::PAY_FROM_NOWPAYMENT,
                $orderExists->id,
                $orderExists->order_code,
                $request_json,
                $orderExists->plan_id
            );

            $orderExists->update([
                'status' => RenewUtils::PAYMENT_STATUS_SUCCESS
            ]);
        }
        return response()->view('success_paid');
    }

    public function payonCancel()
    {
        return response()->view('payment/error_payment', [
            'msg_code' => 'CANCEL ORDER',
            'msg' => 'CANCEL ORDER',
        ]);
        exit;
    }
}
