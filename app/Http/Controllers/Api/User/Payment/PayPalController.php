<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Helper\PayPalDataUtils;
use App\Helper\RenewUtils;
use App\Models\OrderPlan;
use Illuminate\Http\Request;
use Srmklive\PayPal\Services\PayPal as PayPalClient;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationAdminJob;
use App\Models\RenewalHistory;
use Exception;




class PayPalController extends Controller
{




    public function inputPayPalCreate()
    {
        return response()->view('payment/input_payment_paypal');
        return response()->view('payment/error_payment', []);
    }

    public function inputPayPalCreateLink(Request $request)
    {

        $appCredentials = PayPalDataUtils::getCredentials();
        $provider = new PayPalClient;
        $provider->setApiCredentials($appCredentials);
        $paypalToken = $provider->getAccessToken();
        $response = $provider->createOrder([
            "intent" => "CAPTURE",
            "application_context" => [
                "return_url" => PayPalDataUtils::getSuccessPath($appCredentials),
                "cancel_url" => PayPalDataUtils::getCancelPath($appCredentials),
            ],
            "purchase_units" => [
                0 => [
                    "amount" => [
                        "currency_code" => "USD",
                        "value" =>  $request->price
                    ]
                ]
            ]
        ]);


        if (isset($response['id']) && $response['id'] != null) {


            // redirect to approve href
            foreach ($response['links'] as $links) {
                if ($links['rel'] == 'approve') {
                    return response()->view('payment/create_link_payment_paypal', [
                        "link" => $links['href'],
                        "price" => $request->price
                    ]);
                }
            }




            return redirect()
                ->to(PayPalDataUtils::getCreatePath($appCredentials))
                ->with('error', 'Something went wrong.');
        } else {

            PushNotificationAdminJob::dispatch(
                "Paypal ERROR create link " . $response['error']['name'],
                $response['error']['message']
            );

            return response()->view('payment/error_payment', [
                'msg_code' => $response['error']['name'],
                'msg' => $response['error']['message'],
            ]);


            return redirect()
                ->to(PayPalDataUtils::getCreatePath($appCredentials))
                ->with('error', $response['message'] ?? 'Something went wrong.');
        }
    }

    /**
     * create transaction.
     *
     * process transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function paypalCreate(Request $request)
    {

        try {


            $appCredentials = PayPalDataUtils::getCredentials();
            $provider = new PayPalClient;
            $provider->setApiCredentials($appCredentials);
            $paypalToken = $provider->getAccessToken();
            $response = $provider->createOrder([
                "intent" => "CAPTURE",
                "application_context" => [
                    "return_url" => PayPalDataUtils::getSuccessPath($appCredentials),
                    "cancel_url" => PayPalDataUtils::getCancelPath($appCredentials),
                ],
                "purchase_units" => [
                    0 => [
                        "amount" => [
                            "currency_code" => "USD",
                            "value" =>  number_format((float)($request->price), 2, '.', '')
                        ]
                    ]
                ]
            ]);


            if (isset($response['id']) && $response['id'] != null) {


                $orderPlanCreate =  OrderPlan::create([
                    'order_code' => $response['id'],
                    'user_id' => $request->user->id,
                    'product_id' => $request->product_id,
                    'plans_json' => json_encode($request->plans),
                    'plan_ids_json' => json_encode($request->plan_ids_input),
                    'price' => $request->price,
                    'before_price' => $request->before_price,
                    'code_voucher' => $request->code_voucher,
                    'status' => RenewUtils::PAYMENT_STATUS_WAIT,
                    'payment_partner' => 'paypal'
                ]);



                // redirect to approve href
                foreach ($response['links'] as $links) {
                    if ($links['rel'] == 'approve') {
                        return redirect()->away($links['href']);
                    }
                }

                return redirect()
                    ->to(PayPalDataUtils::getCreatePath($appCredentials))
                    ->with('error', 'Something went wrong.');
            } else {

                PushNotificationAdminJob::dispatch(
                    "paypalCreate ERROR " . $response['error']['name'],
                    $response['error']['message']
                );

                return response()->view('payment/error_payment', [
                    'msg_code' => $response['error']['name'],
                    'msg' => $response['error']['message'],
                ]);

                return redirect()
                    ->to(PayPalDataUtils::getCreatePath($appCredentials))
                    ->with('error', $response['message'] ?? 'Something went wrong.');
            }
        } catch (Exception $e) {
            PushNotificationAdminJob::dispatch(
                "Error payment paypalCreate",
                $e->getMessage(),
            );
        }
    }
    /**
     * success transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function paypalSuccess(Request $request)
    {

        try {
            $appCredentials = PayPalDataUtils::getCredentials();
            $provider = new PayPalClient;
            $provider->setApiCredentials($appCredentials);
            $provider->getAccessToken();
            $response = $provider->capturePaymentOrder($request['token']);
            if (isset($response['status']) && $response['status'] == 'COMPLETED') {


                $orderExists = OrderPlan::where('order_code', $response['id'])->first();
                if ($orderExists  != null && $orderExists->status != RenewUtils::PAYMENT_STATUS_SUCCESS) {
                    $net_price = 0.0;
                    $transactionId = "";
                    try {
                        $net_price = floatval($response['purchase_units'][0]['payments']['captures'][0]['seller_receivable_breakdown']['net_amount']['value']);
                    } catch (Exception $e) {
                        PushNotificationAdminJob::dispatch(
                            "Error payment paypal 1",
                            $e->getMessage(),
                        );
                    }
                    try {
                        $transactionId = $response['purchase_units'][0]['payments']['captures'][0]['id'];
                    } catch (Exception $e) {
                        PushNotificationAdminJob::dispatch(
                            "Error payment paypal 2",
                            $e->getMessage(),
                        );
                    }



                    foreach ($orderExists->plans  as $plan) {
                        RenewUtils::renew_service_for_user(
                            $orderExists->user_id,
                            $orderExists->product_id,
                            RenewUtils::PAY_FROM_PAYPAL,
                            $orderExists->id,
                            $transactionId,
                            json_encode($response) ?? "",
                            $plan->id,
                            $net_price
                        );
                    }

                    $orderExists->update([
                        'status' => RenewUtils::PAYMENT_STATUS_SUCCESS,
                        'net_price' => $net_price
                    ]);
                    //trường hợp có response thành công từ tạo thành tạo

                } else {
                    $net_price = 0.0;
                    try {
                        $net_price = floatval($response['purchase_units'][0]['payments']['captures'][0]['seller_receivable_breakdown']['net_amount']['value']);
                    } catch (Exception $e) {
                        PushNotificationAdminJob::dispatch(
                            "Error payment paypal 3",
                            $e->getMessage(),
                        );
                    }

                    if (isset($response['id']) && $response['id'] != null) {
                        $his = OrderPlan::where('order_code', $response['id'])->first();

                        if ($his  == null) {

                            $orderPlanCreate =  OrderPlan::create([
                                'order_code' => $response['id'],
                                'user_id' => 1,
                                'product_id' => null,
                                'plans_json' => null,
                                'plan_ids_json' => null,
                                'price' => null,
                                'service' => 'PAYMENT_LINK',
                                'before_price' => null,
                                'code_voucher' => null,
                                'payment_partner' => 'paypal',
                                'status' => RenewUtils::PAYMENT_STATUS_SUCCESS,
                                'net_price' => $net_price
                            ]);

                            PushNotificationAdminJob::dispatch(
                                "Renewed from link",
                                $response['id'] . " - " . $net_price . " - " . $_SERVER['HTTP_HOST'],
                            );

                            RenewalHistory::create([
                                "user_id" => 1,
                                "month" => 0,
                                "base_service" => "",
                                "service" => "",
                                "price" => 0,
                                "paid" => 0,
                                "before_expiration_date" =>  null,
                                "after_expiration_date" =>  null,
                                "extender_user_id" =>  null,
                                "references_id" =>   $response['id'],
                                "references_value" =>   $response['id'],
                                'product_id' =>  null,
                                'pay_from' =>  RenewUtils::PAY_FROM_PAYPAL,
                                'json_data' =>  null,
                                'net_price' =>   $net_price,
                            ]);
                        }
                    }
                }

                PushNotificationAdminJob::dispatch(
                    "JSON RESPONSE",
                    json_encode($response),
                );
                return response()->view('success_paid');
            } else {

                $orderExists = OrderPlan::where('order_code', $response['id'] ?? "")->first();

                if ($orderExists  != null) {
                    $orderExists->update([
                        'status' => RenewUtils::PAYMENT_STATUS_CANCEL
                    ]);
                }

                return redirect()
                    ->to(PayPalDataUtils::getCreatePath($appCredentials))
                    ->with('error', $response['message'] ?? 'Something went wrong.');
            }
        } catch (Exception $e) {
            PushNotificationAdminJob::dispatch(
                "Error payment paypal 3",
                $e->getMessage(),
            );
        }
    }
    /**
     * cancel transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function paypalCancel(Request $request)
    {
        return response()->view('payment/error_payment', [
            'msg_code' => 'CANCEL ORDER',
            'msg' => 'CANCEL ORDER',
        ]);
    }
}
