<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Helper\Helper;
use App\Helper\RenewUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\OrderPlan;
use App\Models\User;
use Exception;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

//http://localhost:8001/payment_drcar/buymeacoffee-create?plan_id=30&email=<EMAIL>


class BuyMeACoffeeController extends Controller
{

    public function buymeacoffeeCreate(Request $request)
    {

        $data_links = [
            "ALLDATA" => [
                1 => 'https://www.buymeacoffee.com/drcar/e/110685',
                6 => 'https://www.buymeacoffee.com/drcar/e/110705',
                12 => 'https://www.buymeacoffee.com/drcar/e/110708',
            ],
            "AUTODATA" => [
                1 => 'https://www.buymeacoffee.com/drcar/e/110686',
                6 => 'https://www.buymeacoffee.com/drcar/e/110707',
                12 => 'https://www.buymeacoffee.com/drcar/e/110709',
            ],
            "HAYNESPRO" => [
                1 => 'https://www.buymeacoffee.com/drcar/e/110688',
                6 => 'https://www.buymeacoffee.com/drcar/e/110706',
                12 => 'https://www.buymeacoffee.com/drcar/e/110710',
            ],
            "ALLDATAVSAUTODATAVSHAYNESPRO" => [
                1 => 'https://www.buymeacoffee.com/drcar/e/110711',
                6 => 'https://www.buymeacoffee.com/drcar/e/110712',
                12 => 'https://www.buymeacoffee.com/drcar/e/110713',
            ],
        ];


        try {
            $link = null;
            if (isset($data_links[$request->plan->service][$request->plan->month])) {
                $link = $data_links[$request->plan->service][$request->plan->month];
            } else {
                return response()->view('payment/error_payment', [
                    'msg_code' => 'NOT FOUND LINK PAYMENT',
                    'msg' => "NOT FOUND LINK PAYMENT",
                ]);
            }


            OrderPlan::create([
                'order_code' =>   "CF" . (Helper::getRandomOrderString()),
                'user_id' => $request->user->id,
                'month' => $request->plan->month,
                'product_id' => $request->plan->product_id,
                'plan_id' => $request->plan->id,
                'service' => $request->plan->service,
                'price' => $request->plan->price,
                'status' => RenewUtils::PAYMENT_STATUS_WAIT,
                'payment_partner' => 'buymeacoffee'
            ]);

            return redirect($link);
        } catch (Exception $e) {

            return response()->view('payment/error_payment', [
                'msg_code' => 'CANCEL ORDER',
                'msg' => $e->getMessage(),
            ]);
        }



        //  return redirect("https://buymeacoffee.com/merchant/?m_shop=$m_shop&m_orderid=$m_orderid&m_amount=$m_amount&m_curr=$m_curr&m_desc=$m_desc&m_sign=$sign");
    }

    public function buymeacoffeeSuccess(Request $request)
    {

        // {"response":
        //     {"supporter_email":"<EMAIL>",
        //         "number_of_coffees":1,
        //         "total_amount":"3",
        //         "support_created_on":"2023-01-07T15:01:37.000000Z"
        //     }}


        $request_body =  $request->all();
        $response =  $request->response;

        $email = $response['supporter_email'] ?? null;
        $total_amount = (int)($response['total_amount'] ?? 0);


        if ($total_amount > 0 &&  $email != null) {
            $user = User::where('email', $email)->first();
            if ($user  != null) {
            }

            $orderExists =  OrderPlan::where('user_id', $user->id)
                ->where('payment_partner', 'buymeacoffee')
                ->where('price',  $total_amount)
                // ->where('status',  0)
                ->orderBy('id', 'desc')
                ->first();

            if ($orderExists  != null &&   $orderExists->status != RenewUtils::PAYMENT_STATUS_SUCCESS) {

                RenewUtils::renew_service_for_user(
                    $orderExists->user_id,
                    $orderExists->product_id,
                    RenewUtils::PAY_FROM_NOWPAYMENT,
                    $orderExists->id,
                    $orderExists->order_code,
                    json_encode($request_body),
                    $orderExists->plan_id
                );

                $orderExists->update([
                    'status' => RenewUtils::PAYMENT_STATUS_SUCCESS
                ]);

                return response()->view('success_paid');
                exit;
            }
        }
        return response()->view('payment/error_payment', [
            'msg_code' => 'ORDER NOT FOUND',
            'msg' => 'ORDER NOT FOUND',
        ]);
        exit;
    }

    public function buymeacoffeeCancel()
    {
        return response()->view('payment/error_payment', [
            'msg_code' => 'CANCEL ORDER',
            'msg' => 'CANCEL ORDER',
        ]);
        exit;
    }
}
