<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;


/**
 * @group  Customer/thanh toán
 */
class NganLuongController extends Controller
{

    // Địa chỉ thanh toán hoá đơn của NgânLượng.vn
    public $nganluong_url= 'https://www.nganluong.vn/checkout.php';
    // Mã website của bạn đăng ký trong chức năng tích hợp thanh toán của NgânLượng.vn.
    public $merchant_site_code = "66986"; //100001 chỉ là ví dụ, bạn hãy thay bằng mã của bạn
    // Mật khẩu giao tiếp giữa website của bạn và NgânLượng.vn.
    public $secure_pass = 'af5092081321f589cc570fd622810904'; //d685739bf1 chỉ là ví dụ, bạn hãy thay bằng mật khẩu của bạn
    // Nếu bạn thay đổi mật khẩu giao tiếp trong quản trị website của chức năng tích hợp thanh toán trên NgânLượng.vn, vui lòng update lại mật khẩu này trên website của bạn
    public $affiliate_code = ''; //Mã đối tác tham gia chương trình liên kết của NgânLượng.vn

    /**
     * HÀM TẠO ĐƯỜNG LINK THANH TOÁN QUA NGÂNLƯỢNG.VN VỚI THAM SỐ MỞ RỘNG
     *
     * @param string $return_url: Đường link dùng để cập nhật tình trạng hoá đơn tại website của bạn khi người mua thanh toán thành công tại NgânLượng.vn
     * @param string $receiver: Địa chỉ Email chính của tài khoản NgânLượng.vn của người bán dùng nhận tiền bán hàng
     * @param string $transaction_info: Tham số bổ sung, bạn có thể dùng để lưu các tham số tuỳ ý để cập nhật thông tin khi NgânLượng.vn trả kết quả về
     * @param string $order_code: Mã hoá đơn hoặc tên sản phẩm
     * @param int $price: Tổng tiền hoá đơn/sản phẩm, chưa kể phí vận chuyển, giảm giá, thuế.
     * @param string $currency: Loại tiền tệ, nhận một trong các giá trị 'vnd', 'usd'. Mặc định đồng tiền thanh toán là 'vnd'
     * @param int $quantity: Số lượng sản phẩm
     * @param int $tax: Thuế
     * @param int $discount: Giảm giá
     * @param int $fee_cal: Nhận giá trị 0 hoặc 1. Do trên hệ thống NgânLượng.vn cho phép chủ tài khoản cấu hình cho nhập/thay đổi phí lúc thanh toán hay không. Nếu website của bạn đã có phí vận chuyển và không cho sửa thì đặt tham số này = 0
     * @param int $fee_shipping: Phí vận chuyển
     * @param string $order_description: Mô tả về sản phẩm, đơn hàng
     * @param string $buyer_info: Thông tin người mua 
     * @param string $affiliate_code: Mã đối tác tham gia chương trình liên kết của NgânLượng.vn
     * @return string
     */
    public function nganLuongCreate(Request $request)
    {


        $affiliate_code =  $this->affiliate_code;
        $secure_pass =   $this->secure_pass;
        $merchant_site_code =    $this->merchant_site_code;
        $nganluong_url = $this->nganluong_url;

        $host = $request->getSchemeAndHttpHost();
        $order_code = request('order_code') ?? "ggfdsfdsgdsfv";

        // $has =  Post::where('order_code',    $order_code)->where('paid', false)->first();
        // if ($has == null) {
        //     echo "Đơn thanh toán không tồn tại hoặc đã thanh toán";
        //     return;
        // }

        $return_url = $host . "/api/payment/nganluong/post/return";
        $receiver = '<EMAIL>';
        $transaction_info = $order_code;
        $order_code =  $order_code;
        $price = 10000;
        $currency = 'vnd';
        $quantity = 1;
        $tax = 0;
        $discount = 0;
        $fee_cal = 0;
        $fee_shipping = 0;
        $order_description = '';
        $buyer_info = '';
        $affiliate_code = '';

        if ($affiliate_code == "") $affiliate_code = $this->affiliate_code;
        $arr_param = array(
            'merchant_site_code' =>    strval($this->merchant_site_code),
            'return_url'        =>    strval(strtolower($return_url)),
            'receiver'            =>    strval($receiver),
            'transaction_info'    =>    strval($transaction_info),
            'order_code'        =>    strval($order_code),
            'price'                =>    strval($price),
            'currency'            =>    strval($currency),
            'quantity'            =>    strval($quantity),
            'tax'                =>    strval($tax),
            'discount'            =>    strval($discount),
            'fee_cal'            =>    strval($fee_cal),
            'fee_shipping'        =>    strval($fee_shipping),
            'order_description'    =>    strval($order_description),
            'buyer_info'        =>    strval($buyer_info), //"Họ tên người mua *|* Địa chỉ Email *|* Điện thoại *|* Địa chỉ nhận hàng"
            'affiliate_code'    =>    strval($affiliate_code)
        );


        $secure_code = implode(' ', $arr_param) . ' ' . $this->secure_pass;
        //var_dump($secure_code). "<br/>";
        $arr_param['secure_code'] = md5($secure_code);
        //echo $arr_param['secure_code'];
        /* */
        $redirect_url = $this->nganluong_url;
        if (strpos($redirect_url, '?') === false) {
            $redirect_url .= '?';
        } else if (substr($redirect_url, strlen($redirect_url) - 1, 1) != '?' && strpos($redirect_url, '&') === false) {
            $redirect_url .= '&';
        }

        /* */
        $url = '';
        foreach ($arr_param as $key => $value) {
            $value = urlencode($value);
            if ($url == '') {
                $url .= $key . '=' . $value;
            } else {
                $url .= '&' . $key . '=' . $value;
            }
        }
        //echo $url;
        // die;
dd($redirect_url . $url);
        return redirect($redirect_url . $url);
    }

    public function nganLuongReturn(Request $request)
    {

     //   $info = InfoAdmin::first()->toArray();
        // Địa chỉ thanh toán hoá đơn của NgânLượng.vn
        $nganluong_url = 'https://sandbox.nganluong.vn:8088/nl35/checkout.php';
        // Mã website của bạn đăng ký trong chức năng tích hợp thanh toán của NgânLượng.vn.
        $merchant_site_code = $info["nganluong_merchant_site_code"] ?? ""; //100001 chỉ là ví dụ, bạn hãy thay bằng mã của bạn

        // Mật khẩu giao tiếp giữa website của bạn và NgânLượng.vn.
        $secure_pass = $info["nganluong_secure_pass"] ?? ""; //d685739bf1 chỉ là ví dụ, bạn hãy thay bằng mật khẩu của bạn
        // Nếu bạn thay đổi mật khẩu giao tiếp trong quản trị website của chức năng tích hợp thanh toán trên NgânLượng.vn, vui lòng update lại mật khẩu này trên website của bạn
        $affiliate_code = ''; //Mã đối tác tham gia chương trình liên kết của NgânLượng.vn

        $this->affiliate_code =  $affiliate_code;
        $this->secure_pass =   $secure_pass;
        $this->merchant_site_code =    $merchant_site_code;
        $this->nganluong_url = $nganluong_url;


        if (isset($_GET['payment_id'])) {
            // Lấy các tham số để chuyển sang Ngânlượng thanh toán:

            $transaction_info = $_GET['transaction_info'];
            $order_code = $_GET['order_code'];
            $price = $_GET['price'];
            $payment_id = $_GET['payment_id'];
            $payment_type = $_GET['payment_type'];
            $error_text = $_GET['error_text'];
            $secure_code = $_GET['secure_code'];
            //Khai báo đối tượng của lớp NL_Checkout

            //Tạo link thanh toán đến nganluong.vn
            $checkpay = $this->verifyPaymentUrl($transaction_info, $order_code, $price, $payment_id, $payment_type, $error_text, $secure_code);

            if ($checkpay) {

                // $has =  Post::where('order_code',    $order_code)->first();
                // if ($has != null) {
                //     $has->update([
                //         'paid' => true
                //     ]);
                // }
                echo 'Thanh toán thành công hãy nhấn nút trở về';
                // bạn viết code vào đây để cung cấp sản phẩm cho người mua		
                // print_r($_GET);
            } else {
                echo "payment failed";
            }
        } else {
            echo "payment failed";
        }
    }

    /**
     * HÀM TẠO ĐƯỜNG LINK THANH TOÁN QUA NGÂNLƯỢNG.VN VỚI THAM SỐ CƠ BẢN
     *
     * @param string $return_url: Đường link dùng để cập nhật tình trạng hoá đơn tại website của bạn khi người mua thanh toán thành công tại NgânLượng.vn
     * @param string $receiver: Địa chỉ Email chính của tài khoản NgânLượng.vn của người bán dùng nhận tiền bán hàng
     * @param string $transaction_info: Tham số bổ sung, bạn có thể dùng để lưu các tham số tuỳ ý để cập nhật thông tin khi NgânLượng.vn trả kết quả về
     * @param string $order_code: Mã hoá đơn/Tên sản phẩm
     * @param int $price: Tổng tiền phải thanh toán
     * @return string
     */
    public function buildCheckoutUrl($return_url, $receiver, $transaction_info, $order_code, $price)
    {

        // Bước 1. Mảng các tham số chuyển tới nganluong.vn
        $arr_param = array(
            'merchant_site_code' =>    strval($this->merchant_site_code),
            'return_url'        =>    strtolower(urlencode($return_url)),
            'receiver'            =>    strval($receiver),
            'transaction_info'    =>    strval($transaction_info),
            'order_code'        =>    strval($order_code),
            'price'                =>    strval($price)
        );
        $secure_code = '';
        $secure_code = implode(' ', $arr_param) . ' ' . $this->secure_pass;
        $arr_param['secure_code'] = md5($secure_code);

        /* Bước 2. Kiểm tra  biến $redirect_url xem có '?' không, nếu không có thì bổ sung vào*/
        $redirect_url = $this->nganluong_url;
        if (strpos($redirect_url, '?') === false) {
            $redirect_url .= '?';
        } else if (substr($redirect_url, strlen($redirect_url) - 1, 1) != '?' && strpos($redirect_url, '&') === false) {
            // Nếu biến $redirect_url có '?' nhưng không kết thúc bằng '?' và có chứa dấu '&' thì bổ sung vào cuối
            $redirect_url .= '&';
        }

        /* Bước 3. tạo url*/
        $url = '';
        foreach ($arr_param as $key => $value) {
            if ($key != 'return_url') $value = urlencode($value);

            if ($url == '')
                $url .= $key . '=' . $value;
            else
                $url .= '&' . $key . '=' . $value;
        }
        return $redirect_url . $url;
    }

    /**
     * HÀM KIỂM TRA TÍNH ĐÚNG ĐẮN CỦA ĐƯỜNG LINK KẾT QUẢ TRẢ VỀ TỪ NGÂNLƯỢNG.VN
     *
     * @param string $transaction_info: Thông tin về giao dịch, Giá trị do website gửi sang
     * @param string $order_code: Mã hoá đơn/tên sản phẩm
     * @param string $price: Tổng tiền đã thanh toán
     * @param string $payment_id: Mã giao dịch tại NgânLượng.vn
     * @param int $payment_type: Hình thức thanh toán: 1 - Thanh toán ngay (tiền đã chuyển vào tài khoản NgânLượng.vn của người bán); 2 - Thanh toán Tạm giữ (tiền người mua đã thanh toán nhưng NgânLượng.vn đang giữ hộ)
     * @param string $error_text: Giao dịch thanh toán có bị lỗi hay không. $error_text == "" là không có lỗi. Nếu có lỗi, mô tả lỗi được chứa trong $error_text
     * @param string $secure_code: Mã checksum (mã kiểm tra)
     * @return unknown
     */

    public function verifyPaymentUrl($transaction_info, $order_code, $price, $payment_id, $payment_type, $error_text, $secure_code)
    {
        // Tạo mã xác thực từ chủ web
        $str = '';
        $str .= ' ' . strval($transaction_info);
        $str .= ' ' . strval($order_code);
        $str .= ' ' . strval($price);
        $str .= ' ' . strval($payment_id);
        $str .= ' ' . strval($payment_type);
        $str .= ' ' . strval($error_text);
        $str .= ' ' . strval($this->merchant_site_code);
        $str .= ' ' . strval($this->secure_pass);

        // Mã hóa các tham số
        $verify_secure_code = '';
        $verify_secure_code = md5($str);

        // Xác thực mã của chủ web với mã trả về từ nganluong.vn
        if ($verify_secure_code === $secure_code) return true;
        else return false;
    }
    function GetTransactionDetails($order_code)
    {

        ###################### BEGIN #####################
        $checksum = $order_code . "|" . $this->secure_pass;
        //echo $checksum;
        $params = array(
            'merchant_id'       => $this->merchant_site_code,
            'checksum' => MD5($checksum),
            'order_code'             => $order_code
        );

        $api_url = "https://sandbox.nganluong.vn:8088/nl35/service/order/checkV2";
        $post_field = '';
        foreach ($params as $key => $value) {
            if ($post_field != '') $post_field .= '&';
            $post_field .= $key . "=" . $value;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_ENCODING, 'UTF-8');
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_field);
        $result = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        if ($result != '' && $status == 200) {
            return $result;
        }

        return false;
        ###################### END #####################

    }
}
