<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Helper\Helper;
use App\Helper\RenewUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\OrderPlan;
use Exception;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

//http://localhost:8001/payment_drcar/payeer-create?plan_id=30&email=<EMAIL>


class PayeerController extends Controller
{

    public function payeerCreate(Request $request)
    {
        $m_shop =  '1807539192';
        $m_orderid = Helper::getRandomOrderString();
        $m_amount = number_format(2, 2, '.', '');
        $m_curr = 'USD';
        $m_desc = base64_encode('Пополнение баланса');
        $m_key = 'hoangdr';

        $arHash = array(
            $m_shop,
            $m_orderid,
            $m_amount,
            $m_curr,
            $m_desc,
            $m_key
        );

        $sign = strtoupper(hash('sha256', implode(':', $arHash)));

        if ($m_amount != 0) {
            try {

                OrderPlan::create([
                    'order_code' =>   $m_orderid,
                    'user_id' => $request->user->id,
                    'month' => $request->plan->month,
                    'product_id' => $request->plan->product_id,
                    'plan_id' => $request->plan->id,
                    'service' => $request->plan->service,
                    'price' => $request->plan->price,
                    'status' => RenewUtils::PAYMENT_STATUS_WAIT,
                    'payment_partner' => 'payeer'
                ]);
            } catch (\PDOException $e) {

                dd($e->getMessage());
                // DB::connection()->getPdo()->rollBack();
            }
        }



        return redirect("https://payeer.com/merchant/?m_shop=$m_shop&m_orderid=$m_orderid&m_amount=$m_amount&m_curr=$m_curr&m_desc=$m_desc&m_sign=$sign");
    }

    public function payeerSuccess(Request $request)
    {

        if (!in_array($_SERVER['REMOTE_ADDR'], array('185.71.65.92', '185.71.65.189', '149.202.17.210'))) return;

        if (isset($_POST['m_operation_id']) && isset($_POST['m_sign'])) {
            $m_key = 'hoangdr';

            $arHash = array(
                $_POST['m_operation_id'],
                $_POST['m_operation_ps'],
                $_POST['m_operation_date'],
                $_POST['m_operation_pay_date'],
                $_POST['m_shop'],
                $_POST['m_orderid'],
                $_POST['m_amount'],
                $_POST['m_curr'],
                $_POST['m_desc'],
                $_POST['m_status']
            );

            if (isset($_POST['m_params'])) {
                $arHash[] = $_POST['m_params'];
            }

            $arHash[] = $m_key;

            $sign_hash = strtoupper(hash('sha256', implode(':', $arHash)));

            if ($_POST['m_sign'] == $sign_hash && $_POST['m_status'] == 'success') {

                //xử lý id thành công ở đây
                $orderExists = OrderPlan::where('order_code', $_POST['m_orderid'])->first();

                if ($orderExists  != null && $orderExists->status != RenewUtils::PAYMENT_STATUS_SUCCESS) {

                    try {
                        RenewUtils::renew_service_for_user(
                            $orderExists->user_id,
                            $orderExists->product_id,
                            RenewUtils::PAY_FROM_PAYEER,
                            $orderExists->id,
                            $orderExists->order_code,
                            $request->json_data ?? "",
                            $orderExists->plan_id
                        );
                    } catch (Exception $e) {
                        dd($e);
                    }


                    $orderExists->update([
                        'status' => RenewUtils::PAYMENT_STATUS_SUCCESS
                    ]);
                }
                ///
                return response()->view('success_paid');
            }

            return response()->view('payment/error_payment', [
                'msg_code' => 'CANCEL ORDER',
                'msg' => 'CANCEL ORDER',
            ]);
            exit;
        }
    }

    public function payeerCancel()
    {
        return response()->view('payment/error_payment', [
            'msg_code' => 'CANCEL ORDER',
            'msg' => 'CANCEL ORDER',
        ]);
        exit;
    }
}
