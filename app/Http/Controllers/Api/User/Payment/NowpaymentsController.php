<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Helper\Helper;
use App\Helper\RenewUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\OrderPlan;
use Exception;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

//http://localhost:8001/payment_drcar/nowpayments-create?plan_id=30&email=<EMAIL>


class NowpaymentsController extends Controller
{

    public function nowpaymentsCreate(Request $request)
    {
        $client = new \GuzzleHttp\Client();
        $domain = Helper::getDomainCurrentWithoutSubAndNoHttp();

        try {
            $response1 = $client->request(
                'POST',
                'https://api.nowpayments.io/v1/invoice',
                [
                    'headers' =>   [
                        'x-api-key' => 'JWK04TX-W11M2XJ-NKXCYAC-9J1XAFN'
                    ],
                    'timeout' => 10000, // Response timeout
                    'connect_timeout' => 10000, // Connection timeout
                    'form_params' => [

                        "price_amount" => $request->price,
                        "price_currency" => "usd",
                        "order_id" => Helper::getRandomOrderString(),
                        "order_description" => number_format((float)($request->price), 2, '.', ''),
                        "ipn_callback_url" => "https://main.$domain/payment_drcar/nowpayments-success",
                        "success_url" => "https://main.$domain/payment_drcar/nowpayments-success",
                        "cancel_url" => "https://main.$domain/payment_drcar/nowpayments-cancel"

                    ]
                ],

            );
            $res = json_decode($response1->getBody()->getContents());

            OrderPlan::create([
                'order_code' =>   $res->order_id,
                'user_id' => $request->user->id,
                'product_id' => $request->product_id,
                'plans_json' => json_encode($request->plans),
                'plan_ids_json' => json_encode($request->plan_ids_input),
                'price' => $request->price,
                'before_price' => $request->before_price,
                'code_voucher' => $request->code_voucher,
                'status' => RenewUtils::PAYMENT_STATUS_WAIT,
                'payment_partner' => 'nowpayments'
            ]);

            return redirect($res->invoice_url);
        } catch (Exception $e) {

            return response()->view('payment/error_payment', [
                'msg_code' => 'CANCEL ORDER',
                'msg' => $e->getMessage(),
            ]);
        }



        //  return redirect("https://nowpayments.com/merchant/?m_shop=$m_shop&m_orderid=$m_orderid&m_amount=$m_amount&m_curr=$m_curr&m_desc=$m_desc&m_sign=$sign");
    }

    public function nowpaymentsSuccess(Request $request)
    {

        $request_json = file_get_contents('php://input');
        $res = json_decode($request_json);

        $orderExists = OrderPlan::where('order_code', $res == null ? "@3%" : $res->order_id)->first();
        if ($orderExists  != null &&   $orderExists->status != RenewUtils::PAYMENT_STATUS_SUCCESS && $request->payment_status ==  'finished') {

            foreach ($orderExists->plans  as $plan) {
                RenewUtils::renew_service_for_user(
                    $orderExists->user_id,
                    $orderExists->product_id,
                    RenewUtils::PAY_FROM_NOWPAYMENT,
                    $orderExists->id,
                    $orderExists->order_code,
                    $request_json,
                    $plan->id
                );
            }

            $orderExists->update([
                'status' => RenewUtils::PAYMENT_STATUS_SUCCESS
            ]);
        }
        return response()->view('success_paid');
    }

    public function nowpaymentsCancel()
    {
        return response()->view('payment/error_payment', [
            'msg_code' => 'CANCEL ORDER',
            'msg' => 'CANCEL ORDER',
        ]);
        exit;
    }
}
