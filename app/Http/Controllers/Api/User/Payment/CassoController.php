<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Helper\Helper;
use App\Helper\RenewUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\OrderPlan;
use App\Models\WebHookRecord;
use Illuminate\Support\Facades\DB;

//http://localhost:8001/payment_drcar/casso-create?plan_id=30&email=<EMAIL>
class CassoController extends Controller
{
    /**
     * create transaction.
     *
     * process transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function cassoCreate(Request $request)
    {
        $maxID = DB::table('order_plans')->max('id') + 1;

        $order_code =   "DRCAR" . (Helper::generateRandomNum(2)) . (Helper::generateRandomChart(2));

        while (OrderPlan::where('order_code',  $order_code)->exists()) {
            $order_code =   "DRCAR" . (Helper::generateRandomNum(2)) . (Helper::generateRandomChart(2));
        }

        OrderPlan::create([
            'order_code' =>  $order_code,
            'user_id' => $request->user->id,
            'product_id' => $request->product_id,
            'plans_json' => json_encode($request->plans),
            'plan_ids_json' => json_encode($request->plan_ids_input),
            'price' => $request->price,
            'before_price' => $request->before_price,
            'code_voucher' => $request->code_voucher,
            'status' => RenewUtils::PAYMENT_STATUS_WAIT,
            'payment_partner' => 'casso'
        ]);


        $account_name = "HOANG TIEN SY";
        $account_number = "2986447";
        $code = $order_code;
        $moneyVND = round($request->price * 23000, 0, PHP_ROUND_HALF_UP);

        return response()->view('payment/casso', [
            'email' => "<EMAIL> ",
            'money' => $request->price,
            'moneyVND' => $moneyVND,
            'account_name' =>  $account_name,
            'account_number' => $account_number,
            'code' => $code,
            'imageLink' => "https://img.vietqr.io/image/ACB-$account_number-print.jpg?amount=$moneyVND&addInfo=$code&accountName=$account_name"
        ]);
    }

    public function sync(Request $request)
    {
        $curl = curl_init();

        $data = array(
            'bank_acc_id' => '2986447',
        );
        $postdata = json_encode($data);

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://oauth.casso.vn/v2/sync",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $postdata,
            CURLOPT_HTTPHEADER => [
                "Authorization: Apikey AK_CS.84270be05c4e11eea4a507861474003d.mRJBI0bbEzfClsETW6XbnjY3xeXcubSrTzCu1xjgpOZOEr56JPTwoTXIwMe7u5aa64s26Vzk",
                "Content-Type: application/json"
            ],
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        echo  $response ;
        curl_close($curl);
    }

    public function cassoSuccess(Request $request)
    {

        $request_body =  $request->all();
        $response =  $request->response;

        WebHookRecord::create([
            'type' => "casso",
            'request_body' => json_encode($request_body),
            'note' => "",
        ]);

        $request_json = file_get_contents('php://input');
        $res = json_decode($request_json);

        if (isset($res->error) && $res->error  == 0 && isset($res->data[0])) {

            $order_code = "";
            $description = $res->data[0]->description;
            $description = str_replace(';'," ",$description);
            $descriptions = $description == null ? [] : explode(' ', $description);
            foreach ($descriptions as $des) {
                if (!empty($des) && str_contains($des, "DRCAR") && strlen($des) == 9) {
                    $order_code  = strtoupper($des);
                    break;
                }
            }

            $descriptions = $description == null ? [] : explode('.', $description);
            foreach ($descriptions as $des) {
                if (!empty($des) && str_contains($des, "DRCAR") && strlen($des) == 9) {
                    $order_code  = strtoupper($des);
                    break;
                }
            }


            $orderExists = OrderPlan::where('order_code', $order_code)->first();

            if (!empty($order_code) && $orderExists  != null &&   $orderExists->status != RenewUtils::PAYMENT_STATUS_SUCCESS) {
                $amount = $res->data[0]->amount;

                if ($amount >=  $orderExists->price * 23000) {

                    foreach ($orderExists->plans  as $plan) {
                        RenewUtils::renew_service_for_user(
                            $orderExists->user_id,
                            $orderExists->product_id,
                            RenewUtils::PAY_FROM_CASSO,
                            $orderExists->id,
                            $orderExists->order_code,
                            $request_json,
                            $plan->id
                        );
                    }
                }

                $orderExists->update([
                    'status' => RenewUtils::PAYMENT_STATUS_SUCCESS
                ]);
            }
        }



        return response()->view('success_paid');
    }
}
