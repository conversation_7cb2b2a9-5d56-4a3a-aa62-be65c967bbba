<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Helper\RenewUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\OrderPlan;
use Exception;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

//http://localhost:8001/payment_drcar/stripe-create?plan_id=30&email=<EMAIL>


class StripeController extends Controller
{

    const KEY_TET = 'sk_test_51MD7mSGV0rpmKtAYoR5M2azRMLDWmhtq99KU2WFEDNwgpsOvPdaYwLC4WfTkPdzgZxrfPh6C5hVzyZYxoICmH7Yq002JTD5gap';
    const KEY = '***********************************************************************************************************';


    public function stripeCreate(Request $request)
    {

        \Stripe\Stripe::setApiKey(StripeController::KEY);

        $session = \Stripe\Checkout\Session::create([
            'line_items' => [
                [
                    'quantity' => 1,
                    'price_data' => [

                        'currency' => 'usd',
                        'product_data' => [
                            'name' => $request->plan->service . " " . $request->plan->month . " month"
                        ],
                        'unit_amount' => $request->plan->price * 100
                    ]
                ]
            ],
            'mode' => 'payment',
            'success_url' => route('stripeSuccess') . "?session_id={CHECKOUT_SESSION_ID}",
            'cancel_url' => route('stripeCancel'),
        ]);

        OrderPlan::create([
            'order_code' =>  $session->id,
            'user_id' => $request->user->id,
            'month' => $request->plan->month,
            'product_id' => $request->plan->product_id,
            'plan_id' => $request->plan->id,
            'service' => $request->plan->service,
            'price' => $request->plan->price,
            'status' => RenewUtils::PAYMENT_STATUS_WAIT,
            'payment_partner' => 'stripe'
        ]);

        //$session id
        // $session->id

        return redirect($session->url);
    }

    public function stripeSuccess(Request $request)
    {

        \Stripe\Stripe::setApiKey(StripeController::KEY);

        $session_id = $request->get('session_id');

        $session = \Stripe\Checkout\Session::retrieve($session_id);

        try {
            if (!$session) {
                throw new NotFoundHttpException;
            }

            //xử lý id thành công ở đây
            $orderExists = OrderPlan::where('order_code', $session->id)->first();

            if ($orderExists  != null && $orderExists->status != RenewUtils::PAYMENT_STATUS_SUCCESS) {

                try {
                    RenewUtils::renew_service_for_user(
                        $orderExists->user_id,
                        $orderExists->product_id,
                        RenewUtils::PAY_FROM_STRIPE,
                        $orderExists->id,
                        $orderExists->order_code,
                        $request->json_data ?? "",
                        $orderExists->plan_id
                    );
                } catch (Exception $e) {
                    dd($e);
                }


                $orderExists->update([
                    'status' => RenewUtils::PAYMENT_STATUS_SUCCESS
                ]);
            }
        } catch (Exception $e) {
            dd($e);
            throw new NotFoundHttpException();
        }


        return response()->view('success_paid');
    }

    public function stripeCancel()
    {
        return response()->view('payment/error_payment', [
            'msg_code' => 'CANCEL ORDER',
            'msg' => 'CANCEL ORDER',
        ]);
        exit;
    }
}
