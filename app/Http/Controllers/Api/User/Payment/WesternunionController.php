<?php

namespace App\Http\Controllers\Api\User\Payment;


use Illuminate\Http\Request;
use App\Http\Controllers\Controller;


//http://localhost:8001/payment_drcar/westernunion-create?plan_id=30&email=<EMAIL>
class WesternunionController extends Controller
{
    /**
     * create transaction.
     *
     * process transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function westernunionCreate(Request $request)
    {


        return response()->view('payment/westernunion', [
            'email' => "<EMAIL> ",
            'money' => number_format((float)($request->price), 2, '.', ''),
        ]);
    }
}
