<?php

namespace App\Http\Controllers\Api\User\Payment;


use Illuminate\Http\Request;
use App\Http\Controllers\Controller;


class TetherController extends Controller
{
    /**
     * create transaction.
     *
     * process transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function tetherCreate(Request $request)
    {


        return response()->view('payment/tether', [
            'email' => "<EMAIL> ",
            'money' => number_format((float)($request->price), 2, '.', ''),
        ]);
    }
}
