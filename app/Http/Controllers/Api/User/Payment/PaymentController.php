<?php

namespace App\Http\Controllers\Api\User\Payment;

use App\Helper\Helper;
use App\Helper\IPUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON>\Location\Facades\Location;


/**
 * @group  Customer/thanh toán
 */
class PaymentController extends Controller
{

    /**
     * Danh sách phương thức thanh toán
     */
    public function paymentMethodList(Request $request)
    {

        $platform = request()->header('platform-app-local') ?? $cookies['platform-app-local'] ?? null;
        $domain = Helper::getDomainCurrentWithoutSubAndNoHttp();
        if ($platform  != 'ios2') {
            $all = array();

            if ($position = Location::get(IPUtils::getIP())) {
                if ($position->countryName == 'Vietnam') {
                    array_push($all, [
                        "name" => "Chuyển khoản Ngân Hàng Việt Nam",
                        "description" => "Quét mã QR hoặc chuyển thủ công, x<PERSON><PERSON> nhận thanh toán nhanh chóng tại VIỆT NAM",
                        "image" => "https://web.$domain/image/method_pay/bank-viet.png?v=2",
                        "example_url" => "https://main.$domain/payment_drcar/casso-create?email=<EMAIL>&plan_ids=30",
                        "payment_url" => "https://main.$domain/payment_drcar/casso-create?email={{email}}&plan_ids={{plan_ids}}&plan_id={{plan_id}}",
                    ]);
                }
            }

            array_push($all, [
                "name" => "PayPal",
                "description" => "Pay via PayPal; you can pay with your credit card if you don't have a PayPal account.",
                "image" => "https://web.$domain/image/method_pay/paypal.png",
                "example_url" => "https://main.$domain/payment_drcar/paypal-create?email=<EMAIL>&plan_ids=30",
                "payment_url" => "https://main.$domain/payment_drcar/paypal-create?email={{email}}&plan_ids={{plan_ids}}&plan_id={{plan_id}}",
            ]);

            // array_push($all, [
            //     "name" => "USDT,BTC,.. ",
            //     "description" => "USDT, Bitcoin, Ethereum, Litecoin, Visa, MasterCard, Qiwi, Perfect Money, Bitcoin cash, Dash, WebMoney, Skrill, Neteller, Epay, BinanceCoin, Western Union, Ripple, Dogecoin, ... ",
            //     "image" => "https://web.$domain/image/method_pay/usdt.png",
            //     "example_url" => "https://main.$domain/payment_drcar/nowpayments-create?email=<EMAIL>&plan_ids=30",
            //     "payment_url" => "https://main.$domain/payment_drcar/nowpayments-create?email={{email}}&plan_ids={{plan_ids}}&plan_id={{plan_id}}",
            // ]);
            array_push($all, [
                "name" => "USDT TRC20 ",
                "description" => "Binance, Coinbase, Kraken, Huobi, OKEx, Bitfinex, Bittrex, KuCoin, Gemini, Bitstamp, Poloniex, Gate.io, Upbit, Bybit, Crypto, ... ",
                "image" => "https://web.$domain/image/method_pay/usdt.png",
                "example_url" => "https://main.$domain/payment_drcar/tether-create?email=<EMAIL>&plan_ids=30",
                "payment_url" => "https://main.$domain/payment_drcar/tether-create?email={{email}}&plan_ids={{plan_ids}}&plan_id={{plan_id}}",
            ]);

            // array_push($all,  [
            //     "name" => "Credit Card",
            //     "description" => "",
            //     "image" => "https://web.$domain/image/method_pay/creditcard.png",
            //     "example_url" => "https://main.$domain/payment_drcar/buymeacoffee-create?email=<EMAIL>&plan_ids=30",
            //     "payment_url" => "https://main.$domain/payment_drcar/buymeacoffee-create?email={{email}}&plan_ids={{plan_ids}}&plan_id={{plan_id}}",
            // ]);

            array_push($all, [
                "name" => "Western Union",
                "description" => "",
                "image" => "https://web.$domain/image/method_pay/western-union.png",
                "example_url" => "https://main.$domain/payment_drcar/westernunion-create?email=<EMAIL>&plan_ids=30",
                "payment_url" => "https://main.$domain/payment_drcar/westernunion-create?email={{email}}&plan_ids={{plan_ids}}&plan_id={{plan_id}}",
            ]);

            array_push($all, [
                "name" => "LocalBank, Other method",
                "description" => "",
                "image" => "https://web.$domain/image/method_pay/localbank.png",
                "example_url" => "https://wa.me/+***********",
                "payment_url" => "https://wa.me/+***********",
            ]);
        }
        if ($request->user != null && $request->user->email == "<EMAIL>") {
            $all = array();


            // De mo khoa paypal smilepunny
            array_push($all, [
                "name" => "PayPal",
                "description" => "Pay via PayPal; you can pay with your credit card if you don't have a PayPal account.",
                "image" => "https://web.$domain/image/method_pay/paypal.png",
                "example_url" => "https://main.$domain/payment_drcar/paypal-manual-create?email=<EMAIL>&plan_ids=30",
                "payment_url" => "https://main.$domain/payment_drcar/paypal-manual-create?email={{email}}&plan_ids={{plan_ids}}&plan_id={{plan_id}}",
            ]);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Thực hiện thanh toán
     * @urlParam  store_code required Store code cần lấy
     * @urlParam  order_code required Mã đơn hàng
     */
    public function pay(Request $request)
    {

        $email = request('email');
        $product_id = request('product_id'); //in  plan service
        $method = request('method'); //phương thức thanh toán


        //return response()->view('payment/payment_index');

        $query_pram = "?email=$email&product_id=$product_id";



        return redirect("/payment_drcar/paypal-process" . $query_pram);
    }
}
