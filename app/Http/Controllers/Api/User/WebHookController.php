<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\AgencyWebTheme;
use App\Models\MsgCode;
use App\Models\WebHookRecord;
use Illuminate\Http\Request;
use Stripe\Webhook;

/**
 * @group  User/Ghi webhook
 */
class WebHookController extends Controller
{

    /**
     * Thông tin giao diện
     */
    public function saveWebHookBuymecoffee(Request $request)
    {
        $request_body =  $request->all();
        $response =  $request->response;

        WebHookRecord::create([
            'type' => "buymecoffee",
            'request_body' => json_encode($request_body),
            'note' => ($response['supporter_email'] ?? "") . " " . ($response['total_amount'] ?? ""),
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    public function saveWebHookCasso(Request $request)
    {
        $request_body =  $request->all();
        $response =  $request->response;

        WebHookRecord::create([
            'type' => "casso",
            'request_body' => json_encode($request_body),
            'note' => "",
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
