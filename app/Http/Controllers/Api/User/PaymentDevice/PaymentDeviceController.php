<?php

namespace App\Http\Controllers\Api\User\PaymentDevice;

use App\Helper\Helper;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Illuminate\Http\Request;

use function PHPSTORM_META\map;

/**
 * @group  Customer/thanh toán
 */
class PaymentDeviceController extends Controller
{

    /**
     * Danh sách phương thức thanh toán
     */
    public function paymentMethodList(Request $request)
    {


        $platform = request()->header('platform-app-local') ?? $cookies['platform-app-local'] ?? null;
        $domain = Helper::getDomainCurrentWithoutSubAndNoHttp();
        if ($platform  != 'ios2') {
            $all = [
                [
                    "name" => "PayPal",
                    "description" => "Pay via PayPal; you can pay with your credit card if you don't have a PayPal account.",
                    "image" => "https://web.$domain/image/method_pay/paypal.png",
                    "example_url" => "https://main.$domain/payment_drcar/paypal-create?email=<EMAIL>&plan_id=30",
                    "payment_url" => "https://main.$domain/payment_drcar/paypal-create?email={{email}}&plan_id={{plan_id}}",
                ],
                // [
                //     "name" => "PayPal",
                //     "description" => "Pay via PayPal; you can pay with your credit card if you don't have a PayPal account.",
                //     "image" => "https://web.$domain/image/method_pay/paypal.png",
                //     "example_url" => "https://main.$domain/payment_drcar/paypal-manual-create?email=<EMAIL>&plan_id=30",
                //     "payment_url" => "https://main.$domain/payment_drcar/paypal-manual-create?email={{email}}&plan_id={{plan_id}}",
                // ],
                [
                    "name" => "USDT,BTC,.. ",
                    "description" => "USDT, Bitcoin, Ethereum, Litecoin, Visa, MasterCard, Qiwi, Perfect Money, Bitcoin cash, Dash, WebMoney, Skrill, Neteller, Epay, BinanceCoin, Western Union, Ripple, Dogecoin, ... ",
                    "image" => "https://web.$domain/image/method_pay/usdt.png",
                    "example_url" => "https://main.$domain/payment_drcar/nowpayments-create?email=<EMAIL>&plan_id=30",
                    "payment_url" => "https://main.$domain/payment_drcar/nowpayments-create?email={{email}}&plan_id={{plan_id}}",
                ],
                // [
                //     "name" => "Credit Card",
                //     "description" => "",
                //     "image" => 'https://web.$domain/image/method_pay/creditcard.png",
                //     "example_url" => 'https://main.$domain/payment_drcar/stripe-create?email=<EMAIL>&plan_id=30",
                //     "payment_url" => 'https://main.$domain/payment_drcar/stripe-create?email={{email}}&plan_id={{plan_id}}",
                // ],
                // [
                //     "name" => "Alipay",
                //     "description" => "",
                //     "image" => "https://web.$domain/image/method_pay/alipay.png",
                //     "example_url" => "https://main.$domain/payment_drcar/stripe-create?email=<EMAIL>&plan_id=30",
                //     "payment_url" => "https://main.$domain/payment_drcar/stripe-create?email={{email}}&plan_id={{plan_id}}",
                // ],
                // [
                //     "name" => "Credit Card",
                //     "description" => "",
                //     "image" => "https://web.$domain/image/method_pay/creditcard.png",
                //     "example_url" => "https://main.$domain/payment_drcar/buymeacoffee-create?email=<EMAIL>&plan_id=30",
                //     "payment_url" => "https://main.$domain/payment_drcar/buymeacoffee-create?email={{email}}&plan_id={{plan_id}}",
                // ],
                [
                    "name" => "LocalBank, Other method",
                    "description" => "",
                    "image" => "https://web.$domain/image/method_pay/localbank.png",
                    "example_url" => 'https://wa.me/+***********',
                    "payment_url" => 'https://wa.me/+***********',
                ],
            ];
        }
        if ($request->user != null && $request->user->email == "<EMAIL>") {
            $all = array();
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Thực hiện thanh toán
     * @urlParam  store_code required Store code cần lấy
     * @urlParam  order_code required Mã đơn hàng
     */
    public function pay(Request $request)
    {

        $email = request('email');
        $month = request('month'); //in  plan service
        $device_login_id = request('device_login_id'); //id thiet bi


        //return response()->view('payment/payment_index');

        $query_pram = "?email=$email&month=$month&device_login_id=$device_login_id";



        return redirect("/payment_device_drcar/paypal-process" . $query_pram);
    }
}
