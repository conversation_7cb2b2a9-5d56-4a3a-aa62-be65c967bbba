<?php

namespace App\Http\Controllers\Api\User\PaymentDevice;

use App\Helper\PayPalDataUtils;
use App\Helper\PlanUtils;
use App\Helper\RenewUtils;

use App\Models\OrderPlan;
use Illuminate\Http\Request;
use Srmklive\PayPal\Services\PayPal as PayPalClient;
use App\Http\Controllers\Controller;

class PayPalDeviceController extends Controller
{

    public function inputPayPalCreate()
    {
        return response()->view('payment/input_payment_paypal');
        return response()->view('payment/error_payment', []);
    }

    public function inputPayPalCreateLink(Request $request)
    {

        $appCredentials = PayPalDataUtils::getCredentials();
        $provider = new PayPalClient;
        $provider->setApiCredentials($appCredentials);
        $paypalToken = $provider->getAccessToken();
        $response = $provider->createOrder([
            "intent" => "CAPTURE",
            "application_context" => [
                "return_url" => PayPalDataUtils::getSuccessPath($appCredentials, 'payment_device_drcar'),
                "cancel_url" => PayPalDataUtils::getCancelPath($appCredentials, 'payment_device_drcar'),
            ],
            "purchase_units" => [
                0 => [
                    "amount" => [
                        "currency_code" => "USD",
                        "value" =>  $request->price
                    ]
                ]
            ]
        ]);


        if (isset($response['id']) && $response['id'] != null) {


            // redirect to approve href
            foreach ($response['links'] as $links) {
                if ($links['rel'] == 'approve') {
                    return response()->view('payment/create_link_payment_paypal', [
                        "link" => $links['href'],
                        "price" => $request->price
                    ]);
                }
            }

            return redirect()
                ->to(PayPalDataUtils::getCreatePath($appCredentials, 'payment_device_drcar'))
                ->with('error', 'Something went wrong.');
        } else {

            return response()->view('payment/error_payment', [
                'msg_code' => $response['error']['name'],
                'msg' => $response['error']['message'],
            ]);


            return redirect()
                ->to(PayPalDataUtils::getCreatePath($appCredentials, 'payment_device_drcar'))
                ->with('error', $response['message'] ?? 'Something went wrong.');
        }
    }

    /**
     * create transaction.
     *
     * process transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function paypalDeviceCreate(Request $request)
    {
        $appCredentials = PayPalDataUtils::getCredentials();
        $provider = new PayPalClient;
        $provider->setApiCredentials($appCredentials);
        $paypalToken = $provider->getAccessToken();
        $response = $provider->createOrder([
            "intent" => "CAPTURE",
            "application_context" => [
                "return_url" => PayPalDataUtils::getSuccessPath($appCredentials, 'payment_device_drcar'),
                "cancel_url" => PayPalDataUtils::getCancelPath($appCredentials, 'payment_device_drcar'),
            ],
            "purchase_units" => [
                0 => [
                    "amount" => [
                        "currency_code" => "USD",
                        "value" =>  $request->plan->price
                    ]
                ]
            ]
        ]);


        if (isset($response['id']) && $response['id'] != null) {

            OrderPlan::create([
                'order_code' => $response['id'],
                'user_id' => $request->user->id,
                'month' => $request->plan->month,
                'product_id' => null,
                'plan_id' => null,
                'service' => PlanUtils::MORE_DEVICE,
                'price' => $request->plan->price,
                'status' => RenewUtils::PAYMENT_STATUS_WAIT,
                'payment_partner' => 'paypal',
                'device_login_id' => $request->device_login_id
            ]);




            // redirect to approve href
            foreach ($response['links'] as $links) {
                if ($links['rel'] == 'approve') {
                    return redirect()->away($links['href']);
                }
            }

            return redirect()
                ->to(PayPalDataUtils::getCreatePath($appCredentials, "payment_device_drcar"))
                ->with('error', 'Something went wrong.');
        } else {

            return response()->view('payment/error_payment', [
                'msg_code' => $response['error']['name'],
                'msg' => $response['error']['message'],
            ]);


            return redirect()
                ->to(PayPalDataUtils::getCreatePath($appCredentials, "payment_device_drcar"))
                ->with('error', $response['message'] ?? 'Something went wrong.');
        }
    }
    /**
     * success transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function paypalDeviceSuccess(Request $request)
    {
        $provider = new PayPalClient;
        $appCredentials = PayPalDataUtils::getCredentials();
        $provider->setApiCredentials($appCredentials);
        $provider->getAccessToken();
        $response = $provider->capturePaymentOrder($request['token']);
        if (isset($response['status']) && $response['status'] == 'COMPLETED') {
            // return redirect()
            //     ->route('success_paid')
            //     ->with('success', 'Transaction complete.');


            $orderExists = OrderPlan::where('order_code', $response['id'])->first();

            if ($orderExists  != null && $orderExists->status != RenewUtils::PAYMENT_STATUS_SUCCESS) {

                RenewUtils::add_sub_expiry_device(
                    $orderExists->user_id,
                    $orderExists->month,
                    $orderExists->price,
                    true,
                    PlanUtils::MORE_DEVICE,
                    PlanUtils::MORE_DEVICE,
                    $orderExists->id,
                    $orderExists->order_code,
                    $orderExists->device_login_id,
                    RenewUtils::PAY_FROM_PAYPAL,
                    $request->json_data ?? "",
                );

                $orderExists->update([
                    'status' => RenewUtils::PAYMENT_STATUS_SUCCESS
                ]);
            }
            return response()->view('success_paid');
        } else {

            $orderExists = OrderPlan::where('order_code', $response['id'] ?? "")->first();

            if ($orderExists  != null) {
                $orderExists->update([
                    'status' => RenewUtils::PAYMENT_STATUS_CANCEL
                ]);
            }

            return redirect()
                ->to(PayPalDataUtils::getCreatePath($appCredentials, 'payment_device_drcar'))
                ->with('error', $response['message'] ?? 'Something went wrong.');
        }
    }
    /**
     * cancel transaction.
     *
     * @return \Illuminate\Http\Response
     */
    public function paypalDeviceCancel(Request $request)
    {
        return response()->view('payment/error_payment', [
            'msg_code' => 'CANCEL ORDER',
            'msg' => 'CANCEL ORDER',
        ]);
    }
}
