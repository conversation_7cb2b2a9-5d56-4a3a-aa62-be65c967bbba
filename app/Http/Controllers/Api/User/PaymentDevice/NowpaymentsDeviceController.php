<?php

namespace App\Http\Controllers\Api\User\PaymentDevice;

use App\Helper\Helper;
use App\Helper\PlanUtils;
use App\Helper\RenewUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\OrderPlan;
use Exception;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

//http://localhost:8001/payment_device_drcar/nowpayments-create?plan_id=30&email=<EMAIL>


class NowpaymentsDeviceController extends Controller
{

    public function nowpaymentsDeviceCreate(Request $request)
    {
        $client = new \GuzzleHttp\Client();
        $domain = Helper::getDomainCurrentWithoutSubAndNoHttp();

        try {
            $response1 = $client->request(
                'POST',
                'https://api.nowpayments.io/v1/invoice',
                [
                    'headers' =>   [
                        'x-api-key' => 'JWK04TX-W11M2XJ-NKXCYAC-9J1XAFN'
                    ],
                    'timeout' => 10000, // Response timeout
                    'connect_timeout' => 10000, // Connection timeout
                    'form_params' => [

                        "price_amount" => $request->plan->price,
                        "price_currency" => "usd",
                        "order_id" => Helper::getRandomOrderString(),
                        "order_description" => $request->plan->service . " " . $request->plan->month . " month",
                        "ipn_callback_url" => "https://main.$domain/payment_device_drcar/nowpayments-success",
                        "success_url" => "https://main.$domain/payment_device_drcar/nowpayments-success",
                        "cancel_url" => "https://main.$domain/payment_device_drcar/nowpayments-cancel"

                    ]
                ],

            );
            $res = json_decode($response1->getBody()->getContents());

            OrderPlan::create([
                'order_code' =>   $res->order_id,
                'user_id' => $request->user->id,
                'month' => $request->plan->month,
                'product_id' => null,
                'plan_id' => $request->plan->id,
                'service' => PlanUtils::MORE_DEVICE,
                'price' => $request->plan->price,
                'status' => RenewUtils::PAYMENT_STATUS_WAIT,
                'payment_partner' => 'nowpayments',
                'device_login_id' => $request->device_login_id
            ]);

            return redirect($res->invoice_url);
        } catch (Exception $e) {

            return response()->view('payment/error_payment', [
                'msg_code' => 'CANCEL ORDER',
                'msg' => $e->getMessage(),
            ]);
        }



        //  return redirect("https://nowpayments.com/merchant/?m_shop=$m_shop&m_orderid=$m_orderid&m_amount=$m_amount&m_curr=$m_curr&m_desc=$m_desc&m_sign=$sign");
    }

    public function nowpaymentsDeviceSuccess(Request $request)
    {

        $request_json = file_get_contents('php://input');
        $res = json_decode($request_json);

        $orderExists = OrderPlan::where('order_code',$res==null? "@3%" : $res->order_id)->first();
        if ($orderExists  != null &&   $orderExists->status != RenewUtils::PAYMENT_STATUS_SUCCESS && $request->payment_status ==  'finished') {

            RenewUtils::add_sub_expiry_device(
                $orderExists->user_id,
                $orderExists->month,
                $orderExists->price,
                true,
                PlanUtils::MORE_DEVICE,
                PlanUtils::MORE_DEVICE,
                $orderExists->id,
                $orderExists->order_code,
                $orderExists->device_login_id,
                RenewUtils::PAY_FROM_NOWPAYMENT,
                $request_json,
            );


            $orderExists->update([
                'status' => RenewUtils::PAYMENT_STATUS_SUCCESS
            ]);
        }
        return response()->view('success_paid');
    }

    public function nowpaymentsDeviceCancel()
    {
        return response()->view('payment/error_payment', [
            'msg_code' => 'CANCEL ORDER',
            'msg' => 'CANCEL ORDER',
        ]);
        exit;
    }
}
