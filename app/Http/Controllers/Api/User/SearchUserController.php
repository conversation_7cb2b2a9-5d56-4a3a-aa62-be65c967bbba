<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\PhoneUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\SessionUser;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

/**
 * @group  User/Search user
 */
class SearchUserController extends Controller
{

    /**
     * Login
     * @bodyParam phone_number string required (Username, email hoặc số điện thoại)
     */
    public function searchOne(Request $request)
    {
        $phone = PhoneUtils::convert($request->phone_number);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => User::select('id', 'phone_number','name','type', 'avatar_image')
            ->when($request->user != null && $request->user->type == 0, function ($query) {
                $query->where('type', '!=', 0);
            })
            ->where('phone_number', $phone)->first()
        ], 200);
    }
}
