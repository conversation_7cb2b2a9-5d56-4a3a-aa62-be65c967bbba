<?php

namespace App\Http\Controllers\Api\User;
use App\Http\Controllers\Controller;
use App\Models\AgencyWebTheme;
use App\Models\MsgCode;
use Illuminate\Http\Request;

/**
 * @group  User/Thay đổi giao diện
 */
class WebThemeController extends Controller
{

    /**
     * Thông tin giao diện
     */
    public function getThemeWeb(Request $request)
    {
        $theme = AgencyWebTheme::where('agency_id', $request->agency->id)->first();
        $default = [
            "title" => $theme->title ?? "DrCar",
            "description" => $theme->description ?? "Repair Documentation Full Data.",
            "color_main_1" => $theme->color_main_1 ?? "#fff",
            "background_color" => null,
            'theme_style' => $theme->theme_style ?? "",
            "logo_url" => $theme->logo_url ?? "",
            "background_url" => $theme->background_url ?? "",
            "favicon_url" => $theme->favicon_url ?? "",
            "contact_link" => $theme->contact_link ?? "",
        ];
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $default,
        ], 200);
    }
}
