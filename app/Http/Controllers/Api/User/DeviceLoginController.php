<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\Helper;
use App\Helper\RenewUtils;
use App\Helper\SecurityUtils;
use App\Http\Controllers\Controller;
use App\Models\DeviceLogin;
use App\Models\MsgCode;
use App\Models\PlanDevice;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;

const LIST_MONTH_PLAN_DEVICE = [
    [
        "month" => 6,
        "price" => 30
    ],
    [
        "month" => 12,
        "price" => 50
    ],
];

/**
 * @group  User/Danh sách device
 */

class DeviceLoginController extends Controller
{



    public function list_plan_devices()
    {

        $planDevice  = PlanDevice::get()->toArray();
        $domain = Helper::getDomainCurrentWithoutSubAndNoHttp();
        $data = [
            "list_month" =>  $planDevice,
            "list_method" => [
                [
                    "name" => "PayPal",
                    "description" => "Pay via PayPal; you can pay with your credit card if you don't have a PayPal account.",
                    "image" => "https://web.$domain/image/method_pay/paypal.png",
                    "example_url" => "https://main.$domain/payment_device_drcar/paypal-create?email=<EMAIL>&month=30&device_login_id=1",
                    "payment_url" => "https://main.$domain/payment_device_drcar/paypal-create?email={{email}}&month={{month}}&device_login_id={{device_login_id}}",
                ],
                // [
                //     "name" => "USDT,BTC,.. ",
                //     "description" => "USDT, Bitcoin, Ethereum, Litecoin, Visa, MasterCard, Qiwi, Perfect Money, Bitcoin cash, Dash, WebMoney, Skrill, Neteller, Epay, BinanceCoin, Western Union, Ripple, Dogecoin, ... ",
                //     "image" => "https://web.$domain/image/method_pay/usdt.png",
                //     "example_url" => "https://main.$domain/payment_device_drcar/nowpayments-create?email=<EMAIL>&month=30&device_login_id=1",
                //     "payment_url" => "https://main.$domain/payment_device_drcar/nowpayments-create?email={{email}}&month={{month}}&device_login_id={{device_login_id}}",
                // ],

            ]
        ];


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $data
        ], 200);
    }

    /**
     * Danh sách thiết bị
     * 
     * 
     */
    public function getAll(Request $request)
    {

        $all = DeviceLogin::where('user_id', $request->user->id)
            ->get();

        foreach ($all as $item) {

            if ((\Carbon\Carbon::now()->lessThan($item->expiry_use) || $item->is_main) &&  str_contains($item->device_id, SecurityUtils::getSplitDevice())) {
                $item->active = true;
                break;
            }
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }


    /**
     * 
     * Reset device
     * 
     * @bodyParam device_login_id id của máy
     * 
     * 
     */
    public function resetDevice(Request $request)
    {


        DeviceLogin::where('user_id',  $request->user->id)
            ->where('id', $request->device_login_id)
            ->update([
                "ip_using" => null,
                "address" => null,
                "device_id" => null,
                "model_name" => null,
                "platform" => null,
                "last_visit_time" => null,
                "app_version" => null,
                "login_time" => null,
            ]);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * 
     * Xoa device
     * 
     * @bodyParam device_login_id id của máy
     * 
     * 
     */
    public function deleteDevice(Request $request)
    {

        $device = DeviceLogin::where('user_id',  $request->user->id)
            ->where('id', $request->device_login_id)
            ->first();
        if ($device  != null) {
            if ($device->is_main) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => "Main can't delete ",
                ], 400);
            } else  if (\Carbon\Carbon::now()->lessThan($device->expiry_use)) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => "Has expiry can't delete ",
                ], 400);
            }
        }
        $device->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * 
     * Check device 
     * 
     * 
     */
    public function deviceCheckSecurity(Request $request)
    {

        $has_expiry = RenewUtils::check_has_expiry($request->user);
        if ($has_expiry == false) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
            ], 200);
        }

        if (SecurityUtils::useDevice($request->user) == false) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR_LOGGED_IN_ON_ANOTHER_DEVICE[0],
                'msg' => MsgCode::ERROR_LOGGED_IN_ON_ANOTHER_DEVICE[1],
            ], 400);
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * 
     * Check device gia hạn và device
     * 
     * 
     */
    public function checkSecurity(Request $request)
    {

        $tailLink = $_SERVER["REQUEST_URI"];

        $domain_request = $_SERVER['HTTP_HOST'];

        if (str_contains($domain_request, "main-temp.")) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
            ], 200);
        }
        

        
        if ($request->user != null) {
            $column = request('column_expiry');
            $user = $request->user;

            $timeNow = Carbon::parse(Helper::getTimeNowString());

            if ($column  != null && $user->$column  == null || !Carbon::parse($user->$column)->greaterThan($timeNow)) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::EXPIRED[0],
                    'msg' => MsgCode::EXPIRED[1],
                ], 400);
            }
        }

        if (SecurityUtils::useDevice($request->user) == false) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR_LOGGED_IN_ON_ANOTHER_DEVICE[0],
                'msg' => MsgCode::ERROR_LOGGED_IN_ON_ANOTHER_DEVICE[1],
            ], 400);
        }




        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
