<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\User;
use App\Models\UserFriend;
use App\Models\FriendRequest;
use App\Models\Voucher;
use Carbon\Carbon;
use Illuminate\Http\Request;


/**
 * @group  User/Voucher
 */

class VoucherController extends Controller
{


    /**
     * Kiểm tra voucher hợp lệ
     * 
     * @bodyParam code_voucher required Code voucher
     * 
     */
    public function checkVoucher(Request $request)
    {
        $code_voucher = $request->code_voucher;

        $voucherExists = Voucher::where('code_voucher',  strtoupper($code_voucher))
            ->where('end_time', ">=", Carbon::now()->format('Y-m-d H:i:s'))->first();

        if ($voucherExists  != null) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
            ], 200);
        } else {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::VOUCHER_DOES_NOT_EXIST[0],
                'msg' => MsgCode::VOUCHER_DOES_NOT_EXIST[1],
            ], 400);
        }
    }
}
