<?php

namespace App\Http\Controllers\Api\User;

use App\Events\RedisChatEventUserToUser;
use App\Helper\ChatUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\GroupBanMember;
use App\Models\GroupChat;
use App\Models\GroupMember;
use App\Models\UToUMessage;
use App\Models\User;
use App\Models\MsgCode;
use App\Models\PersonChat;


/**
 * @group  User/Group
 */

class GroupController extends Controller
{

    function check_permisstion(Request $request,   $group_id)
    {

        $userInGroup = GroupMember::where('group_id',  $group_id)->where('user_id', $request->user->id)->first();
        if ($userInGroup == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Bạn không phải thành viên nhóm này",
            ], 400);
        }

        if ($request->user->type != 1 && $request->user->type != 3 && $userInGroup->is_key == false) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Trưởng nhóm, quản lý hoặc thầy mới có quyền này",
            ], 400);
        }
    }

    static function get_members($group_id, $request)
    {

        $group = Group::where('id', $group_id)->first();
        $get_list_member =   filter_var(request('get_list_member'), FILTER_VALIDATE_BOOLEAN);


        if ($get_list_member  == true) {
            $memebers = $group->members = GroupMember::where('group_id',  $group_id)->orderBy('created_at', 'desc')->paginate(request('limit') ?? 20);
            foreach ($memebers  as $memebr) {
                $banned = GroupBanMember::where('user_id',  $memebr->user_id)->where('group_id',  $group_id)->first() != null;
                $memebr->banned =  $banned;
            }
            $group->members =  $memebers;
        }


        return   $group;
    }
    /**
     * Tạo group chat
     * 
     * @bodyParam name required Tên group
     * @bodyParam list_user_ids required List danh sách id member sp (VD: [1,2,3])
     * 
     * 
     */
    public function createGroup(Request $request)
    {
        if (empty($request->name)) {
            return response()->json([
                'code' => 400,
                'success' => true,
                'msg_code' => MsgCode::NAME_IS_REQUIRED[0],
                'msg' => MsgCode::NAME_IS_REQUIRED[1],
            ], 400);
        }

        if (!is_array($request->list_user_ids) && count($request->list_user_ids) < 3) {
            return response()->json([
                'code' => 400,
                'success' => true,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Số lượng thành viên phải từ 3 trở lên",
            ], 400);
        }

        $group = Group::create([
            'name' => $request->name,
            'image_url' => $request->image_url,
            'user_id' => $request->user->id,
        ]);

        foreach ($request->list_user_ids as $user_id) {
            GroupMember::create([
                'group_id' =>  $group->id,
                'user_id' => $user_id,
                'is_key' => $user_id == $request->user->id
            ]);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  Group::where('id',  $group->id)->first()
        ], 200);
    }

    /**
     * Rời khỏi group
     * 
     * 
     */
    public function leaveGroup(Request $request)
    {
        $group_id = $request->route()->parameter('group_id');

        GroupMember::where('group_id',  $group_id)
            ->where('user_id', $request->user->id)->delete();


        GroupChat::where('user_id', $request->user->id)->where('to_group_id',  $group_id)->delete();
        ChatUtils::sendListSocket($request->user->id, ChatUtils::LIST_GROUP_CHAT);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Xem 1 group
     * 
     * image_url
     * name
     * 
     */
    public function update(Request $request)
    {
        $group_id = $request->route()->parameter('group_id');

        $group = Group::where('id', $group_id)->first();

        if ($group  == null) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $group
            ], 200);
        }

        $group->update([
            'name' => $request->name,
            'image_url' =>  $request->image_url,
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => GroupController::get_members($group_id, $request)
        ], 200);
    }

    /**
     * Xem 1 group
     * 
     * 
     * 
     */
    public function getOne(Request $request)
    {
        $group_id = $request->route()->parameter('group_id');

        $group = Group::where('id', $group_id)->first();

        if ($group  == null) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $group
            ], 200);
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => GroupController::get_members($group_id, $request)
        ], 200);
    }

    /**
     * OnOff chat
     * 
     * @bodyParam list_user_ids required List danh sách id member sp (VD: [1,2,3])
     * 
     */
    public function onOffChatMember(Request $request)
    {
        $group_id = $request->route()->parameter('group_id');

        $group = Group::where('id', $group_id)->first();

        if ($group  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_EXISTS[0],
                'msg' => MsgCode::NO_GROUP_EXISTS[1],
            ], 400);
        }

        $e =  $this->check_permisstion($request,   $group_id);
        if ($e != null) {
            return $e;
        }

        $group->update([
            'off_chat' => $request->off_chat
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => GroupController::get_members($group_id, $request)
        ], 200);
    }

    /**
     * Thêm thành viên vào group
     * 
     * @bodyParam list_user_ids required List danh sách id member sp (VD: [1,2,3])
     * 
     */
    public function kickMember(Request $request)
    {
        $group_id = $request->route()->parameter('group_id');

        $group = Group::where('id', $group_id)->first();

        if ($group  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_EXISTS[0],
                'msg' => MsgCode::NO_GROUP_EXISTS[1],
            ], 400);
        }

        $userInGroup = GroupMember::where('group_id',  $group_id)->where('user_id', $request->user->id)->first();
        if ($userInGroup == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Bạn không phải thành viên nhóm này",
            ], 400);
        }

        if (in_array($request->user->id, $request->list_user_ids)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không thể Kick chính bạn ra khỏi nhóm",
            ], 400);
        }

        if ($request->user->type != 1 && $request->user->type != 3 && $userInGroup->is_key == false) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Trưởng nhóm, quản lý hoặc thầy mới có quyền này",
            ], 400);
        }



        foreach ($request->list_user_ids as $user_id) {
            GroupMember::where('group_id', $group->id)->where('user_id', $user_id)->delete();
            GroupChat::where('user_id', $user_id)->delete();
            ChatUtils::sendListSocket($user_id, ChatUtils::LIST_GROUP_CHAT);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => GroupController::get_members($group_id, $request)
        ], 200);
    }
    /**
     * Thêm thành viên vào group
     * 
     * @bodyParam list_user_ids required List danh sách id member sp (VD: [1,2,3])
     * 
     */
    public function addMember(Request $request)
    {
        $group_id = $request->route()->parameter('group_id');

        $group = Group::where('id', $group_id)->first();

        if ($group  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_EXISTS[0],
                'msg' => MsgCode::NO_GROUP_EXISTS[1],
            ], 400);
        }

        foreach ($request->list_user_ids as $user_id) {
            if (GroupMember::where('group_id', $group->id)->where('user_id', $user_id)->first() == null) {
                GroupMember::create([
                    'group_id' =>  $group->id,
                    'user_id' => $user_id,
                    'is_key' => $user_id == $request->user->id
                ]);
            }
        }


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => GroupController::get_members($group_id, $request)
        ], 200);
    }

    /**
     * Xóa group
     * 
     * 
     */
    public function removeGroup(Request $request)
    {
        $group_id = $request->route()->parameter('group_id');

        if (
            Group::where('id',  $group_id)
            ->where('user_id', $request->user->id)->first() == null
        ) {
            return response()->json([
                'code' => 400,
                'success' => true,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Bạn không phải người tạo nhóm này không thể xóa",
            ], 400);
        }

        Group::where('id',  $group_id)
            ->where('user_id', $request->user->id)->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
