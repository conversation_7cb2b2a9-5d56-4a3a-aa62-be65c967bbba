<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;


class DecoderController extends Controller
{



    /**
     * Giải mã VIN
     * 
     */
    public function vinDecode(Request $request)
    {
        $content = Storage::get('vindata/wmis/wmis.json');
        $jsonWmis = json_decode($content, true);

        $country = "";
        $manufacturer = "";
        $makeName = "";
        $makeCode = "";

        $arrDataWMIs = $jsonWmis['wmis'];
        $vin = $request->vin ?? "";
        if (strlen($vin) != 17) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'Vin code required 17 characters',
            ], 400);
        }

        $wmi = substr($vin, 0, 3);

        foreach ($arrDataWMIs as $WMIitem) {
            if (in_array($wmi, explode(" ", $WMIitem['WMI']))) {
                $country = $WMIitem['Country'];
                $manufacturer = $WMIitem['Manufacturer'];
                break;
            }
        }

        //đã lấy được make
        $content = Storage::get('vindata/makes/makes.json');
        $jsonMakes = json_decode($content, true);
        $arrDataMakes = $jsonMakes['makes'];
        foreach ($arrDataMakes as $makeItem) {

            $namebase = strtolower(str_replace(" ", "", $makeItem['name']));
            $manufacturerbase = strtolower(str_replace(" ", "", $manufacturer));

            if (str_contains($manufacturerbase, $namebase)) {
                $makeName = $makeItem['name'];
                $makeCode = $makeItem['code'];
            }
        }

        dd($country,  $manufacturer,  $makeName,  $makeCode);

        $data = [
            [
                "name" => "General infomation",
                "data" => [
                    [
                        "name" => "VIN",
                        "value" => "RL4BT9135B9511751",
                    ],
                    [
                        "name" => "WMI/VDS/VIS",
                        "value" => "RL4 BT9135 B9511751",
                    ],
                    [
                        "name" => "Manufacturer",
                        "value" => $manufacturer,
                        "value_image" => 'https://www.vindecoderz.com/media/brands/TOYOT.png'
                    ],
                    [
                        "name" => "Brand",
                        "value" => "Toyota",
                    ],
                    [
                        "name" => "Model",
                        "value" => "Yaris",
                    ],
                    [
                        "name" => "Name",
                        "value" => "VIOS",
                    ],
                    [
                        "name" => "Description",
                        "value" => "NCP9#",
                    ],
                    [
                        "name" => "Date",
                        "value" => "02.2011",
                    ],
                ]
            ],
            [
                "name" => "Manufacturer",
                "data" => [
                    [
                        "name" => "Manufacturer",
                        "value" => $manufacturer,
                    ],
                    [
                        "name" => "Adress #1",
                        "value" => "Phuc Thang Ward",
                    ],
                    [
                        "name" => "Adress #2",
                        "value" => "Phuc Yen Town Vinh Phuc P"
                    ],
                    [
                        "name" => "Region",
                        "value" => "Asia",
                    ],
                    [
                        "name" => "Country",
                        "value" => $country,
                    ],
                    "value_image" => 'https://www.vindecoderz.com/media/brands/TOYOT.png'
                ]
            ]
        ];



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $data,
            'arrDataWMIs' => $arrDataWMIs
        ], 200);
    }
}
