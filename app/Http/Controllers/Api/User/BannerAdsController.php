<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\BannerAds;
use App\Models\Category;
use App\Models\MsgCode;
use Illuminate\Http\Request;

    /**
    * @group  User/Danh sách banner
    */
class BannerAdsController extends Controller
{

    /**
	* Danh sách banner
	*/
    public function getAll(Request $request)
    {

        $bannerAds = BannerAds::orderBy('position', 'ASC')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $bannerAds,
        ], 200);
    }
}
