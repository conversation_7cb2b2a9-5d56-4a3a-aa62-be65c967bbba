<?php

namespace App\Http\Controllers\Api\User;

use App\Events\RedisChatEventUserToGroup;
use App\Events\RedisChatEventUserToUser;
use App\Helper\ChatUtils;
use App\Helper\Helper;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationUserJob;
use App\Models\Group;
use App\Models\GroupBanMember;
use App\Models\GroupChat;
use App\Models\GroupMember;
use App\Models\User;
use App\Models\MsgCode;
use App\Models\PersonChat;
use App\Models\UToGroupMessage;

/**
 * @group  User/Chat
 */

class GroupMessageController extends Controller
{


    /**
     * Danh sách group ddang dang
     * 
     */
    public function getAllGroup(Request $request)
    {

        $all = GroupChat::where('user_id', $request->user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }


    /**
     * Danh sách tin nhắn với 1 group
     * @bodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * 
     * 
     */
    public function getAllMessage(Request $request)
    {
        $group_id = $request->route()->parameter('to_group_id');

        $groupChat =  GroupChat::where('user_id', $request->user->id)->where('to_group_id', $group_id)->first();

        if ($groupChat != null) {
            $groupChat->update([
                'seen' => true,
            ]);
        }
        $all = UToGroupMessage::where('to_group_id', $group_id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Thu hồi tin nhắn
     * @bodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * 
     * 
     */
    public function recallMessage(Request $request)
    {
        $group_id = $request->route()->parameter('to_group_id');
        $mess_id = $request->route()->parameter('mess_id');


        $mess =  UToGroupMessage::where('to_group_id', $group_id)
            ->where('id',  $mess_id)
            ->first();


        if ($mess == null) {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Tin nhắn đã được xóa",
            ], 200);
        }


    
        if ($mess  != null) {

            event($e = new RedisChatEventUserToGroup($mess->toArray(), ChatUtils::TYPE_CHAT_RECALL));
            $mess->delete();

            $messLass = UToGroupMessage::where('to_group_id', $group_id)->orderBy('created_at', 'desc')->first();
            if ($messLass  != null) {
                $members = GroupMember::where('group_id',  $group_id)->get();
            
                foreach ($members  as  $member) {
                    $lass = GroupChat::where('user_id', $member->user_id)->where('to_group_id', $group_id)->first();
               
                    if ($lass  != null) {
                        $lass->update([
                            "user_id" => $member->user_id,
                            "from_user_id" =>  $messLass->user_id,
                            "last_mess" => $messLass->content,
                            'seen' => $messLass->seen,

                        ]);
                    }

                    ChatUtils::sendListSocket($member->user_id, ChatUtils::LIST_GROUP_CHAT);
                }
            }
        }



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Gửi tin nhắn
     * 
     * @bodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * Khách nhận tin nhắn reatime khai báo io socket port 6441 nhận 
     * var socket = io("http://localhost:6441")
     * socket.on("chat:message2_from_user_to_user:1:2", function(data) {   (1:2   1 là từ user nào gửi tới cusotmer nào nếu đang cần nhận thì 1 là người cần nhận 2 là id của bạn)
     *   console.log(data)
     *   })
     * chat:message:1   với 1 là user_id
     * 
     * 
     * 
     */
    public function sendMessage(Request $request)
    {
        $to_group_id = $request->route()->parameter('to_group_id');
        $groupExist = Group::where('id', $to_group_id)->first();

        if ($groupExist  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_EXISTS[0],
                'msg' => MsgCode::NO_GROUP_EXISTS[1],
            ], 400);
        }

        if ($request->images == null && empty($request->content)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::CONTENT_IS_REQUIRED[0],
                'msg' => MsgCode::CONTENT_IS_REQUIRED[1],
            ], 400);
        }

        if (empty($request->id) || UToGroupMessage::where('id', $request->id)->first()  != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "ID không hợp lệ",
            ], 400);
        }

        $mess = new UToGroupMessage(
            [
                "user_id" => $request->user->id,
                "to_group_id" =>  $to_group_id,
                "content" => $request->content,
                'images_json' => json_encode($request->images),
                "id" => $request->id,
            ]
        );


        $off_chat = $groupExist->off_chat;
        if ($off_chat == true && ($request->user->type != 1 && $request->user->type != 3)) {
            event($e = new RedisChatEventUserToGroup($mess->toArray(), ChatUtils::TYPE_CHAT_RECALL));
            $mess->delete();

            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không thể chat cho nhóm này",
            ], 400);
        }

        $has_ban = GroupBanMember::where('user_id',  $request->user->id)->where('group_id',  $to_group_id)->first() != null;
        if ($has_ban == true) {

            event($e = new RedisChatEventUserToGroup($mess->toArray(), ChatUtils::TYPE_CHAT_RECALL));
            $mess->delete();

            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Tài khoản của bạn bị cấm chat với nhóm này",
            ], 400);
        }

        event($e = new RedisChatEventUserToGroup($mess->toArray(), ChatUtils::TYPE_CHAT_0));

        //thêm mess cho người gửi
        $mess->save();

        $members = GroupMember::where('group_id',  $to_group_id)->get();

        foreach ($members  as  $member) {
            $gc =   GroupChat::where('user_id', $member->user_id)->where('to_group_id', $to_group_id)->first();

            if ($gc != null) {
                $gc->update([
                    "user_id" => $member->user_id,
                    "from_user_id" =>  $request->user->id,
                    "to_group_id" => $to_group_id,
                    "last_mess" => $request->content,
                    'seen' => $member->user_id == $request->user->id ?  true : false,
                    "created_at" => Helper::getTimeNowString(),
                    'updated_at' => Helper::getTimeNowString(),
                ]);
            } else {
                GroupChat::create([
                    "user_id" => $member->user_id,
                    "from_user_id" =>  $request->user->id,
                    "to_group_id" => $to_group_id,
                    "last_mess" => $request->content,
                    'seen' => $member->user_id == $request->user->id ?  true : false,
                ]);
            }



            if ($member->user_id != $request->user->id) {


                PushNotificationUserJob::dispatch(
                    $member->user_id,
                    $groupExist->name,
                    substr($request->content, 0, 80),
                );
            }


            ChatUtils::sendListSocket($member->user_id, ChatUtils::LIST_GROUP_CHAT);
        }



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>   $mess
        ], 200);
    }
}
