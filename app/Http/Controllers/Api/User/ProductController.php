<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\Product;
use Illuminate\Http\Request;

/**
 * @group  User/Sản phẩm
 */
class ProductController extends Controller
{
    /**
     * Danh sách sản phẩm Home
     * 
     * @queryParam category_ids list id category
     * 
     */
    public function getAllHome(Request $request)
    {

        $data = Product::where('status', '!=', 1)->where('is_show_home', true)->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $data
        ], 200);
    }
    /**
     * Danh sách sản phẩm
     * 
     * @queryParam category_ids list id category
     * 
     */
    public function getAll(Request $request)
    {

        $search = StringUtils::convert_name_lowcase(request('search'));

        $categoryIds = request("category_ids") == null ? [] : explode(',', request("category_ids"));

        $data = Product::where('status', '!=', 1)
            ->when(count($categoryIds) > 0, function ($query) use ($categoryIds) {
                $query->whereHas('categories', function ($query) use ($categoryIds) {
                    $query->whereIn('categories.id', $categoryIds);
                });
            })
            ->when(!empty($search), function ($query) use ($search, $request) {
                $query->search($search, null, true, true);
            })
            ->paginate(request('limit') == null ? 20 : request('limit'));

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $data
        ], 200);
    }
}
