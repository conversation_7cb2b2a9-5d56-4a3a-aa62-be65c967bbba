<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\Helper;
use App\Helper\IPUtils;
use App\Helper\SendEmailUtils;
use App\Http\Controllers\Controller;
use App\Models\MailReset;
use App\Models\User;
use App\Models\MsgCode;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Mail;

/**
 * @group  User/Đăng ký
 */
class RegisterController extends Controller
{
    /**
     * Register
     * @bodyParam phone_number string required Số điện thoại
     * @bodyParam password string required Password
     * 
     */
    public function register(Request $request)
    {

        $client_ip = IPUtils::getIP();

        if (Cache::get('register' . $client_ip) != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' =>  MsgCode::ERROR[0],
                'msg' => "Please don't ddos my server",
            ], 400);
        }

        //Config
        $dateFrom = Carbon::parse(Carbon::now()->format('Y-m-d 00:00:00'))->timezone('UTC')->toDateTimeString();
        $dateTo = Carbon::parse(Carbon::now()->format('Y-m-d 23:59:59'))->timezone('UTC')->toDateTimeString();

        if (
            DB::table('users')->where('ip_using',  $client_ip)->where('created_at', '>=',  $dateFrom)
            ->where('created_at', '<', $dateTo)->count() > 5
        ) {
            Cache::put('register' . $client_ip, 'has', $seconds = 15);


            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "MAX USERS",
            ], 400);
        }

        if (empty($request->email) || !filter_var($request->email, FILTER_VALIDATE_EMAIL)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_EMAIL[0],
                'msg' => MsgCode::INVALID_EMAIL[1],
            ], 400);
        }


        if (
            !empty($request->email) &&
            $request->email != null &&
            strlen($request->email) > 0 && User::where('email', $request->email)->exists()
        ) {

            return response()->json([
                'code' => 409,
                'success' => false,
                'msg_code' => MsgCode::EMAIL_ALREADY_EXISTS[0],
                'msg' => MsgCode::EMAIL_ALREADY_EXISTS[1],
            ], 409);
        }



        if (
            strlen($request->password) < 6
        ) {

            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[0],
                'msg' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[1],
            ], 400);
        }



        if ($client_ip != null) {
            if (Cache::lock('register22' . $client_ip, 2)->get()) {
                //tiếp tục handle
            } else {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' =>  MsgCode::ERROR[0],
                    'msg' => "Wait 5s",
                ], 400);
            }
        }

        $timeNow = Carbon::parse(Helper::getTimeNowString());
        $timeNext =  $timeNow->addHours(1);

        $userCreate = User::create(
            [
                'email' => $request->email,
                'username' => $request->email,
                'name' => $request->name,
                'password' => bcrypt($request->password),

                'ip_using' =>  $client_ip,
                'ip_register' => $client_ip,

                'email_verified_at' => Helper::getTimeNowString()

                //  'expiry_alldata' =>  $timeNext,
                //  'expiry_autodata' =>  $timeNext,

            ]
        );



        SendEmailUtils::sendEmailRegisterSuccess($request->email);

        try {
            $mailReset =  MailReset::create([
                'user_id' => $userCreate->id,
                'code' => Helper::generateRandomNum() . Helper::generateRandomString(10),
                'status' => 0,
                'type' => "VERIFY_EMAIL"
            ]);

            $actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
            $link = $actual_link . "/api/user/email_reset?code=" . $mailReset->code . "&type=VERIFY_EMAIL";

            Mail::to([$userCreate->email])
                ->send(new \App\Mail\SendMailVerifyEmail($link));
        } catch (Exception $e) {
        }


        return response()->json([
            'code' => 201,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $userCreate
        ], 201);
    }
}
