<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\User;
use App\Models\UserFriend;
use App\Models\FriendRequest;
use Illuminate\Http\Request;


/**
 * @group  User/Danh sách bạn bè
 */

class FriendController extends Controller
{


    /**
     * Tìm Danh sách user
     * 
     * @bodyParam search required Tìm theo tên hoặc sdt
     * 
     */
    public function searchAll(Request $request)
    {

        $search = StringUtils::convert_name_lowcase(request('search'));


        $all = User::select('id', 'name', 'avatar_image', 'date_of_birth')
        ->orderBy('created_at', 'desc')
        ->search($search)
        ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Danh sách bạn bè
     * 
     * @queryParam user_id required Nếu là user id
     * @queryParam search required Tìm tên sdt
     * 
     */
    public function getAll(Request $request)
    {

        $search = StringUtils::convert_name_lowcase(request('search'));
        $list_friends_ids =  UserFriend::where('user_id', $request->user->id)
            ->pluck('friend_user_id');

        $all = User::select('id', 'avatar_image', 'name', 'date_of_birth')->whereIn('id',  $list_friends_ids)
            ->orderBy('name', 'desc')
            ->search($search)
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Danh sách bạn bè
     * 
     * @queryParam user_id required Nếu là user id
     * 
     */
    public function cancelFriend(Request $request)
    {

        $user_id = $request->route()->parameter('user_id');

        UserFriend::where('user_id', $request->user->id)->where('friend_user_id',   $user_id)->delete();
        // UserFriend::where('user_id', $user_id)->where('friend_user_id',   $request->user->id)->delete();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Danh sách yêu cầu kết bạn
     * 
     * @urlParam request_id required Request id
     * @bodyParam status int Hành động 0 xóa 1 đồng ý kết bạn
     * 
     */
    public function getAllRequestFriend(Request $request)
    {

        $all = FriendRequest::where('to_user_id',  $request->user->id)
            ->orderBy('id', 'desc')
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[1],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $all
        ], 200);
    }

    /**
     * Thêm bạn
     * 
     * @urlParam request_id required Request id
     * @bodyParam status int Hành động 0 xóa 1 đồng ý kết bạn
     * 
     */
    public function addFriend(Request $request)
    {
        $user_id = $request->route()->parameter('user_id');



        if (User::where('id',     $user_id)->first() == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $isFriend = UserFriend::where('friend_user_id',  $user_id)
            ->where('user_id', $request->user->id)
            ->first() != null;

        if ($isFriend  == true) {
        } else {
            UserFriend::create([
                'friend_user_id' => $user_id,
                'user_id' => $request->user->id,
            ]);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }


    /**
     * Danh sách bạn bè của 1 người
     * 
     * @queryParam user_id required Nếu là user id
     * @queryParam search required Tìm tên sdt
     * 
     */
    public function getAllFriendOfUser(Request $request)
    {
        $user_id = request('user_id');

        $userExists = User::where('id',   $user_id)->first();
        if ($userExists  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[1],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $search = StringUtils::convert_name_lowcase(request('search'));
        $list_friends_ids =  UserFriend::where('user_id',   $user_id)
            ->pluck('friend_user_id');

        $all = User::whereIn('id',  $list_friends_ids)
            ->orderBy('created_at', 'desc')
            ->search($search)
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[1],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }
}
