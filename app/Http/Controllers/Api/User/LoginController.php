<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\IPUtils;
use App\Helper\PhoneUtils;
use App\Helper\SecurityUtils;
use App\Http\Controllers\Controller;
use App\Models\DeviceLogin;
use App\Models\MsgCode;
use App\Models\SessionUser;
use App\Models\User;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * @group  User/Đăng nhập
 */
class LoginController extends Controller
{



    /**
     * Login
     * @bodyParam username string required (Username, email hoặc số điện thoại)
     * @bodyParam password string required Password
     */
    public function login(Request $request)
    {

        $username = $request->username ?? $request->email;

        $loginAttempts = Cache::get('login_attempts_' .  $username, 0);
        if ($loginAttempts >= 5) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Try after 1 minute",
            ], 400);
        }


        if (empty($username)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USERNAME_EXISTS[0],
                'msg' => MsgCode::NO_USERNAME_EXISTS[1],
            ], 400);
        }

        $username =  str_replace(" ", "", $username);


        $dataCheckLogin = [
            'username' =>   $username,
            'password' => $request->password,
        ];


        //B1 xác thực tồn tại
        if (Auth::attempt($dataCheckLogin)) {
            $checkTokenExists = SessionUser::where(
                'user_id',
                Auth::id()
            )->first();
            //B2 tạo token


            $user = User::where('id',  Auth::id())->first();

            if ($user  != null && $user->is_block) {
                return response()->json([
                    'code' => 401,
                    'msg_code' => MsgCode::YOU_HAVE_BEEN_BLOCKED[0],
                    'msg' => MsgCode::YOU_HAVE_BEEN_BLOCKED[1],
                    'success' => false,
                ], 401);
            }

            SecurityUtils::useDevice($user);

            if (empty($checkTokenExists)) {
                $userSession = SessionUser::create([
                    'token' => Str::random(40),
                    'refresh_token' => Str::random(40),
                    'token_expried' => date('Y-m-d H:i:s',  strtotime('+100 day')),
                    'refresh_token_expried' => date('Y-m-d H:i:s',  strtotime('+365 day')),
                    'user_id' => Auth()->id()
                ]);
            } else {
                $userSession =  $checkTokenExists;
            }

            return response()->json([
                'code' => 200,
                'success' => true,
                'data' => $userSession,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
            ], 200);
        }


        // Người dùng đăng nhập sai
        // Lấy số lần đăng nhập sai hiện tại của người dùng
        $loginAttempts = Cache::get('login_attempts_' .  $username, 0);
        // Tăng số lần đăng nhập sai lên 1
        $loginAttempts++;
        // Lưu số lần đăng nhập sai mới vào cache
        Cache::put('login_attempts_' .  $username, $loginAttempts, now()->addMinutes(1));



        return response()->json([
            'code' => 401,
            'success' => false,
            'msg_code' => MsgCode::WRONG_ACCOUNT_OR_PASSWORD[1],
            'msg' => MsgCode::WRONG_ACCOUNT_OR_PASSWORD[1],
        ], 401);
    }

    public function logout(Request $request)
    {

        $request->user->update([
            'platform' => null,
            'device_id' => null,
            'model' => null,
        ]);
        // SessionUser::where('user_id',  $request->user->id)->delete();

        $device_id_split  =  SecurityUtils::getSplitDevice();

        //Kiểm tra slot đó có trong list device
        $carbon = new Carbon();
        $time = $carbon->format('Y-m-d H:i:s');
        DeviceLogin::where('user_id',  $request->user->id)
            ->where('device_id', 'like', '%' . $device_id_split  . '%')
            ->update([
                "ip_using" => null,
                "address" => null,
                "device_id" => null,
                "model_name" => null,
                "platform" => null,
                "last_visit_time" => null,
                "app_version" => null,
                "login_time" => null,
            ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[1],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Lấy lại mật khẩu
     * @bodyParam phone_number string required Số điện thoại
     * @bodyParam password string required Mật khẩu mới
     * @bodyParam otp string gửi tin nhắn (DV SAHA gửi tới 8085)
     * @bodyParam otp_from string  phone(từ sdt)  email(từ email) mặc định là phone
     */
    public function reset_password(Request $request)
    {
        // $phone = PhoneUtils::convert($request->phone_number);
        // $email = $request->email;
        // $otp = $request->otp;

        // // if (
        // //     $phone == null &&
        // //     PhoneUtils::check_valid($phone) == false
        // // ) {
        // //     return response()->json([
        // //         'code' => 400,
        // //         'success' => false,
        // //         'msg_code' => MsgCode::INVALID_PHONE_NUMBER[0],
        // //         'msg' => MsgCode::INVALID_PHONE_NUMBER[1],
        // //     ], 400);
        // // }

        // $user = User::where('phone_number', $phone)->first();


        // $user = null;

        // if ($request->email_or_phone_number != null) {
        //     if (Helper::validEmail($request->email_or_phone_number) != null) {
        //         $email = $request->email_or_phone_number;
        //         $user = User::where('email', $request->email_or_phone_number)->first();
        //     } else {
        //         $phone = PhoneUtils::convert($request->email_or_phone_number);
        //         $user = User::where('phone_number', $request->email_or_phone_number)->first();
        //     }
        // } else {
        //     $user = User::where('phone_number', $phone)->first();
        // }


        // if ($user == null) {
        //     return response()->json([
        //         'code' => 400,
        //         'success' => false,
        //         'msg_code' => MsgCode::NO_PHONE_NUMBER_EXISTS[0],
        //         'msg' => MsgCode::NO_PHONE_NUMBER_EXISTS[1],
        //     ], 400);
        // }

        // /////
        // $from = "";
        // $type = "";
        // if ($request->otp_from == "email") {
        //     $from = $email;
        //     $type = "email";
        //     $otpExis = OtpCodeEmail::where('email', $email)
        //         ->where('otp', $otp)
        //         ->first();
        //     if ($otpExis == null) {
        //         return response()->json([
        //             'code' => 400,
        //             'success' => false,
        //             'msg_code' => MsgCode::INVALID_OTP[0],
        //             'msg' => MsgCode::INVALID_OTP[1],
        //         ], 400);
        //     }
        // } else {
        //     $from = $phone;
        //     $type = "phone";
        //     $otpExis = OtpCodePhone::where('phone', $phone)
        //         ->where('otp', $otp)
        //         ->first();
        //     if ($otpExis == null) {
        //         return response()->json([
        //             'code' => 400,
        //             'success' => false,
        //             'msg_code' => MsgCode::INVALID_OTP[0],
        //             'msg' => MsgCode::INVALID_OTP[1],
        //         ], 400);
        //     }
        // }


        // if (HandleReceiverSmsController::has_expired_otp($from, $type)) {
        //     return response()->json([
        //         'code' => 400,
        //         'success' => false,
        //         'msg_code' => MsgCode::EXPIRED_PIN_CODE[0],
        //         'msg' => MsgCode::EXPIRED_PIN_CODE[1],
        //     ], 400);
        // }
        // /////

        // if (
        //     strlen($request->password) < 6
        // ) {

        //     return response()->json([
        //         'code' => 400,
        //         'success' => false,
        //         'msg_code' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[0],
        //         'msg' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[1],
        //     ], 400);
        // }


        // SessionUser::where('user_id',  $user->id)->delete();
        // HandleReceiverSmsController::reset_otp($phone);

        // $user->update(
        //     [
        //         'password' => bcrypt($request->password)
        //     ]
        // );

        // return response()->json([
        //     'code' => 200,
        //     'success' => true,
        //     'msg_code' => MsgCode::SUCCESS[0],
        //     'msg' => MsgCode::SUCCESS[1],
        // ], 200);
    }


    /**
     * Thay đổi mật khẩu
     * @bodyParam password string required Mật khẩu mới
     */
    public function change_password(Request $request)
    {
        $newPassword = $request->new_password;



        $dataCheckLogin = [
            'email' => $request->user->email,
            'password' => $request->old_password,

        ];


        if (!Auth::attempt($dataCheckLogin)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_OLD_PASSWORD[0],
                'msg' => MsgCode::INVALID_OLD_PASSWORD[1],
            ], 400);
        }

        if (
            strlen($newPassword) < 6
        ) {

            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[0],
                'msg' => MsgCode::PASSWORD_NOT_LESS_THAN_6_CHARACTERS[1],
            ], 400);
        }

        $user = $request->user;

        $user->update(
            [
                'password' => bcrypt($newPassword)
            ]
        );

        $checkTokenExists = SessionUser::where(
            'user_id',
            $user->id
        )->first();

        if ($checkTokenExists) {
            $checkTokenExists->delete();
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Kiểm tra email,phone_number đã tồn tại
     * Sẽ ưu tiên kiểm tra phone_number (kết quả true tồn tại, false không tồn tại)
     * @bodyParam phone_number required phone_number
     * @bodyParam email string required email
     */
    public function check_exists(Request $request)
    {

        $email = $request->email;

        $list_check = [];


        $user2 = User::where('email', $email)->first();
        if ($user2 != null) {


            array_push($list_check, [
                "name" => "email",
                "value" => true
            ]);
        } else {
            array_push($list_check, [
                "name" => "email",
                "value" => false
            ]);
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $list_check
        ], 200);
    }
}
