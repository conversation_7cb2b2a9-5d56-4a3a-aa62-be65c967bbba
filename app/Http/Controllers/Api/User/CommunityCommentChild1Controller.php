<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\TypeFCM;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationAdminJob;
use App\Models\Comment;
use App\Models\MsgCode;
use App\Models\CommunityComment;
use App\Models\CommunityCommentChild1;
use Illuminate\Http\Request;

/**
 * @group  User/Comment
 */
class CommunityCommentChild1Controller extends Controller
{


    /**
     * Danh sách comment của 1 bài đăng
     * @queryParam community_comment_id int id bài viết cần xem
     * @urlParam status integer required trạng thái  (1 chờ duyệt, 0 đã duyệt, 2 đã ẩn)
     * 
     * 
     */
    public function getAll(Request $request)
    {
        $community_comment_id = request('community_comment_id');


        if ($request->user == null) {
            $all =
                CommunityCommentChild1::where(function ($query) use ($request) {
                    $query->where('status', '=', 0);
                })->where('community_comment_id', $community_comment_id)
                ->orderBy('id', 'desc')
                ->paginate(20);
        } else {
            $all =
            CommunityCommentChild1::where(function ($query) use ($request) {
                    $query->where('user_id', $request->user->id)
                        ->orWhere('status', '=', 0);
                })->where('community_comment_id', $community_comment_id)
                ->orderBy('id', 'desc')
                ->paginate(20);;
        }



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[1],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }
    /**
     * Trả lại 1 comment
     * 
     * @bodyParam community_comment_id integer required id bài viết
     * @BodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * 
     */
    public function create(Request $request)
    {


        $checkCommentExists = CommunityComment::where(
            'id',
            $request->community_comment_id
        )->first();

        if ($checkCommentExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_COMMENT_EXISTS[1],
                'msg' => MsgCode::NO_COMMENT_EXISTS[1],
            ], 400);
        }


        $postCreated = CommunityCommentChild1::create(
            [
                'community_comment_id' => $request->community_comment_id,
                'user_id' => $request->user == null ? null : $request->user->id,
                'status' => 0,
                'content' => $request->content,
                'images_json' => json_encode($request->images),
            ]
        );

        // PushNotificationAdminJob::dispatch(
        //     "Bình luận mới" . ($is_buy ? " cần mua " : " cần bán "),
        //     "Bài đăng: " . ($checkCommentExists->name),
        //     $is_buy ?   TypeFCM::NEW_COMMENT_BUY : TypeFCM::NEW_COMMENT_SELL,
        //     $checkCommentExists->id,
        //     $postCreated->name,
        // );



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[1],
            'msg' => MsgCode::SUCCESS[1],
            'data' => CommunityComment::where('id', $postCreated->id)->first()
        ], 200);
    }



    /**
     * Cập nhật Commet cộng đồng
     * 
     * @bodyParam content required Content
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * 
     */
    public function update(Request $request)
    {


        $id = $request->route()->parameter('community_comment_id');
        $checkCommentExists = CommunityComment::where(
            'id',
            $id
        )->where('user_id', $request->user->id)
        ->first();

        if ($checkCommentExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_COMMENT_EXISTS[1],
                'msg' => MsgCode::NO_COMMENT_EXISTS[1],
            ], 400);
        }


        $checkCommentExists->update(
            [
                'content' => $request->content,
                'images_json' => json_encode($request->images),
            ]
        );


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[1],
            'msg' => MsgCode::SUCCESS[1],
            'data' => CommunityCommentChild1::where('id', $checkCommentExists->id)->first()
        ], 200);
    }



    /**
     * Xóa comment
     * 
     * 
     */
    public function delete(Request $request)
    {

        $id = $request->route()->parameter('community_comment_id');
        $checkCommentExists = CommunityCommentChild1::where(
            'id',
            $id
        )->where('user_id', $request->user->id)
        ->first();


        if ($checkCommentExists == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_COMMENT_EXISTS[1],
                'msg' => MsgCode::NO_COMMENT_EXISTS[1],
            ], 400);
        }

        $checkCommentExists->delete();
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[1],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
