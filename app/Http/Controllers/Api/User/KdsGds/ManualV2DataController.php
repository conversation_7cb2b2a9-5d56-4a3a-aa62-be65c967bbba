<?php

namespace App\Http\Controllers\Api\User\KdsGds;

use App\Classes\ParamManualData;
use App\Helper\DataDSUtils;
use App\Helper\Helper;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * @group  User/Đăng nhập
 */
class ManualV2DataController extends Controller
{


    function getManualV2DataMain(ParamManualData $paramManualData)
    {

        if ($paramManualData->TYPE  == null) {
            $paramManualData->TYPE = 'CONTENTS';
        }
        if ($paramManualData->USER_ID  == null) {
            $paramManualData->USER_ID = DataDSUtils::getUserID();
        }


        try {

            $TOKEN  = $paramManualData->TOKEN;
            $url = request()->carprovidertype == "hyundai" ?
                DataDSUtils::HYUNDAI_URL . ":443/gdsM2/manualV2Data.action" :
                DataDSUtils::KIA_URL . ":443/gdsM2/manualV2Data.action";

            $paramManualData->AREA = request()->carprovidertype == "hyundai" ?
                "HME" :
                "KME";

          //  $USER_ID = DataDSUtils::getUserID();


            //str nay test
            //   $str = "TOKEN=$TOKEN&TYPE=CONTENTS&USER_ID=$USER_ID&VEHICLE_TYPE=Passenger&MFR_CODE=HY&AREA=HME&LANG_CODE=ENG&MODEL_ID=IB12&MODEL_YR=2015&MMC_TYPE=all&MMC_ID=0&SC=10&SCQ=1010&COMP_ID=none&SIT_Q=102&SIT_SQ=none&PCODE=none&REL_COMP=none&SYMP_ID=none&STEP_NBR=none&REV_NO=0&GROUP=SHOP";
            $arrParam =  array(
                "TOKEN" => $paramManualData->TOKEN,
                "TYPE" =>  $paramManualData->TYPE,
                "USER_ID" =>  $paramManualData->USER_ID,
                "VEHICLE_TYPE" =>  $paramManualData->VEHICLE_TYPE,
                "MFR_CODE" =>  $paramManualData->MFR_CODE,
                "AREA" =>  $paramManualData->AREA,
                "LANG_CODE" =>  $paramManualData->LANG_CODE,
                "MODEL_ID" =>   $paramManualData->MODEL_ID,
                "MODEL_YR" =>   $paramManualData->MODEL_YR,
                "MMC_TYPE" =>   $paramManualData->MMC_TYPE,
                "MMC_ID" =>   $paramManualData->MMC_ID,
                "MMC_INFO" =>   $paramManualData->MMC_INFO,
                "ENGINE_CODE" => $paramManualData->ENGINE_CODE,
                "SC" =>  $paramManualData->SC,
                "SCQ" =>  $paramManualData->SCQ,
                "COMP_ID" =>  $paramManualData->COMP_ID,
                "SIT_Q" => $paramManualData->SIT_Q,
                "SIT_SQ" =>  $paramManualData->SIT_SQ,
                "PCODE" =>  $paramManualData->PCODE,
                "REL_COMP" =>  $paramManualData->REL_COMP,
                "SYMP_ID" =>  $paramManualData->SYMP_ID,
                "STEP_NBR" =>  $paramManualData->STEP_NBR,
                "REV_NO" =>  $paramManualData->REV_NO,
                "GROUP" =>  $paramManualData->GROUP,
            );

            $str = http_build_query($arrParam);

            $str = str_replace('%40', '@', $str);
            $str = str_replace('%2C', ',', $str);
            $str = str_replace('%7C', '|', $str);

            $AES256Encodev = DataDSUtils::AES256Encode($str);

            $client = new Client();
            $response = $client->post($url, [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'body' =>  $AES256Encodev
            ]);


            $dataMap = json_decode($response->getBody()->getContents(), true);
          
            return $dataMap;
        } catch (Exception $err) {

            throw $err->getMessage();
        }
        return "";
    }

    /**
     * Kiem tra han su dung
     * @bodyParam username string required (Username, email hoặc số điện thoại)
     * @bodyParam password string required Password
     */
    public function getManualV2Data(Request $request)
    {

        function requestFromHex($hex)
        {
            $url = request()->carprovidertype == "hyundai" ?
                DataDSUtils::HYUNDAI_URL . ":443/gdsM/manualV2Data.action" :
                DataDSUtils::KIA_URL . ":443/gdsM/manualV2Data.action";

            $requestBodyHex = $hex; // Chuỗi hex của request body
            $requestBodyHex  = str_replace(" ", "",  $requestBodyHex);
            $requestBody = hex2bin($requestBodyHex); //hàm này ra text
            // dd( $requestBody);
            $options = array(
                CURLOPT_URL            => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST           => true,
                CURLOPT_POSTFIELDS     => $requestBody,
                CURLOPT_HTTPHEADER     => array(
                    'Content-Type: application/x-www-form-urlencoded',
                    'Content-Length: ' . strlen($requestBody)
                )
            );
            $curl = curl_init();
            curl_setopt_array($curl, $options);
            $response = curl_exec($curl);

            // Kiểm tra nếu có lỗi
            if ($response === false) {
                dd('Curl error: ' . curl_error($curl));
            } else {
                dd('Response: ' . $response);
            }
            curl_close($curl);
            die();
        }

        function encodeAESString($plainText, $secretKey)
        {
            $key = DataDSUtils::Aes256key1;
            $iv = DataDSUtils::Aes256IV;
            $encrypted = openssl_encrypt($plainText, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);

            return  bin2hex($encrypted);
        }



        // Chuỗi cần mã hóa
        $plainText = '{"jsonRequest":"{"user_id":"guest"}"}';
        // Khóa AES
        $secretKey = "nfactor!planemo!nfactor!planemo!";
        // Mã hóa chuỗi và in ra kết quả
        $encodedHex = encodeAESString($plainText, $secretKey);


        $he = '0d da 14 47 80 12 21 11 44 ab b6 48 7c 67 30 8b 6b 06 6b 2f f7 21 54 89 9d 60 a6 21 4b 92 67 f3 d6 bf a2 5c 8a 94 47 4a 1a a3 a5 de cb 72 bd 05 ca fd 99 26 fc 3e 4a 54 a3 6b 43 a1 0f 0e d8 ea 2f 9a 8f 82 38 c2 48 78 c2 a0 e3 1c 94 1c 98 2c b3 d8 06 f0 d5 7b f4 29 6e 59 ca 5a b1 9c 2f 07 3a e0 9c 87 cb 2a 1d 08 28 4c ea d0 09 75 57 1b 3d fa 65 e9 63 26 2d ab 32 b7 f2 fd 09 9a a9 d5 a4 ef fb ac f0 83 4d d2 88 80 cc 91 25 d1 5d ec 62 11 18 32 70 0e 32 ec a7 a1 76 54 90 53 93 36 a5 fb 9f fb 37 77 10 14 94 65 f3 88 c8 ff d5 c3 2f 9a 8f 82 38 c2 48 78 c2 a0 e3 1c 94 1c 98 2c b3 d8 06 f0 d5 7b f4 29 6e 59 ca 5a b1 9c 2f 07 3a e0 9c 87 cb 2a 1d 08 28 4c ea d0 09 75 57 1b 3d fa 65 e9 63 26 2d ab 32 b7 f2 fd 09 9a a9 d5 a4 ef fb ac f0 83 4d d2 88 80 cc 91 25 d1 5d ec 98 c3 66 e9 b7 ef f1 d8 5b 9c f4 01 8e 25 66 54 bf 94 6d fb 4d fa f4 9a fc 90 7c da 82 fa ea 77 b3 ad f4 1b d1 8d 44 3f 2f cc fb 89 a5 62 74 8f c4 2b 38 94 b8 b8 4f 2c 2e 9c c7 03 35 1f 85 3f 39 57 09 08 70 3c c3 77 1c 7f 1b 2e f1 e1 f5 95 8a b8 e6 da dd c6 28 e4 f6 b8 22 c4 a3 f8 ac 84 20 21 fb 9a 2b 86 f7 35 98 2e 19 9a 21 d6 9f cc';
        $he = str_replace(" ", "", $he);
        //  dd(DataDSUtils::AES256Decode(base64_encode(hex2bin($he))));
        //  dd($encodedHex);
        requestFromHex('0d da 14 47 80 12 21 11 44 ab b6 48 7c 67 30 8b 6b 06 6b 2f f7 21 54 89 9d 60 a6 21 4b 92 67 f3 d6 bf a2 5c 8a 94 47 4a 1a a3 a5 de cb 72 bd 05 ca fd 99 26 fc 3e 4a 54 a3 6b 43 a1 0f 0e d8 ea 2f 9a 8f 82 38 c2 48 78 c2 a0 e3 1c 94 1c 98 2c b3 d8 06 f0 d5 7b f4 29 6e 59 ca 5a b1 9c 2f 07 3a e0 9c 87 cb 2a 1d 08 28 4c ea d0 09 75 57 1b 3d fa 65 e9 63 26 2d ab 32 b7 f2 fd 09 9a a9 d5 a4 ef fb ac f0 83 4d d2 88 80 cc 91 25 d1 5d ec 62 11 18 32 70 0e 32 ec a7 a1 76 54 90 53 93 36 a5 fb 9f fb 37 77 10 14 94 65 f3 88 c8 ff d5 c3 2f 9a 8f 82 38 c2 48 78 c2 a0 e3 1c 94 1c 98 2c b3 d8 06 f0 d5 7b f4 29 6e 59 ca 5a b1 9c 2f 07 3a e0 9c 87 cb 2a 1d 08 28 4c ea d0 09 75 57 1b 3d fa 65 e9 63 26 2d ab 32 b7 f2 fd 09 9a a9 d5 a4 ef fb ac f0 83 4d d2 88 80 cc 91 25 d1 5d ec 98 c3 66 e9 b7 ef f1 d8 5b 9c f4 01 8e 25 66 54 bf 94 6d fb 4d fa f4 9a fc 90 7c da 82 fa ea 77 b3 ad f4 1b d1 8d 44 3f 2f cc fb 89 a5 62 74 8f c4 2b 38 94 b8 b8 4f 2c 2e 9c c7 03 35 1f 85 3f 39 57 09 08 70 3c c3 77 1c 7f 1b 2e f1 e1 f5 95 8a b8 e6 da dd c6 28 e4 f6 b8 22 c4 a3 f8 ac 84 20 21 fb 9a 2b 86 f7 35 98 2e 19 9a 21 d6 9f cc');



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            //  'data' => $AES256Encodev
        ], 200);
    }

    /**
     * Lấy danh sách menu khi chọn xe
     * VEHICLE_TYPE      VD: Passenger, vehicletypecode
     * MFR_CODE      VD: HY, mfrcode
     * AREA      VD: HME, area
     * LANG_CODE      VD: ENG, langcode
     * MODEL_ID      VD: HCM12, modelcode
     * MODEL_YR      VD: 2022, modelyr
     * MMC_TYPE      VD: en, enginetype
     * MMC_ID      VD: 129, enginecode
     * ENGINE_CODE      VD: 129, enginecode
     * GROUP      VD: SHOP ETM  (các tab)
     */
    public function getMenuDocumentOfDTC(Request $request)
    {

        $MFR_CODE = $request->mfrcode;
        $AREA = $request->area;
        $MODEL_ID = $request->modelcode;
        $MODEL_YR = $request->modelyr;
        $ENGINE_CODE = $request->enginecode;

        $arrCheck = [
            ["MFR_CODE", "mfrcode"],
            ["AREA", "area"],
            ["MODEL_ID", "modelcode"],
            ["MODEL_YR", "modelyr"],
            ["ENGINE_CODE", "enginecode"],
        ];

        foreach ($arrCheck  as $itemCheck) {

            $paramRq = $itemCheck[1];
            if ($request->$paramRq === null) {

                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => $paramRq . ' not found',
                ], 400);
            }
        }
        $strDataSrc = 'DTC';
        $mfrcode = $MFR_CODE;
        $area = $AREA;
        $modelid = $MODEL_ID;
        $modelyr =  $MODEL_YR;
        $engine = $ENGINE_CODE;

        $dbPath = $request->carprovidertype == "hyundai" ? "sqliteHyundaiVehicle" : "sqliteKiaVehicle";
        $results =  DB::connection($dbPath)->select("SELECT DISTINCT 
                        VL.modeldesc,
                        VL.modelyr,
                        VM.sysitemtype, 
                        VM.syssubitemcode, 
                        VM.sysitemdesc, 
                        VM.syssubitemdesc 
                        FROM vehiclelist AS VL 
                        INNER JOIN vehicle_mmcinfo AS VM ON VL.id = VM.id
                        WHERE VM.provideinfo LIKE '%$strDataSrc%' 
                        AND VL.mfrcode='$mfrcode' 
                        AND VL.area='$area' 
                        AND VL.modelcode='$modelid'
                        AND VL.modelyr='$modelyr' 
                        AND VM.enginecode ='$engine' ");


        if (count($results) == 0) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'There is no data',
            ], 400);
        } else {
            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' =>  MsgCode::SUCCESS[1],
                'data' => $results
            ], 200);
        }
    }

    /**
     * Lấy danh sách menu khi chọn xe
     * VEHICLE_TYPE      VD: Passenger, vehicletypecode
     * MFR_CODE      VD: HY, mfrcode
     * AREA      VD: HME, area
     * LANG_CODE      VD: ENG, langcode
     * MODEL_ID      VD: HCM12, modelcode
     * MODEL_YR      VD: 2022, modelyr
     * MMC_TYPE      VD: en, enginetype
     * MMC_ID      VD: 129, enginecode
     * ENGINE_CODE      VD: 129, enginecode
     * GROUP      VD: SHOP ETM  (các tab)
     */
    public function getMenuDocument(Request $request)
    {


        $TYPE = "TOC";

        $VEHICLE_TYPE = $request->vehicletypecode;
        $MFR_CODE = $request->mfrcode;
        $AREA = $request->area;
        $LANG_CODE = $request->langcode;
        $MODEL_ID = $request->modelcode;
        $MODEL_YR = $request->modelyr;
        $MMC_TYPE = $request->sysitemtype ?? "all";
        $MMC_ID =  $request->syssubitemcode;
        $ENGINE_CODE = $request->enginecode;

        $GROUP = $request->group;

        $tailLink = $_SERVER["REQUEST_URI"];
        if ($GROUP == "SHOP" && str_contains($tailLink, "getMenuDocumentOfShop")) {
            $TYPE = "TOP_TOC";
        }

        $arrCheck = [
            ["VEHICLE_TYPE", "vehicletypecode"],
            ["MFR_CODE", "mfrcode"],
            ["AREA", "area"],
            ["LANG_CODE", "langcode"],
            ["MODEL_ID", "modelcode"],
            ["MODEL_YR", "modelyr"],
            // ["MMC_TYPE", "sysitemtype"],
            // ["MMC_ID", "syssubitemcode"],
            ["ENGINE_CODE", "enginecode"],
            ["GROUP", "group"],
        ];
        foreach ($arrCheck  as $itemCheck) {

            $paramRq = $itemCheck[1];
            if ($request->$paramRq === null) {

                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => $paramRq . ' not found',
                ], 400);
            }
        }


        $TOKEN = DataDSUtils::getTokenByInterface();
        $paramManualData = new ParamManualData();
        $paramManualData->TOKEN = $TOKEN;
        $paramManualData->TYPE = $TYPE;
        $paramManualData->VEHICLE_TYPE = $VEHICLE_TYPE;
        $paramManualData->MFR_CODE = $MFR_CODE;
        $paramManualData->AREA = $AREA;
        $paramManualData->LANG_CODE = $LANG_CODE;
        $paramManualData->MODEL_ID = $MODEL_ID;
        $paramManualData->MODEL_YR = $MODEL_YR;
        $paramManualData->MMC_TYPE = $MMC_TYPE ?? "all";
        $paramManualData->MMC_ID = $MMC_ID ?? $ENGINE_CODE;
        $paramManualData->ENGINE_CODE = $ENGINE_CODE;
        $paramManualData->GROUP = $GROUP;

        if ($GROUP == "SHOP" && str_contains($tailLink, "getMenuDocumentOfShop")) {

            $dbPath = $request->carprovidertype == "hyundai" ? "sqliteHyundaiVehicle" : "sqliteKiaVehicle";
            $results =  DB::connection($dbPath)->select("SELECT DISTINCT 
            VL.modeldesc,
            VL.modelyr,
            VM.sysitemtype, 
            VM.syssubitemcode, 
            VM.sysitemdesc, 
            VM.syssubitemdesc 
            FROM vehiclelist AS VL 
            INNER JOIN vehicle_mmcinfo AS 
            VM ON VL.id = VM.id
            WHERE  VL.mfrcode='$MFR_CODE' 
            AND VL.area='$AREA' 
            AND VL.modelcode='$MODEL_ID'
            AND VL.modelyr='$MODEL_YR' 
            AND VM.enginecode ='$ENGINE_CODE' ");
            $infosArray = array();

            foreach ($results as $item) {
                array_push($infosArray, $item->sysitemtype . "," . $item->syssubitemcode);
            }

            $paramManualData->MMC_INFO = 'all,0|' . implode("|", $infosArray);
            $paramManualData->MMC_TYPE = null;
            $paramManualData->MMC_ID = null;
        }

        if ($GROUP != 'ETM') {
            $paramManualData->ENGINE_CODE = "null";
        }


      
        $data = $this->getManualV2DataMain($paramManualData);
      

        if (isset($data['MANUALDATA'][0]['strValue'])) {

            $xmlString = $data['MANUALDATA'][0]['strValue'];
            $xml = simplexml_load_string($xmlString);
            $json = json_encode($xml);
            $array = json_decode($json, TRUE);

            if (isset($array['inode'])  && count($array['inode']) < 7) {
                $paramManualData->MMC_TYPE = "all";
                $paramManualData->MMC_ID = "0";

                $TOKEN = DataDSUtils::getTokenByInterface();
                $paramManualData->TOKEN = $TOKEN;
                $data2 = $this->getManualV2DataMain($paramManualData);

                if ($data2  != null && isset($data2['MANUALDATA'][0]['strValue'])) {
                    $xmlString2 = $data2['MANUALDATA'][0]['strValue'];

                    $xml2 = simplexml_load_string($xmlString2);
                    $json2 = json_encode($xml2);
                    $array2 = json_decode($json2, TRUE);
                    if (isset($array2['inode'])  && count($array2['inode']) > count($array['inode'])) {
                        $data = $data2;
                        $array = $array2;
                    }
                }
            };
        };

        if ($GROUP == "SHOP"  && str_contains($tailLink, "getMenuDocumentOfShop")) {
            try {

                $xmlString = $data['XMLDATA'];
                $xml = simplexml_load_string($xmlString);
                $json = json_encode($xml);
                $array = json_decode($json, TRUE);
                $arrayRTs = array();

                foreach ($array['infonode'] as $item) {

                    $att = $item['@attributes'];
                    array_push($arrayRTs, [
                        "desc" => $att['desc'],
                        "kindid" => $att['kindid'],
                        "mmcid" => $att['mmcid'],
                        "mmctype" => $att['mmctype'],
                    ]);
                }


                return response()->json([
                    'code' => 200,
                    'success' => true,
                    'msg_code' => MsgCode::SUCCESS[0],
                    'msg' => MsgCode::SUCCESS[1],
                    'data' => $arrayRTs
                ], 200);
            } catch (Exception $e) {

               
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => 'There is no data',
                ], 400);
            }
        }

        try {

          
            $xmlString = $data['MANUALDATA'][0]['strValue'];
            // $xml = Xml2Array::create($xmlString);

            $xml = simplexml_load_string($xmlString);
            $json = json_encode($xml);
            $array = json_decode($json, TRUE);

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => $array
            ], 200);
        } catch (Exception $e) {
          
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => 'There is no data',
            ], 400);
        }
    }

    /**
     * Lấy danh sách menu khi chọn xe
     * VEHICLE_TYPE      VD: Passenger, vehicletypecode
     * MFR_CODE      VD: HY, mfrcode
     * AREA      VD: HME, area
     * LANG_CODE      VD: ENG, langcode
     * MODEL_ID      VD: HCM12, modelcode
     * MODEL_YR      VD: 2022, modelyr
     * MMC_TYPE      VD: en, enginetype
     * MMC_ID      VD: 129, enginecode
     * ENGINE_CODE      VD: 129, enginecode
     * GROUP      VD: SHOP ETM  (các tab)
     */
    public function getTabsDocument(Request $request)
    {

        $TYPE = "CONTENTS";

        $arrCheck = [
            ["VEHICLE_TYPE", "vehicletypecode"],
            ["MFR_CODE", "mfrcode"],
            ["AREA", "area"],
            ["LANG_CODE", "langcode"],
            ["MODEL_ID", "modelcode"],
            ["MODEL_YR", "modelyr"],

            ["MMC_TYPE", "mmctype"],
            ["MMC_ID", "mmcid"],

            ["SC", "sc"],
            ["SCQ", "scq"],
            ["COMP_ID", "compid"],
            ["SIT_Q", "sitq"],
            ["SIT_SQ", "sitsq"],
            ["PCODE", "pcode"],
            ["REL_COMP", "relcomp"],
            ["SYMP_ID", "sympid"],
            ["STEP_NBR", "stepnbr"],
            ["REV_NO", "revno"],

            ["GROUP", "group"],
        ];
        foreach ($arrCheck  as $itemCheck) {

            $paramRq = $itemCheck[1];
            if ($request->$paramRq === null) {

                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => $paramRq . ' not found',
                ], 400);
            }
        }

        $VEHICLE_TYPE = $request->vehicletypecode;
        $MFR_CODE = $request->mfrcode;
        $AREA = $request->area;
        $LANG_CODE = $request->langcode;
        $MODEL_ID = $request->modelcode;
        $MODEL_YR = $request->modelyr;

        $MMC_TYPE = $request->mmctype ?? 'all'; //$request->enginetype;
        $MMC_ID =  $request->mmcid;

        $SC = $request->sc;
        $SCQ = $request->scq;
        $COMP_ID = $request->compid;
        $SIT_Q = $request->sitq;
        $SIT_SQ = $request->sitsq;
        $PCODE = $request->pcode;
        $REL_COMP = $request->relcomp;
        $SYMP_ID = $request->sympid;
        $STEP_NBR = $request->stepnbr;
        $REV_NO = $request->revno;

        $GROUP = $request->group;


        try {


            $TOKEN = DataDSUtils::getTokenByInterface();
            $paramManualData = new ParamManualData();
            $paramManualData->TOKEN = $TOKEN;
            $paramManualData->TYPE = $TYPE;
            $paramManualData->VEHICLE_TYPE = $VEHICLE_TYPE;
            $paramManualData->MFR_CODE = $MFR_CODE;
            $paramManualData->AREA = $AREA;
            $paramManualData->LANG_CODE = $LANG_CODE;
            $paramManualData->MODEL_ID = $MODEL_ID;
            $paramManualData->MODEL_YR = $MODEL_YR;
            $paramManualData->MMC_TYPE = $MMC_TYPE;
            $paramManualData->MMC_ID = $MMC_ID;
            $paramManualData->SC = $SC;
            $paramManualData->SCQ = $SCQ;
            $paramManualData->COMP_ID = $COMP_ID;
            $paramManualData->SIT_Q = $SIT_Q;
            $paramManualData->SIT_SQ = $SIT_SQ;
            $paramManualData->PCODE = $PCODE;
            $paramManualData->REL_COMP = $REL_COMP;
            $paramManualData->SYMP_ID = $SYMP_ID;
            $paramManualData->STEP_NBR = $STEP_NBR;
            $paramManualData->REV_NO = $REV_NO;
            $paramManualData->GROUP = $GROUP;


            $data = $this->getManualV2DataMain($paramManualData);

            if (isset($data['MANUALDATA']) && count($data['MANUALDATA']) == 0) {

                if ($paramManualData->TYPE == null || $paramManualData->TYPE == 'CONTENTS') {

                    $paramManualData->MMC_TYPE  = 'all';
                    $paramManualData->MMC_ID  = '0';
                    $data = $this->getManualV2DataMain($paramManualData);
                }
            }

            $xmlString = $data['MANUALDATA'][0]['strValue'];
        } catch (Exception $e) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Can't get tabs",
            ], 400);
        }


        $xml = simplexml_load_string($xmlString);
        $json = json_encode($xml);
        $array = json_decode($json, TRUE);

        $isOnlyImage = true;
        $servinfosub = "";


        if (substr_count($xmlString, "<table") > 0) {
            $isOnlyImage = false;
        } else
        if (!isset($array['servinfo']["p"])) {
            $isOnlyImage = false;
        } else
        if (!isset($array['servinfo']['img'])) {
            $isOnlyImage = false;
        } else
        if (is_string($array['servinfo']["p"])) {
            $isOnlyImage = true;
        } else {
            $isOnlyImage = false;
        }

        if ($isOnlyImage == true) {
            if (isset($array['servinfo']['img']) && count($array['servinfo']['img']) == 1) {
                $ob1 = $array['servinfo']['img'];
                $array['servinfo']['img'] = array();
                array_push($array['servinfo']['img'], $ob1);
            }

            $imageRTs = array();
            foreach ($array['servinfo']['img'] as $itemImg) {
                array_push($imageRTs, $itemImg['@attributes']);
            }

            return response()->json([
                'code' => 200,
                'success' => true,
                'msg_code' => MsgCode::SUCCESS[0],
                'msg' => MsgCode::SUCCESS[1],
                'data' => [
                    "is_only_image" => true,
                    "images" =>  $imageRTs,
                    "html" => ""
                ]
            ], 200);
        }

        $xmlContent =  $xmlString;
        $xsltPath = public_path('kdsgds/stylesheet.xsl');
        // Tạo một bộ xsl
        $xsl = new \XSLTProcessor();
        // Nạp stylesheet
        $xsl->importStylesheet(new \SimpleXMLElement(file_get_contents($xsltPath)));
        // Tải và xử lý XML
        $xml = new \DOMDocument();
        $xml->loadXML($xmlContent);
        // Chuyển đổi XML thành HTML bằng XSLT
        $html = $xsl->transformToXML($xml);
        function removeFileExtension($url)
        {
            $path_parts = pathinfo($url);
            if (isset($path_parts['extension'])) {
                $url = str_replace('.' . $path_parts['extension'], '', $url);
            }
            return $url;
        }

        function replaceImgSrc($text, $paramManualData)
        {
            $pattern = '/(<img[^>]+)src="?\'?([^"\'>]+)"?\'?([^>]*>)/';
            $text = preg_replace_callback($pattern, function ($matches) use ($paramManualData) {
                $tag = $matches[1];
                $src = $matches[2];
                $rest = $matches[3];

                if (strpos($src, 'http') !== false) {
                    return $tag . 'src="' . $src . '"' . $rest;
                } else {

                    $providerType = request()->carprovidertype;

                    $paramManualData->AREA = request()->carprovidertype == "hyundai" ?
                        "HME" :
                        "KME";

                    $domainCR = "https://main." . Helper::getDomainCurrentWithoutSubAndNoHttp();
                    $baseUrl = $domainCR .
                        "/api/user/kdsgds/$providerType/gdsM2/getFileFromPath?filePath=" .
                        "UPLOAD/data/" .
                        $paramManualData->VEHICLE_TYPE .
                        "/" .
                        $paramManualData->MFR_CODE .
                        "/" .
                        $paramManualData->AREA .
                        "/" .
                        $paramManualData->LANG_CODE .
                        "/" .
                        $paramManualData->GROUP .
                        "-IMAGES/" .
                        $paramManualData->MFR_CODE .
                        "-" .
                        strtoupper($paramManualData->MODEL_ID) .
                        "-IMAGES-" .
                        $paramManualData->LANG_CODE .
                        "/" .
                        $src;

                    $src = removeFileExtension($baseUrl);
                    return $tag . 'src="' . $src . '"' . $rest;
                }
            }, $text);
            return $text;
        }

        $html = replaceImgSrc($html, $paramManualData);

        if (!str_contains($html, '<body>')) {
            $html = '
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Load SVG File</title>
            </head>
            <body>'
                . $html .
                '</body>
            </html>
            ';
        }

        function replaceImgWithTagObject($html)
        {
            $pattern = '/<img([^>]*)>/i';
            $html = preg_replace_callback($pattern, function ($matches) {
                $attributes = $matches[1];
                $attributes_array = [];
                preg_match_all('/(\w+)\s*=\s*([\'"])(.*?)\2/', $attributes, $matches_attributes, PREG_SET_ORDER);
                foreach ($matches_attributes as $attribute) {
                    $attributes_array[$attribute[1]] = $attribute[3];
                }
                $data_attribute = 'src="' . $attributes_array['src'] . '"';
                $object_tag = '<img id="svgObject" ' . $data_attribute . ' ';
                foreach ($attributes_array as $key => $value) {
                    if ($key !== 'src') {
                        $object_tag .= $key . '="' . $value . '" ';
                    }
                }
                $object_tag .= '>';
                $object_tag .= '</img>';

                return $object_tag;
            }, $html);

            return $html;
        }

        $html = replaceImgWithTagObject($html);

        $dataInfoSub['html'] =  $html;

        if ($request->method() == 'GET') {
            echo $html;
            die();
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => [
                "is_only_image" => false,
                "images" => array(),
                "html" => $html,
            ]
        ], 200);
    }
}
