<?php

namespace App\Http\Controllers\Api\User\KdsGds;

use App\Helper\DataDSUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Midnite81\Xml2Array\Xml2Array;


/**
 * @group  User/Đăng nhập
 */
class DataFileController extends Controller
{

    public function getInfoFilePart(Request $request)
    {

        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }

        $arrCheck = [
            ["VEHICLE_TYPE", "vehicletypecode"],
            ["MFR_CODE", "mfrcode"],
            ["AREA", "area"],
            ["LANG_CODE", "langcode"],
            ["MODEL_ID", "modelcode"],
            ["MODEL_YR", "modelyr"]
        ];

        foreach ($arrCheck  as $itemCheck) {

            $paramRq = $itemCheck[1];
            if ($request->$paramRq === null) {

                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ERROR[0],
                    'msg' => $paramRq . ' not found',
                ], 400);
            }
        }


        $VEHICLE_TYPE = $request->vehicletypecode;
        $MFR_CODE = $request->mfrcode;
        $AREA = $request->area;
        $LANG_CODE = $request->langcode;
        $MODEL_ID = strtoupper($request->modelcode);
        $MODEL_YR = $request->modelyr;

        $MODEL_ID_LOW = strtolower($MODEL_ID);
        $MODEL_YR_LOW = strtolower($MODEL_YR);


        $AREA = request()->carprovidertype == "hyundai" ?
            "HME" :
            "KME";

        $link = request()->carprovidertype == "hyundai" ?
            DataDSUtils::HYUNDAI_URL . "/UPLOAD/data/$VEHICLE_TYPE/$MFR_CODE/$AREA/$LANG_CODE/ETM-IMAGES/$MFR_CODE-$MODEL_ID-IMAGES-$LANG_CODE/linkinfo-$MODEL_ID_LOW-$MODEL_YR_LOW.xml" :
            DataDSUtils::KIA_URL . "/UPLOAD/data/$VEHICLE_TYPE/$MFR_CODE/$AREA/$LANG_CODE/ETM-IMAGES/$MFR_CODE-$MODEL_ID-IMAGES-$LANG_CODE/linkinfo-$MODEL_ID_LOW-$MODEL_YR_LOW.xml";

        $headerArr = [
            'Accept'                    => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language'           => 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5,zh-CN;q=0.4,zh;q=0.3,ca;q=0.2,ko;q=0.1,de;q=0.1,ar;q=0.1,hi;q=0.1',
            'Cache-Control'             => 'max-age=0',
            'Connection'                => 'keep-alive',
            'DNT'                       => '1',
            'Upgrade-Insecure-Requests' => '1',
            'User-Agent'                => 'Dalvik/2.1.0 (Linux; U; Android 9; SM-N976N Build/PQ3A.190605.12201529)',
        ];

        $client = new \GuzzleHttp\Client();

        $response1 = $client->request(
            "GET",
            $link,
            [
                // 'cookies' => $jar,
                'headers' => $headerArr,
                'timeout' => 90, // Response timeout
                'connect_timeout' => 90, // Connection timeout,
            ]
        );
        $content = $response1->getBody()->getContents();

        $xml = simplexml_load_string($content);
        $json = json_encode($xml);
        $array = json_decode($json, TRUE);

        return response()->json([
            'code' => 200,
            'success' => true,
            'data' =>  $array,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Kiem tra han su dung
     * @bodyParam username string required (Username, email hoặc số điện thoại)
     * @bodyParam password string required Password
     */
    public function getFileFromPath(Request $request)
    {


        $headers_requests = [];
        if ($request != null) {
            foreach ($request->headers->all() as $key => $value) {
                if ($value != null && is_array($value) && count($value) > 0) {
                    $headers_requests[$key] = $value[0];
                }
            }
        }


        $filePath = $request->filePath;

        $link = request()->carprovidertype == "hyundai" ?
            DataDSUtils::HYUNDAI_URL . "/" . $filePath :
            DataDSUtils::KIA_URL . "/" . $filePath;

        if (request()->carprovidertype == "kia") {
            $link = str_replace("/HME/", '/KME/', $link);
        }
      
        $headerArr = [
            'Accept'                    => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language'           => 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5,zh-CN;q=0.4,zh;q=0.3,ca;q=0.2,ko;q=0.1,de;q=0.1,ar;q=0.1,hi;q=0.1',
            'Cache-Control'             => 'max-age=0',
            'Connection'                => 'keep-alive',
            'DNT'                       => '1',
            'Upgrade-Insecure-Requests' => '1',
            'User-Agent'                => 'Dalvik/2.1.0 (Linux; U; Android 9; SM-N976N Build/PQ3A.190605.12201529)',
        ];

        function removeExt($text)
        {
            $viTriCham = strrpos($text, '.');
            if ($viTriCham !== false && $viTriCham === strlen($text) - 4) {
                return substr($text, 0, $viTriCham);
            } else {
                return $text;
            }
        }

        $linkFileNoExt = request()->carprovidertype == "hyundai" ?
            DataDSUtils::HYUNDAI_URL  . "/" . (removeExt($filePath)) :
            DataDSUtils::KIA_URL  . "/" . (removeExt($filePath));

        $linkFileNoExt = str_replace("/UPLOAD", "",  $linkFileNoExt);

        $USER_ID = DataDSUtils::getUserID();
        $getAppendImgExt = DataDSUtils::AESEncrypt("TYPE=AppendImgExt&USER_ID=$USER_ID&URL=$linkFileNoExt");

        $url = request()->carprovidertype == "hyundai" ?
            DataDSUtils::HYUNDAI_URL . ":443/gdsM/manualV2Data.action" :
            DataDSUtils::KIA_URL . ":443/gdsM/manualV2Data.action";


      
        $client = new Client();
        $response = $client->post($url, [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'body' =>  $getAppendImgExt
        ]);

        $responseFileInfo = $response->getBody()->getContents();
    

        $decodeJson = json_decode($responseFileInfo, true);
        $linkFile = ($decodeJson['FILEINFO']);

        $client = new \GuzzleHttp\Client();
        $arrExts = [$linkFile, '.svg', '.gif', '.png'];


        $index = 0;
        foreach ($arrExts as $ex) {
            if ($index == 0) {
                $link = $ex;
            } else {
                $link = removeExt($link) . $ex;
            }
            if (request()->carprovidertype == "kia") {
                $link = str_replace("/HME/", '/KME/', $link);
            }
            $index++;

            $response1 = $client->request(
                "GET",
                $link,
                [
                    // 'cookies' => $jar,
                    'headers' => $headerArr,
                    'timeout' => 90, // Response timeout
                    'connect_timeout' => 90, // Connection timeout,
                ]
            );

            $content = $response1->getBody()->getContents();

            if (str_contains($content, 'Wrong Access')) {

                continue;
            } else {

                function modifyHtmlText($html)
                {
                    // Sử dụng regex để tìm và thay thế các mẫu tương ứng
                    $pattern = '/xlink:href="javascript:([^"]+)"/i';
                    $modifiedHtml = preg_replace_callback($pattern, function ($matches) {
                        $ctBase64 = base64_encode("javascript:$matches[1]");
                        $onclick = "window.parent.postMessage('$ctBase64')";
                        return "xlink:href=\"javascript:$matches[1]\" onclick=\"$onclick\"";
                    }, $html);

                    return $modifiedHtml;
                }


                $ContentType  =  $response1->getHeader('Content-Type')[0] ?? "";

                if (str_contains($ContentType, "svg")) {
                    $content = modifyHtmlText($content);
                }

                return response($content)->header('Content-Type',  $ContentType)->header('X-Frame-Options', 'allow-from *');
                break;
                die();
            }
        }
    }
}
