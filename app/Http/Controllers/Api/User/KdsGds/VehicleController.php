<?php

namespace App\Http\Controllers\Api\User\KdsGds;

use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\vehiclelist;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/**
 * @group  User/Đăng nhập
 */
class VehicleController extends Controller
{



    /**
     * Kiem tra han su dung
     * @bodyParam username string required (Username, email hoặc số điện thoại)
     * @bodyParam password string required Password
     */
    public function getVehicle(Request $request)
    {

        $dbPath = $request->carprovidertype == "hyundai" ? "sqliteHyundaiVehicle" : "sqliteKiaVehicle";

        $data = vehiclelist::on($dbPath)->get();
        try {
            $results = DB::connection($dbPath)->select("SELECT DISTINCT VL.modeldesc,
             VL.modelyr, 
             VM.enginedesc, 
             VL.modelcode, 
             VM.enginecode, 
             VL.geozonevincode, 
             VL.modelvincode, 
             VL.modelsubvincode, 
             VL.modelyrvincode, 
             VM.enginevincode, 
             VM.enginetype, 
             VL.mfrcode , 
             VL.area , 
             VL.langcode , 
             VL.vehicletypecode , 
             VL.vehicletypevincode
        FROM vehiclelist AS VL
        INNER JOIN vehicle_mmcinfo AS VM ON VL.id = VM.id
        ORDER BY VL.modelyr DESC, VL.modeldesc ");

            $data = [];
            foreach ($results as $item) {

                if (!isset($data[$item->modelcode])) {
                    $data[$item->modelcode] = [
                        'name' => $item->modeldesc,
                        'code' => $item->modelcode,
                        'items' => array()
                    ];
                }

                array_push($data[$item->modelcode]['items'], $item);
            }
        } catch (Exception $e) {
            dd($e);
        }

        $data =  array_values($data);
        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $data
        ], 200);
    }

    /**
     * Search VIN
     * @bodyParam username string required (Username, email hoặc số điện thoại)
     * @bodyParam password string required Password
     */
    public function searchVin(Request $request)
    {


        $obj = strtoupper($request->vin_code ?? "");
        $_strArea =  $request->area ?? "";

        if (strlen($obj) != 17) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "INVALID VINCODE",
            ], 400);
        }

        $substring = "";
        $substring3 = substr($obj, 0, 1);
        $substring4 = substr($obj, 2, 1);
        $substring5 = substr($obj, 3, 1);
        $substring6 = substr($obj, 9, 1);

        // if (GDSMModelManager.GetInstance().GetConfigurationData().GetCurrentCVType().equals("CV")) {
        //     if (this._strArea.startsWith("H") || this._strArea.startsWith("G")) {
        //         $substring = obj.substring(4, 1);
        //     } else {
        //         $substring = obj.substring(5, 1);
        //     }
        // } else {
        $substring = substr($obj, 4, 1);
        // }

        if ($_strArea === "HBHMC" || $_strArea === "HCHN" || $_strArea === "GME" || $_strArea === "HME") {
            if ($substring3 === "L") {
                $substring2 = substr($obj, 6, 1);
            } else {
                $substring2 = substr($obj, 7, 1);
            }
        } else {
            $substring2 = substr($obj, 7, 1);
        }

        if (($_strArea === "KMA" || $_strArea === "KCI" || $_strArea === "KMU") && $substring5 === "J" && ($substring === "H5" || $substring === "H7") && $substring6 === 8) {
            $substring6 = "9";
        }

        $str3 = "SELECT DISTINCT VL.modeldesc,VL.modelyr,VM.enginedesc,VL.modelcode,VM.enginecode, VL.evgroup";


        $str4 = ((((((($str3 . " FROM vehiclelist AS VL ") .
            "INNER JOIN vehicle_mmcinfo AS VM ") .
            "ON VL.id = VM.id ") .
            "WHERE VL.geozonevincode LIKE '%" . $substring3
            . "%' ")
            . "  AND VL.modelvincode LIKE '%"
            . $substring5 . "%' ")
            . "  AND VL.modelsubvincode LIKE '%"
            . $substring . "%' ")
            . "  AND VL.modelyrvincode='"
            . $substring6 . "' ")
            . "  AND VM.enginevincode LIKE '%"
            . $substring2 . "%' ";

         

        $dbPath = $request->carprovidertype == "hyundai" ? "sqliteHyundaiVehicle" : "sqliteKiaVehicle";
        $results = DB::connection($dbPath)->select($str4);

        if (count($results) > 0) {
            $data =  $results[0];
        } else {

            $data = null;
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $data
        ], 200);
    }
}
