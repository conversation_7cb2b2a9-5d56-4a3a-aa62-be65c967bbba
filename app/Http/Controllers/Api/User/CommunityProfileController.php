<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\CommunityPost;
use App\Models\MsgCode;
use App\Models\User;
use App\Models\UserFriend;
use App\Models\FriendRequest;
use Illuminate\Http\Request;


/**
 * @group  User/Thông tin 1 người trong cộng đồng
 */

class CommunityProfileController extends Controller
{

    /**
     * Thông tin tổng quan
     * 
     * @urlParam user_id required Nếu là user id
     * 
     */
    public function getInfoOverview(Request $request)
    {
        $user_id = request('user_id');

        $userExists = User::where('id',   $user_id)->first();
        if ($userExists  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[1],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $total_images = 0;
        $total_friends = UserFriend::where('user_id',  $request->user->id)
            ->count();

        $sent_friend_request = FriendRequest::where('user_id',  $request->user->id)
            ->where('to_user_id',    $user_id)
            ->first() != null;

        $is_friend =  UserFriend::where('user_id',  $request->user->id)
            ->where('friend_user_id',    $user_id)
            ->first() != null;


        unset($userExists->email);
        unset($userExists->username);
        unset($userExists->email_verified_at);
        unset($userExists->is_admin);
        unset($userExists->is_user_ios);
        unset($userExists->is_user_apple);
        unset($userExists->is_block);
        unset($userExists->ip_using);
        unset($userExists->last_ip_admin_using);

        unset($userExists->ip_register);
        unset($userExists->platform);
        unset($userExists->device_id);
        unset($userExists->app_version);
        unset($userExists->model);
        unset($userExists->expiry_alldata);

        unset($userExists->expiry_autodata);
        unset($userExists->expiry_autodata_italy);
        unset($userExists->expiry_haynespro);
        unset($userExists->expiry_haynespro_truck);
        unset($userExists->expiry_tecdoc);
        unset($userExists->expiry_identifix);
        unset($userExists->expiry_ford_pts);
        unset($userExists->expiry_toyota_tis);
        unset($userExists->expiry_mitchell_repair_center);
        unset($userExists->expiry_mitchell_prodemand);
        unset($userExists->expiry_partslink24);
        unset($userExists->expiry_kdsgds);
        unset($userExists->allow_mobile);
        unset($userExists->allow_web);


        unset($userExists->address_detail);
        unset($userExists->admin_lv);
        unset($userExists->last_visit_time_alldata);
        unset($userExists->last_visit_time_autodata);
        unset($userExists->last_visit_time_autodata_italy);
        unset($userExists->last_visit_time_haynespro);
        unset($userExists->last_visit_time_tecdoc);
        unset($userExists->last_visit_time_identifix);
        unset($userExists->last_visit_time_mitchell_repair_center);
        unset($userExists->last_visit_time_mitchell_prodemand);
        unset($userExists->last_visit_time_haynespro_truck);
        unset($userExists->last_visit_time_partslink24);
        unset($userExists->last_visit_time_kdsgds);
        unset($userExists->last_visit_time_ford_pts);
        unset($userExists->last_visit_time_toyota_tis);


        unset($userExists->of_agency_id);
        unset($userExists->note);
        unset($userExists->date_of_birth);
        unset($userExists->sex);

        unset($userExists->created_at);
        unset($userExists->updated_at);

        unset($userExists->area_code);
        
        $data = [
            'total_friends' => $total_friends,
            'total_images' => $total_images,
            'sent_friend_request' =>  $sent_friend_request,
            'is_friend' =>  $is_friend,
            'user' =>  $userExists
        ];


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[1],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $data
        ], 200);
    }
}
