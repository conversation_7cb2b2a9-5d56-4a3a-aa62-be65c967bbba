<?php

namespace App\Http\Controllers\Api\User;

use App\Events\RedisChatEventUserToUser;
use App\Helper\ChatUtils;
use App\Helper\Helper;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationUserJob;
use App\Models\GroupMember;
use App\Models\UToUMessage;
use App\Models\User;
use App\Models\MsgCode;
use App\Models\PersonChat;
use Mpbarlow\LaravelQueueDebouncer\Facade\Debouncer;

/**
 * @group  User/Chat
 */

class UserMessageController extends Controller
{

    /**
     * Danh sách người chat với user
     * 
     */
    public function getAllPerson(Request $request)
    {

        $all = PersonChat::where('user_id', $request->user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }


    /**
     * Xóa chat với 1 người
     * 
     */
    public function removeChat(Request $request)
    {

        $vs_user_id = $request->route()->parameter('vs_user_id');

        PersonChat::where('user_id', $request->user->id)->where('vs_user_id', $vs_user_id)->delete();
        PersonChat::where('user_id', $vs_user_id)->where('vs_user_id', $request->user->id)->delete();

        ChatUtils::sendListSocket($request->user->id, ChatUtils::LIST_USER_CHAT);
        ChatUtils::sendListSocket($vs_user_id, ChatUtils::LIST_USER_CHAT);

        UToUMessage::where('user_id', $request->user->id)->where('vs_user_id', $vs_user_id)->update([
            'is_remove' => true
        ]);
        UToUMessage::where('user_id', $vs_user_id)->where('vs_user_id', $request->user->id)->update([
            'is_remove' => true
        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Danh sách tin nhắn với 1 người
     * @bodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * 
     * 
     */
    public function getAllMessage(Request $request)
    {
        $vs_user_id = $request->route()->parameter('vs_user_id');

        $user = User::where('id',   $vs_user_id)->first();

        if ($user == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }


        $last = PersonChat::where('user_id', $request->user->id)
            ->where('vs_user_id', $vs_user_id)->first();

        if ($last  != null) {
            $last->update([
                'seen' => true,
            ]);
        }

        $all = UToUMessage::where('user_id', $request->user->id)->where('vs_user_id', $vs_user_id)
            ->orderBy('created_at', 'desc')
            ->where('is_remove', false)
            ->paginate(20);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }

    /**
     * Thu hồi tin nhắn
     * @bodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * 
     * 
     */
    public function recallMessage(Request $request)
    {
        $vs_user_id = $request->route()->parameter('vs_user_id');
        $mess_id = $request->route()->parameter('mess_id');

        $oMax = UToUMessage::where('vs_user_id', $vs_user_id)->orderBy('created_at', 'desc')->first();

        $mess =    UToUMessage::where('vs_user_id', $vs_user_id)
            ->where('user_id', $request->user->id)
            ->where('id',  $mess_id)
            ->first();


        $mess2 =  UToUMessage::where('vs_user_id',  $request->user->id)
            ->where('user_id', $vs_user_id)
            ->where('id',  $mess_id . "2")
            ->first();

        if ($mess2  != null) {
            event($e = new RedisChatEventUserToUser($mess2->toArray(), ChatUtils::TYPE_CHAT_RECALL));
            $mess2->delete();
        }


        if ($mess  != null) {
            event($e = new RedisChatEventUserToUser($mess, ChatUtils::TYPE_CHAT_RECALL));
            $mess->delete();
            if ($oMax  != null &&   $oMax->id == $mess_id) {
                $messLass = UToUMessage::where('vs_user_id', $vs_user_id)->orderBy('created_at', 'desc')->first();
                if ($messLass  !== null) {


                    $lass = PersonChat::where('user_id', $vs_user_id)->where('vs_user_id', $request->user->id)->first();

                    if ($lass  != null) {
                        $lass->update([
                            "last_mess" => $messLass->content,
                        ]);
                    }

                    ChatUtils::sendListSocket($request->user->id, ChatUtils::LIST_USER_CHAT);
                    ChatUtils::sendListSocket($vs_user_id, ChatUtils::LIST_USER_CHAT);
                }
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }

    /**
     * Gửi tin nhắn
     * 
     * @bodyParam content required Nội dung
     * @bodyParam images required List danh sách ảnh sp (VD: ["linl1", "link2"])
     * Khách nhận tin nhắn reatime khai báo io socket port 6441 nhận 
     * var socket = io("http://localhost:6441")
     * socket.on("chat:message2_from_user_to_user:1:2", function(data) {   (1:2   1 là từ user nào gửi tới cusotmer nào nếu đang cần nhận thì 1 là người cần nhận 2 là id của bạn)
     *   console.log(data)
     *   })
     * chat:message:1   với 1 là user_id
     * 
     * 
     * 
     */
    public function sendMessage(Request $request)
    {
        $vs_user_id = $request->route()->parameter('vs_user_id');

        $user = User::where('id',   $vs_user_id)->first();

        if ($user == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }


        if ($request->images == null && empty($request->content)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::CONTENT_IS_REQUIRED[0],
                'msg' => MsgCode::CONTENT_IS_REQUIRED[1],
            ], 400);
        }

        if (empty($request->id) || UToUMessage::where('id', $request->id)->first()  != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Id không hợp lệ",
            ], 400);
        }


        //data nguoi gui
        $mess = new UToUMessage([
            "id" => ($request->id),
            "user_id" => $request->user->id,
            "vs_user_id" =>  $vs_user_id,
            "content" => $request->content,
            'is_sender' => true,
            'images_json' => json_encode($request->images),
        ]);

        //data nguoi nhan
        $mess_receiver = new UToUMessage([
            'id' => ($request->id) . "2",
            "user_id" =>  $vs_user_id,
            "vs_user_id" => $request->user->id,
            "content" => $request->content,
            'is_sender' => false,
            'images_json' => json_encode($request->images),
        ]);



        //socket này là của người nhận chứ người gửi ko cần nhận socket
        if ($request->user->id != $vs_user_id) {
            event($e = new RedisChatEventUserToUser($mess->toArray(), ChatUtils::TYPE_CHAT_0));
        }

        //thêm mess cho người gửi
        $mess->save();

        PushNotificationUserJob::dispatch(
            $vs_user_id,
            $request->user->name,
            substr($request->content, 0, 80),
        );


        $personChat =  PersonChat::where('user_id', $request->user->id)
            ->where('vs_user_id', $vs_user_id)->first();

        if (
            $personChat != null
        ) {
            $personChat->update([
                "user_id" => $request->user->id,
                "vs_user_id" => $vs_user_id,
                "last_mess" => $request->content,
                'seen' => true,
                "created_at" => Helper::getTimeNowString(),
                'updated_at' => Helper::getTimeNowString(),
            ]);
        } else {
            PersonChat::create([
                "user_id" => $request->user->id,
                "vs_user_id" => $vs_user_id,
                "last_mess" => $request->content,
                'seen' => true,
            ]);
        }
        ChatUtils::sendListSocket($request->user->id, ChatUtils::LIST_USER_CHAT);


        if ($request->user->id != $vs_user_id) {
            //thêm mess cho người nhận
            $mess_receiver->save();
        }

        $per =  PersonChat::where('user_id', $vs_user_id)->where('vs_user_id', $request->user->id)->first();
        if ($per != null) {
            $per->update([
                "user_id" =>  $vs_user_id,
                "vs_user_id" => $request->user->id,
                "last_mess" => $request->content,
                'seen' => false,
                "created_at" => Helper::getTimeNowString(),
                'updated_at' => Helper::getTimeNowString(),
            ]);
        } else {
            PersonChat::create([
                "user_id" =>  $vs_user_id,
                "vs_user_id" => $request->user->id,
                "last_mess" => $request->content,
                'seen' => false,
            ]);
        }

        ChatUtils::sendListSocket($vs_user_id, ChatUtils::LIST_USER_CHAT);


        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>   $mess
        ], 200);
    }
}
