<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\PlanUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\PlanService;
use App\Models\RenewalHistory;
use Illuminate\Http\Request;


/**
 * @group  User/Lịch sử gia hạn
 */

class RenewalHistoriesController extends Controller
{


    /**
     * Danh sách lịch sử
     * 
     */
    public function getAll(Request $request)
    {
        $all = RenewalHistory::where('user_id', $request->user->id)->orderBy('id', 'desc')->paginate(20);

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>  $all
        ], 200);
    }
}
