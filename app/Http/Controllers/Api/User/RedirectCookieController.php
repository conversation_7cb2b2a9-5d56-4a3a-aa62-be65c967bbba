<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\Helper;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Exception;
use Illuminate\Http\Request;

/**
 * @group  User/Chuyển hướng và setCookie
 */
class RedirectCookieController extends Controller
{

    /**
     * Tạo L<PERSON>y thông tin profile
     */
    public function redirect(Request $request)
    {
        $link = base64_decode(request('link'));
        $code = request('code');
        $domain = Helper::getDomainCurrentWithoutSubAndNoHttp();

        try {
            $base64 = $code;
            $json = (base64_decode($base64));

            $arr = json_decode($json);
            foreach ($arr as $key => $value) {
                setcookie($key, $value, time() + 24 * 3600, "/", ".$domain");
            }
        } catch (Exception $e) {
        };
        header('Location: ' . $link);
        die();
    }
}
