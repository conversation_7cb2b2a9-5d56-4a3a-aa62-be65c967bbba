<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\MsgCode;
use Illuminate\Http\Request;

    /**
    * @group  User/<PERSON>h mục sản phẩm
    */
class CategoryController extends Controller
{

    /**
	* <PERSON>h sách danh mục sản phẩm
	*/
    public function getAll(Request $request)
    {

        $categories = Category::orderBy('position', 'ASC')->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $categories,
        ], 200);
    }
}
