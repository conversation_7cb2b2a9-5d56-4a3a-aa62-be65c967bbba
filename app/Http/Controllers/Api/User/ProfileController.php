<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\Helper;
use App\Helper\Place;
use App\Helper\StringUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use Illuminate\Http\Request;

/**
 * @group  Ussr/Thông tin cá nhân
 */
class ProfileController extends Controller
{

    /**
     * Tạo Lấy thông tin profile
     */
    public function getProfile(Request $request)
    {
        return response()->json([
            'code' => 200,
            'success' => true,
            'data' => $request->user,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }


    /**
     * Cập nhật thông tin profile
     * @bodyParam name String   Họ và tên
     * @bodyParam date_of_birth string ngày sinh
     * @bodyParam address_detail string địa chỉ
     * @bodyParam phone_number string số điện thoại
     * @bodyParam avatar_image string  ảnh đại diện
     */
    public function updateProfile(Request $request)
    {

    
        $request->user->update([
            'name' =>  $request->name,
            'name_str_filter' => StringUtils::convert_name_lowcase($request->name),

            "sex" =>  $request->sex,
            'date_of_birth' =>  $request->date_of_birth,
            "address_detail" =>  $request->address_detail,
            'avatar_image' =>  $request->avatar_image,

        ]);

        return response()->json([
            'code' => 200,
            'success' => true,
            'data' => $request->user,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
