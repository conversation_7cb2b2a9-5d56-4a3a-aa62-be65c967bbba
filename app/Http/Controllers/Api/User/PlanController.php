<?php

namespace App\Http\Controllers\Api\User;

use App\Helper\IPUtils;
use App\Helper\PlanUtils;
use App\Http\Controllers\Controller;
use App\Models\Agency;
use App\Models\MsgCode;
use App\Models\PlanService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use <PERSON>bauman\Location\Facades\Location;

/**
 * @group  User/Gói
 */

class PlanController extends Controller
{


    /**
     * Danh sách gói
     * 
     */
    public function allPlans(Request $request)
    {
        $platform = request()->header('platform-app-local') ?? $cookies['platform-app-local'] ??  $cookies['dr-platform'] ?? null;

        $expiry_alldata = $request->user != null ? $request->user->expiry_alldata : null;
        $expiry_autodata = $request->user != null ? $request->user->expiry_autodata : null;
        $expiry_autodata_italy = $request->user != null ? $request->user->expiry_autodata_italy : null;
        $expiry_haynespro = $request->user != null ? $request->user->expiry_haynespro : null;
        $expiry_haynespro_truck = $request->user != null ? $request->user->expiry_haynespro_truck : null;
        $expiry_tecdoc = $request->user != null ? $request->user->expiry_tecdoc : null;
        $expiry_identifix = $request->user != null ? $request->user->expiry_identifix : null;
        $expiry_mitchell_prodemand = $request->user != null ? $request->user->expiry_mitchell_prodemand : null;
        $expiry_partslink24 = $request->user != null ? $request->user->expiry_partslink24 : null;
        $expiry_kdsgds = $request->user != null ? $request->user->expiry_kdsgds : null;
        $expiry_ford_pts = $request->user != null ? $request->user->expiry_ford_pts : null;
        $expiry_toyota_tis = $request->user != null ? $request->user->expiry_toyota_tis : null;

        if (filter_var(request("pay_in_link"), FILTER_VALIDATE_BOOLEAN) == true) {
            $alldatavsautodatavshaynespro = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO)->where('pay_in_link', true)->where('public', true)->orderBy('price', 'asc')->get();
            //  $alldatavsautodata = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATA)->where('pay_in_link', true)->where('public', true)->orderBy('price', 'asc')->get();
            $alldatas = PlanService::where('service', PlanUtils::ALLDATA)->where('pay_in_link', true)->orderBy('price', 'asc')->get();
            $autodatas = PlanService::where('service', PlanUtils::AUTODATA)->where('pay_in_link', true)->orderBy('price', 'asc')->get();
            $haynespros = PlanService::where('service', PlanUtils::HAYNESPRO)->where('pay_in_link', true)->orderBy('price', 'asc')->get();

            $haynesprotrucks = PlanService::where('service', PlanUtils::HAYNESPRO_TRUCK)->where('pay_in_link', true)->orderBy('price', 'asc')->get();
            //$tecdocs = PlanService::where('service', PlanUtils::TECDOC)->where('pay_in_link', true)->orderBy('price', 'asc')->get();

            $partslink24s = PlanService::where('service', PlanUtils::PARTSLINK24)->where('pay_in_link', true)->orderBy('price', 'asc')->get();

            $identifixs = PlanService::where('service', PlanUtils::IDENTIFIX)->where('pay_in_link', true)->orderBy('price', 'asc')->get();

            $fordtis = PlanService::where('service', PlanUtils::FORD_PTS)->where('pay_in_link', true)->orderBy('price', 'asc')->get();

            $mitchellprodemand = PlanService::where('service', PlanUtils::MITCHELL_PRODEMAND)->where('pay_in_link', true)->orderBy('price', 'asc')->get();
            $mitchellrepaircenter = PlanService::where('service', PlanUtils::MITCHELL_REPAIR_CENTER)->where('pay_in_link', true)->orderBy('price', 'asc')->get();

            $kdsgds = PlanService::where('service', PlanUtils::KDSGDS)->where('pay_in_link', true)->orderBy('price', 'asc')->get();

            $toyotatiss = PlanService::where('service', PlanUtils::TOYOTA_TIS)->where('pay_in_link', true)->orderBy('price', 'asc')->get();
        } else
        if ($platform  == 'web') {
            $alldatavsautodatavshaynespro = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            //  $alldatavsautodata = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATA)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            $alldatas = PlanService::where('service', PlanUtils::ALLDATA)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            $autodatas = PlanService::where('service', PlanUtils::AUTODATA)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            $haynespros = PlanService::where('service', PlanUtils::HAYNESPRO)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();

            $haynesprotrucks = PlanService::where('service', PlanUtils::HAYNESPRO_TRUCK)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            //  $tecdocs = PlanService::where('service', PlanUtils::TECDOC)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            $identifixs = PlanService::where('service', PlanUtils::IDENTIFIX)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();

            $fordtis = PlanService::where('service', PlanUtils::FORD_PTS)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            $toyotatiss = PlanService::where('service', PlanUtils::TOYOTA_TIS)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            $mitchellprodemand = PlanService::where('service', PlanUtils::MITCHELL_PRODEMAND)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            // $mitchellrepaircenter = PlanService::where('service', PlanUtils::MITCHELL_REPAIR_CENTER)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
            $partslink24s = PlanService::where('service', PlanUtils::PARTSLINK24)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();

            $kdsgds = PlanService::where('service', PlanUtils::KDSGDS)->where('apply', 'WEB')->where('public', true)->orderBy('price', 'asc')->get();
        } else
        if ($platform  == 'android') {
            $alldatavsautodatavshaynespro = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO)->where('apply', 'ANDROID')->where('public', true)->orderBy('price', 'asc')->get();
            //  $alldatavsautodata = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATA)->where('apply', 'ANDROID')->where('public', true)->orderBy('price', 'asc')->get();
            $alldatas = PlanService::where('service', PlanUtils::ALLDATA)->where('apply', 'ANDROID')->where('public', true)->orderBy('price', 'asc')->get();
            $autodatas = PlanService::where('service', PlanUtils::AUTODATA)->where('apply', 'ANDROID')->where('public', true)->orderBy('price', 'asc')->get();
            $haynespros = PlanService::where('service', PlanUtils::HAYNESPRO)->where('apply', 'ANDROID')->where('public', true)->orderBy('price', 'asc')->get();
            $kdsgds = PlanService::where('service', PlanUtils::KDSGDS)->where('apply', 'ANDROID')->where('public', true)->orderBy('price', 'asc')->get();
        } else
        if ($platform  == 'ios') {
            $alldatavsautodatavshaynespro = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO)->where('apply', 'IOS')->where('public', true)->orderBy('price', 'asc')->get();
            //  $alldatavsautodata = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATA)->where('apply', 'IOS')->where('public', true)->orderBy('price', 'asc')->get();
            $alldatas = PlanService::where('service', PlanUtils::ALLDATA)->where('apply', 'IOS')->where('public', true)->orderBy('price', 'asc')->get();
            $autodatas = PlanService::where('service', PlanUtils::AUTODATA)->where('apply', 'IOS')->where('public', true)->orderBy('price', 'asc')->get();
            $haynespros = PlanService::where('service', PlanUtils::HAYNESPRO)->where('apply', 'IOS')->where('public', true)->orderBy('price', 'asc')->get();
            $kdsgds = PlanService::where('service', PlanUtils::KDSGDS)->where('apply', 'IOS')->where('public', true)->orderBy('price', 'asc')->get();
        } else {
            $alldatavsautodatavshaynespro = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO)->where('apply', 'APP')->where('public', true)->orderBy('price', 'asc')->get();
            // $alldatavsautodata = PlanService::where('service', PlanUtils::ALLDATAVSAUTODATA)->where('apply', 'APP')->where('public', true)->orderBy('price', 'asc')->get();
            $alldatas = PlanService::where('service', PlanUtils::ALLDATA)->where('apply', 'APP')->where('public', true)->orderBy('price', 'asc')->get();
            $autodatas = PlanService::where('service', PlanUtils::AUTODATA)->where('apply', 'APP')->where('public', true)->orderBy('price', 'asc')->get();
            $haynespros = PlanService::where('service', PlanUtils::HAYNESPRO)->where('apply', 'APP')->where('public', true)->orderBy('price', 'asc')->get();
            $kdsgds = PlanService::where('service', PlanUtils::KDSGDS)->where('apply', 'APP')->where('public', true)->orderBy('price', 'asc')->get();
        }

        $autodatas = [];
        $partslink24s = [];

        $tecdocs =  array();

        if (($platform  == 'web') || (filter_var(request("pay_in_link"), FILTER_VALIDATE_BOOLEAN) == true)) {
            $data = [
                // [
                //     'service' => PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO,
                //     'name' =>  PlanUtils::getNameService(PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO),
                //     'plans' =>  $alldatavsautodatavshaynespro
                // ],
                [
                    'service' => PlanUtils::ALLDATA,
                    'expiry' => $expiry_alldata,
                    'name' =>  PlanUtils::getNameService(PlanUtils::ALLDATA),
                    'plans' =>  $alldatas
                ],
                [
                    'service' => PlanUtils::KDSGDS,
                    'expiry' => $expiry_kdsgds,
                    'name' =>  PlanUtils::getNameService(PlanUtils::KDSGDS),
                    'plans' =>  $kdsgds
                ],
                [
                    'service' => PlanUtils::HAYNESPRO,
                    'expiry' => $expiry_haynespro,
                    'name' =>  PlanUtils::getNameService(PlanUtils::HAYNESPRO),
                    'plans' =>     $haynespros
                ],
                [
                    'service' => PlanUtils::HAYNESPRO_TRUCK,
                    'expiry' => $expiry_haynespro_truck,
                    'name' =>  PlanUtils::getNameService(PlanUtils::HAYNESPRO_TRUCK),
                    'plans' =>     $haynesprotrucks
                ],
                // [
                //     'service' => PlanUtils::TECDOC,
                //     'expiry' => $expiry_tecdoc,
                //     'name' =>  PlanUtils::getNameService(PlanUtils::TECDOC),
                //     'plans' =>     $tecdocs
                // ],
                [
                    'service' => PlanUtils::IDENTIFIX,
                    'expiry' => $expiry_identifix,
                    'name' =>  PlanUtils::getNameService(PlanUtils::IDENTIFIX),
                    'plans' =>     $identifixs
                ],
                [
                    'service' => PlanUtils::MITCHELL_PRODEMAND,
                    'expiry' => $expiry_mitchell_prodemand,
                    'name' =>  PlanUtils::getNameService(PlanUtils::MITCHELL_PRODEMAND),
                    'plans' =>     $mitchellprodemand
                ],
                [
                    'service' => PlanUtils::PARTSLINK24,
                    'expiry' => $expiry_partslink24,
                    'name' =>  PlanUtils::getNameService(PlanUtils::PARTSLINK24),
                    'plans' =>     $partslink24s
                ],
                [
                    'service' => PlanUtils::AUTODATA,
                    'expiry' => $expiry_autodata,
                    'name' =>  PlanUtils::getNameService(PlanUtils::AUTODATA),
                    'plans' =>    $autodatas
                ],
                [
                    'service' => PlanUtils::FORD_PTS,
                    'expiry' => $expiry_ford_pts,
                    'name' =>  PlanUtils::getNameService(PlanUtils::FORD_PTS),
                    'plans' =>    $fordtis
                ],
                [
                    'service' => PlanUtils::TOYOTA_TIS,
                    'expiry' => $expiry_toyota_tis,
                    'name' =>  PlanUtils::getNameService(PlanUtils::TOYOTA_TIS),
                    'plans' =>    $toyotatiss
                ],
            ];
        } else {
            $data = [
                // [
                //     'service' => PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO,
                //     'name' =>  PlanUtils::getNameService(PlanUtils::ALLDATAVSAUTODATAVSHAYNESPRO),
                //     'plans' =>  $alldatavsautodatavshaynespro
                // ],
                [
                    'service' => PlanUtils::ALLDATA,
                    'expiry' => $expiry_alldata,
                    'name' =>  PlanUtils::getNameService(PlanUtils::ALLDATA),
                    'plans' =>  $alldatas
                ],
                [
                    'service' => PlanUtils::KDSGDS,
                    'expiry' => $expiry_kdsgds,
                    'name' =>  PlanUtils::getNameService(PlanUtils::KDSGDS),
                    'plans' =>  $kdsgds
                ],
                [
                    'service' => PlanUtils::HAYNESPRO,
                    'expiry' => $expiry_haynespro,
                    'name' =>  PlanUtils::getNameService(PlanUtils::HAYNESPRO),
                    'plans' =>     $haynespros
                ],
                [
                    'service' => PlanUtils::AUTODATA,
                    'expiry' => $expiry_autodata,
                    'name' =>  PlanUtils::getNameService(PlanUtils::AUTODATA),
                    'plans' =>    $autodatas
                ],
            ];
        }



        if ($request->user != null && $request->user->username == "<EMAIL>") {
            $data = [
                [
                    'service' => PlanUtils::HAYNESPRO,
                    'expiry' => $expiry_haynespro,
                    'name' =>  PlanUtils::getNameService(PlanUtils::HAYNESPRO),
                    'plans' =>     $haynespros
                ]
            ];
        }

        $hidden = false;
        $isAgencyPay = false;

        //Kiểm tra param agency code để check ẩn
        if ($request->user != null) {
            if (request("agency_code") != null) {
                $agency = DB::table('agencies')->where('agency_code', request("agency_code"))->first();
                if ($agency  != null) {
                    if ($agency->hidden_price  == true) {
                        $hidden = true;
                    }
                }
            }

            if ($request->user->of_agency_id != null) {
                $agency = DB::table('agencies')->where('id', $request->user->of_agency_id)->first();
                if ($agency  != null) {
                    if ($agency->hidden_price  == true) {
                        $hidden = true;
                    }
                }
            }


            $agency2 = DB::table('agencies')->where('user_id', $request->user->id)->first();
            if ($request->user->is_admin != true && $agency2  != null) {
                $hidden = true;
                $isAgencyPay = true;
            }
        }


        //Cấm US , UK và Germany
        if ($position = Location::get(IPUtils::getIP())) {
            if (
                $position->countryName == 'United Kingdom'
                ||
                $position->countryName == 'Germany'
                // ||
                // $position->countryName == 'United States'

            ) {
                $hidden = true;
            }
        }



        if ($hidden == true) {
            $data = [
                [
                    'service' => PlanUtils::ALLDATA,
                    'expiry' => $expiry_alldata,
                    'name' =>  PlanUtils::getNameService(PlanUtils::ALLDATA),
                    'plans' =>  array()
                ],
                [
                    'service' => PlanUtils::HAYNESPRO,
                    'expiry' => $expiry_haynespro,
                    'name' =>  PlanUtils::getNameService(PlanUtils::HAYNESPRO),
                    'plans' =>  array()
                ],
                [
                    'service' => PlanUtils::HAYNESPRO_TRUCK,
                    'expiry' => $expiry_haynespro_truck,
                    'name' =>  PlanUtils::getNameService(PlanUtils::HAYNESPRO_TRUCK),
                    'plans' =>  array()
                ],
                // [
                //     'service' => PlanUtils::TECDOC,
                //     'expiry' => $expiry_tecdoc,
                //     'name' =>  PlanUtils::getNameService(PlanUtils::TECDOC),
                //     'plans' =>  array()
                // ],
                [
                    'service' => PlanUtils::IDENTIFIX,
                    'expiry' => $expiry_identifix,
                    'name' =>  PlanUtils::getNameService(PlanUtils::IDENTIFIX),
                    'plans' =>  array()
                ],
                [
                    'service' => PlanUtils::MITCHELL_PRODEMAND,
                    'expiry' => $expiry_mitchell_prodemand,
                    'name' =>  PlanUtils::getNameService(PlanUtils::MITCHELL_PRODEMAND),
                    'plans' =>  array()
                ],
                [
                    'service' => PlanUtils::PARTSLINK24,
                    'expiry' => $expiry_partslink24,
                    'name' =>  PlanUtils::getNameService(PlanUtils::PARTSLINK24),
                    'plans' =>  array()
                ],
                [
                    'service' => PlanUtils::KDSGDS,
                    'expiry' => $expiry_kdsgds,
                    'name' =>  PlanUtils::getNameService(PlanUtils::KDSGDS),
                    'plans' =>  array()
                ],
                [
                    'service' => PlanUtils::AUTODATA,
                    'expiry' => $expiry_autodata,
                    'name' =>  PlanUtils::getNameService(PlanUtils::AUTODATA),
                    'plans' =>  array()
                ],
                [
                    'service' => PlanUtils::FORD_PTS,
                    'expiry' => $expiry_ford_pts,
                    'name' =>  PlanUtils::getNameService(PlanUtils::FORD_PTS),
                    'plans' =>   array()
                ],
                [
                    'service' => PlanUtils::TOYOTA_TIS,
                    'expiry' => $expiry_toyota_tis,
                    'name' =>  PlanUtils::getNameService(PlanUtils::TOYOTA_TIS),
                    'plans' =>   array()
                ],
            ];
        }

        if ($isAgencyPay == true) {
            array_push($data, [
                'service' => "Billing agent for DrCar 90$",
                'expiry' => null,
                'name' =>  'Billing agent for DrCar 90$',
                'plans' =>  [
                    [

                        "apply" => "WEB",
                        "id"  =>  81,
                        "month"  =>  1,
                        "pay_in_link"  =>  false,
                        "price" => 90,
                        "product_id"  => "comboagent90",
                        "public" => true,
                        "service"  => "Billing agent for DrCar",

                    ]
                ]
            ]);
        }



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' =>   $data
        ], 200);
    }


    /**
     * Đăng ký gia hạn
     * 
     */
    public function renewal_plan(Request $request)
    {

        $user_id = $request->user->id;
        $plan_id = $request->plan->id;
        return redirect("/paypal-process?user_id=$user_id&plan_id=$plan_id");
    }
}
