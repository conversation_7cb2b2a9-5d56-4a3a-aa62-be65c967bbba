<?php

namespace App\Http\Controllers\Api\User;

use App\Events\RedisChatEventUserToUser;
use App\Helper\ChatUtils;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\GroupBanMember;
use App\Models\GroupChat;
use App\Models\GroupMember;
use App\Models\UToUMessage;
use App\Models\User;
use App\Models\MsgCode;
use App\Models\PersonChat;


/**
 * @group  User/Group
 */

class GroupBanMemberController extends Controller
{

    function check_permisstion(Request $request,   $group_id)
    {

        $userInGroup = GroupMember::where('group_id',  $group_id)->where('user_id', $request->user->id)->first();
        if ($userInGroup == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Bạn không phải thành viên nhóm này",
            ], 400);
        }

        if ($request->user->type != 1 && $request->user->type != 3 && $userInGroup->is_key == false) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Trưởng nhóm, quản lý hoặc thầy mới có quyền này",
            ], 400);
        }
    }
    /**
     * Xem  member bị ban
     * 
     * image_url
     * name
     * 
     */
    public function getAll(Request $request)
    {

        $group_id = $request->route()->parameter('group_id');

        $e =  $this->check_permisstion($request,   $group_id);
        if ($e != null) {
            return $e;
        }

        $group = Group::where('id', $group_id)->first();

        if ($group  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_EXISTS[0],
                'msg' => MsgCode::NO_GROUP_EXISTS[1],
            ], 400);
        }

        $members = GroupBanMember::where('group_id',  $group_id)->get();



        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => $members
        ], 200);
    }


    /**
     * Thêm thành viên vào ban
     * 
     * @bodyParam list_user_ids required List danh sách id member sp (VD: [1,2,3])
     * 
     */
    public function removeMember(Request $request)
    {
        $group_id = $request->route()->parameter('group_id');

        $group = Group::where('id', $group_id)->first();

        if ($group  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_EXISTS[0],
                'msg' => MsgCode::NO_GROUP_EXISTS[1],
            ], 400);
        }


        $e =  $this->check_permisstion($request,   $group_id);
        if ($e != null) {
            return $e;
        }

        foreach ($request->list_user_ids as $user_id) {
            GroupBanMember::where('group_id', $group->id)->where('user_id', $user_id)->delete();
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => GroupBanMember::where('group_id',  $group_id)->get()
        ], 200);
    }
    /**
     * Thêm thành viên vào ban
     * 
     * @bodyParam list_user_ids required List danh sách id member sp (VD: [1,2,3])
     * 
     */
    public function addMember(Request $request)
    {
        $group_id = $request->route()->parameter('group_id');

        $group = Group::where('id', $group_id)->first();

        if ($group  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_GROUP_EXISTS[0],
                'msg' => MsgCode::NO_GROUP_EXISTS[1],
            ], 400);
        }
        $e =  $this->check_permisstion($request,   $group_id);
        if ($e != null) {
            return $e;
        }

        if (in_array($request->user->id, $request->list_user_ids)) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "Không thể cấm chính bạn",
            ], 400);
        }

        foreach ($request->list_user_ids as $user_id) {
            if (GroupBanMember::where('group_id', $group->id)->where('user_id', $user_id)->first() == null) {
                GroupBanMember::create([
                    'group_id' =>  $group->id,
                    'user_id' => $user_id,
                ]);
            }
        }

        $group->members = GroupMember::where('group_id',  $group_id)->get();

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
            'data' => GroupBanMember::where('group_id',  $group_id)->get()
        ], 200);
    }
}
