<?php

namespace App\Http\Controllers\Api\Payment;

use App\Helper\Helper;
use App\Helper\PlanUtils;
use App\Helper\RenewUtils;
use App\Http\Controllers\Controller;
use App\Models\MsgCode;
use App\Models\PlanService;
use App\Models\RenewalHistory;
use Exception;
use Illuminate\Http\Request;


/**
 * @group  Customer/thanh toán google
 */
class GooglePayController extends Controller
{

    /**
     * 
     * @bodyParam  product_id id product gia hạn
     * @queryParam references_value mã code xử lý
     * @queryParam security_code mã code kiểm tra an toàn md5(references_value,HOANGDRCARPAY)
     */
    public function create(Request $request)
    {
        $plan =  PlanService::where('product_id',  $request->product_id)->first();

        if ($plan  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_PLAN_EXISTS[0],
                'msg' => MsgCode::NO_PLAN_EXISTS[1],
            ], 400);
        }
        $history =  RenewalHistory::where('references_value',  $request->references_value)->first();
        if (empty($request->references_value) ||  $history   != null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::TRANSACTION_PROCESSED[0],
                'msg' => MsgCode::TRANSACTION_PROCESSED[1],
            ], 400);
        }

        $codemd5 = $request->references_value . ",HOANGDRCARPAY";

        if (empty($request->security_code) ||  md5($codemd5) != $request->security_code) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::INVALID_SECURITY_CODE[0],
                'msg' => MsgCode::INVALID_SECURITY_CODE[1],
            ], 400);
        }


        RenewUtils::renew_service_for_user(
            $request->user->id,
            $request->product_id,
            RenewUtils::PAY_FROM_APPLE_PAY,
            $request->user->id,
            $request->references_value ?? "",
            $request->json_data ?? ""
        );

        return response()->json([
            'code' => 200,
            'success' => true,
            'msg_code' => MsgCode::SUCCESS[0],
            'msg' => MsgCode::SUCCESS[1],
        ], 200);
    }
}
