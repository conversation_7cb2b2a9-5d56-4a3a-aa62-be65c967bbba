<?php

namespace App\Http\Controllers\Api;

use App\Classes\WebClass;
use App\Helper\AllDataUSv2UtilsWeb;
use App\Helper\AllDataUtils;
use App\Helper\AllDataUtilsWeb;
use App\Helper\AutoDataItalyUtilsWeb;
use App\Helper\AutoDataUtils;
use App\Helper\AutoDataUtilsWeb;
use App\Helper\DomainConfigHelper;
use App\Helper\ETKAUtilsWeb;
use App\Helper\FordUtilsWeb;
use App\Helper\HaynesTruckUtilsWeb;
use App\Helper\HaynesUtils;
use App\Helper\HaynesUtilsWeb;
use App\Helper\Helper;
use App\Helper\IdentifixUtilsWeb;
use App\Helper\IPUtils;
use App\Helper\LogUtils;
use App\Helper\MitchellProdemandUtilsWeb;
use App\Helper\MitchellRepairCenterUtilsWeb;
use App\Helper\Partslink24UtilsMobileWeb;
use App\Helper\Partslink24UtilsWeb;
use App\Helper\PartsouqUtilsWeb;
use App\Helper\PlanUtils;
use App\Helper\SecurityUtils;
use App\Helper\ToyotaUtilsWeb;
use App\Helper\UserUtils;
use App\Helper\ViewUtils;
use App\Helper\WebDataUtils;
use App\Helper\WebDataUtilsNasHttp;
use App\Http\Controllers\Controller;
use App\Models\ConfigAdmin;
use App\Models\MsgCode;
use App\Models\Statistic;
use Illuminate\Support\Facades\Cache;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use HungCP\PhpSimpleHtmlDom\HtmlDomParser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Stevebauman\Location\Facades\Location;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\ServerException;

/**
 * @group  Admin/Log
 */
class DataLeechControllerForm3Controller extends Controller
{

    /**
     * Thông tin server
     */
    public function indexGet(Request $request)
    {


        $domain_request = $_SERVER['HTTP_HOST'];
        $position = Location::get(IPUtils::getIP());
        if ($position != null && str_contains($domain_request, 'docfix')) {
            if (
                $position->countryName == 'United Kingdom'
                ||
                $position->countryName == 'Germany'
            ) {
                return response()->view('suspension');
            }
        }

        if (
            $position != null && $position->countryName == 'Russia'
        ) {
            return response()->view('suspension');
        }



        $tailLink = $_SERVER["REQUEST_URI"];


        $domainWithoutSub = Helper::getDomainCurrentWithoutSubAndNoHttp();

        if (
            !str_contains($domain_request, "main.") &&
            !str_contains($domain_request, "main-temp.") &&
            !str_contains($domain_request, "app-alldata.") &&
            !str_contains($domain_request, "autodata-sv.") &&
            !str_contains($domain_request, "workshop-autodata-group.") &&
            !str_contains($domain_request, "kdsgds.") &&
            !str_contains($domain_request, "kdsgds.") &&
            !str_contains($domain_request, "my-alldata.") &&
            !str_contains($domain_request, "mitchell-prodemand.") &&
            !str_contains($domain_request, "atsg.") &&
            !str_contains($domain_request, "haynespro.") &&
            !str_contains($domain_request, "haynespro-truck.") &&
            !str_contains($domain_request, "identifix.") &&
            !str_contains($domain_request, "partslink24.") &&
            !str_contains($domain_request, "etka.") &&
            !str_contains($domain_request, "tecdoc.") &&
            !str_contains($domain_request, "ford-pts.") &&
            !str_contains($domain_request, "toyota.")
        ) {

            if (str_contains($domain_request, "main.") && $tailLink == "/") {
                header("Location: https://down.$domainWithoutSub");
                exit;
            }



            if ($domain_request == $domainWithoutSub) {
                http_response_code(404);
                echo "404";
                die();
                //return response()->view('welcome');
            }

            /////////Handle metadata
            $title = "DOC DATA";
            $agency_code = "";
            $description = "ALLDATA, AUTODATA, HAYNESPRO, IDENTIFIX, MITCHELL, PARTSLINK24, ASGT, KDSGDS, FORD TIS";

            $favicon_url = DomainConfigHelper::getConfig('default_ico');
            $image_share_web_url  = DomainConfigHelper::getConfig('default_logo');
            $logo_url  = DomainConfigHelper::getConfig('default_logo');

            $domains = $request->getHost();
            $actual_link = "http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
            $splitDomain = explode('.', $domains);


            $domains = str_replace("https://", "", $domains);
            $domains = str_replace("http://", "", $domains);
            $domains = str_replace("/", "", $domains);

            $agency_code = $splitDomain[0];

            //agency theo sub
            $agency = DB::table('agencies')->where('sub_domain',  $agency_code)->first();

            $appName = DomainConfigHelper::getConfig('appName');
            if ($appName == "FHOST") {

                if ($agency_code == "web") { //nếu đại lý đã mua domain
                    $agency = DB::table('agencies')->where('domain',  $domainWithoutSub)->first();
                }

                if ($agency == null) {
                    // agency theo agency code
                    $agency = DB::table('agencies')->where('agency_code',  $agency_code)->first();
                    if ($agency == null) {
                        http_response_code(404);
                        echo "404";
                        die();
                    }
                }
            }
            if ($appName == "DRCAR") {
                if ($agency_code  == "my") {
                } else
                if ($agency_code  != "app") {
                    http_response_code(404);
                    echo "404";
                    die();
                }
            }

            $agency_web_theme = null;
            if ($agency  != null) {
                $agency_web_theme = DB::table('agency_web_themes')->where('agency_id',  $agency->id)->first();
                if ($agency_web_theme != null) {
                    $title =  $agency_web_theme->title;
                    $description =  $agency_web_theme->description;
                    $logo_url =  $agency_web_theme->logo_url;
                    $background_url =  $agency_web_theme->background_url;
                    $background_color =  $agency_web_theme->background_color;
                    $favicon_url =  $agency_web_theme->favicon_url;
                }
            }

            $webType =  $appName == "DRCAR" ? ($agency_code  == "my" ? '' : 'main') : '';


            if ($tailLink == "/manifest.json") {
                return response()->json([
                    "short_name" =>  $agency_code,
                    "name" => $title,
                    "icons" => [
                        [
                            "src" => $favicon_url,
                            "sizes" => "64x64 32x32 24x24 16x16",
                            "type" => "image/x-icon"
                        ],
                        [
                            "src" =>  $logo_url,
                            "type" => "image/png",
                            "sizes" => "192x192"
                        ],
                        [
                            "src" =>  $logo_url,
                            "type" => "image/png",
                            "sizes" => "512x512"
                        ]
                    ],
                    "start_url" => ".",
                    "display" => "standalone",
                    "theme_color" => $agency_web_theme == null  ? "#000000" : $agency_web_theme->color_main_1,
                    "background_color" => "#ffffff"
                ], 200);
            }




            $contactLink = UserUtils::getContactLinkWithAgency($agency);

            return response()->view('agent/welcome',     [
                'description' => $description,
                'favicon_url' => $favicon_url,
                'image_share_web_url' => $logo_url,
                'logo_url' => $logo_url,
                'title' => $title,
                'web_theme' => $agency_web_theme == null  ?
                    json_encode([
                        'logo_url' => null,
                        'background_url' =>  null,
                        'background_color' =>  null,
                        'color_main_1' => "rgb(31, 41, 55)",
                        'type' => $webType,
                        'title' => $title,
                        'contact_link' => '',
                        'agent_code' => "DOC DATA",
                        'theme_style' > 'light'
                    ])
                    : json_encode([
                        'logo_url' =>  $agency_web_theme->logo_url,
                        'background_url' =>  $agency_web_theme->background_url,
                        'background_color' => $agency_web_theme->background_color,
                        'color_main_1' => $agency_web_theme->color_main_1,
                        'type' => $webType,
                        'title' => $title,
                        'contact_link' => $contactLink,
                        'agent_code' => $agency_code,
                        'theme_style' => $agency_web_theme->theme_style,
                    ])
            ]);
        }

        $server = $request->data3_server;
        $domain = $request->data3_domain;


        if ($server == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "NO SERVER",
            ], 400);
        }
        if ($domain == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "NO DOMAIN",
            ], 400);
        }

        $today = Carbon::today();
        $statistic = Statistic::firstOrCreate(
            ['day_statistic' => $today],
        );

        $link = $domain .  $tailLink;
        $statistic->update([
            'total_request' => $statistic->total_request + 1,
        ]);


        $response1 =  null;
        if (($server == SecurityUtils::SERVER_AUTO_DATA_WEB || $server == SecurityUtils::SERVER_AUTO_DATA_ITALY_WEB) && str_contains($tailLink, 'app_redirect')) {

            if ($request != null && request('manufacturer_id') != null && request('model_id') != null) {
                AutoDataUtilsWeb::setCache($request, 'manufacturer_id', request('manufacturer_id'));
                AutoDataUtilsWeb::setCache($request, 'model_id', request('model_id'));
            }

            if (
                $request != null && request('vehicle_id') != null
                && request('engine_id') != null
                && request('engine_name') != null
                && request('route_name') != null
            ) {
                AutoDataUtilsWeb::setCache($request, 'vehicle_id', request('vehicle_id'));
                AutoDataUtilsWeb::setCache($request, 'engine_id', request('engine_id'));
                AutoDataUtilsWeb::setCache($request, 'engine_name', request('engine_name'));
                AutoDataUtilsWeb::setCache($request, 'route_name', request('route_name'));
                AutoDataUtilsWeb::setCache($request, 'module_id', request('module_id'));
                AutoDataUtilsWeb::setCache($request, 'back', request('back'));
            }

            $vehicle_id = request('vehicle_id');
            $module_id = request('module_id');
            $route_name = request('route_name');
            $domain = Helper::getDomainCurrent();

            $newURL =  "$domain/w1/vehicles/variants/$route_name/$vehicle_id?route_name=$route_name&module=$module_id";
            header('Location: ' . $newURL);
            exit();
        }

        //autodata save
        if ($server == SecurityUtils::SERVER_AUTO_DATA_WEB_SAVED) {

            $tailLinkSave = Helper::removeParamsFromUrlAutodata($tailLink);
            $webClass = new WebClass();
            $webClass->hours_ago = 24 * 30;
            $webClass->tail_link = $tailLinkSave;
            $webClass->service =  PlanUtils::AUTODATA;
            $webClass->method = $request->method();

            $webDataExists =   WebDataUtilsNasHttp::getWebData($webClass);
            if (is_array($webDataExists) && $webDataExists['code'] == 404) {
                echo "no data";
                http_response_code(404);
                die();
            }

            if (
                $webDataExists  != null && $webDataExists->content != null && $webDataExists->content != ""
            ) {

                echo AutoDataUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", $webDataExists->status_code == 404 ? 404 : 200, $request);
                die();
            }

            echo "no data";
            http_response_code(404);
            die();
        }

        if ($server == SecurityUtils::SERVER_AUTO_DATA_WEB || $server == SecurityUtils::SERVER_AUTO_DATA) {
            try {
                if ($request != null && $request->method() == "GET") {

                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::AUTODATA;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);


                    $valid = true;

                    if ($webDataExists != null) {
                        $valid =    AutoDataUtilsWeb::checkValidContent($request, $webDataExists->content_type, base64_decode($webDataExists->content), $tailLink, $link);
                    }


                    if ($webDataExists  != null && $webDataExists->content != null && $webDataExists->content != "" && $valid == true) {
                        echo AutoDataUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                        die();
                    }
                }


                $response1 = AutoDataUtilsWeb::getResponse($link, "GET", null, null, $request, null);

                if ($response1 instanceof Exception) {
                    return response()->json(['error' => 'Too many requests. Please try again later.'], 429);
                }


                $content = $response1->getBody()->getContents();
                if ($content != null && str_contains($content, '<input type="hidden" name="form_id" value="user_login" />')) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::AUTODATA_OF_MAINTENANCE[0],
                        'msg' => MsgCode::AUTODATA_OF_MAINTENANCE[1],
                    ]);
                }


                $timesTry = $response1->timesTry ?? 0;
                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));
                echo AutoDataUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", ($redirectCount == 0 &&  $timesTry == 0) ? $response1->getStatusCode() : null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::AUTODATA_OF_MAINTENANCE[0],
                    'msg' => MsgCode::AUTODATA_OF_MAINTENANCE[1],
                ]);
            }
        }

        //autodata italy
        if ($server == SecurityUtils::SERVER_AUTO_DATA_ITALY_WEB) {
            try {
                if ($request != null && $request->method() == "GET") {
                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::AUTODATA_ITALY;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);


                    $valid = true;

                    if ($webDataExists != null) {
                        $valid =    AutoDataItalyUtilsWeb::checkValidContent($request, $webDataExists->content_type, base64_decode($webDataExists->content), $tailLink, $link);
                    }


                    if ($webDataExists  != null && $webDataExists->content != null && $webDataExists->content != "" && $valid == true) {
                        echo AutoDataItalyUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                        die();
                    }
                }


                $response1 = AutoDataItalyUtilsWeb::getResponse($link, "GET", null, null, $request, null);

                if ($response1 instanceof Exception) {
                    return response()->json(['error' => 'Too many requests. Please try again later.'], 429);
                }


                $content = $response1->getBody()->getContents();
                if ($content != null && str_contains($content, '<input type="hidden" name="form_id" value="user_login" />')) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::AUTODATA_OF_MAINTENANCE[0],
                        'msg' => MsgCode::AUTODATA_OF_MAINTENANCE[1],
                    ]);
                }


                $timesTry = $response1->timesTry ?? 0;
                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));
                echo AutoDataItalyUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", ($redirectCount == 0 &&  $timesTry == 0) ? $response1->getStatusCode() : null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::AUTODATA_OF_MAINTENANCE[0],
                    'msg' => MsgCode::AUTODATA_OF_MAINTENANCE[1],
                ]);
            }
        }

        //Alldata eu
        if ($server == SecurityUtils::SERVER_ALL_DATA) {

            //Chặn logout
            if (
                str_contains($tailLink, "login") ||   str_contains($tailLink, "logout") || str_contains($tailLink, "session") ||  str_contains($tailLink, "check")
            ) {
                echo "0";
                die();
            }

            $statistic->update([
                'total_alldata_eu_request' => $statistic->total_alldata_eu_request + 1,
            ]);

            try {
                $response1 = AllDataUtils::getResponse($link, 'GET', null, null, $request);
                $content = $response1->getBody()->getContents();
                if ($content != null && str_contains($content, '<title>Login</title>')) {
                    return response()->json([
                        'code' => 400,
                        'success' => false,
                        'msg_code' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[0],
                        'msg' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[1],
                    ], 400);
                }
                echo AllDataUtils::remove_element_reponse($response1);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[1],
                ], 400);
            }
        }

        if ($server == SecurityUtils::SERVER_ALL_DATA_WEB) {

            $configAdmin =  ConfigAdmin::first();
            if ($configAdmin->temp_alldata_eu == 'AUTODATA') {
                $domainMain = Helper::getDomainCurrent();
                $domainMain = str_replace('app-alldata', 'autodata-sv', $domainMain);
                header("Location: " . $domainMain);
                exit;
            }


            if ($configAdmin->temp_alldata_eu == 'HAYNESPRO') {
                $domainMain = Helper::getDomainCurrent();
                $domainMain = str_replace('app-alldata', 'haynespro', $domainMain);
                header("Location: " . $domainMain);
                exit;
            }

            if (
                str_contains($tailLink, "akam/13")
            ) {
                echo "Not Found";
                die();
            }

            if (str_contains($tailLink, "vehicle/search/vrmCountry")) {
                header('Content-Type: application/octet-stream;charset=UTF-8');
                echo "ZmFsc2U=";

                die(); //ẩn plate  , true thì show plate die(); 
            }

            //Chặn logout
            if (
                str_contains($tailLink, "login") ||   str_contains($tailLink, "logout") || str_contains($tailLink, "session") ||  str_contains($tailLink, "check")
            ) {
                echo "0";
                die();
            }

            if (
                str_contains($tailLink, "bookmarks/getBookmarksCount")
            ) {
                echo "0";
                die();
            }

            if (
                str_contains($tailLink, "getUserEncryptedPin")
            ) {
                return response()->json(["accountPinEncrypt" => "41bcbe6"], 200);
                die();
            }

            //Chặn logout
            if (
                str_contains($tailLink, "login") ||   str_contains($tailLink, "logout") || str_contains($tailLink, "session") ||  str_contains($tailLink, "check")
            ) {
                echo "0";
                die();
            }

            if ($tailLink == "/") {

                $hiddenLogo = 0;
                if ($request->user != null && $request->user->of_agency_id != null) {
                    $agency = DB::table('agencies')->where('id', $request->user->of_agency_id)->first();
                    if ($agency  != null) {
                        $hiddenLogo = 1;
                    }
                }

                return response()->view('alldataeu/choose_version', [
                    'domain' => Helper::getDomainCurrentWithoutSubAndNoHttp(),
                    'agency_sub' => $request->agency_sub,
                    'hiddenLogo' => $hiddenLogo,
                    'msg_code' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[1],
                ]);
                exit;
            }

            if ($tailLink == "/" || str_contains($tailLink, "alldataeu_over")) {
                return response()->view('alldataeu/home', [
                    'email' => $request->user->username,
                    'msg_code' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[1],
                ]);
                exit;
            }


            if (str_contains($tailLink, '/alldata/user/vehicle/selector')) {

                echo ("YMME");
                die();
            }




            if (str_contains($tailLink, 'alldata/user/load') || str_contains($tailLink, 'alldata/vehicle/undefined/user/load')) {
                $res = [
                    "id" => "",
                    "email" => $request->user->username,
                    "firstName" => "Harald",
                    "lastName" => "Torn",
                    "defaultLocale" => "en_GB",
                    "phoneNumber" => "+**********",
                    "subscription" => [
                        "account" => [
                            "id" => "670cd692e4b063ae934357a8",
                            "primaryUserName" => "<EMAIL>",
                            "shop" => [
                                "shopName" => "Auto",
                                "address" => [
                                    "ADDRESS_1" =>
                                    "cam mu",
                                    "ADDRESS_2" => "",
                                    "ALT_PHONE_NUMBER" => "+**********",
                                    "CITY_TOWN_LOCALITY" => "Kuree",
                                    "FAX_NUMBER" => "+**********",
                                    "PHONE_NUMBER" => "+**********",
                                    "POSTAL_CODE" => "12345",
                                    "STATE_PROVINCE" => "Saare maakond",
                                ],
                                "country" => ["name" => "Estonia", "countryCode" => "EE"],
                            ],
                            "country" => null,
                            "type" => "C",
                        ],
                        "id" => "670cd693e4b063ae934357a9",
                        "startDate" => 1728900102000,
                        "endDate" => 1730199702000,
                        "availableMakes" => [["code" => "all", "name" => "all"]],
                        "billType" => "N",
                    ],
                    "vehicleSelector" => "YMME",
                    "wantsMarketingInfo" => false,
                    "wantsProductUpdateInfo" => false,
                    "trialUser" => false,
                    "activeFeatures" => [
                        ["id" => "LT", "name" => "Labour Times", "active" => true],
                    ],
                    "roles" => [["code" => "Gen3 Owner", "name" => "Owner"]],
                    "acceptedTerms" => false,
                    "token" => "911579c4-a56a-4f7f-8d36-69db420b37a0",
                    "expired" => false,
                    "remainingDays" => 0,
                ];

                return response()->json($res, 200);
            }


            if (
                str_contains($tailLink, "save") || str_contains($tailLink, "load") ||
                (str_contains($tailLink, "/alldata/vehicle/information/") && !str_contains($tailLink, "/alldata/vehicle/information/article"))
            ) {
                return response()->json([
                    "id" => "0",
                    "engineId" => "null",
                    "modelId" => "null",
                    "manufacturerId" => "null",
                    "vehicle" => "null",
                    "engine" => "null",
                    "model" => "null",
                    "make" => "null",
                    "fullVehicle" => "null",
                    "fullEngine" => "null",
                    "sessionBMWVehicle" => "null",
                    "sessionVehicle" => "null",
                    "VIN" => "",
                ], 200);
            }

            if (
                str_contains($tailLink, "/alldata/vehicle/breadcrumb")
            ) {
                header('Content-Type: application/octet-stream; charset=UTF-8');

                // Dữ liệu cần trả về
                $data = [
                    ["id" => "ALLDATA_Classic_Component_1", "name" => ".", "clickable" => false],
                    ["id" => "ALLDATA_Classic_Component_1236", "name" => ".", "clickable" => false],
                    ["id" => "ALLDATA_Classic_Component_611", "name" => ".", "clickable" => false],
                    ["id" => "ALLDATA_Classic_InfoType_7", "name" => ".", "clickable" => false],
                    ["id" => "ALLDATA_Classic_InfoType_21", "name" => ".", "clickable" => false],
                    ["id" => "eu-ada1gb-en-GB-A005GBS00009_original_html", "name" => ".", "clickable" => true]
                ];

                echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                die();
            }
            $referer = $request->headers->get('referer');
            $pathReferer = parse_url($referer, PHP_URL_PATH);
            $tailLinkSave = Helper::removeParamsFromUrl($tailLink, ['_']);
            //Thêm vào để xử lý 2 server speed và original
            if (str_contains($referer, "alldata-eu")) {
                if (strpos($tailLinkSave, 'fr=eu') === false) {
                    if (strpos($tailLinkSave, '?') !== false) {
                        $tailLinkSave .= '&fr=eu';
                    } else {
                        $tailLinkSave .= '?fr=eu';
                    }
                }
            }


            try {

                if ($request != null && $request->method() == "GET") {
                    if (
                        str_contains($tailLink, '.js') ||
                        str_contains($tailLink, '.css') ||
                        str_contains($tailLink, '.properties') ||
                        str_contains($tailLink, '.png')  ||
                        str_contains($tailLink, '.svg') ||
                        str_contains($tailLink, '.woff') ||
                        // || $pathReferer == "/alldata/vehicle/"
                        str_contains($tailLink, "/alldata/vehicle/")
                    ) {

                        $webClass = new WebClass();
                        $webClass->hours_ago = 24 * 30;
                        $webClass->tail_link = $tailLinkSave;
                        $webClass->service =  PlanUtils::ALLDATA;
                        $webClass->method = $request->method();

                        $webDataExists =   WebDataUtils::getWebData($webClass);

                        //   dd(   $webDataExists  );

                        if (
                            $webDataExists  != null  && $webDataExists->content != null &&
                            $webDataExists->updated_at != null &&
                            WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)
                        ) {

                            $body = base64_decode($webDataExists->content);
                            if ($body == "" && str_contains(".js",  $tailLink)  && str_contains(".css",  $tailLink)) {
                                abort(404);
                            }
                            if (str_contains($body, '<TITLE>Access Denied</TITLE>')) {
                                abort(404);
                            }

                            if (!str_contains($body, 'Whoops!, Something went wrong.')) {
                                header('aaacache-fr-server: true');
                                header('baacache-fr-server: true');
                                echo AllDataUtilsWeb::remove_element_reponse($body, $webDataExists->content_type ?? "", null, $request);
                                die();
                            }
                        }
                    }
                }

                $statistic->update([
                    'total_alldata_eu_request' => $statistic->total_alldata_eu_request + 1,
                ]);

                $response1 = AllDataUtilsWeb::getResponse($link, 'GET', null, null, $request);

                $content = $response1->getBody()->getContents();
                if ($content != null && str_contains($content, '<title>Login</title>')) {

                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[0],
                        'msg' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[1],
                    ]);
                    exit;
                }

                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));

                if (str_contains($response1->getBody(), '<TITLE>Access Denied</TITLE>')) {
                    abort(404);
                }


                echo AllDataUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", $redirectCount == 0 ? $response1->getStatusCode() : null, $request);
                die();
            } catch (Exception $e) {

                if (str_contains($tailLink, "/alldata/vehicle/breadcrumb?componentId") && str_contains($e->getResponse()->getBody()->getContents(), 'Whoops!, Something went wrong.')) {
                    return response()->json([], 200);
                }


                LogUtils::error($e);
                //   http_response_code($e->getResponse()->getStatusCode());
                echo $e->getResponse()->getBody()->getContents();
                exit;
            }
        }

        //ALldata us
        if ($server == SecurityUtils::SERVER_ALL_DATA_US_V2) {

            //Thay đổi link        
            $tailLink = str_replace('?has-repair-data=true&locale=en_US', '?locale=en_US&region=Region_1&has-repair-data=true', $tailLink);
            $tailLink = str_replace('?locale=en_US&has-repair-data=true', '?locale=en_US&region=Region_1&has-repair-data=true', $tailLink);

            $link = str_replace('?has-repair-data=true&locale=en_US', '?locale=en_US&region=Region_1&has-repair-data=true', $link);
            $link = str_replace('?locale=en_US&has-repair-data=true', '?locale=en_US&region=Region_1&has-repair-data=true', $link);


            try {


                $statistic->update([
                    'total_alldata_us_request' => $statistic->total_alldata_us_request + 1,
                ]);




                if ($request != null && $request->method() == "GET") {
                    $tailLink = Helper::removeParamsFromUrl($tailLink, ['t']);

                    $webClass = new WebClass();
                    if (str_contains($tailLink, "/profile")) {
                        $webClass->hours_ago = 3;
                    } else {
                        $webClass->hours_ago = 24 * 30;
                    }

                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::ALLDATA_US;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if (
                        $webDataExists  != null && $webDataExists->content != null &&
                        $webDataExists->updated_at != null
                        && WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)
                    ) {
                        $body = base64_decode($webDataExists->content);

                        if ($body == "" && (str_contains(".js",  $tailLink)  || str_contains(".css",  $tailLink))) {
                            abort(404);
                        }

                        if (!str_contains($body, "404 Not Found")) {

                            $statistic->update([
                                'total_alldata_us_request_has_cache' => $statistic->total_alldata_us_request_has_cache + 1,
                            ]);


                            echo AllDataUSv2UtilsWeb::remove_element_reponse($body, $webDataExists->content_type ?? "", null, $request);
                            die();
                        }
                    }
                }


                $response1 = AllDataUSv2UtilsWeb::getResponse($link, null, "GET", null, $request);
                $content = $response1->getResponseBody();
                if ($content != null && str_contains($content, '<title>Login</title>')) {
                    return response()->json([
                        'code' => 400,
                        'success' => false,
                        'msg_code' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[0],
                        'msg' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[1],
                    ], 400);
                    exit;
                }
                $body = AllDataUSv2UtilsWeb::remove_element_reponse($response1->getResponseBody(),  $response1->getContentType(), $response1->getStatusCode(), $request, true);
                echo $body;
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[1],
                ], 400);
            }
        }
        if ($server == SecurityUtils::SERVER_ALL_DATA_US_V2_WEB) {

            //Thay đổi link    
            $tailLink = str_replace('?has-repair-data=true&locale=en_US', '?locale=en_US&region=Region_1&has-repair-data=true', $tailLink);
            $tailLink = str_replace('?locale=en_US&has-repair-data=true', '?locale=en_US&region=Region_1&has-repair-data=true', $tailLink);

            $link = str_replace('?has-repair-data=true&locale=en_US', '?locale=en_US&region=Region_1&has-repair-data=true', $link);
            $link = str_replace('?locale=en_US&has-repair-data=true', '?locale=en_US&region=Region_1&has-repair-data=true', $link);


            $configAdmin =  ConfigAdmin::first();
            if ($configAdmin->temp_alldata_us == 'MITCHELL_PRODEMAND') {
                $domainMain = Helper::getDomainCurrent();
                $domainMain = str_replace('my-alldata', 'mitchell-prodemand', $domainMain);
                header("Location: " . $domainMain);
                exit;
            }

            try {

                if (
                    $tailLink == "/"
                ) {

                    $domainMain = Helper::getDomainCurrent();
                    header("Location: " . $domainMain . "/repair/#/landingPage");
                    exit;
                }


                if (
                    str_contains($tailLink, 'v1/voting/card/events') ||   str_contains($tailLink, 'message-center/users/messages/product')
                ) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => "",
                        'msg' => "",
                    ]);
                    exit;
                }

                if (
                    str_contains($tailLink, 'Zss6V/fNj')
                ) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => "",
                        'msg' => "",
                    ]);
                    exit;
                }

                $statistic->update([
                    'total_alldata_us_request' => $statistic->total_alldata_us_request + 1,
                ]);

                $trueList = str_contains($tailLink, 'ADAG/repair/ADConnect/v5/carids/') && str_contains($tailLink, 'search/*/show_itypes/7');


                if ($request != null && $request->method() == "GET" &&  !$trueList) {
                    $tailLink = Helper::removeParamsFromUrl($tailLink, ['t']);

                    $webClass = new WebClass();
                    if (str_contains($tailLink, "/profile")) {
                        $webClass->hours_ago = 3;
                    } else {
                        $webClass->hours_ago = 24 * 30;
                    }

                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::ALLDATA_US;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if (
                        $webDataExists  != null && $webDataExists->content != null &&
                        $webDataExists->updated_at != null
                        && WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)
                    ) {
                        $body = base64_decode($webDataExists->content);

                        if ($body == "" && (str_contains(".js",  $tailLink)  || str_contains(".css",  $tailLink))) {
                            abort(404);
                        }

                        if (!str_contains($body, "404 Not Found") && !str_contains($body, "No data found for nonstandard")) {

                            $statistic->update([
                                'total_alldata_us_request_has_cache' => $statistic->total_alldata_us_request_has_cache + 1,
                            ]);

                            http_response_code($webDataExists->status_code == 404 ? 404 : 200);
                            echo AllDataUSv2UtilsWeb::remove_element_reponse($body, $webDataExists->content_type ?? "", null, $request);
                            die();
                        }
                    }
                }

                $response1 = AllDataUSv2UtilsWeb::getResponse($link, null, "GET", null, $request);
                $content = $response1->getResponseBody();
                if ($content != null && str_contains($content, '<title>Login</title>')) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[0],
                        'msg' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[1],
                    ]);
                    exit;
                }
                $body = AllDataUSv2UtilsWeb::remove_element_reponse($response1->getResponseBody(),  $response1->getContentType(), $response1->getStatusCode(), $request, true);
                http_response_code($response1->getStatusCode() == 404 ? 404 : 200);
                echo $body;
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[1],
                ]);
                exit;
            }
        }


        //Haynespro Car
        if ($server == SecurityUtils::SERVER_HAYNES_PRO) {
            try {

                $statistic->update([
                    'total_haynes_pro_request' => $statistic->total_haynes_pro_request + 1,
                ]);


                // if ($request != null && $request->method() == "GET") {
                //     $webClass = new WebClass();
                //     $webClass->hours_ago = 24 * 30;
                //     $webClass->tail_link = $tailLink;
                //     $webClass->service =  PlanUtils::HAYNESPRO;
                //     $webClass->method = $request->method();

                //     $webDataExists =   WebDataUtils::getWebData($webClass);

                //     if ($webDataExists  != null && $webDataExists->content != null && $webDataExists->content != "") {
                //         $contentDecode = base64_decode($webDataExists->content);

                //         if (!str_contains($contentDecode, "_csrf")) {
                //             $statistic->update([
                //                 'total_haynes_pro_request_has_cache' => $statistic->total_haynes_pro_request_has_cache + 1,
                //             ]);

                //             echo HaynesUtilsWeb::remove_element_reponse($contentDecode, $webDataExists->content_type ?? "", null, $request);
                //             die();
                //         }
                //     }
                // }

                // $response1 = HaynesUtils::getResponse($link, 'GET', null, null, $request);
                // $content = $response1->getBody()->getContents();
                // if ($content != null && str_contains($content, '<title>Login</title>')) {
                //     return response()->json([
                //         'code' => 400,
                //         'success' => false,
                //         'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                //         'msg' => 'Please try again after 1 - 3 minutes',
                //     ], 400);
                // }
                // echo HaynesUtils::remove_element_reponse($response1);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                    'msg' => 'Please try again after 1 - 3 minutes',
                ], 400);
            }
        }
        if ($server == SecurityUtils::SERVER_HAYNES_PRO_WEB) {
            try {

                $statistic->update([
                    'total_haynes_pro_request' => $statistic->total_haynes_pro_request + 1,
                ]);

                if ($request != null && $request->method() == "GET") {
                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::HAYNESPRO;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if (
                        $webDataExists  != null && $webDataExists->content != null && $webDataExists->content != ""
                        &&
                        WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)
                        &&
                        !str_contains($tailLink, 'electronicSystems')
                    ) {

                        $contentDecode = base64_decode($webDataExists->content);

                        $statistic->update([
                            'total_haynes_pro_request_has_cache' => $statistic->total_haynes_pro_request_has_cache + 1,
                        ]);

                        echo HaynesUtilsWeb::remove_element_reponse($contentDecode, $webDataExists->content_type ?? "", null, $request);
                        die();
                    }
                }

                $response1 = HaynesUtilsWeb::getResponse($link, 'GET', null, null, $request);
                $content = $response1->getBody()->getContents();
                if ($content != null && (str_contains($content, '<input type="text" name="username" id="username"')
                    || str_contains($content, 'class="login"')
                    || str_contains($content, 'id="Username"')

                )) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                        'msg' => 'Please try again after 1 - 3 minutes',
                        'try_again' => true
                    ]);
                    exit;
                }
                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));

                echo HaynesUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", $redirectCount == 0 ? $response1->getStatusCode() : null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                    'msg' => 'Please try again after 1 - 3 minutes',
                    'try_again' => true
                ]);
            }
        }

        //Haynespro Truck
        if ($server == SecurityUtils::SERVER_HAYNES_PRO_TRUCK_WEB) {
            try {

                if ($request != null && $request->method() == "GET") {
                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::HAYNESPRO_TRUCK;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if (
                        $webDataExists  != null && $webDataExists->content != null && $webDataExists->content != ""
                        &&
                        WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)
                    ) {

                        $contentDecode = base64_decode($webDataExists->content);

                        // $statistic->update([
                        //     'total_haynes_pro_request_has_cache' => $statistic->total_haynes_pro_request_has_cache + 1,
                        // ]);

                        echo HaynesTruckUtilsWeb::remove_element_reponse($contentDecode, $webDataExists->content_type ?? "", null, $request);
                        die();
                    }
                }

                $statistic->update([
                    'total_haynes_pro_truck_request' => $statistic->total_haynes_pro_truck_request + 1,
                ]);

                $response1 = HaynesTruckUtilsWeb::getResponse($link, 'GET', null, null, $request);
                $content = $response1->getBody()->getContents();


                if ($content != null && (
                    str_contains($content, '<input type="text" name="username" id="username"') ||
                    str_contains($content, 'name="password"') ||
                    str_contains($content, 'Sign In'))) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::HAYNESPRO_TRUCK_OF_MAINTENANCE[0],
                        'msg' => MsgCode::HAYNESPRO_TRUCK_OF_MAINTENANCE[1],
                    ]);
                    exit;
                }
                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));
                echo HaynesTruckUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "",  $redirectCount == 0 ? $response1->getStatusCode() : null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::HAYNESPRO_TRUCK_OF_MAINTENANCE[0],
                    'msg' => MsgCode::HAYNESPRO_TRUCK_OF_MAINTENANCE[1],
                ]);
            }
        }

        //Tecdoc
        // if ($server == SecurityUtils::SERVER_TECDOC_WEB) {
        //     try {
        //         $response1 = TecDocUtilsWeb::getResponse($link, 'GET', null, null, $request);
        //         $content = $response1->getBody()->getContents();
        //         if ($content != null && str_contains($content, '<title>Login</title>')) {
        //             return ViewUtils::viewErrorPage([
        //                 'msg_code' => MsgCode::TECDOC_OF_MAINTENANCE[0],
        //                 'msg' => MsgCode::TECDOC_OF_MAINTENANCE[1],
        //             ]);
        //             exit;
        //         }
        //         echo TecDocUtilsWeb::remove_element_reponse($response1, $request);
        //         die();
        //     } catch (Exception $e) {

        //         LogUtils::error($e);
        //         return ViewUtils::viewErrorPage([
        //             'msg_code' => MsgCode::TECDOC_OF_MAINTENANCE[0],
        //             'msg' => MsgCode::TECDOC_OF_MAINTENANCE[1],
        //         ]);
        //     }
        // }

        //Mitchell repair center
        if ($server == SecurityUtils::SERVER_MITCHELL_REPAIR_CENTER_WEB) {
            try {
                $response1 = MitchellRepairCenterUtilsWeb::getResponse($link, null, "GET", null, $request);
                $content = $response1->getResponseBody();
                if ($content != null && (str_contains($content, 'Forgot Password?') || str_contains($content, 'Server Unavaiable'))) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::MITCHELL_REPAIR_CENTER_OF_MAINTENANCE[0],
                        'msg' => 'Please try again after 1 - 3 minutes',
                        'try_again' => true
                    ]);
                    exit;
                }
                echo MitchellRepairCenterUtilsWeb::remove_element_reponse($response1, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::MITCHELL_REPAIR_CENTER_OF_MAINTENANCE[0],
                    'msg' => 'Please try again after 1 - 3 minutes',
                    'try_again' => true
                ]);
                exit;
            }
        }
        //Mitchell
        if ($server == SecurityUtils::SERVER_MITCHELL_PRODEMAND_WEB) {

            $configAdmin =  ConfigAdmin::first();
            if ($configAdmin->temp_mitchell_prodemand == 'IDENTIFIX') {
                $domainMain = Helper::getDomainCurrent();
                $domainMain = str_replace('mitchell-prodemand', 'identifix', $domainMain);
                header("Location: " . $domainMain);
                exit;
            }

            if (Cache::has('PRODEMAND_FIXING')) {
                return ViewUtils::viewErrorPage([
                    'msg_code' => "MITCHELL PRODEMAND FIXING",
                    'msg' => 'Please try again after 1 - 2 minutes',
                    'try_again' => true
                ]);
                exit;
            }

            // if (str_contains($tailLink, '/api') || str_contains($tailLink, '/Api')) {
            //     $strings = [
            //         "/Main/Index",
            //         "api/Qualifier/",
            //         "/api/vehicles",
            //         "/api/bulletin/Recalls",
            //         "/Api/QuickAccess/QuickAccessByVehicle",
            //         "/Api/VehicleProfile/RepairOrders",
            //         "/Api/VehicleProfile/TopReplaced",
            //         "/Api/VehicleProfile/TopDtcs",
            //         "Api/VehicleProfile/TopSymptoms",
            //         "/Api/VehicleProfile/TopSearches",
            //         "/Api/QuickLookups/QuickLookupArticleCategories",
            //         "/api/oneview/searchterms",
            //         "/Api/OneView/MissingQualifier",
            //         "/api/bulletin/categorizedList",
            //         "/Api/Content/ArticleHeadings",
            //         "/Api/Content",
            //         "/api/Image/",
            //         "/Api/HtmlContent",
            //         "/Api/QuickAccess",
            //         "/Api/OneView",
            //         "/Api/Maintenance",
            //         "/Api/Bulletin",
            //         "/Api/PartsAndLabor",
            //         "/api/img/",
            //         "/api/article/",
            //         "/Api/ShopSettings",
            //         "/api/Vehicles",
            //         "/Api/UserInformation",
            //         "/Api/Servicemanual",
            //         "Search/Suggestions",
            //         "api/oneview/synonyms",
            //         "api/suretrack/componentTest"
            //     ];

            //     $found = false;
            //     foreach ($strings as $string) {
            //         if (strpos($tailLink, $string) !== false) {
            //             $found = true;
            //             break;
            //         }
            //     }

            //     if (!$found) { //không tìm thấy chuỗi chứa trong mảng cần
            //         echo "404";
            //         http_response_code(404);
            //         die();
            //     }
            // }

            if (
                str_contains($tailLink, 'GMMotors/log/') ||
                $tailLink == '/.env' ||
                str_contains($tailLink, 'LogJsError') ||
                $tailLink == '/actuator/health' ||
                $tailLink == '/adminer.php' ||
                str_contains($tailLink, '/index.php?pass=') ||
                $tailLink == '/stalker_portal/server/tools/auth_simple.php' ||
                str_contains($tailLink, 'sqlmanager/index.php') ||
                $tailLink == '/phpMyAdmin5.2/index.php?lang=en' ||
                str_contains($tailLink, 'Api/History')
            ) {
                return ViewUtils::viewErrorPage([
                    'msg_code' => "",
                    'msg' => "",
                ]);
                exit;
            }

            if ($tailLink == "/") {

                $arr_cookies = [];
                $arr = [];
                $str_javascript = "";
                if (is_array($arr)) {
                    foreach ($arr as $key => $value) {
                        $str_javascript =      $str_javascript .  "window.localStorage.setItem('$key', '$value');
                        ";
                    }
                    foreach ($arr_cookies as $key => $value) {
                        $str_javascript =      $str_javascript .  "setCookie('$key','$value',365);
                        ";
                    }
                    $scriptProdemand = "<script>
                    function setCookie(name,value,days) {
                        var expires = '';
                        if (days) {
                            var date = new Date();
                            date.setTime(date.getTime() + (days*24*60*60*1000));
                            expires = '; expires=' + date.toUTCString();
                        }
                        document.cookie = name + '=' + (value || '')  + expires + '; path=/';
                    }
                    $str_javascript

                    var hasRedirect = false;
                    if(hasRedirect == false ){
                        hasRedirect = true;
                        window.location.replace('https://mitchell-prodemand.$domainWithoutSub/Main/Index#|||||||||||||||||/Home');
                    }
                   
                            </script>";
                }

                return response()->view('service/mitchell_prodemand', [
                    'scriptProdemand' => $scriptProdemand
                ]);
                exit;
            }

            $statistic->update([
                'total_mitchell_prodemand_request' => $statistic->total_mitchell_prodemand_request + 1,
            ]);

            if (
                $request != null && $request->method() == "GET"
                // && $tailLink != "/Main/Index"
            ) {

                $now = Carbon::now();
                $twentyFourHoursAgo = $now->subHours(120);

                $webClass = new WebClass();
                $webClass->hours_ago = 24 * 30;
                $webClass->tail_link = $tailLink;
                $webClass->service =  PlanUtils::MITCHELL_PRODEMAND;
                $webClass->method = $request->method();

                $webDataExists =   WebDataUtils::getWebData($webClass);

                if (
                    $webDataExists  != null && $webDataExists->content != null &&
                    $webDataExists->content != ""  &&
                    $webDataExists->updated_at != null &&
                    WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)
                ) {

                    $statistic->update([
                        'total_mitchell_prodemand_request_has_cache' => $statistic->total_mitchell_prodemand_request_has_cache + 1,
                    ]);

                    echo MitchellProdemandUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                    die();
                }
            }

            try {
                $response1 = MitchellProdemandUtilsWeb::getResponse($link, null, "GET", null, $request);

                $content = $response1->getResponseBody();

                if ($content != null && str_contains($content, '<div id="login_menu_content">')) { //Cookies hoặc session lỗi

                    // $c =      ConfigAdmin::first();
                    // $lastTecDoc =  HistoryStatusServer3::where('service', PlanUtils::MITCHELL_PRODEMAND)->orderBy('id', 'desc')->first();
                    // if ($lastTecDoc  == null ||   $lastTecDoc->status !=  MsgCode::STATUS_ERR) {
                    //     $c->update([
                    //         'cookies_mitchell_prodemand_json_info' => json_encode(["status" => false, 'begin_time_err' => Helper::getTimeNowStringVietNam()])
                    //     ]);

                    //     HistoryStatusServer3::create([
                    //         'service' =>  PlanUtils::MITCHELL_PRODEMAND,
                    //         'status' => MsgCode::STATUS_ERR,
                    //     ]);

                    //     //Tìm admin
                    //     $title =  "Mitchell prodemand Stopped (Has div Login) " . $link;
                    //     PushNotificationAdminJob::dispatch(
                    //         $title,
                    //         $title,
                    //     );
                    // }

                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::MITCHELL_PRODEMAND_OF_MAINTENANCE[0],
                        'msg' => 'Please try again after 1 - 3 minutes',
                        'try_again' => true
                    ]);
                    exit;
                }
                echo MitchellProdemandUtilsWeb::remove_element_reponse($response1->getResponseBody(),  $response1->getContentType(), $response1->getStatusCode(), $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::MITCHELL_PRODEMAND_OF_MAINTENANCE[0],
                    'msg' => 'Please try again after 1 - 3 minutes',
                    'try_again' => true
                ]);
                exit;
            }
        }

        //Identifix
        if ($server == SecurityUtils::SERVER_IDENTIFIX_WEB) {
            try {

                $statistic->update([
                    'total_identifix_request' => $statistic->total_identifix_request + 1,
                ]);


                if (
                    str_contains($tailLink, "FixCollection/GetMyOpenFix")
                ) {
                    return response()->json([
                        "TotalFixes" => 0,
                        "TotalNewFixes" => 0,
                        "TotalConfirmedFixes" =>  0,
                        "TotalPostedFixes" =>  0,
                        "Fixes" => [],
                        "ServiceResult" =>  1
                    ]);
                }
                if (
                    str_contains($tailLink, "FixCollection/GetMyShopConfirmedAndPostedFix")
                ) {
                    return response()->json([
                        "TotalFixes" => 0,
                        "TotalNewFixes" => 0,
                        "TotalConfirmedFixes" => 0,
                        "TotalPostedFixes" => 0,
                        "Fixes" => [],
                        "ServiceResult" => 1
                    ]);
                }
                if (
                    str_contains($tailLink, "Shared/CheckShopCardExpiration")
                ) {
                    return response()->json([
                        "HeaderRemainingDays" => -1,
                        "PopupRemainingDays" => -2,
                    ]);
                }
                if (
                    str_contains($tailLink, "Shared/GetUserPreference")
                ) {
                    return response()->json([
                        "Data" => "0",
                        "IsSuccess" => true,
                        "ResultMessage" => null
                    ]);
                }

                if (
                    str_contains($tailLink, "CreateVehicle/RenderNewRecentVehiclePartial")
                    || str_contains($tailLink, "Default/LogOff?redirectUrl")
                ) {
                    echo "";
                    die();
                }




                if ($request != null && $request->method() == "GET" && !str_contains($tailLink, "ROID")  && !str_contains($tailLink, "User") && !str_contains($tailLink, "Check") && !str_contains($tailLink, "Quote")) {
                    $now = Carbon::now();

                    $tailLink = Helper::removeParamsFromUrl($tailLink, ['CacheId', '_']);
                    // $tailLink = Helper::removeParamsFromUrl($tailLink, ['ROID']);

                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::IDENTIFIX;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if ($webDataExists  != null && $webDataExists->content != null && $webDataExists->content != "") {

                        $statistic->update([
                            'total_identifix_request_has_cache' => $statistic->total_identifix_request_has_cache + 1,
                        ]);

                        echo IdentifixUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                        die();
                    }
                }

                $response1 = IdentifixUtilsWeb::getResponse($link, 'GET', null, null, $request);
                $content = $response1->getBody()->getContents();
                if (str_contains($tailLink, 'SessionTerminated')) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::IDENTIFIX_OF_MAINTENANCE[0],
                        'msg' => MsgCode::IDENTIFIX_OF_MAINTENANCE[1],
                    ]);
                    exit;
                }
                if ($content != null && str_contains($content, 'error-panel')) {

                    $dom = HtmlDomParser::str_get_html($content);
                    if ($dom  != null) {
                        //Error mess
                        $error = $dom->find('#error-panel h1', 0);

                        if ($error != null) {
                            return ViewUtils::viewErrorPage([
                                'msg_code' => MsgCode::ERROR[0],
                                'msg' => $error->innertext,
                            ]);
                            exit;
                        }
                    }
                }
                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));
                echo IdentifixUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", $redirectCount == 0 ? $response1->getStatusCode() : null, $request);
                die();
            } catch (ClientException $e) {
                if ($e->getResponse()->getStatusCode() === 404) {
                    echo "404 identifix";
                    http_response_code(404);
                    die();
                } else {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::IDENTIFIX_OF_MAINTENANCE[0],
                        'msg' => MsgCode::IDENTIFIX_OF_MAINTENANCE[1],
                    ], ($e->getCode() == null || $e->getCode() == 0) ? 200 : $e->getCode());
                }
            } catch (Exception $e) {
                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::IDENTIFIX_OF_MAINTENANCE[0],
                    'msg' => MsgCode::IDENTIFIX_OF_MAINTENANCE[1],
                ], ($e->getCode() == null || $e->getCode() == 0) ? 200 : $e->getCode());
            }
        }

        //Partslink24 mobile
        if ($server == SecurityUtils::SERVER_PARTSLINK24_WEB) {

            if (
                $tailLink == "/"
            ) {

                $domainMain = Helper::getDomainCurrent();
                header("Location: " . $domainMain . "/pl24-app");
                exit;
            }

            // if ($tailLink == "/pl24-app") {
            //     return response()->view('partslink24/home');
            // }


            if (
                $request != null && $request->method() == "GET"
            ) {

                $now = Carbon::now();
                $twentyFourHoursAgo = $now->subHours(120);

                $webClass = new WebClass();
                $webClass->hours_ago = 24 * 30;
                $webClass->tail_link = $tailLink;
                $webClass->service =  PlanUtils::PARTSLINK24;
                $webClass->method = $request->method();

                $webDataExists =   WebDataUtils::getWebData($webClass);

                if (
                    $webDataExists  != null && $webDataExists->content != null &&
                    $webDataExists->content != ""  &&
                    $webDataExists->updated_at != null &&
                    WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)
                ) {
                    echo Partslink24UtilsMobileWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                    die();
                }
            }



            if (str_contains($tailLink, "ext/api/1.0/session")) {

                $configAdmin =      ConfigAdmin::first();
                $access_token = "";

                $cookies_str = Cache::get(json_encode(['access_token_partslink24']));
                if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_partslink24_str)) {
                    $cookies_str = $configAdmin->cookies_partslink24_str;
                }

                $jsonToken = json_decode($cookies_str, true);
                if (isset($jsonToken['access_token'])) {
                    $access_token = $jsonToken['access_token'];
                }

                return response()->json([
                    "securables" => null,
                    "status" => null,
                    "title" => null,
                    "refreshToken" => null,
                    "token" => [
                        "access_token" =>     $access_token,
                        "token_type" => "Bearer",
                        "expires_in" => 600,
                        "scope" => "mini_parts landrover_parts bmwmotorrad_parts vwclassic_parts porscheclassic_parts citroenDs_parts cart bmwclassic_parts dacia_parts smart_parts citroen_parts mercedesunimog_parts bentley_parts peugeot_parts bmw_parts jaguar_parts vw_parts cupra_parts audi_parts porsche_parts mercedestrucks_parts renault_parts mercedes_parts seat_parts skoda_parts pl24-arch2-cart alpine_parts vn_parts mercedesvans_parts miniclassic_parts bmwmotorradclassic_parts pl24-usage"
                    ]
                ], 200);
            }


            try {
                $statistic->update([
                    'total_partslink_request' => $statistic->total_partslink_request + 1,
                ]);

                $response1 = Partslink24UtilsMobileWeb::getResponse($link, 'GET', null, null, $request);


                if ($response1 instanceof Exception) {
                    return response()->json(['error' => 'Too many requests. Please try again later.'], 429);
                }

                $content = $response1->getBody()->getContents();
                if (str_contains($tailLink, 'SessionTerminated')) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::PARTSLINK24_OF_MAINTENANCE[0],
                        'msg' => MsgCode::PARTSLINK24_OF_MAINTENANCE[1],
                    ]);
                    exit;
                }

                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));
                echo Partslink24UtilsMobileWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "",  $redirectCount == 0 ?  $response1->getStatusCode() : null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::PARTSLINK24_OF_MAINTENANCE[0],
                    'msg' => MsgCode::PARTSLINK24_OF_MAINTENANCE[1],
                ]);
            }
        }

        // //Partslink24 PC - GET
        // if ($server == SecurityUtils::SERVER_PARTSLINK24_WEB) {
        //     sleep(1);

        //     $redirects = [
        //         '/partslink24/launchCatalog.do?service=audi_parts' => '/p5/latest/p5.html#%2Fp5vwag~audi_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=bentley_parts' => '/p5/latest/p5.html#%2Fp5vwag~bentley_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=bmw_parts' => '/p5/latest/p5.html#%2Fp5bmw~bmw_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=bmwclassic_parts' => '/p5/latest/p5.html#%2Fp5bmw~bmwclassic_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=bmwmotorrad_parts' => '/p5/latest/p5.html#%2Fp5bmw~bmwmotorrad_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=bmwmotorradclassic_parts' => '/p5/latest/p5.html#%2Fp5bmw~bmwmotorradclassic_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=busandtrucks_parts' => '/p5/latest/p5.html#%2Fp5vwag~busandtrucks_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=cupra_parts' => '/p5/latest/p5.html#%2Fp5vwag~cupra_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=dacia_parts' => '/p5/latest/p5.html#%2Fp5renault~dacia_parts~en~~~~~',

        //         '/partslink24/launchCatalog.do?service=fordp_parts' => '/ford/fordp_parts/vehicle.action?mode=A0LW0GTGT&lang=en&startup=true&upds=2025.05.20+15%3A45%3A09+CEST',
        //         '/partslink24/launchCatalog.do?service=fordt_parts' => '/ford/fordt_parts/vehicle.action?mode=A0LW0GTGT&lang=en&startup=true&upds=2025.05.20+15%3A45%3A09+CEST',

        //         '/partslink24/launchCatalog.do?service=infiniti_parts' => '/nissan/infiniti_parts/vehicle.action?mode=A0LW0GTGT&lang=en&startup=true&upds=2025.05.09+09%3A24%3A22+CEST',
        //         '/partslink24/launchCatalog.do?service=jaguar_parts' => '/p5/latest/p5.html#%2Fp5jlr~jaguar_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=landrover_parts' => '/p5/latest/p5.html#%2Fp5jlr~landrover_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=man_parts' => '/p5/latest/p5.html#%2Fp5man~man_parts~en~~~~~',

        //         '/partslink24/launchCatalog.do?service=mercedes_parts' => '/p5/latest/p5.html#%2Fp5daimler~mercedes_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=mercedesclassic_parts' => '/p5/latest/p5.html#%2Fp5daimler~mercedesclassic_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=mercedestrucks_parts' => '/p5/latest/p5.html#%2Fp5daimler~mercedestrucks_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=mercedesunimog_parts' => '/p5/latest/p5.html#%2Fp5daimler~mercedesunimog_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=mercedesvans_parts' => '/p5/latest/p5.html#%2Fp5daimler~mercedesvans_parts~en~~~~~',

        //         '/partslink24/launchCatalog.do?service=mini_parts' => '/p5/latest/p5.html#%2Fp5bmw~mini_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=miniclassic_parts' => '/p5/latest/p5.html#%2Fp5bmw~mini_parts~en~~~~~',

        //         '/partslink24/launchCatalog.do?service=mmc_parts' => '/p5/latest/p5.html#%2Fp5mitsubishi~mmc_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=nissan_parts' => '/nissan/nissan_parts/vehicle.action?mode=A0LW0GTGT&lang=en&startup=true&upds=2025.05.09+09%3A24%3A22+CEST',
        //         '/partslink24/launchCatalog.do?service=opel_parts' => '/opel/opel_parts/vehicle.action?mode=A0LW0GTGT&lang=en&startup=true&upds=2025.05.16+21%3A17%3A41+CEST',
        //         '/partslink24/launchCatalog.do?service=polestar_parts' => '/volvo/polestar_parts/vehicle.action?mode=A0LW0GTGT&lang=en&startup=true&upds=2025.05.20+13%3A22%3A07',

        //         '/partslink24/launchCatalog.do?service=porsche_parts' => '/p5/latest/p5.html#%2Fp5vwag~porsche_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=porscheclassic_parts' => '/p5/latest/p5.html#%2Fp5vwag~porscheclassic_parts~en~~~~~',

        //         '/partslink24/launchCatalog.do?service=renault_parts' => '/p5/latest/p5.html#%2Fp5renault~renault_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=seat_parts' => '/p5/latest/p5.html#%2Fp5vwag~seat_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=skoda_parts' => '/p5/latest/p5.html#%2Fp5vwag~skoda_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=smart_parts' => '/p5/latest/p5.html#%2Fp5daimler~smart_parts~en~~~~~',

        //         '/partslink24/launchCatalog.do?service=vauxhall_parts' => '/opel/vauxhall_parts/vehicle.action?mode=A0LW0GTGT&lang=en&startup=true&upds=2025.05.16+21%3A17%3A41+CEST',


        //         '/partslink24/launchCatalog.do?service=vw_parts' => '/p5/latest/p5.html#%2Fp5vwag~vw_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=vwclassic_parts' => '/p5/latest/p5.html#%2Fp5vwag~vwclassic_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=vn_parts' => '/p5/latest/p5.html#%2Fp5vwag~vn_parts~en~~~~~',
        //         '/partslink24/launchCatalog.do?service=volvo_parts' => '/volvo/volvo_parts/vehicle.action?mode=A0LW0GTGT&lang=en&startup=true&upds=2025.05.20+13%3A22%3A07',

        //     ];

        //     if (array_key_exists($tailLink, $redirects)) {
        //         header("Location: " . $redirects[$tailLink]);
        //         exit();
        //     }

        //     if (str_contains($tailLink, "/partslink24/launchCatalog.do?service=")) {
        //         $parsedUrl = parse_url($tailLink);
        //         if (isset($parsedUrl['query'])) {
        //             parse_str($parsedUrl['query'], $queryParams);
        //             if (isset($queryParams['service'])) {
        //                 $service = $queryParams['service'];
        //                 $redirectUrl = "/p5/latest/p5.html#%2Fp5vwag~{$service}~en~~~~~";
        //                 header("Location: " . $redirectUrl);
        //                 exit();
        //             }
        //         }
        //     }



        //     if (
        //         $tailLink == "/"
        //     ) {

        //         $domainMain = Helper::getDomainCurrent();
        //         header("Location: " . $domainMain . "/partslink24/startup.do");
        //         exit;
        //     }

        //     if (str_contains($tailLink, "/authorize")) {

        //         $configAdmin =      ConfigAdmin::first();
        //         $access_token = "";

        //         $cookies_str = Cache::get(json_encode(['access_token_partslink24']));
        //         if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_partslink24_str)) {
        //             $cookies_str = $configAdmin->cookies_partslink24_str;
        //         }

        //         $jsonToken = json_decode($cookies_str, true);
        //         if (isset($jsonToken['access_token'])) {
        //             $access_token = $jsonToken['access_token'];
        //         }
        //         $access_token = Partslink24UtilsWeb::fakeTokenPartslink();
        //         return response()->json([
        //             "securables" => null,
        //             "status" => null,
        //             "title" => null,
        //             "refreshToken" => null,
        //             "token" => [
        //                 "access_token" =>     $access_token,
        //                 "token_type" =>  "Bearer",
        //                 "expires_in" =>  600,
        //                 "scope" =>  "",
        //                 "session_status" =>  "gone",
        //                 "lcSessionId" =>  null

        //             ]
        //         ], 200);
        //     }
        //     if (str_contains($tailLink, "/session")) {

        //         $configAdmin =      ConfigAdmin::first();
        //         $access_token = "";

        //         $cookies_str = Cache::get(json_encode(['access_token_partslink24']));
        //         if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_partslink24_str)) {
        //             $cookies_str = $configAdmin->cookies_partslink24_str;
        //         }

        //         $jsonToken = json_decode($cookies_str, true);
        //         if (isset($jsonToken['access_token'])) {
        //             $access_token = $jsonToken['access_token'];
        //         }

        //         $access_token = Partslink24UtilsWeb::fakeTokenPartslink();

        //         return response()->json([
        //             "securables" => null,
        //             "status" => null,
        //             "title" => null,
        //             "refreshToken" => null,
        //             "token" => [
        //                 "access_token" => $access_token,
        //                 "token_type" => "Bearer",
        //                 "expires_in" => 600,
        //                 "scope" => "mini_parts landrover_parts bmwmotorrad_parts vwclassic_parts porscheclassic_parts cart busandtrucks_parts bmwclassic_parts dacia_parts smart_parts mercedesunimog_parts bentley_parts bmw_parts jaguar_parts mmc_parts vw_parts cupra_parts audi_parts porsche_parts mercedestrucks_parts renault_parts man_parts mercedes_parts seat_parts skoda_parts pl24-arch2-cart alpine_parts vn_parts mercedesvans_parts miniclassic_parts bmwmotorradclassic_parts pl24-usage"
        //             ]
        //         ], 200);
        //     }

        //     if (
        //         $request != null && $request->method() == "GET"
        //     ) {

        //         $now = Carbon::now();
        //         $twentyFourHoursAgo = $now->subHours(120);

        //         $webClass = new WebClass();
        //         $webClass->hours_ago = 24 * 30;
        //         $webClass->tail_link = $tailLink;
        //         $webClass->service =  PlanUtils::PARTSLINK24;
        //         $webClass->method = $request->method();

        //         $webDataExists =   WebDataUtils::getWebData($webClass);

        //         $validNotDemo = true;
        //         if (
        //             $webDataExists  != null && $webDataExists->content != null &&
        //             $webDataExists->content != ""
        //         ) {
        //             $data = json_decode(base64_decode($webDataExists->content), true);

        //             if (json_last_error() === JSON_ERROR_NONE && is_array($data) && isset($data['demo'])) {
        //                 if ($data['demo'] === true) {
        //                     $validNotDemo = false;
        //                 } else {
        //                     $validNotDemo = true;
        //                 }
        //             } else {
        //                 $validNotDemo = true;
        //             }
        //         }


        //         if (
        //             $webDataExists  != null && $webDataExists->content != null &&
        //             $webDataExists->content != ""  &&
        //             $webDataExists->updated_at != null &&
        //             WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)
        //             &&  $validNotDemo
        //         ) {
        //             echo Partslink24UtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
        //             die();
        //         }
        //     }

        //     sleep(1);

        //     try {
        //         $statistic->update([
        //             'total_partslink_request' => $statistic->total_partslink_request + 1,
        //         ]);

        //         $response1 = Partslink24UtilsWeb::getResponse($link, 'GET', null, null, $request);


        //         if ($response1 instanceof Exception) {
        //             return response()->json(['error' => 'Too many requests. Please try again later.'], 429);
        //         }

        //         $content = $response1->getBody()->getContents();
        //         if (str_contains($tailLink, 'SessionTerminated')) {
        //             return ViewUtils::viewErrorPage([
        //                 'msg_code' => MsgCode::PARTSLINK24_OF_MAINTENANCE[0],
        //                 'msg' => MsgCode::PARTSLINK24_OF_MAINTENANCE[1],
        //             ]);
        //             exit;
        //         }

        //         $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));
        //         echo Partslink24UtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "",  $redirectCount == 0 ?  $response1->getStatusCode() : null, $request);
        //         die();
        //     } catch (Exception $e) {

        //         LogUtils::error($e);
        //         return ViewUtils::viewErrorPage([
        //             'msg_code' => MsgCode::PARTSLINK24_OF_MAINTENANCE[0],
        //             'msg' => MsgCode::PARTSLINK24_OF_MAINTENANCE[1],
        //         ]);
        //     }
        // }

        //Kds gds
        if ($server == SecurityUtils::SERVER_KDS_GDS_WEB) {
            $file = public_path('kdsgds_web/index.html');

            if (File::exists($file)) {
                return response()->file($file);
            } else {
                abort(404, 'File not found');
            }
        }

        //ETKA
        if ($server == SecurityUtils::SERVER_ETKA_WEB) {

            if ($tailLink == "/") {
                return response()->view('etka/choose_manu', [
                    'domain' => Helper::getDomainCurrentWithoutSubAndNoHttp(),
                    'agency_sub' => $request->agency_sub,
                ]);
                exit;
            }


            try {

                $statistic->update([
                    'total_identifix_request' => $statistic->total_identifix_request + 1,
                ]);


                if ($request != null && $request->method() == "GET" &&  str_contains($tailLink, '.png')) {
                    $now = Carbon::now();

                    $tailLink = Helper::removeParamsFromUrl($tailLink, ['_']);
                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::ETKA;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if ($webDataExists  != null && $webDataExists->content != null && $webDataExists->content != "") {

                        // $statistic->update([
                        //     'total_identifix_request_has_cache' => $statistic->total_identifix_request_has_cache + 1,
                        // ]);

                        echo ETKAUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                        die();
                    }
                }

                $response1 = ETKAUtilsWeb::getResponse($link, 'GET', null, null, $request);
                $content = $response1->getBody()->getContents();

                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));
                echo ETKAUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", $redirectCount == 0 ? $response1->getStatusCode() : null, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::ETKA_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ETKA_OF_MAINTENANCE[1],
                ], ($e->getCode() == null || $e->getCode() == 0) ? 200 : $e->getCode());
            }
        }

        if ($server == SecurityUtils::SERVER_TECDOC_WEB) {
            $file = public_path('tecdoc_web/index.html');

            if (File::exists($file)) {
                return response()->file($file);
            } else {
                abort(404, 'File not found');
            }
        }

        //Partsouq
        if ($server == SecurityUtils::SERVER_PARTSOUQ_WEB) {
            try {
                $response1 = PartsouqUtilsWeb::getResponse($link, 'GET', null, null, $request);
                $content = $response1->getBody()->getContents();
                if (str_contains($tailLink, 'SessionTerminated')) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::PARTSOUQ_OF_MAINTENANCE[0],
                        'msg' => MsgCode::PARTSOUQ_OF_MAINTENANCE[1],
                    ]);
                    exit;
                }
                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));
                echo PartsouqUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "",  $redirectCount == 0 ?  $response1->getStatusCode() : null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::PARTSOUQ_OF_MAINTENANCE[0],
                    'msg' => MsgCode::PARTSOUQ_OF_MAINTENANCE[1],
                ]);
            }
        }

        //Ford PTS
        if ($server == SecurityUtils::SERVER_FORD_PTS_WEB) {
            try {

                if (
                    str_contains($tailLink, '/logout') ||
                    str_contains($tailLink, '/vdirs/common/privacypolicycglobal.asp')
                ) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => "",
                        'msg' => "",
                    ]);
                    exit;
                }

                $statistic->update([
                    'total_ford_pts_request' => $statistic->total_ford_pts_request + 1,
                ]);

                if (str_contains($tailLink, "CheckTokenExpiration")) {
                    return response()->json([
                        'expirationTime' => "2026-02-07T23:27:05.0000000Z",
                        'message' => "Token is valid.",
                        'success' => true,
                    ], 200);
                }


                if ($request != null && $request->method() == "GET") {
                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::FORD_PTS;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if (
                        $webDataExists  != null &&
                        $webDataExists->content != null &&
                        $webDataExists->content != ""
                        && WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)

                    ) {

                        $contentSaved = base64_decode($webDataExists->content);

                        if (!str_contains($contentSaved, 'Service Unavailable') && !str_contains($contentSaved, 'Your current subscription has expired')) {
                            echo FordUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                            die();
                        }
                    }
                }

                $response1 = FordUtilsWeb::getResponse($link, 'GET', null,  $request);
                $content = $response1->getResponseBody();

                if (str_contains($content, '<title>Login - Ford Service Information</title>')) {
                    return response()->json([
                        'code' => 401,
                        'success' => false,
                        'msg_code' => 'AUTHEN ERROR',
                        'msg' => 'AUTHEN ERROR',
                    ], 401);

                    exit;
                }

                echo FordUtilsWeb::remove_element_reponse($response1->getResponseBody(), $response1->getContentType(), $response1->getStatusCode(), $request);
                die();
            } catch (Exception $e) {
                if (isset($e)) {

                    $mess = $e->getMessage();
                    if (str_contains($mess, 'Could not resolve host')) {
                        return response()->json([
                            'code' => 500,
                            'success' => false,
                            'msg_code' => 'NETWORK_ERROR',
                            'msg' =>  $mess,
                        ], 500);
                    }
                }

                if (str_contains($e->getMessage(), 'was not closed cleanly')) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::FORD_PTS_OF_MAINTENANCE[0],
                        'msg' => MsgCode::FORD_PTS_OF_MAINTENANCE[1],
                    ], 500);
                }

                return response()->json([
                    'code' => 500,
                    'success' => false,
                    'msg_code' => MsgCode::FORD_PTS_OF_MAINTENANCE[0],
                    'msg' => $e->getMessage(),
                ], 500);
                exit;
            }
        }


        //Toyota TIS
        if ($server == SecurityUtils::SERVER_TOYOTA_TIS_WEB) {
            try {

                $statistic->update([
                    'total_toyota_tis_request' => $statistic->total_toyota_tis_request + 1,
                ]);

                if ($request != null && $request->method() == "GET" && !str_contains($tailLink, 't3Portal')) {
                    $now = Carbon::now();
                    $tailLinkSave = Helper::removeParamsFromUrlToyota($tailLink);

                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLinkSave;
                    $webClass->service =  PlanUtils::TOYOTA_TIS;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if ($webDataExists  != null && $webDataExists->content != null && $webDataExists->content != "") {

                        $statistic->update([
                            'total_toyota_tis_request_has_cache' => $statistic->total_toyota_tis_request_has_cache + 1,
                        ]);

                        echo ToyotaUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                        die();
                    }
                }

                $response1 = ToyotaUtilsWeb::getResponse($link, 'GET', null, null, $request);
                $content = $response1->getBody()->getContents();

                if (str_contains($content, '<title>Techinfo prelogin desktop</title>')) {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::TOYOTA_TIS_OF_MAINTENANCE[0],
                        'msg' => MsgCode::TOYOTA_TIS_OF_MAINTENANCE[1],
                    ]);
                    exit;
                }
                $redirectCount = count($response1->getHeader('X-Guzzle-Redirect-History'));
                echo ToyotaUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", $redirectCount == 0 ? $response1->getStatusCode() : null, $request);
                die();
            } catch (ClientException $e) {
                if ($e->getResponse()->getStatusCode() === 404) {
                    echo "404 toyota";
                    http_response_code(404);
                    die();
                } else {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::TOYOTA_TIS_OF_MAINTENANCE[0],
                        'msg' => MsgCode::TOYOTA_TIS_OF_MAINTENANCE[1],
                    ], ($e->getCode() == null || $e->getCode() == 0) ? 200 : $e->getCode());
                }
            } catch (Exception $e) {
                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::TOYOTA_TIS_OF_MAINTENANCE[0],
                    'msg' => MsgCode::TOYOTA_TIS_OF_MAINTENANCE[1],
                ], ($e->getCode() == null || $e->getCode() == 0) ? 200 : $e->getCode());
            }
        }
    }

    /**
     * Thông tin server
     */
    public function indexPost(Request $request, $slug)
    {
        $body = $request->all();
        if ($request != null) {
            $body = array_diff_key($request->all(), $request->query());
            $body =  Helper::removeBodyExcessive($body);
        }



        $domain_request = $_SERVER['HTTP_HOST'];
        $tailLink = $_SERVER["REQUEST_URI"];

        $server = $request->data3_server;
        $domain = $request->data3_domain;

        if ($server == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "NO SERVER",
            ], 400);
        }
        if ($domain == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::ERROR[0],
                'msg' => "NO DOMAIN",
            ], 400);
        }

        $link = $domain .  $tailLink;

        $response1 =  null;
        $statistic =  Statistic::first();
        $link = $domain .  $tailLink;
        $statistic->update([
            'total_request' => $statistic->total_request + 1,
            'total_request_not_get' => $statistic->total_request_not_get + 1,
        ]);


        //Autodata
        if ($server == SecurityUtils::SERVER_AUTO_DATA_WEB_SAVED) {
            $now = Carbon::now();
            $twentyFourHoursAgo = $now->subHours(120);

            $webClass = new WebClass();
            $webClass->hours_ago = 24 * 30;
            $webClass->tail_link = $tailLink;
            $webClass->service =  PlanUtils::AUTODATA;
            $webClass->method = $request->method();
            $webClass->bodyRequest =  $body;

            if (str_contains($tailLink, "engines/codes")) {
                if ($webClass->bodyRequest['freetext'] == null) {
                    $webClass->bodyRequest['freetext'] = "";
                }
            }

            $webDataExists =   WebDataUtilsNasHttp::getWebData($webClass);
            if (is_array($webDataExists) && $webDataExists['code'] == 404) {
                echo "no data";
                http_response_code(404);
                die();
            }

            if (
                $webDataExists  != null && $webDataExists->content != null && $webDataExists->content != ""
            ) {
                echo AutoDataUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", $webDataExists->status_code, $request, false);
                die();
            }
        }
        if ($server == SecurityUtils::SERVER_AUTO_DATA_WEB || $server == SecurityUtils::SERVER_AUTO_DATA) {
            try {

                if ($request != null && $request->method() == "POST") {
                    $now = Carbon::now();
                    $twentyFourHoursAgo = $now->subHours(120);

                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLink;
                    $webClass->service =  PlanUtils::AUTODATA;
                    $webClass->method = $request->method();

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if ($webDataExists  != null && $webDataExists->content != null && $webDataExists->content != "") {
                        echo AutoDataUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                        die();
                    }
                }

                $response1 = AutoDataUtilsWeb::getResponse($link, 'POST', $body, null, $request, null);
                if ($response1 instanceof Exception) {
                    return response()->json(['error' => 'Too many requests. Please try again later.'], 429);
                }

                $w2 = [
                    'engine-oil',
                    'tyres',
                    "tyre-pressure-monitoring-system",
                    "service-indicator",
                    "key-programming",
                    "wheel-alignment",
                    "battery-disconnection-and-reconnection",
                    "electric-parking-brake",
                    "diesel-exhaust-gas-aftertreatment",
                    "camshaft-drive-system",
                    "service-advisor",
                    "static-content",
                    "diagnostic-trouble-codes",
                ];

                $w1orw2 = "w1";
                if ($request != null && in_array($request->route_name, $w2) && $request->vehicle_id != null) {
                    $w1orw2 = "w2";


                    return response()->json([
                        "action" => 1,
                        "message" => "All fine. Redirecting to: /$w1orw2/$request->route_name/$request->vehicle_id",
                        "back" => "/$w1orw2/$request->route_name/$request->vehicle_id", //?cacao=drcar
                        // "handle_from" => "drcar",
                    ], 200);
                }

                echo AutoDataUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);

                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::AUTODATA_OF_MAINTENANCE[0],
                    'msg' => MsgCode::AUTODATA_OF_MAINTENANCE[1],
                ]);
            }
        }

        if ($server == SecurityUtils::SERVER_AUTO_DATA_ITALY_WEB) {
            try {


                $response1 = AutoDataItalyUtilsWeb::getResponse($link, 'POST', $body, null, $request, null);
                if ($response1 instanceof Exception) {
                    return response()->json(['error' => 'Too many requests. Please try again later.'], 429);
                }

                $w2 = [
                    'engine-oil',
                    'tyres',
                    "tyre-pressure-monitoring-system",
                    "service-indicator",
                    "key-programming",
                    "wheel-alignment",
                    "battery-disconnection-and-reconnection",
                    "electric-parking-brake",
                    "diesel-exhaust-gas-aftertreatment",
                    "camshaft-drive-system",
                    "service-advisor",
                    "static-content",
                    "diagnostic-trouble-codes",
                ];

                $w1orw2 = "w1";
                if ($request != null && in_array($request->route_name, $w2) && $request->vehicle_id != null) {
                    $w1orw2 = "w2";


                    return response()->json([
                        "action" => 1,
                        "message" => "All fine. Redirecting to: /$w1orw2/$request->route_name/$request->vehicle_id",
                        "back" => "/$w1orw2/$request->route_name/$request->vehicle_id", //?cacao=drcar
                        // "handle_from" => "drcar",
                    ], 200);
                }

                echo AutoDataItalyUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);

                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::AUTODATA_OF_MAINTENANCE[0],
                    'msg' => MsgCode::AUTODATA_OF_MAINTENANCE[1],
                ]);
            }
        }
        //Alldata eu
        if ($server == SecurityUtils::SERVER_ALL_DATA) {

            //Chặn logout
            if (
                str_contains($tailLink, "login") ||   str_contains($tailLink, "logout") || str_contains($tailLink, "session") ||  str_contains($tailLink, "check")
            ) {
                echo "0";
                die();
            }

            $statistic->update([
                'total_alldata_eu_request_not_get' => $statistic->total_alldata_eu_request_not_get + 1,
            ]);


            $response1 = AllDataUtils::getResponse($link, 'POST', null, $body, $request);
        }
        if ($server == SecurityUtils::SERVER_ALL_DATA_WEB) {
            try {

                if (
                    str_contains($tailLink, "save") || str_contains($tailLink, "load") ||
                    str_contains($tailLink, "/alldata/vehicle/information/")
                ) {
                    return response()->json([
                        "engineId" => "AUDIEngine_DKLA",
                        "modelId" => "Model_AUDI_A1_CITYCARVER",
                        "id" => "Vehicle_138405_GB_CC_2021",
                        "manufacturerId" => "Manufacturer_5",
                        "vehicle" => "null",
                        "engine" => "null",
                        "model" => "null",
                        "make" => "null",
                        "fullVehicle" => "null",
                        "fullEngine" => "null",
                        "sessionBMWVehicle" => "null",
                        "sessionVehicle" =>  "null",
                        "VIN" => "",
                        "year" => "null",
                        "supported" => "null",
                        "componentId" => "ALLDATA_Classic_Component_611",
                        "infotype" => "ALLDATA_Classic_InfoType_21",
                        "hasData" => "null",
                        "vin" => "null"

                    ], 200);
                }




                if (
                    str_contains($tailLink, "akam/13")
                ) {
                    return response()->json([
                        'code' => 200,
                        'success' => true,
                        'fr-app' => "fr-app"
                    ], 200);
                }


                //Chặn logout
                if (
                    str_contains($tailLink, "login") ||   str_contains($tailLink, "logout") || str_contains($tailLink, "session") ||  str_contains($tailLink, "check")
                ) {
                    echo "0";
                    die();
                }


                $referer = $request->headers->get('referer');
                $pathReferer = parse_url($referer, PHP_URL_PATH);
                $tailLinkSave = Helper::removeParamsFromUrl($tailLink, ['_']);
                //Thêm vào để xử lý 2 server speed và original
                if (str_contains($referer, "alldata-eu")) {
                    if (strpos($tailLinkSave, 'fr=eu') === false) {
                        if (strpos($tailLinkSave, '?') !== false) {
                            $tailLinkSave .= '&fr=eu';
                        } else {
                            $tailLinkSave .= '?fr=eu';
                        }
                    }
                }

                if ($request != null && $request->method() == "POST") {
                    if (
                        str_contains($tailLink, '.js') ||
                        str_contains($tailLink, '.css') ||
                        str_contains($tailLink, '.properties') ||
                        str_contains($tailLink, '.png')  ||
                        str_contains($tailLink, '.svg') ||
                        str_contains($tailLink, '.woff') ||
                        // || $pathReferer == "/alldata/vehicle/"

                        str_contains($tailLink, "/alldata/vehicle/")
                    ) {

                        $webClass = new WebClass();
                        $webClass->hours_ago = 24 * 30;
                        $webClass->tail_link = $tailLinkSave;
                        $webClass->service =  PlanUtils::ALLDATA;
                        $webClass->method = $request->method();

                        $webDataExists =   WebDataUtils::getWebData($webClass);

                        //   dd(   $webDataExists  );

                        if (
                            $webDataExists  != null  && $webDataExists->content != null &&
                            $webDataExists->updated_at != null &&
                            WebDataUtils::checkDateWithinRange($webDataExists->updated_at, 45)
                        ) {

                            $body = base64_decode($webDataExists->content);
                            if ($body == "" && str_contains(".js",  $tailLink)  && str_contains(".css",  $tailLink)) {
                                abort(404);
                            }
                            if (str_contains($body, '<TITLE>Access Denied</TITLE>')) {
                                abort(404);
                            }

                            if (!str_contains($body, 'Whoops!, Something went wrong.')) {
                                header('aaacache-fr-server: true');
                                header('baacache-fr-server: true');
                                echo AllDataUtilsWeb::remove_element_reponse($body, $webDataExists->content_type ?? "", null, $request);
                                die();
                            }
                        }
                    }
                }



                $statistic->update([
                    'total_alldata_eu_request_not_get' => $statistic->total_alldata_eu_request_not_get + 1,
                ]);
                $response1 = AllDataUtilsWeb::getResponse($link, 'POST', null, $body, $request);


                if ($request != null && $request->method() == "POST" && str_contains($tailLink, '/search')) {
                    echo AllDataUtilsWeb::remove_element_reponse($response1->getResponseBody(),  $response1->getContentType(), null, $request);
                    die();
                }
                echo AllDataUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[1],
                ], 400);
            }
        }
        if ($server == SecurityUtils::SERVER_ALL_DATA_EU_WEB_CUSTOM) {
            //Chặn logout
            if (
                str_contains($tailLink, "login") ||   str_contains($tailLink, "logout") || str_contains($tailLink, "session") ||  str_contains($tailLink, "check")
            ) {
                echo "0";
                die();
            }

            try {
                $statistic->update([
                    'total_alldata_eu_request_not_get' => $statistic->total_alldata_eu_request_not_get + 1,
                ]);
                $response1 = AllDataUtilsWeb::getResponse($link, 'POST', null, $body, $request);
                echo HaynesUtils::remove_element_reponse($response1, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ALLDATA_EU_V1_OF_MAINTENANCE[1],
                ], 400);
            }
        }
        //Alldata us
        if ($server == SecurityUtils::SERVER_ALL_DATA_US_V2) {
            if (strpos($request->getContent() ?? "", '{"sensor_data":') === 0) {
                return response()->json(["success" => true], 200);
            }

            try {
                $statistic->update([
                    'total_alldata_us_request_not_get' => $statistic->total_alldata_us_request_not_get + 1,
                ]);
                $response1 = AllDataUSv2UtilsWeb::getResponse($link, null, "POST", $body, $request);
                echo AllDataUSv2UtilsWeb::remove_element_reponse($response1->getResponseBody(),  $response1->getContentType(), null, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[1],
                ], 400);
            }
        }
        if ($server == SecurityUtils::SERVER_ALL_DATA_US_V2_WEB) {


            //tắt lưu lịch sử
            if ($tailLink == "/ADAG/repair/ADConnect/v5/account/recent_cars") {
                $domainMain = Helper::getDomainCurrentWithoutSubAndNoHttp();
                return response()->json(array(
                    'recentVehicles' =>
                    array(),
                    '_links' =>
                    array(
                        'self' =>
                        array(
                            'href' => 'https://my-alldata.' . $domainMain . '/ADAG/repair/ADConnect/v5/account/recent_cars',
                        ),
                    ),
                ), 200);
            }
            //tắt sensor
            if (strpos($request->getContent() ?? "", '{"sensor_data":') === 0) {
                return response()->json(["success" => true], 200);
            }
            //tắt thêm user
            if (str_contains($tailLink, 'ADAG/sso/mpm') || str_contains($tailLink, 'ADAG/sso/mpm/users')) {
                return response()->json(["success" => true], 200);
            }

            try {

                $statistic->update([
                    'total_alldata_us_request_not_get' => $statistic->total_alldata_us_request_not_get + 1,
                ]);
                $response1 = AllDataUSv2UtilsWeb::getResponse($link, null, "POST", $body, $request);
                echo AllDataUSv2UtilsWeb::remove_element_reponse($response1->getResponseBody(),  $response1->getContentType(), null, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);

                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ALLDATA_US_V2_OF_MAINTENANCE[1],
                ]);
            }
        }
        //Haynespro  Car
        if ($server == SecurityUtils::SERVER_HAYNES_PRO) {
            try {
                $statistic->update([
                    'total_haynes_pro_not_get' => $statistic->total_haynes_pro_not_get + 1,
                ]);
                $response1 = HaynesUtils::getResponse($link, 'POST', null, $body, $request);
                echo HaynesUtils::remove_element_reponse($response1, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                    'msg' => 'Please try again after 1 - 3 minutes',
                ], 400);
            }
        }
        if ($server == SecurityUtils::SERVER_HAYNES_PRO_WEB) {
            try {
                $statistic->update([
                    'total_haynes_pro_not_get' => $statistic->total_haynes_pro_not_get + 1,
                ]);
                $response1 = HaynesUtilsWeb::getResponse($link, $request->method(), null, $body, $request);
                echo HaynesUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                    'msg' => 'Please try again after 1 - 3 minutes',
                    'try_again' => true
                ]);
            }
        }

        //Haynespro  Truck
        if ($server == SecurityUtils::SERVER_HAYNES_PRO_TRUCK_WEB) {
            try {


                if (str_contains($link, 'touch/site/layout/modelDetail?typeId=')) {
                    $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $link =  str_replace("https://www.workshopdata.com", $path, $link);
                    header("Location: $link");
                    die();
                }
                $statistic->update([
                    'total_haynes_pro_truck_request_not_get' => $statistic->total_haynes_pro_truck_request_not_get + 1,
                ]);

                $response1 = HaynesTruckUtilsWeb::getResponse($link, 'POST', null, $body, $request);
                echo HaynesTruckUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (Exception $e) {
                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
                    'msg' => 'Please try again after 1 - 3 minutes',
                    'try_again' => true
                ]);
            }
        }

        //Mitchell repair center
        if ($server == SecurityUtils::SERVER_MITCHELL_REPAIR_CENTER_WEB) {

            if (str_contains($link, 'CheckSession')) {
                return response()->json(array(
                    'd' =>
                    array(
                        '__type' => 'com.mitchell.crs.web.lib.SessionTimeOutServiceReturnResult',
                        'IsSessionExpired' => false,
                        'RedirectUrl' => '/SessionExpired.aspx',
                        'CallbackType' => 'ArticleListCallback',
                        'CustomValue' => $request->input['CustomValue'] ?? null,
                        'IsSubscribedTopic' => false,
                        'GUID' => NULL,
                        'DS' => 0,
                        'Message' => NULL,
                        'refId' => NULL,
                        'PositionLeft' => 0,
                        'PositionTop' => 0,
                    ),
                ), 200);
            }

            try {
                $response1 = MitchellRepairCenterUtilsWeb::getResponse($link, null, 'POST', $body, $request);
                echo MitchellRepairCenterUtilsWeb::remove_element_reponse($response1, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::MITCHELL_REPAIR_CENTER_OF_MAINTENANCE[0],
                    'msg' => $e->getMessage(),
                ]);
            }
        }
        //Mitchell prodemand 
        if ($server == SecurityUtils::SERVER_MITCHELL_PRODEMAND_WEB) {

            http_response_code(200);
            echo "404";
            die();

            if (Cache::has('PRODEMAND_FIXING')) {
                return ViewUtils::viewErrorPage([
                    'msg_code' => "MITCHELL PRODEMAND FIXING",
                    'msg' => 'Please try again after 1 - 2 minutes',
                    'try_again' => true
                ]);
                exit;
            }


            if (
                str_contains($tailLink, 'GMMotors/log/') ||
                $tailLink == '/.env' ||
                str_contains($tailLink, 'LogJsError') ||
                $tailLink == '/actuator/health' ||
                $tailLink == '/adminer.php' ||
                str_contains($tailLink, '/index.php?pass=') ||
                $tailLink == '/stalker_portal/server/tools/auth_simple.php' ||
                str_contains($tailLink, 'sqlmanager/index.php') ||
                $tailLink == '/phpMyAdmin5.2/index.php?lang=en' ||
                str_contains($tailLink, 'Api/History')
            ) {
                return ViewUtils::viewErrorPage([
                    'msg_code' => "",
                    'msg' => "",
                ]);
                exit;
            }

            try {

                $statistic->update([
                    'total_mitchell_prodemand_request_not_get' => $statistic->total_mitchell_prodemand_request_not_get + 1,
                ]);

                $response1 = MitchellProdemandUtilsWeb::getResponse($link, null, 'POST', $body, $request);
                echo MitchellProdemandUtilsWeb::remove_element_reponse($response1->getResponseBody(),  $response1->getContentType(), null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::MITCHELL_PRODEMAND_OF_MAINTENANCE[0],
                    'msg' => $e->getMessage(),
                ]);
            }
        }

        // //Techdoc
        // if ($server == SecurityUtils::SERVER_TECDOC_WEB) {
        //     try {
        //         $response1 = TecDocUtilsWeb::getResponse($link, 'POST', null, $body, $request);
        //         echo TecDocUtilsWeb::remove_element_reponse($response1, $request);
        //         die();
        //     } catch (Exception $e) {
        //         LogUtils::error($e);
        //         return ViewUtils::viewErrorPage([
        //             'msg_code' => MsgCode::HAYNES_OF_MAINTENANCE[0],
        //             'msg' => 'Please try again after 1 - 3 minutes',
        //             'try_again' => true
        //         ]);
        //     }
        // }
        //Identifix
        if ($server == SecurityUtils::SERVER_IDENTIFIX_WEB) {

            if (
                str_contains($tailLink, "FixCollection/LogFixCollectionCounter")
            ) {
                echo "true";
                die();
            }
            if (
                str_contains($tailLink, "SetUserPreference")
            ) {
                echo "true";
                die();
            }

            try {

                $statistic->update([
                    'total_identifix_request_not_get' => $statistic->total_identifix_request_not_get + 1,
                ]);

                $response1 = IdentifixUtilsWeb::getResponse($link, 'POST', null, $body, $request);
                echo IdentifixUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::IDENTIFIX_OF_MAINTENANCE[0],
                    'msg' => MsgCode::IDENTIFIX_OF_MAINTENANCE[1],
                ]);
            }
        }

        //Partslink24 mobile
        if ($server == SecurityUtils::SERVER_PARTSLINK24_WEB) {
            try {

                $statistic->update([
                    'total_partslink_request_not_get' => $statistic->total_partslink_request_not_get + 1,
                ]);

                $response1 = Partslink24UtilsMobileWeb::getResponse($link, 'POST', null, $body, $request);

                if ($response1 instanceof Exception) {
                    return response()->json(['error' => 'Too many requests. Please try again later.'], 429);
                }

                echo Partslink24UtilsMobileWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::PARTSLINK24_OF_MAINTENANCE[0],
                    'msg' => MsgCode::PARTSLINK24_OF_MAINTENANCE[1],
                ]);
            }
        }


        //Partslink24  PC POST
        // if ($server == SecurityUtils::SERVER_PARTSLINK24_WEB) {
        //     sleep(1);

        //     try {

        //         if (str_contains($tailLink, "/session")) {

        //             $configAdmin =      ConfigAdmin::first();
        //             $access_token = "";

        //             $cookies_str = Cache::get(json_encode(['access_token_partslink24']));
        //             if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_partslink24_str)) {
        //                 $cookies_str = $configAdmin->cookies_partslink24_str;
        //             }

        //             $jsonToken = json_decode($cookies_str, true);
        //             if (isset($jsonToken['access_token'])) {
        //                 $access_token = $jsonToken['access_token'];
        //             }

        //             $access_token = Partslink24UtilsWeb::fakeTokenPartslink();
        //             return response()->json([
        //                 "securables" => null,
        //                 "status" => null,
        //                 "title" => null,
        //                 "refreshToken" => null,
        //                 "token" => [
        //                     "access_token" => $access_token,
        //                     "token_type" => "Bearer",
        //                     "expires_in" => 600,
        //                     "scope" => "mini_parts landrover_parts bmwmotorrad_parts vwclassic_parts porscheclassic_parts cart busandtrucks_parts bmwclassic_parts dacia_parts smart_parts mercedesunimog_parts bentley_parts bmw_parts jaguar_parts mmc_parts vw_parts cupra_parts audi_parts porsche_parts mercedestrucks_parts renault_parts man_parts mercedes_parts seat_parts skoda_parts pl24-arch2-cart alpine_parts vn_parts mercedesvans_parts miniclassic_parts bmwmotorradclassic_parts pl24-usage"
        //                 ]
        //             ], 200);
        //         }
        //         if (str_contains($tailLink, "/authorize")) {

        //             $configAdmin =      ConfigAdmin::first();
        //             $access_token = "";

        //             $cookies_str = Cache::get(json_encode(['access_token_partslink24']));
        //             if (empty($cookies_str) && $configAdmin  != null && !empty($configAdmin->cookies_partslink24_str)) {
        //                 $cookies_str = $configAdmin->cookies_partslink24_str;
        //             }

        //             $jsonToken = json_decode($cookies_str, true);
        //             if (isset($jsonToken['access_token'])) {
        //                 $access_token = $jsonToken['access_token'];
        //             }
        //             $access_token = Partslink24UtilsWeb::fakeTokenPartslink();

        //             return response()->json([
        //                 "securables" => null,
        //                 "status" => null,
        //                 "title" => null,
        //                 "refreshToken" => null,
        //                 "token" => [
        //                     "access_token" =>     $access_token,
        //                     "token_type" =>  "Bearer",
        //                     "expires_in" =>  600,
        //                     "scope" =>  "",
        //                     "session_status" =>  "gone",
        //                     "lcSessionId" =>  null

        //                 ]
        //             ], 200);
        //         }
        //         $statistic->update([
        //             'total_partslink_request_not_get' => $statistic->total_partslink_request_not_get + 1,
        //         ]);

        //         $response1 = Partslink24UtilsWeb::getResponse($link, 'POST', null, $body, $request);

        //         if ($response1 instanceof Exception) {
        //             return response()->json(['error' => 'Too many requests. Please try again later.'], 429);
        //         }

        //         echo Partslink24UtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
        //         die();
        //     } catch (Exception $e) {

        //         LogUtils::error($e);
        //         return ViewUtils::viewErrorPage([
        //             'msg_code' => MsgCode::PARTSLINK24_OF_MAINTENANCE[0],
        //             'msg' => MsgCode::PARTSLINK24_OF_MAINTENANCE[1],
        //         ]);
        //     }
        // }


        //Partsouq
        if ($server == SecurityUtils::SERVER_PARTSOUQ_WEB) {
            try {
                $response1 = PartsouqUtilsWeb::getResponse($link, 'POST', null, $body, $request);
                echo PartsouqUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::PARTSOUQ_OF_MAINTENANCE[0],
                    'msg' => MsgCode::PARTSOUQ_OF_MAINTENANCE[1],
                ]);
            }
        }

        //ETKA
        if ($server == SecurityUtils::SERVER_ETKA_WEB) {

            try {

                // $statistic->update([
                //     'total_identifix_request_not_get' => $statistic->total_identifix_request_not_get + 1,
                // ]);

                $response1 = ETKAUtilsWeb::getResponse($link, 'POST', null, $body, $request);
                echo ETKAUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (Exception $e) {

                LogUtils::error($e);
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::ETKA_OF_MAINTENANCE[0],
                    'msg' => MsgCode::ETKA_OF_MAINTENANCE[1],
                ]);
            }
        }

        //Ford PTS
        if ($server == SecurityUtils::SERVER_FORD_PTS_WEB) {
            try {

                $currentDomain = $_SERVER['HTTP_HOST'];

                if (str_contains($tailLink, "rb_") && str_contains($tailLink, "&flavor=post") && str_contains($tailLink, "&end=1")) {
                    header("Content-Type: text/plain; charset=utf-8");
                    echo "OK(OA)|name=ruxitagentjs|featureHash=ICA7NVfghqrux|version=|buildNumber=10303241106123517|lastModification=1738938470074";
                    die();
                }

                if (str_contains($tailLink, "LogMessagesToSplunk")) {
                    echo "";
                    die();
                }

                // if ((str_contains($currentDomain, "saved") || str_contains($currentDomain, "main-saved")) && $request != null && $request->method() == "POST") {
                //     $now = Carbon::now();
                //     $twentyFourHoursAgo = $now->subHours(120);
                //     $tailLinkSave = Helper::removeParamsFromUrlFord($tailLink);

                //     $webClass = new WebClass();
                //     $webClass->hours_ago = 24 * 30;
                //     $webClass->tail_link = $tailLinkSave;
                //     $webClass->service =  PlanUtils::FORD;
                //     $webClass->method = $request->method();
                //     $webClass->bodyRequest =   Helper::removeFieldFromBodyFord($body);

                //     $webDataExists =   WebDataUtils::getWebDataHttp($webClass);
                //     // dd($webDataExists);
                //     if (is_array($webDataExists) && $webDataExists['code'] == 404) {
                //         echo "no data";
                //         http_response_code(404);
                //         die();
                //     }

                //     if (
                //         $webDataExists  != null && $webDataExists->content != null && $webDataExists->content != ""
                //     ) {
                //         echo FordUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                //         die();
                //     }
                // }

                // $statistic->update([
                //     'total_ford_pts_request' => $statistic->total_ford_pts_request + 1,
                // ]);



                $response1 = FordUtilsWeb::getResponse($link, 'POST', $body, $request);
                $content = $response1->getResponseBody();

                if (str_contains($content, '<title>Login - Ford Service Information</title>')) {
                    return response()->json([
                        'code' => 401,
                        'success' => false,
                        'msg_code' => 'AUTHEN ERROR',
                        'msg' => 'AUTHEN ERROR',
                    ], 401);

                    exit;
                }

                echo FordUtilsWeb::remove_element_reponse($response1->getResponseBody(), $response1->getContentType(), $response1->getStatusCode(), $request);
                die();
            } catch (Exception $e) {
                if (isset($e)) {

                    $mess = $e->getMessage();
                    if (str_contains($mess, 'Could not resolve host')) {
                        return response()->json([
                            'code' => 500,
                            'success' => false,
                            'msg_code' => 'NETWORK_ERROR',
                            'msg' =>  $mess,
                        ], 500);
                    }
                }

                if (str_contains($e->getMessage(), 'stream 1 was not closed cleanly')) {
                    return response()->json([
                        'code' => 500,
                        'success' => false,
                        'msg_code' => MsgCode::FORD_PTS_OF_MAINTENANCE[0],
                        'msg' => 'ERROR COOKIES',
                    ], 500);
                }
                return response()->json([
                    'code' => 500,
                    'success' => false,
                    'msg_code' => MsgCode::FORD_PTS_OF_MAINTENANCE[0],
                    'msg' => $e->getMessage(),
                ], 500);
                exit;
            }
        }

        //Toyota TIS
        if ($server == SecurityUtils::SERVER_TOYOTA_TIS_WEB) {

            parse_str(request()->getContent(), $body);
            $body = array_map(function ($value) {
                return is_null($value) ? "" : $value;
            }, $body);

            $newBody = [];
            foreach ($body as $key => $value) {
                $newKey = str_replace('Form_', 'Form.', $key);
                $newBody[$newKey] = $value;
            }
            $body = $newBody;

            if (str_contains($tailLink, 'IdleSessionTimeoutServlet')) {
                return response()->json([
                    "POLL_FREQUENCY" => "30",
                    "IDLE_SESSION_TIMEOUT" => "300",
                    "MSG_DISPLAY_DURATION" => "15",
                    // "TIMEOUT_WARNING_MSG" =>
                    // "Your session is about to be timed out. Please click OK to continue your current session.",
                    // "LOGOUT_URL" => "/t3Portal/resources/jsp/tessSignoff.jsp",
                    // "SESSION_EXPIRY_MSG" =>
                    // "Your session has expired, please re-login to continue.",
                    // "PROP_NAME_PREFIX" => "TS",
                    // "TS_SESSION_EXPIRATION" => (time() + 15 * 60) * 1000,
                ], 200);
            }

            try {
                if ($request != null && $request->method() == "POST" && !str_contains($tailLink, 't3Portal')) {
                    $now = Carbon::now();
                    $tailLinkSave = Helper::removeParamsFromUrlToyota($tailLink);

                    $webClass = new WebClass();
                    $webClass->hours_ago = 24 * 30;
                    $webClass->tail_link = $tailLinkSave;
                    $webClass->service =  PlanUtils::TOYOTA_TIS;
                    $webClass->method = $request->method();
                    $webClass->bodyRequest =   Helper::removeFieldFromBodyToyota($body);

                    $webDataExists =   WebDataUtils::getWebData($webClass);

                    if ($webDataExists  != null && $webDataExists->content != null && $webDataExists->content != "") {

                        $statistic->update([
                            'total_toyota_tis_request_has_cache' => $statistic->total_toyota_tis_request_has_cache + 1,
                        ]);

                        echo ToyotaUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
                        die();
                    }
                }


                $statistic->update([
                    'total_toyota_tis_request_not_get' => $statistic->total_toyota_tis_request_not_get + 1,
                ]);

                $response1 = ToyotaUtilsWeb::getResponse($link, 'POST', $body, $request);
                echo ToyotaUtilsWeb::remove_element_reponse($response1->getBody(), $response1->getHeader('Content-Type')[0] ?? "", null, $request);
                die();
            } catch (ServerException $e) {


                $response = $e->getResponse();
                $responseBodyAsString = $response->getBody()->getContents();
                $json = json_decode($responseBodyAsString, true);


                if (isset($json['_TYPE']) && isset($json['_MSG']) && $json['_MSG'] == 'Screen transition error.') {
                    return response()->json([
                        'code' => 500,
                        'success' => false,
                        'msg_code' => 'TIME_SPAN_ERROR',
                        'msg' => 'TIME_SPAN_ERROR',
                    ], 500);
                }

                return response()->json([
                    'code' => 500,
                    'success' => false,
                    'msg_code' => 'TIME_SPAN_ERROR',
                    'msg' => 'TIME_SPAN_ERROR',
                    'data_err' => $json
                ], 500);
            } catch (ClientException $e) {
                if ($e->getResponse()->getStatusCode() == 404) {
                    return response()->json([
                        'code' => 404,
                        'success' => false,
                        'msg_code' => 'NOT_FOUND',
                        'msg' => $e->getMessage(),
                    ], 404);
                } else {
                    return response()->json([
                        'code' =>  $e->getResponse()->getStatusCode(),
                        'success' => false,
                        'msg_code' => 'ERROR',
                        'msg' => $e->getMessage(),
                    ],  $e->getResponse()->getStatusCode());
                }
            } catch (ConnectException $e) {

                return response()->json([
                    'code' => 500,
                    'success' => false,
                    'msg_code' => 'NETWORK_ERROR',
                    'msg' => 'NETWORK_ERROR',
                ], 500);
            } catch (Exception $e) {

                return response()->json([
                    'code' => 500,
                    'success' => false,
                    'msg_code' => MsgCode::TOYOTA_TIS_OF_MAINTENANCE[0],
                    'msg' => MsgCode::TOYOTA_TIS_OF_MAINTENANCE[1],
                ], 500);
            }
        }

        $path = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $pathNoHTTP = str_replace("http://", "", $path);
        $pathNoHTTP = str_replace("https://", "",  $pathNoHTTP);
    }

    /**
     * Thông tin server
     */
    public function index2(Request $request, $slug)
    {

        $tailLink = $_SERVER["REQUEST_URI"];
        $link = AutoDataUtils::PATH_AUTODATA .  $tailLink;

        $ch = curl_init($link);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_exec($ch);
        $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        header('Content-type: ' .  $content_type);

        //    echo $output = curl_exec($ch);
        return response(curl_exec($ch))->header('Content-type',  $content_type);
        curl_close($ch);
    }
}
