<?php

namespace App\Http\Controllers;

use App\Classes\WebClass;
use App\Helper\AutoDataUtilsWeb;
use App\Helper\PlanUtils;
use App\Helper\WebDataUtils;
use App\Helper\WebDataUtilsNasHttp;
use App\Http\Controllers\Api\DataLeechControllerForm3Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SelectEngineController extends Controller
{
    public function indexSelectEngine(Request $request)
    {


        $car = $request->car;
        $model = $request->model;
        $route_name = $request->route_name;
        $module = $request->module;

        $webClass = new WebClass();
        $webClass->hours_ago = 24 * 30;
        $webClass->tail_link = "/w1/manufacturers/$car/$model/engines?route_name=$route_name&module=$module";

        $webClass->service =  PlanUtils::AUTODATA;
        $webClass->method = $request->method();
        $currentDomain = $_SERVER['HTTP_HOST'];

        $webDataExists =   WebDataUtilsNasHttp::getWebData($webClass);
        if (is_array($webDataExists) && $webDataExists['code'] == 404) {
            echo "no data";
            http_response_code(404);
            die();
        }

        if (
            $webDataExists  != null && $webDataExists->content != null && $webDataExists->content != ""
        ) {
            echo AutoDataUtilsWeb::remove_element_reponse(base64_decode($webDataExists->content), $webDataExists->content_type ?? "", null, $request);
            die();
        }

        $autodataCrawlController = new DataLeechControllerForm3Controller();
        $autodataCrawlController->indexGet($request);
    }
}
