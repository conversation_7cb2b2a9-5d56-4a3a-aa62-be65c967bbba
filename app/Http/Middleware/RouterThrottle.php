<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class RouterThrottle
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle($request, Closure $next, $routerName, $maxAttempts = 60, $decayMinutes = 1)
    {
        // Kiểm tra tần suất yêu cầu dựa trên router hoặc URL
        $key = 'router:' . $routerName . ':' . $request->ip();

        $limiter = app(\Illuminate\Cache\RateLimiter::class);

        if ($limiter->tooManyAttempts($key, $maxAttempts, $decayMinutes)) {
            abort(429, 'Too Many Requests');
        }

        $limiter->hit($key, $decayMinutes);

        return $next($request);
    }
}
