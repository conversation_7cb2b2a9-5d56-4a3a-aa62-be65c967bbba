<?php

namespace App\Http\Middleware;

use App\Models\Customer;
use App\Models\MsgCode;
use App\Models\SessionCustomer;
use Closure;
use Illuminate\Http\Request;


class CustomerLogin
{

    public function handle(Request $request, Closure $next)
    {

        $token = request()->header('customer-token');

    
    
        $checkTokenIsValid = SessionCustomer::where('token',$token)->first();
      
        if($checkTokenIsValid != null && $checkTokenIsValid->user_id != null) {
            $c = Customer::where('id', $checkTokenIsValid->user_id)->first();
        }
        

        if(empty($token)) {
      
            return response()->json([
                'code'=> 401,
                'msg_code' => MsgCode::NO_TOKEN[0],
                'msg' => MsgCode::NO_TOKEN[1],
                'success' => false,
            ]);
        } else if(empty($checkTokenIsValid) ||  $c == null) {
         
           
            return response()->json([
                'code'=> 401,
                'msg_code' => MsgCode::NOT_HAVE_ACCESS[0],
                'msg' => MsgCode::NOT_HAVE_ACCESS[1],
                'success' => false,
            ]);
        } else {
        
            $request->merge([
                'customer' => $c ,
            ]);
        
            return $next($request);
        }

        return $next($request);
    }
}
