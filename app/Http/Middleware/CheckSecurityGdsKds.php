<?php

namespace App\Http\Middleware;

use App\Helper\Helper;
use App\Helper\IPUtils;
use App\Helper\PlanUtils;
use App\Helper\SecurityUtils;
use App\Models\HistoryAccess;
use App\Models\MsgCode;
use App\Models\SessionUser;
use App\Models\User;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;

class CheckSecurityGdsKds
{

    public function handle(Request $request, Closure $next)
    {
        $cookies = $_COOKIE;
        $dataDevice = SecurityUtils::getDataDevice();
        $device_id = $dataDevice->device_id;
        $domain_request = $_SERVER['HTTP_HOST'];
        $user = null;
        $agency_sub = "web";

        $token =  $dataDevice->token;
        if (!empty($token)) {
            $checkTokenIsValidForUser = SessionUser::where('token', $token)->first();
            if ($checkTokenIsValidForUser != null) {
                $user = User::where('id', $checkTokenIsValidForUser->user_id)->first();
            }
        }


        $domain_request = $_SERVER['HTTP_HOST'];
        $server = null;
        $is_web = false;
        $is_mobile = true;



        $cookies = $_COOKIE;

        $device_id = $dataDevice->device_id;
        $platform = $dataDevice->platform;
        $model = $dataDevice->model;
        $security_code = request()->header('dr-security-code') ?? $cookies['dr-security-code'] ?? request()->header('security-code') ?? $cookies['security-code'] ?? null;
        $app_version = $dataDevice->app_version;



        if ($checkTokenIsValidForUser) {
            $user = User::where('id', $checkTokenIsValidForUser->user_id)->first();
            if ($user != null) {


                if ($user->is_user_ios == false) {
                    if ($user->is_block == true) {
                        return response()->json([
                            'code' => 400,
                            'success' => false,
                            'msg_code' => MsgCode::YOU_HAVE_BEEN_BLOCKED[0],
                            'msg' => MsgCode::YOU_HAVE_BEEN_BLOCKED[1],
                        ], 400);
                    }
                }


                $user->update([
                    'ip_using' =>  IPUtils::getIP(),
                    "platform" => $platform,
                    "device_id" => $device_id,
                    "app_version" => $app_version,
                    "model" => $model,
                ]);
            }
            $request->merge([
                'user' => $user
            ]);
        }


        $server = SecurityUtils::SERVER_KDS_GDS;
        $last_visit_time_kdsgds = Helper::getTimeNowString();
        $is_web = false;

        if ($user  != null) {
          //  $referer = $request->headers->get('referer');
            $url = $request->fullUrl();
          //  $origin = $request->header('Origin');

         //   if ($referer == null && empty($origin) &&  !str_ends_with($url, '.map')) {
                if ($user->is_block != true) {
                    // HistoryAccess::create([
                    //     "user_id" => $user->id,
                    //     "tail_link" =>  $url,
                    //     "ip" => IPUtils::getIP(),
                    //     "cookie_use" => $token,
                    //     "use_agent" => $request->server('HTTP_USER_AGENT') ?? "",
                    //     "method" => $request->method(),
                    //     "service" => $server ?? null,
                    //     "is_mobile" => $is_mobile ?? false
                    // ]);
                }
           // }
        }

        $check_expiry = true;
        //Code cho xài tạm dịch vụ khác
        $has_temp = false;

        if ($has_temp  == false) {
            $check_expiry = $this->check_expiry($user, $server);
        }


        if ($check_expiry  !== true) {
            return     $check_expiry;
        }

        if ($user != null) {
            $user->update(Helper::sahaRemoveItemArrayIfNullValue([
                "last_visit_time_kdsgds" => $last_visit_time_kdsgds,
                "device_id" => $device_id,
            ]));

            if ($user->allow_web == false && $is_web == true) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_WEB[0],
                    'msg' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_WEB[1],
                ], 400);
            }
            if ($user->allow_mobile == false && $is_mobile == true) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_MOBILE[0],
                    'msg' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_MOBILE[1],
                ], 400);
            }
        }
        $request->merge([
            'agency_sub' => $agency_sub,
        ]);


        return $next($request);
    }

    public function check_expiry($user, $server)
    {

        $domain_request = $_SERVER['HTTP_HOST'];

        if (str_contains($domain_request, "main-temp.")) {
            return true;
        }


        if ($user == null) return true;
        $has_expiry = true;

        $timeNow = Carbon::parse(Helper::getTimeNowString());

        if (
            $server == SecurityUtils::SERVER_KDS_GDS
        ) {
            if ($user->expiry_kdsgds == null || !Carbon::parse($user->expiry_kdsgds)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if ($has_expiry == false) {
            if (
                $server == SecurityUtils::SERVER_KDS_GDS
            ) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::EXPIRED[0],
                    'msg' => (MsgCode::EXPIRED[1]) . "(" . ($user->id) . ")" . $user->expiry_kdsgds,
                ], 400);
            }
        }


        return true;
    }
}
