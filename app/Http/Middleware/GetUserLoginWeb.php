<?php

namespace App\Http\Middleware;

use App\Models\SessionUser;
use App\Models\User;
use Closure;

class GetUserLoginWeb
{
    public function handle($request, Closure $next)
    {

        $token = request()->header('ad-web-token') ?? $_COOKIE['ad-web-token'] ?? $cookies['ad-web-token'] ?? request()->header('token') ?? $cookies['token'] ?? null;
       
        $checkTokenIsValidForUser = SessionUser::where('token', $token)->first();

        if ( $token  != null && $checkTokenIsValidForUser) {
            $request->merge([
                'user' => User::where('id', $checkTokenIsValidForUser->user_id)->first(),
            ]);
        }

        return $next($request);
    }
}
