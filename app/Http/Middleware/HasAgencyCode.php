<?php

namespace App\Http\Middleware;

use App\Models\Agency;
use App\Models\MsgCode;
use Closure;


class HasAgencyCode
{


    public function handle($request, Closure $next)
    {
        $agency_code = $request->route()->parameter('agency_code');
        $agency = Agency::where('agency_code', $agency_code)->first();
        if ($agency  == null) {
            return response()->json([
                'code' => 404,
                'success' => false,
                'msg_code' => MsgCode::NO_AGENCY_EXISTS[0],
                'msg' => MsgCode::NO_AGENCY_EXISTS[1],
            ], 404);
        }
        $request->merge([
            'agency' => $agency,
        ]);

        return $next($request);
    }
}
