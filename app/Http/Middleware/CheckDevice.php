<?php

namespace App\Http\Middleware;

use App\Helper\SecurityUtils;
use App\Models\Admin;
use App\Models\Employee;
use App\Models\MsgCode;
use App\Models\SessionAdmin;
use App\Models\SessionEmployee;
use App\Models\SessionUser;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;


class CheckDevice
{

    public function handle(Request $request, Closure $next)
    {

      $device_id =  SecurityUtils::getDataDevice()->device_id;
        if (empty(  $device_id )) {
            return response()->json([
                'code' => 400,
                'msg_code' => MsgCode::INVALID_DEVICE[0],
                'msg' => MsgCode::INVALID_DEVICE[1],
                'success' => false,
            ], 400);
        }

        return $next($request);
    }
}
