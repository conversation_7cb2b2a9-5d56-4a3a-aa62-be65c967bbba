<?php

namespace App\Http\Middleware;

use App\Models\SessionUser;
use App\Models\User;
use Closure;

class GetUserLogin
{
    public function handle($request, Closure $next)
    {

        $cookies = $_COOKIE;
        $token = request()->header('dr-token') ?? $cookies['dr-token'] ?? request()->header('token') ?? $cookies['token'] ?? null;
      
        $checkTokenIsValidForUser = SessionUser::where('token', $token)->first();

        if ( $token  != null && $checkTokenIsValidForUser) {
            $request->merge([
                'user' => User::where('id', $checkTokenIsValidForUser->user_id)->first(),
            ]);
        }

        return $next($request);
    }
}
