<?php

namespace App\Http\Middleware;

use App\Helper\Helper;
use App\Helper\IPUtils;
use App\Helper\SecurityUtils;
use App\Models\DeviceLogin;
use App\Models\MsgCode;
use App\Models\SessionUser;
use App\Models\User;
use Carbon\Carbon;
use Closure;
use DateTime;
use Illuminate\Support\Facades\Cookie;

class UserLogin
{


    public function handle($request, Closure $next)
    {
        $cookies = $_COOKIE;
        $token = request()->header('dr-token') ?? $cookies['dr-token'] ?? request()->header('token') ?? $cookies['token'] ?? null;
        $device_id = request()->header('dr-device-id') ?? $cookies['dr-device-id'] ?? request()->header('device-id')  ?? $cookies['device-id'] ?? null;
        $platform = request()->header('dr-platform') ?? $cookies['dr-platform'] ?? request()->header('platform') ?? $cookies['platform'] ?? null;
        $model = request()->header('dr-modelx')  ?? $cookies['dr-modelx'] ?? request()->header('model') ?? $cookies['model'] ?? null;
        $security_code = request()->header('dr-security-code') ?? $cookies['dr-security-code'] ?? request()->header('security-code') ?? $cookies['security-code'] ?? null;

        $checkTokenIsValidForUser = SessionUser::where('token', $token)->first();
        if (empty($token)) {
            return response()->json([
                'code' => 401,
                'msg_code' => MsgCode::NO_TOKEN[0],
                'msg' => MsgCode::NO_TOKEN[1],
                'success' => false,
            ], 401);
        }


        if (empty($checkTokenIsValidForUser)) {
            return response()->json([
                'code' => 401,
                'msg_code' => MsgCode::NOT_HAVE_ACCESS[0],
                'msg' => MsgCode::NOT_HAVE_ACCESS[1],
                'success' => false,
            ], 401);
        }

        if ($checkTokenIsValidForUser) {
            $user = User::where('id', $checkTokenIsValidForUser->user_id)->first();
            if ($user != null) {


                $ip_using = IPUtils::getIP();
                if ($user->is_user_ios == false) {


                    $dt = Carbon::now('UTC');
                    $hour = (int) $dt->format('H');

                    $security_codeC1 = md5($hour . "HOANGDRCAR");
                    $security_codeC2 = md5(($hour + 1) . "HOANGDRCAR");
                    $security_codeC3 = md5(($hour - 1) . "HOANGDRCAR");

                    // if ($security_code  !=    $security_codeC1 && $security_code  != $security_codeC2 && $security_code  != $security_codeC3) {
                    //     return response()->json([
                    //         'code' => 401,
                    //         'msg_code' => MsgCode::INVALID_SECURITY_CODE[0],
                    //         'msg' => MsgCode::INVALID_SECURITY_CODE[1],
                    //         'success' => false,
                    //     ], 401);
                    // }

                    if (empty($user->email_verified_at)) {
                        return response()->json([
                            'code' => 400,
                            'msg_code' => MsgCode::UNVERIFIED_EMAIL[0],
                            'msg' => MsgCode::UNVERIFIED_EMAIL[1],
                            'success' => false,
                        ], 400);
                    }


                    if ($user->is_block == true) {
                        return response()->json([
                            'code' => 401,
                            'msg_code' => MsgCode::YOU_HAVE_BEEN_BLOCKED[0],
                            'msg' => MsgCode::YOU_HAVE_BEEN_BLOCKED[1],
                            'success' => false,
                        ], 401);
                    }
                }

                SecurityUtils::useDevice($user);

                $user->update([
                    'ip_using' =>  $ip_using,
                    "platform" => $platform,
                    "device_id" => $device_id,
                    "model" => $model,
                ]);
            }
            $request->merge([
                'user' => $user
            ]);
        }

        if ($user == null) {
            return response()->json([
                'code' => 401,
                'msg_code' => MsgCode::NOT_HAVE_ACCESS[0],
                'msg' => MsgCode::NOT_HAVE_ACCESS[1],
                'success' => false,
            ], 401);
        }


        return $next($request);
    }
}
