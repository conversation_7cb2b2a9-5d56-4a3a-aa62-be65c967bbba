<?php

namespace App\Http\Middleware;

use App\Models\DeviceLogin;
use App\Models\MsgCode;
use App\Models\PlanDevice;
use App\Models\PlanService;
use App\Models\User;
use Closure;

class PaymentCheckPlanDeviceAndUser
{
    public function handle($request, Closure $next)
    {

        $email = request('email');
        $month = request('month'); //in  plan service
        $device_login_id = request('device_login_id'); //plan id hoac product id


        if ($month == null && $month  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_PLAN_EXISTS[0],
                'msg' => MsgCode::NO_PLAN_EXISTS[1],
            ], 400);
        }
        if ($email == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }
        ////

        $plan =  null;
        $device_login = null;
        if ($month != null) {
            $plan =   PlanDevice::where('month',  $month)->first();
        }

        if ($plan == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_PLAN_EXISTS[0],
                'msg' => MsgCode::NO_PLAN_EXISTS[1],
            ], 400);
        }

        $user =  User::where('email', $email)->first();

        if ($user  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $request->merge([
            'user' => $user,
            'plan' => $plan,
            'device_login_id' => $device_login_id
        ]);

        return $next($request);
    }
}
