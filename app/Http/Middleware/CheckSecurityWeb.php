<?php

namespace App\Http\Middleware;

use App\Helper\Helper;
use App\Helper\IPUtils;
use App\Helper\SecurityUtils;
use App\Helper\ViewUtils;
use App\Models\ConfigAdmin;
use App\Models\HistoryAccess;
use App\Models\MsgCode;
use App\Models\SessionUser;
use App\Models\User;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CheckSecurityWeb
{


    public function handle(Request $request, Closure $next)
    {
        $cookies = $_COOKIE;
        $dataDevice = SecurityUtils::getDataDevice();
        $device_id = $dataDevice->device_id;
        $domain_request = $_SERVER['HTTP_HOST'];
        $tailLink = $_SERVER["REQUEST_URI"];
        $user = null;
        $agency = null;
        $domainWithoutSub = Helper::getDomainCurrentWithoutSubAndNoHttp();
        $agency_sub = "web";
        $token =  $dataDevice->token;



        if (!empty($token)) {
            $checkTokenIsValidForUser = SessionUser::where('token', $token)->first();
            if ($checkTokenIsValidForUser != null) {
                $user = User::where('id', $checkTokenIsValidForUser->user_id)->first();
            }
        }

        if ($user != null && $user->of_agency_id != null) {
            $agency = Cache::remember(json_encode(["agencyuser", $user->of_agency_id]), 30, function ()  use ($user) {
                return DB::table('agencies')->where('id', $user->of_agency_id)->first();
            });
            if ($agency != null && $agency->not_back_home_web_agency == false) {
                $agency_sub = $agency->agency_code;
            }
        }

        $domain_request = $_SERVER['HTTP_HOST'];
        $tailLink = $_SERVER["REQUEST_URI"];
        $server = null;
        $domain = null;
        $is_web = false;
        $is_mobile = false;

        if (
            // !str_contains($domain_request, '.localhost')
            // &&
            (str_contains($domain_request, 'my-alldata.') ||
                str_contains($domain_request, 'app-alldata.') ||
                str_contains($domain_request, 'workshop-autodata-group.') ||
                str_contains($domain_request, 'autodata.') ||
                str_contains($domain_request, 'autodata-sv.') ||
                str_contains($domain_request, 'autodata-italy.') ||
                str_contains($domain_request, 'haynespro.') ||
                str_contains($domain_request, 'haynespro-truck.') ||
                str_contains($domain_request, 'partslink24.') ||
                str_contains($domain_request, 'identifix.') ||
                str_contains($domain_request, 'alldata-eu.') ||
                str_contains($domain_request, 'mitchell-repair-center.') ||
                str_contains($domain_request, 'mitchell-prodemand.') ||
                str_contains($domain_request, 'kgdgds.') ||
                str_contains($domain_request, 'etka.') ||
                str_contains($domain_request, 'ford-pts.') ||
                str_contains($domain_request, 'toyota.') ||
                str_contains($domain_request, 'tecdoc.')
            )

        ) {

            $cookies = $_COOKIE;

            $device_id = $dataDevice->device_id;
            $platform = $dataDevice->platform;
            $model = $dataDevice->model;
            $security_code = request()->header('dr-security-code') ?? $cookies['dr-security-code'] ?? request()->header('security-code') ?? $cookies['security-code'] ?? null;
            $app_version = $dataDevice->app_version;

            $checkTokenIsValidForUser = SessionUser::where('token', $token)->first();


            if (empty($token)) {

                if (str_contains($domain_request, 'alldata-eu.') || request()->header('from1') == 'eu_web_cus') {
                    return response()->json([
                        'code' => 400,
                        'success' => false,
                        'msg_code' => MsgCode::NO_TOKEN[0],
                        'msg' => MsgCode::NO_TOKEN[1],
                    ], 400);
                } else {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::NO_TOKEN[0],
                        'msg' => MsgCode::NO_TOKEN[1],
                    ]);
                }
            }


            if (empty($checkTokenIsValidForUser)) {
                if (str_contains($domain_request, 'alldata-eu.') || request()->header('from1') == 'eu_web_cus') {
                    return response()->json([
                        'code' => 400,
                        'success' => false,
                        'msg_code' => MsgCode::INVALID_TOKEN[0],
                        'msg' => MsgCode::INVALID_TOKEN[1],
                    ], 400);
                } else {
                    return ViewUtils::viewErrorPage([
                        'msg_code' => MsgCode::INVALID_TOKEN[0],
                        'msg' => $token  ?? "nul",
                    ]);
                }
            }

            if ($checkTokenIsValidForUser) {

                $user = User::where('id', $checkTokenIsValidForUser->user_id)->first();
                if ($user != null) {

                    if ($user->is_user_ios == false) {
                        // $dt = Carbon::now('UTC');
                        // $hour = (int) $dt->format('H');
                        // $security_codeC1 = md5($hour . "HOANGDRCAR");
                        // $security_codeC2 = md5(($hour + 1) . "HOANGDRCAR");
                        // $security_codeC3 = md5(($hour - 1) . "HOANGDRCAR");

                        // if (empty($device_id)) {
                        //     return response()->json([
                        //         'code' => 400,
                        //         'msg_code' => MsgCode::INVALID_DEVICE[0],
                        //         'msg' => MsgCode::INVALID_DEVICE[1],
                        //         'success' => false,
                        //     ], 400);

                        //     return [
                        //         'msg_code' => MsgCode::INVALID_DEVICE[0],
                        //         'msg' => MsgCode::INVALID_DEVICE[1],
                        //     ];
                        // }


                        // if (SecurityUtils::useDevice($user) == false) {
                        //        return ViewUtils::viewErrorPage([
                        //         'msg_code' => MsgCode::ERROR_LOGGED_IN_ON_ANOTHER_DEVICE[0],
                        //         'msg' => MsgCode::ERROR_LOGGED_IN_ON_ANOTHER_DEVICE[1],
                        //         'info' => 'Note that you can only log in on one device. If you want to log in on a new device, please log out from the old device or reset the device via email using the button below.'
                        //         //'info' => $device_id ?? "No device id"
                        //     ]);
                        // }



                        if (empty($user->email_verified_at)) {


                            return ViewUtils::viewErrorPage([
                                'msg_code' => MsgCode::UNVERIFIED_EMAIL[0],
                                'msg' => MsgCode::UNVERIFIED_EMAIL[1],
                            ]);
                        }


                        if ($user->is_block == true) {


                            return ViewUtils::viewErrorPage([
                                'msg_code' => MsgCode::YOU_HAVE_BEEN_BLOCKED[0],
                                'msg' => MsgCode::YOU_HAVE_BEEN_BLOCKED[1],
                            ]);
                        }
                    }


                    $user->update([
                        'ip_using' =>  IPUtils::getIP(),
                        "platform" => $platform,
                        "device_id" => $device_id,
                        "app_version" => $app_version,
                        "model" => $model,
                    ]);
                }

                $request->merge([
                    'user' => $user
                ]);
            }
        }


        $last_visit_time_alldata = null;
        $last_visit_time_autodata = null;
        $last_visit_time_haynespro = null;
        $last_visit_time_tecdoc = null;
        $last_visit_time_identifix = null;
        $last_visit_time_mitchell_repair_center = null;
        $last_visit_time_mitchell_prodemand = null;
        $last_visit_time_haynespro_truck = null;
        $last_visit_time_partslink24 = null;
        $last_visit_time_kdsgds = null;
        $last_visit_time_etka = null;
        $last_visit_time_ford_pts = null;
        $last_visit_time_toyota_tis = null;

        if (str_contains($domain_request, 'app-alldata.')) {
            $server = SecurityUtils::SERVER_ALL_DATA_WEB;
            $domain = "https://app.alldata.com";
            $last_visit_time_alldata = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'alldata-eu')) {
            $server = SecurityUtils::SERVER_ALL_DATA_EU_WEB_CUSTOM;
            $domain = "https://app.alldata.com";
            $last_visit_time_alldata = Helper::getTimeNowString();
            $is_web = false;
        } else
        if (str_contains($domain_request, 'autodata-sv')) {
            $server = SecurityUtils::SERVER_AUTO_DATA_WEB_SAVED;
            $domain = "https://workshop.autodata-group.com";
            $last_visit_time_autodata = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'workshop-autodata-group.')) {
            $server = SecurityUtils::SERVER_AUTO_DATA_WEB;
            $domain = "https://workshop.autodata-group.com";
            $last_visit_time_autodata = Helper::getTimeNowString();



            if ($user != null) {
                $agency = DB::table('agencies')->where('user_id', $user->id)->first();
                if ($agency != null && $agency->autodata_language == "ITALY") {
                    $server = SecurityUtils::SERVER_AUTO_DATA_ITALY_WEB;
                } else if ($user->of_agency_id != null) {
                    $agency = DB::table('agencies')->where('id', $user->of_agency_id)->first();
                    if ($agency != null && $agency->autodata_language == "ITALY") {
                        $server = SecurityUtils::SERVER_AUTO_DATA_ITALY_WEB;
                    }
                }
            }
        } else
        if (str_contains($domain_request, 'autodata-italy.')) {
            $server = SecurityUtils::SERVER_AUTO_DATA_ITALY_WEB;
            $domain = "https://workshop.autodata-group.com";
            $last_visit_time_autodata = Helper::getTimeNowString();
        } else
        if (str_contains($domain_request, 'haynespro.')) {
            $domain = "https://www.workshopdata.com";
            $server = SecurityUtils::SERVER_HAYNES_PRO_WEB;
            $last_visit_time_haynespro = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'haynespro-truck.')) {
            $domain = "https://www.workshopdata.com";
            $server = SecurityUtils::SERVER_HAYNES_PRO_TRUCK_WEB;
            $last_visit_time_haynespro_truck = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'my-alldata.')) {
            $domain = "https://my.alldata.com";
            $server = SecurityUtils::SERVER_ALL_DATA_US_V2_WEB;
            $last_visit_time_alldata = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'mitchell-prodemand.')) {
            $domain = "https://www2.prodemand.com";
            $server = SecurityUtils::SERVER_MITCHELL_PRODEMAND_WEB;
            $last_visit_time_mitchell_prodemand = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'identifix.')) {
            $domain = "https://dh.identifix.com";
            $server = SecurityUtils::SERVER_IDENTIFIX_WEB;
            $last_visit_time_identifix = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'etka.')) {
            $domain = "https://superetka.com";
            $server = SecurityUtils::SERVER_ETKA_WEB;
            $last_visit_time_etka = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'partslink24.')) {
            $domain = "https://www.partslink24.com";
            $server = SecurityUtils::SERVER_PARTSLINK24_WEB;
            $last_visit_time_partslink24 = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'partsouq.')) {
            $domain = "https://partsouq.com";
            $server = SecurityUtils::SERVER_PARTSOUQ_WEB;
            $last_visit_time_partsouq = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'kdsgds.')) {
            $domain = "https://kdsgds.drcar.ch";
            $server = SecurityUtils::SERVER_KDS_GDS_WEB;
            $last_visit_time_kdsgds = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'tecdoc.')) {
            $domain = "https://web.tecalliance.net";
            $server = SecurityUtils::SERVER_TECDOC_WEB;
            $last_visit_time_tecdoc = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'ford-pts.')) {
            $domain = "https://www.fordtechservice.dealerconnection.com";
            $server = SecurityUtils::SERVER_FORD_PTS_WEB;
            $last_visit_time_ford_pts = Helper::getTimeNowString();
            $is_web = true;
        } else
         if (str_contains($domain_request, 'toyota.')) {
            $domain = "https://techinfo.toyota.com";
            $server = SecurityUtils::SERVER_TOYOTA_TIS_WEB;
            $last_visit_time_toyota_tis = Helper::getTimeNowString();
            $is_web = true;
        } else
        if (str_contains($domain_request, 'assets')) {
            $server = SecurityUtils::SERVER_AUTO_DATA;
            $domain = "https://workshop.autodata-group.com";
            $last_visit_time_autodata = Helper::getTimeNowString();
        } else if (str_contains($tailLink, "/w1/") || str_contains($tailLink, "/w2/")) {
            $domain = "https://workshop.autodata-group.com";
            $server = SecurityUtils::SERVER_AUTO_DATA;
            $last_visit_time_autodata = Helper::getTimeNowString();
            $is_mobile = true;
        } else if (str_contains($tailLink, "/ADAG/") || str_contains($tailLink, "/repair/") || str_contains($tailLink, "/DataWebServices/")) {
            $domain = "https://my.alldata.com";
            $server = SecurityUtils::SERVER_ALL_DATA_US_V2;
            $last_visit_time_alldata = Helper::getTimeNowString();
            $is_mobile = true;
        } else if (str_contains($tailLink, "/alldata/")) {
            $domain = "https://app.alldata.com";
            $server = SecurityUtils::SERVER_ALL_DATA;
            $last_visit_time_alldata = Helper::getTimeNowString();
            $is_mobile = true;
        } else {
            // $server = SecurityUtils::SERVER_AUTO_DATA;
            // $domain = "https://workshop.autodata-group.com";
            // $last_visit_time_autodata = Helper::getTimeNowString();
            // $is_mobile = true;
        }

        if ($user  != null) {
            $referer = $request->headers->get('referer');
            $url = $request->fullUrl();
            $origin = $request->header('Origin');

            // $condition1 = true;//($referer == null && empty($origin) &&  !str_ends_with($url, '.map'));
            $condition2 =   $server == SecurityUtils::SERVER_ALL_DATA || $server == SecurityUtils::SERVER_ALL_DATA_WEB || $server ==  SecurityUtils::SERVER_ALL_DATA_EU_WEB_CUSTOM;
            // $condition3 =  ($server == SecurityUtils::SERVER_HAYNES_PRO_WEB);

            $condition3 =  ($server == SecurityUtils::SERVER_PARTSLINK24_WEB &&
                !str_ends_with($url, '.png')
                &&  !str_ends_with($url, '.gif')
                &&  !str_ends_with($url, '.js')
            );

            $condition4 =  (str_contains($request->header('Content-Type'), 'application/json'));

            if ($condition2 && $condition4 == true) {
                if ($user->is_block != true) {
                    HistoryAccess::create([
                        "user_id" => $user->id,
                        "tail_link" =>  $url,
                        "ip" => IPUtils::getIP(),
                        "headers" => json_encode($request->headers->all()),
                        "cookie_use" => $token,
                        "use_agent" => $request->server('HTTP_USER_AGENT') ?? "",
                        "method" => $request->method(),
                        "service" => $server ?? null,
                        "is_mobile" => $is_mobile ?? false
                    ]);
                }
            }
        }

        $check_expiry = true;

        //Code cho xài tạm dịch vụ khác, temp nhớ sửa sau khi chuyển tạm
        $has_temp = false;

        // if ($server == SecurityUtils::SERVER_AUTO_DATA_WEB  || $server == SecurityUtils::SERVER_HAYNES_PRO_WEB) {
        //     $configAdmin =  ConfigAdmin::first();
        //     if ($configAdmin->temp_autodata == PlanUtils::HAYNESPRO) {
        //         $domain = "https://www.workshopdata.com";
        //         $server = SecurityUtils::SERVER_HAYNES_PRO_WEB;
        //         $last_visit_time_haynespro = Helper::getTimeNowString();
        //         $is_web = true;
        //         $has_temp = true;
        //     }
        // }

        // if ($server == SecurityUtils::SERVER_AUTO_DATA_ITALY_WEB || $server == SecurityUtils::SERVER_HAYNES_PRO_WEB) {
        //     $configAdmin =  ConfigAdmin::first();
        //     if ($configAdmin->temp_autodata_italy == PlanUtils::HAYNESPRO) {
        //         $domain = "https://www.workshopdata.com";
        //         $server = SecurityUtils::SERVER_HAYNES_PRO_WEB;
        //         $last_visit_time_haynespro = Helper::getTimeNowString();
        //         $is_web = true;
        //         $has_temp = true;
        //     }
        // }


        if ($has_temp  == false) {
            $check_expiry = $this->check_expiry($user, $server);
        }


        if ($check_expiry  !== true) {
            return     $check_expiry;
        }

        if ($user != null) {
            $user->update(Helper::sahaRemoveItemArrayIfNullValue([
                "last_visit_time_alldata" => $last_visit_time_alldata,
                "last_visit_time_autodata" => $last_visit_time_autodata,
                "last_visit_time_haynespro" => $last_visit_time_haynespro,
                "last_visit_time_tecdoc" => $last_visit_time_tecdoc,
                "last_visit_time_identifix" => $last_visit_time_identifix,
                "last_visit_time_mitchell_repair_center" => $last_visit_time_mitchell_repair_center,
                "last_visit_time_mitchell_prodemand" => $last_visit_time_mitchell_prodemand,
                "last_visit_time_haynespro_truck" => $last_visit_time_haynespro_truck,
                "last_visit_time_partslink24" => $last_visit_time_partslink24,
                "last_visit_time_kdsgds" => $last_visit_time_kdsgds,
                "last_visit_time_etka" => $last_visit_time_etka,
                "last_visit_time_ford_pts" => $last_visit_time_ford_pts,
                "last_visit_time_toyota_tis" => $last_visit_time_toyota_tis,
                "device_id" => $device_id,
            ]));

            if ($user->allow_web == false && $is_web == true) {
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_WEB[0],
                    'msg' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_WEB[1],
                ]);
            }
            if ($user->allow_mobile == false && $is_mobile == true) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_MOBILE[0],
                    'msg' => MsgCode::YOU_DO_NOT_HAVE_ACCESS_FROM_MOBILE[1],
                ], 400);
            }
        }
        $request->merge([
            'data3_server' => $server,
            'data3_domain' => $domain,
            'agency_sub' => $agency_sub,
        ]);


        return $next($request);
    }

    public function check_expiry($user, $server)
    {



        if ($user == null) return true;
        $has_expiry = true;

        $timeNow = Carbon::parse(Helper::getTimeNowString());
        if (
            $server == SecurityUtils::SERVER_ALL_DATA || $server == SecurityUtils::SERVER_ALL_DATA_WEB || $server ==  SecurityUtils::SERVER_ALL_DATA_EU_WEB_CUSTOM
        ) {
            if ($user->expiry_alldata == null || !Carbon::parse($user->expiry_alldata)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_AUTO_DATA || $server == SecurityUtils::SERVER_AUTO_DATA_WEB ||  $server == SecurityUtils::SERVER_AUTO_DATA_WEB_SAVED
        ) {
            $configAdmin =  ConfigAdmin::first();
            if ($configAdmin->temp_alldata_eu == 'AUTODATA' && Carbon::parse($user->expiry_alldata)->greaterThan($timeNow)) {
                $has_expiry == true;
            } else
            if ($user->expiry_autodata == null || !Carbon::parse($user->expiry_autodata)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_ALL_DATA_US_V2 || $server == SecurityUtils::SERVER_ALL_DATA_US_V2_WEB
        ) {
            if ($user->expiry_alldata ==  null || !Carbon::parse($user->expiry_alldata)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_HAYNES_PRO || $server == SecurityUtils::SERVER_HAYNES_PRO_WEB
        ) {
            $configAdmin =  ConfigAdmin::first();

            if ($configAdmin->temp_alldata_eu == 'HAYNESPRO' && Carbon::parse($user->expiry_alldata)->greaterThan($timeNow)) {
                $has_expiry == true;
            } else
            if ($user->expiry_haynespro == null || !Carbon::parse($user->expiry_haynespro)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_HAYNES_PRO_TRUCK_WEB
        ) {
            if ($user->expiry_haynespro_truck == null || !Carbon::parse($user->expiry_haynespro_truck)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_TECDOC_WEB
        ) {
            if ($user->expiry_tecdoc == null || !Carbon::parse($user->expiry_tecdoc)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_IDENTIFIX_WEB
        ) {
            $configAdmin =  ConfigAdmin::first();
            if ($configAdmin->temp_mitchell_prodemand == 'IDENTIFIX' && Carbon::parse($user->proxy_mitchell_prodemand)->greaterThan($timeNow)) {
                $has_expiry == true;
            } else
            if ($user->expiry_identifix == null || !Carbon::parse($user->expiry_identifix)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_KDS_GDS_WEB
        ) {
            if ($user->expiry_kdsgds == null || !Carbon::parse($user->expiry_kdsgds)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_MITCHELL_PRODEMAND_WEB
        ) {

            $configAdmin =  ConfigAdmin::first();

            if ($configAdmin->temp_alldata_us == 'MITCHELL_PRODEMAND' && Carbon::parse($user->expiry_alldata)->greaterThan($timeNow)) {
                $has_expiry == true;
            } else
            if ($user->expiry_mitchell_prodemand == null || !Carbon::parse($user->expiry_mitchell_prodemand)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_PARTSLINK24_WEB
        ) {
            if ($user->expiry_partslink24 == null || !Carbon::parse($user->expiry_partslink24)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }

        if (
            $server == SecurityUtils::SERVER_ETKA_WEB
        ) {
            $has_expiry = true;
        }

        if (
            $server == SecurityUtils::SERVER_FORD_PTS_WEB
        ) {
            if ($user->expiry_ford_pts == null || !Carbon::parse($user->expiry_ford_pts)->greaterThan($timeNow)) {
                $has_expiry = false;
            }
        }


        if ($has_expiry == false) {
            if (
                $server == SecurityUtils::SERVER_ALL_DATA_WEB ||
                $server == SecurityUtils::SERVER_AUTO_DATA_WEB ||
                $server == SecurityUtils::SERVER_AUTO_DATA_WEB_SAVED ||
                $server == SecurityUtils::SERVER_ALL_DATA_US_V2_WEB ||
                $server == SecurityUtils::SERVER_HAYNES_PRO_WEB ||
                $server == SecurityUtils::SERVER_HAYNES_PRO_TRUCK_WEB ||
                $server == SecurityUtils::SERVER_TECDOC_WEB ||
                $server == SecurityUtils::SERVER_IDENTIFIX_WEB ||
                $server == SecurityUtils::SERVER_KDS_GDS_WEB ||
                $server == SecurityUtils::SERVER_MITCHELL_PRODEMAND_WEB ||
                $server == SecurityUtils::SERVER_PARTSLINK24_WEB ||
                $server == SecurityUtils::SERVER_ETKA_WEB ||
                $server == SecurityUtils::SERVER_FORD_PTS_WEB ||
                $server == SecurityUtils::SERVER_TOYOTA_TIS_WEB
            ) {
                return ViewUtils::viewErrorPage([
                    'msg_code' => MsgCode::EXPIRED[0],
                    'msg' => MsgCode::EXPIRED[1],
                ]);
            }

            if (
                $server == SecurityUtils::SERVER_ALL_DATA ||
                $server == SecurityUtils::SERVER_AUTO_DATA ||
                $server == SecurityUtils::SERVER_ALL_DATA_EU_WEB_CUSTOM ||
                $server == SecurityUtils::SERVER_ALL_DATA_US_V2 ||
                $server == SecurityUtils::SERVER_HAYNES_PRO
            ) {
                return response()->json([
                    'code' => 400,
                    'success' => false,
                    'msg_code' => MsgCode::EXPIRED[0],
                    'msg' => (MsgCode::EXPIRED[1]) . "(" . ($user->id) . ")" . $user->expiry_haynespro,
                ], 400);
            }
        }


        return true;
    }

    public function check_ip_avalible_for_autodata($ip) {}
}
