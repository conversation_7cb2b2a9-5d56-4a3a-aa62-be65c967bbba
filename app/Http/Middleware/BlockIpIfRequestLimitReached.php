<?php

namespace App\Http\Middleware;

use App\Models\BlockedIp;
use App\Models\HistoryBlockIP;
use App\Models\MsgCode;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Helper\IPUtils;

class BlockIpIfRequestLimitReached
{
    public function handle(Request $request, Closure $next)
    {

        $ip = IPUtils::getIP();
        if (!isset($_SERVER['SERVER_ADDR'])) {
            return $next($request);
        }
        if (isset($_SERVER['SERVER_ADDR']) && $_SERVER['SERVER_ADDR'] ==  $ip || $_SERVER['SERVER_ADDR'] == "************"  || $_SERVER['SERVER_ADDR'] == "************") {
            return $next($request);
        }
        $tailLink =  $request->fullUrl();

        if (str_contains($tailLink, "7dETZK3okVEW") || str_contains($tailLink, "1gfg6K") ||  str_contains($tailLink, "LogJsError") ||  str_contains($tailLink, "js") ||  str_contains($tailLink, "css")) {
            return $next($request);
        }

        if ($request->method() == "POST") {
            $formData = json_encode($request->json()->all());
            if (str_contains($formData, 'sensor_data')) {
                return $next($request);
            }
        }

        try {



            $blockedIpWebs = [
                "5.39.217.*",
                "158.222.*.*",
                "24.45.226.*"
            ];


            foreach ($blockedIpWebs as $pattern) {
                // Thay thế * thành (0-255), phù hợp với các dải IP hợp lệ
                $regex = '/^' . str_replace(['*', '.'], ['(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)', '\.'], $pattern) . '$/';

                if (preg_match($regex, $ip)) {
                    return response()->view('suspension_block_ip');
                }
            }


            //Nếu là ip cloudflare bỏ qua
            if (str_contains($ip, "172.") || str_contains($ip, "162.") || str_contains($ip, "108.")) {
                return $next($request);
            }

            $blocked = Cache::get("blocked_ip" . $ip); // 1 ngày
            if ($blocked  == true) {
                return response()->json([
                    'code' => 429,
                    'success' => false,
                    'msg_code' =>  MsgCode::ERROR[0],
                    'msg' => "You have been blocked 2",
                ], 429);
            }
            $blockExists = BlockedIp::where('ip', $ip)->first();
            if ($blockExists  != null) {
                Cache::put("blocked_ip" . $ip, true, now()->addMinutes(60 * 1)); // 1 ngày

                return response()->json([
                    'code' => 429,
                    'success' => false,
                    'msg_code' =>  MsgCode::ERROR[0],
                    'msg' => "You have been blocked 1",
                ], 429);
            }

            // Đặt ngưỡng số lần yêu cầu (ví dụ: 100 yêu cầu trong 1 phút)
            $limit = 120;
            $expiry = Carbon::now()->addMinutes(1);



            if ($ip != null) {

                $key_block = 'blocked_ip:' . $ip .  $tailLink;
                $key_count_request = 'request_count:' . $ip .  $tailLink;

                // Kiểm tra xem địa chỉ IP đã bị chặn hay chưa
                $blockedUntil = Cache::get($key_block);

                if ($blockedUntil && $blockedUntil > now()) {
                    //Khóa tiếp khi cố gắng truy cập tiếp
                    Cache::put($key_block, $expiry, $expiry);

                    // Địa chỉ IP đã bị chặn, trả về thông báo hoặc lỗi 429 Too Many Requests
                    return response()->json([
                        'code' => 429,
                        'success' => false,
                        'msg_code' =>  MsgCode::ERROR[0],
                        'msg' => "Too Many Requests",
                    ], 429);
                }

                if (!Cache::has($key_count_request)) {
                    Cache::put($key_count_request, 0, now()->addMinutes(1)); // Đặt TTL 1 phút
                }

                // Địa chỉ IP chưa bị chặn, kiểm tra và tăng số lần yêu cầu
                $requestCount = Cache::increment($key_count_request);

                // Nếu vượt quá ngưỡng, chặn địa chỉ IP
                if ($requestCount > $limit) {
                    Cache::put($key_block, $expiry, $expiry);

                    HistoryBlockIP::create([
                        "ip" =>  $ip,
                        "tail_link" => $tailLink
                    ]);

                    $blockExists = BlockedIp::where('ip', $ip)->first();
                    $user = DB::table('users')->where('ip_using', $ip)->first();

                    Cache::put("blocked_ip" . $ip, true, now()->addMinutes(60 * 1)); // 1 ngày

                    $data = [
                        "ip" =>  $ip,
                        "tail_link" => $tailLink,
                        "user_id" =>   $user == null ? null :   $user->id,
                        "headers" =>  json_encode($request->headers->all()),
                        "form_data" => json_encode($request->json()->all()),
                        "method" => $request->method()
                    ];
                    if ($blockExists  == null) {
                        BlockedIp::create($data);
                    } else {
                        $blockExists->update($data);
                    }


                    return response()->view('suspension');
                }
            }
        } catch (Exception $e) {
        }

        return $next($request);
    }
}
