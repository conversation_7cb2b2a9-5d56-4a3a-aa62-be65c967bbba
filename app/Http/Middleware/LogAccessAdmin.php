<?php

namespace App\Http\Middleware;

use App\Models\Agency;
use Closure;
use Illuminate\Http\Request;
use App\Models\HistoryAccessAdmin;
use App\Models\SessionUser;
use App\Models\User;

class LogAccessAdmin
{
    public function handle(Request $request, Closure $next)
    {
        // Bắt đầu thời gian <PERSON><PERSON> lý request
        $startTime = microtime(true);

        // Thực hiện request
        $response = $next($request);

        // Kết thúc thời gian x<PERSON> lý request
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;


        $token = request()->header('dr-token') ?? $cookies['dr-token'] ?? request()->header('token') ?? $cookies['token'] ?? null;
        $checkTokenIsValidForUser = SessionUser::where('token', $token)->first();

        $user_id = null;
        $agency_id = null;
        $agency_code = null;
        if (!empty($checkTokenIsValidForUser)) {
            $user =  User::where('id', $checkTokenIsValidForUser->user_id)->first();
            if ($user) {
                $user_id = $user->id;
            }
            $agency = Agency::where('user_id',  $checkTokenIsValidForUser->user_id)->first();
            if ($agency) {
                $agency_id = $agency->id;
                $agency_code = $agency->agency_code;
            }
        }


        // Lưu thông tin truy cập vào MongoDB
        $history = new HistoryAccessAdmin();
        $history->method = $request->method();
        $history->url = $request->fullUrl();
        $history->ip = $request->ip();
        $history->user_agent = $request->header('User-Agent');
        $history->referrer = $request->header('referer');
        $history->headers = json_encode($request->headers->all());
        $history->data = json_encode($request->all());
        $history->user_id = $user_id;
        $history->agency_id = $agency_id;
        $history->agency_code = $agency_code;
        $history->status_code = $response->getStatusCode();
        $history->execution_time = $executionTime;
        $history->response = json_encode($response->getContent());
        $history->save();

        return $response;
    }
}
