<?php

namespace App\Http\Middleware;

use App\Models\Admin;
use App\Models\MsgCode;
use App\Models\SessionAdmin;
use Closure;
use Illuminate\Http\Request;


class CheckDelete
{

    public function handle(Request $request, Closure $next)
    {
        $token = request()->header('dr-token') ?? $cookies['dr-token'] ?? request()->header('token') ?? $cookies['token'] ?? null;
        $checkTokenIsValid = SessionAdmin::where('token',$token)->first();
        $admin = Admin::where('id', $checkTokenIsValid->admin_id)->first();

        if ($admin != null && $admin->is_admin == true) {
            return $next($request);
        }
        if ($admin  == null|| $admin->allow_delete == false) {
            return response()->json([
                'code' => 400,
                'msg_code' => MsgCode::NO_DELETE_PERMISSION[0],
                'msg' => MsgCode::NO_DELETE_PERMISSION[1],
                'success' => false,
            ]);
        }

        return $next($request);
    }
}
