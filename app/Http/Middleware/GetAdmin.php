<?php

namespace App\Http\Middleware;

use App\Helper\IPUtils;
use App\Models\Admin;
use App\Models\Agency;
use App\Models\Employee;
use App\Models\MsgCode;
use App\Models\SessionAdmin;
use App\Models\SessionEmployee;
use App\Models\SessionUser;
use App\Models\User;
use Closure;
use <PERSON>bauman\Location\Facades\Location;
use Illuminate\Http\Request;


class GetAdmin
{

    public function handle(Request $request, Closure $next)
    {


        $token = request()->header('dr-token') ?? $cookies['dr-token'] ?? request()->header('token') ?? $cookies['token'] ?? null;
        $checkTokenIsValidForUser = SessionUser::where('token', $token)->first();

        if (empty($token)) {

            return response()->json([
                'code' => 401,
                'msg_code' => MsgCode::NO_TOKEN[0],
                'msg' => MsgCode::NO_TOKEN[1],
                'success' => false,
            ], 401);
        } else if (empty($checkTokenIsValidForUser)) {
            return response()->json([
                'code' => 401,
                'msg_code' => MsgCode::NOT_HAVE_ACCESS[0],
                'msg' => MsgCode::NOT_HAVE_ACCESS[1],
                'success' => false,
            ], 401);
        } else {

            $user =  User::where('id', $checkTokenIsValidForUser->user_id)->first();
            $agency = Agency::where('user_id',  $checkTokenIsValidForUser->user_id)->first();

            if ($user  != null) {
                $newIp =  IPUtils::getIP();
                if ($newIp  != null && $newIp != $user->last_ip_admin_using) {
                    $user->update([
                        "last_ip_admin_using" => IPUtils::getIP()
                    ]);
                }
            }

            if ($position = Location::get(IPUtils::getIP())) {
                if ($agency != null && $position->countryName == 'Algeria') {
                    if (!str_contains($agency->name, '+213')) {
                        return response()->json([
                            'code' => 401,
                            'msg_code' => MsgCode::NOT_HAVE_ACCESS[0],
                            'msg' => MsgCode::NOT_HAVE_ACCESS[1],
                            'success' => false,
                        ], 401);
                    }
                }
            }


            $request->merge([
                'user' => $user,
                'agency' => $agency
            ]);

            return $next($request);
        }

        return response()->json([
            'code' => 401,
            'msg_code' => MsgCode::NOT_HAVE_ACCESS[0],
            'msg' => MsgCode::NOT_HAVE_ACCESS[1],
            'success' => false,
        ], 401);
    }
}
