<?php

namespace App\Http\Middleware;

use App\Models\MsgCode;
use App\Models\PlanService;
use App\Models\User;
use Closure;

class PaymentCheckPlanAndUser
{
    public function handle($request, Closure $next)
    {

        $email = request('email');
        $product_id = request('product_id'); //in  plan service
        $plan_id = request('plan_id'); //plan id hoac product id
        $plan_ids = request("plan_ids") == null ? [] : explode(',', request("plan_ids"));

        $plan_ids_input = array();
        if ($plan_id  != null) {
            $plan_ids_input = [$plan_id];
        }
        foreach ($plan_ids_input as $key => $va) {
            if (!is_numeric($va)) {
                unset($plan_ids_input[$key]);
            }
        }

        if (count($plan_ids_input) == 0) {
            $plan_ids_input = $plan_ids;
        }
        foreach ($plan_ids_input as $key => $va) {
            if (!is_numeric($va)) {
                unset($plan_ids_input[$key]);
            }
        }




        $email = request('email');


        if (
            count($plan_ids_input) == 0
            && $product_id  == null
        ) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_PLAN_EXISTS[0],
                'msg' => MsgCode::NO_PLAN_EXISTS[1],
            ], 400);
        }
        if ($email == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }
        ////

        $plans =  array();
        $price = 0;
        if (count($plan_ids_input) > 0) {
            $plans =   PlanService::whereIn('id',  $plan_ids_input)->get();
            $before_price =   PlanService::whereIn('id',  $plan_ids_input)->sum('price');
            $product_id  = null;
        }
        if ($plans == null) {
            $before_price =   PlanService::where('product_id',  $product_id)->limit(1)->sum('price');
            $plans =  PlanService::where('product_id',  $product_id)->limit(1)->get();
        }

        $desciption = "";
        foreach ($plans as $plan) {
            $price  = $price + $plan->price;
            $desciption = $plan . ($plan->service . " " . $plan->month . " month") . ",";
        }


        if (count($plans) == 0) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_PLAN_EXISTS[0],
                'msg' => MsgCode::NO_PLAN_EXISTS[1],
            ], 400);
        }


        $user =  User::where('email', $email)->first();

        if ($user  == null) {
            return response()->json([
                'code' => 400,
                'success' => false,
                'msg_code' => MsgCode::NO_USER_EXISTS[0],
                'msg' => MsgCode::NO_USER_EXISTS[1],
            ], 400);
        }

        $request->merge([
            'user' => $user,
            'plans' => $plans,
            'price' => $price,
            'product_id' =>  $product_id,
            'plan_ids_input' => $plan_ids_input,
            'before_price' => $before_price,
            'code_voucher' => $request->voucher == null ? null : $request->voucher->code_voucher,
            'desciption' => $desciption
        ]);

        return $next($request);
    }
}
