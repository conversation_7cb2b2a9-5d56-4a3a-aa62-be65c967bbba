<?php

namespace App\Http\Middleware;

use App\Helper\IPUtils;
use App\Models\Admin;
use App\Models\Agency;
use App\Models\Employee;
use App\Models\MsgCode;
use App\Models\SessionAdmin;
use App\Models\SessionEmployee;
use App\Models\SessionUser;
use App\Models\User;
use App\Models\Voucher;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;


class VoucherCheck
{

    public function handle(Request $request, Closure $next)
    {

        $code_voucher = $request->query('code_voucher');

        $voucherExists = Voucher::where('on_start', true)->where('code_voucher',  strtoupper($code_voucher??"$#%#$%"))
            ->where('end_time', ">=", Carbon::now()->format('Y-m-d H:i:s'))->first();

        $request->merge([
            'voucher' => $voucherExists
        ]);

        return $next($request);
    }
}
