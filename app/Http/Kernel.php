<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        // \Fruitcake\Cors\HandleCors::class,
        \App\Http\Middleware\TrustProxies::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
    ];
    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            //\App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            // \Fruitcake\Cors\HandleCors::class,
        ],


        'api' => [
            // \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            'throttle:300,1',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],



        'admin_auth' => [\App\Http\Middleware\AdminLogin::class], //buộc đăng nhập admin
        'get_admin' => [\App\Http\Middleware\GetAdmin::class], //buộc đăng nhập admin
        'voucher_check' => [\App\Http\Middleware\VoucherCheck::class], //kiểm tra voucher
        'agency_auth' => [\App\Http\Middleware\AgencyLogin::class], //buộc đăng nhập agency
        'user_auth' => [\App\Http\Middleware\UserLogin::class], //buộc đăng nhập user
        'check_security_web' => [\App\Http\Middleware\CheckSecurityWeb::class], //Kiểm tra bảo mật quá
        'get_user_web' => [\App\Http\Middleware\GetUserLoginWeb::class], //Lấy thông tin user
        'check_security_kdsgds' => [\App\Http\Middleware\CheckSecurityGdsKds::class], //Kiểm tra bảo mật quá kds
        'check_device' => [\App\Http\Middleware\CheckDevice::class], //check_device
        'get_user' => [\App\Http\Middleware\GetUserLogin::class], //Lấy thông tin user
        'mid_res' => [\App\Http\Middleware\JsonResponseMiddleware::class], //Lấy thông tin user
        'payment_check_user_service' => [\App\Http\Middleware\PaymentCheckPlanAndUser::class], //Lấy thông tin user
        'payment_check_plan_device_user' => [\App\Http\Middleware\PaymentCheckPlanDeviceAndUser::class], //Lấy thông tin user
        'has_agency_code' => [\App\Http\Middleware\HasAgencyCode::class], //Kiểm tra code agency

        'log_access_admin' => [\App\Http\Middleware\LogAccessAdmin::class],
        'timezone' => [\App\Http\Middleware\TimeZoneMiddleware::class],

        'custom_router_throttle' => [
            \App\Http\Middleware\RouterThrottle::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'cors'    => \App\Http\Middleware\Cors::class, // added
        'blockip' => \App\Http\Middleware\BlockIpIfRequestLimitReached::class,
    ];
}
