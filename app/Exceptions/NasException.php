<?php

namespace App\Exceptions;

use Exception;

class NasException extends Exception
{
    // Constructor to accept a custom message
    public function __construct($msg)
    {
        // Call parent constructor with the custom message
        parent::__construct($msg);
    }

    public function report()
    {
        // Optional: log or send notifications
    }

    public function render($request)
    {
        // Customize the response
        return response()->json([
            'error' => 'Custom Exception Occurred',
            'message' => $this->getMessage(), // This will display the message passed to the constructor
        ], 400);
    }
}
