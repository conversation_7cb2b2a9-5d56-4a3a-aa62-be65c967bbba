<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlanService extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $casts = [
        'public' => 'boolean',
        'pay_in_link' => 'boolean',
    ];

    protected $appends = [
        'discount'
    ];

    public function getPriceAttribute()
    {
        $voucher = request('voucher', $default = null);
        if ($voucher != null &&    is_array($voucher->voucher_items)) {
            foreach ($voucher->voucher_items as $item) {
                if ($item->service == $this->attributes['service']) {
                    $valueDiscount = (float)($item->value);

                    $priceNew =  round(($this->attributes['price'] * (100 - $valueDiscount)) / 100, 2);
                    return $priceNew;
                }
            }
        }


        return  $this->attributes['price'];
    }

    public function getDiscountAttribute()
    {
        $voucher = request('voucher', $default = null);
        if ($voucher != null &&    is_array($voucher->voucher_items)) {
            foreach ($voucher->voucher_items as $item) {
                if ($item->service == $this->attributes['service']) {
                    $valueDiscount = (float)($item->value);

                    if( $valueDiscount  == 0) return null;
                    return [
                        "discount_value" => $valueDiscount,
                        "last_price" => $this->attributes['price']
                    ];
                }
            }
        }


        return  null;
    }
}
