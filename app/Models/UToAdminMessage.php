<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UToAdminMessage extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $appends = ['user',     'images',];
    public $incrementing = false;

    protected $casts = [
        'is_admin' => 'boolean',
        'seen' => 'boolean'
    ];

    protected $hidden = ['images_json'];

    public function getImagesAttribute()
    {
        return json_decode($this->images_json);
    }

    public function getUserAttribute()
    {

        if ($this->user_id != null) {
            return User::select('id', 'name', 'avatar_image', 'email')->where('id', $this->user_id)->first();
        } else {
            return User::select('id', 'name', 'avatar_image', 'email')->where('device_id', $this->device_id)->first();
        }
    }
}
