<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HistoryAccess extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $casts = [
        'is_mobile' => "boolean"
    ];

    //  php artisan migrate --path=/database/migrations/2023_10_15_143010_create_history_accesses_table.php --database=mysql_history
    protected $connection = 'mysql_history';
}
