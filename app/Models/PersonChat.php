<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Base\BaseModel;
class PersonChat extends BaseModel
{
    use HasFactory;
    protected $guarded = [];
    protected $casts = [
        'seen' => 'boolean',
    ];

    protected $appends = ['to_user'];


    public function getToUserAttribute()
    {

        $user = User::select('id', 'name', 'avatar_image')->where('id', $this->vs_user_id)->get()->first();
        return $user;
    }
}
