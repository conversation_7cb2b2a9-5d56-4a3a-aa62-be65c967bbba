<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Base\BaseModel;

class ConfigAdmin extends BaseModel
{
    use HasFactory;
    protected $guarded = [];
    protected $appends = ['alldata_info', 'autodata_info', 'autodata_italy_info',  'alldata_us_v2_info', 'haynes_info', 'haynes_truck_info', 'mitchell_repair_center_info', 'mitchell_prodemand_info', 'tecdoc_info', 'partslink24_info', 'identifix_info', 'ford_pts_info', 'etka_info'];

    protected $casts = [
        'cookies_alldata_str' => 'string',
        'cookies_autodata_str' => 'string',
        'cookies_autodata_italy_str' => 'string',
        'cookies_alldata_us_v2_str' => 'string',
        'cookies_haynes_str' => 'string',
        'cookies_haynes_truck_str' => 'string',
        'cookies_mitchell_repair_center_str' => 'string',
        'cookies_mitchell_prodemand' => 'string',
        'cookies_tecdoc_str' => 'string',
        'cookies_partslink24_str' => 'string',
        'cookies_identifix_str' => 'string',
        'cookies_ford_pts_str' => 'string',
        'cookies_etka_str' => 'string',
        
    ];

    public function getAlldataInfoAttribute()
    {
        return json_decode($this->cookies_alldata_json_info);
    }

    public function getAutodataInfoAttribute()
    {
        return json_decode($this->cookies_autodata_json_info);
    }

    public function getAutodataItalyInfoAttribute()
    {
        return json_decode($this->cookies_autodata_italy_json_info);
    }

    public function getAlldataUsV2InfoAttribute()
    {
        return json_decode($this->cookies_alldata_us_v2_json_info);
    }
    public function getHaynesInfoAttribute()
    {
        return json_decode($this->cookies_haynes_json_info);
    }
    public function getHaynesTruckInfoAttribute()
    {
        return json_decode($this->cookies_haynes_truck_json_info);
    }
    public function getMitchellRepairCenterInfoAttribute()
    {
        return json_decode($this->cookies_mitchell_repair_center_json_info);
    }
    public function getMitchellProdemandInfoAttribute()
    {
        return json_decode($this->cookies_mitchell_prodemand_json_info);
    }
    public function getTecdocInfoAttribute()
    {
        return json_decode($this->cookies_tecdoc_json_info);
    }
    public function getPartslink24InfoAttribute()
    {
        return json_decode($this->cookies_partslink24_json_info);
    }
    public function getIdentifixInfoAttribute()
    {
        return json_decode($this->cookies_identifix_json_info);
    }
    public function getFordPtsInfoAttribute()
    {
        return json_decode($this->cookies_ford_pts_json_info);
    }
    public function getEtkaInfoAttribute()
    {
        return json_decode($this->cookies_etka_json_info);
    }
}
