<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminChat extends Model
{
    use HasFactory;
    protected $guarded = [];

    public $timestamps = false;


    protected $casts = [
        'updated_at' => 'datetime',
    ];

    protected $appends = ['user'];

    public function getUserAttribute()
    {

        if ($this->user_id != null) {
            return User::select('id', 'name', 'avatar_image', 'email', 'username')->where('id', $this->user_id)->first();
        } else {
            return User::select('id', 'name', 'avatar_image', 'email', 'username')->where('device_id', $this->device_id)->first();
        }
    }
}
