<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Nicolaslopezj\Searchable\SearchableTrait;

class Product extends Model
{
    use SearchableTrait;

    use HasFactory;
    protected $guarded = [];

    protected $with = [
        'categories',
    ];

    protected $casts = [
        'is_show_home' => 'boolean'
    ];

    protected $searchable = [
        'columns' => [
            'name_str_filter' => 1,
            'name' => 2,
        ],
    ];

    public function categories()
    {
        return $this->belongsToMany(
            'App\Models\Category',
            'product_categories',
            'product_id',
            'category_id'
        );
    }
}
