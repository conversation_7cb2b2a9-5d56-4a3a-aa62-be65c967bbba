<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Base\BaseModel;

class Group extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $casts = [
        'off_chat' => 'boolean',
    ];
    
    protected $appends = ['total_member'];


    public function getTotalMemberAttribute()
    {

        $total = GroupMember::where('group_id', $this->id)->count();
        return $total;
    }
}
