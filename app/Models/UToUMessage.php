<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Base\BaseModel;
class UToUMessage extends Model
{
    use HasFactory;
    protected $guarded = [];
    public $incrementing = false;
    protected $appends = [
        'images',
        'vs_user'
    ];

    protected $casts = [
        'vs_user_id' => "integer",
        'is_sender' => 'boolean',
        'is_remove' => 'boolean'
    ];
    protected $hidden = ['images_json'];


    public function getImagesAttribute()
    {
        return json_decode($this->images_json);
    }

    public function getVsUserAttribute()
    {
        return User::select('id','name','avatar_image' )->where('id', $this->vs_user_id)->first();
    }

}
