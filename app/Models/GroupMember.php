<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Base\BaseModel;
class GroupMember extends BaseModel
{
    use HasFactory;
    protected $guarded = [];

    protected $appends = ['user'];

    protected $casts = [
        'is_key' => 'boolean'
    ];
    public function getUserAttribute()
    {

        $user = User::select('id', 'name', 'avatar_image')->where('id', $this->user_id)->get()->first();
        return $user;
    }

}
