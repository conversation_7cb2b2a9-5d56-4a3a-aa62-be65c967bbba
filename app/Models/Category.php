<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $hidden = ['store_id', 'pivot', 'created_at', 'updated_at'];

    protected $casts = [
        'is_show_home' => 'boolean',
    ];

    public function getTotalProducts()
    {
        $ids_product = ProductCategory::where('category_id', $this->id)->pluck('product_id');
        $count = Product::whereIn('id', $ids_product)->where('status', '!=', 1)->count();

        return      $count;
    }
}
