<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Nicolaslopezj\Searchable\SearchableTrait;

class EmailSupport extends Model
{
    use SearchableTrait;
    use HasFactory;
    protected $guarded = [];

    protected $searchable = [
        'columns' => [
            'title' => 1,
        ],
    ];

    protected $casts = [
        'send_time' => 'datetime',
        'running' => 'boolean'
    ];
}
