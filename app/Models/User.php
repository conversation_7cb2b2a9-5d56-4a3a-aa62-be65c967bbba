<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON>lopezj\Searchable\SearchableTrait;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;
    use SearchableTrait;
    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $guarded = [];


    protected $appends = ['is_friend'];

    public function getIsFriendAttribute()
    {
        $user = request('user', $default = null);
        if ($user  != null && ($user->id ?? null) != null) {
            return UserFriend::where('user_id', $user->id)->where('friend_user_id', $this->id)->first() != null;
        }
        return false;
    }


    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $searchable = [
        /**
         * Columns and their priority in search results.
         * Columns with higher values are more important.
         * Columns with equal values have equal importance.
         *
         * @var array
         */
        'columns' => [
            'users.email' => 0,
            'users.username' => 1,
            'users.name' => 2,
            'users.ip_using' => 3,
            'users.device_id' => 4,
        ],
    ];


    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_admin' => 'boolean',
        'is_block' => 'boolean',
        'is_user_ios' => 'boolean',
        'is_user_apple' => 'boolean',


        'expiry_alldata' => 'datetime',
        'expiry_autodata' => 'datetime',
        'expiry_autodata_italy' => 'datetime',
        'expiry_haynespro' => 'datetime',
        'expiry_identifix' => 'datetime',
        'expiry_mitchell_repair_center' => 'datetime',
        'expiry_mitchell_prodemand' => 'datetime',
        'expiry_haynespro_truck' => 'datetime',
        'expiry_partslink24' => 'datetime',
        'expiry_kdsgds' => 'datetime',
        'expiry_etka' => 'datetime',
        'expiry_tecdoc' => 'datetime',
        'expiry_ford_pts' => 'datetime',
        'expiry_toyota_tis' => 'datetime',

        'last_visit_time' => 'datetime',

        'last_visit_time_alldata' => 'datetime',
        'last_visit_time_autodata' => 'datetime',
        'last_visit_time_autodata_italy' => 'datetime',
        'last_visit_time_haynespro' => 'datetime',
        'last_visit_time_identifix' => 'datetime',
        'last_visit_time_mitchell_repair_center' => 'datetime',
        'last_visit_time_mitchell_prodemand' => 'datetime',
        'last_visit_time_haynespro_truck' => 'datetime',
        'last_visit_time_partslink24' => 'datetime',
        'last_visit_time_kdsgds' => 'datetime',
        'last_visit_time_etka' => 'datetime',
        'last_visit_time_tecdoc' => 'datetime',
        'last_visit_time_ford_pts' => 'datetime',
        'last_visit_time_toyota_tis' => 'datetime',

        'allow_mobile' => 'boolean',
        'allow_web' => 'boolean',

    ];
}
