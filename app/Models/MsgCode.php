<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Base\BaseModel;

class MsgCode extends BaseModel
{
    use HasFactory;

    const CANNOT_POST_PICTURES = [
        "CANNOT_POST_PICTURES",
        "Unable to post photo"
    ];

    const STATUS_OK = 2;
    const STATUS_ERR = 1;




    const ALLDATA_EU_V1_OF_MAINTENANCE = ["ALLDATA_EU_V1_OF_MAINTENANCE", "Sorry! ALLData EU of maintenance"];
    const ALLDATA_US_V2_OF_MAINTENANCE = ["ALLDATA_US_V2_OF_MAINTENANCE", "Sorry! ALLData US of maintenance"];
    const AUTODATA_OF_MAINTENANCE = ["AUTODATA_OF_MAINTENANCE", "Sorry! AutoData of maintenance"];
    const AGENT_WITH_THIS_USER_ALREADY_EXISTS = ["AGENT WITH THIS USER ALREADY EXISTS", "Agent with this user already exists"];
    const AGENCY_CODE_ALREADY_EXISTS = ["AGENCY_CODE_ALREADY_EXISTS", "Agency code already exists"];
    const CODE_ALREADY_EXISTS = ["CODE_ALREADY_EXISTS", "Code already exists"];
    const CODE_IS_REQUIRED = ["CODE_IS_REQUIRED", "Code cannot be empty"];
    const CANT_SEND_OTP = ["CANT_SEND_OTP", "Unable to send code"];
    const CONTENT_IS_REQUIRED = ["CONTENT_IS_REQUIRED", "Content cannot be empty"];

    const EMAIL_ALREADY_EXISTS = ["EMAIL_ALREADY_EXISTS", "Email already exists"];
    const BONUS_EXISTS = ["BONUS_EXISTS", "Bonus already exists"];
    const ERROR = ["ERROR", "An error occurred"];
    const ERROR_LOGGED_IN_ON_ANOTHER_DEVICE = ["ERROR_LOGGED_IN_ON_ANOTHER_DEVICE", "Login on another device"];

    const EXPIRED_PIN_CODE = ["EXPIRED_PIN_CODE", "Pin code has expired please request a new one"];
    const GREAT_TIME = ["GREAT_TIME", "Time is too large"];
    const FACEBOOK_ID_EXISTS = ["FACEBOOK_ID_EXISTS", "Facebook id already exists"];
    const HAVE_AN_UNPAID_REQUEST = ["HAVE_AN_UNPAID_REQUEST", "Previous payment request"];
    const HAYNES_OF_MAINTENANCE = ["HAYNES_OF_MAINTENANCE", "Sorry! HaynesPro of maintenance"];
    const IDENTIFIX_OF_MAINTENANCE = ["IDENTIFIX_OF_MAINTENANCE", "Sorry! Identifix of maintenance"];
    const TOYOTA_TIS_OF_MAINTENANCE = ["TOYOTA_TIS_OF_MAINTENANCE", "Sorry! Toyota of maintenance"];
    const ETKA_OF_MAINTENANCE = ["ETKA_OF_MAINTENANCE", "Sorry! ETKA of maintenance"];
    const MITCHELL_REPAIR_CENTER_OF_MAINTENANCE = ["MITCHELL_REPAIR_CENTER_OF_MAINTENANCE", "Sorry! Mitchell repair center of maintenance"];
    const MITCHELL_PRODEMAND_OF_MAINTENANCE = ["MITCHELL_PRODEMAND_OF_MAINTENANCE", "Sorry! Mitchell prodemand of maintenance"];
    const TECDOC_OF_MAINTENANCE = ["TECDOC_OF_MAINTENANCE", "Sorry! Tecdoc of maintenance"];
    const PARTSLINK24_OF_MAINTENANCE = ["PARTLINK24_OF_MAINTENANCE", "Sorry! Partslink24 of maintenance"];
    const PARTSOUQ_OF_MAINTENANCE = ["PARTSOUQ_OF_MAINTENANCE", "Sorry! Partsouq of maintenance"];
    const FORD_PTS_OF_MAINTENANCE = ["FORD_PTS_OF_MAINTENANCE", "Sorry! Ford PTS of maintenance"];
    const HAYNESPRO_TRUCK_OF_MAINTENANCE = ["HAYNESPRO_TRUCK_OF_MAINTENANCE", "Sorry! Haynespro Truck of maintenance"];
    const INVALID_PHONE_NUMBER = ["INVALID_PHONE_NUMBER", "Invalid phone number"];
    const CAN_NOT_USE = ["CAN_NOT_USE", "This store code cannot be used"];
    const VALUE_MUST_BE_GREATER_THAN_0 = ["VALUE_MUST_BE_GREATER_THAN_0", "Vote value must be greater than 0"];
    const NO_TOKEN = ["NO_TOKEN", "If you are not logged in, you do not have access"];
    const NOT_HAVE_ACCESS = ["NOT_HAVE_ACCESS", "You do not have access rights"];
    const NO_POST_EXISTS = ["NO_POST_EXISTS", "No post exists"];
    const NO_COMMENT_EXISTS = ["NO_COMMENT_EXISTS", "No comment exists"];
    const NO_ACCOUNT_EXISTS = ["NO_ACCOUNT_EXISTS", "Account does not exist"];
    const NO_MARKET_EXISTS = ["NO_LMARKET_EXISTS", "The period does not exist"];
    const NO_WITH_DRAW_EXISTS = ["NO_WITH_DRAW_EXISTS", "Withdrawal request does not exist"];
    const NO_RECHARGE_EXISTS = ["NO_RECHARGE_EXISTS", "Deposit does not exist"];
    const NO_EMAIL_EXISTS = ["NO_EMAIL_EXISTS", "Email does not exist"];
    const NO_USER_EXISTS = ["NO_USER_EXISTS", "Customer does not exist"];
    const NO_USERNAME_EXISTS = ["NO_USERNAME_EXISTS", "Username does not exist"];
    const NO_AGENCY_EXISTS = ["NO_AGENCY_EXISTS", "Agency does not exist"];
    const NO_GROUP_EXISTS = ["NO_GROUP_EXISTS", "Group does not exist"];
    const NO_GROUP_USER = ["NO_GROUP_USER", "Group does not exist"];
    const NO_STAFF_EXISTS = ["NO_STAFF_EXISTS", "Employee does not exist"];
    const NO_PLAN_EXISTS = ["NO_PLAN_EXISTS", "Plan does not exist"];
    const NO_DEVICE_TOKEN = ["NO_DEVICE_TOKEN", "No device token"];
    const NO_DEVICE_LOGIN = ["NO_DEVICE_LOGIN", "No device"];
    const NOT_FOUND = ["NOT_FOUND", "Not found"];
    const USERNAME_IS_REQUIRED = ["USERNAME_IS_REQUIRED", "Username cannot be empty"];
    const NAME_IS_REQUIRED = ["NAME_IS_REQUIRED", "Name cannot be empty"];
    const USERNAME_ALREADY_EXISTS = ["USERNAME_ALREADY_EXISTS", "Username already exists"];
    const UNAUTHENTICATED_ACCOUNT = ["UNAUTHENTICATED_ACCOUNT", "Unauthenticated account"];
    const UNVERIFIED_EMAIL = ["UNVERIFIED_EMAIL", "Your email is not authenticated"];
    const UNABLE_TO_FIND_THE_UPLOAD_IMAGE = ["UNABLE_TO_FIND_THE_UPLOAD_IMAGE", "Upload not found"];
    const YOU_DO_NOT_HAVE_ACCESS_FROM_WEB = ["YOU_DO_NOT_HAVE_ACCESS_FROM_WEB", "You do not have access from web, please contact admin"];
    const YOU_DO_NOT_HAVE_ACCESS_FROM_MOBILE = ["YOU_DO_NOT_HAVE_ACCESS_FROM_MOBILE", "You do not have access from mobile, please contact admin"];
    const YOU_HAVE_BEEN_BLOCKED = ["YOU_HAVE_BEEN_BLOCKED", "You have been blocked"];
    const PASSWORD_IS_REQUIRED = ["PASSWORD_IS_REQUIRED", "Password cannot be empty"];
    const PASSWORD_NOT_LESS_THAN_6_CHARACTERS = ["PASSWORD_NOT_LESS_THAN_6_CHARACTERS", "Password must be greater than 6 characters"];
    const PHONE_NUMBER_ALREADY_EXISTS = ["PHONE_NUMBER_ALREADY_EXISTS", "Phone number already exists"];
    const SUCCESS = ["SUCCESS", "Success"];
    const SERVER_ERROR = ["SERVER_ERROR", "SERVER ERROR"];
    const TWO_PASSWORDS_ARE_NOT_THE_SAME = ["TWO_PASSWORDS_ARE_NOT_THE_SAME", "Two passwords are not the same"];
    const TRANSACTION_PROCESSED = ["TRANSACTION_PROCESSED", "Transaction has been processed"];
    const NAME_ALREADY_EXISTS = ["NAME_ALREADY_EXISTS", "Name already exists"];
    const MAIL_RESET_NOT_FOUND = ["MAIL_RESET_NOT_FOUND", "Code does not exist"];
    const INVALID_TOKEN = ["INVALID_TOKEN", "Invalid Token"];
    const INVALID_EMAIL = ["INVALID_EMAIL", "Invalid email"];
    const INVALID_USERNAME = ["INVALID_USERNAME", "Invalid username"];
    const INVALID_VALUE = ["INVALID_VALUE", "Invalid value"];
    const INVALID_TYPE = ["INVALID_TYPE", "Invalid Type"];
    const INVALID_SECURITY_CODE = ["INVALID_SECURITY_CODE", "Invalid Security Code"];
    const INVALID_DEVICE = ["INVALID_DEVICE", "Invalid device"];
    const INVALID_AGENCY_CODE = ["INVALID_AGENCY_CODE", "Invalid agency code"];
    const INVALID_OLD_PASSWORD = ["INVALID_OLD_PASSWORD", "Invalid old password"];
    const INTERNAL_SERVER_ERROR = ['INTERNAL SERVER ERROR', '500 Internal Server Error'];
    const VOUCHER_DOES_NOT_EXIST = ["VOUCHER_DOES_NOT_EXIST", "Voucher does not exist"];
    const EXPIRED = ["EXPIRED", "Service expiration, please renew to use"];
    const WRONG_ACCOUNT_OR_PASSWORD = ["WRONG_ACCOUNT_OR_PASSWORD", "Wrong account or password"];
}
