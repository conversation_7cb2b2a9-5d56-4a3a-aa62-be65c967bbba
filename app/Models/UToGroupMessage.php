<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Base\BaseModel;
class UToGroupMessage extends Model
{
    use HasFactory;
    public $incrementing = false;
    protected $guarded = [];

    protected $appends = ['user',     'images',];

    protected $hidden = ['images_json'];
    public function getImagesAttribute()
    {
        return json_decode($this->images_json);
    }
    public function getUserAttribute()
    {

        $user = User::select('id', 'name', 'avatar_image')->where('id', $this->user_id)->first();
        return $user;
    }

   
}
