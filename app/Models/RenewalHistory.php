<?php

namespace App\Models;

use App\Models\Base\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>ezj\Searchable\SearchableTrait;

class RenewalHistory extends Model
{

  use SearchableTrait;
  use HasFactory;
  protected $guarded = [];



  protected $searchable = [
    'columns' => [
      'renewal_histories.service' => 1,
      'renewal_histories.references_value' => 2,
      'users.email' => 3,
      'users.name' => 4,
    ],
    'joins' => [
      'users' => ['renewal_histories.user_id', 'users.id'],
    ],
  ];

  protected $casts = [
    'paid_for_admin' => 'boolean',
    'before_expiration_date' => 'datetime',
    'after_expiration_date' => 'datetime'
  ];



  public function user()
  {
    return $this->belongsTo('App\Models\User');
  }
}
