<?php

namespace App\Models;

use App\Helper\PlanUtils;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Agency extends Model
{

    use HasFactory;
    protected $guarded = [];

    protected $appends = ['user', 'total_users', 'total_month', 'list_cookie_manager', 'need_pay', 'package'];
    protected $casts = [

        "hidden_price" => 'boolean',
        "enable_customer_add_device" => 'boolean',

        "allow_alldata" => 'boolean',
        "allow_autodata" => 'boolean',
        "allow_haynespro" => 'boolean',
        "allow_haynespro_truck" => 'boolean',
        "allow_identifix" => 'boolean',
        "allow_mitchell_repair_center" => 'boolean',
        "not_back_home_web_agency" => 'boolean',
        "allow_mitchell_prodemand" => 'boolean',
        "allow_renew" => 'boolean',
        "allow_tecdoc" => 'boolean',
        "allow_partslink24" => 'boolean',
        "allow_etka" => 'boolean',
        "allow_kdsgds" => 'boolean',
        "allow_ford_pts" => 'boolean',
    ];

    const allServices = [
        [
            "value" => PlanUtils::ALLDATA,
            "name" => "ALLData",
            "expiryName" => "ALLData expiry date",
            "expiryColumn" => "expiry_alldata",
            "lastVisitColumn" => "last_visit_time_alldata",
            "allowColumn" => "allow_alldata",
            "columnAgencyTotal" => "total_month_alldata",
            "columnAgencyUnpaid" => "total_month_alldata_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_alldata_unpaid_split",
        ],
        [
            "value" => PlanUtils::AUTODATA,
            "name" => "AutoData",
            "expiryName" => "AutoData expiry",
            "expiryColumn" => "expiry_autodata",
            "lastVisitColumn" => "last_visit_time_autodata",
            "allowColumn" => "allow_autodata",
            "columnAgencyTotal" => "total_month_autodata",
            "columnAgencyUnpaid" => "total_month_autodata_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_autodata_unpaid_split",
        ],
        [
            "value" => PlanUtils::HAYNESPRO,
            "name" => "HaynesPro",
            "expiryName" => "HaynesPro expiry",
            "expiryColumn" => "expiry_haynespro",
            "lastVisitColumn" => "last_visit_time_haynespro",
            "allowColumn" => "allow_haynespro",
            "columnAgencyTotal" => "total_month_haynespro",
            "columnAgencyUnpaid" => "total_month_haynespro_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_haynespro_unpaid_split",
        ],


        [
            "value" => PlanUtils::IDENTIFIX,
            "name" => "Identifix",
            "expiryName" => "Identifix expiry",
            "expiryColumn" => "expiry_identifix",
            "lastVisitColumn" => "last_visit_time_identifix",
            "allowColumn" => "allow_identifix",
            "columnAgencyTotal" => "total_month_identifix",
            "columnAgencyUnpaid" => "total_month_identifix_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_identifix_unpaid_split",
        ],
        [
            "value" => PlanUtils::MITCHELL_PRODEMAND,
            "name" => "Mitchell prodemand",
            "expiryName" => "Mitchell prodemand expiry",
            "expiryColumn" => "expiry_mitchell_prodemand",
            "lastVisitColumn" => "last_visit_time_mitchell_prodemand",
            "allowColumn" => "allow_mitchell_prodemand",
            "columnAgencyTotal" => "total_month_mitchell_prodemand",
            "columnAgencyUnpaid" => "total_month_mitchell_prodemand_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_mitchell_prodemand_unpaid_split",
        ],
        [
            "value" => PlanUtils::HAYNESPRO_TRUCK,
            "name" => "Haynes Truck",
            "expiryName" => "Haynes Truck expiry",
            "expiryColumn" => "expiry_haynespro_truck",
            "lastVisitColumn" => "last_visit_time_haynespro_truck",
            "allowColumn" => "allow_haynespro_truck",
            "columnAgencyTotal" => "total_month_haynespro_truck",
            "columnAgencyUnpaid" => "total_month_haynespro_truck_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_haynespro_truck_unpaid_split",
        ],
        [
            "value" => PlanUtils::PARTSLINK24,
            "name" => "Partslink24",
            "expiryName" => "Partslink24 expiry",
            "expiryColumn" => "expiry_partslink24",
            "lastVisitColumn" => "last_visit_time_partslink24",
            "allowColumn" => "allow_partslink24",
            "columnAgencyTotal" => "total_month_partslink24",
            "columnAgencyUnpaid" => "total_month_partslink24_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_partslink24_unpaid_split",
        ],
        [
            "value" => PlanUtils::KDSGDS,
            "name" => "KdsGds",
            "expiryName" => "KdsGds expiry",
            "expiryColumn" => "expiry_kdsgds",
            "lastVisitColumn" => "last_visit_time_kdsgds",
            "allowColumn" => "allow_kdsgds",
            "columnAgencyTotal" => "total_month_kdsgds",
            "columnAgencyUnpaid" => "total_month_kdsgds_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_kdsgds_unpaid_split",
        ],
        [
            "value" => PlanUtils::ETKA,
            "name" => "ETKA",
            "expiryName" => "ETKA expiry",
            "expiryColumn" => "expiry_etka",
            "lastVisitColumn" => "last_visit_time_etka",
            "allowColumn" => "allow_etka",
            "columnAgencyTotal" => "total_month_etka",
            "columnAgencyUnpaid" => "total_month_etka_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_etka_unpaid_split",
        ],
        [
            "value" => PlanUtils::TECDOC,
            "name" => "TecDoc",
            "expiryName" => "TecDoc expiry",
            "expiryColumn" => "expiry_tecdoc",
            "lastVisitColumn" => "last_visit_time_tecdoc",
            "allowColumn" => "allow_tecdoc",
            "columnAgencyTotal" => "total_month_tecdoc",
            "columnAgencyUnpaid" => "total_month_tecdoc_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_tecdoc_unpaid_split",
        ],
        [
            "value" => PlanUtils::FORD_PTS,
            "name" => "Ford PTS",
            "expiryName" => "Ford PTS expiry",
            "expiryColumn" => "expiry_ford_pts",
            "lastVisitColumn" => "last_visit_time_ford_pts",
            "allowColumn" => "allow_ford_pts",
            "columnAgencyTotal" => "total_month_ford_pts",
            "columnAgencyUnpaid" => "total_month_ford_pts_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_ford_pts_unpaid_split",
        ],
          [
            "value" => PlanUtils::TOYOTA_TIS,
            "name" => "Toyota TIS",
            "expiryName" => "Toyota TIS expiry",
            "expiryColumn" => "expiry_toyota_tis",
            "lastVisitColumn" => "last_visit_time_toyota_tis",
            "allowColumn" => "allow_toyota_tis",
            "columnAgencyTotal" => "total_month_toyota_tis",
            "columnAgencyUnpaid" => "total_month_toyota_tis_unpaid",
            "columnAgencyUnpaidSplit" => "total_month_toyota_tis_unpaid_split",
        ],
    ];

    public function getListCookieManagerAttribute()
    {
        return json_decode($this->json_list_cookie_manager ?? "[]");
    }


    public function getPackageAttribute()
    {
        $arr_pac = array();
        if ($this->price_package  == 0) return  $arr_pac;

        $pizza  = "ALLDATA,AUTODATA,HAYNESPRO";
        if ($this->package_list != null && strlen($this->package_list) > 0) {
            $pizza  = $this->package_list;
        }
        $pieces = explode(",", $pizza);

        if ($this->price_package  == 0) return  $arr_pac;
        foreach ($pieces  as $p) {
            foreach (Agency::allServices as $s) {
                if ($s['value'] == $p) {
                    array_push($arr_pac, $p);
                }
            }
        }



        return $arr_pac;
    }
    public function splitMonth($totalMonth, $this1,  &$total_price)
    {
        $total_year =  (int)($totalMonth / 12);
        $total_6_month =   (int)(($totalMonth % 12) / 6);
        $total_1_month = $totalMonth - ($total_6_month * 6) -   ($total_year * 12);

        $price_total_year  =  $total_year * ($this1->price_12_month);
        $price_total_6_month =  $total_6_month * ($this1->price_6_month);
        $price_total_1_month =  $total_1_month * ($this1->price_1_month);

        $total_after_split =  $price_total_year +   $price_total_6_month   +   $price_total_1_month;

        $total_price = $total_price + $total_after_split;
        return [
            "total_year"  => (int) $total_year,
            "total_6_month" => (int) $total_6_month,
            "total_1_month" => (int) $total_1_month,

            "price_total_year"  => (float) $price_total_year,
            "price_total_6_month" => (float)  $price_total_6_month,
            "price_total_1_month" =>  (float) $price_total_1_month,
            "total_after_split" => (float) $total_after_split,
        ];
    }

    public function getNeedPayAttribute()
    {
        if (request()->need_info_pay != "true") return null;

        $dataTotal = $this->getTotalMonthAttribute();
        $total_month_alldata_unpaid = $dataTotal['total_month_alldata_unpaid'] ?? 0;
        $total_month_autodata_unpaid = $dataTotal['total_month_autodata_unpaid'] ?? 0;
        $total_month_haynespro_unpaid = $dataTotal['total_month_haynespro_unpaid'] ?? 0;
        $total_month_identifix_unpaid = $dataTotal['total_month_identifix_unpaid'] ?? 0;
        $total_month_mitchell_prodemand_unpaid = $dataTotal['total_month_mitchell_prodemand_unpaid'] ?? 0;
        $total_month_haynespro_truck_unpaid = $dataTotal['total_month_haynespro_truck_unpaid'] ?? 0;
        $total_month_partslink24_unpaid = $dataTotal['total_month_partslink24_unpaid'] ?? 0;
        $total_month_kdsgds_unpaid = $dataTotal['total_month_kdsgds_unpaid'] ?? 0;
        $total_month_etka_unpaid = $dataTotal['total_month_etka_unpaid'] ?? 0;
        $total_month_tecdoc_unpaid = $dataTotal['total_month_tecdoc_unpaid'] ?? 0;
        $total_month_ford_pts_unpaid = $dataTotal['total_month_ford_pts_unpaid'] ?? 0;
        $total_month_toyota_tis_unpaid = $dataTotal['total_month_toyota_tis_unpaid'] ?? 0;

        $arrPackageObject = [];
        $packageOfAgency = $this->getPackageAttribute(); //[ ALLDATA AUTODATA HAYNESPRO ]
        foreach ($packageOfAgency  as $p) {
            foreach (Agency::allServices as $s) {
                if ($s['value'] == $p) {
                    array_push($arrPackageObject,  $s);
                }
            }
        }

        $needPayStr = "";
        $total_price_1 = 0;


        if ($this->price_package > 0) {
            $hasCombo = true;
            $namePackage = "";
            $min = 0;
            $arrMonthUnpaid = [];
            foreach ($arrPackageObject  as $p) {
                $namePackage =  $namePackage . $p['name'] . ",";
                $columnAgencyUnpaid = $p['columnAgencyUnpaid'];
                if ($$columnAgencyUnpaid < 12) {
                    $hasCombo =  false;
                }
                array_push($arrMonthUnpaid, $$columnAgencyUnpaid);
            }
            $total_year = 0;
            $min = min($arrMonthUnpaid);
            $namePackage  = rtrim($namePackage, ",");
            if ($hasCombo == true && $min >= 12) {
                $total_year =  (int)($min / 12);

                $total_price_1 = $this->price_package * $total_year;
                $needPayStr = "Package(" . $namePackage . ")x" . $total_year . "=" . ($total_price_1) . "$ | ";
            }

            foreach ($arrPackageObject  as $p) {
                $keyUnpaid = $p['columnAgencyUnpaid'];
                $$keyUnpaid  =   $$keyUnpaid -   $total_year * 12; //tìm giá trị theo colum vào set lại giá
            }
        }

        $total_price_2 = 0;

        $text_add_item =  "";
        foreach (Agency::allServices as $item) {
            $columnAgencyUnpaid = $item['columnAgencyUnpaid'];
            $name = $item['name'];

            $calcu = $this->splitMonth($$columnAgencyUnpaid, $this,   $total_price_2);

            if ($calcu['total_after_split'] == 0) continue;

            $text_add_item = $text_add_item . $name . ": ";

            $plitTextyear =
                $calcu['total_year'] == 0
                ? "" : $calcu['total_year'] . "year" . "(" . $calcu['price_total_year'] . "$)";
            $text_add_item  = $text_add_item  . $plitTextyear . " ";

            $plitText6month = ($calcu['total_6_month'] == 0 ? "" :
                $calcu['total_6_month'] . "x6month" . "(" . $calcu['price_total_6_month'] . "$)");

            $text_add_item =  $text_add_item . $plitText6month . " ";

            $plitText1month = ($calcu['total_1_month'] == 0
                ? ""
                : $calcu['total_1_month'] . "x1month" . "(" . $calcu['price_total_1_month'] . "$)");

            $text_add_item =   $text_add_item . $plitText1month . "| ";
        }

        $countAdd12MonthDevices = RenewalHistory::where('references_id', $this->user_id)->where('month', 12)->where('service', PlanUtils::MORE_DEVICE)->where('paid_for_admin', false)->count();
        $total_price_3 =   $countAdd12MonthDevices * $this->price_12_month_device;
        $strAddDevice = "";
        if($countAdd12MonthDevices > 0) {
             $strAddDevice = 'Add Device: '.$countAdd12MonthDevices.'year('.$total_price_3.'$) | ';
        }

        return [
            'need_pay' =>  $total_price_1 +  $total_price_2 +  $total_price_3,
            'str_pay' =>$strAddDevice . $needPayStr . $text_add_item . " =" . ($total_price_1 +  $total_price_2 +  $total_price_3) . "$"
        ];

        return $this->getTotalMonthAttribute();
    }


    public function getTotalMonthAttribute()
    {
        if (request()->need_info_pay != "true") return null;

        $timeCache = 3;
        if (str_contains(request()->path(), 'paid')) {
            $timeCache = 0;
        }

        return Cache::remember(json_encode(["agencyPay",  $this->id]), $timeCache, function () {
            $total_month_alldata = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::ALLDATA)->sum('month');
            $total_month_alldata_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::ALLDATA)->where('paid_for_admin', false)->sum('month');

            $total_month_autodata = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::AUTODATA)->sum('month');
            $total_month_autodata_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::AUTODATA)->where('paid_for_admin', false)->sum('month');

            $total_month_haynespro = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::HAYNESPRO)->sum('month');
            $total_month_haynespro_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::HAYNESPRO)->where('paid_for_admin', false)->sum('month');


            $total_month_identifix = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::IDENTIFIX)->sum('month');
            $total_month_identifix_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::IDENTIFIX)->where('paid_for_admin', false)->sum('month');

            $total_month_mitchell_repair_center = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::MITCHELL_REPAIR_CENTER)->sum('month');
            $total_month_mitchell_repair_center_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::MITCHELL_REPAIR_CENTER)->where('paid_for_admin', false)->sum('month');

            $total_month_mitchell_prodemand = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::MITCHELL_PRODEMAND)->sum('month');
            $total_month_mitchell_prodemand_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::MITCHELL_PRODEMAND)->where('paid_for_admin', false)->sum('month');

            $total_month_haynespro_truck = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::HAYNESPRO_TRUCK)->sum('month');
            $total_month_haynespro_truck_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::HAYNESPRO_TRUCK)->where('paid_for_admin', false)->sum('month');

            $total_month_partslink24 = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::PARTSLINK24)->sum('month');
            $total_month_partslink24_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::PARTSLINK24)->where('paid_for_admin', false)->sum('month');

            $total_month_kdsgds = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::KDSGDS)->sum('month');
            $total_month_kdsgds_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::KDSGDS)->where('paid_for_admin', false)->sum('month');

            $total_month_etka = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::ETKA)->sum('month');
            $total_month_etka_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::ETKA)->where('paid_for_admin', false)->sum('month');

            $total_month_tecdoc = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::TECDOC)->sum('month');
            $total_month_tecdoc_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::TECDOC)->where('paid_for_admin', false)->sum('month');

            $total_month_ford_pts = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::FORD_PTS)->sum('month');
            $total_month_ford_pts_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::FORD_PTS)->where('paid_for_admin', false)->sum('month');

            $total_month_toyota_tis = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::TOYOTA_TIS)->sum('month');
            $total_month_toyota_tis_unpaid = RenewalHistory::where('references_id', $this->user_id)->where('service', PlanUtils::TOYOTA_TIS)->where('paid_for_admin', false)->sum('month');

            $total_price = 0;

            return [
                'total_month_alldata' =>  $total_month_alldata,
                'total_month_alldata_unpaid' =>  $total_month_alldata_unpaid,
                "total_month_alldata_unpaid_split" => $this->splitMonth($total_month_alldata_unpaid, $this, $total_price),

                'total_month_autodata' => $total_month_autodata,
                'total_month_autodata_unpaid' =>  $total_month_autodata_unpaid,
                "total_month_autodata_unpaid_split" => $this->splitMonth($total_month_autodata_unpaid, $this,  $total_price),

                'total_month_haynespro' =>   $total_month_haynespro,
                'total_month_haynespro_unpaid' =>   $total_month_haynespro_unpaid,
                "total_month_haynespro_unpaid_split" =>  $this->splitMonth($total_month_haynespro_unpaid, $this,  $total_price),

                'total_month_identifix' =>  $total_month_identifix,
                'total_month_identifix_unpaid' => $total_month_identifix_unpaid,
                "total_month_identifix_unpaid_split" =>  $this->splitMonth($total_month_identifix_unpaid, $this, $total_price),

                'total_month_mitchell_repair_center' =>  $total_month_mitchell_repair_center,
                'total_month_mitchell_repair_center_unpaid' =>  $total_month_mitchell_repair_center_unpaid,
                "total_month_mitchell_repair_center_unpaid_split" => $this->splitMonth($total_month_mitchell_repair_center_unpaid, $this,  $total_price),

                'total_month_mitchell_prodemand' =>  $total_month_mitchell_prodemand,
                'total_month_mitchell_prodemand_unpaid' =>  $total_month_mitchell_prodemand_unpaid,
                "total_month_mitchell_prodemand_unpaid_split" => $this->splitMonth($total_month_mitchell_prodemand_unpaid, $this,  $total_price),

                'total_month_haynespro_truck' =>  $total_month_haynespro_truck,
                'total_month_haynespro_truck_unpaid' => $total_month_haynespro_truck_unpaid,
                "total_month_haynespro_truck_unpaid_split" =>  $this->splitMonth($total_month_haynespro_truck_unpaid, $this,  $total_price),

                'total_month_partslink24' =>   $total_month_partslink24,
                'total_month_partslink24_unpaid' =>   $total_month_partslink24_unpaid,
                "total_month_partslink24_unpaid_split" => $this->splitMonth($total_month_partslink24_unpaid, $this,  $total_price),


                'total_month_kdsgds' =>  $total_month_kdsgds,
                'total_month_kdsgds_unpaid' => $total_month_kdsgds_unpaid,
                "total_month_kdsgds_unpaid_split" =>  $this->splitMonth($total_month_kdsgds_unpaid, $this,  $total_price),

                'total_month_etka' =>  $total_month_etka,
                'total_month_etka_unpaid' => $total_month_etka_unpaid,
                "total_month_etka_unpaid_split" =>  $this->splitMonth($total_month_etka_unpaid, $this,  $total_price),

                'total_month_tecdoc' =>   $total_month_tecdoc,
                'total_month_tecdoc_unpaid' =>   $total_month_tecdoc_unpaid,
                "total_month_tecdoc_unpaid_split" => $this->splitMonth($total_month_tecdoc_unpaid, $this,  $total_price),

                'total_month_ford_pts' =>   $total_month_ford_pts,
                'total_month_ford_pts_unpaid' =>   $total_month_ford_pts_unpaid,
                "total_month_ford_pts_unpaid_split" => $this->splitMonth($total_month_ford_pts_unpaid, $this,  $total_price),

                'total_month_toyota_tis' =>   $total_month_toyota_tis,
                'total_month_toyota_tis_unpaid' =>   $total_month_toyota_tis_unpaid,
                "total_month_toyota_tis_unpaid_split" => $this->splitMonth($total_month_toyota_tis_unpaid, $this,  $total_price),
                
                'total_price' =>  $total_price
            ];
        });
    }

    public function getUserAttribute()
    {

        $user = User::where('id', $this->user_id)->first();
        return $user;
    }


    public function getTotalUsersAttribute()
    {
        if (request()->need_info_pay != "true") return null;

        $count = User::where('of_agency_id', $this->id)->count();
        return $count;
    }
}
