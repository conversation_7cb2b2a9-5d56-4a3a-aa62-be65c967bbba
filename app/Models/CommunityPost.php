<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use AjCastro\Searchable\Searchable;

class CommunityPost extends Model
{
    use Searchable;
    use HasFactory;

    protected $guarded = [];

    protected $searchable = [
        'columns' => [
            'name',
            'content',
        ],
    ];

    protected $casts = [
        'is_pin' => 'boolean'
    ];
    protected $hidden = ['images_json'];
    protected $appends = [
        'is_like',
        'total_like',
        'total_comment',
        'staff',
        'user',
        'images',
        'last_comments'
    ];

    public function getImagesAttribute()
    {
        return json_decode($this->images_json);
    }

    public function getUserAttribute()
    {
        return User::select('id', 'name', 'avatar_image')->where('id', $this->user_id)->first();
    }
    public function getStaffAttribute()
    {
        return Staff::select('id', 'name')->where('id', $this->staff_id)->first();
    }

    public function getTotalLikeAttribute()
    {
        return  CommunityLike::where(
            'community_post_id',
            $this->id
        )
            ->count();
    }

    public function getTotalCommentAttribute()
    {
        return  CommunityComment::where(
            'community_post_id',
            $this->id
        )
            ->count();
    }

    public function getLastCommentsAttribute()
    {
        return  CommunityComment::where(
            'community_post_id',
            $this->id
        )->orderBy('id', 'desc')
            ->take(5)
            ->get();
    }

    public function getIsLikeAttribute()
    {
        $request = request();
        $user = request('user', $default = null);


        if ($user   != null) {

            $fv = CommunityLike::where(
                'user_id',
                $user->id
            )->where(
                'community_post_id',
                $this->id
            )
                ->first();
            if ($fv != null) return true;
        }

        return false;
    }
}
