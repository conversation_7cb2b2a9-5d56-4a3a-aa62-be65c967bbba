<?php

namespace App\Models;

use App\Models\Base\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;



class Admin extends BaseModel
{
    const ADMIN_LEVEL_BOSS = 10;
    const ADMIN_LEVEL_MAINTENANCE = 0;
    const ADMIN_LEVEL_SUPPORT_CONTENT = 1;

    use HasFactory;


    protected $guarded = [];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
    ];
}
