<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Base\BaseModel;
use <PERSON>lopezj\Searchable\SearchableTrait;

class Staff extends Model
{
    use HasFactory;
    use SearchableTrait;

    protected $guarded = [];

    protected $searchable = [
        'columns' => [
            'staff.name' => 1,
            'staff.phone_number' => 2,
            'staff.facebook_id' => 3,
        ],
    ];

    protected $appends = ['total_referral'];

    public function getTotalReferralAttribute()
    {

        return 0;
    }
}
