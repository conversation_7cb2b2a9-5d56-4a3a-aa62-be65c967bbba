<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GroupBanMember extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $appends = ['user'];

    public function getUserAttribute()
    {

        $user = User::select('id', 'name', 'avatar_image')->where('id', $this->user_id)->get()->first();
        return $user;
    }
}
