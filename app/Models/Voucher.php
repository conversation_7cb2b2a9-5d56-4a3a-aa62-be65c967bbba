<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Voucher extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $hidden = ['json_voucher_items'];

    protected $appends = [
        'voucher_items',
    ];

    protected $casts = [
        'on_start' => 'boolean',
    ];

    public function getVoucherItemsAttribute()
    {
        return json_decode($this->json_voucher_items);
    }
}
