<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use AjCastro\Searchable\Searchable;

class CommunityComment extends Model
{
    use HasFactory;
    use Searchable;
    protected $guarded = [];

    protected $searchable = [
        'columns' => [
            'content',
        ],
    ];

    protected $appends = [
        'user',
        'images',
        'last_childs_comment1',
        'total_comment_child1'
    ];

    public function getImagesAttribute()
    {
        return json_decode($this->images_json);
    }
    
    public function getUserAttribute()
    {
        return User::select('id','name','avatar_image')->where('id', $this->user_id)->first();
    }

    public function getLastChildsComment1Attribute()
    {
        return  CommunityCommentChild1::where(
            'community_comment_id',
            $this->id
        )->orderBy('id', 'desc')
            ->take(1)
            ->get();
    }

    public function getTotalCommentChild1Attribute()
    {
        return  CommunityCommentChild1::where(
            'community_comment_id',
            $this->id
        )
            ->count();
    }
}
