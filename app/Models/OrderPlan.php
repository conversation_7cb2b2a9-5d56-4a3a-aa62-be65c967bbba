<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderPlan extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $appends = [
        'plans',
        'plan_ids'
    ];

    public function getPlanIdsAttribute()
    {
        return  json_decode($this->plan_ids_json) ?? array();
    }

    public function getPlansAttribute()
    {
        return  json_decode($this->plans_json) ?? array();
    }
    
}
