<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Base\BaseModel;
class GroupChat extends BaseModel
{
    use HasFactory;
    protected $guarded = [];
 
    protected $casts = [
        'seen' => 'boolean',
    ];


    protected $appends = ['group','from_user'];


    public function getFromUserAttribute()
    {

        $user = User::select('id', 'name','avatar_image')->where('id', $this->from_user_id)->first();
        return $user;
    }

    public function getGroupAttribute()
    {

        $user = Group::select('id', 'name','image_url')->where('id', $this->to_group_id)->first();
        return $user;
    }

}


