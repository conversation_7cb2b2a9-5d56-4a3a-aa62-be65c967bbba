<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CommunityCommentChild1 extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $searchable = [
        'columns' => [
            'content',
        ],
    ];

    protected $appends = [
        'user',
        'images',

    ];

    public function getImagesAttribute()
    {
        return json_decode($this->images_json);
    }
    
    public function getUserAttribute()
    {
        return User::select('id','name','avatar_image')->where('id', $this->user_id)->first();
    }


}
