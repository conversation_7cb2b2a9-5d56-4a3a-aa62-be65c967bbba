<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use App\Models\Base\BaseModel;
use <PERSON>lopezj\Searchable\SearchableTrait;

class HistoryRecharge extends BaseModel
{
    use HasFactory;
    use SearchableTrait;

    protected $guarded = [];

    protected $searchable = [
        'columns' => [
            'history_recharges.code' => 1,
            'users.cmnd' => 2,
            'users.name' => 3,
            'users.phone_number' => 3,
        ],
        'joins' => [
            'users' => ['history_recharges.user_id', 'users.id'],
        ],
    ];
}
