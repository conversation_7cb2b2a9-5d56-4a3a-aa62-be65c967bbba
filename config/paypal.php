<?php

/**
 * PayPal Setting & API Credentials
 * Created by <PERSON><PERSON> <<EMAIL>>.
 */

// return [
//     'mode'    => env('PAYPAL_MODE', 'live'), // Can only be 'sandbox' Or 'live'. If empty or invalid, 'live' will be used.
//     'sandbox' => [
//         'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'Aa5ija42JHgPqU9tqErhmr6ebJXtI29UKAXSOIla3ml0tqXgWJpFPSUt7D6jziPrLxxjjaMIRHdvtiUI'),
//         'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EKkwbGaGBg3xjTMB4pVmd_GxJ_x-4R3_52tT2-DmykzZRLdQNGbeVsZvkI9UTuvh-6SCxvwtTKPwPcQP'),
//         'app_id'            => 'APP-80W284485P519543T',
//     ],
//     'live' => [
//         'client_id'         => env('PAYPAL_LIVE_CLIENT_ID', 'AUGJZpo-8VnKGo2R4KjnkU-YndvTLcC8JY5kQFZFOeiHkssy_cK9oVKrcMF8SeqyjRE9NsiLV068v-0I'),
//         'client_secret'     => env('PAYPAL_LIVE_CLIENT_SECRET', 'EOOMP2jxx_fZmSSKBVmVAiqIRDIs-gK07NIMNVsKcsUDf9zLSaOmTc_ddag0CEqVjctsBaRsQFfth0jh'),
//         'app_id'            => env('PAYPAL_LIVE_APP_ID', 'DrCar Live'),
//     ],

//     'payment_action' => env('PAYPAL_PAYMENT_ACTION', 'Sale'), // Can only be 'Sale', 'Authorization' or 'Order'
//     'currency'       => env('PAYPAL_CURRENCY', 'USD'),
//     'notify_url'     => env('PAYPAL_NOTIFY_URL', ''), // Change this accordingly for your application.
//     'locale'         => env('PAYPAL_LOCALE', 'en_US'), // force gateway language  i.e. it_IT, es_ES, en_US ... (for express checkout only)
//     'validate_ssl'   => env('PAYPAL_VALIDATE_SSL', true), // Validate SSL when creating api client.
// ];

return [
    'mode'    => env('PAYPAL_MODE', 'sandbox'), // Can only be 'sandbox' Or 'live'. If empty or invalid, 'live' will be used.
    'sandbox' => [
        'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AdeukURjlNIf_4fzmMoODyx3u5fZpXwS07IgZ_psm0FccgbfGJYKfCdWkE1QD10jwqgdzEKu20mb22-N'),
        'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EI9WUXqOjNaV1JOcki7o77qzsa7Xp2llitFox88UbKHb0zMX2vsoa5rSRUHCB46d_yIJdu_JLXhOp7oL'),
        'app_id'            => 'DrCar2',
    ],

    // 'live' => [ //Trần xuân sơn 1
    //     'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AbgjTSOaPeDal2IGJme-I-XeMmqiMbey6nETI3TEUthQuAFp8isB6StnHoXl2_hTHtRshX8Gk9o8e0Qc'),
    //     'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EKLTWmz5wNZkQvhaQOR_WSlNwfF5dQH5OtlW4lWWBwczTeniGlABY8usAgX6V4CmtdsH8Qvnhy_6PYWB'),
    //     'app_id'            => 'Saha',
    // ],
    // 'live' => [ //Muffins	Main 2
    //     'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'Afh7QFyN4Qcunnd4EjhV3s6M_fswNs02iaurTBYcyLlhbRjKWGBrucokTr4NwyJFnFSpzvtohfbtEVS_'),
    //     'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EMjcWzIgxdiGdho8nktJukRN9uqQEwV_UQUFGWBReSAzyTXP-edICseOr1MMbBb-Gmu0iPlXoDsrh7QS'),
    //     'app_id'            => 'MainCar',
    // ],
    // 'live' => [ //Đức Hoài Ngô 3
    //     'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AY5xMjMAfO8HVYUl7IAK8AStO1i8TJNgor0I6KGnZkhkZrAPQBZMgoEhMeQXllqn2QmEWWreN5BeFquQ'),
    //     'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EIzpa15CF_sWu-vG2q2HmrSSL-vz3sSb2Hz-ZbDCvLsP0dwmeQR8b9NgCn4qwChYvk7hWJC6LNSO8Y5h'),
    //     'app_id'            => 'KingCar',
    // ],
    'live' => [ //Thu anh 4
        'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AYG1Pcn7XOSFv5OCObhEOjhG2C8yT8lnP0yi_w7qyFCRdp6ha50DQS9suqqbLjaXbqIY6AfOj-UbmwdU'),
        'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'ELW8B_eCueJx2yOjIj_DU9D97jR4ZFqx1_QAUiYsMPmBPWeKe4WCvE-FQwgDQs5y5OtYji27gcSoy3M6'),
        'app_id'            => 'ThuAnh',
    ],
    // 'live' => [ //TIÊU MỸ LỆ 5
    //     'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AW0YezLAFn086CEQBdBEYlSzS87KGvQykwmXPjCUWrOpdrquGItzbnHGZmeE31p3M7ynAVTWBZDwTx-4'),
    //     'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EKZvwV4cwr2z9oRHh_b90m8niUov94BdMLGcmLQXubRgprX5nTDaYhz-ycTXyRUqrONieAWI0jmXKZdK'),
    //     'app_id'            => 'LeTieu',
    // ],
    // 'live' => [ //Thuy anh 6
    //     'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AV8wD4uZYDB_f9DnfgsqazorjCRVahgmyq6ALbjhKAfQJfiXMasX2VluH_5tXNUyZmWV-Jv2H9rm4Hah'),
    //     'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EKLqUoVgEkE2lNLMcYnxcead_0YT4DxK76ISsOAMuCpw69BUUE6xGgp_u-_k3OKSq8mCH7SkOGDdfy1k'),
    //     'app_id'            => 'ThuyAnh6',
    // ],
    // 'live' => [ //Tran van tuan 7
    //     'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AUGLHqlammXkPSj17P-nZAzVuccjCHiTAfyqff-mGC7rSXpVjm5LPOyk_k65vxHBQvkBGkQVsC1ABFUZ'),
    //     'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EHZN_qDtf_kavR8JavpuXQUDJzEB294K023sevxNjZOxfSgqFM5lSkKC1IFsK-uFRzPcRRo2_u90l4Hq'),
    //     'app_id'            => 'VanTuan',
    // ],
    // 'live' => [ //Văn No 8
    //     'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'ARBE3zMNV5X_eXcsYrDFPCJ7H-hUhWDNVjdkjC4t60BWDp4YP7i6Q_eszuD_-LKLkxsAvYA2MIP0pKmJ'),
    //     'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EKcDFzOwCjcQ8iv0Myiix3pF3m1e0yf160z7mgfHNpC2jHDtjwAFL2fQCWP-neNwBoGHV65kYPZBuQmd'),
    //     'app_id'            => 'VanNo',
    // ],
    // 'live' => [ //Văn Tự Bùi 9
    //     'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'AXVbQdlGErWN2wu4b_zkB-NK_r7BPcOSVjiygeYdDMe98LrswW5vWo2Ygz0QwjNy_IzhO-HIHJOz0i0l'),
    //     'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EEOHKufvilW3ZzhkxhAZ_PVZahmvZaQAzbNf1idvg7FhahgmYBaeDFs26Tt1PQ5LIVcFyCaBjxsvkOmA'),
    //     'app_id'            => 'VanBui',
    // ],

    ///////RS THIÊN
    // 'live' => [ //Thiên 1
    //     'client_id'         => env('PAYPAL_SANDBOX_CLIENT_ID', 'Ac8Swy3IcLGAAaJj8MvuxqY1vGm41WiIqTjqrVQ8--oundyjQgz8NChQat6M1skPGatWOJTTqSuAaFe1'),
    //     'client_secret'     => env('PAYPAL_SANDBOX_CLIENT_SECRET', 'EJKPO75T8HbuxrCnOZs20E8Wox1F1Sy2_6JnBUaA3bbMUWGbXlBXgBxJxJRt6VJaZljQmVW8OTSxNcgi'),
    //     'app_id'            => 'Thien1',
    // ],

    'payment_action' => env('PAYPAL_PAYMENT_ACTION', 'Sale'), // Can only be 'Sale', 'Authorization' or 'Order'
    'currency'       => env('PAYPAL_CURRENCY', 'USD'),
    'notify_url'     => env('PAYPAL_NOTIFY_URL', ''), // Change this accordingly for your application.
    'locale'         => env('PAYPAL_LOCALE', 'en_US'), // force gateway language  i.e. it_IT, es_ES, en_US ... (for express checkout only)
    'validate_ssl'   => env('PAYPAL_VALIDATE_SSL', true), // Validate SSL when creating api client.
];
