/*
 * V2 version.d
 */
// JavaScript should be made compatible with libraries other than jQuery by
// wrapping it with an "anonymous closure". See:
// - http://drupal.org/node/1446420
// - http://www.adequatelygood.com/2010/3/JavaScript-Module-Pattern-In-Depth
(function ($, Drupal, window, document, undefined) {
    // Place your code here.

    jQuery(document).ready(function () {
        // Engine code Page
        jQuery("a.engine-code-link").live("click", function (e) {
            e.preventDefault();

            var self = jQuery(this);
            var manufacturer = self.attr("manufacturer");
            var body = self.attr("body");
            var fuel = self.attr("fuel");
            var litres = self.attr("litres");
            var freetext = self.attr("freetext");
            var vehicletype = self.attr("vehicletype");
            var module = self.attr("module");
            var data = {};

            data["manufacturer"] = manufacturer;
            data["body"] = body;
            data["module"] = module;
            data["litres"] = litres;
            data["fuel"] = fuel;
            data["freetext"] = freetext;
            data["vehicletype"] = vehicletype;

            jQuery.ajax({
                type: "POST",
                url: getRoute("engine-selection-list", manufacturer, body),
                data: data,
                beforeSend: function () {
                    var loaderHeight = jQuery(".left-column").height();
                    jQuery(".ajax_loader")
                        .addClass("loaderShow")
                        .height(loaderHeight)
                        .show();
                },
                success: function (data) {
                    self.parent().siblings().removeClass("selected");
                    jQuery("#engine-model-list")
                        .find("li.selected")
                        .removeClass("selected");
                    self.parent().addClass("selected");
                    data = JSON.parse(data);
                    jQuery("#engine-code-filtered").html(data);
                    jQuery(
                        "#vehicleSelection_expertview .matchHeight"
                    ).equalHeight();
                    jQuery(".ajax_loader").removeClass("loaderShow").hide();
                },
            });

            e.stopPropagation();
        });

        jQuery(".ecRight tr.enabled *").click(function () {
            jQuery("input#engine-code-submit").click();
        });
    });
})(jQuery, Drupal, this, this.document);

// START CLICK: ENGINE CODE SHOW / HIDE KEY
function keyToggle(clickID) {
    jQuery(clickID).toggleClass("hidden");
}

jQuery(".hideKey").live("click", function (e) {
    keyToggle(".engineCodeKey");
    var cont = jQuery(this).text();
    if (cont === "Show key") {
        cont = "Hide key";
        jQuery(this).text(cont);
    } else {
        cont = "Show key";
        jQuery(this).text(cont);
    }
});
function addEngineCodeValues(obj) {
    console.log(obj);

    jQuery("table#engine-code-filtered tr").removeClass("active");
    jQuery(obj).addClass("active");

    var vid = jQuery(obj).attr("data-vechicle-nid");
    var enid = jQuery(obj).attr("engine-code-nid");
    var enname = jQuery(obj).attr("engine-code-nid");

    if (vid == "undefined" || enid == "undefined") {
        alert("Invalid Selection");
        return false;
    }

    jQuery("form#engine-code-selection").find("input#vehicle_id").val(vid);
    jQuery("form#engine-code-selection").find("input#engine_id").val(enid);
    jQuery("form#engine-code-selection").find("input#engine_name").val(enname);

    return;
}

function engineSubmit() {
    var mid = jQuery("form#engine-code-selection > #vehicle_id").val();
    jQuery(".left-column > .ajax_loader").show();
    jQuery.ajax({
        url: getRoute("engine-selection-engine-code", mid),
        type: "POST",
        dataType: "json",
        data: jQuery("form#engine-code-selection").serialize(),
        success: function (data) {
            jQuery("#loaderDiv").removeClass("loaderShow");
            if (data["action"] == 1) {
                window.location.href = data["back"];
            } else {
                alert(data["message"]);
                jQuery(".left-column > .ajax_loader").hide();
                return;
            }
        },
    });
    return false;
}

// END CLICK: ENGINE CODE SHOW / HIDE KEY
//Engine page height box
function resizeSelectToWindowHeight() {
    jQuery(".ecLeft")
        .find(".scroll-wrapper")
        .height(
            jQuery(window).height() *
            (Drupal.settings.ad_gms_facelift_sep16 ? 0.5 : 0.6)
        );
    if (jQuery(this).width() < 630) {
        jQuery(".ecLeft")
            .find(".scroll-wrapper")
            .height(
                jQuery(window).height() *
                (Drupal.settings.ad_gms_facelift_sep16 ? 0.25 : 0.6)
            );
    }
    jQuery(".ecRight")
        .find(".scroll-wrapper")
        .height(
            jQuery(window).height() *
            (Drupal.settings.ad_gms_facelift_sep16 ? 0.5 : 0.6)
        );

    if (jQuery(this).width() < 630) {
        jQuery(".ecRight")
            .find(".scroll-wrapper")
            .height(
                jQuery(window).height() *
                (Drupal.settings.ad_gms_facelift_sep16 ? 0.25 : 0.6)
            );
    }
}

jQuery(window).resize(function () {
    resizeSelectToWindowHeight();
});
//End engine page height box

//Bonus code
jQuery(document).ready(function () {
    document.querySelector("#engine-model-list\\  > li.selected > a")?.click();

    setTimeout(function () {
        console.log(Drupal.settings.routes);
    }, 1000);
});

function engineSubmit() {
    const urlParams = new URLSearchParams(window.location.search);
    const routeName = urlParams.get("route_name");

    const bodyParamsString = jQuery("form#engine-code-selection").serialize();
    const bodyParams = new URLSearchParams(bodyParamsString);
    const vehicleId = bodyParams.get("vehicle_id");

    const module = urlParams.get("module");

    if (routeName && vehicleId && Drupal.settings.routes[routeName]) {
        const routes = {
            "air-conditioning": "w1/air-conditioning/%/%",
            airbags: "w1/airbags/%/%",
            "anti-lock-brake-systems": "w1/anti-lock-brake-systems/%/%",
            "assist-me": "w1/assist-me/%/%/%/%",
            "assist-me-component-checks": "w1/assist-me-component-checks/%/%",
            "assist-me-pin-data": "w1/assist-me-pin-data/%/%",
            "auxiliary-drive-belts": "w1/auxiliary-drive-belts/%/%",
            "battery-disconnection-and-reconnection":
                "w1/battery-disconnection-and-reconnection/%/%",
            bulbs: "w1/bulbs/%",
            bulletin: "w1/known-fixes/%/%",
            "camshaft-drive-system": "w2/camshaft-drive-system/%",
            "camshaft-drive-system-general-guide":
                "w1/general-guide/timing-belt",
            "camshaft-drive-system-important":
                "w1/camshaft-drive-system/important",
            "camshaft-drive-system-variants-w2": "w2/camshaft-drive-system/%",
            "camshaft-drive-system-w2": "w2/camshaft-drive-system/%",
            "category-page-manufacturer-model": "w1/vehicles/%/%",
            "category-page-mid": "w1/vehicles/%",
            clutches: "w1/clutches/%/%",
            "component-page": "w1/components/%/%",
            "control-module-pin-data": "w1/control-module-pin-data/%",
            "diagnostic-trouble-codes": "w2/diagnostic-trouble-codes/%",
            "diagnostic-trouble-codes-general-information":
                "w1/diagnostic-trouble-codes/general-information/%/%",
            "diagnostic-trouble-codes-known-fixes":
                "w1/diagnostic-trouble-codes/known-fixes/%/%/%",
            "diagnostic-trouble-codes-search":
                "w1/diagnostic-trouble-codes/search/%/%",
            "diagnostic-trouble-codes-search-result":
                "w1/diagnostic-trouble-codes/search/result/%/%/%",
            "diagnostic-trouble-codes-variants-w2":
                "w2/diagnostic-trouble-codes/%",
            "diagnostic-trouble-codes-w2": "w2/diagnostic-trouble-codes/%",
            "diesel-exhaust-gas-aftertreatment":
                "w1/diesel-exhaust-gas-aftertreatment/%/%",
            "dtc-general-guide": "w1/general-guide/diagnostic-trouble-codes",
            "electric-parking-brake": "w1/electric-parking-brake/%/%",
            "electrical-component-locations":
                "w1/electrical-component-locations/%",
            "electrical-component-locations-image":
                "w1/electrical-component-locations/image/%/%/%",
            emissions: "w1/emissions/%/%",
            "emissions-variants": "w1/variants/emissions/%",
            "engine-management": "w1/engine-management/%/%",
            "engine-management-accordion": "w1/engine-management/%/accordion",
            "engine-oil": "w2/engine-oil/%",
            "engine-oil-note": "w1/engine-oil/%/note/%",
            "engine-oil-w2": "w2/engine-oil/%",
            "engine-selection": "w1/manufacturers/%/%/engines",
            "engine-selection-engine-code": "w1/vehicle-selection/mid/%",
            "engine-selection-list": "w1/manufacturers/%/%/engines/codes",
            "estimate-calculator": "w1/estimate/%/%",
            "estimate-calculator-ajax": "w1/estimate/%/ajax",
            "estimate-calculator-engine-selection":
                "w1/manufacturers/estimate/%/%/engines",
            "estimate-calculator-manufacturer-model":
                "w1/estimate-manufacturer-model/%/%",
            "estimate-calculator-model-selection":
                "w1/model-selection/estimate",
            "estimate-calculator-save-variant": "w1/estimate-save-variant/%/%",
            "estimate-calculator-variants": "w1/variants/estimate/%",
            "fuses-and-relays": "w1/fuses-and-relays/%",
            "garage-account-management": "w1/manage-account",
            "jacking-mode": "w1/jacking-mode/%/%",
            "key-programming": "w2/key-programming/%/%",
            "key-programming-variants-w2": "w2/key-programming/%",
            "known-fixes": "w1/known-fixes/%",
            "model-selection": "w2/model-selection",
            "model-selection-manufacturers": "w1/model-selection/manufacturers",
            "model-selection-models": "w1/model-selection/manufacturers/%",
            "model-selection-w2": "w2/model-selection",
            "mtc-estimate": "motorcycle/estimate/%/%",
            "mtc-estimate-variants": "motorcycle/estimate/%",
            "mtc-expertview": "motorcycle/expertview",
            mtc_diagnostic_trouble_codes: "motorcycle/dtc/%",
            mtc_exhaust_emissions: "motorcycle/emissions/%",
            mtc_lubricants_and_capacities: "motorcycle/lubricants/%",
            mtc_repair_times: "motorcycle/rt/%",
            mtc_service_schedules: "motorcycle/service-schedules/%",
            mtc_technical_specifications: "motorcycle/td/%",
            mtc_tightening_torques: "motorcycle/tightening-torques/%",
            mtc_tyre_pressures: "motorcycle/tyre-pressure/%",
            "nordic-vehicle-inspection": "w1/nordic-vehicle-inspection/%",
            "nordic-vehicle-inspection-variant":
                "w1/nordic-vehicle-inspection/%/%/%",
            "repair-times": "w1/repair-times/%/%",
            "repair-times-variants": "w1/vehicles/variants/repair-times/%",
            "service-ac": "w1/service-ac/%",
            "service-advisor": "w2/service-advisor/%",
            "service-advisor-list": "w1/service-advisor/%/%",
            "service-brakes": "w1/service-brakes/%",
            "service-illustrations": "w1/service-illustrations/%",
            "service-indicator": "w2/service-indicator/%",
            "service-indicator-variants-w2": "w2/service-indicator/%",
            "service-indicator-w2": "w2/service-indicator/%",
            "service-schedules": "w1/service-schedules/%",
            "service-schedules-intervals": "w1/service-schedules-intervals/%/%",
            "service-schedules-motorcycles": "motorcycle/service-schedules/%",
            "service-schedules-motorcycles-additional-services":
                "motorcycle/service-schedules/%/%/additional-services",
            "service-schedules-motorcycles-intervals":
                "motorcycle/service-schedules/%/%",
            "service-schedules-motorcycles-operations":
                "motorcycle/service-schedules/%/%/%",
            "service-schedules-motorcycles-variants":
                "motorcycle/service-schedules/%/variants",
            "service-schedules-technical-data":
                "w1/service-schedules-technical-data/%/%",
            "service-schedules-update-job-folder":
                "w1/service-schedules-update-job-folder/%",
            "service-schedules-update-state":
                "w1/service-schedules-update-state/%/%",
            "service-summary": "w1/service-summary/%/%",
            "service-summary-variants": "w1/variants/service-summary/%",
            "service-transmission": "w1/service-transmission/%/%",
            "service-transmission-variants":
                "w1/variants/service-transmission/%",
            "smart-estimator": "w2/",
            "technical-specifications": "w1/technical-specifications/%",
            "tyre-pressure-monitoring-system":
                "w2/tyre-pressure-monitoring-system/%",
            "tyre-pressure-monitoring-system-variants-w2":
                "w2/tyre-pressure-monitoring-system/%",
            "tyre-pressure-monitoring-system-w2":
                "w2/tyre-pressure-monitoring-system/%",
            "tyre-pressures": "w1/tyre-pressures/%/%",
            "tyre-pressures-variants": "w1/vehicles/variants/tyre-pressures/%/%",
            tyres: "w2/tyres/%",
            "tyres-w2": "w2/tyres/%",
            "vehicle-variants": "w1/vehicles/variants/%/%",
            "vin-plate-location": "w1/vin-plate-location/%",
            vrm: "w1/vehicles/search/%/%",
            "warning-lamps-and-symbols": "w1/warning-lamps-and-symbols/%",
            "warning-messages": "w1/warning-messages/%",
            "wheel-alignment": "w2/wheel-alignment/%",
            "wheel-alignment-general-guide": "w1/general-guide/wheel-alignment",
            "wheel-alignment-variants": "w2/wheel-alignment/%",
            "wheel-alignment-variants-w2": "w2/wheel-alignment/%",
            "wheel-alignment-w2": "w2/wheel-alignment/%",
            "wiring-diagram": "w1/diagram/%/%/%",
            "wiring-diagrams": "w1/wiring-diagrams/%",
            "wiring-diagrams-component-accordion":
                "w1/wiring-diagrams/component-accordion/%/%",
            "wiring-diagrams-module-accordion":
                "w1/wiring-diagrams/module-accordion/%/%",
        };

        const noRedirects = ["anti-lock-brake-systems", "air-conditioning"];

        if (noRedirects.includes(routeName)) {
            const link = `/w1/vehicles/variants/${routeName}/${vehicleId}?route_name=${routeName}&module=${module}`;
            window.location.href = link;
        } else {
            var link = "/" + routes[routeName].replace("%", vehicleId);
            link = link.replace("%", "0");
            window.location.href = link;
        }
    }
}
