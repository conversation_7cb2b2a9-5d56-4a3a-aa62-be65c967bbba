:root {
    --line-height: 1.425
}

html {
    font-family: sans-serif;
    line-height: var(--line-height);
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    font-family: Arial, Helvetica, sans-serif;
    font-style: normal;
    font-size: 21px;
    color: #1f2642;
    line-height: var(--line-height);
    margin: 0
}

h1, h2, h3, h4, h5, h6 {
    display: block;
    font-size: 2em;
    margin-top: .67em;
    margin-bottom: .67em;
    margin-left: 0;
    margin-right: 0;
    font-weight: 700;
    line-height: var(--line-height)
}
.text-link {
    color: #0275d8;
}
.text-link:hover {
    text-decoration: underline;
}
h2 {
    font-size: 1.5em;
    margin-top: .83em;
    margin-bottom: .83em
}

h3 {
    font-size: 1.17em;
    margin-top: 1em;
    margin-bottom: 1em
}

h4 {
    font-size: 1.15em;
    margin-top: 1em;
    margin-bottom: 1em
}

h5 {
    font-size: 1.12em;
    margin-top: 1em;
    margin-bottom: 1em
}

h6 {
    font-size: 1.1em;
    margin-top: 1em;
    margin-bottom: 1em
}

p {
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0
}
b, strong {
    font-weight: bold;
}
a.link {
    color: #087cce
}

a.link:hover {
    color: #087cce;
    text-decoration: underline
}

:focus {
    outline-color: #ff7800
}

.img, img, img.lazyload {
    -webkit-transition: opacity .5s ease-in;
    -o-transition: opacity .5s ease-in;
    transition: opacity .5s ease-in;
    border-style: none;
    max-width: 100%
}

img.lazyload {
    -ms-opacity: 0;
    opacity: 0
}

.img-responsive {
    max-width: 100%
}

.f-left {
    float: left
}

.f-right {
    float: right
}

.fix {
    overflow: hidden
}

.button {
    -webkit-transition: all .3s ease-out 0s;
    -o-transition: all .3s ease-out 0s;
    transition: all .3s ease-out 0s
}

.button:focus, a:focus {
    text-decoration: none;
    outline: 0
}

a:focus, a:hover {
    color: inherit;
    text-decoration: none
}

a, button {
    color: inherit;
    outline: medium none
}

a:hover {
    color: #ff7800
}

button:focus, input:focus, textarea, textarea:focus {
    outline: 0
}

.uppercase {
    text-transform: uppercase
}

.capitalize {
    text-transform: capitalize
}

h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
    color: inherit
}

ul {
    margin: 0;
    padding: 0
}

li {
    list-style: none
}

hr {
    border-bottom: 1px solid #eceff8;
    border-top: 0 none;
    margin: 20px 0;
    padding: 0
}

label {
    color: #7e7e7e;
    cursor: pointer;
    font-weight: 400
}

.v-content a, .v-content a:focus, .v-content a:visited {
    color: #076db6;
    outline: 0;
    text-decoration: none
}

.v-content a:hover {
    color: #087cce;
    text-decoration: underline
}

.v-content img {
    max-width: 100%;
    height: auto !important;
}

.v-content iframe {
    max-width: 100%
}

.table, .table-all, table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    display: table
}

.table-all, table {
    border: 1px solid #ccc
}

.table-all tr, .table-bordered tr, table tr {
    border-bottom: 1px solid #ddd
}

.table-striped tbody tr:nth-child(even) {
    background-color: #f1f1f1
}

.table-all tr:nth-child(odd) {
    background-color: #fff
}

.table-all tr:nth-child(even) {
    background-color: #f1f1f1
}

.hoverable tbody tr:hover {
    background-color: #ccc
}

.table-centered tr td, .table-centered tr th {
    text-align: center
}

.container {
    position: relative;
    margin-left: auto;
    margin-right: auto;
    padding-right: 15px;
    padding-left: 15px;
}

.table td, .table th, .table-all td, .table-all th, tabke table td, table th {
    padding: 8px 8px;
    display: table-cell;
    text-align: left;
    vertical-align: top;
    border: 1px solid #e1e1e1
}

.table td:first-child, .table th:first-child, .table-all td:first-child, .table-all th:first-child, table td:first-child, table th:first-child {
    padding-left: 16px
}

.table-responsive {
    display: block;
    overflow-x: auto
}

.v-content ol, .v-content ul {
    padding: 0;
    margin: 0
}

.v-content ol {
    list-style: decimal
}

.v-content li, .v-content ul {
    list-style: inherit;
    list-style-position: inside
}

.v-content ol li, .v-content ul li {
    padding: 0 15px;
    display: list-item;
    text-align: -webkit-match-parent
}

::-moz-selection {
    background: #333;
    color: #fff;
    text-shadow: none
}

::-moz-selection {
    background: #333;
    color: #fff;
    text-shadow: none
}

::selection {
    background: #333;
    color: #fff;
    text-shadow: none
}

::-moz-placeholder {
    color: #333;
    opacity: 1
}

::-webkit-input-placeholder {
    color: #333;
    opacity: 1
}

:-ms-input-placeholder {
    color: #333;
    opacity: 1
}

::-ms-input-placeholder {
    color: #333;
    opacity: 1
}

::placeholder {
    color: #333;
    opacity: 1
}

.text-color {
    color: #1f2642
}

.text-white {
    color: #fff
}

.main-color {
    color: #ff7800 !important;
    opacity: 1 !important
}

.opacity-0 {
    opacity: 0 !important
}

.opacity-05 {
    opacity: .5
}

.opacity-06 {
    opacity: .6
}

.opacity-07 {
    opacity: .7
}

.section {
    padding: 50px 0;
    overflow: hidden
}

.section .section-title {
    margin-bottom: 30px
}

.section .section-title .headline {
    margin-top: 0;
    font-size: 42px
}

.section .section-title.text-center p.description {
    margin: 0 auto
}

.text-ellipsis {
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: 40px;
    display: -webkit-box !important
}

.text-ellipsis.ellipsis-1 {
    -webkit-line-clamp: 1
}

.text-ellipsis.ellipsis-3 {
    -webkit-line-clamp: 3
}

.text-ellipsis.ellipsis-4 {
    -webkit-line-clamp: 4
}

.text-ellipsis.ellipsis-5 {
    -webkit-line-clamp: 5
}

.btn-zozo {
    min-width: 120px;
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    border-radius: 5px;
    border: 1px solid transparent;
    display: inline-block;
    text-align: center;
    cursor: pointer
}

.btn-zozo-small{
    height: 34px;
    line-height: 34px;
}

.btn-zozo .svg-icon {
    position: relative;
    top: 3px;
    left: 5px
}

.btn-white {
    background: #fff;
    color: #ff7800
}

.btn-white:hover {
    background: #ff7800;
    border-color: #fff;
    color: #fff
}

.btn-orange {
    background: #ff7800;
    color: #fff
}

.btn-orange .svg-icon path {
    fill: #fff
}

.btn-orange:hover .svg-icon path {
    fill: #ff7800
}

.btn-white .svg-icon path {
    fill: #ff7800
}

.btn-white:hover .svg-icon path {
    fill: #fff
}

.btn-orange:hover {
    background: #fff;
    color: #ff7800
}

.btn-orange.btn-border:hover {
    border-color: #ff7800
}

.btn-green {
    background: #78c145;
    color: #fff
}

.btn-green:hover {
    background: #fff !important;
    color: #78c145 !important;
    border-color: #78c145 !important;
}

.btn-green.btn-border:hover {
    border-color: #78c145 !important;
}

.btn-oceans {
    background: #2468b3;
    color: #fff
}

.btn-oceans:hover {
    background: #fff !important;
    color: #2468b3
}

.btn-oceans.btn-border:hover {
    border-color: #2468b3
}
.btn-green {
    background-color: #78C145 !important;
    color: #fff !important;
}
.btn-blue {
    background-color: #2E6CF6 !important;
    color: #fff !important;
}
.btn-sea-blue {
    background-color: #163C79 !important;
    color: #fff !important;
}
.btn-pink {
    background-color: #D5002E !important;
    color: #fff !important;
}
.btn-border-green {
    border: 1px solid #78C145 !important;
    color: #78C145 !important;
}
.btn-border-blue {
    border: 1px solid #2E6CF6 !important;
    color: #2E6CF6 !important;
}
.btn-border-sea-blue {
    border: 1px solid #163C79 !important;
    color: #163C79 !important;
}
.btn-border-pink {
    border: 1px solid #D5002E !important;
    color: #D5002E !important;
}
.btn-border-white {
    border: 1px solid #FFFFFF !important;
    color: #FFFFFF !important;
}
.btn-border-green:hover {
    border: 1px solid #78C145 !important;
    background-color: #78C145 !important;
    color: #fff !important;
}
.btn-border-blue:hover {
    border: 1px solid #2E6CF6 !important;
    background-color: #2E6CF6 !important;
    color: #fff !important;
}
.btn-border-sea-blue:hover {
    border: 1px solid #163C79 !important;
    background-color: #163C79 !important;
    color: #fff !important;
}
.btn-border-pink:hover {
    border: 1px solid #D5002E !important;
    background-color: #D5002E !important;
    color: #fff !important;
}
.btn-border-white:hover {
    border: 1px solid #FFFFFF !important;
    background-color: #FFFFFF !important;
    color: #000 !important;
}
.text-green {
    color: #78C145 !important;
}
.text-blue {
    color: #2E6CF6 !important;
}
.text-sea-blue {
    color: #163C79 !important;
}
.text-pink {
    color: #D5002E !important;
}
.text-hover-green:hover {
    color: #78C145 !important;
}
.text-hover-blue:hover {
    color: #2E6CF6 !important;
}
.text-hover-sea-blue:hover {
    color: #163C79 !important;
}
.text-hover-pink:hover {
    color: #D5002E !important;
}

.btn-medium {
    min-width: 150px
}

.btn-big-large {
    min-width: 300px
}

.btn-large {
    min-width: 200px
}

.btn-small {
    min-width: 100px;
    height: 35px;
    line-height: 35px;
    padding: 0 16px
}

.btn-radius {
    border-radius: 100px
}

@media(max-width:320px) {
    .btn-zozo {
        min-width: unset
    }
}

.height-40 {
    height: 40px;
    line-height: 40px
}

.height-45 {
    height: 45px;
    line-height: 45px
}

.height-50 {
    height: 50px;
    line-height: 50px
}

.height-60 {
    height: 60px;
    line-height: 60px
}

input.outline-success {
    border-color: #25b900
}

.input-zozo {
    box-sizing: border-box;
    border-radius: 6px;
    padding: 15px 25px
}

.input-zozo::placeholder {
    font-size: 16px
}

.text-danger {
    color: #a94442
}

.text-success {
    color: #25b900
}

.space-height {
    width: 100%
}

.space-height--10 {
    height: 10px
}

.space-height--20 {
    height: 20px
}

.space-height--30 {
    height: 30px
}

.space-height--40 {
    height: 40px
}

.space-height--50 {
    height: 50px
}

.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px dashed;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent
}

.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px dashed;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent
}

.close {
    position: absolute;
    right: 15px;
    top: 25px;
    width: 32px;
    height: 32px;
    opacity: 1
}

.close:after, .close:before {
    position: absolute;
    left: 15px;
    content: ' ';
    height: 20px;
    width: 2px;
    background-color: #1f2642
}

.close:before {
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg)
}

.close:after {
    -webkit-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.checkmark {
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 12px;
    width: 7px;
    border-bottom: 3px solid #fff;
    border-right: 3px solid #fff
}

.fs-14 {
    font-size: 14px
}

.fs-20 {
    font-size: 20px
}

.arrow {
    border: solid #1f2642;
    border-width: 0 1px 1px 0;
    display: inline-block;
    padding: 3px
}

.right {
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg)
}

.left {
    -ms-transform: rotate(135deg);
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg)
}

.up {
    -ms-transform: rotate(-135deg);
    transform: rotate(-135deg);
    -webkit-transform: rotate(-135deg)
}

.down {
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg)
}

.svg-icon {
    width: 1em;
    height: 1em
}

.svg-icon path, .svg-icon polygon, .svg-icon rect {
    fill: #ff7800
}

.svg-icon circle {
    stroke: #4691f6;
    stroke-width: 1
}

.plus-minus {
    position: relative;
    width: 10px;
    height: 10px
}

.plus-minus:after, .plus-minus:before {
    content: "";
    position: absolute;
    background-color: #000;
    -webkit-transition: -webkit-transform .25s ease-out;
    transition: -webkit-transform .25s ease-out;
    -o-transition: transform .25s ease-out;
    transition: transform .25s ease-out;
    transition: transform .25s ease-out, -webkit-transform .25s ease-out
}

.plus-minus:before {
    top: 0;
    left: 50%;
    width: 2px;
    height: 100%;
    margin-left: -1px
}

.plus-minus:after {
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    margin-top: -1px
}

.plus-minus:hover {
    cursor: pointer
}

.card-header.active .plus-minus:before {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}

.card-header.active .plus-minus:after {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg)
}

.fix {
    overflow: hidden
}

.clear {
    clear: both
}

.grey-bg {
    background: #f8f8f8
}

.grey-bg-2 {
    background: #f4f9fc
}

.white-bg {
    background: #fff
}

.black-bg {
    background: #333
}

.orange-bg {
    background-color: #ff7800;
}

.theme-bg {
    background: #096bd8
}

.theme-bg {
    background: #096bd8
}

.footer-bg {
    background: #091b29
}

.orange-bg-blur {
    background: #fff1e4
}

.white-color {
    color: #fff
}

.black-color {
    color: #333
}

.theme-color {
    color: #096bd8
}

.mt-5 {
    margin-top: 5px
}

.mt-10 {
    margin-top: 10px
}

.mt-15 {
    margin-top: 15px
}

.mt-20 {
    margin-top: 20px
}

.mt-25 {
    margin-top: 25px
}

.mt-30 {
    margin-top: 30px
}

.mt-35 {
    margin-top: 35px
}

.mt-40 {
    margin-top: 40px
}

.mt-45 {
    margin-top: 45px
}

.mt-50 {
    margin-top: 50px
}

.mt-55 {
    margin-top: 55px
}

.mt-60 {
    margin-top: 60px
}

.mt-65 {
    margin-top: 65px
}

.mt-70 {
    margin-top: 70px
}

.mt-75 {
    margin-top: 75px
}

.mt-80 {
    margin-top: 80px
}

.mt-85 {
    margin-top: 85px
}

.mt-90 {
    margin-top: 90px
}

.mt-95 {
    margin-top: 95px
}

.mt-100 {
    margin-top: 100px
}

.mt-105 {
    margin-top: 105px
}

.mt-110 {
    margin-top: 110px
}

.mt-115 {
    margin-top: 115px
}

.mt-120 {
    margin-top: 120px
}

.mt-125 {
    margin-top: 125px
}

.mt-130 {
    margin-top: 130px
}

.mt-135 {
    margin-top: 135px
}

.mt-140 {
    margin-top: 140px
}

.mt-145 {
    margin-top: 145px
}

.mt-150 {
    margin-top: 150px
}

.mt-155 {
    margin-top: 155px
}

.mt-160 {
    margin-top: 160px
}

.mt-165 {
    margin-top: 165px
}

.mt-170 {
    margin-top: 170px
}

.mt-175 {
    margin-top: 175px
}

.mt-180 {
    margin-top: 180px
}

.mt-185 {
    margin-top: 185px
}

.mt-190 {
    margin-top: 190px
}

.mt-195 {
    margin-top: 195px
}

.mt-200 {
    margin-top: 200px
}

.mb-5 {
    margin-bottom: 5px
}

.mb-10 {
    margin-bottom: 10px
}

.mb-15 {
    margin-bottom: 15px
}

.mb-20 {
    margin-bottom: 20px
}

.mb-25 {
    margin-bottom: 25px
}

.mb-30 {
    margin-bottom: 30px
}

.mb-35 {
    margin-bottom: 35px
}

.mb-40 {
    margin-bottom: 40px
}

.mb-45 {
    margin-bottom: 45px
}

.mb-50 {
    margin-bottom: 50px
}

.mb-55 {
    margin-bottom: 55px
}

.mb-60 {
    margin-bottom: 60px
}


.mb-65 {
    margin-bottom: 65px
}

.mb-70 {
    margin-bottom: 70px
}

.mb-75 {
    margin-bottom: 75px
}

.mb-80 {
    margin-bottom: 80px
}

.mb-85 {
    margin-bottom: 85px
}

.mb-90 {
    margin-bottom: 90px
}

.mb-95 {
    margin-bottom: 95px
}

.mb-100 {
    margin-bottom: 100px
}

.mb-105 {
    margin-bottom: 105px
}

.mb-110 {
    margin-bottom: 110px
}

.mb-115 {
    margin-bottom: 115px
}

.mb-120 {
    margin-bottom: 120px
}

.mb-125 {
    margin-bottom: 125px
}

.mb-130 {
    margin-bottom: 130px
}

.mb-135 {
    margin-bottom: 135px
}

.mb-140 {
    margin-bottom: 140px
}

.mb-145 {
    margin-bottom: 145px
}

.mb-150 {
    margin-bottom: 150px
}

.mb-155 {
    margin-bottom: 155px
}

.mb-160 {
    margin-bottom: 160px
}

.mb-165 {
    margin-bottom: 165px
}

.mb-170 {
    margin-bottom: 170px
}

.mb-175 {
    margin-bottom: 175px
}

.mb-180 {
    margin-bottom: 180px
}

.mb-185 {
    margin-bottom: 185px
}

.mb-190 {
    margin-bottom: 190px
}

.mb-195 {
    margin-bottom: 195px
}

.mb-200 {
    margin-bottom: 200px
}

.mb-205 {
    margin-bottom: 205px
}

.mb-210 {
    margin-bottom: 210px
}

.mb-215 {
    margin-bottom: 215px
}

.mb-220 {
    margin-bottom: 220px
}

.mb-225 {
    margin-bottom: 225px
}

.mb-230 {
    margin-bottom: 230px
}

.mb-235 {
    margin-bottom: 235px
}

.mb-240 {
    margin-bottom: 240px
}

.mb-245 {
    margin-bottom: 245px
}

.mb-250 {
    margin-bottom: 250px
}

.mb-255 {
    margin-bottom: 255px
}

.mb-260 {
    margin-bottom: 260px
}

.mb-265 {
    margin-bottom: 265px
}

.mb-270 {
    margin-bottom: 270px
}

.mb-275 {
    margin-bottom: 275px
}

.mb-280 {
    margin-bottom: 280px
}

.mb-285 {
    margin-bottom: 285px
}

.mb-290 {
    margin-bottom: 290px
}

.mb-295 {
    margin-bottom: 295px
}

.mb-300 {
    margin-bottom: 300px
}

.ml-5 {
    margin-left: 5px
}

.ml-10 {
    margin-left: 10px
}

.ml-15 {
    margin-left: 15px
}

.ml-20 {
    margin-left: 20px
}

.ml-25 {
    margin-left: 25px
}

.ml-30 {
    margin-left: 30px
}

.ml-35 {
    margin-left: 35px
}

.ml-40 {
    margin-left: 40px
}

.ml-45 {
    margin-left: 45px
}

.ml-50 {
    margin-left: 50px
}

.ml-55 {
    margin-left: 55px
}

.ml-60 {
    margin-left: 60px
}

.ml-65 {
    margin-left: 65px
}

.ml-70 {
    margin-left: 70px
}

.ml-75 {
    margin-left: 75px
}

.ml-80 {
    margin-left: 80px
}

.ml-85 {
    margin-left: 85px
}

.ml-90 {
    margin-left: 90px
}

.ml-95 {
    margin-left: 95px
}

.ml-100 {
    margin-left: 100px
}

.ml-105 {
    margin-left: 105px
}

.ml-110 {
    margin-left: 110px
}

.ml-115 {
    margin-left: 115px
}

.ml-120 {
    margin-left: 120px
}

.ml-125 {
    margin-left: 125px
}

.ml-130 {
    margin-left: 130px
}

.ml-135 {
    margin-left: 135px
}

.ml-140 {
    margin-left: 140px
}

.ml-145 {
    margin-left: 145px
}

.ml-150 {
    margin-left: 150px
}

.ml-155 {
    margin-left: 155px
}

.ml-160 {
    margin-left: 160px
}

.ml-165 {
    margin-left: 165px
}

.ml-170 {
    margin-left: 170px
}

.ml-175 {
    margin-left: 175px
}

.ml-180 {
    margin-left: 180px
}

.ml-185 {
    margin-left: 185px
}

.ml-190 {
    margin-left: 190px
}

.ml-195 {
    margin-left: 195px
}

.ml-200 {
    margin-left: 200px
}

.mr-5 {
    margin-right: 5px
}

.mr-10 {
    margin-right: 10px
}

.mr-15 {
    margin-right: 15px
}

.mr-20 {
    margin-right: 20px
}

.mr-25 {
    margin-right: 25px
}

.mr-30 {
    margin-right: 30px
}

.mr-35 {
    margin-right: 35px
}

.mr-40 {
    margin-right: 40px
}

.mr-45 {
    margin-right: 45px
}

.mr-50 {
    margin-right: 50px
}

.mr-55 {
    margin-right: 55px
}

.mr-60 {
    margin-right: 60px
}

.mr-65 {
    margin-right: 65px
}

.mr-70 {
    margin-right: 70px
}

.mr-75 {
    margin-right: 75px
}

.mr-80 {
    margin-right: 80px
}

.mr-85 {
    margin-right: 85px
}

.mr-90 {
    margin-right: 90px
}

.mr-95 {
    margin-right: 95px
}

.mr-100 {
    margin-right: 100px
}

.mr-105 {
    margin-right: 105px
}

.mr-110 {
    margin-right: 110px
}

.mr-115 {
    margin-right: 115px
}

.mr-120 {
    margin-right: 120px
}

.mr-125 {
    margin-right: 125px
}

.mr-130 {
    margin-right: 130px
}

.mr-135 {
    margin-right: 135px
}

.mr-140 {
    margin-right: 140px
}

.mr-145 {
    margin-right: 145px
}

.mr-150 {
    margin-right: 150px
}

.mr-155 {
    margin-right: 155px
}

.mr-160 {
    margin-right: 160px
}

.mr-165 {
    margin-right: 165px
}

.mr-170 {
    margin-right: 170px
}

.mr-175 {
    margin-right: 175px
}

.mr-180 {
    margin-right: 180px
}

.mr-185 {
    margin-right: 185px
}

.mr-190 {
    margin-right: 190px
}

.mr-195 {
    margin-right: 195px
}

.mr-200 {
    margin-right: 200px
}

.pt-5 {
    padding-top: 5px
}

.pt-10 {
    padding-top: 10px
}

.pt-15 {
    padding-top: 15px
}

.pt-20 {
    padding-top: 20px
}

.pt-25 {
    padding-top: 25px
}

.pt-30 {
    padding-top: 30px
}

.pt-35 {
    padding-top: 35px !important;
}

.pt-40 {
    padding-top: 40px !important;
}

.pt-45 {
    padding-top: 45px !important;
}

.pt-50 {
    padding-top: 50px !important;
}

.pt-55 {
    padding-top: 55px !important;
}

.pt-60 {
    padding-top: 60px !important;
}

.pt-65 {
    padding-top: 65px !important;
}

.pt-70 {
    padding-top: 70px !important;
}

.pt-75 {
    padding-top: 75px !important;
}

.pt-80 {
    padding-top: 80px !important;
}

.pt-85 {
    padding-top: 85px !important;
}

.pt-90 {
    padding-top: 90px !important;
}

.pt-95 {
    padding-top: 95px !important;
}

.pt-100 {
    padding-top: 100px !important;
}

.pt-105 {
    padding-top: 105px
}

.pt-110 {
    padding-top: 110px
}

.pt-115 {
    padding-top: 115px
}

.pt-120 {
    padding-top: 120px
}

.pt-125 {
    padding-top: 125px
}

.pt-130 {
    padding-top: 130px
}

.pt-135 {
    padding-top: 135px
}

.pt-140 {
    padding-top: 140px
}

.pt-145 {
    padding-top: 145px
}

.pt-150 {
    padding-top: 150px
}

.pt-155 {
    padding-top: 155px
}

.pt-160 {
    padding-top: 160px
}

.pt-165 {
    padding-top: 165px
}

.pt-170 {
    padding-top: 170px
}

.pt-175 {
    padding-top: 175px
}

.pt-180 {
    padding-top: 180px
}

.pt-185 {
    padding-top: 185px
}

.pt-190 {
    padding-top: 190px
}

.pt-195 {
    padding-top: 195px
}

.pt-200 {
    padding-top: 200px
}

.pt-205 {
    padding-top: 205px
}

.pt-210 {
    padding-top: 210px
}

.pt-215 {
    padding-top: 215px
}

.pt-220 {
    padding-top: 220px
}

.pt-225 {
    padding-top: 225px
}

.pt-230 {
    padding-top: 230px
}

.pt-235 {
    padding-top: 235px
}

.pt-240 {
    padding-top: 240px
}

.pt-245 {
    padding-top: 245px
}

.pt-250 {
    padding-top: 250px
}

.pt-255 {
    padding-top: 255px
}

.pt-260 {
    padding-top: 260px
}

.pt-265 {
    padding-top: 265px
}

.pt-270 {
    padding-top: 270px
}

.pt-275 {
    padding-top: 275px
}

.pt-280 {
    padding-top: 280px
}

.pt-285 {
    padding-top: 285px
}

.pt-290 {
    padding-top: 290px
}

.pt-295 {
    padding-top: 295px
}

.pt-300 {
    padding-top: 300px
}

.pb-5 {
    padding-bottom: 5px
}

.pb-10 {
    padding-bottom: 10px
}

.pb-15 {
    padding-bottom: 15px
}

.pb-20 {
    padding-bottom: 20px
}

.pb-25 {
    padding-bottom: 25px
}

.pb-30 {
    padding-bottom: 30px
}

.pb-35 {
    padding-bottom: 35px
}

.pb-40 {
    padding-bottom: 40px
}

.pb-45 {
    padding-bottom: 45px
}

.pb-50 {
    padding-bottom: 50px
}

.pb-55 {
    padding-bottom: 55px
}

.pb-60 {
    padding-bottom: 60px
}

.pb-65 {
    padding-bottom: 65px
}

.pb-70 {
    padding-bottom: 70px
}

.pb-75 {
    padding-bottom: 75px
}

.pb-80 {
    padding-bottom: 80px
}

.pb-85 {
    padding-bottom: 85px
}

.pb-90 {
    padding-bottom: 90px
}

.pb-95 {
    padding-bottom: 95px
}

.pb-100 {
    padding-bottom: 100px
}

.pb-105 {
    padding-bottom: 105px
}

.pb-110 {
    padding-bottom: 110px
}

.pb-115 {
    padding-bottom: 115px
}

.pb-120 {
    padding-bottom: 120px
}

.pb-125 {
    padding-bottom: 125px
}

.pb-130 {
    padding-bottom: 130px
}

.pb-135 {
    padding-bottom: 135px
}

.pb-140 {
    padding-bottom: 140px
}

.pb-145 {
    padding-bottom: 145px
}

.pb-150 {
    padding-bottom: 150px
}

.pb-155 {
    padding-bottom: 155px
}

.pb-160 {
    padding-bottom: 160px
}

.pb-165 {
    padding-bottom: 165px
}

.pb-170 {
    padding-bottom: 170px
}

.pb-175 {
    padding-bottom: 175px
}

.pb-180 {
    padding-bottom: 180px
}

.pb-185 {
    padding-bottom: 185px
}

.pb-190 {
    padding-bottom: 190px
}

.pb-195 {
    padding-bottom: 195px
}

.pb-200 {
    padding-bottom: 200px
}

.pb-205 {
    padding-bottom: 205px
}

.pb-210 {
    padding-bottom: 210px
}

.pb-215 {
    padding-bottom: 215px
}

.pb-220 {
    padding-bottom: 220px
}

.pb-225 {
    padding-bottom: 225px
}

.pb-230 {
    padding-bottom: 230px
}

.pb-235 {
    padding-bottom: 235px
}

.pb-240 {
    padding-bottom: 240px
}

.pb-245 {
    padding-bottom: 245px
}

.pb-250 {
    padding-bottom: 250px
}

.pb-255 {
    padding-bottom: 255px
}

.pb-260 {
    padding-bottom: 260px
}

.pb-265 {
    padding-bottom: 265px
}

.pb-270 {
    padding-bottom: 270px
}

.pb-275 {
    padding-bottom: 275px
}

.pb-280 {
    padding-bottom: 280px
}

.pb-285 {
    padding-bottom: 285px
}

.pb-290 {
    padding-bottom: 290px
}

.pb-295 {
    padding-bottom: 295px
}

.pb-300 {
    padding-bottom: 300px
}

.pl-5 {
    padding-left: 5px !important;
}

.pl-10 {
    padding-left: 10px !important;
}

.pl-15 {
    padding-left: 15px !important;
}

.pl-20 {
    padding-left: 20px !important;
}

.pl-25 {
    padding-left: 25px !important;
}

.pl-30 {
    padding-left: 30px !important;
}

.pl-35 {
    padding-left: 35px !important;
}

.pl-40 {
    padding-left: 40px
}

.pl-45 {
    padding-left: 45px
}

.pl-50 {
    padding-left: 50px
}

.pl-55 {
    padding-left: 55px
}

.pl-60 {
    padding-left: 60px
}

.pl-65 {
    padding-left: 65px
}

.pl-70 {
    padding-left: 70px
}

.pl-75 {
    padding-left: 75px
}

.pl-80 {
    padding-left: 80px
}

.pl-85 {
    padding-left: 85px
}

.pl-90 {
    padding-left: 90px
}

.pl-95 {
    padding-left: 95px
}

.pl-100 {
    padding-left: 100px
}

.pl-105 {
    padding-left: 105px
}

.pl-110 {
    padding-left: 110px
}

.pl-115 {
    padding-left: 115px
}

.pl-120 {
    padding-left: 120px
}

.pl-125 {
    padding-left: 125px
}

.pl-130 {
    padding-left: 130px
}

.pl-135 {
    padding-left: 135px
}

.pl-140 {
    padding-left: 140px
}

.pl-145 {
    padding-left: 145px
}

.pl-150 {
    padding-left: 150px
}

.pl-155 {
    padding-left: 155px
}

.pl-160 {
    padding-left: 160px
}

.pl-165 {
    padding-left: 165px
}

.pl-170 {
    padding-left: 170px
}

.pl-175 {
    padding-left: 175px
}

.pl-180 {
    padding-left: 180px
}

.pl-185 {
    padding-left: 185px
}

.pl-190 {
    padding-left: 190px
}

.pl-195 {
    padding-left: 195px
}

.pl-200 {
    padding-left: 200px !important;
}

.pl-300 {
    padding-left: 300px !important;
}

.pr-5 {
    padding-right: 5px !important;
}

.pr-10 {
    padding-right: 10px !important;
}

.pr-15 {
    padding-right: 15px !important;
}

.pr-20 {
    padding-right: 20px !important;
}

.pr-25 {
    padding-right: 25px !important;
}

.pr-30 {
    padding-right: 30px !important;
}

.pr-35 {
    padding-right: 35px !important;
}

.pr-40 {
    padding-right: 40px !important;
}

.pr-45 {
    padding-right: 45px !important;
}

.pr-50 {
    padding-right: 50px !important;
}

.pr-55 {
    padding-right: 55px !important;
}

.pr-60 {
    padding-right: 60px !important;
}

.pr-65 {
    padding-right: 65px !important;
}

.pr-70 {
    padding-right: 70px
}

.pr-75 {
    padding-right: 75px
}

.pr-80 {
    padding-right: 80px
}

.pr-85 {
    padding-right: 85px
}

.pr-90 {
    padding-right: 90px
}

.pr-95 {
    padding-right: 95px
}

.pr-100 {
    padding-right: 100px
}

.pr-105 {
    padding-right: 105px
}

.pr-110 {
    padding-right: 110px
}

.pr-115 {
    padding-right: 115px
}

.pr-120 {
    padding-right: 120px
}

.pr-125 {
    padding-right: 125px
}

.pr-130 {
    padding-right: 130px
}

.pr-135 {
    padding-right: 135px
}

.pr-140 {
    padding-right: 140px
}

.pr-145 {
    padding-right: 145px
}

.pr-150 {
    padding-right: 150px
}

.pr-155 {
    padding-right: 155px
}

.pr-160 {
    padding-right: 160px
}

.pr-165 {
    padding-right: 165px
}

.pr-170 {
    padding-right: 170px
}

.pr-175 {
    padding-right: 175px
}

.pr-180 {
    padding-right: 180px
}

.pr-185 {
    padding-right: 185px
}

.pr-190 {
    padding-right: 190px
}

.pr-195 {
    padding-right: 195px
}

.pr-200 {
    padding-right: 200px
}

.pr-300 {
    padding-right: 300px
}

.mt--50 {
    margin-top: -50px
}

.mt--100 {
    margin-top: -100px
}

.mt--120 {
    margin-top: -120px
}

.mt--150 {
    margin-top: -150px
}

.mt--170 {
    margin-top: -170px
}

@media (min-width:991px) {
    .no-padding {
        padding: 0
    }
}

.no-padding-right {
    padding-right: 0
}

.no-padding-left {
    padding-left: 0
}

.btn-text {
    color: #fff;
    position: relative;
    -webkit-transition: .3s;
    -o-transition: .3s;
    transition: .3s
}

.btn:hover .btn-text {
    color: #fff
}

.btn::before {
    content: '';
    width: 0;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 100%;
    background: #096bd8;
    -webkit-transition: all .3s ease;
    -webkit-transition: all .4s ease;
    -o-transition: all .4s ease;
    transition: all .4s ease;
    border-radius: 5px
}

svg.arrow-right {
    width: 12px;
    height: 9px;
    margin-left: 9px;
    display: inline-block;
    fill: #ff7800
}

a.btn-zozo:hover .arrow-right {
    fill: #fff
}

.btn:hover .btn-text {
    color: #fff
}

.breadcrumb > .active {
    color: #888
}

.pink-btn {
    background: #d10459
}

.pink-btn .btn-text {
    color: #fff
}

.pink-btn .btn-border {
    background: #fff
}

.btn-none::before {
    display: none
}

.border-btn {
    -moz-user-select: none;
    border: 2px solid #fff;
    display: inline-block;
    margin-bottom: 0;
    padding: 20px 40px;
    text-align: center;
    text-transform: uppercase;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-transition: all .3s ease 0s;
    -o-transition: all .3s ease 0s;
    transition: all .3s ease 0s;
    vertical-align: middle;
    white-space: nowrap;
    border-radius: 5px;
    position: relative
}

.btn-text {
    color: #fff;
    position: relative;
    -webkit-transition: .3s;
    -o-transition: .3s;
    transition: .3s
}

.border-btn:hover {
    border-color: #096bd8
}

.btn-text i {
    padding-left: 10px
}

.border-btn::before {
    content: '';
    width: 0;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 100%;
    background: #096bd8;
    -webkit-transition: all .3s ease;
    -webkit-transition: all .4s ease;
    -o-transition: all .4s ease;
    transition: all .4s ease;
    border-radius: 5px
}

.border-btn:hover::before {
    width: 100%;
    left: 0;
    -webkit-transition: width .3s ease;
    -o-transition: width .3s ease;
    transition: width .3s ease;
    background: #1876df
}

.ab-01 {
    top: 0;
    right: 0
}

.ab-02 {
    top: 18%;
    left: 39%
}

.ab-03 {
    top: 24%;
    left: 10%
}

.ab-04 {
    top: 64%;
    left: 0
}

.ab-05 {
    bottom: 19%;
    left: 13%
}

.header-2-button a:hover, .header-2-button a:hover .fa-angle-down, .main-menu nav > ul > li.active > a, .main-menu nav > ul > li:hover .fa-angle-down, .main-menu nav > ul > li:hover > a {
    color: #ff7800 !important;
    -webkit-transition: all .3s ease 0s;
    -o-transition: all .3s ease 0s;
    transition: all .3s ease 0s
}

span.arrowper {
    display: none;
}

.main-menu nav > ul > li.has-child:hover span.arrowper {
    display: block;
}

.hide {
    opacity: 0;
    height: 0;
    visibility: hidden;
    -webkit-transition: all .5s;
    -o-transition: all .5s;
    transition: all .5s
}

.open {
    opacity: 1 !important;
    height: auto !important;
    visibility: visible !important;
    -webkit-transition: all .5s;
    -o-transition: all .5s;
    transition: all .5s;
    transform: translate(0) !important
}

.horizontal-line {
    width: 100px;
    height: 1px;
    background: #ff7800;
    margin: 0 auto;
    margin-top: 12px;
    margin-bottom: 80px
}

.btn.focus, .btn:focus {
    -webkit-box-shadow: unset;
    box-shadow: unset
}

span.title {
    font-size: 18px;
    font-weight: 600;
    color: #ff7800;
    display: block
}

.headline {
    margin-bottom: 18px;
    font-size: 32px;
    color: #1f2642;
    font-weight: 700
}
.logo img, .logo-menumb img, .title-footer img {
    max-height: 45px;
}
.scrollToTop {
    background: #ff7800;
    height: 35px;
    width: 35px;
    right: 35px;
    bottom: 100px;
    color: #fff;
    font-size: 20px;
    text-align: center;
    border-radius: 50%;
    font-size: 20px;
    display: block;
    position: fixed;
    z-index: 9999;
    display: none
}

.half-field-control {
    width: 50%;
    float: left
}

.half-field-control input {
    width: 96%;
    float: left
}

.half-field-control + .half-field-control input {
    float: right
}

.half-field-control + .half-field-control .control-label {
    margin-left: 6px
}

.no-position {
    position: unset
}

.angle-right {
    position: absolute;
    top: 47%;
    right: 20px;
    -webkit-transform: translateY(-47%);
    -ms-transform: translateY(-47%);
    transform: translateY(-47%);
    display: none
}

.angle-right.white, .slider-button > .btn:hover .angle-right {
    display: block
}

.owl-carousel .owl-dots {
    padding: 40px 0;
    text-align: center
}

.premium-box .owl-carousel .owl-dots {
    padding: 0
}

.owl-carousel .owl-dots .owl-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    background: #ff7800;
    border-radius: 50%;
    margin: 0 6px;
    opacity: .5
}

.owl-carousel .owl-dots .owl-dot.active {
    opacity: 1
}

.owl-next i, .owl-next img, .owl-prev i, .owl-prev img {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%) rotate(135deg);
    -ms-transform: translate(-50%, -50%) rotate(135deg);
    transform: translate(-50%, -50%) rotate(135deg)
}

.owl-next i {
    -webkit-transform: translate(-50%, -50%) rotate(-45deg);
    -ms-transform: translate(-50%, -50%) rotate(-45deg);
    transform: translate(-50%, -50%) rotate(-45deg)
}

.owl-carousel .owl-nav .owl-next, .owl-carousel .owl-nav .owl-prev {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%)
}

.owl-carousel .owl-nav .owl-next {
    left: unset;
    right: 0
}

.owl-carousel .owl-dots .owl-dot {
    width: 10px;
    height: 10px
}

.header-transparent {
    position: relative;
    z-index: 9999;
    background: #fff;
    width: 100%
}

.header-transparent.scroll-down {
    top: -72px
}

.header-2-right {
    margin-top: 16px
}

.header-cta-text .btn {
    height: 40px;
    line-height: 40px
}

.header-cta-text span {
    color: #333;
    font-size: 14px
}

.header-cta-text span i {
    margin-right: 6px;
    color: #333;
    font-size: 14px
}

.header-cta-text p {
    color: #333;
    margin-bottom: 0
}

.header-cta-text p:last-child {
    font-weight: 700;
    margin-top: 0
}

.header-2-button:hover {
    cursor: pointer
}

.header-2-button a {
    -moz-user-select: none;
    display: inline-block;
    margin-bottom: 0;
    padding: 15px 5px;
    text-align: center;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-transition: all .3s ease 0s;
    -o-transition: all .3s ease 0s;
    transition: all .3s ease 0s;
    vertical-align: middle;
    white-space: nowrap;
    border-radius: 5px;
    position: relative;
    color: #1f2642
}

.header-2-button a i {
    padding-left: 5px;
    color: #333
}

.main-menu nav > ul > li {
    display: inline-block;
    margin-left: 25px
}

#desktop-menu ul li.active > a {
    color: #ff7800
}

#desktop-menu ul li.active .arrow {
    border-color: #ff7800
}

.main-menu nav > ul > li:first-child {
    margin-left: 0
}

.main-menu nav > ul > li > a {
    color: #1f2642;
    display: block;
    padding: 25px 0;
    background: 0 0;
    position: relative;
    border-bottom: 1px solid #fff
}

.main-menu nav > ul > li > a i {
    position: relative;
    top: -2px;
    left: 2px;
    color: #333
}

.main-menu nav > ul > li > a:hover {
    border-bottom: 1px solid #000
}

.main-menu nav > ul > li.active > a, .main-menu nav > ul > li:hover > a {
    color: #ff7800
}

.main-menu > nav > ul > li > a::before {
    background: #096bd8;
    content: "";
    height: 3px;
    position: absolute;
    bottom: 0;
    width: 0;
    -webkit-transition: .5s;
    -o-transition: .5s;
    transition: .5s
}

.main-menu nav > ul > li > a .angle-down {
    position: absolute;
    right: -12px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    display: none
}

.main-menu nav > ul > li .angle-down.black, .main-menu nav > ul > li.has-child:hover .angle-down {
    display: block
}

.main-menu nav > ul > li.has-child:hover .angle-down.black {
    display: none
}

.header-2-button {
    position: relative
}

.header-2-button > a img:last-child {
    margin-left: 5px
}

.header-2-button .sub-language {
    position: absolute;
    min-width: 125px;
    top: 40px;
    right: 0;
    z-index: 9999;
    display: none
}

.header-2-button .sub-language.active {
    display: block
}

.header-2-button .sub-language li {
    background: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-box-shadow: 0 4px 4px rgba(0, 0, 0, .12);
    box-shadow: 0 4px 4px rgba(0, 0, 0, .12)
}

.header-2-button .sub-language li:hover {
    background: #e9eefc
}

.header-2-button .sub-language li:first-child {
    border-bottom: 1px solid #eee
}

.header-2-button .sub-language a {
    width: 100%;
    padding: 9px 20px;
    border-radius: unset
}

.header-2-button .sub-language a:hover {
    color: #333 !important
}

.header-2-button .sub-language a span {
    float: left
}

.header-2-button .sub-language img {
    float: left;
    margin-right: 5px
}

.header-2-button:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999
}

.login-text {
    padding: 12px 15px
}

.login-text p:hover a {
    color: #ff7800 !important
}

.login-text a {
    display: block
}

.register-text p {
    color: #fff
}

.register-text:hover a {
    color: #ff7800
}

.sub-menu p {
    margin-bottom: 0;
    margin-top: 0
}

.main-menu nav > ul > li .sub-menu {
    background: #fff none repeat scroll 0 0;
    -webkit-box-shadow: 0 0 20px rgba(68, 38, 14, .25);
    box-shadow: 0 0 20px rgba(68, 38, 14, .25);
    left: 0;
    top: 60px;
    opacity: 0;
    position: absolute;
    -webkit-transition: all .3s ease 0s;
    -o-transition: all .3s ease 0s;
    transition: all .3s ease 0s;
    display: none;
    z-index: 9;
    text-align: left
}

.main-menu nav > ul > li.has-child {
    position: relative
}

.main-menu nav > ul > li:hover .sub-menu {
    opacity: 1;
    display: block;
    position: absolute
}

.main-menu nav > ul > li .sub-menu.product {
    width: 100%
}

.main-menu nav > ul > li:hover .sub-menu {
    min-width: 275px;
    left: -20px;
    padding: 10px 20px 20px 20px
}

.main-menu nav > ul > li .sub-menu li {
    display: block;
    position: relative;
}

.main-menu nav > ul > li .sub-menu ul.extra li a {
    margin: 0;
    padding: 5px 0px;
    display: block
}
.main-menu nav > ul > li .sub-menu li.sub-menu-requirement {
    margin-bottom: 10px;
}
.main-menu nav > ul > li .sub-menu li.sub-menu-requirement:after {
    content: '';
    position: absolute;
    left: 0px;
    right: 0px;
    bottom: -5px;
    border-bottom: 1px solid #dfe3e8;
}

.main-menu nav > ul > li .sub-menu.product ul.extra li:nth-child(2) a {
    padding-top: 0
}

.main-menu nav > ul > li.has-child:first-child {
    position: unset
}

.header-transparent .main-menu-area {
    position: relative;
    /* box-shadow: 0 20px 30px rgb(0 0 0 / 5%) */
}

.header-transparent .main-menu-area > .container {
    position: unset
}

div.sub-menu .text-sub-menu {
    padding-bottom: 30px;
    margin-bottom: 0;
    color: #1f2642;
    font-weight: 700
}

div.sub-menu ul.sub-menu-detail {
    position: relative !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    float: left
}

div.sub-menu ul.sub-menu-detail li {
    width: 33.33%;
    float: left
}

div.sub-menu ul.sub-menu-detail li .desc-product {
    padding: 0 20px 30px 70px
}

div.sub-menu ul.sub-menu-detail li img {
    width: 55px;
    height: 55px;
    float: left;
    -webkit-transition: unset;
    -o-transition: unset;
    transition: unset
}

div.sub-menu ul.sub-menu-detail li .desc-product .name-product {
    color: #1f2642;
    margin-bottom: 0;
    margin-top: 0
}

div.sub-menu ul.sub-menu-detail li .desc-product .detail-product {
    overflow: hidden;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    min-height: 52px;
    display: -webkit-box;
    font-size: 14px
}

.decrease-item .main-menu nav > ul > li.has-child, .decrease-item .no-position {
    position: relative
}

.decrease-item div.sub-menu ul.sub-menu-detail li {
    width: auto;
    float: unset;
    margin-bottom: 10px
}

.decrease-item div.sub-menu ul.sub-menu-detail {
    width: auto;
    padding: 0
}

.decrease-item .main-menu nav > ul > li .sub-menu.product {
    min-width: 350px;
    left: -150px;
    padding: 30px 30px 20px 30px
}

.decrease-item div.sub-menu ul.sub-menu-detail li .desc-product {
    padding: 0 0 0 70px
}

.decrease-item div.sub-menu ul.sub-menu-detail li.EMA .desc-product {
    padding-bottom: 0
}

#mobile-menu.decrease-item #mbsub-product li:nth-child(5), #mobile-menu.decrease-item #mbsub-product li:nth-child(6), #mobile-menu.decrease-item #mbsub-product li:nth-child(7) {
    display: none
}

@media (min-width:991px) {
    div.sub-menu ul.sub-menu-detail {
        width: 960px;
        max-width: 100%;
        margin: 0 auto;
        padding: 0 15px;
        overflow: hidden;
        float: none
    }

}

@media (min-width:1200px) {
    div.sub-menu ul.sub-menu-detail {
        width: 1140px;
        max-width: 100%
    }

}

div.sub-menu .vertical-line {
    width: 1px;
    height: 100%;
    background: #ffd4b0;
    margin: 0 auto
}

div.sub-menu ul.extra li a {
    padding-left: 0 !important;
    -webkit-transition: unset;
    -o-transition: unset;
    transition: unset
}

li.has-child > a {
    position: relative
}

span.arrowper {
    content: '';
    width: 0;
    height: 0;
    position: absolute;
    bottom: 6px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    border-bottom: 15px solid #fff;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    z-index: 9999
}

.header-2-right {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 0;
    position: relative
}

.header-cta-text.notification-icon {
    margin-right: 25px
}

.notification-dot {
    position: relative
}

.notification-dot:before {
    content: '';
    position: absolute;
    right: 0;
    top: -3px;
    width: 10px;
    height: 10px;
    background: #ff7800;
    border: 2px solid #fff;
    box-sizing: border-box;
    border-radius: 10px
}

.notification-box, .user-box {
    position: absolute;
    min-width: 325px;
    right: 0;
    top: 40px;
    z-index: 9999;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #f6f6f6;
    font-size: 12px;
    -webkit-box-shadow: 0 0 20px rgba(68, 38, 14, .25);
    box-shadow: 0 0 20px rgba(68, 38, 14, .25)
}

.notification-box_body, .notification-box_top {
    margin: 10px 10px 0 10px
}

.notification-box_toplist li {
    display: inline-block
}

.notification-box_toplist li:first-child {
    color: #b4b7c1
}

.notification-box_toplist li:nth-child(2) {
    margin-left: 35px;
    margin-right: 13px
}

.count-notification {
    background: #ff7800;
    border-radius: 100px;
    padding: 3px;
    margin-left: 5px;
    color: #fff;
    font-weight: 700
}

.notification-box_bodylist .bodylist-item {
    padding: 10px;
    margin: 10px 0;
    position: relative
}

.notification-box_bodylist .bodylist-item > a {
    display: flex;
    flex-wrap: wrap
}

.notification-box_bodylist .bodylist-item:last-child {
    margin-bottom: 0
}

.notification-box_bodylist .bodylist-item:hover {
    background: #fdf2e8;
    border-radius: 4px
}

.notification-time {
    position: absolute;
    right: 10px;
    color: #707485;
    opacity: .5
}

.notification-box_bodylist .avatar-icon {
    flex: 0 0 12%;
    max-width: 12%
}

.notification-box_bodylist .notification-content {
    flex: 0 0 88%;
    max-width: 88%;
    padding-left: 10px
}

.notification-username {
    color: #1f2642;
    font-weight: 700;
    margin-bottom: 5px
}

.notification-text {
    color: #707485
}

.notification-box_bottom {
    border-top: 1px solid #dfe1e9;
    padding: 10px 0
}

.notification-special {
    background: #ff7800;
    border-radius: 4px;
    color: #fff
}

.notification-special:hover {
    background: #ff7800 !important;
    color: #fff
}

.notification-special a:hover {
    color: #fff
}

.notification-special_title {
    font-weight: 700
}

.notification-special_text {
    opacity: .8;
    margin: 8px 0
}

.notification-special_update {
    text-decoration-line: underline
}

.notification-box .arrowper, .user-box .arrowper {
    display: block;
    top: -7px;
    left: unset;
    right: 53px;
    border-width: 7px
}

.notification-box_bodylist {
    height: 350px;
    overflow-y: scroll
}

.notification-box_bodylist::-webkit-scrollbar-track {
    border-radius: 10px
}

.notification-box_bodylist::-webkit-scrollbar {
    width: 8px
}

.notification-box_bodylist::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #e6e6e6
}

.avatar-icon img, .header-cta-text .avatar-icon {
    width: 32px;
    height: 32px;
    border-radius: 100%
}

.user-box {
    min-width: 210px
}

.user-box .arrowper {
    right: 0
}

.user-box_body li a {
    padding: 8px 12px;
    display: block
}

.user-box_icon img {
    width: 15px;
    height: 15px
}

.user-box_text {
    margin-left: 5px
}

.user-box_body li:hover {
    background: #ff7800
}

.user-box_body li:hover a {
    color: #fff
}

.slider-area {
    position: relative;
    background: linear-gradient(90deg, #ff983c 0, #ff7800 100%)
}

/* custom noel */
.slider-area.noel{
    background: #fff;
}

.slider-area.noel .btn-green{
    background: #ff7800;
}

.slider-area.noel .btn-green:hover{
    background: #fff;
    color: #ff7800;
    border:  2px solid #ff7800;
}

.slider-area.noel h1,
.slider-area.noel p{
    color: #1F2642!important;
}

.slider-area.noel .section-sub-title{
    visibility:  hidden;
}
/* end custom noel */

.slogan-zz {
    font-weight: 700;
    font-size: 14px !important;
    letter-spacing: .1em
}

.slider-content-3 {
    position: relative;
    z-index: 3;
    padding: 110px 60px 120px 0
}

.single-slider.slider-height-3:after {
    content: "";
    clear: both;
    display: table
}

.slider-content h1 {
    font-size: 35px;
    margin-bottom: 22px;
    letter-spacing: -3px;
    color: #fff;
    font-weight: 700;
    letter-spacing: -.03em
}

.slider-content p {
    font-size: 16px;
    color: #fff;
    margin-bottom: 0
}

.slider-button {
    margin-top: 24px
}

.slider-active button.slick-arrow {
    position: absolute;
    top: 50%;
    left: 70px;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    border: 0;
    font-size: 15px;
    padding: 0;
    color: #222;
    z-index: 2;
    opacity: 0;
    visibility: hidden;
    height: 70px;
    width: 70px;
    border-radius: 50%;
    cursor: pointer;
    background: #fff
}

.slider-active button.slick-next {
    left: auto;
    right: 70px
}

.slider-active:hover button {
    opacity: 1;
    visibility: visible
}

.slider-active button:hover {
    background: #096bd8;
    color: #fff
}

.mainbanner-content .section-title h1 {
    font-size: 28px
}

.slider-content .section-title span.description {
    color: #fff
}

.slogan {
    font-weight: 700;
    font-size: 14px !important;
    letter-spacing: .1em;
    color: #fff;
    text-transform: uppercase
}

.slider-thumb img {
    max-width: 100%
}

.home-page .slider-thumb {
    position: relative;
    z-index: 2;
    min-height: 100%;
    margin-top: 4px;
    background-size: contain;
    background-position: bottom;
    background-repeat: no-repeat
}

.slider-area {
    background: linear-gradient(90deg, #ff983c 0, #ff7800 100%)
}

.slider-area .bg-slider {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: bottom;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.mainbanner-content .section-title {
    margin-top: 86px;
    margin-bottom: 170px
}

@media(max-width:767px) {
    .mainbanner-content .section-title h1 {
        font-size: 25px
    }

    .mainbanner-content .description {
        font-size: 18px
    }

    .mainbanner-content .section-sub-title {
        font-size: 14px
    }

    .slider-area .bg-slider {
        background-size: cover;
        background-position: center
    }

    .mainbanner-content .section-title {
        margin-top: 70px;
        margin-bottom: 70px
    }

}

p.trial {
    font-style: italic;
    color: #ff7800;
    opacity: .78
}

.business-platform-area .slider-button > .btn:hover .angle-right.white, .fact-are-area .slider-button > .btn:hover .angle-right.white, .slider-area .slider-button > .btn:hover .angle-right.white {
    display: none
}

.logo-product-list {
    margin: 0 80px
}

.services-area {
    background-size: cover;
    background-position: center center;
    position: relative;
    padding-top: 85px
}

.services-text h3 {
    font-size: 16px;
    margin-top: 23px;
    margin-bottom: 15px
}

.services-text p, .services-text .services-name {
    margin: 0;
    position: relative
}

.description {
    font-style: normal;
    font-weight: 400;
    color: #1f2642
}

.section-title h1 > span {
    color: #096bd8
}

.single-services:hover::before {
    opacity: 1
}

.single-services.active::before {
    opacity: 1
}

.single-services .services-text {
    z-index: 999;
    position: relative
}

.single-services .services-text h3 {
    -webkit-transition: .3s;
    -o-transition: .3s;
    transition: .3s
}

.services-button i {
    margin-left: 5px
}

.single-services.active .services-button {
    color: #fff
}

.single-services.active .services-text a {
    border-color: #1065c4
}

.single-services.active .services-text a::before {
    width: 100%;
    left: 0;
    -webkit-transition: width .3s ease;
    -o-transition: width .3s ease;
    transition: width .3s ease
}

.single-services {
    position: relative;
    z-index: 2;
    background: #fff;
    -webkit-box-shadow: 0 0 44px rgba(144, 151, 179, .19);
    box-shadow: 0 0 44px rgba(144, 151, 179, .19);
    padding: 30px;
    margin-bottom: 40px;
    border-radius: 20px
}

.single-services .description-services {
    min-height: 110px;
    color: #6b6b6b;
    padding-top: 20px;
    padding-bottom: 25px;
    margin-bottom: 0 !important
}

.single-services .btn-white .svg-icon path {
    fill: #1f2642
}

.single-services .btn-white:hover .svg-icon path {
    fill: #fff
}

.img-services {
    min-height: 70px
}

.img-services img {
    width: 95px;
    height: 95px;
    margin: 0 auto
}

.services-name {
    font-size: 16px;
    font-weight: 700;
    color: #1f2642;
    padding-top: 13px;
    padding-bottom: 15px
}

.services-name .tag-line {
    position: absolute;
    top: 5px;
    right: 30px;
    border: 1px solid;
    box-sizing: border-box;
    border-radius: 100px;
    padding: 2px 10px;
    margin-left: 5px;
    font-size: 13px
}

.services-name .tag-line.sale {
    color: #ff7800
}

.services-name .tag-line.free {
    color: #25b900
}

.services-name .tag-line.beta {
    color: #9d9d9d
}

.services-viewmore {
    font-style: normal;
    font-weight: 400;
    color: #ff7800;
    position: relative
}

.description-services {
    font-style: normal;
    font-weight: 400;
    text-align: center;
    color: #696969;
    overflow: hidden;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    min-height: 45px;
    display: -webkit-box
}

.sub-services-name {
    font-size: 20px;
    color: #1f2642
}

.services-viewmore i {
    font-size: 10px;
    padding-left: 6px
}

.services-viewmore:hover {
    color: #ff7800
}

.services-area .slider-button {
    margin: 0
}

.services-area .slider-button > .btn {
    height: 40px;
    line-height: 40px;
    text-transform: none;
    color: #1f2642
}

.services-area .slider-button > .btn:hover {
    color: #fff
}

.services-area .btn-text i {
    padding-left: 10px;
    font-size: 12px
}

.services-area .slider-button .btn {
    border: 1px solid #1f2642;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.services-area .slider-button .btn span {
    color: #333;
    font-weight: 400
}

.services-area .slider-button .btn:hover, .services-area .slider-button .btn:hover::before {
    background: #ff7800
}

.services-area .slider-button .btn:hover {
    border: 1px solid #ff7800
}

.services-area .slider-button .btn:hover span {
    color: #fff
}

.services-area .angle-right.white, .services-area .slider-button .btn:hover .angle-right.black {
    display: none
}

.services-area .angle-right.black {
    display: block
}

.services-area .angle-right {
    right: 15px
}

.services-area .slider-button .btn:hover .services-area .angle-right.white {
    display: block
}

.services-area .section-title .description {
    width: 60%
}

.services-area .bg-right {
    position: absolute;
    right: 20%;
    top: 20px;
    z-index: 1
}

.services-area .slider-button .btn svg.arrow-right {
    fill: #1f2642
}

.services-area .slider-button .btn:hover svg.arrow-right {
    fill: #fff
}

.about-us-img img {
    width: 100%
}

.about-us-text li {
    margin-bottom: 38px
}

.about-us-icon i {
    font-size: 24px;
    color: #096bd8;
    position: relative;
    top: 4px
}

.about-us-content {
    overflow: hidden
}

.about-us-content h3 {
    font-size: 20px;
    font-weight: 700;
    color: #1f2642;
    margin: 15px 0
}

.about-us-content p {
    margin-bottom: 0;
    margin-top: 0
}

.about-area {
    background-repeat: no-repeat;
    background-position: right;
    background-size: contain;
    background-color: #f9f9fb
}

.about-area .description-services {
    text-align: left;
    color: #1f2642
}

.about-us-wrapper .about-us-text, .about-us-wrapper .section-title {
    margin-right: 60px
}

.about-area ul li {
    width: 50%;
    float: left;
    padding-right: 20px
}

.about-area ul {
    float: left
}

.about-area .about-us-icon {
    min-height: 42px
}

.about-right {
    margin-top: 5px
}

.about-us-img.zindex-1 {
    position: absolute;
    bottom: -65px;
    right: 0;
    z-index: 1
}

.about-us-img.zindex-2 {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 3
}

.about-us-img.zindex-3 {
    position: absolute;
    bottom: -130px;
    left: -30px;
    z-index: 3
}

.blog-area {
    background-color: #f9f9fb
}

.blog-meta {
    margin-top: 20px;
    margin-bottom: 14px
}
.blog-img img {
    image-rendering: -moz-crisp-edges;         /* Firefox */
    image-rendering:   -o-crisp-edges;         /* Opera */
    image-rendering: -webkit-optimize-contrast;/* Webkit (non-standard naming) */
    image-rendering: crisp-edges;
    -ms-interpolation-mode: nearest-neighbor;  /* IE (non-standard property) */
}

/*.blog-area .blog-img img{height:210px}*/
.blog-text .sub-title, .blog-text h3 {
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: 50px;
    display: -webkit-box
}

.blog-text h3 {
    font-weight: 700;
    font-size: 18px;
    color: #1f2642
}

.blog-area .slider-button {
    margin-top: 60px;
    text-align: center
}

.blog-area .blog-text h3:hover {
    color: #ff7800
}

.blog-area .angle-right {
    display: block;
    right: -15px
}

.blog-area .section-title .description {
    display: block;
    width: 80%;
    margin: 0 auto 50px auto
}

.blog-text-head {
    margin-top: 17px;
    margin-bottom: 7px;
    font-weight: 700;
    color: #777c8c;
}

.blog-text h3 {
    font-size: 20px;
    margin-bottom: 8px;
    margin-top: 0
}

.blog-meta {
    margin: 0;
    color: #1f2642
}

.customer-home-area .customer-home-headline{
    margin-right:  25px;
}

.customer-home-area .customer-home-headline h3 {
    margin-top: 0
}

.customer-home-area .customer-home-headline .description {
    width: 60%;
    margin: 0 auto
}

.customer-home-area .viewmore {
    font-weight: 700;
    color: #ff7800;
    margin-top: 10px;
    font-size: 20px
}

.customer-home-item {
    text-align: center;
    min-height: 145px;
    border-radius: 10px;
    border: 1px solid #e8e8e8;
    background-color: #fff;
}

.customer-home-area .mr-0 {
    margin-right: 0
}

.customer-home-item img {
    max-width: 100%;
    -webkit-transition: .5s;
    -o-transition: .5s;
    transition: .5s;
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%);
      padding: 15px;
}

.customer-home-slider-item-desc {
    color: #fff;
    padding: 105px 60px 140px 60px;
    height: 531px
}

.customer-home-slider-item-desc h3 {
    font-weight: 700;
    font-size: 24px
}

.customer-home-slider-item-desc p {
    opacity: .8
}

.customer-home-slider-item-desc > p {
    margin: 40px 0
}

.customer-home-slider-item-desc .customer-product p {
    opacity: 1
}

.customer-home-product-items img {
    width: 33px;
    height: 33px
}

.customer-home-slider-list {
    overflow: hidden
}

.customer-home-slider-item {
    display: flex !important;
    margin: 0 15px;
    outline: 0
}

.slick-list {
    max-width: 1140px;
    margin: auto !important;
    overflow: visible !important
}

.slick-list.draggable {
    padding: 0 !important
}

.customer-home-item img:hover {
    -webkit-filter: grayscale(0);
    filter: grayscale(0)
}

.customer-home-slider-item-desc {
    background: linear-gradient(90deg, #ff983c 0, #ff7800 100%);
    border-radius: 0 20px 20px 0
}

.customer-home-slider .customer-home-slider-item .customer-home-slider-item-image {
    display: inline-block;
    margin-right: 15px
}

.customer-home-slider .customer-home-slider-item .customer-home-slider-item-image:last-child {
    margin-right: 0
}

.customer-home-slider {
    background: linear-gradient(180deg, #f9f9f9 0, #f9f9f9 100%);
    padding-top: 50px;
    position: relative
}

.slick-dots {
    bottom: 60px !important
}

.slick-dots li {
    background: unset !important;
    margin: 0 !important
}

.slick-dots li button:before {
    font-size: 12px !important
}

.slick-dots li.slick-active button:before {
    color: #ff7800 !important;
    opacity: 1 !important
}

.slick-dots li button:focus, .slick-dots li button:focus:before, .slick-dots li button:hover, .slick-dots li button:hover:before {
    color: #ff7800 !important
}

.customer-home-slider .paginator-center {
    position: absolute;
    bottom: 70px;
    left: 50%;
    transform: translateX(-45%)
}

.customer-home-slider .paginator-center li {
    display: inline-block !important;
    cursor: pointer
}

.customer-home-slider .paginator-center li svg:hover path {
    color: #ff7800
}

.customer-home-slider .slick-arrow {
    padding: 0 5px
}


customer-area .customer-headline {
    margin-bottom: 40px
}

.customer-area .customer-headline h3 {
    margin-top: 0
}

.customer-area .customer-headline .description {
    width: 60%;
    margin: 0 auto
}

.customer-area .viewmore {
    font-weight: 700;
    color: #ff7800;
    margin-top: 10px;
    font-size: 20px
}

.customer-list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.customer-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 18.8% !important;
    flex: 0 0 18.8% !important;
    max-width: 18.8%;
    text-align: center;
    padding: 15px;
    min-height: 95px;
    border-radius: 10px;
    border: 1px solid #e8e8e8;
    background-color: #fff;
    margin-right: 1.5%;
    margin-bottom: 1.5%
}

.customer-area .mr-0 {
    margin-right: 0
}

.customer-item img {
    max-height: 120px;
    max-width: 100%;
    -webkit-transition: .5s;
    -o-transition: .5s;
    transition: .5s;
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%)
}

.customer-slider-item-desc {
    color: #fff;
    padding: 105px 60px 140px 60px;
    height: 531px
}

.customer-slider-item-desc h3 {
    font-weight: 700;
    font-size: 24px
}

.customer-slider-item-desc p {
    opacity: .8
}

.customer-slider-item-desc > p {
    margin: 40px 0
}

.customer-slider-item-desc .customer-product p {
    opacity: 1
}

.customer-product-items img {
    width: 33px;
    height: 33px
}

.customer-slider-list {
    overflow: hidden
}

.customer-slider-item {
    display: flex !important;
    margin: 0 15px;
    outline: 0
}

.slick-list {
    max-width: 1140px;
    margin: auto !important;
    overflow: visible !important
}

.slick-list.draggable {
    padding: 0 !important
}

.customer-item img:hover {
    -webkit-filter: grayscale(0);
    filter: grayscale(0)
}

.customer-slider-item-desc {
    background: linear-gradient(90deg, #ff983c 0, #ff7800 100%);
    border-radius: 0 20px 20px 0
}

.customer-slider .customer-slider-item .customer-slider-item-image {
    display: inline-block;
    margin-right: 15px
}

.customer-slider .customer-slider-item .customer-slider-item-image:last-child {
    margin-right: 0
}

.customer-slider {
    background: linear-gradient(180deg, #f9f9f9 0, #f9f9f9 100%);
    padding-top: 50px;
    position: relative
}

.map-area .description {
    width: 60%;
    display: block;
    margin: 0 auto
}

.map-image {
    padding: 70px 0 55px 0;
    position: relative
}

.map-area .map-box-item {
    margin-bottom: 30px
}

.map-area .map-box-item .text-zozo {
    margin-bottom: 0;
    color: #ff7800;
    font-size: 32px;
    margin-top: 10px;
    font-weight: bold;
}

.map-area .map-box-item p {
    margin-top: 0
}

.map-image img {
    max-width: 100%
}

.four-column .box-left {
    float: left
}

.four-column .box-left img {
    width: 40px;
    height: 40px;
    padding-top: 6px
}

.four-column .box-right {
    padding-left: 55px
}

.four-column .box-right .head-text p {
    font-weight: 700;
    font-size: 18px;
    margin-bottom: 10px;
    margin-top: 0
}

.four-column .box-right .bottom-text p {
    margin-bottom: 10px;
    margin-top: 0
}

.map-image .blob {
    position: absolute
}

.map-image .blob:nth-child(2) {
    top: 25%;
    left: 7%
}

.map-image .blob:nth-child(3) {
    top: 35%;
    left: 10%
}

.map-image .blob:nth-child(4) {
    top: 45%;
    left: 20%
}

.map-image .blob:nth-child(5) {
    top: 25%;
    left: 45%
}

.map-image .blob:nth-child(6) {
    top: 30%;
    right: 15%
}

.map-image .blob:nth-child(7) {
    top: 35%;
    right: 12%
}

.map-image .blob:nth-child(8) {
    top: 40%;
    right: 20%
}

.map-image .blob:nth-child(9) {
    top: 50%;
    right: 20%
}

.map-image .blob:nth-child(10) {
    top: 58%;
    right: 22%
}

.map-image .blob:nth-child(11) {
    top: 75%;
    right: 12%
}

.business-platform-area {
    background: #ff7800;
    background-size: cover;
    color: #fff;
    text-align: center;
    padding-right: 300px;
    padding-left: 300px;
    position: relative
}

.business-platform-area {
    background-image: url(../images/bg/city.png)
}

.business-platform-area .wrapper-content {
    position: relative;
    z-index: 2
}

.business-platform-area p.headline {
    color: #fff;
    letter-spacing: -.03em;
    font-weight: 700
}

.business-platform-area p.description {
    color: #fff;
    margin-bottom: 0
}

.business-platform-area .slider-button {
    margin: 0
}

.business-platform-area .angle-right {
    top: 44%;
    -webkit-transform: translateY(-44%);
    -ms-transform: translateY(-44%);
    transform: translateY(-44%)
}

.business-platform-area p.headline {
    width: 50%;
    margin: 0 auto 30px auto
}

footer a:hover {
    color: #fff
}

.footer-area {
    background: #1b1e2d;
    color: #fff
}

.footer-logo {
    margin-bottom: 29px
}

.footer-text p {
    margin-bottom: 0
}

.footer-icon {
    margin-top: 22px
}

.footer-icon a {
    color: #cbcbcb;
    height: 40px;
    width: 40px;
    display: inline-block;
    text-align: center;
    border-radius: 50%;
    margin-right: 5px;
    -webkit-transition: .3s;
    -o-transition: .3s;
    transition: .3s;
    border: 1px solid #cbcbcb
}

.footer-icon a:hover {
    background: #096bd8;
    color: #fff;
    border-color: #096bd8
}

.copyright p {
    margin-bottom: 0
}

.footer-top-area .list-footer-icon li:first-child {
    padding-left: 0
}

.footer-top-area .list-footer-icon li {
    padding-left: 10px
}

.footer-area p, .footer-area span {
    color: #9f9f9f;
    font-weight: 500
}

.footer-wrapper .title-footer {
    margin-bottom: 15px;
    color: #fff;
    font-weight: 700;
    text-transform: capitalize
}

.footer-wrapper .list-footer p, .footer-wrapper .list-footer h3 {
    color: #9d9d9d;
    margin-bottom: 8px;
    margin-top: 0;
    padding: 0;
    font-size: 16px;
    font-weight: normal;
}

.footer-wrapper .list-footer .ios-android {
    border: 1px solid grey;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 10px;
    display: inline-block;
    padding: 5px 15px;
    margin-bottom: 10px;
    min-width: 130px
}

.footer-wrapper .list-footer li:nth-child(2) .ios-android {
    margin-bottom: 0
}

.footer-wrapper .list-footer .ios-android .name-ios-android {
    padding-left: 5px;
    color: grey;
    font-size: 13px
}

.footer-wrapper .list-footer .languague-footer {
    margin-top: 10px;
    font-size: 14px
}

.footer-wrapper .list-footer .languague-footer span {
    padding-left: 5px
}

.footer-top-area .list-footer-icon img {
    width: 30px;
    height: 30px
}

.footer-middle-area {
    border-top: 1px solid rgba(187, 187, 187, .1);
    border-bottom: 1px solid rgba(187, 187, 187, .1);
    padding: 42px 0 35px 0
}

.footer-middle-area span {
    padding-left: 7px;
    color: #9d9d9d
}

.footer-middle-area .footer-wrapper .list-footer li:not(:last-child) {
    margin-bottom: 8px
}

.footer-middle-area .title-footer {
    margin-bottom: 24px
}

.footer-middle-area .list-footer li img {
    float: left;
    padding-top: 4px
}

.footer-middle-area .list-footer li p {
    padding-left: 27px
}

.footer-bottom-area .copyright p:first-child {
    font-weight: 700;
    margin-bottom: 13px
}

.list-footer-icon li {
    display: inline-block;
    padding-left: 9px
}

.footer-bottom-area {
    background: #1b1e2d
}

.footer-bottom-area p {
    color: #9d9d9d;
    font-size: 10px
}

.reserved {
    padding-top: 20px;
    margin: 0;
    text-transform: none
}

.trial .trial-left {
    background: url(../images/bg/bg-trial.png);
    background-size: cover;
    min-height: 100vh
}

.trial-left .logo {
    text-align: center
}

.trial-right .form {
    background: #fff;
    padding: 20px 200px 65px 90px
}

.trial-right .form-title {
    font-weight: 700;
    font-size: 24px;
    color: #000;
    margin-bottom: 7px
}

.trial-right .form-description {
    font-weight: 400;
    color: #1f2642;
    margin-bottom: 30px
}

.trial-right form .suggest {
    margin-top: 8px
}

.trial-right form .suggest span {
    font-weight: 700
}

.trial-right form .form-group:not(:last-child) {
    margin-bottom: 20px
}

.trial-right .form .slider-button {
    margin-bottom: 35px
}

.form-control:focus {
    border-color: rgba(255, 120, 0, .5)
}

.trial-right .form .has-account {
    color: #666;
    font-size: 11px;
    display: block;
    margin: 0
}

.trial {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.trial-left {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 40%;
    flex: 0 0 40%;
    max-width: 40%;
    padding: 40px 65px 0 150px
}

.trial-left .logo {
    text-align: left;
    padding-bottom: 80px
}

.trial-right {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 60%;
    flex: 0 0 60%;
    max-width: 60%
}

.trial-title {
    color: #1f2642;
    font-weight: 700;
    font-size: 18px;
    padding-bottom: 15px
}

.trial-desc {
    color: #696969
}

.trial form button[type=submit] {
    margin-top: 20px;
    margin-bottom: 35px
}

.trial-right .dropdown-category-icon {
    float: right;
    margin-top: -30px;
    position: relative;
    right: 10px
}

.trial-right .sub-category {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    z-index: 1000;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    padding: .5rem .75rem;
    display: none
}

.trial-right .sub-category li:not(:last-child) {
    padding-bottom: 10px
}

.trial-right .sub-category.show {
    display: block
}

@media (max-width:767px) {
    .trial-right {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .trial-right .form {
        padding: 0 15px 50px 15px
    }

    .trial-right .form-description, .trial-right .form-title {
        text-align: center
    }

    .trial-right .form-description {
        margin-bottom: 20px
    }

    .trial-right .form-title {
        margin-top: 0
    }
}

.packages-page .section-title .description {
    overflow: hidden;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box !important
}

.head-packages ul.nav-tabs {
    border: none;
    width: 100%
}

.head-packages ul li {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33%;
    flex: 0 0 33%;
    max-width: 33%
}

.head-packages ul li span {
    display: block
}

.head-packages ul li .nav-link {
    border: none;
    border-radius: 0;
    padding: 20px 10px;
    background: #f6f6f6;
    opacity: .9;
    border: 1px solid #d9ddea;
    border-right: none
}

.head-packages ul.nav-tabs li:last-child .nav-link {
    border-right: 1px solid #d9ddea
}

.nav-tabs .nav-link.active, .nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {
    border-color: #d9ddea
}

.head-packages ul li .nav-link.active {
    opacity: 1;
    position: relative;
    -webkit-box-shadow: 0 0 44px rgba(144, 151, 179, .19);
    box-shadow: 0 0 44px rgba(144, 151, 179, .19)
}

.head-packages ul li .nav-link .first-name {
    font-weight: 400;
    font-size: 20px;
    margin-bottom: 5px
}

.head-packages ul li .nav-link.active .first-name {
    font-weight: 700
}

.head-packages ul li .nav-link.active .first-name {
    color: #ff7800
}

.head-packages ul li .nav-link.active:before {
    content: "";
    position: absolute;
    top: -5px;
    left: 0;
    width: 100%;
    height: 5px;
    background: #ff7800
}

.head-packages ul li .nav-link span:first-child {
    font-size: 20px;
    padding-bottom: 5px
}

.pack-common .btn, .packages-item .btn {
    height: 40px;
    line-height: 40px
}

.package-gift {
    -webkit-line-clamp: 3;
    min-height: 60px
}

.package-header {
    position: relative
}

.packages-content-desktop > .row:first-child .pack-common .pack-item:last-child, .packages-content-desktop > .row:first-child .packages-table .pack-item:last-child, .packages-content-desktop > .row:last-child .pack-common .pack-item:last-child, .packages-content-desktop > .row:last-child .packages-table .pack-item:last-child {
    border-bottom: 1px solid rgba(132, 137, 145, .2)
}

.half-fullwidth {
    width: 50%;
    float: right
}

.package-name {
    font-size: 20px
}

.package-price p:nth-child(1) {
    font-size: 35px;
    position: relative;
    margin: 0;
    display: inline-block;
}

.packages-page .package-price p.package-year {
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    opacity: .5
}

.currency {
    position: relative;
    top: -8px;
    font-size: 16px;
    font-weight: 400
}

.package-header .btn {
    height: 40px;
    line-height: 40px
}

.pack-item {
    position: relative;
    width: 100%;
    min-height: 30px;
    /*overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;*/
    margin: 0 0 5px 0 !important;
    padding: 0 20px
}

.pack-item .close {
    top: 30px;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    cursor: unset;
    color: #1f2642
}

.close:after, .close:before {
    width: 2px;
    height: 11px
}

.pack-item .close:hover {
    color: #1f2642;
    opacity: 1
}

.pack-item .circle {
    width: 20px;
    height: 20px;
    line-height: 20px;
    background: #ff7800;
    display: inline-block;
    border-radius: 100%
}

.packages-main .packages-item {
    background: #f6f6f6;
    border: 1px solid #e9e9e9;
    box-sizing: border-box;
    border-radius: 6px;
    padding: 30px 0;
    height: 100%;
    box-shadow: 4px 6px 10px rgba(0, 0, 0, 0.05);
}

.packages-item .btn {
    background: #fff;
    border: 1px solid #ff7800;
    border-radius: 4px;
    display: block;
    margin: 30px 15px;
    color: #ff7800
}

.packages-hot .btn, .packages-item .btn:hover {
    background: #ff7800;
    color: #fff
}

.packages-hot .btn:hover {
    background: #fff;
    color: #ff7800
}

.packages-main .packages-item.packages-hot {
    box-shadow: 0 5px 20px rgba(144, 151, 179, .15);
    border-radius: 0 0 6px 6px
}

.packages-hot .package-header:before {
    background: #ff7800
}

.icon-crown {
    position: absolute;
    top: -15px
}

.package-header {
    padding: 0 0 30px 0
}

.packages-desktop ul li a:hover {
    color: unset
}

#accordion-pack {
    margin-top: 40px
}

#accordion-pack .card, #accordion-pack .card-header {
    background: 0 0;
    border: none;
    padding: 0
}

#accordion-pack .card {
    border-bottom: 1px solid rgba(132, 137, 145, .2);
    padding: 20px 0
}

#accordion-pack .card-header {
    position: relative
}

#accordion-pack .card-header h2 {
    margin: 0;
    padding-right: 80px
}

#accordion-pack .card-header .plus-minus {
    position: absolute;
    top: 10px;
    right: 0
}

#accordion-pack .card-header a:active {
    color: unset
}

#accordion-pack .card-header a:hover {
    color: unset
}

.head-packages .nav-tabs .nav-link img {
    width: 55px;
    height: 55px;
    float: left
}

.img-pack img {
    width: 55px;
    height: 55px
}

.head-packages .nav-tabs .nav-link span {
    text-align: center
}

.head-packages .nav-tabs .nav-link span:nth-child(3) {
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: 30px;
    display: -webkit-box;
    padding-left: 15px
}
.hot-deal {
    color: #fff;
    font-weight: 700;
    position: absolute;
    top: -69px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    text-transform: uppercase;
    width: 100%;
    padding: 12px 0;
    background: #ff7800;
    border-radius: 6px 6px 0 0
}

.choose-package-area {
    background: #fff;
    position: unset
}

.choose-package-area p.description, .choose-package-area p.headline {
    color: #1f2642
}

.choose-package-area p.headline {
    margin: 0 auto 10px auto
}

.choose-package-area p.description {
    margin-top: 0
}

.packages-page .choose-package-area {
    padding-left: 0;
    padding-right: 0
}

.pack-item .checkmark {
    border-color: #25b900;
    margin-right: 15px;
    float: left;
    margin-top: 3px;
    width: 7px;
}

.pack-item .text-item {
    width: calc(100% - 15px);
}

.features-name {
    font-size: 20px;
    margin-bottom: 45px
}

.account-page .main-menu-area, .contact-page .main-menu-area, .packages-page .main-menu-area {
    border-bottom: 2px solid #f6f6f6
}

.packages-content {
    background: #f6f6f6;
    padding-bottom: 45px
}

.packages-content .tab-content {
    padding-top: 25px;
}

.background-packages-page {
    background: #fff;
    border: 1px solid #e3e6f1;
    box-sizing: border-box;
    border-radius: 6px
}

.question-packages {
    background: #f6f6f6
}

.business-platform-area .headline, .question-packages .headline {
    margin-top: 0
}

.box-features .pack-item {
    line-height: inherit;
    -webkit-line-clamp: 2
}

.packages-content .packages-item img {
    width: auto !important
}

.package-special .package__old-price {
    text-decoration: line-through;
    margin-right: 5px;
    font-size: 14px;
    color: #969696
}

.package-special .package__discount-percent {
    background: rgb(255 120 0 / 20%);
    color: #ff7800;
    padding: 5px 10px;
    border-radius: 50px;
    font-size: 12px;
    font-weight: 700
}

.package-special {
    min-height: 30px
}

/* Combo */
.section.feature .container .container-content, .group .container .container-content {
    max-width: 950px;
    margin: 0 auto;
}

.combo {
    background-color: #f6f6f6;
}

.combo .main-title {
    margin-top: 0;
}

.combo .headline {
    padding-top: 70px;
}

.combo .combo-item {
    background: #FFFFFF;
    padding: 20px 10px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.combo .combo-item ul {
    padding-left: 15px;
    min-height: 88px;
}

.combo .combo-item:hover {
    background: linear-gradient(93.07deg, #ff7800 6.43%, #ff8c25 95.29%);
}

.combo .combo-item .price {
    border: 1px solid #ff8c25;
}

.combo .combo-item:hover h3, .combo .combo-item:hover .price {
    color: #fff;
}

.combo .combo-item:hover .price {
    background: #fff;
    color: #ff7800;
}

.combo .combo-item {
    margin-top: 0;
}

.combo .combo-item h3 {
    font-weight: bold;
    font-size: 16px;
    line-height: 19px;
    color: #ff7800;
    text-align: center;
    text-transform: uppercase;
}

.combo .combo-item ul li {
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 22px;
    text-align: left;
}

.combo .combo-item .price {
    width: 100% !important;
    height: 34px;
    background: linear-gradient(93.07deg, #ff7800 6.43%, #ff8c25 95.29%);
    border-radius: 4px;
    position: relative;
    z-index: 10;
    border: 1px solid #f17303;
}

.combo .combo-item .price-old {
    font-style: normal;
    font-weight: normal;
    font-size: 13px;
    line-height: 14px;
    margin-top: 5px;
}

.combo .combo-item .overlay {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    right: 0;
    bottom: 0;
    background: url(../images/card/pattern_gra.png) 0 0 no-repeat;
    background-position: top left;
    transform: rotate(180deg);
    z-index: 1;
}

.combo .combo-item .add-to-cart {
    position: absolute;
    display: none;
    top: 10px;
    right: 10px;
    width: 26px;
    height: 26px;
    background: url(../images/icon/plus.png) 0 0 no-repeat;
    position: absolute;
    z-index: 10;
}

.combo .combo-item:hover .overlay {
    background: url(../images/card/pattern.png) 0 0 no-repeat;
    background-position: top left;
    transform: rotate(180deg);
}

.combo .combo-item:hover .add-to-cart {
    display: block;
}

@media screen and (min-width:0\0) {
    .main-menu nav > ul > li:first-child.has-child, .no-position {
        position: static
    }

    .slider-thumb {
        min-height: 393px !important
    }

}

@media (min-width:991px) {
    [class*=max-width-] {
        margin: 0 auto
    }

    .max-width-50 {
        max-width: 50%
    }

    .max-width-60 {
        max-width: 60%
    }

    .max-width-70 {
        max-width: 70%
    }

    .max-width-80 {
        max-width: 80%
    }

    .max-width-90 {
        max-width: 90%
    }

}

.section-title__title {
    margin-top: 0;
    margin-bottom: 12px
}

.section-title__description {
    margin-top: 0;
    margin-bottom: 0
}

.theme-page .section-title h2.headline {
    font-size: 24px
}

.theme-page .section-title .description {
    font-size: 18px;
    color: #7b7a81
}

.theme-page .wrapper-mainbanner {
    background: linear-gradient(90deg, #ff983c 0, #ff7800 100%)
}

.theme-page .wrapper-mainbanner .section-title h1 {
    color: #fff;
    font-size: 36px
}

.theme-page .wrapper-mainbanner .section-title .description {
    color: #fff;
    font-size: 16px
}

.wrapper-mainbanner.oceans .description, .wrapper-mainbanner.oceans .headline {
    color: #fff !important
}

.theme-page.products-page .slider-area:before {
    display: none
}

.theme-detail-page .extra-header .logo a span, .theme-detail-page .extra-header .menu ul li a, .web-page .extra-header .logo a span, .web-page .extra-header .menu ul li a {
    /*color: #fff !important;*/
    font-weight: 400 !important
}

.theme-detail-page .extra-header .logo a:after, .web-page .extra-header .logo a:after {
    background: #fff !important
}

.web-page .extra-header.sticky .logo a:after {
    background: #42464e !important
}

.extra-header.sticky .logo a:after {
    right: -10px
}

.theme-detail-page .extra-header .menu ul li.active a, .web-page .extra-header .menu ul li.active a {
    font-weight: 700 !important
}

.theme-detail-page .extra-header .menu ul li a:hover, .web-page .extra-header .menu ul li a:hover {
    /*color: #fff*/
}

.theme-detail-page .extra-header .menu ul li a, .web-page .extra-header .menu ul li a {
    position: relative
}

.theme-detail-page .extra-header .menu ul li a:before, .web-page .extra-header .menu ul li a:before {
    content: "";
    position: absolute;
    width: 0;
    height: 1px;
    background: #fff;
    bottom: 0;
    transition: all .5s
}

.theme-detail-page .extra-header .menu ul li a:hover:before, .web-page .extra-header .menu ul li a:hover:before {
    width: 100%
}

.theme-page .section-title .description {
    margin-bottom: 25px;
    opacity: .9
}

.theme-page .section-title .input-group {
    height: 52px;
    border-radius: 6px 0 0 6px;
    filter: drop-shadow(0 4px 4px rgba(0, 0, 0, .12))
}

.theme-page .premium-theme {
    padding-bottom: 0
}
.premium-theme .container {
    max-width: 990px;
}
.theme-page .section-title .input-group input {
    border: none;
    padding: 0 25px
}

.theme-page .section-title .input-group input::placeholder {
    font-size: 16px;
    color: #1f2642;
    opacity: .7
}

.theme-page .section-title .input-group button {
    width: 89px;
    height: 52px;
    background: #78c145;
    border-radius: 0 6px 6px 0;
    border: none;
    cursor: pointer
}

.theme-page .section-title .input-group button img {
    width: auto
}

.themes-list .image img {
    min-height: 325px
}
.themes-list .page-nav {
    padding-top: 0;
}
@media (min-width:991px) {
    .theme-page .section-title .input-group {
        width: 617px;
        margin: 0 auto
    }

    .theme-page .premium-box {
        margin-bottom: 80px
    }
}

.website-technology {
    padding: 0;
}

.technology-item {
    margin-bottom: 30px
}

.premium-theme .theme-item {
    position: relative
}

.premium-theme .button-action .view-theme {
    margin-right: 12px
}

.premium-theme .owl-next i, .premium-theme .owl-prev i {
    left: 5px !important;
    top: 2px !important
}

.premium-theme .owl-next i {
    left: 1px !important
}

.premium-box .owl-carousel {
    z-index: unset
}

.theme-overlay .button {
    font-weight: 600
}

.type-theme {
    color: #8F8F8F;
}
.content-theme {
    padding-top: 50px;
}
.name-theme {
    color: #1f2642;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 0;
}
.price-theme {
    font-size: 20px;
    font-weight: 600;
}
.theme-page .price-theme {
    color: #ff7800;
}

.content-theme .price-box .price-theme:not(.special-theme){
  color: #8F8F8F;
  text-decoration: line-through;
  font-size: 14px;
  transform: translateY(3px);
}

.content-theme .price-box .special-theme{
  margin-right: 10px;
  font-size: 25px;
  font-weight: 600;
}

.description-theme {
    opacity: .7
}

.button-action .view-detail {
    border-color: #1f2642
}

.button-action .view-detail:hover {
    background: #1f2642;
    color: #fff
}
.premium-theme .owl-next, .premium-theme .owl-prev {
    border: 1px solid #444;
    box-sizing: border-box;
    border-radius: 5px;
    padding: 5px 12px;
    display: inline-block;
}

.premium-theme .owl-carousel .owl-nav .owl-prev {
    margin-right: 10px;
    left: -65px;
}
.premium-theme .owl-carousel .owl-nav .owl-next {
    margin-right: 10px;
    right: -65px;
}

.premium-theme .owl-next i, .premium-theme .owl-prev i {
    position: relative;
    left: 2px
}

.premium-theme .owl-next i {
    left: unset;
    right: 2px
}

.premium-theme .owl-next.active, .premium-theme .owl-prev.active {
    border-color: #ff7800
}

.premium-theme .owl-prev:hover i, .premium-theme .owl-next:hover i, .premium-theme .owl-next.active i, .premium-theme .owl-prev.active i {
    border-color: #ff7800
}

.section-theme-menu-bar.new-version {
    background: #fff !important;
    border-bottom: 1px solid #e7e7f3;
    border-top: 1px solid #e7e7f3
}

.section.features-info hr {
    display: none
}

.section.features-info .section-title {
    margin: 0
}

.section-theme-menu-bar.new-version .checkbox-free input {
    margin-right: 9px
}

.section-theme-menu-bar.new-version .checkbox-free label {
    color: inherit
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu {
    background: #fff;
    padding: 20px;
    border: 1px solid #e7e7f3;
    box-sizing: border-box;
    box-shadow: 0 20px 30px rgba(0, 0, 0, .1)
}

.section-theme-menu-bar.new-version .container > ul li:nth-child(2) .sub-menu {
    min-width: 720px
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu ul li img {
    width: 100%
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu .ul-career {
    flex: 0 0 30%;
    max-width: 30%;
    border-right: 1px solid #e7e7f3;
    padding-left: 35px
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu .ul-career:first-child {
    padding-left: 0
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu .ul-career:nth-child(2) {
    flex: 0 0 40%;
    max-width: 40%
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu .ul-career:last-child {
    border-right: none
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu ul > li {
    border-bottom: none;
    margin: 0
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu .ul-career > li {
    min-width: unset;
    display: block;
    float: unset
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu .ul-career > li > a {
    color: inherit;
    padding: 0 0 10px 0
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu .ul-career > li:last-child > a {
    padding: 0
}

.section-theme-menu-bar.new-version .container > ul > li:hover > .sub-menu {
    display: flex;
    flex-wrap: wrap
}

.section-theme-menu-bar input[type=radio]:checked ~ p.text-center {
    color: #ff7800;
    font-weight: 700
}

.section-theme-menu-bar .ul-career li a:hover {
    text-decoration: underline
}

.section-theme-menu-bar .ul-common .ul-career a {
    font-weight: 400 !important
}

.section-theme-menu-bar .container > ul > li.active > a, .section-theme-menu-bar .container > ul > li:hover > a {
    background: unset !important;
    color: #ff7800 !important;
}

.section-theme-menu-bar ul li:hover .arrow {
    border-color: #1f2642 !important
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu ul {
    width: 100%;
    float: unset
}

.section-theme-menu-bar.new-version .container > ul li .sub-menu ul > li {
    min-width: unset
}

.section-theme-menu-bar.new-version .style-design p:first-child {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 20px
}

.section-theme-menu-bar.new-version .style-design p:first-child a {
    color: inherit;
    font-weight: 700;
    font-size: 16px;
    text-align: left
}

.section-theme-menu-bar.new-version .style-design li a {
    font-size: 12px;
    text-align: center;
    color: inherit !important;
    margin: 0 auto !important
}

.section-theme-menu-bar.new-version .style-design {
    display: flex;
    flex-wrap: wrap;
    width: 100%
}

.section-theme-menu-bar.new-version .style-design li {
    flex: 0 0 33.3333333%;
    max-width: 33.3333333%;
    padding: 0 10px !important
}

@media(min-width:991px) {
    .section-theme-menu-bar .ul-career li:hover label {
        text-decoration: underline
    }

    .section-theme-menu-bar.new-version .layout-design {
        position: unset
    }

    .section-theme-menu-bar.new-version .layout-design .sub-menu {
        width: 100%;
        padding: 0 !important;
        border: none !important
    }

    .section-theme-menu-bar.new-version .layout-design .style-design {
        border-top: 1px solid #ececec
    }

    .section-theme-menu-bar.new-version .style-design li {
        flex: 0 0 20%;
        max-width: 20%;
        padding: 20px 50px !important;
        border-right: 1px solid #ececec;
        border-bottom: 1px solid #ececec !important
    }

    .section-theme-menu-bar.new-version .style-design li p {
        font-weight: 700
    }

}

@media(max-width:767px) {
    .choose-theme-page .themes-list {
        padding-top: 20px
    }

}

.themes-item {
    box-shadow: 0 2px 30px rgba(31, 38, 66, .1);
    border-radius: 5px;
    border: none;
    margin-bottom: 35px
}

.theme-overlay .button:first-child {
    color: #fff
}

.theme-overlay .button-action .view-detail {
    color: #1f2642;
    border-color: #fff
}

.theme-overlay .button-action .view-detail:hover {
    color: #fff;
    border-color: #1f2642
}

.themes-item .overlay .view-theme:hover {
    color: #78c145;
    border-color: #fff
}

.theme-spec .category, .themes-item .name .category {
    color: #7b7a81
}

.themes-item .price {
    font-weight: 700
}

.themes-item .price.price-free {
    color: #78c145
}

.catalogs-index .section-title .headline, .design-style .section-title .headline {
    font-size: 24px
}

.design-style .design-style__box {
    padding: 20px 30px;
    display: flex;
    flex-wrap: wrap;
    color: #fff;
    align-items: center;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    border-radius: 5px
}

.design-style .design-style__box.basic-design-bg, .design-style .design-style__box.normal-design-bg, .design-style .design-style__box.onecolumn-design-bg {
    background-image: url(../images/bg/basic_design_bg.svg);
    background-repeat: no-repeat;
    background-size: cover
}

.design-style .design-style__box.normal-design-bg {
    background-image: url(../images/bg/normal_design_bg.svg)
}

.design-style .design-style__box.onecolumn-design-bg {
    background-image: url(../images/bg/onecolumn_design_bg.svg)
}

.design-style__image {
    margin-right: 25px
}

.theme-page .features-info {
    padding-top: 0
}

.themes-list {
    background: #f9f9fb
}

.themes-list .tech-list {
    display: inline-block;
    margin-top: 5px;
    margin-bottom: 15px;
}

ul.tech-list li {
    width: 32px;
    height: 32px;
    display: inline-block;
    margin-right: 0px
}
ul.tech-list li img {
    opacity: 0.3;
}
ul.tech-list li.active img {
    opacity: 1;
}

.label-pre-order {
    position: absolute;
    z-index: 9;
    top: 5px;
    right: 5px;
    background: #ffaa17;
    padding: 3px 5px;
    font-size: 13px;
    color: white;
    border-radius: 3px;
}

.themes-list .price {
    float: right;
    margin-right: 15px;
    margin-top: 3px;
    font-size: 14px
}

.themes-list .price {
    float: right;
    margin-right: 15px;
    margin-top: 3px;
    font-size: 14px
}

.ul-common .ck-button label {
    width: 100%;
    color: #1f2642;
    margin: 0 0 10px 0
}

.ul-common .ck-button label p {
    font-size: 14px;
    padding-top: 10px
}

.ul-common .ck-button input:checked .text-align {
    color: #ff7800
}

.form-advisory .form-requirement {
    text-align: left !important;
    margin: 0 auto !important;
    padding-top: 0
}

.form-advisory .form-requirement p span {
    display: inline-block
}

.form-advisory .form-requirement {
    background: unset;
    margin: 0
}

.form-advisory .form-requirement .form-requirement__title {
    display: none
}

.form-advisory .form-requirement button[type=submit]:hover {
    background: #fff;
    color: #fa8300
}

.form-advisory .form-requirement input[type=text]:focus, .form-requirement textarea:focus {
    border-color: #ff7800
}

.form-advisory .form-requirement #features button[type=submit] {
    background: #ff7800;
    border-color: #ff7800
}

.form-advisory .form-requirement #features button[type=submit]:hover {
    background: #fff;
    color: #ff7800
}

.form-advisory .form-requirement .ui-slider-handle, .form-advisory .form-requirement .ui-slider-range {
    background: #ff7800
}

.form-advisory .form-requirement p.mt-0 {
    color: #ff7800 !important
}

.form-advisory .form-requirement p.mt-0:hover {
    text-decoration: underline
}

.form-advisory .form-requirement .project-types__item.checked label {
    color: #ff7800
}

.form-advisory .form-requirement .check-icon:after, .form-advisory .form-requirement .project-types__item.checked, .form-advisory .form-requirement input:checked ~ .check-icon {
    border-color: #ff7800
}

.form-advisory .form-requirement .project-types__item.checked {
    background: #ffeede
}

@media (min-width:991px) {
    .theme-page .wrapper-mainbanner .section-title {
        margin-top: 67px;
        margin-bottom: 83px
    }

    .themes-list {
        padding-top: 40px
    }

    .section-theme-menu-bar.new-version .checkbox-free {
        float: right;
        height: 50px;
        line-height: 50px
    }

}

@media (max-width:767px) {
    .theme-page .logo-white {
        display: none
    }

    .theme-page .section .section-title {
        margin-bottom: 30px
    }

    .theme-page .services-text .services-name {
        padding-top: 12px
    }

    .premium-theme .content-theme {
        text-align: center
    }

    .premium-theme .content-theme .name-theme {
        margin: 0
    }

    .premium-theme .content-theme .price-theme {
        margin-top: 5px
    }

    .premium-theme .content-theme .type-theme {
        margin-bottom: 5px
    }

    .premium-theme .description-theme {
        margin-bottom: 20px
    }

    .premium-theme .button-action .view-theme {
        margin-right: 0
    }

    .theme-page .premium-theme .owl-next i, .theme-page .premium-theme .owl-prev i {
        border-width: 0 1px 1px 0;
        padding: 3px
    }

    .theme-page .premium-theme .owl-dots {
        display: none
    }

    .section-theme-menu-bar.new-version {
        background: #fff !important
    }

    .theme-page .themes-list #result {
        padding-top: 35px
    }

    .theme-page .design-style .design-style__box {
        margin-bottom: 15px
    }

    .wrapper-mainbanner .section-title {
        margin-bottom: 60px
    }

    .theme-page .section-title .input-group input {
        padding: 10px;
        padding-left: 25px
    }

    .theme-page .section-title .input-group button {
        width: 60px
    }

    .theme-page .section-title .input-group input::placeholder {
        font-size: 16px
    }

    .section-theme-menu-bar.new-version {
        padding: 8px 0
    }

    .section-theme-menu-bar.new-version:before {
        content: "";
        position: absolute;
        top: 60px;
        left: 0;
        background: #e7e7f3;
        width: 100%;
        height: 1px
    }

    .section-theme-menu-bar.new-version .input-group {
        position: relative
    }

    .section-theme-menu-bar.new-version .input-group input {
        background: #f3f3f3;
        border-radius: 6px;
        border: none;
        height: 44px;
        padding-left: 17px
    }

    .section-theme-menu-bar.new-version .input-group input::placeholder {
        color: #1f2642
    }

    .section-theme-menu-bar.new-version .input-group .input-group-append {
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 999;
        height: 44px;
        line-height: 44px
    }

    .section-theme-menu-bar.new-version .input-group .input-group-append button {
        background: unset;
        border: none
    }

}

.page-nav {
    text-align: center;
    margin: 0;
    padding: 30px 0;
    display: flex;
    align-items: center;
    justify-content: center
}

.page-nav .page-numbers {
    text-decoration: none;
    border: 1px solid #bfbfbf;
    margin: 4px;
    font-size: 16px;
    color: #3e3d3c;
    padding: 0;
    background: 0 0;
    border-radius: 3px
}

.page-nav .page-numbers a {
    display: inline-block;
    line-height: 36px;
    width: 36px;
    height: 36px;
}

.page-nav .next, .page-nav .prev {
    position: relative
}

.page-nav .page-numbers.current {
    border: 1px solid #ff7800;
    color: #ff7800
}

.page-nav .page-numbers:not(.dots):hover {
    border: 1px solid #ff7800;
    background-color: #ff7800;
    color: #fff
}

.page-nav .page-numbers:not(.dots):hover a {
    color: #fff
}

.wrapper-mainbanner.oceans {
    background: url(../images/thiet-ke-website/bg-main.png) no-repeat;
    background-size: cover;
    position: relative
}

.web-page.home-page .service-type-web.extra-header .menu .btn {
    border: none
}

.web-page.home-page .service-type-web.extra-header.sticky .menu .btn {
    background: #ff7800;
    color: #fff
}

.web-page.home-page .service-type-web.extra-header.sticky .menu .btn:hover {
    background: #fff;
    color: #ff7800;
    border: 1px solid #ff7800
}

.wrapper-mainbanner.oceans:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 2;
    background-color: #2468b3;
    opacity: .9
}

.wrapper-mainbanner.oceans div {
    position: relative;
    z-index: 3
}

.wrapper-mainbanner.oceans .section-title {
    color: #fff
}

.ourcustomer-page .mainbanner-content .section-title, .web-page.home-page .mainbanner-content .section-title {
    margin-top: 86px
}

.web-page.home-page .section-title .headline {
    font-weight: 700;
    font-size: 48px;
    color: inherit
}

.web-page.home-page .section-title .description {
    font-size: 20px
}

.trial-bottom .section-title .button-action {
    margin-top: 40px;
    margin-bottom: 32px
}

.web-page.home-page .section-sub-title {
    font-size: 14px;
    font-style: italic;
    color: inherit;
    margin-bottom: 60px;
    margin-top: 30px
}

.web-page.home-page .banner-theme {
    overflow: hidden
}

.web-page.home-page .banner-theme {
    display: flex;
    flex-wrap: wrap
}

.web-page.home-page .banner-theme .banner-theme__item.banner-theme__item--left, .web-page.home-page .banner-theme .banner-theme__item.banner-theme__item--right {
    flex: 0 0 40%;
    max-width: 40%
}

.web-page.home-page .banner-theme .banner-theme__item.banner-theme__item--left img, .web-page.home-page .banner-theme .banner-theme__item.banner-theme__item--right img {
    width: 100%;
    height: 100%
}

.web-page.home-page .banner-theme .banner-theme__item.banner-theme__item--center {
    flex: 0 0 20%;
    max-width: 20%;
    position: relative
}

.web-page.home-page .banner-theme .banner-theme__item.banner-theme__item--center img {
    position: absolute;
    left: 0;
    bottom: 0;
    transform: translateX(-40%)
}

.packages-content .nav-tabs-links {
    display: flex;
    justify-content: center
}

.packages-content ul.nav-tabs {
    border: none;
    width: auto
}

.packages-content ul.nav-tabs {
    border-bottom: 2px solid #d4d4d4;
    font-weight: 700
}

.packages-content ul li .nav-link {
    font-size: 16px
}

.packages-content ul li .nav-link:hover {
    color: #595959
}

.packages-content .nav-tabs .nav-link span {
    text-align: center;
    font-size: 13px;
    font-weight: 400
}

.packages-content ul li {
    flex: none;
    -webkit-box-flex: none;
    -ms-flex: none;
    max-width: none;
    margin-bottom: -1px
}

.packages-content ul li .nav-link {
    border: none
}

.packages-content .nav-tabs .nav-item.show .nav-link, .packages-content .nav-tabs .nav-link.active {
    background: 0 0 !important
}

.packages-content .nav-tabs .nav-item {
    margin-bottom: 1px;
    align-self: flex-end
}

.packages-content ul.nav-tabs li:last-child .nav-link {
    border-right: none
}

.packages-content ul li .nav-link {
    background: 0 0
}

.packages-content .color-spamassassin {
    font-weight: 700;
    color: #008c41
}

.packages-content .color-spamassassin strong {
    color: #006b93;
    font-weight: 700
}

.packages-content ul li .nav-link {
    padding: 10px 20px;
    margin: 0;
    box-shadow: none
}

.packages-content ul li .nav-link.active:before {
    display: none
}

.packages-content ul li .nav-link.active {
    box-shadow: none;
    border-bottom: 3px solid #ff7800;
    margin-bottom: -3px
}

.packages-content ul.nav-tabs li:last-child .nav-link:hover {
    color: #ff7800
}

@media (max-width:767px) {
    @media(max-width: 767px){
        [id^="web-pricing"] .packages-item{
            margin-top: 45px;
        }
    }
    .packages-web {
        margin-top: 0
    }

    .packages-web ul li {
        flex: unset !important
    }

    #mobile-menu .sub-menumb ul li i {
        transform: rotate(135deg) translateY(8px);
        -webkit-transform: rotate(135deg) translateY(2px)
    }

    .packages-page .head-packages .nav-tabs .nav-link span:nth-child(3) {
        padding-left: 0
    }

    .packages-web .view-details {
        padding: 5px 0;
        font-weight: 400
    }

    .packages-web .packages-item .arrow {
        top: 10px
    }

    .packages-item .arrow.up {
        top: 14px !important
    }

    .packages-item.open .collapse-hidden {
        padding: 5px 15px !important;
        margin-top: 15px
    }

    .head-packages .pack-common:before {
        bottom: 0
    }

    .packages-web .packages-item .view-details:hover {
        color: #ff7800
    }

    .packages-item.open .pack-common {
        height: auto;
        overflow: unset
    }

    .packages-item.open .pack-common:before {
        display: none
    }

    .packages-main .packages-item {
        height: auto
    }

    .web-page .packages-web .packages-item .arrow {
        top: 20px
    }

    .web-page .packages-item.open .collapse-hidden {
        padding: 15px 0 !important
    }

    .web-page .packages-item .arrow.up {
        top: 24px !important
    }

    .web-page .price-list .package-header .hot-deal {
        top: -69px;
        left: 50%;
        padding: 12px 0;
        background: #ff7800;
        color: #fff;
        border-radius: 6px 6px 0 0;
        width: 100%;
        font-size: 16px
    }

    .web-page .price-list .icon-crown {
        top: -15px;
        left: unset
    }

    .web-page .price-list .icon-crown img {
        transform: unset
    }
}

.packages-web .hot-deal {
    background: #ff7800
}

.packages-web .packages-item .btn {
    border: 1px solid #ff7800;
    color: #ff7800
}

.packages-web .packages-item .package-header .btn {
    margin-bottom: 10px
}

.packages-web .packages-item .btn + .btn-pre-trial {
    margin-bottom: 30px;
    display: inline-block;
    font-size: 14px;
    text-decoration: underline;
    color: #087cce
}

.packages-web .packages-hot .btn, .packages-web .packages-item .btn:hover {
    background: #ff7800;
    color: #fff
}

.packages-web .packages-hot .package-header:before {
    background: #ff7800
}

.packages-web .packages-content .package-name {
    font-size: 20px
}

.section-title__title {
    font-size: 28px
}

.outstanding-features {
    margin-top: 90px;
    margin-bottom: 75px
}

.outstanding-features .section-title {
    margin-bottom: 0 !important
}

.outstanding-features .outstanding-features__item {
    margin: 30px 0 35px 0;
    padding: 20px 15px;
    background: #f9f9fb;
}

.outstanding-features .outstanding-features__item .outstanding-features__icon img {
    width: 70px;
    height: 70px
}

.outstanding-features .outstanding-features__item .outstanding-features__title {
    font-weight: 700;
    font-size: 18px;
    margin: 30px 0 18px 0;
    min-height: 50px
}

.outstanding-features .outstanding-features__item .outstanding-features__description {
    font-size: 16px;
    color: #7b7a81
}

.outstanding-features .button-action {
    margin: 0 !important
}

/* timeline */
.section-app_heading{
  font-weight: bold;
}
.section-app_link .section-app_heading{
  margin-right: 30px;
}
.section-app_link a img{
  margin-left: 10px;
}

.section-timeline{
  margin: 30px 0 ;
}

.timeline {
    border-left: 2px dashed #ff7800;
    margin: 0 auto;
    position: relative;
    padding: 0 20px;
    list-style: none;
    text-align: left;
}

.timeline .event{
  position: relative;
  margin-bottom: 10px;
  padding: 10px;
  cursor: pointer;
}

.timeline .event p{
  display: none;
  color: #8F8F8F;
  font-size: 16px;
}

.timeline .event.active p{
  display: block;
}

.timeline .event.active{
  background: #FFFFFF;
box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.05);
}

.timeline .event h3{
  font-size: 16px;
  font-weight: normal;
  color: #8F8F8F;
}

.timeline .event.active h3{
  font-weight: bold;
  color: #383838;
}

.timeline .event:last-of-type {
    padding-bottom: 8px;
    margin-bottom: 0;
    border: none;
}

.timeline .event:before,
.timeline .event:after {
    position: absolute;
    display: block;
    top: 0;
}

.timeline .event.active:after {
  background: linear-gradient(14.64deg, #FF7800 0%, #FFB340 100%);
}

.timeline .event:after {
    background: #F0F0F0;
    border-radius: 50%;
    height: 16px;
    width: 16px;
    content: "";
    top: 12px;
    left: -29px;
}
.intro-app_img img:not(.active){
  display: none!important;
}
.intro-app_img img.active{
  display: block!important;
  opacity: 1;
}

@media (max-width: 767px) {
    .section-app .intro-app_img img{
      width: 100%;
    }
    .section-app_link .section-app_heading{
        margin:  0;
    }
    .section-app_link a img{
       float:  right;
       margin-left:  10px;
    }
}
/* end timeline */

.price-list .section-title {
    padding-top: 50px
}

.price-list .section-title {
    margin-bottom: 15px !important
}

.price-list h2 {
    margin: 0 !important
}

.price-list .packages-content {
    background: unset
}

.price-list .packages-content ul li a, .price-list .packages-content ul li a.active {
    color: #fff;
    font-size: 18px
}

.price-list .packages-content ul li a:hover {
    color: #ff7800
}

.price-list .packages-content ul li a span {
    font-size: 14px;
    opacity: .7
}

.price-list .packages-web ul.nav-tabs {
    border-color: rgba(255, 255, 255, .5)
}

.price-list .packages-web ul li .nav-link {
    min-width: 186px
}

.price-list .packages-web ul li .nav-link.active {
    border-color: #fff
}

.price-list .packages-web ul.nav-tabs li .nav-link:hover span {
    color: rgba(255, 255, 255, .7)
}

.price-list .packages-web .packages-item .btn {
    height: 53px;
    line-height: 53px
}

.price-list .hot-deal {
    top: -72px;
    width: calc(100% + 2px)
}

.price-list .packages-content {
    padding-bottom: 85px
}

.price-list .packages-content .tab-content {
    padding-top: 35px
}

.price-list .package-price p:nth-child(1) {
    font-size: 36px
}

.price-list .currency {
    top: -16px
}

.price-list .packages-content .package-name {
    font-size: 20px
}

.price-list .package-feature {
    font-size: 14px;
    opacity: .5;
    min-height: unset
}

.price-list .package-year {
    font-size: 12px
}

.price-list .btn {
    font-weight: 700
}

.web-page.home-page .premium-box {
    margin-bottom: 100px
}

.web-page.home-page .name-theme {
    margin: 0
}

.web-page.home-page .price-theme {
    margin-top: 12px
}

@media(min-width:991px) {
    .premium-theme .owl-dots {
        position: absolute;
        right: 44%;
        bottom: -45px
    }
    .premium-theme .owl-dot {
        width: 9px;
        height: 9px
    }
}

.web-page.home-page .premium-theme .owl-next i, .web-page.home-page .premium-theme .owl-prev i {
    top: -2px;
    left: 1px
}

.web-page.home-page .premium-theme .owl-next i {
    left: -1px
}

.web-page.home-page .owl-dot {
    background: #c4c4c4
}

.web-page.home-page .owl-dot.active {
    background: #ff7800
}

.web-page.home-page #themes .themes-list {
    background: unset;
    padding-top: 0
}

.web-page.home-page .premium-theme {
    padding-bottom: 0
}

.web-page.home-page .command {
    margin: 0
}

.web-page.home-page .themes-list .command .btn-zozo {
    height: 60px;
    line-height: 60px
}

.web-page.home-page #themes {
    margin-top: 0;
    margin-bottom: 50px
}

.web-page.home-page .section-title {
    margin-bottom: 40px
}

.web-page.home-page .customer-item img {
    filter: unset
}

.web-page.home-page .accordion .card h3 {
    font-size: 18px
}

.question-area #accordion-pack .card {
    padding: 0 0 40px 0;
    border: none
}

.question-area {
    background: #f9f9fb
}

.question-area .section-title__title {
    font-size: 28px
}

.question-area .section-title__description {
    font-size: 20px
}

@media(min-width:991px) {
    .question-area .accordion .class-xl-6:nth-child(5) .card, .question-area .accordion .class-xl-6:nth-child(6) .card {
        border-bottom: none !important;
        padding-bottom: 0 !important
    }

}

@media(max-width:767px) {
    .question-area {
        padding-bottom: 0
    }

}

.trial-bottom .section-title {
    padding-top: 70px;
    padding-bottom: 40px;
    margin-bottom: 0 !important
}

.trial-bottom .section-title .section-title__title {
    margin-bottom: 0
}

.trial-bottom .button-action {
    margin-top: 30px !important
}

.trial-bottom .section-sub-title {
    margin-bottom: 0 !important
}

.trial-bottom .section-title {
    position: relative
}

.trial-bottom .section-title:before {
    content: "";
    position: absolute;
    width: 219px;
    height: 1px;
    background: #fff;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%)
}

.trial-bottom .trial-form__wrapper .input-zozo {
    background: #417fbf;
    border: 1px solid #5e9adc
}

.trial-bottom .trial-form__wrapper .input-zozo, .trial-bottom .trial-form__wrapper .input-zozo::placeholder {
    color: #fff
}

.trial-bottom .trial-form {
    padding: 40px 0 60px 0
}

.trial-bottom .trial-form__title {
    font-size: 24px;
    font-weight: 700;
    margin: 0
}

.trial-bottom .trial-form__description {
    font-size: 20px;
    margin: 5px 0 24px 0
}

.trial-bottom .text-danger {
    color: #ff7800 !important;
    font-size: 13px;
    margin-top: 5px
}

.ck-button label {
    margin-top: 8px;
    margin-bottom: 24px;
    margin-right: 8px
}

.ck-button {
    text-align: left
}

.estimation-form input {
    width: 100%;
    border-radius: 2px;
    margin-bottom: 16px;
    border: none;
    padding: 10px 16px;
    resize: none;
    color: #333
}

.ck-button label input {
    display: none
}

.ck-button label span {
    background: #f3f3f3;
    color: #929292;
    padding: 10px 16px;
    border: 1px solid #b8bec5;
    cursor: pointer;
    border-radius: 4px
}

.ck-button label span {
    font-size: 16px
}

.ck-button input:checked + span {
    background: #ff7800;
    color: #fff;
    border: 1px solid transparent
}

.trial-success .trial-success__notification {
    font-size: 20px;
    font-weight: 700;
    color: #68b13a;
    margin-bottom: 10px
}

.trial-success .trial-success__subnotification {
    color: #7b7a81;
    margin: 0;
    padding-bottom: 25px;
    margin-bottom: 50px;
    position: relative
}

.trial-success .trial-success__subnotification:before {
    content: "";
    position: absolute;
    background: #68b13a;
    width: 95px;
    height: 1px;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%)
}

.trial-success .trial-success__form {
    font-size: 14px;
    color: #42414d
}

.trial-success .trial-success__business button {
    position: relative;
    width: 100%;
    height: 45px;
    background: #fff;
    border: 1px solid #c4c4c4;
    color: #42414d;
    font-size: 14px
}

.trial-success .trial-success__form h3 {
    margin-top: 30px
}

.trial-success .trial-success__business .dropdown-toggle::after {
    position: absolute;
    right: 15px;
    top: 20px
}

.trial-success .trial-success__business {
    position: relative
}

.themes-list .theme-list__top .dropdown-menu, .trial-success .trial-success__business .dropdown-menu {
    width: 100%;
    padding: 0;
    max-height: 300px;
    overflow-y: auto
}

.trial-success .trial-success__business .dropdown-menu li a {
    display: block;
    padding: 10px
}

.trial-success .trial-success__business .dropdown-menu li:hover a {
    background: #68b13a;
    color: #fff
}

.trial-success .trial-success__business.show .dropdown-toggle {
    background: #68b13a;
    border-color: #68b13a
}

.trial-success .trial-success__feature label span {
    background: #fff;
    color: #42414d
}

.trial-success .trial-success__feature label span:focus {
    outline-color: #68b13a
}

.trial-success .trial-success__feature .ck-button input:checked + span {
    background: #68b13a;
    border-color: #68b13a;
    color: #fff
}

.trial-success textarea::placeholder {
    font-size: 14px;
    opacity: .5
}

.trial-success button[type=submit] {
    display: block;
    width: 100%;
    margin-top: 23px
}

.trial-success-wrap {
    border-radius: 6px;
    padding: 30px 35px 20px 35px !important
}

@media(min-width:991px) {
    .fancybox-slider .trial-success .trial-success__business .dropdown-menu li {
        width: 50%;
        float: left
    }

}

.success-page .mainbanner-content .section-title {
    margin: 40px 0 !important
}

#themes {
    margin-top: 50px
}

#themes .business-choose {
    color: #ff7800
}

.success-page .trial-bottom .section-title {
    padding-bottom: 75px !important
}

.success-page .trial-bottom .section-title:before {
    display: none
}

.feedback-customer {
    background: #f9f9fb;
    padding: 60px 0 90px 0
}

.feedback-customer .feedback-customer__box {
    background: #fff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, .03);
    border-radius: 6px;
    padding: 35px 100px 45px 100px;
    max-width: 945px;
    margin: 0 auto
}

.feedback-customer .feedback-customer__center {
    margin: 20px 32px
}

.feedback-customer .feedback-customer__img img {
    width: auto;
    margin: 0 auto
}

.feedback-customer .feedback-customer__content .owl-next, .feedback-customer .feedback-customer__content .owl-prev {
    width: 67px;
    height: 67px;
    background: #fff;
    border-radius: 100%;
    left: -24%
}

.feedback-customer .feedback-customer__content .owl-next {
    left: unset;
    right: -24%
}

.feedback-customer .feedback-customer__content .owl-nav i {
    padding: 7px;
    border-color: #ff7800;
    border-width: 0 2px 2px 0
}

.feedback-customer .feedback-customer__content .owl-nav .owl-prev i {
    margin-left: 2px
}

.feedback-customer .feedback-customer__content .owl-nav .owl-next i {
    margin-left: -2px
}

.feedback-customer .feedback-customer__content .owl-dots {
    position: absolute;
    bottom: -140px;
    left: 50%;
    transform: translateX(-50%)
}

@media(max-width:767px) {
    .success-page #themes .section-title {
        margin-bottom: 30px
    }

    .success-page .feedback-customer .feedback-customer__box {
        max-width: 100%;
        padding: 50px 10px
    }

    .success-page .feedback-customer .feedback-customer__center {
        margin: 0
    }

    .success-page .feedback-customer .owl-dots .owl-dot {
        background: #c4c4c4 !important
    }

    .success-page .owl-carousel .owl-dots .owl-dot.active {
        background: #ff7800 !important
    }

    .success-page .feedback-customer .owl-nav {
        display: none
    }

}

@media(max-width:767px) {
    .ourcustomer-page .menu-bar span, .web-page.home-page .menu-bar span {
        background: #fff
    }

    .logo {
        width: 100%;
        text-align: center;
        padding: 0 !important
    }

    .logo-white {
        display: none
    }

    .web-page.home-page .logo-orange {
        display: none
    }

    .web-page.home-page .logo-white {
        display: block
    }

    .menu-bar {
        right: 15px;
        top: 50%;
        transform: translateY(-50%)
    }

    .ourcustomer-page .main-menu-area, .web-page.home-page .main-menu-area, .bao-gia-theo-du-an-website .main-menu-area {
        position: absolute;
        width: 100%;
        background: 0 0;
        border-bottom: 1px solid rgba(255, 255, 255, .5) !important
    }

    .web-page.home-page .subpage-content {
        padding-top: 0
    }

    .web-page.home-page .main-menu-area {
        border-bottom: none
    }

    .web-page.home-page .wrapper-mainbanner {
        padding-top: 0
    }

    .web-page.home-page h1 {
        font-size: 36px !important;
        padding-top: 80px
    }

    .outstanding-features {
        margin-top: 54px;
        margin-bottom: 40px
    }

    .outstanding-features .outstanding-features__item {
        margin: 0 0 12px 0;
        padding: 30px 0
    }

    .outstanding-features .outstanding-features__item .outstanding-features__title {
        margin: 30px 0 12px 0;
        min-height: unset
    }

    .outstanding-features .outstanding-features__item .outstanding-features__description {
        margin: 0;
        padding: 0 25px
    }

    .web-page.home-page .outstanding-features .col-12:first-child {
        margin-top: 36px
    }

    .web-page.home-page .outstanding-features .button-action {
        margin-top: 15px !important
    }

    .price-list .packages-web ul li .nav-link {
        min-width: unset
    }

    .price-list .packages-web ul li:first-child .nav-link span {
        opacity: 0
    }

    .price-list .packages-content .tab-content {
        padding-top: 0
    }

    .package-header {
        padding-bottom: 0
    }

    .price-list .packages-content .tab-content .package-header .btn {
        background: #ff7800;
        color: #fff
    }

    .price-list .package-header .hot-deal {
        position: absolute;
        width: unset;
        top: 3px;
        left: 70px;
        font-size: 14px;
        padding: 3px 10px 2px 10px;
        background: #ffe4cc;
        border-radius: 6px;
        color: #ff7800;
        margin: 0
    }

    .price-list .packages-content {
        padding-bottom: 40px
    }

    .web-page.home-page .premium-box .owl-carousel .owl-dots .owl-dot {
        background: #c4c4c4
    }

    .web-page.home-page .premium-box .owl-carousel .owl-dots .owl-dot.active {
        background: #ff7800
    }

    .web-page.home-page .premium-box .owl-dots {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%)
    }

    .web-page.home-page .premium-box {
        margin-bottom: 55px
    }

    .web-page.home-page .customer-area {
        margin-bottom: 40px
    }

    .web-page.home-page .question-area {
        padding: 50px 0 30px 0
    }

    .web-page.home-page #accordion-pack {
        margin-top: 0
    }

    .web-page.home-page #accordion-pack .card {
        padding: 10px 0
    }

    .web-page.home-page .question-area .section-title {
        margin-bottom: 20px
    }

    .web-page.home-page .question-area .card-body p {
        margin: 10px 0 0 0
    }

    .trial-bottom .trial-form__wrapper .input-zozo {
        margin-bottom: 10px
    }

    .trial-bottom-btn {
        text-align: center
    }

}

.choose-theme-page .progressbar-row {
    margin: 35px 0 20px 0;
    padding-bottom: 40px
}

.choose-theme-page .progressbar-row ul li {
    color: #fff;
    font-size: 14px;
    text-transform: unset;
    position: relative
}

.choose-theme-page .progressbar-row ul li.active {
    font-weight: 700
}

.choose-theme-page .progressbar li:before {
    width: 40px;
    height: 40px;
    line-height: 40px;
    background: #6da7e5;
    color: #fff;
    font-size: 18px;
    border: none;
    z-index: 2;
    position: relative
}

.choose-theme-page .progressbar li.active:before {
    background: #ff7800
}

.choose-theme-page ul li.active .progressbar-circle {
    position: absolute;
    width: 52px;
    height: 52px;
    background: #fff;
    top: 20px;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    border-radius: 100%
}

.choose-theme-page .progressbar-row:before {
    content: "";
    position: absolute;
    width: 93px;
    height: 1px;
    background: #fff;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%)
}

.infomation-trial__title {
    color: #fff;
    margin-top: 0
}

.choose-theme-page .infomation-trial #register-form {
    background: #fff;
    border-radius: 6px;
    padding: 30px 35px
}

.choose-theme-page .infomation-trial form label {
    color: #42414d;
    font-weight: 700;
    font-size: 14px
}

.choose-theme-page .infomation-trial button[type=submit] {
    width: 100%
}

.style-dots .progressbar li:after {
    display: none
}

.style-dots ul li .wrap-progress-dots {
    overflow: hidden;
    display: block
}

.style-dots .progress-dots {
    width: 9px;
    height: 9px;
    background: #6da7e5;
    display: block;
    border-radius: 100%;
    position: absolute;
    top: 20px
}

.style-dots .progress-dots.active {
    background: #fff
}

.style-dots ul li .wrap-progress-dots .progress-dots:nth-child(1) {
    right: 20px
}

.style-dots ul li .wrap-progress-dots .progress-dots:nth-child(2) {
    right: 0
}

.style-dots ul li .wrap-progress-dots .progress-dots:nth-child(3) {
    right: -20px
}

@media(min-width:991px) {
    .infomation-trial {
        padding-bottom: 100px
    }

    .choose-theme-page .infomation-trial #register-form {
        max-width: 620px;
        margin: 0 auto 0 auto
    }

    .choose-theme-page .infomation-trial #register-form .form-group {
        margin-bottom: 24px
    }

}

@media(max-width:767px) {
    .choose-theme-page .logo.logo-white {
        display: none
    }

    .choose-theme-page .infomation-trial {
        padding: 0 15px 60px 15px
    }

    .choose-theme-page .infomation-trial #register-form {
        padding: 25px 15px 15px 15px
    }

    .choose-theme-page .infomation-trial button[type=submit] {
        margin-top: 20px
    }

    .choose-theme-page ul li .progressbar-title {
        display: none
    }

    .choose-theme-page .progressbar-row:before {
        display: none
    }

    .choose-theme-page .progressbar-row {
        padding: 0 65px
    }

    .choose-theme-page .infomation-trial__title {
        margin-left: 65px;
        margin-right: 65px
    }

    .choose-theme-page .progressbar-row.style-dots {
        padding: 0
    }

    .style-dots ul li .wrap-progress-dots .progress-dots:nth-child(1) {
        right: 12px
    }

    .style-dots ul li .wrap-progress-dots .progress-dots:nth-child(2) {
        right: -4px
    }

    .style-dots ul li .wrap-progress-dots .progress-dots:nth-child(3) {
        right: -20px
    }

}

.process-page {
    background-color: #f9f9fb
}

.process-theme {
    min-height: 100vh;
    padding: 60px 0
}

#animated {
    height: 300px
}

#animated path {
    stroke: #ff7800;
    stroke-width: 1
}

#animated #count {
    font-size: 14px;
    fill: #ff7800
}

#animated circle {
    stroke: #e0e0e0;
    stroke-width: .5
}

.process-theme .process-theme__title {
    margin-top: 0;
    margin-bottom: 5px
}

.process-theme .process-theme__description {
    margin: 0
}

.process-theme .status-process {
    margin-top: -45px;
    margin-bottom: 0
}

.process-success {
    padding: 50px 0 40px 0
}

.process-error .process-theme__title, .process-success .process-theme__title {
    margin: 0 0 7px 0
}

.process-error .process-theme__description, .process-success .process-theme__description {
    margin: 0 0 7px 0
}

.process-error .button-action, .process-success .button-action {
    margin: 20px 0 35px 0
}

.process-success__image img {
    max-height: 320px
}

.process-error {
    padding: 50px 0
}

.process-error .process-success__image img {
    max-height: 200px
}

.contact-hotline {
    border: 1px solid #dfe2ec;
    box-sizing: border-box;
    border-radius: 6px;
    display: inline-block;
    padding: 10px 70px;
    margin-bottom: 27px
}

.contact-hotline p {
    margin: 0;
    color: #7b7a81
}

.contact-hotline p a {
    color: #ff7800;
    font-weight: 700;
    font-size: 18px
}

.web-page.home-page .site-banner {
    padding: 0 !important
}

@media(max-width:1200px) {
    .main-menu nav > ul > li {
        margin-left: 15px
    }

    .order-page .price-list .packages-main {
        padding-top: 100px
    }

}

@media(max-width:991px) {
    .web-page.home-page .subpage-content {
        padding-top: 0
    }

    .outstanding-features .outstanding-features__item .outstanding-features__description {
        min-height: 70px
    }

    .outstanding-features .outstanding-features__item {
        margin-bottom: 0;
        margin-top: 0
    }

    .outstanding-features .button-action {
        margin-top: 50px !important
    }

    .outstanding-features .col-12 {
        margin-top: 36px
    }

    .outstanding-features .outstanding-features__item .outstanding-features__title {
        padding: 0 15px
    }

    .price-list .package-header {
        padding: 0
    }

    .price-list .packages-content .packages-item {
        height: 100%
    }

    .price-list .packages-content .tab-content {
        padding-top: 40px
    }

    .price-list .pl-0 {
        padding-left: 15px !important
    }

    .price-list .packages-content {
        padding-bottom: 0 !important
    }

    .trial-bottom .trial-bottom-btn {
        text-align: center;
        margin-top: 20px
    }

    .customer-area {
        margin-top: 80px
    }

    .premium-theme .headline {
        font-size: 28px !important
    }

    .premium-theme .content-theme {
        text-align: center
    }

    .section-theme-menu-bar.new-version .input-group input {
        background: #f3f3f3;
        border-radius: 6px;
        border: none;
        height: 44px;
        padding-left: 17px
    }

    .section-theme-menu-bar.new-version .input-group .input-group-append {
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 999;
        height: 44px;
        line-height: 44px
    }

    .section-theme-menu-bar.new-version .input-group .input-group-append button {
        background: unset;
        border: none
    }

    .section-theme-menu-bar.new-version {
        padding: 8px 0
    }

    .filter-theme-mb__fields-box .ck-button label {
        margin: 0 0 10px 0;
        width: 100%
    }

    .filter-theme-mb .ck-button .col-6:nth-child(odd) {
        padding-right: 5px
    }

    .filter-theme-mb .ck-button .col-6:nth-child(even) {
        padding-left: 5px
    }

    .filter-theme-mb__body .ck-button label span, .theme-page .filter-theme-mb__body .ck-button label p {
        color: #1f2642;
        font-size: 13px
    }

    .filter-theme-mb__fields-box .ck-button label span {
        background: #fff;
        border: 1px solid #c4c4c4;
        box-sizing: border-box;
        border-radius: 6px;
        font-size: 13px;
        width: 100%;
        display: block;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis
    }

    .filter-theme-mb__design .style-design li {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 !important
    }

    .filter-theme-mb__design .style-design li a {
        width: 100%;
        text-align: center;
        display: block;
        padding: 0
    }

    .filter-theme-mb__design .filter-theme-mb__item label {
        margin: 0;
        width: 100%
    }

    .filter-theme-mb__design .filter-theme-mb__item label img {
        width: 100%
    }

    .filter-theme-mb {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        height: 100%;
        background: #fff;
        z-index: 9999;
        transition: all .5s;
        transform: translate(-100%);
        opacity: 0;
        visibility: hidden;
        overflow-x: hidden;
        overflow-y: auto
    }

    .filter-theme-mb .filter-theme-mb__head {
        border-bottom: 1px solid #dedede;
        padding: 0 15px
    }

    .filter-theme-mb .filter-theme-mb__body {
        background: #f9f9fb;
        padding: 20px 15px 40px 15px
    }

    .filter-theme-mb .filter-theme-mb__body .filter-theme-mb__fields {
        padding-bottom: 20px
    }

    .filter-theme-mb .filter-theme-mb__body .filter-theme-mb__title {
        margin: 0;
        padding-bottom: 25px
    }

    .filter-theme-mb .filter-theme-mb__body .filter-theme-mb__design .filter-theme-mb__title {
        padding-bottom: 0
    }

    .filter-theme-mb__design .ck-button input:checked ~ p:last-child {
        color: #ff7800 !important;
        font-weight: 700
    }

    .renew-content {
        padding-top: 20px
    }

    .renew-content .info-pack {
        margin-bottom: 20px
    }

    .renew-content .info-renew, .renew-content .info-renew .info-pack__head {
        border: none
    }

    .renew-content .info-renew .info-pack__head {
        padding: 10px 0
    }

    .renew-content .info-renew__body .info-renew__price {
        margin-left: 25px
    }

    .renew-content .info-renew__body .info-renew__percent {
        display: block;
        width: 120px;
        text-align: center;
        margin-left: 25px;
        margin-top: 5px
    }

    .renew-content .button-action .btn-oceans {
        width: 100%
    }

    .contact-page .image-men, .contact-page .section-title h1 {
        margin-top: 40px
    }

    .contact-page .section-title .description {
        margin-bottom: 40px
    }

    .order-page .price-list .packages-main {
        padding-top: 100px
    }

}

@media(max-width:991px) and (min-width:767px) {
    .price-list .view-details {
        display: none
    }

    .btn.btn-mobile {
        display: none
    }

}

@media(max-width:767px) {
    .web-page.home-page .price-list .package-price {
        display: block
    }

    .outstanding-features .outstanding-features__item {
        height: 100%
    }

    .price-list .packages-content .packages-item {
        height: auto
    }

    .order-page .price-list .packages-main {
        padding-top: 20px
    }

    .order-page .price-list .packages-main .packages-item {
        border-right: 1px solid #e3e6f1
    }

}

.web-page.bao-gia-theo-du-an-website {
    background: #f9f9fb
}

.form-requirement {
    position: relative;
    width: 747px;
    margin: -80px auto 60px auto;
    background: #fff;
    padding: 40px 50px;
    font-size: 14px
}

.form-requirement h3 {
    font-size: 28px;
    font-weight: 400;
    text-align: center !important;
    margin-top: 0
}

.form-requirement input[type=text] {
    font-size: 14px
}

.form-requirement input[type=text]:focus, .form-requirement textarea:focus {
    border-color: #2a62ff
}

.form-requirement input[type=text]::placeholder {
    color: #42414d;
    opacity: .5
}

.form-requirement .text-danger {
    color: #ed1c24 !important;
    font-weight: 700;
    font-size: 14px;
    margin-top: 5px
}

.form-requirement .form-requirement__label {
    color: #4f5466
}

.form-requirement input[type=checkbox] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0
}

.form-requirement .check-icon {
    position: absolute;
    top: 12px;
    left: 26px;
    height: 17px;
    width: 17px;
    border-radius: 100%;
    border: 1px solid #c4c4c4;
    background: #fff
}

.form-requirement .project-types__item.checked input ~ .check-icon:after {
    display: block
}

.form-requirement .project-types__item.checked input ~ .check-icon {
    border-color: #2a62ff
}

.form-requirement .check-icon:after {
    content: "";
    position: absolute;
    display: none;
    left: 5px;
    top: 1px;
    width: 5px;
    height: 10px;
    border: solid #2a62ff;
    border-width: 0 1px 1px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg)
}

.form-requirement .project-types__item {
    background: #f3f3f3;
    border: 1px solid #d3d9f0;
    border-radius: 6px;
    margin-bottom: 15px
}

.form-requirement .project-types__item.checked {
    border: 1px solid #2a62ff
}

.form-requirement .project-types__item.checked label {
    color: #2a62ff
}

.form-requirement .project-types__item label {
    font-size: 14px;
    margin-bottom: 0;
    padding: 10px 10px 10px 35px;
    display: block
}

.form-requirement .project-types__item label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.form-requirement .project-types__item--add {
    background: #fff !important
}

.form-requirement .project-types__item--add.checked:hover {
    border-color: #404040 !important
}

.form-requirement .project-types__item--add label {
    padding-left: 26px
}

.form-requirement .project-types__item--add label:hover {
    color: #404040 !important
}

.gg-trash {
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    width: 10px;
    height: 12px;
    left: -10px;
    top: 2px;
    border: 2px solid transparent;
    box-shadow: 0 0 0 2px, inset -2px 0 0, inset 2px 0 0;
    border-bottom-left-radius: 1px;
    border-bottom-right-radius: 1px
}

.gg-trash::after, .gg-trash::before {
    content: "";
    display: block;
    box-sizing: border-box;
    position: absolute
}

.gg-trash::after {
    border-radius: 3px;
    width: 16px;
    height: 2px;
    top: -4px;
    left: -5px
}

.gg-trash::before {
    width: 10px;
    height: 4px;
    border: 2px solid;
    border-bottom: transparent;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    top: -7px;
    left: -2px
}

@media(min-width:991px) {
    #features .col-xl-4:nth-child(3n) {
        padding-right: 15px
    }

    #features .col-xl-4 {
        padding-left: 15px;
        padding-right: 0
    }

    .form-requirement .project-types__item label {
        width: 194px
    }

}

.form-requirement__budget {
    margin: 30px 0 80px 0
}

.slider-wrapper {
    width: 100%;
    display: inline-block;
    position: relative;
    font-family: arial
}

.ui-slider {
    background: #f3f3f3;
    border: 1px solid #d3d9f0;
    height: 8px;
    position: relative;
    border-radius: 6px
}

.ui-slider-range {
    background: #2a62ff;
    height: 12px;
    position: absolute;
    transform: translateY(-3px)
}

.ui-slider-range:after {
    content: '';
    width: 100%;
    height: 1px;
    background: #fff;
    position: absolute;
    top: 0;
    left: 0;
    opacity: .3
}

.ui-slider-handle {
    background: #2a62ff;
    position: absolute;
    width: 22px;
    height: 22px;
    top: 50%;
    display: block;
    transform: translate(-50%, -50%);
    border-radius: 100px;
    z-index: 10;
    cursor: move;
    cursor: grab;
    cursor: -moz-grab;
    cursor: -webkit-grab;
    transition: width .1s
}

.ui-slider-handle:focus {
    outline: 0
}

.ui-slider-handle:active {
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing
}

.ui-state-active {
    width: 22px
}

.range-wrapper {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%)
}

.range {
    border-radius: 6px;
    font-size: 13px;
    letter-spacing: .02em;
    color: #555;
    min-width: 110px;
    z-index: 10;
    position: relative
}

.range-value {
    position: absolute;
    left: -50%;
    transform: translateX(10px);
    background: #f3f3f3;
    min-width: 110px;
    padding: 2px 10px;
    text-align: center;
    display: inline-block;
    transition: background .1s;
    border-radius: 6px;
    border: 1px solid #d3d9f0
}

.form-requirement #project-types {
    margin-bottom: 5px
}

.form-requirement #features button[type=button] {
    line-height: inherit;
    background: #2a62ff;
    border-radius: 6px;
    border-color: #2a62ff;
    color: #fff;
    width: 123px;
    cursor: pointer
}

.form-requirement #features button[type=button]:hover {
    background: #fff;
    color: #2a62ff
}

.form-requirement #features input[type=text] {
    border-radius: 6px;
    margin-right: 9px
}

.range {
    margin-top: 30px
}

#slider-range .ui-slider-handle:nth-child(2) .value-max, #slider-range .ui-slider-handle:nth-child(3) .value-min {
    display: none
}

#slider-range .ui-slider-handle:before {
    content: "";
    position: absolute;
    width: 14px;
    height: 14px;
    background: #fff;
    top: 4px;
    left: 4px;
    border-radius: 100%
}

.range-value:before {
    content: "";
    position: absolute;
    border-top: 1px solid #d3d9f0;
    border-left: 1px solid #d3d9f0;
    background: #f3f3f3;
    width: 5px;
    height: 5px;
    transform: rotate(45deg);
    top: -3.5px;
    left: 53px
}

.form-requirement .form-requirement__label {
    font-size: 16px
}

.form-requirement textarea::placeholder {
    font-size: 14px;
    color: #42414d;
    opacity: .5
}

.form-requirement button[type=submit] {
    background-color: #2a62ff;
    border-color: #2a62ff;
    width: 100%;
    font-weight: 700;
    font-size: 16px;
    line-height: unset
}

.form-requirement button[type=submit]:hover {
    background-color: #fff;
    color: #2a62ff;
    cursor: pointer
}

.form-requirement .form-requirement__file {
    margin: 50px 0 35px 0
}

.form-requirement .addfile__item {
    position: relative
}

.form-requirement .addfile__item .close {
    right: -5px;
    top: 2px
}

.form-requirement .addfile__item .close:after, .form-requirement .addfile__item .close:before {
    background: #c4c4c4;
    height: 12px
}

.bao-gia-theo-du-an-website .wrapper-mainbanner .wrapper-content {
    padding: 125px 0 145px 0;
    color: #fff
}

.bao-gia-theo-du-an-website .wrapper-mainbanner .wrapper-content h1, .bao-gia-theo-du-an-website .wrapper-mainbanner .wrapper-content p {
    color: inherit
}

.bao-gia-theo-du-an-website .wrapper-mainbanner .wrapper-content p {
    opacity: .7
}

@media(max-width:991px) {
    .form-requirement {
        max-width: 100%;
        margin-top: 0;
        margin-bottom: 0;
        padding-left: 15px;
        padding-right: 15px
    }

    .web-page.bao-gia-theo-du-an-website .main-menu-area {
        border-width: 1px
    }

    .web-page.bao-gia-theo-du-an-website .form-requirement {
        position: relative;
        width: auto;
        margin: -70px 15px 40px 15px
    }

    .bao-gia-theo-du-an-website .wrapper-mainbanner .wrapper-content {
        padding: 170px 0 100px 0
    }

    .bao-gia-theo-du-an-website .menu-bar span {
        background: #fff
    }

    .bao-gia-theo-du-an-website .logo-orange {
        display: none
    }

    .bao-gia-theo-du-an-website .logo-white {
        display: block
    }

}

#related-themes .themes-list {
    background: unset
}

.app-page .page-content {
    margin-top: 40px
}

.app-page .subpage-content {
    padding-top: 0
}

.app-page .app-category-title {
    font-size: 28px;
    margin-top: 0 !important
}

.app-page .menu-title {
    margin-top: 10px !important;
    margin-bottom: 0 !important
}

.app-page .apps-search form {
    position: relative
}

.app-page .apps-search svg {
    top: 12px
}

.app-page .apps-dropdown-icon {
    margin-top: 0;
    color: #1f2642
}

@media (max-width:767px) {
    .app__menu-bar span {
        display: block;
        width: 25px;
        height: 3px;
        background: #1f2642;
        margin-top: 4px
    }

    .app-page .page-content {
        margin-top: 0
    }

    .app-page .app-category-title {
        margin-top: 30px
    }

    .app-page .app-category .app-item {
        height: auto
    }

    .app-page .app-category .app-item {
        height: auto
    }

    .app-page .app-category .app-item ul {
        position: unset
    }

    .app-page .app-category .app-item ul li {
        float: unset
    }

    .app-page .menu-xs {
        display: flex;
        flex-wrap: wrap;
        align-items: center
    }

    .app-page .menu-xs .app__menu-bar {
        position: unset;
        margin-right: 10px
    }

    .app-page .app__menu-bar span:first-child {
        margin-top: 0
    }

    .app-page .apps-search-xs .category-dropdown {
        padding-left: 0;
        padding-right: 0
    }

    .app-page .apps-search-xs .category-dropdown a {
        line-height: 36px
    }

    .app-page .page-content .apps-search {
        display: none !important
    }

}

.result-search {
    background: #f9f9fb;
    margin-bottom: 40px
}

.result-search-box {
    padding: 0 30px;
    background: #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, .1);
    border-radius: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center
}

.result-search-box__text {
    margin-right: 20px
}

.result-search-box__item a {
    position: relative;
    border: 1px solid #fe7a05;
    box-sizing: border-box;
    border-radius: 12px;
    padding: 6px 12px 6px 35px;
    margin-right: 12px;
    color: #ff7800
}

.result-search-box__item a .close {
    top: 10px;
    left: 5px
}

.result-search-box__item a .close:after, .result-search-box__item a .close:before {
    background-color: #ff7800
}

.result-search-box__item a.active {
    background: #ff7800
}

.result-search-box__item a.active {
    color: #fff
}

.result-search-box__item a.active .close:after, .result-search-box__item a.active .close:before {
    background-color: #fff
}

@media(max-width:767px) {
    .result-search-box {
        padding: 5px 10px 20px 10px;
        display: block
    }

    .result-search-box__item a {
        display: inline-block;
        margin-right: 0;
        margin-bottom: 5px
    }

}

.certification .bct-intro {
    background-size: cover !important;
    background-position: center !important
}

.certification .body-procedure {
    padding: 50px 0 !important
}

@media(min-width:991px) {
    .certification .bct-intro {
        padding: 50px 0
    }
}

.cart-area {
    position: fixed;
    right: 40px;
    top: 45%;
    width: 56px;
    height: 56px;
    background: #ebebeb;
    border-radius: 100%;
    display: flex;
    padding: 10px;
    z-index: 9999;
    transition: all .5s;
    opacity: 0
}

.main-menu-area.sticky .cart-area {
    opacity: 1
}

.cart-area .cart-area.sticky {
    position: fixed;
    top: 10px;
    z-index: 9999
}

.cart-area .cart-area__icon {
    width: 30px;
    height: 30px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center
}

.cart-area .cart-area__icon .cart-area__count {
    width: 18px;
    height: 18px;
    background: #ff7800;
    border-radius: 100%;
    color: #fff;
    padding: 12px 10px 10px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: -8px;
    top: -8px
}

.refer-services {
    margin-bottom: 75px
}

.refer-services__title {
    margin-top: 40px
}

.refer-services__content {
    background: #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, .06)
}

.refer-services__name {
    border-bottom: 1px solid #e9ecef;
    padding: 10px 25px;
    display: flex;
    align-items: center
}

.refer-services__name span {
    font-size: 20px;
    margin-left: 15px
}

.refer-services__details {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef
}

.refer-services__details p {
    font-size: 14px;
    color: #8c8f9b
}

.refer-services__img {
    text-align: center
}

.refer-services__content .btn {
    color: #fff;
    border-radius: 3px;
    margin-top: 20px
}

.refer-service__ema .btn {
    background: #6722d1
}

.refer-service__ema .btn:hover {
    background: #fff;
    border-color: #6722d1;
    color: #6722d1
}

.refer-service__inet .btn {
    background: #4791ec
}

.refer-service__inet .btn:hover {
    background: #fff;
    border-color: #4791ec;
    color: #4791ec
}

.refer-service__web .btn {
    background: #ff7800
}

.refer-service__web .btn:hover {
    background: #fff;
    border-color: #ff7800;
    color: #ff7800
}

main {
    background: #f9f9fb
}

.cart-step-billing.box-margin {
    margin-top: 0
}

.bg-cart {
    background: #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, .06)
}

.cart-title {
    padding: 15px 25px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 700
}

.sso-register__form {
    padding: 30px 25px 40px 25px;
    margin-bottom: 25px
}

.sso-register__form label {
    font-size: 14px;
    font-weight: 700;
    color: #42414d
}

.sso-register__form input.form-control {
    height: 45px;
    font-size: 14px
}

#cartItemContent table {
    border: none
}

#cartItemContent table thead tr {
    background: #444;
    color: #fff
}

#cartItemContent table thead tr th {
    border: none;
    padding: 15px 25px;
    text-align: left
}

#cartItemContent table tbody tr td {
    padding: 15px 25px;
    text-align: left
}

#cartItemContent table tbody tr {
    background: #fff
}

#cartItemContent table tbody tr:hover {
    background: rgba(0, 0, 0, .05)
}

#cartItemContent table tbody tr:last-child {
    border-bottom: none
}

#cartItemContent table tbody .period, #cartItemContent table tbody .status {
    display: inline-block
}

#cartItemContent table tbody .status {
    margin-left: 15px
}

#cartItemContent .detail-info .status {
    margin-left: 0
}

.cart-step-billing .active .cart-step .icon-white, .cart-step-billing .cart-step .icon-gray {
    display: block
}

.cart-step-billing .active .cart-step .icon-gray, .cart-step-billing .cart-step .icon-white {
    display: none
}

.back-to-cart {
    color: #8c8f9b;
    font-size: 14px;
    text-decoration: underline;
    margin-bottom: 35px
}

#cartItemContent table {
    margin-bottom: 0
}

@media(max-width:767px) {
    .cart-step-billing .cart-step-wrap:before, .cart-step-billing .txt-cart-step {
        display: none !important
    }

    .refer-services__content .btn {
        margin-bottom: 30px
    }

    .refer-services__img {
        text-align: left
    }

    #cartItemContent table {
        margin-bottom: 0
    }

    #cartItemContent table tbody .status {
        margin-left: 0
    }

    .info-payment__detail .info-payment__text {
        display: block
    }

    .order-detail .refer-services__name span {
        min-height: unset
    }

}

.fancybox-slide > * {
    background: unset
}

.header-transparent .main-menu-area .header-right-box .cart-area__icon img {
    width: 24px;
    height: 22px
}

.header-transparent .main-menu-area .header-right-box .cart-area__icon {
    position: relative
}

.header-transparent .main-menu-area .header-right-box .cart-area__count {
    position: absolute;
    top: -8px;
    left: 15px;
    background: #ff7800;
    color: #fff;
    padding: 0 5px;
    border-radius: 100%;
    font-size: 14px;
    width: 18px;
    height: 18px
}

.header-transparent .main-menu-area .header-right-box {
    display: flex !important;
    flex-direction: row;
    justify-content: flex-end;
    width: 100%;
    height: 100%
}

.header-transparent .main-menu-area .header-right-box .cart-header {
    display: flex;
    align-items: center;
    position: relative
}

.header-transparent .main-menu-area .header-right-box .cart-header__text {
    font-size: 14px;
    margin-right: 10px;
    color: #7b7a81
}

@media(min-width:1200px) {
    header .main-menu-area .row {
        align-items: center
    }

    .header-2-right, .header-right-box {
        display: flex !important
    }

    .header-transparent .main-menu-area .container {
        width: 1300px;
    }

    .header-transparent .main-menu-area .main-logo img {
        width: 105px;
        max-width: unset
    }

    .header-transparent .main-menu-area .header-right-box .cart-header {
        margin-right: 50px
    }

    .header-transparent .main-menu-area .header-right-box .cart-header:before {
        content: "";
        position: absolute;
        width: 1px;
        height: 24px;
        background: rgba(123, 122, 129, .2);
        top: 50%;
        right: -35px;
        transform: translateY(-50%)
    }

}

@media(min-width:1400px) {
    .header-transparent .main-menu-area .container {
        width: 1500px
    }

}

@media(min-width:1650px) {
    .header-transparent .main-menu-area .container {
        width: 1600px
    }

}

@media(max-width:991px) {
    .menu-bar span {
        display: block;
        width: 25px;
        height: 3px;
        background: #1f2642;
        margin-top: 4px
    }

    .main-menu-area {
        padding: 5px 0 !important
    }
    .main-menu-area .col-xl-1 {
        justify-content: center;
    }
    .main-menu-area .logo-white{
        display:  none;
    }
    .ws-page .main-menu-area .logo-white{
        display:  block!important;
    }

}

@media(max-width:767px) {
    .header-right-box .cart-header__text {
        display: none
    }

    .header-right-box .cart-header {
        margin-top: 6px
    }

    .header-transparent .main-menu-area .header-right-box .cart-area__icon {
        right: 35px
    }

}

@media(max-width:480px) {
    .header-transparent .main-menu-area .header-right-box .cart-area__icon {
        right: 15px
    }

    .cart-area {
        right: 25px
    }
    .contact-hotline {
        padding: 10px 40px
    }

    .customer-area {
        margin-top: 0;
    }

    .blog-area {
        padding-bottom: 0;
    }

    .map-area {
        padding-top: 0;
    }

    .web-page.home-page h1 {
        padding-top: 30px;
    }

    .outstanding-features .col-12 {
        margin-top: 0 !important;
    }

    .outstanding-features .outstanding-features__item {
        padding-bottom: 0;
    }

    .web-page.home-page .premium-box .owl-dots {
        position: unset;
        transform: unset;
    }

    .section.premium-theme .content-theme {
        margin-bottom: 30px;
        padding-top: 0px;
    }

    .web-page.home-page .customer-area {
        margin-bottom: 0;
    }

    .web-page.home-page .question-area {
        padding: 35px 0;
    }
    .footer-area {
        padding-top: 30px !important;
    }
    .footer-middle-area {
        padding: 30px 0 0 0 !important;
    }
}

/* menu blog page */
.blog-page .menu li a:not(.actmenu) {
    background: unset;
}

.blog-page .menu li a {
    padding: 5px 10px;
}

.blog-page .menu li a:hover, .blog-page .menu li a.actmenu:hover {
    color: #ff7800;
}

.blog-page .menu li a.actmenu:hover {
    color: #fff;
}

.blog-page .menu li {
    margin-right: 10px;
}

.blog-page .menu li.active {
    background: #ff7800;
}

.blog-page .menu li.active a {
    color: #fff;
}

/* end menu blog page */

/* submenu new version */
@media(min-width: 1024px){
    .web-page .extra-header .logo a:after{
      display: none;
    }
    .web-page .extra-header .menu ul li a,
    .ema-page .service-type-ema.extra-header .menu ul li a {
      color: #484848!important;
    }
    .web-page .header-transparent .main-menu-area{
      box-shadow: none;
      border: 1px solid #E8E8E8;
    }
    .extra-header .menu{
      justify-content: center;
      background: #fff;
      position: relative;
    }
    .extra-header:not(.sticky) .menu{
        height:  55px;
    }
    .extra-header.sticky .menu:before{
        content: "";
        left: 10px;
        height: 19px;
        width: 1px;
        background: #42464e;
        position: absolute;
        top: 57%;
        z-index: 9999;
        transform: translateY(-50%);
    }
    .extra-header .menu .btn-zozo{
      background: #ff7800;
      color: #fff;
      border-radius: 20px;
      height: auto;
      line-height: 1.5;
      padding: 8px 30px;
      min-width: 120px;
      border: 1px solid #ff7800!important;
    }
    .extra-header .menu .btn-zozo:hover{
      background: #fff;
      color: #ff7800;
    }
    .extra-header .menu ul li:first-child{
      margin-left: 0;
    }
    .decor-img{
      position: absolute!important;
      top: 0;
      z-index: 9!important;
    }
    .decor-img img{
      width: auto!important;
      position: unset!important;
    }
    .decor-left{
      left: -57px;
    }
    .decor-left + div{
      position: relative;
    }
    .decor-right{
      right: -57px;
      transform: rotateY(180deg);
    }
    .extra-header .menu_col{
      display: flex;
    }
    .extra-header .menu ul,
    .extra-header .menu ul li{
        margin:  0!important;
    }
    .extra-header .menu_col:first-child{
        margin-right:  30px!important;
        justify-content: flex-end;
    }
    .extra-header .menu_col:last-child{
        margin-left:  30px!important;
    }
    .extra-header .menu_col:first-child li{
        margin-left:  30px!important;
    }
    .extra-header .menu_col:last-child li{
        margin-right:  30px!important;
    }
    .extra-header.sticky .decor-img{
      display: none;
    }
    .extra-header.sticky .menu{
      display: block;
      padding-top: 20px;
    }
    .extra-header.sticky .menu .menu_col{
      margin: 0!important;
    }
    .extra-header.sticky .menu .menu_col:first-child{
      margin-right: 30px!important;
    }
    .extra-header .menu:before{
      display: none;
    }
    .extra-header.sticky .menu:before{
      display: block;
    }
    .extra-header.sticky .btn-zozo{
      position: absolute;
      top:  -10px;
      right: 0;
    }
    .extra-header .menu_col{
      flex: 0 0 38%;
      max-width: 38%;
    }
    .extra-header .btn-zozo{
      flex: 0 0 20%;
      max-width: 20%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .extra-header:not(.sticky) .menu{
      width: auto;
      min-width: 800px;
    }
    .main-menu{
        margin-left: 20px;
    }
    .web-page .extra-header .menu ul li.active a{
        font-weight: normal!important;
    }
    .web-page .header-2-right .header-cta-text,
    .ema-page .header-2-right .header-cta-text,
    .ws-page .header-2-right .header-cta-text{
      /*border: 1px solid #E8E8E8;
      border-radius: 20px;
      padding: 8px 30px;*/
    }
    .web-page .header-2-right .header-cta-text p,
    .ema-page .header-2-right .header-cta-text p,
    .ws-page .header-2-right .header-cta-text p{
      font-weight: 600;
    }
    /* when scroll */
    .extra-header.sticky .menu_col{
      display: inline-block;
    }
    .extra-header.sticky .menu_col li{
      display: inline-block;
    }
    .extra-header.sticky .decor-colorful + div{
      display: block!important;
    }
    .ws-page .extra-header.sticky .decor-img + div,
    .ema-page .extra-header.sticky .decor-img + div,
    .omnichannel-page .extra-header.sticky .decor-img + div{
      display: block!important;
    }
    /* end srcoll */
    .ws-page main .extra-header .logo a img, .ema-page main .extra-header .logo a img{
      margin-right: 0;
    }
    .ws-page .extra-header .logo a:after, .ema-page .extra-header .logo a:after{
      top: 65%;
      right: -15px;
    }
    .ws-page .extra-header .logo a:after{
        background: #21283b!important;
    }
    .ws-page .service-type-ws.extra-header.sticky .logo a:after, .ema-page .service-type-ema.extra-header.sticky .logo a:after{
      background: #111!important;
    }
    .ws-page .extra-header:not(.sticky) .menu {
        background: #21283b;
    }
    .ws-page .main-menu-area{
        background: #21283b;
    }
    .ws-page .main-menu-area:before{
        display: none;
    }
    .ws-page .extra-header .menu .btn-zozo:hover{
        border: none;
    }
    .ws-page .extra-header .logo{
        padding-top: 25px;
    }
    .ws-page .extra-header .menu{
        padding-top:  6px;
    }
    .packages-page.bang-gia .extra-header:not(.sticky) .decor-colorful{
      display: block!important;
    }
    .packages-page.bang-gia .extra-header:not(.sticky) .menu{
      box-shadow: 20px 20px 40px rgb(0 0 0 / 5%);
    }
    @media (min-width: 1024px) {
        .packages-page.bang-gia-web-services .extra-header:not(.sticky) .menu {
            box-shadow: 20px 20px 40px rgb(0 0 0 / 5%);
        }
        .packages-page.bang-gia-email-marketing .extra-header:not(.sticky) .menu {
            box-shadow: 20px 20px 40px rgb(0 0 0 / 5%);
        }
    }
}

/* bang gia v2 */
:root{
    --packages-color: #6D6D6D;
}
.packages-page main{
  background: none;
}
.packages-page .packages-content{
  background: none;
}
.packages-page .packages-page-title{
  margin-top: 50px;
}
.packages-page .packages-content .section-title{
  padding-top: 0;
}
.packages-page .packages-desktop{
  background: #F8F8F8;
  border-radius: 100px;
  padding: 10px;
  width: 490px;
  margin: 0 auto;
}
.packages-page .packages-desktop .nav-item .nav-link.active{
  background: #FFFFFF;
  box-shadow: 5px 5px 20px rgba(0, 0, 0, 0.1);
  border-radius: 100px;
}
.packages-page .packages-desktop .nav-item .nav-link.active{
  border-radius: 100px;
}
.packages-page .packages-desktop .nav-item .nav-link{
  background: none;
  border: none!important;
  padding: 5px 10px;
}
.packages-page .packages-desktop .nav-item a{
  padding: 0;
  border: none;
}
.head-packages ul li .nav-link.active:before{
  display: none;
}
.packages-page .packages-desktop .nav-tabs .nav-link span{
  font-size: 10px;
  color: var(--packages-color);
}
.packages-page .packages-desktop .nav-tabs .nav-link .first-name{
  font-size: 16px;
  font-weight: bold;
  padding: 0;
  margin: 0;
}
.packages-page .packages-content .nav .nav-item:hover .nav-link{
  color: #ff7800;
}
.packages-page .packages-content .nav .nav-item .nav-link{
  font-size: 14px;
  border: none;
}
.packages-page .packages-content .nav .nav-item .nav-link span{
  font-size: 10px;
  color: var(--packages-color);
}
.packages-page .packages-content .nav{
  border: none;
}
.packages-page .packages-content .nav-link.active{
  margin: 0;
}
.packages-page .packages-content .nav-link.active:after{
  content: "";
  position: absolute;
  left: 50%;
  bottom: 0;
  width: 40px;
  height: 2px;
  transform: translateX(-50%);
  background: #ff7800;
}
.packages-page .packages-content .package-name{
  color: #ff7800;
}
.packages-page .currency{
  font-size: 12px;
  color: #969696;
  top: -20px;  
  left: 3px;
}
.packages-page .package-price {
  position: relative;
}
/* .packages-page .package-price p.package-year{
    position: relative;
    top: -25px;
    right: -65px;
    font-size: 10px;
    display: inline-block;
} */
.package-price .package-year {
  position: absolute;
    top: 27px;
    right: -35px;
    font-size: 10px;
    display: inline-block;
    text-transform: uppercase;
    font-weight: 700;
    opacity: .5;
    min-width: 56px;
    text-align: left;
}
.packages-page .package-text{
  font-size: 12px;
  color: var(--packages-color);
  margin: 0 15px;
  overflow: hidden;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  min-height: 50px;
  display: -webkit-box;
}
.packages-page .packages-item{
  padding-bottom: 120px;
}
.packages-page .packages-ema #ema-pricing-dedicated .packages-item {
     padding-bottom: 0px !important;
}
.packages-page .packages-main .col-xl-3{
  position: relative;
}
.packages-page .pack-footer{
  position: absolute!important;
  width: 100%;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 20px;
}
.pack-footer .btn{
  border: 1px solid #ff7800;
  display: block;
  border-radius: 100px;
  max-width: 250px;
  margin: 0 auto;
  color: #ff7800;
  font-weight: bold;
  text-transform: uppercase;
  height: 34px;
  line-height: 34px;
}
.pack-footer .btn.btn-zozo{
  margin-bottom: 15px;
}
.pack-footer .btn-pre-trial{
  font-size: 12px;
  text-decoration: underline;
}
.pack-footer .btn.btn-zozo:hover{
  background: #ff7800;
  color: #fff;
}
.packages-hot + .pack-footer .btn.btn-zozo{
  background: #ff7800;
  color: #fff;
}
.package-header:before{
  display: none;
}
.packages-page .packages-main .position-relative{
  position: relative;
  height: 100%;
}
.pack-item .checkmark{
  border-color: #ff7800;
}
.pack-common .pack-item{
  font-size: 14px;
  color: var(--packages-color);
}
.price-list .packages-web ul li .nav-link{
  min-width: unset;
}
.packages-page .price-list .packages-content .nav .nav-item .nav-link span{
  color: #fff;
}
.padding-price .package-year{
  right: 50px!important;
}

#zpage .btn{
  background: #fff;
  border: 1px solid #F9B51E;
  color: #F9B51E;
}
#zpage .col-xl-3:nth-child(3) .btn{
  background: #F9B51E;
  color: #fff;
}
#zpage .btn:hover{
  background: #F9B51E;
  color: #fff;
}
.packages-ema .btn{
  background: #fff;
  border: 1px solid #670fd3;
  color: #670fd3;
}
.packages-ema .btn:hover{
  background: #670fd3!important;
  color: #fff;
}
.packages-ema .col-xl-3:nth-child(2) .btn{
  background: #670fd3;
  color: #fff;
}

@media(max-width:  767px){
    .pack-common{
        height:  unset!important;
        overflow:  unset!important;
    }
    .head-packages .nav-tabs .nav-link span:nth-child(3){
        min-height: unset;
    }
    .head-packages .nav{
        width: 100%;
    }
    .packages-page .packages-desktop{
        width: 100%;
    }
    .packages-table-main .packages-main > .row{
        width: 100%;
        flex-wrap: nowrap;
        overflow-x: scroll;
        overflow-y: hidden;
        margin: 0;
        padding: 0;
    }
    .packages-table-main .col-xl-12{
        padding: 0;
    }
    .pack-common:before{
        display: none!important;
    }
    .packages-main .packages-item{
        height:  100%;
    }
    .packages-main .row > div.col-xl-3:nth-child(3){
        margin-top:  44px!important;
    }
    .packages-main .col-xl-3{
        max-width: 80%;
    }
    .packages-page .packages-item {
        padding-bottom: 120px!important;
    }
    .packages-page .package-text{
        padding: 10px 0;
    }
    /*.packages-page .package-price p.package-year{
        right:  80px;
    }*/
    .packages-page .packages-item{
        border: none;
    }
    .packages-page .pack-item{
        overflow: unset;
    }
    .packages-main .packages-item.packages-hot{
        box-shadow: 4px 6px 10px rgba(0, 0, 0, 0.05)!important;
    }
    .price-list .packages-content .packages-item{
      height: 100%!important;
    }
    .pack-item{
        display: block;
    }
    #zema .packages-main .row > div.col-xl-4:nth-child(2){
        margin-top: 0;
    }
    #zema .packages-hot{
        padding-top: 55px!important;
    }
    #zema .packages-main .row > div.col-xl-3:nth-child(3){
        margin-top: 0!important;
    }
    #zpage .padding-price .package-year{
        right: 65px!important;
    }
    .package-price .package-year{
        padding-left: 0;
    }
}
/* end bang gia v2 */

/* customer v2 */
.customer-area{
    background: #F1F1F1
}
.customer-list-home .customer-item{
  max-width: unset;
}
.customer-area .section-title__description{
  font-size: 17px;
  color: var(--packages-color);
}
.customer-area
.achievement-wrapper{
  padding: 20px;
  background: #FFFFFF;
  border: 1px solid #E8E8E8;
  border-radius: 8px;
}
.achievement-wrapper .section-title__title{
  margin-bottom: 5px;
}
.achievement-wrapper .section-title__description{
  font-size: 16px
}
.achievement-bottom{
  margin-top: 20px;
  padding-top: 25px;
  border-top: 1px solid #F7F7F7;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.achievement-bottom .section-title__description{
  margin-right: 20px;
} 
.achievement-img{
  margin-right: 20px;
}
.achievement-img:last-child{
  margin-right: 0;
}
@media(min-width: 991px){
    .customer-area .customer-headline .section-title__description{
        max-width: 80%;
    }
    .achievement-wrapper{
        position: relative;
    }
    .achievement-wrapper .achievement-img__blur{
        position: absolute;
        right: 10px;
        top: 5px;
    }
}
@media(max-width: 767px){
    .achievement-bottom{
        position: relative;
        padding-top: 40px
    }
    .achievement-bottom .section-title__description{
        position: absolute;
        top: 20px;
        right: 0;
        margin: 0!important;
    }
    .achievement-img{
        margin-top: 5px;
    }
    .section-title__title{
        font-size: 24px;
    }
}
@media(max-width: 480px){
    .customer-area__achievement .col-xl-6 + .col-xl-6{
        margin-top: 20px;
    }
    .call-livechat span.text-or {
        display: block;
        margin: 5px 0;
    }
}
/* end customer v2 */

/* end submenu new version */

/* app page */
:root {
    --color-title: #383838;
    --color-sub_title: #FF7800;
    --color-description: #8F8F8F;
    --fs-title: 36px;
    --fs-sub_title: 24px;
    --fs-description: 20px;
    --fw-title: bold;
    --fw-description: 500;
}

.app-page .app-headline{
  color: var(--color-title);
  font-size: 36px;
  font-weight: var(--fw-title);
}
.app-page .app-title{
  color: var(--color-title);
  font-size: var(--fs-title);
  font-weight: var(--fw-title);
}
.app-page .app-sub_title{
  color: var(--color-sub_title);
  font-size: var(--fs-sub_title);
  font-weight: var(--fw-description);
}
.app-page .app-description{
  color: var(--color-description);
  font-size: var(--fs-description);
  font-weight: var(--fw-description);
}
.app-page .section{
  padding-top: 0;
  padding-bottom: 80px;
  /* margin: 0; */
}
.app-main{
  position: relative;
  padding-top: 105px!important;
}
.app-main__bg{
  position: absolute;
  top: 0;
  right: 0;
}
.app-intro__img{
  position: relative;
}
.app-intro__img:before{
  content: "";
  position: absolute;
  width: 75px;
  height: 75px;
  left: 50%;
  top: 52%;
  transform: translate(-50%, -52%);
  z-index: 9;
  cursor: pointer;
}
.app-feature__content .col-xl-8 img{
  width: 100%;
}
.app-feature__box{
  background: linear-gradient(152.6deg, #FAFAFA 0%, #FFFFFF 100%);
  box-shadow: 5px 5px 12px rgba(0, 0, 0, 0.05);
  border-radius: 15px;
  padding: 30px;
}
.app-feature__revenue{
  margin-bottom: 15px;
}
.app-feature__revenue .app-feature__img{
  text-align: center;
  padding-bottom: 75px;
}
.app-feature__title{
  font-size: var(--fs-sub_title);
  font-weight: var(--fw-description);
  color: var(--color-title);
}
.app-feature__desc{
  font-size: 16px;
  color: var(--color-description);
}
.app-feature-absolute{
  position: relative;
}
.app-feature-absolute .app-feature__text{
  position: absolute;
  bottom: 10px;
  padding: 30px;
}
.app-feature__order-statistic .app-feature__text{
  right: 10px;
}
.app-feature__order-statistic .app-feature__text{
  right: -20px;
}
.app-feature__channel{
  padding: 30px;
}
.app-feature__setting{
  padding: 0;
}
.app-feature__setting img{
  padding-left: 60px;
  padding-right: 60px;
}
.app-feature__setting .app-feature__text{
  padding: 15px 30px;
}
.choose_us{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.choose_us .app-title{
  font-size: var(--fs-sub_title);
}
.choose_us .app-sub_title{
  font-size: var(--fs-sub_title);
}
.list-choose li{
  position: relative;
  padding-left: 30px;
  padding-bottom: 10px;
  color: var(--color-description);
}
.list-choose li:before{
  position: absolute;
  content: "";
  width: 8px;
  height: 8px;
  left: 0;
  top: 6px;
  background: #ff7800;
  border-radius: 100%;
}
.choose-even{
  justify-content: right;
}
.choose-even .list-choose li{
  padding-left: 0;
  padding-right: 30px;
}
.choose-even .list-choose li:before{
  left: unset;
  right: 0;
}
.step-content .app-title{
  font-size: 20px;
  text-transform: unset;
}
.step-content .app-description{
  font-size: 16px;
}
.step-number{
  position: relative;
  display: inline-block;
  background: #ff7800;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  padding: 4px 12px;
  border-radius: 100%;
}
.step-number:after{
  content: "";
  position: absolute;
  left: 0;
  top: 6px;
  border-bottom: 18px solid #ff7800;
  border-top: 18px solid transparent;
  border-right: 18px solid transparent;
}
.app-box_shadow{
  background: #FFFFFF;
  box-shadow: 0px 12px 40px rgba(143, 143, 143, 0.2), inset 5px 5px 12px rgba(255, 255, 255, 0.4), inset -5px -5px 12px rgba(0, 0, 0, 0.07);
  border-radius: 10px;
}
.app-main .app-qr__left .app-title{
  font-size: 16px;
  text-transform: unset;
  position: relative;
}
.app-page .app-qr__left .app-description{
  font-size: 14px;
}
.app-main .app-qr__left .app-title:before{
  display:  none;
  content: "";
  position: absolute;
  background: #C4C4C4;
  width: 1px;
  height: 40px;
  right: 0;
  top: 10px;
}
.app-main .app-qr__left{
  padding: 10px 20px;
}
.app-main .app-title__QR{
    margin-top: 15px;
}
.app-main .app-qr__left .row > div:first-child > .row{
    border-right: 1px solid #C4C4C4;
}
.app-main .app-qr{
  margin: auto 0;
}
.app-download__box{
  background: #FFFFFF;
  box-shadow: 0px 12px 40px rgba(143, 143, 143, 0.2), inset 5px 5px 12px rgba(255, 255, 255, 0.4), inset -5px -5px 12px rgba(0, 0, 0, 0.07);
  border-radius: 10px;
  padding: 20px 30px;
  position: relative;
  z-index: 2;
}
.app-download__box .app-title{
  font-size: 20px;
  min-height: unset;
}
.app-download__box .app-description{
  font-size: 16px;
}
.app-download__box a, .app-download__box img{
  display: block;
  width: 100%;
}
.app-download__box > .row > .col-xl-7{
  position: relative;
}
.app-download__box > .row > .col-xl-7:before{
  content: "";
  position: absolute;
  background: #C4C4C4;
  width: 1px;
  height: 40px;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.app-download__bg{
  position: relative;
  z-index: 1;
  height: 120px;
  background: #FF7800;
  box-shadow: inset 0px 0px 40px #DD6800;
  margin-top: -70px;
}
.app-intro__btn{
  content: "";
  position: absolute;
  background: transparent;
  border: none;
  width: 75px;
  height: 75px;
  left: 50%;
  top: 52%;
  transform: translate(-50%, -52%);
  z-index: 9;
  cursor: pointer;
}
.app-download__box .app-description{
  min-height: unset;
}
.toc_link.toc_H2 {
    margin-left: 0px;
}
.toc_link.toc_H3 {
    margin-left: 10px;
}
.toc_link.toc_H4 {
    margin-left: 30px;
}
.toc_link.toc_H5 {
    margin-left: 50px;
}
.toc_link.toc_H5 {
    margin-left: 50px;
}
.table-of-content {
    padding: 15px;
    gap: 15px;
    background: #FAFAFA;
    border: 1px solid #000000;
    border-radius: 4px;
    order: 1;
    margin: 20px 0;
}
.table-of-content .table-of-content__title h4 {
    margin: 0;
}
.table-of-content .toc_link  {
    counter-increment: List;
    list-style: none;
}
.table-of-content .toc_H2 + .toc_H3 {
    counter-reset: List;
}
#table-of-content .toc_H2 a,
#table-of-content .toc_H3 a,
#table-of-content .toc_H4 a {
    color: #000;
    text-underline: none;
}
#table-of-content .toc_H2 a:before,
#table-of-content .toc_H3 a:before,
#table-of-content .toc_H4 a:before {
    line-height: 1.2;
    font-family: inherit;
    /*content: counters(List,".");*/
    margin-right: 10px;
}
#table-of-content .toc_H2 a {
    font-weight: bold;
}
@media(min-width: 1980px){
  .app-main__bg img{
    display: none;
  }
}
@media(min-width: 1600px){
  .app-main__bg img{
    width: 758px;
    height: 520px;
  }
  .app-main .container{
    width: 1440px;
  }
}
@media(min-width: 1440px){

}
@media(min-width: 1024px){
  .app-feature__statistic .app-feature__text{
      max-width: 60%;
    }
      .app-feature-wrapper{
      margin-bottom: 15px;
    }
    #myModalApp .modal-dialog{
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
    }

}
@media(max-width: 1380px){
  .app-main__bg img{
    display: none;
  }
}
@media(max-width: 767px){
  .app-main{
    padding-top: 60px;
  }
  .app-main .app-headline, .app-main .app-sub_title, .app-main .app-description{
    text-align: center;
  }
  .app-page .app-headline{
    font-size: 24px;
  }
  :root {
    --fs-title: 24px;
    --fs-sub_title: 16px;
    --fs-description: 16px;
  }
  .app-sub_title{
    margin: 0;
  }
  .app-qr__left{
    margin-bottom: 30px;
  }
  .app-main .app-qr__left .row > div:first-child > .row{
    border: none;
  }
  .app-main .app-description {
    text-align: left;
  }
  .app-main .app-link{
    padding: 10px 25px;
    margin-bottom: 20px;
  }
  .app-link .app-title{
    font-size: var(--fs-sub_title);
    text-transform: unset;
    padding-top: 10px;
  }
  .app-link .app-description{
    text-align: left;
  }
  .app-feature__revenue{
    margin-top: 15px;
  }
  .app-feature__desc{
    margin-bottom: 60px;
  }
  .app-feature__revenue .app-feature__img{
    padding-bottom: 40px;
  }
  .app-feature__order-statistic .app-feature__text{
    right: 10px;
  }
  .app-feature-wrapper{
    margin-bottom: 15px;
  }
  .app-feature__desc{
    margin-bottom: 30px;
  }
  .choose_us{
    margin-top: 15px;
    display: block;
  }
  .choose-first__content{
    margin-top: 20px;
    padding-left: 40px;
  }
  .list-choose{
    margin-top: 15px;
  }
  .choose-even .choose-content{
    margin-right: 40px;
    margin-top: 15px;
  }
  .app-step{
    margin-left: 0;
    padding: 0 15px 35px 15px!important;
  }
  .step-content .app-title{
    font-size: 18px;
  }
  .app-download__box .app-title{
    font-size: var(--fs-sub_title);
    text-transform: unset;
    padding-top: 10px;
  }
  .app-download__box .app-description{
    color: var(--color-description);
    font-size: var(--fs-description);
    font-weight: var(--fw-description);
  }
  .app-download__box{
    margin: 0 15px;
  }
  .app-download__bg{
    height: 74px;
    margin-top: -45px;
  }
  .app-download > .app-title{
    padding: 0 15px;
  }
  .app-download__box > .row > .col-xl-7:before{
    display: none;
  }
  .app-download__box{
    padding: 15px 10px;
  }
  .app-intro__img{
    position: unset;
  }
}
/* end app page */


.order-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1
}

.order-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13
}

.order-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
}

.order-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
}

.order-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
}

.order-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
}

@media (min-width: 576px) {
    .order-sm-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .order-sm-last {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13
    }
    .order-sm-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .order-sm-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .order-sm-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }
    .order-sm-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }
}

@media (min-width: 768px) {
    .order-md-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .order-md-last {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13
    }
    .order-md-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .order-md-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .order-md-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }
    .order-md-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }
}

@media (min-width: 992px) {
    .order-lg-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .order-lg-last {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13
    }
    .order-lg-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .order-lg-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .order-lg-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }
    .order-lg-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }
}

.subpage-content {
    padding-top: 72px;
}
@media (min-width: 1200px) {
    .container {
        width: 1140px;
        max-width: 100%;
    }
}

@media (min-width: 992px) {
    .container {
        width: 960px;
        max-width: 100%;
    }
}

@media (min-width: 768px) {
    .container {
        width: 720px;
        max-width: 100%;
    }
}


@media (min-width: 1200px) {
    .container {
        padding-right: 15px;
        padding-left: 15px;
    }
}

@media (min-width: 992px) {
    .container {
        padding-right: 15px;
        padding-left: 15px;
    }
}



@media (min-width: 1200px) {
    .order-xl-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .order-xl-last {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13
    }
    .order-xl-0 {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .order-xl-1 {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .order-xl-2 {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }
    .order-xl-3 {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3
    }
}

/** register success **/
.register-success {
    padding: 20px 60px;
    border-radius: 8px;
}

.register-success br {
    margin: 0;
}

.register-success__sub-title {
    color: #272932;
    font-weight: 600;
}

.register-success__sub-desc {
    color: #8F8F8F;
}

.register-success__avatar {
    width: 32px;
    height: 32px;
    margin-right: 10px;
}

.register-success__avatar img{
    width: 32px;
    height: 32px;
    margin-right: 10px;
    border-radius: 100%;
}

.register-success__info .row:not(.register-success__person) {
    max-width: 380px;
    margin: 0 auto;
}

.register-success__person {
    max-width: 350px;
    margin: 0 auto;
    background: #F0F0F0;
    border-radius: 4px;
    padding: 5px 0;
    margin-bottom: 30px;
}

.register-success__change-time {
    font-size: 12px;
}

.register-success__change-time a {
    color: #0275d8;
}

.register-success__change-time a:hover {
    text-decoration: underline;
}

.register-success__bottom {
    margin-top: 10px;
}

.register-success__bottom a {
    display: inline-block;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - (1.75rem * 2));
    }
}

@media(min-width: 991px) {
    .register-success {
        max-width: 620px;
        margin: 0 auto;
    }
}

@media(max-width: 767px) {
    .register-success {
        padding: 15px 0;
        margin: 0;
    }

    .register-success__title {
        font-size: 28px;
    }

    .register-success__sub-title,
    .register-success__sub-desc {
        font-size: 14px;
    }

    .register-success__wrap{
        padding: 60px 15px!important;
    }
}

#change-time .modal-header {
    padding: 15px;
}

#change-time .close:before,
#change-time .close:after {
    display: none;
}

#change-time .modal-dialog {
    pointer-events: none;
}

#change-time .modal-content {
    pointer-events: auto;
    width: 100%;
}

.modal-dialog-centered {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    min-height: calc(100% - (0.5rem * 2));
}

/* change time */
@media(min-width: 991px) {
    #change-time .modal-content {
        min-width: 520px;
    }

    #change-time .input-group {
        max-width: 350px;
        margin: 0 auto;
    }
}

#change-time .input-group-append {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    background: #F0F0F0;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 0px 2px 2px 0px;
}

#change-time #change-time__input {
    border-right: 0;
}

.change-time__box-button {
    margin-top: 10px;
    margin-bottom: 20px;
}

@media(min-width: 991px) {

    #change-time__success .modal-dialog,
    #change-time__success .modal-content {
        min-width: 650px;
        margin: 0 auto;
    }
}

/** end register success **/
