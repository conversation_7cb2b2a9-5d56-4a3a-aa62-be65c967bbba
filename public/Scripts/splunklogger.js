/**
 * This method posts a string to splunk immedeately
 * In general this method should not be called unless there is a specific use case. The QueueSplunkEntry is preferred and posts logs at the end of a users session or after 10 logs have been collected.
 *
 * @param {string} Message Custom string to post to splunk, must be <= 10000 characters
 */
function PostStringToSplunk(Message) {
    try {  //Non-critical function, if it errors just keep on chugging
        var data = JSON.stringify({
            'Message': Message //Needed for ASP to recognize the parameter correctly
        });

        $.ajax({
            type: "POST",
            url: '/Events/LogMessageToSplunk',
            data: data,
            async: true,
            contentType: 'application/json',
            success: function () { console.log('Succesfully posted to splunk'); Success = true; },
            error: function () { console.log('Splunk post failed') }
        });
    } catch { }
}

/**
 * This method posts an array of strings to splunk immedeately
 * In general this method should not be called unless there is a specific use case. The QueueSplunkEntry is preferred and posts logs at the end of a users session or after 10 logs have been collected.
 *
 * @param {string} Messages Custom string to post to splunk, must be <= 10000 characters per message
 */
function PostStringArrayToSplunk(Messages) {

    try {  //Non-critical function, if it errors just keep on chugging
        var data = JSON.stringify({
            'Messages': Message //Needed for ASP to recognize the parameter correctly
        });

        $.ajax({
            type: "POST",
            url: '/Events/LogMessagesToSplunk',
            data: data,
            async: true,
            contentType: 'application/json',
            success: function () { console.log('Succesfully posted to splunk'); Success = true; },
            error: function () { console.log('Splunk post failed') }
        });
    } catch { }
}


/**
 * This function logs a string to a variable in local storage called 'SplunkMessages' which will get called at either the end of a users session or once 10 have built up to send the data to splunk
 * The big benefits of this are that it centeralizes all logging and ensures that a message can be captured at any given time throughout a session
 *
 * @param {string} Message Custom string to post to splunk EX: 'Event: ButtonXClicked MiscData: YourDataHere', usually colon + space delimited, must be <= 10000 characters assuming no options are used
 * @param {string[]} Options Optional - EX: var MyOptions = [SplunkOptions.UserId, SplunkOptions.Country]
 */
function QueueSplunkEntry(Message, Options) {
    try {  //Non-critical function, if it errors just keep on chugging
        if (Options) {
            var UserDataArray = GetUserData(Options);
            var UserDataString = UserDataArray.join(' ');
            Message += ' ' + UserDataString;
        }

        var Messages = JSON.parse(localStorage.SplunkMessages || '[]')
        Messages.push(Message)

        if (Messages.length < 10) { //For users that never close or click off their tabs and keep reusing the same one
            localStorage.SplunkMessages = JSON.stringify(Messages);
        }
        else {
            PostStringArrayToSplunk(Messages);
            localStorage.removeItem('SplunkMessages')
            }
    } catch { }
}

/**
 * Manually send out all messages stored in local storage -- this overrides the normal route where the top level of PTS waits for the user session to end
 */
function PostQueuedSplunkMessages() {


    if (localStorage['SplunkMessages']) {
        var Messages = JSON.parse(localStorage['SplunkMessages'])
        localStorage.removeItem('SplunkMessages');
        //This method is used isntead of the splunklogger.js method because ajax calls will fail if the tab is closed where as the fetch + keepalive will work
        fetch('/Events/LogMessagesToSplunk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: '{"Messages":\'' + JSON.stringify(Messages) + '\'}',
            keepalive: true
        });
    }
}

//Corresponds with each if statement in the GetUserData function
const SplunkOptions = {
    
    Browser: 'Browser',
    Country: 'Country',
    DealerCode: 'DealerCode',
    DeviceType: 'DeviceType',
    Environment: 'Environment',
    Flavor: 'Flavor',
    IP: 'IP',
    Language: 'Language',
    UserId: 'UserId',
    VIN: 'VIN',
    PGroup: 'pGroup',
    isFreeSubscription: 'isFreeSubscription',
    Market: 'Market',
    Region: 'Region',
    Model: 'Model',
    Year: 'Year',
    Brand: 'Brand'
   
}

/**
 * Null safe way to check against cookie for a value using Regex
 * @param {string} RegexString
 * @returns {string} Match containing value contained within the cookie, or 'N/A' if no value is found
 */
function GetCookieValueFromRegex(RegexString) {
    try {
        var Regex = new RegExp(RegexString, 'mi');
        return document.cookie.match(Regex)[0]; //This will throw an error if no value is found
    }
    catch {
        return 'N/A'
    }
}

/**
 * Gets various environment variables and returns them in a string array
 *
 * @param {string[]} SplunkOptions EX: var MyOptions = [SplunkOptions.UserId, SplunkOptions.Country]
 * @return {string[]} String array ex: [ 'UserID: ford123',  'Country: US' ] 
 */
function GetUserData(Options) {
    try {  //Non-critical function, if it errors just keep on chugging
        var ReturnArray = []
        if (Options.includes('Browser')) {
            var Browser = 'Unknown';

            if ((navigator.userAgent.indexOf("Opera") || navigator.userAgent.indexOf('OPR')) != -1) {
                Browser = 'Opera';
            }
            else if (navigator.userAgent.indexOf("Edg") != -1) {
                Browser = 'Edge';
            }
            else if (navigator.userAgent.indexOf("Chrome") != -1) {
                Browser = 'Chrome';
            }
            else if (navigator.userAgent.indexOf("Safari") != -1) {
                Browser = 'Safari';
            }
            else if (navigator.userAgent.indexOf("Firefox") != -1) {
                Browser = 'Firefox';
            }
            else if ((navigator.userAgent.indexOf("MSIE") != -1) || (!!document.documentMode == true)) //IF IE > 10
            {
                Browser = 'IE';
            }
            ReturnArray.push('Browser: ' + Browser)
        }
        
        if (Options.includes('DeviceType')) {
            var DeviceType = 'Desktop'; //Check desktop vs mobile
            if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test((navigator.userAgent || navigator.vendor || window.opera)) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test((navigator.userAgent || navigator.vendor || window.opera).substr(0, 4))) {
                DeviceType = 'Mobile';
            }
            ReturnArray.push('DeviceType: ' + DeviceType)
        }
        if (Options.includes('Environment')) {
            var Environment = 'local';
            var Host = document.location.host;
            if (Host.indexOf('localhost') == -1) {
                var PrefixRegex = '([wqadevWQADEV]{3,6}(?=\.))'
                var EnvRegex = '([qadev]{2,3})'
                var Prefix = Host.match(PrefixRegex)?.[0] || '';
                Environment = Prefix.match(EnvRegex)?.[0] || 'prod';
            }
            ReturnArray.push('Environment: ' + Environment)
        }        

        if (Options.includes('IP')) {
            $.getJSON("https://api.ipify.org/?format=json", function (response) { //Call ipify.org and get the clients IP, mainly for cybersecurity purposes
                sessionStorage['IP'] = "************" //Done like this because javascript doesnt expose variables from inside a call like this to the parent function
            })
            if (sessionStorage['IP']) {
                ReturnArray.push('IP: ' + sessionStorage['IP'])
                sessionStorage.removeItem('IP');
            }        
        }        
   
        if (Options.includes('Country')) {
            var CountryCode = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&country=).*?(?=&|;)');
            ReturnArray.push('Country: ' + CountryCode)
        }   

        if (Options.includes('Language')) {
            var LanguageCode = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&language=).*?(?=&|;)');
            ReturnArray.push('Language: ' + LanguageCode)
        }   

        if (Options.includes('Flavor')) {
            var Flavor = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&flavor=).*?(?=&|;)');
            ReturnArray.push('Flavor: ' + Flavor)
        }

        if (Options.includes('DealerCode')) {
            var DealerCode = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&pacode=).*?(?=&|;)');
            ReturnArray.push('DealerCode: ' + DealerCode)
        } 

        if (Options.includes('UserId')) {
            var UserId = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&userid=).*?(?=&|;)');
            ReturnArray.push('UserId: ' + UserId)
        }

        if (Options.includes('VIN')) {
            var VIN = window.top.document.getElementById('header-vin').innerHTML;
            ReturnArray.push('VIN: ' + VIN)
        }

        if (Options.includes('pGroup')) {
            var PGroup = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?pgroup=).*?(?=&|;)');
            if (PGroup) ReturnArray.push('PGroup: ' + PGroup)
        }

        if (Options.includes('isFreeSubscription')) {
            var isFreeSubscription = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?isFreeSubscription=).*?(?=&|;)');
            if (isFreeSubscription) ReturnArray.push('isFreeSubscription: ' + isFreeSubscription)
        } 
        
        if (Options.includes('Market')) {
            var Market = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?marketGroup=).*?(?=&|;)');
            ReturnArray.push('Market: ' + Market)
        }
        
        if (Options.includes('Region')) {
            var Region = GetCookieValueFromRegex('(?<=TPS%2DMEMBERSHIP=.*?region=).*?(?=&|;)');
            ReturnArray.push('Region: ' + Region)
        } 

        if (Options.includes('Model')) {
            var Model = window.top.ptsSession.model;
            ReturnArray.push('Model: ' + Model)
        } 

        if (Options.includes('Year')) {
            var Year = window.top.ptsSession.vehicle.ModelYear;
            ReturnArray.push('Year: ' + Year)
        } 

        if (Options.includes('Brand')) {
            var Brand = window.top.ptsSession.vehicle.features.brand;
            ReturnArray.push('Brand: ' + Brand)
        } 

        return ReturnArray;
    } catch { }
}


/**
    * Gets various environment variables and appends them to the top level of a given JSON Object
    *
    * @param {string[]} SplunkOptions EX: var MyOptions = [SplunkOptions.UserId, SplunkOptions.Country]
    * @return {object} String array ex: [ 'UserID: ford123',  'Country: US' ] 
    */
function AppendSessionData(JSONObject, Options) {
    try {  //Non-critical function, if it errors just keep on chugging
        if (Options.includes('Browser')) {
            var Browser = 'Unknown';

            if ((navigator.userAgent.indexOf("Opera") || navigator.userAgent.indexOf('OPR')) != -1) {
                Browser = 'Opera';
            }
            else if (navigator.userAgent.indexOf("Edg") != -1) {
                Browser = 'Edge';
            }
            else if (navigator.userAgent.indexOf("Chrome") != -1) {
                Browser = 'Chrome';
            }
            else if (navigator.userAgent.indexOf("Safari") != -1) {
                Browser = 'Safari';
            }
            else if (navigator.userAgent.indexOf("Firefox") != -1) {
                Browser = 'Firefox';
            }
            else if ((navigator.userAgent.indexOf("MSIE") != -1) || (!!document.documentMode == true)) //IF IE > 10
            {
                Browser = 'IE';
            }

            JSONObject.Browser = Browser
        }

        if (Options.includes('DeviceType')) {
            var DeviceType = 'Desktop'; //Check desktop vs mobile
            if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test((navigator.userAgent || navigator.vendor || window.opera)) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test((navigator.userAgent || navigator.vendor || window.opera).substr(0, 4))) {
                DeviceType = 'Mobile';
            }
            JSONObject.DeviceType = DeviceType
        }
        if (Options.includes('Environment')) {
            var Environment = 'local';
            var Host = document.location.host;
            if (Host.indexOf('localhost') == -1) {
                var PrefixRegex = '([wqadevWQADEV]{3,6}(?=\.))'
                var EnvRegex = '([qadev]{2,3})'
                var Prefix = Host.match(PrefixRegex)?.[0] || '';
                Environment = Prefix.match(EnvRegex)?.[0] || 'prod';
            }
            JSONObject.Environment = Environment
        }

        if (Options.includes('IP')) {
            $.getJSON("https://api.ipify.org/?format=json", function (response) { //Call ipify.org and get the clients IP, mainly for cybersecurity purposes
                sessionStorage['IP'] = "************" //Done like this because javascript doesnt expose variables from inside a call like this to the parent function
            })
            if (sessionStorage['IP']) {
                JSONObject.IP = sessionStorage['IP']
                sessionStorage.removeItem('IP');
            }
        }

        if (Options.includes('Country')) {
            var CountryCode = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&country=).*?(?=&|;)');
            JSONObject.County = CountryCode
        }

        if (Options.includes('Language')) {
            var LanguageCode = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&language=).*?(?=&|;)');
            JSONObject.Language = LanguageCode
        }

        if (Options.includes('Flavor')) {
            var Flavor = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&flavor=).*?(?=&|;)');
            JSONObject.Flavor = Flavor
        }

        if (Options.includes('DealerCode')) {
            var DealerCode = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&pacode=).*?(?=&|;)');
            JSONObject.DealerCode = DealerCode
        }

        if (Options.includes('UserId')) {
            var UserId = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?&userid=).*?(?=&|;)');
            JSONObject.UserId = UserId
        }

        if (Options.includes('VIN')) {
            var VIN = window.top.document.getElementById('header-vin').innerHTML;
            JSONObject.VIN = VIN
        }

        if (Options.includes('pGroup')) {
            var PGroup = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?pgroup=).*?(?=&|;)');
            if (PGroup) {
                JSONObject.PGroup = PGroup
            }
        }

        if (Options.includes('isFreeSubscription')) {
            var isFreeSubscription = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?isFreeSubscription=).*?(?=&|;)');
            if (isFreeSubscription) {
                JSONObject.isFreeSubscription = isFreeSubscription
            }
        }

        if (Options.includes('Market')) {
            var Market = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?marketGroup=).*?(?=&|;)');
            JSONObject.Market = Market
        }

        if (Options.includes('Region')) {
            var Region = GetCookieValueFromRegex('(?<=TPS%2DMEMBERSHIP=.*?region=).*?(?=&|;)');
            JSONObject.Region = Region
        }

        if (Options.includes('Model')) {
            var Model = window.top.ptsSession.model;
            JSONObject.Model = Model
        }

        if (Options.includes('Year')) {
            var Year = window.top.ptsSession.vehicle.ModelYear;
            JSONObject.Year = Year
        }

        if (Options.includes('Brand')) {
            var Brand = window.top.ptsSession.vehicle.features.brand;
            JSONObject.Brand = Brand
        }

        return JSONObject;
    } catch { console.error('Failed to get session property for splunk logging') }
}

function PostJSONToSplunk(JSONObject, Options) {
    const url = '/Events/PostStringToSplunk';

    if (Options) {
        JSONObject = AppendSessionData(JSONObject, Options);
    }

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ "RawString": JSON.stringify(JSONObject) }),
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response;
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
