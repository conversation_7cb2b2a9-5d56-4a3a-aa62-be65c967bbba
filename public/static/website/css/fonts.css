/* ICOMOON */
@font-face {
	font-family: 'icomoon';
	src:url('../fonts/icomoon/icomoon.eot?ui7ny');
	src:url('../fonts/icomoon/icomoon.eot?#iefixui7ny') format('embedded-opentype'),
		url('../fonts/icomoon/icomoon.woff?ui7ny') format('woff'),
		url('../fonts/icomoon/icomoon.ttf?ui7ny') format('truetype'),
		url('../fonts/icomoon/icomoon.svg?ui7ny#icomoon') format('svg');
	font-weight: normal;
	font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
	font-family: 'icomoon';
	position: relative;
	display: inline-block;
  	top: 1px;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-recipes_icons_premium:before {
	content: "\e600";
}

.icon-lists_icons_multiple:before {
	content: "\e602";
}

.icon-recipes_icons_yours:before {
	content: "\e60d";
}