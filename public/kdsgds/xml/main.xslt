<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v2004 rel. 4 U (http://www.xmlspy.com) by <PERSON><PERSON> (ORiON) -->
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:myObj="urn:my-object">
	<!-- special char &nbsp; : &#160; -->
	<!--define parameter  -->
	<xsl:param name="groupcode"/>
	<xsl:param name="imgOpenStyleKey"/>
	<xsl:param name="dispDepth"/>
	<xsl:param name="GImgPath"/>
	<xsl:param name="lang"/>
	<xsl:param name="revdesc"/>

	<xsl:param name="s1_index" select="1"/>
	<xsl:variable name="s1_count" select="1"/>
	<!--sitsubqual일 경우 servinfo 자식 노드들만 찾아 처리한다.-->
	<xsl:template match="sitsubqual">
		<xsl:apply-templates select="servinfo"/>
	</xsl:template>
	<!--servinfo 노드에 대한 처리 -->
	<xsl:template match="servinfo">
		<xsl:apply-templates/>
		<!--
    <xsl:apply-templates select="title"></xsl:apply-templates>
	<xsl:apply-templates select="subtitle"></xsl:apply-templates>
	<xsl:apply-templates select="sitsubqualname"></xsl:apply-templates>
	<xsl:apply-templates select="table"></xsl:apply-templates>
	<xsl:apply-templates select="figure"></xsl:apply-templates>
	<xsl:apply-templates select="graphic"></xsl:apply-templates>
	<xsl:apply-templates select="diagnostic"></xsl:apply-templates>
	
	
	<xsl:apply-templates select="para"></xsl:apply-templates>
	<xsl:apply-templates select="ptxt"></xsl:apply-templates>
	<xsl:apply-templates select="spec-ptxt"></xsl:apply-templates>
	<xsl:apply-templates select="attention1"></xsl:apply-templates>
	<xsl:apply-templates select="list1"></xsl:apply-templates>
	<xsl:apply-templates select="s1"></xsl:apply-templates>
	<xsl:apply-templates select="spec"></xsl:apply-templates>
	
	<xsl:apply-templates select="waveform"></xsl:apply-templates>
	<xsl:apply-templates select="servinfosub"></xsl:apply-templates>
	<xsl:apply-templates select="servinfo"></xsl:apply-templates>
	<xsl:apply-templates select="topic"></xsl:apply-templates>
	<xsl:apply-templates select="pin-assign"></xsl:apply-templates>
	<xsl:apply-templates select="tgroup"></xsl:apply-templates>
	<xsl:apply-templates select="text"></xsl:apply-templates>	
	<xsl:apply-templates select="item"></xsl:apply-templates>
	-->
	</xsl:template>
	<!-- PDF 노드에 대한 처리 -->
	<xsl:template match="pdf">
		<iframe width="100%" height="100%" frameborder="0" scrolling="auto">
			<xsl:attribute name="src"><xsl:value-of select="."/></xsl:attribute>
		</iframe>
	</xsl:template>
	<!--servinfo 노드에 대한 처리 -->
	<xsl:template match="servinfosub">
		<xsl:apply-templates/>
	</xsl:template>
	<!--servinfo 노드에 대한 처리 -->
	<xsl:template match="topic">
		<xsl:apply-templates/>
	</xsl:template>
	<!-- s에 대한 처리 YUBIL : 070529 -->
	<!-- s numbering 기능 수정 @idx속성 이용하도록 수정 YUBIL:080424-->
	<xsl:template match="s">
		<xsl:variable name="scnt" select="1"/> 
		<table border="0" cellpadding="0" cellspacing="0" style="width:100%;padding-bottom:6;">
			<tr>
				<td width="12" valign="top" style="padding-left:3px;">
					<xsl:choose>
						<xsl:when test="name(..)='s' or name(..)='attention'">
							<span align="right" class="ptxt">
								<xsl:number value="@idx" format="(1)"/>
							</span>
						</xsl:when>					
						<xsl:otherwise>
							<span align="right" class="ptxt">
								<xsl:number value="@idx"  format="1."/>
							</span>
						</xsl:otherwise>
					</xsl:choose>
				</td>
				<td valign="top" style="padding-left:3px;">
					<xsl:apply-templates/>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					&#160;
				</td>
			</tr>
		</table>
	</xsl:template>
	<!-- s1에 대한 처리 -->
	<xsl:template match="s1">
		<table border="0" cellpadding="0" cellspacing="0" style="width:100%;padding-bottom:6;">
			<tr>
				<td width="12" valign="top" style="padding-left:3px;">
					<xsl:choose>
						<xsl:when test="name(..)='s1' or name(..)='s2' or name(..)='attention1'">
							<span align="right" class="ptxt">
								<xsl:number format="(1)"/>
							</span>
						</xsl:when>
						<xsl:otherwise>
							<span align="right" class="ptxt">
								<xsl:number format="1."/>
							</span>
						</xsl:otherwise>
					</xsl:choose>
				</td>
				<td valign="top" style="padding-left:3px;">
					<xsl:apply-templates/>
				</td>
			</tr>
		</table>
	</xsl:template>
	<!-- s2에 대한 처리 -->
	<xsl:template match="s2">
		<table border="0" cellpadding="0" cellspacing="0" style="width:100%;padding-bottom:6;">
			<tr>
				<td width="12" valign="top" style="padding-left:3px;">
					<xsl:choose>
						<xsl:when test="$groupcode='dtc' or name(..)='s1'">
							<p align="right" class="ptxt">
								<xsl:number format="(1)"/>
							</p>
						</xsl:when>
						<xsl:otherwise>
							<p align="right" class="ptxt">
								<xsl:number format="1."/>
							</p>
						</xsl:otherwise>
					</xsl:choose>
				</td>
				<td valign="top" style="padding-left:3px;">
					<xsl:apply-templates/>
				</td>
			</tr>
		</table>
	</xsl:template>

	<!-- yubil 080117-->
	<xsl:template match="list">
		<xsl:variable name="lvl" select="@lvl"/>
		<xsl:choose>
			<xsl:when test="number($lvl)>1">
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td valign="top">
							<xsl:attribute name="style">
								padding-left:
								<xsl:value-of select="((number($lvl)-1) * 15)"/>px;
							</xsl:attribute>
							<xsl:apply-templates/>
						</td>
					</tr>
				</table>
			</xsl:when>
			<xsl:otherwise>
				<xsl:apply-templates/>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template match="list1">
		<xsl:apply-templates/>
	</xsl:template>
	<xsl:template match="list2">
		<xsl:apply-templates/>
	</xsl:template>

	<!-- yubil 080117-->
	<xsl:template match="item">
		<xsl:variable name="isYesNo">
			<xsl:choose>
				<xsl:when test="child::ptxt[text()]='YES'">1</xsl:when>
				<xsl:when test="child::ptxt[text()]='NO'">2</xsl:when>
			</xsl:choose>
		</xsl:variable>
		<table border="0" cellpadding="0" cellspacing="1" width="100%">
			<tr>
				<xsl:choose>
					<!--<xsl:when test="name(parent::*[position()=1]) = 'list1'">-->
					<xsl:when test="$isYesNo=1">
						<td align="left" valign="bottom" style="width:15;">
							<img src="images/yes_icon.gif" height="21"/>
						</td>
					</xsl:when>
					<xsl:when test="$isYesNo=2">
						<td align="left" style="width:15;">
							<img src="images/no_icon.gif"/>
						</td>
					</xsl:when>
					<xsl:when test="name(..) = 's' or  name(..) = 's1' or name(..) = 's2'">
						<td align="right" style="width:15;">
							<img src="images/bit3.gif" width="10" height="10"/>
						</td>
					</xsl:when>
					<xsl:when test="name(..) = 'list' or name(..) = 'list1' or name(..) = 'list2'">
						<xsl:choose>
							<xsl:when test="$groupcode='dtc'">
								<xsl:choose>
									<xsl:when test="name(parent::*[position()=1]/parent::*[position()=1]) = 's1'">
										<td align="center" valign="top" style="width:15;">
											<img src="images/bit3.gif" style="MARGIN-TOP: 2px;"/>
										</td>
									</xsl:when>
									<xsl:otherwise>
										<td align="right" valign="top" style="width:20;">
											<img src="images/bit4.gif" style="MARGIN-TOP: 3px;"/>
										</td>
									</xsl:otherwise>
								</xsl:choose>
							</xsl:when>
							<xsl:otherwise>
								<xsl:choose>
									<xsl:when test="name(parent::*[position()=1]/parent::*[position()=1]) = 's' or name(parent::*[position()=1]/parent::*[position()=1]) = 's1'">
										<td valign="top" align="right" style="width:15px;">
											<xsl:number format="A."/>
										</td>
									</xsl:when>
									<xsl:when test="parent::*[position()=1]/@enumtype='arabicnum'" >
										<td valign="top" align="right" style="width:15px;">
											<xsl:number format="1."/>
										</td>
									</xsl:when>
									<xsl:when test="parent::*[position()=1]/@enumtype='bullet'" >
										<td valign="top" align="right" style="width:15px;">
											<span>●</span>
										</td>
									</xsl:when>
									<xsl:when test="parent::*[position()=1]/@enumtype='dash'" >
										<td valign="top" align="right" style="width:15px;">
											<span>- </span>
										</td>
									</xsl:when>
									<xsl:otherwise>
										<td valign="top" align="right" style="width:15px;">
											<xsl:number format="1)"/>
										</td>
									</xsl:otherwise>
								</xsl:choose>
							</xsl:otherwise>
						</xsl:choose>
					</xsl:when>
					<xsl:otherwise>
						<td valign="top" align="right" style="width:15px;">
							<xsl:number format="a."/>
						</td>
					</xsl:otherwise>
				</xsl:choose>
				<td style="padding-left:3px;" valign="top">
					<xsl:apply-templates/>
				</td>
			</tr>
		</table>
	</xsl:template>
	
	<xsl:template match="attention">
		<xsl:variable name="iconName">
			<xsl:choose>
				<xsl:when test="@name='note'">images/note_icon_</xsl:when>
				<xsl:when test="@name='warning'">images/warning_icon_</xsl:when>
				<xsl:otherwise>images/caution_icon_</xsl:otherwise>
			</xsl:choose>
			<xsl:value-of select="$lang"/>.gif</xsl:variable>
		<xsl:variable name="class">
			<xsl:choose>
				<xsl:when test="@name='note'">table-note</xsl:when>
				<xsl:when test="@name='warning'">table-hint</xsl:when>
				<xsl:when test="@name='hint'">table-hint</xsl:when>
				<xsl:otherwise>table-caution</xsl:otherwise>
			</xsl:choose>
		</xsl:variable>
		<table border="1" cellpadding="0" cellspacing="0" style="width:97%;margin-left:10;margin-top:10;margin-bottom:5">
			<tr>
				<td width="100%" style="padding-left:20px;padding-top:15px;padding-right:20px;padding-bottom:15px;">
					<xsl:attribute name="class"><xsl:value-of select="$class"/></xsl:attribute>
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td valign="bottom" style="padding-top:5;">
								<img>
									<xsl:attribute name="src"><xsl:value-of select="$iconName"/></xsl:attribute>
									<xsl:attribute name="style"><xsl:value-of select="'border:none'"/></xsl:attribute>
								</img>
							</td>
						</tr>
						<tr>
							<td background="images/dot_line3.gif" width="100%" height="1"/>
						</tr>
					</table>
					<xsl:apply-templates/>
				</td>
			</tr>
		</table>
	</xsl:template>
	<!-- attentions 에 대한 처리 -->
	<xsl:template match="attention1">
		<xsl:variable name="iconName">
			<xsl:choose>
				<xsl:when test="@name='note'">images/note_icon_</xsl:when>
				<xsl:when test="@name='warning'">images/warning_icon_</xsl:when>
				<xsl:otherwise>images/caution_icon_</xsl:otherwise>
			</xsl:choose>
			<xsl:value-of select="$lang"/>.gif</xsl:variable>
		<xsl:variable name="class">
			<xsl:choose>
				<xsl:when test="@name='note'">table-note</xsl:when>
				<xsl:when test="@name='warning'">table-hint</xsl:when>
				<xsl:when test="@name='hint'">table-hint</xsl:when>
				<xsl:otherwise>table-caution</xsl:otherwise>
			</xsl:choose>
		</xsl:variable>
		<table border="1" cellpadding="0" cellspacing="0" style="width:97%;margin-left:10;margin-top:10;margin-bottom:5;">
			<tr>
				<td width="100%" style="padding-left:20px;padding-top:15px;padding-right:20px;padding-bottom:15px;">
					<xsl:attribute name="class">
						<xsl:value-of select="$class"/>
					</xsl:attribute>
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td valign="bottom" style="padding-top:5;">
								<img>
									<xsl:attribute name="src"><xsl:value-of select="$iconName"/></xsl:attribute>
									<xsl:attribute name="style"><xsl:value-of select="'border:none'"/></xsl:attribute>
								</img>
							</td>
						</tr>
						<tr>
							<td background="images/dot_line3.gif" width="100%" height="1"/>
						</tr>
					</table>
					<xsl:apply-templates/>
				</td>
			</tr>
		</table>
	</xsl:template>
	<xsl:template match="title">
		<xsl:choose>
			<xsl:when test="name(..)='servinfo' or name(..)='servinfosub' or name(..)='topic'">
				<xsl:if test="$groupcode != 'etm'">
					<xsl:choose>
						<xsl:when test="text() != ''">
							<xsl:choose>
								<xsl:when test="name(..)='servinfo'">
									<table class="table-st1">
										<tr>
											<td class="table-td-st1-t">
												<xsl:value-of select="."/>
												<xsl:value-of select="$revdesc"/>
											</td>
										</tr>
									</table>
								</xsl:when>
								<xsl:otherwise>
									<div class="title-st2" style="margin-top:10">
										<xsl:value-of select="."/>
									</div>
								</xsl:otherwise>
							</xsl:choose>
							<!--
							<p style='MARGIN-bottom: 7px;MARGIN-TOP: 7px' class='title'>
								<table width='100%' border='0' cellpadding='0' cellspacing='0'>
								<tr><td><img src='images/red1_dot.gif' width='100%' height='2'/></td></tr>
								<tr><td bgcolor='#FAEFDF' height='25' class='title2'><span style='padding-left:10'><b><xsl:value-of select="."/></b></span></td></tr>
								<tr><td></td></tr>
								</table>
							</p>
							-->
						</xsl:when>
						<xsl:otherwise>
							<div class="title-st2" style="margin-top:10">
								<xsl:value-of select="."/>
							</div>
						</xsl:otherwise>
					</xsl:choose>
				</xsl:if>
			</xsl:when>
			<xsl:otherwise>
				<xsl:if test="$groupcode != 'etm'">
					<div class="title-st2" style="margin-top:10">
						<xsl:value-of select="."/>
					</div>
				</xsl:if>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template match="sitsubqualname">
		<xsl:if test="name(..) !='sitsubqual' and normalize-space(text()) != '' and translate(normalize-space(text()),'pino','PINO') != 'PIN NO.'">
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td height="5"/>
				</tr>
				<tr>
					<td bgcolor="#DADADA">
						<p style="MARGIN-left: 10px" class="sitsubqualname">
							<xsl:value-of select="normalize-space(.)"/>
						</p>
					</td>
				</tr>
				<tr>
					<td height="5"/>
				</tr>
			</table>"
  </xsl:if>
	</xsl:template>
	<!-- table 관련 처리  YUBIL : 070529 수정완료 -->
	<xsl:template match="table">
	<xsl:variable name="nColCount">
		<xsl:choose>
			<xsl:when test="tgroup/@cols">tgroup/@cols</xsl:when>
			<xsl:when test="@columns">@columns</xsl:when>
		</xsl:choose>
	</xsl:variable>
	<div>
			<b>
				<xsl:value-of select="title/."/>
			</b>
		</div>
		<table border="1" cellspacing="0" cellpadding="3" style="border:solid 1pt gray;BORDER-COLLAPSE: collapse;bgcolor=#ccffff;width:100%;MARGIN-top: 5px;">
			<xsl:for-each select="thead/tr">
				<xsl:call-template name="row">
					<xsl:with-param name="colcount" select="$nColCount"/>
					<xsl:with-param name="ishead" select="1"/>
				</xsl:call-template>
			</xsl:for-each>
		   <xsl:for-each select="tgroup/thead/row">
			 <xsl:call-template name="oldrow">
			 <xsl:with-param name="colcount" select="$nColCount"/>
			 <xsl:with-param name="ishead" select="1"/>
			 </xsl:call-template>
  		 </xsl:for-each>
			<xsl:for-each select="tbody/tr">
				<xsl:call-template name="row">
					<xsl:with-param name="colcount" select="$nColCount"/>
					<xsl:with-param name="ishead" select="0"/>
				</xsl:call-template>
			</xsl:for-each>
			<xsl:for-each select="tgroup/tbody/row">
			  <xsl:call-template name="oldrow">
		 	  <xsl:with-param name="colcount" select="$nColCount"/>
			  <xsl:with-param name="ishead" select="0"/>
			</xsl:call-template>
		</xsl:for-each>
		</table>
		<br/>
	</xsl:template>
	<!-- table/row 관련 처리 -->
	<xsl:template name="row">
		<xsl:param name="colcount"/>
		<xsl:param name="ishead"/>
		<tr>
			<xsl:choose>
				<xsl:when test="$ishead=1">
					<xsl:attribute name="style"><xsl:value-of select="'BACKGROUND-COLOR: #dddddd; TEXT-ALIGN: center; height: 35px;'"/></xsl:attribute>
				</xsl:when>
				<xsl:when test="position()=1 and $groupcode='dtc'">
					<xsl:attribute name="style"><xsl:value-of select="'TEXT-ALIGN: center; height: 35px;'"/></xsl:attribute>
				</xsl:when>
			</xsl:choose>
			<xsl:for-each select="td">
				<td style="border:solid 1pt #c0c0c0;padding-left:20px;padding-top:15px;padding-right:20px;padding-bottom:15px;">
					<xsl:if test="@align">
						<xsl:attribute name="align"><xsl:value-of select="@align"/></xsl:attribute>
					</xsl:if>
					<xsl:if test="@valign">
						<xsl:attribute name="valign"><xsl:value-of select="@valign"/></xsl:attribute>
					</xsl:if>
					<xsl:if test="@rowspan">
						<xsl:attribute name="rowspan"><xsl:value-of select="@rowspan"/></xsl:attribute>
						<xsl:variable name="CntRows1" select="2"/>
					</xsl:if>
					<xsl:if test="@colspan">
						<xsl:attribute name="colspan"><xsl:value-of select="@colspan"/></xsl:attribute>
					</xsl:if>
					<xsl:if test="@width">
						<xsl:attribute name="width"><xsl:value-of select="@width"/>%</xsl:attribute>
					</xsl:if>
					<xsl:choose>
						<xsl:when test="count(child::*) > 0">
							<xsl:apply-templates/>
						</xsl:when>
						<xsl:otherwise>
							<xsl:text> </xsl:text>
						</xsl:otherwise>
					</xsl:choose>
				</td>
			</xsl:for-each>
		</tr>
	</xsl:template>
	<!-- table 관련 처리 -->
	<xsl:template match="table_old">
		<xsl:variable name="nColCount" select="tgroup/@cols"/>
		<div>
			<b>
				<xsl:value-of select="title/."/>
			</b>
		</div>
		<table border="1" cellspacing="0" cellpadding="3" style="border:solid 1pt gray;BORDER-COLLAPSE: collapse;bgcolor=#ccffff;width:100%;MARGIN-top: 5px;">
			<xsl:for-each select="tgroup/thead/row">
				<xsl:call-template name="row">
					<xsl:with-param name="colcount" select="$nColCount"/>
					<xsl:with-param name="ishead" select="1"/>
				</xsl:call-template>
			</xsl:for-each>
			<xsl:for-each select="tgroup/tbody/row">
				<xsl:call-template name="row">
					<xsl:with-param name="colcount" select="$nColCount"/>
					<xsl:with-param name="ishead" select="0"/>
				</xsl:call-template>
			</xsl:for-each>
		</table>
		<br/>
	</xsl:template>
	<!-- table/row 관련 처리 -->
	<xsl:template name="oldrow">
		<xsl:param name="colcount"/>
		<xsl:param name="ishead"/>
		<tr>
			<xsl:choose>
				<xsl:when test="$ishead=1">
					<xsl:attribute name="style"><xsl:value-of select="'BACKGROUND-COLOR: #dddddd; TEXT-ALIGN: center; height: 35px;'"/></xsl:attribute>
				</xsl:when>
				<xsl:when test="position()=1 and $groupcode='dtc'">
					<xsl:attribute name="style"><xsl:value-of select="'BACKGROUND-COLOR: #dddddd; TEXT-ALIGN: center; height: 35px;'"/></xsl:attribute>
				</xsl:when>
			</xsl:choose>
			<xsl:for-each select="entry">
				<td style="border:solid 1pt #c0c0c0;padding-left:20px;padding-top:15px;padding-right:20px;padding-bottom:15px;">
					<xsl:if test="@align">
						<xsl:attribute name="align"><xsl:value-of select="@align"/></xsl:attribute>
					</xsl:if>
					<xsl:if test="@valign">
						<xsl:attribute name="valign"><xsl:value-of select="@valign"/></xsl:attribute>
					</xsl:if>
					<xsl:if test="@morerows">
						<xsl:attribute name="rowspan"><xsl:value-of select="number(@morerows)+1"/></xsl:attribute>
					</xsl:if>
					<xsl:if test="@morecols">
						<xsl:attribute name="colspan"><xsl:value-of select="number(@morecols)+1"/></xsl:attribute>
					</xsl:if>
					<xsl:if test="@width">
						<xsl:attribute name="width"><xsl:value-of select="number(translate(@width,'*',''))*100 div $colcount"/>%</xsl:attribute>
					</xsl:if>
					<xsl:choose>
						<xsl:when test="count(child::*) > 0">
							<xsl:apply-templates/>
						</xsl:when>
						<xsl:otherwise>
							<xsl:text> </xsl:text>
						</xsl:otherwise>
					</xsl:choose>
				</td>
			</xsl:for-each>
		</tr>
	</xsl:template>
	<!-- ptxt 관련 처리 -->
	<!-- ptxt의 child로 올수 있는 element를 체크 해야 됨. -->
	<xsl:template match="ptxt">
		<xsl:choose>
			<xsl:when test="name(..)='td'">
				<xsl:apply-templates/>
				<br/>
			</xsl:when>
			<xsl:otherwise>
				<div>
					<xsl:apply-templates/>
				</div>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template match="hyperlink">
		<!--hyperlink 관련 처리 모듈이 없네. ??? -->
	</xsl:template>

	<!-- yubil 080117-->
	<!-- emph 의 style이 뭔지 체크해야 됨 -->
	<xsl:template match="emph">		
		<span class="ptxt">
			<xsl:choose>
				<xsl:when test="@etype='bold'">
					<xsl:attribute name="style">
						<xsl:value-of select="'FONT-WEIGHT: bold'"/>
					</xsl:attribute>
				</xsl:when>
			</xsl:choose>
			<xsl:value-of select="."/>
		</span>
	</xsl:template>
	
	<xsl:template match="text()">
		<xsl:if test="name(..)='ptxt'">		
			<span class="ptxt">
				<xsl:value-of select="."/>
			</span>			
		</xsl:if>
	</xsl:template>
	<!-- para 관련 처리  -->
	<xsl:template match="para">
		<xsl:for-each select="child::*">
			<div style="MARGIN-bottom: 7px">
				<xsl:value-of select="."/>
			</div>
		</xsl:for-each>
	</xsl:template>
	<!-- diagnostic 관련 처리 모듈에 없음. ???-->
	<xsl:template match="diagnostic">
		<xsl:apply-templates/>
	</xsl:template>
	<!-- spec 관련 처리  -->
	<xsl:template match="spec">
		<xsl:text/>
	</xsl:template>
	<!-- subtitle 관련 처리  -->
	<xsl:template match="subtitle">
		<table width="100%" border="0" cellpadding="1" cellspacing="0">
			<tr>
				<td bgcolor="#EFEFEF" height="25" class="title3">
					<b>
						<xsl:value-of select="."/>
					</b>
				</td>
			</tr>
		</table>
	</xsl:template>

	<!-- pin-assign 관련 처리  -->
	<!-- yubil 080123 -->
	<xsl:template match="pin-assign">
		<!--<xsl:value-of select="name()"/>-->
	</xsl:template>

	<!-- yubil 080117-->
	<!-- spec-ptxt 관련 처리  -->
	<!-- spec-ptxt hr의 color가 없음 ???-->
	<!-- yubil 080715-->
	<!-- 예전 데이터에서는 연속해서 spec-ptxt가 나올 경우 하나의 ptxt처럼 처리 했는데
		 각각의 ptxt를 처리 하도록 수정함		-->
	<xsl:template match="spec-ptxt">
		<table width="600" cellpadding="0" cellspacing="0">
			<tr>
				<td>
					<hr style="color:rgb(255,162,8);"/>
					<span class="spec-ptxt">
						<xsl:choose>
							<xsl:when test="text()">
								<xsl:value-of select="."/>
							</xsl:when>
							<xsl:otherwise>
								<xsl:apply-templates/>
							</xsl:otherwise>
						</xsl:choose>
					</span>
					<hr style="color:rgb(255,162,8);"/>
				</td>
			</tr>
		</table>
	</xsl:template>

	<!-- yubil 080620  -->
	<!-- img 관련 처리 -->
	<xsl:template match="img">
		<xsl:param name="imgName" select="concat($GImgPath,@graphicname)"/>
		<xsl:param name="figFormat"  select="myObj:GetImgExt($imgName)"/>
		<xsl:param name="imgPath" select="concat($imgName,'.',$figFormat)"/>

		<img>
			<xsl:attribute name="src">
				<xsl:value-of select="$imgPath"/>
			</xsl:attribute>
			<xsl:attribute name="width">99%</xsl:attribute>
		</img>
	</xsl:template>	
	
	<!-- graphic 관련 처리 -->
	<!-- 
     parent : s1,s2,attetion 일 경우 kind = 2
     parent : table/entry 일 경우 kind =1 
     else parent : kind = 0 
	-->
	<xsl:template match="graphic">
		<xsl:call-template name="graphic_template"/>
	</xsl:template>
	<xsl:template name="graphic_template">

		<!--  yubil 070913 graphic 확장자를 파일시스템에 찾아 온다  xmlns 를 선언해두어야 한다. -->
		<xsl:param name="imgName" select="@graphicname"/>
		<xsl:param name="figFormat"  select="$imgName"/>
		<xsl:param name="imgPath" select="$imgName"/>

		<xsl:param name="kind">
			<xsl:choose>
				<xsl:when test="count(ancestor::entry)>0">
					2
				</xsl:when>
				<xsl:when test="count(ancestor::s1)>0 or  count(ancestor::s2)>0 or count(ancestor::attetion)>0">
					1
				</xsl:when>
				<xsl:otherwise>
				  0
				 </xsl:otherwise>
			</xsl:choose>
		</xsl:param>
		<!-- swf 일 경우는 그냥 display 한다.-->
		<xsl:choose>
			<xsl:when test="$figFormat='swf'">
				<p style="margin-top:5px; margin-bottom:0px">
					<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="100%" height="500" id="ShockwaveFlash1">
						<param name="movie">
							<xsl:attribute name="value"><xsl:value-of select="$imgPath"/></xsl:attribute>
						</param>
					</object>
				</p>
			</xsl:when>
			<xsl:when test="$figFormat='svg'">
				<object id="dispsvg"  type="image/svg+xml" name="SVGEmbed" width="100%" height="90%">
					<xsl:attribute name="data"><xsl:value-of select="$imgPath"/></xsl:attribute>			
					<xsl:comment>svg</xsl:comment>
				</object>
			</xsl:when>			
			<xsl:otherwise>
				<xsl:choose>
					<xsl:when test="$imgOpenStyleKey='fullImg'">
						<xsl:call-template name="display_fullimg">
							<xsl:with-param name="kind" select="$kind"/>
							<xsl:with-param name="imgPath" select="$imgPath"/>
						</xsl:call-template>
					</xsl:when>
					<xsl:when test="$imgOpenStyleKey='etmimg'">
						<xsl:call-template name="display_etmimg">
							<xsl:with-param name="kind" select="$kind"/>
							<xsl:with-param name="imgPath" select="$imgPath"/>
						</xsl:call-template>
					</xsl:when>
					<xsl:otherwise>
						<xsl:call-template name="display_gracphic">
							<xsl:with-param name="kind" select="$kind"/>
							<xsl:with-param name="imgPath" select="$imgPath"/>
						</xsl:call-template>
					</xsl:otherwise>
				</xsl:choose>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template name="display_fullimg">
		<xsl:param name="kind"/>
		<xsl:param name="imgPath"/>
	</xsl:template>
	<xsl:template name="display_etmimg">
		<xsl:param name="kind"/>
		<xsl:param name="imgPath"/>
		<table border="0" width="100%" height="87%">
			<tr>
				<td align="center" valign="middle">
					<img name="dispimg" border="1">
						<xsl:attribute name="src"><xsl:value-of select="$imgPath"/></xsl:attribute>
					</img>
				</td>
			</tr>
		</table>
	</xsl:template>
	<xsl:template name="display_gracphic">
		<xsl:param name="kind"/>
		<xsl:param name="imgPath"/>
		<p style="margin-top:5px; margin-bottom:0px">
			<img border="1">
				<xsl:attribute name="src"><xsl:value-of select="$imgPath"/></xsl:attribute>				
				<xsl:attribute name="style">cursor: hand; border-color:silver;</xsl:attribute>
				<xsl:attribute name="onclick">window.external.popup('<xsl:value-of select="translate($imgPath,'\','/')"/>')</xsl:attribute>
				<xsl:choose>
					<xsl:when test="@name='yes' or @name='no'">
						<xsl:attribute name="width">64</xsl:attribute>
						<xsl:attribute name="height">28</xsl:attribute>
					</xsl:when>
					<xsl:when test="parent::td">
						<xsl:attribute name="width">95%</xsl:attribute>
						<xsl:attribute name="cellpadding">100</xsl:attribute>
					</xsl:when>
					<xsl:otherwise>
						<xsl:attribute name="width">99%</xsl:attribute>
					</xsl:otherwise>
				</xsl:choose>
			</img>
		</p>
	</xsl:template>
	<!-- figure 관련 처리 -->
	<!-- groupcode가 etm일 경우는 이해 안됨 ???-->
	<!-- figure의 자식으로 올 수 있는 node를 파악해야됨-->
	<xsl:template match="figure">
		<xsl:apply-templates/>
	</xsl:template>
	<!-- waveform 관련 처리 -->
	<xsl:template match="waveform">
		<xsl:for-each select="child::*">
			<xsl:choose>
				<xsl:when test="waveform-graphic">
					<xsl:call-template name="graphic_template"/>
				</xsl:when>
				<xsl:when test="waveform-hints">
					<xsl:apply-templates/>
				</xsl:when>
			</xsl:choose>
		</xsl:for-each>
	</xsl:template>
	<!-- HME DTCGuide FlowChart 19/06/26 hi2 -->
	<xsl:template match="flowchart">
        <div id="drawing" style="margin:30px auto; width:95%;Height:95%;"  ></div>
        <script>
            var svgDoc = SVG('drawing').size('100%','100%');
            flowsvg.setRoot(svgDoc);
			flowsvg.config({showButtons : false});
            flowsvg.layoutShapes( <xsl:value-of select="."/> );
            createFlowchartSVGPanZoom();
            window.addEventListener("resize",refreshFlowChartSVGPanZoom);
        </script>
    </xsl:template>
</xsl:stylesheet>