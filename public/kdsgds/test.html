<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML Transformation</title>
    <script>
        function transformXML() {
            var xsltProcessor = new XSLTProcessor();
            var xsltRequest = new XMLHttpRequest();
            xsltRequest.open("GET", "stylesheet.xsl", true);
            xsltRequest.onload = function() {
                xsltProcessor.importStylesheet(xsltRequest.responseXML);
                
                var xmlRequest = new XMLHttpRequest();
                xmlRequest.open("GET", "input.xml", true);
                xmlRequest.onload = function() {
                    var resultDocument = xsltProcessor.transformToDocument(xmlRequest.responseXML);
                    var resultString = new XMLSerializer().serializeToString(resultDocument);
                    document.getElementById("output").innerHTML = resultString;
                };
                xmlRequest.send();
            };
            xsltRequest.send();
        }

        window.onload = function() {
            transformXML(); // Gọi hàm transformXML() khi trang đã tải xong
        };
    </script>
</head>
<body>
    <h1>XML Transformation</h1>
    <div id="output"></div>
</body>
</html>