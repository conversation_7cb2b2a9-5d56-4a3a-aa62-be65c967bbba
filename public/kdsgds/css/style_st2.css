BODY {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
TABLE {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
TR {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
TD {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
INPUT {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
FORM {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
DIV {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
SPAN {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
SELECT {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
TEXTAREA {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
BLOCKQUOTE {
	SCROLLBAR-FACE-COLOR: #dddddd; FONT-SIZE: 40px; SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6; SCROLLBAR-SHADOW-COLOR: #dddddd; SCROLLBAR-3DLIGHT-COLOR: #818181; SCROLLBAR-ARROW-COLOR: #ffffff; SCROLLBAR-TRACK-COLOR: #eaeaea; FONT-FAMILY: Roboto; SCROLLBAR-DARKSHADOW-COLOR: #818181; scrollbar-background-color: #F6F6F6
}
A:link {TEXT-DECORATION: none}
A:visited { TEXT-DECORATION: none}
A:active {TEXT-DECORATION: none}
A:hover {TEXT-DECORATION: none}
.dir-style {
	FONT-WEIGHT: bold; FONT-SIZE: 12px; COLOR: #152d5b
}
.title-st1 {
	FONT-SIZE: 19px; COLOR: #ffffff
}
.title-st2 {
	FONT-WEIGHT: bold; FONT-SIZE: 13px; HEIGHT: 25px
}
.title-st3 {
	FONT-WEIGHT: bold; FONT-SIZE: 13px; COLOR: #0b8185; HEIGHT: 25px
}
.table-center {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; BORDER-LEFT: 0px solid; WIDTH: 744px; BORDER-BOTTOM: 0px solid
}
/*.table-st1 {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; MARGIN-TOP: 8px; MARGIN-BOTTOM: 6px; BORDER-LEFT: 0px solid; WIDTH: 100%; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #45a5ab
}
.table-td-st1-t {
	PADDING-RIGHT: 0px; PADDING-LEFT: 18px; FONT-SIZE: 16px; PADDING-BOTTOM: 0px; COLOR: #ffffff; PADDING-TOP: 2px
}*/
.table-out {
	BORDER-RIGHT: #666666 0px solid; PADDING-RIGHT: 0px; BORDER-TOP: #666666 0px solid; PADDING-LEFT: 0px; PADDING-BOTTOM: 0px; BORDER-LEFT: #666666 0px solid; WIDTH: 744px; PADDING-TOP: 0px; BORDER-BOTTOM: #666666 0px solid; BACKGROUND-COLOR: #808080
}
.table-st2 {
	BORDER-RIGHT: #666666 0px solid; BORDER-TOP: #666666 0px solid; BORDER-LEFT: #666666 0px solid; WIDTH: 100%; BORDER-BOTTOM: #666666 0px solid
}
.table-td-st2-t {
	PADDING-RIGHT: 5px; PADDING-LEFT: 5px; FONT-WEIGHT: bold; PADDING-BOTTOM: 5px; PADDING-TOP: 5px; BACKGROUND-COLOR: #f0ece3; TEXT-ALIGN: center
}
.table-td-st2-sub {
	PADDING-RIGHT: 5px; PADDING-LEFT: 5px; PADDING-BOTTOM: 5px; PADDING-TOP: 5px; BACKGROUND-COLOR: #ffffff
}
.table-yes-no {
	BORDER-RIGHT: 0px solid; PADDING-RIGHT: 15px; BORDER-TOP: 0px solid; MARGIN-TOP: 9px; PADDING-LEFT: 15px; MARGIN-BOTTOM: 9px; PADDING-BOTTOM: 10px; BORDER-LEFT: 0px solid; WIDTH: 744px; PADDING-TOP: 10px; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #e6eaef
}
.table-st3 {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; MARGIN-TOP: 12px; MARGIN-BOTTOM: 9px; BORDER-LEFT: 0px solid; WIDTH: 100%; BORDER-BOTTOM: 0px solid
}
.table-td-st3-t {
	PADDING-RIGHT: 0px; PADDING-LEFT: 18px; FONT-SIZE: 16px; PADDING-BOTTOM: 0px; PADDING-TOP: 2px
}
.table-st4 {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; MARGIN-TOP: 9px; MARGIN-BOTTOM: 9px; BORDER-LEFT: 0px solid; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #425363
}
.table-td-st4 {
	PADDING-RIGHT: 15px; PADDING-LEFT: 15px; FONT-SIZE: 13px; PADDING-BOTTOM: 10px; COLOR: #ffffff; PADDING-TOP: 10px
}
.table-caution {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; MARGIN-TOP: 9px; MARGIN-BOTTOM: 9px; BORDER-LEFT: 0px solid; WIDTH: 100%; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #ddf2ff
}
.table-td-box {
	PADDING-RIGHT: 15px; PADDING-LEFT: 15px; PADDING-BOTTOM: 10px; PADDING-TOP: 10px
}
.table-t-caution {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; BORDER-LEFT: 0px solid; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #0a47f3
}
.table-t-td-box {
	PADDING-RIGHT: 10px; PADDING-LEFT: 10px; FONT-WEIGHT: bold; FONT-SIZE: 15px; PADDING-BOTTOM: 6px; COLOR: #ffffff; PADDING-TOP: 6px
}
.table-note {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; MARGIN-TOP: 9px; MARGIN-BOTTOM: 9px; BORDER-LEFT: 0px solid; WIDTH: 100%; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #d8f1de
}
.table-t-note {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; BORDER-LEFT: 0px solid; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #008403
}
.table-hint {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; MARGIN-TOP: 9px; MARGIN-BOTTOM: 9px; BORDER-LEFT: 0px solid; WIDTH: 100%; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #ffeadd
}
.table-t-hint {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; BORDER-LEFT: 0px solid; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #ff4a0b
}
.table-warning {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; MARGIN-TOP: 9px; MARGIN-BOTTOM: 9px; BORDER-LEFT: 0px solid; WIDTH: 100%; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #ffeadd
}
.table-t-warning {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; BORDER-LEFT: 0px solid; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #ff4a0b
}
.table-st5 {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; MARGIN-TOP: 9px; MARGIN-BOTTOM: 9px; BORDER-LEFT: 0px solid; WIDTH: 744px; BORDER-BOTTOM: 0px solid; BACKGROUND-COLOR: #ffffff
}
.table-td-st5 {
	VERTICAL-ALIGN: top
}
.img-icon1 {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; VERTICAL-ALIGN: inherit; BORDER-LEFT: 0px solid; BORDER-BOTTOM: 0px solid
}
.img-border-no {
	BORDER-RIGHT: 0px solid; BORDER-TOP: 0px solid; MARGIN-TOP: 10px; MARGIN-BOTTOM: 10px; BORDER-LEFT: 0px solid; BORDER-BOTTOM: 0px solid
}
.img-border-yes {
	BORDER-RIGHT: #848484 1px solid; BORDER-TOP: #848484 1px solid; MARGIN-TOP: 10px; MARGIN-BOTTOM: 10px; BORDER-LEFT: #848484 1px solid; BORDER-BOTTOM: #848484 1px solid
}
.list-m1 {
	MARGIN-LEFT: 12px
}
.icon-m1 {
	COLOR: #d63526
}

.menu_list div:link {FONT-FAMILY: arial;	FONT-SIZE: 17px;	FONT-WEIGHT: bold;	COLOR: #333333;	TEXT-DECORATION: NONE;	MARGIN: AUTO;	PADDING: 2px;	HEIGHT: auto;	WIDTH: auto;	BORDER: 1px solid #b6b6b6;	BACKGROUND-COLOR: #f1f1f1;LINE-HEIGHT:28px;
}
.menu_list div:visited {FONT-FAMILY: arial;	FONT-SIZE: 17px;	FONT-WEIGHT: bold;	COLOR: #666666;	TEXT-DECORATION: NONE;	BACKGROUND-COLOR: #fafafa;	MARGIN: auto;	PADDING: 2px;	HEIGHT: auto;	WIDTH: auto;	BORDER: 1px solid #dedede;LINE-HEIGHT:28px;
}
.menu_list div:hover {FONT-FAMILY: arial;	FONT-SIZE: 17px;	FONT-WEIGHT: bold;	COLOR: #008AFF;	TEXT-DECORATION: underline;	MARGIN: auto;	PADDING: 2px;	HEIGHT: auto;	WIDTH: auto;	BORDER: 1px solid #b6b6b6;	BACKGROUND-COLOR: #f1f1f1;LINE-HEIGHT:28px;
}