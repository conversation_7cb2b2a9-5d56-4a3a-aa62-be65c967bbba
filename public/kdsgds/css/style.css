/*Style*/

body, table, tr, td, input, form, div, span, select, textarea, blockquote
{font-size:13px;
line-height:22px;
font-family:Arial;


scrollbar-face-color: #DDDDDD; 
scrollbar-shadow-color: #DDDDDD; 
scrollbar-highlight-color: #F6F6F6; 
scrollbar-background-color: #F6F6F6; 
scrollbar-3dlight-color: #819191; 
scrollbar-darkshadow-color: #819191; 
scrollbar-track-color: #EAEAEA; 
scrollbar-arrow-color: #ffffff;
}

A:link {color:#2A2C31; text-decoration: none}
A:visited {color:#2A2C31; text-decoration: none}
A:active {color:#FD8900; text-decoration: none}
A:hover {color:#FD8900; text-decoration: none}
A.sub_u:hover {COLOR: #003366; FONT-FAMILY: arial; FONT-SIZE: 9pt; LINE-HEIGHT: 14pt; TEXT-DECORATION: none; background-color: #FF8D32;}

/*Top*/

.tmenu-style {font-family:"Verdana"; font-size:15px; color:#000000;font-weight:bold}
.tmenu-style:link {font-family:"Verdana"; font-size:15px; color:#000000; font-weight:bold}
.tmenu-style:visited {font-family:"Verdana"; font-size:15px; color: #000000; font-weight:bold}
.tmenu-style:hover {font-family:"Verdana"; font-size:15px; color:#ea7423; font-weight:bold}

.Userinfo-style {font-family:"Arial"; font-size:12px; color:#ffffff}

.search-style {font-family:"Arial"; font-size:12px; color:#D4DAF4;text-decoration:none}
.search-style:link {font-family:"Arial"; font-size:12px; color:#D4DAF4;text-decoration:none}
.search-style:visited {font-family:"Arial"; font-size:12px; color: #D4DAF4; text-decoration:none}
.search-style:hover {font-family:"Arial"; font-size:12px; color:#D4DAF4; text-decoration:underline}

.dir-style {font-family:"Arial"; font-size:12px; color:#152D5B;font-weight:bold}

/*left menu*/

.menu-style1 {font-family:"Arial" ; font-size:18px; color:#FFFFFF; font-weight:bold}
.menu-style2 {font-family:"Arial" ; font-size:18px; color:gray; font-weight:bold}
.list-style1 {font-family:"Arial"; font-size:9pt; color:#003366;text-decoration:none}

/*menu*/

.mmenu1-style {font-family:"Verdana"; font-size:15px; color:#434348; text-decoration:none}
.mmenu1-style:link {font-family:"Verdana"; font-size:19px; color:#434348; text-decoration:none}
.mmenu1-style:visited {font-family:"Verdana"; font-size:19px; color: #434348; text-decoration:none}
.mmenu1-style:hover {font-family:"Verdana"; font-size:19px; color:#FFFFFF; text-decoration:none}

.mmenu1-1-style {font-family:"Verdana"; font-size:13px; color:#3f619b; text-decoration:none; padding-top:0px; padding-right:0px; padding-bottom:0px; padding-left:30px;}
.mmenu1-1-style:link {font-family:"Verdana"; font-size:17px; color:#3f619b; text-decoration:none; padding-top:0px; padding-right:0px; padding-bottom:0px; padding-left:30px;}
.mmenu1-1-style:visited {font-family:"Verdana"; font-size:17px; color: #3f619b; text-decoration:none; padding-top:0px; padding-right:0px; padding-bottom:0px; padding-left:30px;}
.mmenu1-1-style:hover {font-family:"Verdana"; font-size:17px; color:#FFFFFF; text-decoration:none; padding-top:0px; padding-right:0px; padding-bottom:0px; padding-left:30px;}

.mmenu2-style {font-family:"Verdana"; font-size:15px; color:#FFFFFF; text-decoration:none}
.mmenu2-style:link {font-family:"Verdana"; font-size:19px; color:#FFFFFF; text-decoration:none}
.mmenu2-style:visited {font-family:"Verdana"; font-size:19px; color: #FFFFFF; text-decoration:none}
.mmenu2-style:hover {font-family:"Verdana"; font-size:19px; color:#FFFFFF; text-decoration:none}

.mmenu2-1-style {font-family:"Verdana"; font-size:15px; color:#92fffe; text-decoration:none}
.mmenu2-1-style:link {font-family:"Verdana"; font-size:19px; color:#92fffe; text-decoration:none}
.mmenu2-1-style:visited {font-family:"Verdana"; font-size:19px; color: #92fffe; text-decoration:none}
.mmenu2-1-style:hover {font-family:"Verdana"; font-size:19px; color:#FFFFFF; text-decoration:none}

.mmenu2-2-style {font-family:"Verdana"; font-size:15px; color:#808080; text-decoration:none}
.mmenu2-2-style:link {font-family:"Verdana"; font-size:19px; color:#808080; text-decoration:none}
.mmenu2-2-style:visited {font-family:"Verdana"; font-size:19px; color: #808080; text-decoration:none}
.mmenu2-2-style:hover {font-family:"Verdana"; font-size:19px; color:#808080; text-decoration:none}

.tmmenu1-style {font-family:"Arial"; font-size:12px; color:#5C6E97}
.tmmenu1-style:link {font-family:"Arial"; font-size:12px; color:#DE6300; text-decoration:none}
.tmmenu1-style:visited {font-family:"Arial"; font-size:12px; color: #5C6E97; text-decoration:none}
.tmmenu1-style:hover {font-family:"Arial"; font-size:12px; color:#5C6E97; text-decoration:none}

/*Bottom*/

.bottom-style {font-family:"Arial"; font-size:10px; color:#ffffff; line-height:10px; font-weight:bold}
.bottom2-style {font-family:"Arial"; font-size:9px; color:#ffffff; line-height:10px; font-weight:bold}
.bottom3-style {font-family:"Arial"; font-size:8px; color:#ffffff; line-height:10px; font-weight:bold}

/*Center*/

.subtitle-style1 {font-family:"Arial"; font-size:16px; color:#112E58; font-weight:bold}
.subtitle-style2 {font-family:"Arial"; color:#112E58; font-weight:bold}
.subtitle-icon-style2 {font-family:"Arial"; font-size:8px; color:#112E58}

/*Table*/

.table-td-style1 {font-family:"Arial"; font-size:12px; color:#2A2C31; font-weight:bold; background-color: #F0ECE3;}
.table-td-style2 {font-family:"Arial"; font-size:12px; background-color: #ffffff;}
.table-bg-style1 {background-color: #5E5E5C;}

/*Text Box*/

.input.t-box1 {font-family:"Arial"; font-size:12px; background-color:#ffffff; border-top:solid 1px #646464; border-right:solid 1px #646464; border-bottom:solid 1px #646464 ; border-left:solid 1px #646464;line-height:15px;}

/*User's Guide*/
.subtitle-style-t {font-family:"Arial"; font-size:16px; color:#ffffff; font-weight:bold}
.sublist-style {font-family:"Arial"; font-size:15px; color:#416F94; font-weight:bold}
.subtitle-style-t-info {font-family:"Arial"; font-size:12px; color:#ffffff; font-weight:bold}

.vesearchbox { background:url(file:c:/gvci/images/buttons/ve_searchbg.gif) no-repeat; font-weight:bold; color:#19253A; width:960px; height:37px; margin-bottom:3px;}
.vesearchbox2 { background:url(file:c:/gvci/images/buttons/ve_searchbg2.gif) no-repeat; font-weight:bold; color:#19253A; width:960px; height:37px; margin-bottom:3px;}
.vesearchbox tr { width:960px; height:37px}


.vesearchbox tr:hover { background:url(file:c:/gvci/images/buttons/ve_searchbg.gif) no-repeat; color:#416F94 }
.trOver2 {color:#ED3C1F}
.vesearchbox tr {
    behavior: expression(
        this.onmouseover = new Function("this.className += ' trOver2';"),
        this.onmouseout = new Function("this.className = this.className.replace(' trOver2', '');"),
        this.style.behavior = null
    );
}