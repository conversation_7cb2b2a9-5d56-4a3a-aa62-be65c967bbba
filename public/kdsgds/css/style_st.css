@charset "utf-8";
/* CSS Document */

html { overflow-y:scroll}
html, body {margin:0;padding:0;background-color:#fff; width:100%; height:100%; font-family:Roboto;}
hr {display:none;}
img,fieldset {border:0 none;outline:none;}
dl,ul,ol,li {list-style: none;}
h1,h2,h3,h4,h5,dl,dt,dd,ul,li,ol,th,td,p,blockquote,form,fieldset,legend {margin:0; padding:0;}
table {border-collapse:collapse;}
a ,a:link {text-decoration:none; color:#5f5f5f;} 
a:hover { text-decoration:none; color:#5f5f5f;} 
a:active {text-decoration:none;}
input,select,textarea {color:#707070; margin:0; padding:0; border:1px solid #bbbaba; font-size:12px;}
body,th,td {font-size:12px;}
caption { display:none;}
table { border-collapse:collapse;}
focus { outline:none;}
em {font-style: normal;}

.contents-wrap {padding:0 10px;}

img 
{
	border:1px solid #c0c0c0;
	margin:20px 0;
}

BODY
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6;
	margin:0;
}
TABLE
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 13px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6
}
TR
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6
}
TD
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6;
}
INPUT
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6
}
FORM
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6
}
DIV
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6
}
SPAN
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6
}
SELECT
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6
}
TEXTAREA
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6
}
BLOCKQUOTE
{
    SCROLLBAR-FACE-COLOR: #dddddd;
    FONT-SIZE: 40px;
    SCROLLBAR-HIGHLIGHT-COLOR: #f6f6f6;
    SCROLLBAR-SHADOW-COLOR: #dddddd;
    SCROLLBAR-3DLIGHT-COLOR: #818181;
    SCROLLBAR-ARROW-COLOR: #ffffff;
    SCROLLBAR-TRACK-COLOR: #eaeaea;
    FONT-FAMILY: Roboto;
    SCROLLBAR-DARKSHADOW-COLOR: #818181;
    scrollbar-background-color: #F6F6F6
}
A:link
{
    TEXT-DECORATION: none
}
A:visited
{
    TEXT-DECORATION: none
}
A:active
{
	TEXT-DECORATION: none
}
A:hover
{
	TEXT-DECORATION: none
}
.dir-style
{
    FONT-WEIGHT: bold;
    FONT-SIZE: 12px;
    COLOR: #152d5b
}
.title-st1
{
    FONT-SIZE: 19px;
    COLOR: #ffffff
}
.title-st2
{
    FONT-WEIGHT: bold;
    FONT-SIZE: 40px;
	margin:22px 0px !important;
	width:100%;
	float:left;
}
.title-st3
{
    FONT-WEIGHT: bold;
    FONT-SIZE: 40px;
    COLOR: #0b8185;
    HEIGHT: 25px
}
.table-center
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    BORDER-LEFT: 0px solid;
    WIDTH: 744px;
    BORDER-BOTTOM: 0px solid
}
.table-st1
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    BORDER-LEFT: 0px solid;
    WIDTH: 100%;
    BORDER-BOTTOM: 0px solid; 
	margin:26px 0 !important;       
}
.table-st2
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 8px;
    MARGIN-BOTTOM: 6px;
    BORDER-LEFT: 0px solid;
    WIDTH: 100%;
    BORDER-BOTTOM: 0px solid;    
    MARGIN-LEFT: 18px;    
}
.table-tr-st0-t
{
    PADDING-RIGHT: 0px;
    PADDING-LEFT: 18px;    
    PADDING-BOTTOM: 0px;
    COLOR: #ffffff;
    PADDING-TOP: 0px;
    BACKGROUND-COLOR: #bfc6d9;
}
.table-td-st0-t
{
    PADDING-RIGHT: 0px;
    PADDING-LEFT: 18px;
    FONT-FAMILY: Roboto;
    FONT-SIZE: 16px;
    FONT-WEIGHT: bold;
    PADDING-BOTTOM: 0px;
    COLOR: #19253a;
    PADDING-TOP: 0px;    
     
}
.table-td-st1-t
{
    PADDING-RIGHT: 0px;
    PADDING-LEFT: 20px;
    FONT-FAMILY: Roboto;
    FONT-SIZE: 50px;
    PADDING-BOTTOM: 4px;
    COLOR: #3f4f68;
    PADDING-TOP: 4px;
    BACKGROUND-COLOR: #dcdee3;
	height:92px;
	margin-bottom:44px;
	vertical-align:middle;
}
.ptxt
{
    FONT-FAMILY: Roboto;
    FONT-SIZE: 40px;
	font-weight:normal;
}
.table-out
{
    BORDER-RIGHT: #666666 0px solid;
    PADDING-RIGHT: 0px;
    BORDER-TOP: #666666 0px solid;
    PADDING-LEFT: 0px;
    PADDING-BOTTOM: 0px;
    BORDER-LEFT: #666666 0px solid;
    WIDTH: 744px;
    PADDING-TOP: 0px;
    BORDER-BOTTOM: #666666 0px solid;
    BACKGROUND-COLOR: #808080
}
.table-st2
{
    BORDER-RIGHT: #666666 0px solid;
    BORDER-TOP: #666666 0px solid;
    BORDER-LEFT: #666666 0px solid;
    WIDTH: 100%;
    BORDER-BOTTOM: #666666 0px solid
}
.table-td-st2-t
{
    PADDING-RIGHT: 5px;
    PADDING-LEFT: 5px;
    FONT-WEIGHT: bold;
    PADDING-BOTTOM: 5px;
    PADDING-TOP: 5px;
    BACKGROUND-COLOR: #f0ece3;
    TEXT-ALIGN: center
}
.table-td-st2-sub
{
    PADDING-RIGHT: 5px;
    PADDING-LEFT: 5px;
    PADDING-BOTTOM: 5px;
    PADDING-TOP: 5px;
    BACKGROUND-COLOR: #ffffff
}
.table-yes-no
{
    BORDER-RIGHT: 0px solid;
    PADDING-RIGHT: 15px;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 9px;
    PADDING-LEFT: 15px;
    MARGIN-BOTTOM: 9px;
    PADDING-BOTTOM: 10px;
    BORDER-LEFT: 0px solid;
    WIDTH: 744px;
    PADDING-TOP: 10px;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #e6eaef
}
.table-st3
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 12px;
    MARGIN-BOTTOM: 9px;
    BORDER-LEFT: 0px solid;
    WIDTH: 100%;
    BORDER-BOTTOM: 0px solid
}
.table-td-st3-t
{
    PADDING-RIGHT: 0px;
    PADDING-LEFT: 18px;
    FONT-SIZE: 16px;
    PADDING-BOTTOM: 0px;
    PADDING-TOP: 2px
}
.table-st4
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 9px;
    MARGIN-BOTTOM: 9px;
    BORDER-LEFT: 0px solid;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #425363
}
.table-td-st4
{
    PADDING-RIGHT: 15px;
    PADDING-LEFT: 15px;
    FONT-SIZE: 40px;
    PADDING-BOTTOM: 10px;
    COLOR: #ffffff;
    PADDING-TOP: 10px
}
.table-caution
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 9px;
    MARGIN-BOTTOM: 9px;
    BORDER-LEFT: 0px solid;
    WIDTH: 100%;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #ddf2ff
}
.table-caution img
{
	border:none;
	width:auto;
	margin:0 0 5px 0;
}
.table-td-box
{
    PADDING-RIGHT: 15px;
    PADDING-LEFT: 15px;
    PADDING-BOTTOM: 10px;
    PADDING-TOP: 10px
}
.table-t-caution
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    BORDER-LEFT: 0px solid;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #0a47f3
}
.table-t-td-box
{
    PADDING-RIGHT: 10px;
    PADDING-LEFT: 10px;
    FONT-WEIGHT: bold;
    FONT-SIZE: 15px;
    PADDING-BOTTOM: 6px;
    COLOR: #ffffff;
    PADDING-TOP: 6px
}
.table-note
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 9px;
    MARGIN-BOTTOM: 9px;
    BORDER-LEFT: 0px solid;
    WIDTH: 100%;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #d8f1de
}
.table-note img
{
	border:none;
	width:auto;
	margin:0 0 5px 0;
}
.table-t-note
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    BORDER-LEFT: 0px solid;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #008403
}
.table-hint
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 9px;
    MARGIN-BOTTOM: 9px;
    BORDER-LEFT: 0px solid;
    WIDTH: 100%;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #ffeadd
}
.table-hint img 
{
	border:none;
	width:auto;
	margin:0 0 5px 0;
}
.table-t-hint
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    BORDER-LEFT: 0px solid;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #ff4a0b
}
.table-warning
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 9px;
    MARGIN-BOTTOM: 9px;
    BORDER-LEFT: 0px solid;
    WIDTH: 100%;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #ffeadd
}
.table-t-warning
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    BORDER-LEFT: 0px solid;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #ff4a0b
}
.table-t-warning img
{
	border:none;
	width:auto;
	margin:0 0 5px 0;
}
.table-st5
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 9px;
    MARGIN-BOTTOM: 9px;
    BORDER-LEFT: 0px solid;
    WIDTH: 744px;
    BORDER-BOTTOM: 0px solid;
    BACKGROUND-COLOR: #ffffff
}
.table-td-st5
{
    VERTICAL-ALIGN: top
}
.img-icon1
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    VERTICAL-ALIGN: inherit;
    BORDER-LEFT: 0px solid;
    BORDER-BOTTOM: 0px solid
}
.img-border-no
{
    BORDER-RIGHT: 0px solid;
    BORDER-TOP: 0px solid;
    MARGIN-TOP: 10px;
    MARGIN-BOTTOM: 10px;
    BORDER-LEFT: 0px solid;
    BORDER-BOTTOM: 0px solid
}
.img-border-yes
{
    BORDER-RIGHT: #848484 1px solid;
    BORDER-TOP: #848484 1px solid;
    MARGIN-TOP: 10px;
    MARGIN-BOTTOM: 10px;
    BORDER-LEFT: #848484 1px solid;
    BORDER-BOTTOM: #848484 1px solid
}
.list-m1
{
    MARGIN-LEFT: 12px
}
.icon-m1
{
    COLOR: #d63526
}
.txt-middle td
{
	vertical-align:middle;
}
.spec-ptxt
{
	color:#005966;
	display:inline-block;
	margin:32px 0;
}
.img_message
{
	border:none;
	width:auto;
	margin:0 0 5px 0;
}
#t2 *
{
	vertical-align:middle;
}
#t2 
{
	margin-bottom:40px;
}

#t2 a
{
	display:inline-block;
	width:50px;
	padding:10px 0;
	border:1px solid #a8abb0;
	color:#555b6b;
	background:#f9f9f9;
	font-size:20px;
	text-align:center;
	font-weight:bold;
}

.menu_list
{
	display:inline-block;
	width:100px;
	padding:20px 0;
	border:1px solid #a8abb0;
	color:#555b6b;
	background:#f9f9f9;
	font-size:40px;
	text-align:center;
	font-weight:bold;
	float:left;
	margin-right:10px;

}
#t2 a:hover
{
	color:#fff;
	background:#555b6b;
}
#t2 .btn_over
{
	color:#fff;
	background:#555b6b;
}
#t2 a em
{
	position:relative;
	top:13px;
	*top:15px;
}
.table_t01
{
	float:left;
	margin-top:22px;
}
