function prepFindIdLocationButton() {
    $('.jacking-points-link, .eobd-connector-link').click(function( event ) {
        event.preventDefault();
        event.stopPropagation();
        var url = $(this).attr("data-url");
        $.featherlight($('#extra-id-locations'), {
            beforeContent: function () {
                $(".featherlight .featherlight-content")
                    .css("display", "none");
            },
            afterContent: function(event) {
                var targetElement = $('.featherlight #extra-id-locations');
                $('.featherlight').append("<div class='loading loading-id-location'></div>");
                $.ajax({
                    url : url,
                    cache : false,
                    success : function(data) {
                        targetElement.html(data);
                        $('.featherlight').find(".loading").remove();
                        $(".featherlight .featherlight-content")
                            .css("display", "inline-block");
                        targetElement.css("display", "block");
                    },
                    error : function(qXHR, textStatus, errorThrown) {
                        showGenericErrorMessage()
                    },
                });
            }
        });
    });

    $('.id-location').click(function( event ) {
	    event.preventDefault();
	    event.stopPropagation();
        $(this).addClass('active');
    	var url = $(this).attr("data-url");
    	var model = $(this).attr("id");
    	var showEQ = $(this).attr("showEq");
        findIdLocation(url, showEQ);
    });

   var locationPopupDisplayed = $("#content").data("locationShown");
   if(locationPopupDisplayed != "true" && !$('.id-location').hasClass('inactive') && getParam("idLocationTab") != null){
       $('.id-location').trigger("click");
       $("#content").data("locationShown", "true");
   }
}

function findIdLocation(url, showEQ) {
	if($("#id-locator").html().length == 0) {
		$.ajax({
			url : url,
			cache : false,
			beforeSend : function() {
				$('#id-locator').html("<div class='loading'></div>");		
				showIdlocation();
			},
			success : function(data) {
				$("#id-locator").empty().append(data);
				//showIdlocation();
			},
			error : function(qXHR, textStatus, errorThrown) {
			},
			complete : function() {
			    prepIdLocation(showEQ);
			}
		});
	} else {
		showIdlocation();
	}
}
function prepIdLocation(showEQ) {
    $('.button-close').on('click', function () {
        $('#id-locator').css({opacity: 0,  display: 'none'})
        $('#overlay').remove();
        $('.id-location').removeClass('active');
    });

    $('#id-locator').on('click', 'ul li a:not(.inactive)', function(e) {
        e.preventDefault();
        $(this).parent().parent().find('.active').removeClass('active');
        $(this).parent().addClass('active');
        $('#tmpl-holder').empty();
        if($('#tmpl-' + $(this).data('tmpl')).children().length > 0) {
        	$($('#tmpl-' + $(this).data('tmpl')).html()).appendTo('#tmpl-holder');
        }
        $('#tmpl-key-holder').empty();
        if($('#tmpl-key-' + $(this).data('tmpl')).children().length > 0) {
        	$($('#tmpl-key-' + $(this).data('tmpl')).html()).appendTo('#tmpl-key-holder');
        }

        var leftPanelHeight = $('#id-locator-left-panel').height();

        if (leftPanelHeight > 530) {
            var navHeight = $('#id-locator-top-panel').height() + $('#id-locator-nav-panel').height();
            var $idLocator = $('#id-locator');
            $idLocator.css("height", leftPanelHeight + navHeight + 5 + "px");
            $('#id-locator-right-panel').css("height", leftPanelHeight + "px");

            var bodyHeight = $('body').height();
            var idLocatorHeight = $idLocator.height();
            var idLocatorTop = parseInt($idLocator.css('top'), 10);
            if (bodyHeight - idLocatorTop < idLocatorHeight) {
                var newBodyHeight = idLocatorHeight + idLocatorTop + "px"
                $('body').css("height", newBodyHeight);
                $('#overlay').css("height", newBodyHeight)
            }
        } else {
            $('#id-locator').css("height", "614px");
            $('#id-locator-right-panel').css("height", "530px");
        }
    });

    var idLocationTabParam = getParam("idLocationTab");
    var idLocationTab = idLocationTabParam ? idLocationTabParam : '';

    if (showEQ == 'true')
        $('#id-locator ul li:last-child a').trigger('click');
    else if (idLocationTab != '') {
        var found = false;

        $('#id-locator ul li a:not(.inactive)').each(function(){
            if (found == false && $(this).data('tmpl') === idLocationTab) {
                found = true;
                $(this).trigger("click");
            }

        });

        if (found == false) {
            $('#id-locator ul li:first-child a').trigger('click');
        }
    } else {
        $('#id-locator ul li:first-child a').trigger('click');
    }
}

function showIdlocation() {
    $('<div id="overlay"></div>').appendTo('body');

    $('#overlay').click(function( event ) {
    	event.stopPropagation();
        $('#id-locator').css({opacity: 0,  display: 'none'});
        $('#overlay').remove();
        $('.id-location').removeClass('active');
    });
    
    var docHeight = $(document).height(),
	winHeight = $(window).height(),
	scrollTop = $(window).scrollTop(),
	elemHeight = $('#id-locator').innerHeight() - $('#id-locator .top').outerHeight() - $('#id-locator nav').outerHeight(),
	elemWidth = $('#id-locator').innerWidth();
    
    $('#overlay').height($(document).height());

    var topMargin = scrollTop + (winHeight / 2) - (elemHeight / 2);
    if (topMargin < 0) {
        topMargin = 54;
    }
    $('#id-locator').css({top: topMargin, display: 'block', opacity: 1});
}