header .main-nav,
#maintenance-schedule .maintenance-item ul li.active:before,
ul li.active:before,
.filters .custom-radio-button.checked {
    background-color: #103178;
}

table tr.heading th,
table th,
#electronics-component-detail #nonVesa-components-overview tr.row:hover,
#electronics-component-detail #wiring-pins-overview tr.row:hover {
    background-color: rgba(255, 0, 0, 0.1);
}

#maintenance-schedule .maintenance-item ul li.active h3,
ul li.active h3,
.note.important h3:before,
.tmpl.alt li.active label,
#maintenance-schedule .custom-checkbox-wrapper.add-remove,
#smartpack .filter span.selected,
#smartpack-detail .overview > li.pseudo:before,
#filter-car-criteria .column ul li.selected a,
li.pseudo:before,
#model-detail nav.componentConfiguration ul.selectedComponents > li span,
#diagnosis-steps a,
#model-detail nav.componentConfiguration ul li div.currentComponentType span,
#filter-vehicle-criteria .column ul li.active,
.model-detail-subnav-extend nav ul.systemGroup.selected > li > a:not(.button),
.recentTrucks tbody tr td:last-child:after {
    color: #103178;
}

.button,
.button.red,
.button.green,
#id-locator .top,
#converters .top,
#popup .top {
    background: #103178;
    /* Old browsers */
    background: -moz-linear-gradient(top, #103178 0%, #cc0000 100%);
    /* FF3.6+ */
    background: -webkit-gradient(
        linear,
        left top,
        left bottom,
        color-stop(0%, #103178),
        color-stop(100%, #cc0000)
    );
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #103178 0%, #cc0000 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #103178 0%, #cc0000 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #103178 0%, #cc0000 100%);
    /* IE10+ */
    background: linear-gradient(to bottom, #103178 0%, #cc0000 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#103178', endColorstr='#cc0000',GradientType=0 );
    /* IE6-9 */
}

.lineoutLogin .right {
    background: url(splash.jpg);
}

.button,
.button.red,
.button.green {
    border: 1px solid #103178;
}

table th {
    border-right: 1px solid white;
}

#filter-car-criteria .column ul li.selected a {
    border-left: 4px solid #103178;
}

#id-locator .top,
#converters .top,
#popup .top {
    border-bottom: 1px solid #103178;
}

select:focus,
textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus {
    -webkit-box-shadow: 0 0 5px #103178;
    -moz-box-shadow: 0 0 5px #103178;
    box-shadow: 0 0 5px #103178;
    outline: none;
}

#electronics-component-detail #nonVesa-components-overview tr.row:hover,
#electronics-component-detail #wiring-pins-overview tr.row:hover {
    border-bottom: 1px solid white;
}

.tmpl.alt ul.dropdown-items li.active label {
    color: #103178 !important;
}

#modelTypesListContainer table tr:hover {
    background-color: rgba(153, 0, 0, 0.1);
    border-bottom: 1px solid white;
}
