header .main-nav,
#maintenance-schedule .maintenance-item ul li.active:before,
ul li.active:before,
.filters .custom-radio-button.checked {
    background-color: #103178;
}
table tr.heading th,
table th,
#electronics-component-detail #nonVesa-components-overview tr.row:hover,
#electronics-component-detail #wiring-pins-overview tr.row:hover {
    background-color: rgba(67, 150, 57, 0.1);
}
#maintenance-schedule .maintenance-item ul li.active h3,
ul li.active h3,
.note.important h3:before,
.tmpl.alt li.active label,
#maintenance-schedule .custom-checkbox-wrapper.add-remove,
#smartpack .filter span.selected,
#smartpack-detail .overview > li.pseudo:before,
#filter-car-criteria .column ul li.selected a,
li.pseudo:before,
#model-detail nav.componentConfiguration ul.selectedComponents > li span,
#diagnosis-steps a,
#model-detail nav.componentConfiguration ul li div.currentComponentType span,
#filter-vehicle-criteria .column ul li.active,
.model-detail-subnav-extend nav ul.systemGroup.selected > li > a:not(.button),
.recentTrucks tbody tr td:last-child:after {
    color: #439639;
}
.button,
.button.red,
.button.green,
#id-locator .top,
#converters .top,
#popup .top {
    background: #439639;
    background: -moz-linear-gradient(top, #439639 0%, #32712b 100%);
    background: -webkit-gradient(
        linear,
        left top,
        left bottom,
        color-stop(0%, #439639),
        color-stop(100%, #32712b)
    );
    background: -webkit-linear-gradient(top, #439639 0%, #32712b 100%);
    background: -o-linear-gradient(top, #439639 0%, #32712b 100%);
    background: -ms-linear-gradient(top, #439639 0%, #32712b 100%);
    background: linear-gradient(to bottom, #439639 0%, #32712b 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#439639', endColorstr='#32712b',GradientType=0 );
}
.lineoutLogin .right {
    background: url(splash.jpg);
}
.button,
.button.red,
.button.green {
    border: 1px solid #439639;
}
table th {
    border-right: 1px solid #e7e7e7;
}
#filter-car-criteria .column ul li.selected a {
    border-left: 4px solid #439639;
}
#id-locator .top,
#converters .top,
#popup .top {
    border-bottom: 1px solid #439639;
}
select:focus,
textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus {
    -webkit-box-shadow: 0 0 5px #439639;
    -moz-box-shadow: 0 0 5px #439639;
    box-shadow: 0 0 5px #439639;
    outline: none;
}
#electronics-component-detail #nonVesa-components-overview tr.row:hover,
#electronics-component-detail #wiring-pins-overview tr.row:hover {
    border-bottom: 1px solid #e7e7e7;
}
.tmpl.alt ul.dropdown-items li.active label {
    color: #439639 !important;
}
#modelTypesListContainer table tr:hover {
    background-color: rgba(34, 76, 29, 0.1);
    border-bottom: 1px solid #e7e7e7;
}
