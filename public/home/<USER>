const userAgent = navigator.userAgent.toLowerCase();
const isTablet = /(ipad|macintosh|tablet|(android(?!.*mobile))|(windows(?!.*phone)(.*touch))|kindle|playbook|silk|(puffin(?!.*(IP|AP|WP))))/.test(userAgent);

if (isTablet) {
    $('#MobileVersion').show()
}
else {
    $('#MobileVersion').hide()
}

function Mobileversion() {
    localStorage.setItem("Ismobileversion", true)
    var item = localStorage.getItem("Ismobileversion")

    //  location.href = "/ViewSwitcher/SwitchView?mobile=true&returnUrl="+ Request.Url.PathAndQuery;
}
var menuTimer = 0;
var hoverTimer = 0;
var hovering = false;
var hoverTime = 300;
$.Hover.delay = 100;
$.Hover.distance = 50;
$.Hover.leave = 500;
var enableTabs = "yes";
var fdrsSupport = "no";
var vidType = "none";
var quickStart = "false";
var strOasisDesc = "";
var strStyle = "";
var strMasterId = "";
var strBuildDate = "";
var strVehLine = "";
var strProdType = "";
var strStatus = "";
var strResult = "";
var strException = "";
var strModelName = "";
var strModelDesc = "";
var strModelYear = "";
var strRdsModel = "";
var strRdsYear = "";
var strRdsType = "";
//var strRdsSubType = "";
var strVin = "";
var strSymptoms = "";
var bClassicSymptomCodes = false;
var strOasisUrl = "";
var strVid = "";
var strEngineCode = "";
var strTransmissionCode = "";
var strEngine = "";
var strTTag = "";
var contentMarket = "";

var squeeze = function (ev) {//debugger
    var $ul = $(this);
    if (!$ul.hasClass('squeezed')) {
        var $SIBs = $ul.parent().siblings().css('display', 'none');
        var $LIs = $ul.children();
        var $ANCHORs = $LIs.children('a');
        var $SPANs = $ANCHORs.children('span');
        $LIs.css('white-space', 'nowrap');
        var ulWidth = $ul.add($LIs).add($ANCHORs).add($SPANs).css({ 'float': 'none', 'width': 'auto' }).end().end()[0].clientWidth + 12;
        $SIBs.css('display', 'inline');
        if (ulWidth < 144) { ulWidth = 144; }
        $ul.css('width', ulWidth + 'px').addClass('squeezed');
        var liWidth = ulWidth - 16 + 'px';
        $LIs.css({ 'float': offsetDir, 'width': liWidth, 'white-space': 'normal' }).each(function () {
            $('>ul', this).css(offsetDir, ulWidth - 11 + 'px');
        });
    }
},
    shift = function (ev) {//debugger
        $ul = $(this);
        $li = $ul.parent();
        var posTop = $li.height();
        var posLeft = $ul.position().left;
        if ($ul.hasClass('sub-menu')) {
            posTop = -2;
            $maxShift = $ul.find('>li').length - 1,
                $liVertPos = $li.offset().top,
                $ulOffset = $liVertPos + $ul.height();
            if ($ulOffset > $(document).height()) {
                var shift = Math.floor(($ulOffset - $(document).height()) / 24) + 1;
                shift = (shift > $maxShift) ? $maxShift : shift;
                $liIndex = $li.parent().find('>li').index($li);
                if (shift > $liIndex) {
                    $li2 = $li.parent().parent();
                    if (!($li2.hasClass('active') || $li2.hasClass('inactive'))) {
                        $liIndex += $li2.parent().find('>li').index($li2) + 1;
                    }
                    shift = (shift > $liIndex) ? $liIndex : shift;
                }
                posTop -= shift * 24;
            }
        }
        $('div#mainTabsContent').css('z-index', '-1').css('z-index', '1');
        var html = '<iframe class="shim" frameborder="0" style="display: block;position: absolute;' +
            'top:' + posTop + 'px;left:' + posLeft + 'px;' +
            'width:' + $ul.outerWidth() + 'px;height:' + $ul.outerHeight() + 'px;' +
            'z-index:98;" scrolling="no"></iframe>';
        $ul.before(html).css('top', posTop);
    },
    over = function (ev) {//debugger
        //return false;
        $LI = $(this);
        if (!$LI.hasClass('hovering')) {
            if ($LI.hasClass('parent')) { $LI.each(add); }
            else { $LI.addClass('hovering').siblings().each(remove); }
        }
        return false;
    },
    add = function (ev) {//debugger
        reset_timer();
        $LI = $(this);
        $LI.parent().find('>li.hovering').each(remove);
        if (!$LI.hasClass('disabled')) { $LI.addClass('hovering').find('>ul:first').each(squeeze).each(shift); }
    },
    touched = function (ev) {
        $LI = $(this);
        clearTimeout(hoverTimer);
        if ($LI.hasClass('parent')) {
            if (!$LI.hasClass('hovering')) { $LI.each(add); }
            else if (!hovering) { $LI.parent().find('>li.hovering').each(remove); }
        }
        else if (!$LI.hasClass('hovering')) { $LI.addClass('hovering').siblings().each(remove); }
        return false;
    },
    untouched = function (ev) {
        clearTimeout(hoverTimer);
        hoverTimer = setTimeout(function () { hovering = true; }, hoverTime);
        $(this).each(clicked);
        return false;
    },
    clicked = function (ev) {
        $LI = $(this);
        if (!$LI.hasClass('disabled') && $LI.attr('path') != "") {
            //goHref($LI.attr('parent'),$LI.attr('path'),$LI.hasClass('tab'));
        }
        if (!$LI.hasClass('parent')) { reset_all_menus(); }
    },
    out = function (ev) {
        if (hovering) { $(this).each(remove); }
        return false;
    },
    remove = function (ev) {
        $(this).removeClass('hovering').find('iframe.shim').remove().end().find('>ul:first').css('top', -9999);
    },
    hovered = function (ev) {
        clearTimeout(hoverTimer);
        hoverTimer = setTimeout(function () { hovering = true; }, hoverTime);
    },
    unhovered = function (ev) {
        clearTimeout(hoverTimer);
    };

function mouseOverFreeSubcriptionNote() {
    document.getElementById('FreeSubscriptiontImportantNotes').style.display = 'inline';
    document.getElementById('FreeSubscriptiontImportantNotes').style.top = '25px';
    document.getElementById('FreeSubscriptiontImportantNotes').style.postion = 'relative';
}
function mouseLeaveFreeSubcriptionNote() {
    document.getElementById('FreeSubscriptiontImportantNotes').style.display = 'none';
}

//Connected vehicle code{
function handleOTAPTSRetry() {
    $(".modal-fade").modal("hide");
    $(".modal-backdrop").remove();
    window.sessionStorage.setItem("verboseRetry", "true");
    CVOTAReloadFromDashboard();
}
function handleOTAPTSCancel() {
    $(".modal-fade").modal("hide");
    window.sessionStorage.setItem("verboseRetry", "false");
    $(".modal-backdrop").remove();
}

//Checks if there is a valid authentication cookie, if not it redirects the user to the login page so they can get a new one
function getCookieByName(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}


setInterval(function CheckForExpiredCookie() { //Run this code every 60 seconds so it isnt eating up a lot of processing power but still catches expired cookies fairly quickly
    return;
    //This will break if cookie becomes HttpOnly
    var Cookie = getCookieByName("Ford.TSO.PTSSuite");
    if (Cookie == null) {
        window.location.href = '/transport';
    }
}, 60 * 1000); // 60 * 1000 milsec

var getUnsupportedBrowserStorage = function () {
    var item = window.localStorage.getItem("UnsupportedBrowserWarningV2");
    if (item) {
        return JSON.parse(item);
    }
    return {};
}

var setUnsupportedBrowserStorage = function (object) {
    window.localStorage.setItem("UnsupportedBrowserWarningV2", JSON.stringify(object));
}

var getCompatBrowserStorage = function () {
    var item = window.sessionStorage.getItem("CompatBrowserWarning");
    if (item) {
        return JSON.parse(item);
    }
    return {};
}

var setCompatBrowserStorage = function (object) {
    window.sessionStorage.setItem("CompatBrowserWarning", JSON.stringify(object));
}

var getUnsupportedBrowserStorage = function () {
    var item = window.localStorage.getItem("UnsupportedBrowserWarningV2");
    if (item) {
        return JSON.parse(item);
    }
    return {};
}

var setUnsupportedBrowserStorage = function (object) {
    window.localStorage.setItem("UnsupportedBrowserWarningV2", JSON.stringify(object));
}

function hideDropDownMenu(dropdownMenu) {
    dropdownMenu.hide();
    dropdownMenu.find('ul.sub-menu').hide();
}

function showIeRequired(objLinkInfo, ieReqVer) {
    linkIframeDataSrc = "/Home/Warning?message=This page requires Internet Explorer version " + ieReqVer + " or greater.";
    showLoading();
    $(objLinkInfo.linkIframeSelector).attr("src", linkIframeDataSrc);
    $(objLinkInfo.linkSelector).tab('show');
}

function deActivateMenuItems(eleUl) {
    $(eleUl).find("li").each(function () {
        $(this).removeClass('active');
    });
}

function LaunchOasisMain(theVin, sympCodes, symptomList, espParts, oasisEnv, idsFound, readVin, netTest, inMarket) {
    showLoading();
    resetIframes();
    disableMainTabs();
    //$('#mainTabDiv-OASIS iframe').attr('src', '/oasis/Index?vin=' + theVin + '&symptomCodes=' + sympCodes + '&SymptomListType=' + symptomList + '&espparts=' + espParts + '&OasisEnv=' + oasisEnv + '&IdsFound=' + idsFound + '&ReadVin=' + readVin + '&NetTest=' + netTest + '&InMarket=' + inMarket);
    //$(".oasis_row").css('display', 'block');
    //$("#oasis_frame").attr('src', '/oasis/Index?vin=' + theVin + '&symptomCodes=' + sympCodes + '&SymptomListType=' + symptomList + '&espparts=' + espParts + '&OasisEnv=' + oasisEnv + '&IdsFound=' + idsFound + '&ReadVin=' + readVin + '&NetTest=' + netTest + '&InMarket=' + inMarket);
    if (strEtisId && strEtisId != "") {
        var etisIdArr = strEtisId.split(':');
        if (etisIdArr.length === 2) {
            if (etisIdArr[0] === "EtisIds") {
                $('#feedback-link').click(function () {
                    openEtisFeedbackWin();
                    event.preventDefault();
                });
            }
            else if (etisIdArr[0] == "SbdSessionId") {
                $('#feedback-link').html('<img src="Content/images/etis_logo.png" alt="FordEtis" />');
                $('#feedback-link').click(function (event) {
                    $('#returnToFordEtisModal').modal();
                    event.preventDefault();
                });
            }
            else if (etisIdArr[0] == "JSessionId") {
                $('#feedback-link').html('<img src="Content/images/etis_logo.png" alt="FordEtis" />');
                $('#feedback-link').attr("href", "https://www.etis.dealerconnection.com/fromPts.do?vin=" + theVin + "&JSessionId=" + etisIdArr[1]);
            }
            $('#feedback-link').toggle();
        }
    }
    else {
        $('#feedback-link').hide();
    }
}

function returnToEtis(found) {
    window.location = "https://www.etis.dealerconnection.com/fromPts.do?" + strEtisId.replace(':', '=') + "&isFaultFound=" + found;
}

function openEtisFeedbackWin() {
    var etisid = window.top.ptsSession.etisid;
    if (etisid.indexOf("EtisIds") > -1) {
        window.top.ptsSession.openDiagWin(new Array("DIAG", "ETIS-FBK", "ETIS", "", "", "", "", "", "", "", ""));
    }
}

function InitalizeOasisMain(blnOasisError) {
    if (blnOasisError == true || window.top.ptsSession.vehicle.Status == "Invalid") {
        disableMainTabs(blnOasisError);
        setMainTabsTitle(false, true);
    } else {
        enableMainTabs();
        setMainTabsTitle(false, false);
    }
    //$('#mainTabLink-OASIS').tab('show');
    showVin(true);
}

function showTabPaneOrSubTabs(linkId) {
    var linkSelector = "#" + linkId;
    var linkFrameSelector = linkSelector.replace("Link", "Frame");
    if ($(linkFrameSelector)[0] == undefined) {
        //if a Maintab has subtabs, link has no iframe but subtabs loaded in div.
        //therefore, show subtabs div
        var linkDivSelector = linkSelector.replace("Link", "Div");
        $(linkDivSelector).attr("tab-pane active");
        showTabPane(linkId, false);
    } else {
        showTabPane(linkId, true);
    }
}
//Connected vehicle code{
function activateConnectedVehicleTab() {
    // showLoading();

    var cviframesrc = "";
    $('.nav-tabs a[href="#mainTabDiv-CONVEHICLE"]').tab('show');
    triggerEvent("OPEN_TAB", "CONVEHICLE_HOME");
    if (window.sessionStorage.getItem("CVRefresh") == "true") {

        var strVin = (ptsSession.vinInfoJson && ptsSession.vinInfoJson.Vin) ? ptsSession.vinInfoJson.Vin : "";
        cviframesrc = $('#mainTabDiv-CONVEHICLE iframe').attr('src', '/vdirs/cv-dashboard?vin=' + strVin, sandbox = '');
        if (cviframesrc !== "") {

            setTimeout(function () { hideLoading() }, 2000);
            return cviframesrc

        }

    }
}
function activateConnectedVehicleOtaTab() {

    $('.nav-tabs a[href="#mainTabDiv-CONVEHICLE"]').tab('show');
    window.sessionStorage.setItem("CVRefresh", "true")
    triggerEvent("OPEN_TAB", "CONVEHICLE_OTA");
    var strVin = (ptsSession.vinInfoJson && ptsSession.vinInfoJson.Vin) ? ptsSession.vinInfoJson.Vin : "";
    $('#mainTabDiv-CONVEHICLE iframe').attr('src', '/vdirs/cv-dashboard/?vin=' + strVin + '&tab=[ota]#');
}

function reloadCVTabEnabledFlag() {
    var objLinkInfo = getLinkInfo("mainTabLink-CONVEHICLE");
    if (objLinkInfo.linkDataIframeSrc != "") {
        console.log("Connected vehicle enabled for this country");
        window.sessionStorage.setItem("ConnectVehicleTabEnabled", "true");
    }
    else {
        console.log("Connected vehicle not enabled for this country");
        window.sessionStorage.setItem("ConnectVehicleTabEnabled", "false");
    }
}

function activateDiagTab() {
    var masterIDVal = "";
    if (window.sessionStorage.getItem("cvMasterCode")) {
        masterIDVal = window.sessionStorage.getItem("cvMasterCode");
    }
    var strVin = (ptsSession.vinInfoJson && ptsSession.vinInfoJson.Vin) ? ptsSession.vinInfoJson.Vin : "";
    var strbuildDate = (ptsSession && ptsSession.builddate) ? ptsSession.builddate : "";
    var strYear = (ptsSession && ptsSession.year) ? ptsSession.year : "";
    var DiagURLvalue = '/Diagnostics/Index?vehicleId=' + masterIDVal + '&from=cv&vin=' + strVin + '&buildDate=' + strbuildDate + '&ModelYear=' + strYear;
    $('.nav-tabs a[href="#mainTabDiv-DIAGNOSTICS"]').tab('show');
    $('#mainTabDiv-DIAGNOSTICS iframe').attr('src', DiagURLvalue);
}

function showClearTransAdaptiveMenu(bShow) {
    if (bShow) {
        $("#mainTabMenuItemLink-TCM_CLEAR_TRANS_ADAPTIVE").show();
    }
    else {
        $("#mainTabMenuItemLink-TCM_CLEAR_TRANS_ADAPTIVE").hide();
    }
}

function showRemoteDiagnosticMenu(bShow) {
    if (bShow) {
        $("#mainTabMenuItemLink-REMOTE_DIAG").show();
    }
    else {
        $("#mainTabMenuItemLink-REMOTE_DIAG").hide();
    }
}


function showOBDReadinessMenu(bShow) {
    if (bShow) {
        $("#mainTabMenuItemLink-OBD_READINESS_MONITOR").show();
    }
    else {
        $("#mainTabMenuItemLink-OBD_READINESS_MONITOR").hide();
    }
}

function showRDHighVoltMenu(bShow) {
    if (bShow) {
        $("#mainTabMenuItemLink-HIGH_VOLTAGE_BATTERY").show();
    }
    else {
        $("#mainTabMenuItemLink-HIGH_VOLTAGE_BATTERY").hide();
    }
}
function showOTAMenu(bShow) {
    if (bShow) {
        $("#mainTabMenuItemLink-CONVEHICLE_OTA").show();
        $("#mainTabMenuItemLink-EZU").show();
        $("#mainTabMenuItemLink-BLUECRUISE_SUBSCRIP").show();
    }
    else {
        $("#mainTabMenuItemLink-CONVEHICLE_OTA").hide();
        $("#mainTabMenuItemLink-EZU").hide();
        $("#mainTabMenuItemLink-BLUECRUISE_SUBSCRIP").hide();
    }
}
function showCVHomeMenu(bShow) {
    if (bShow) {
        window.sessionStorage.setItem("HideCVMenu", "false");
        $("#mainTabMenuItemLink-CONVEHICLE_HOME").show();
        $("#mainTabMenuItemLink-BLUECRUISE_SUBSCRIP").show();
    }
    else {
        $("#mainTabMenuItemLink-CONVEHICLE_HOME").hide();
        $("#mainTabMenuItemLink-BLUECRUISE_SUBSCRIP").hide();
        window.sessionStorage.setItem("HideCVMenu", "true");
    }
}
function showConnectedTabs(mainTabLiId, bShow) {

}

function resetIframeHeight(linkIframeSelector) {
    if ($(linkIframeSelector)[0] != undefined) {
        $(linkIframeSelector).css("height", ($(linkIframeSelector)[0].clientHeight - 1) + "px");
        $(linkIframeSelector).css("height", ($(linkIframeSelector)[0].clientHeight + 1) + "px");
    }
}

function showCenterPopup(url, title, intWidth, intHeight) {
    var left = (screen.width / 2) - (intWidth / 2);
    var top = (screen.height - intHeight) / 4;
    return window.open(url, title, 'toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=' + intWidth + ', height=' + intHeight + ', top=100, left=' + left);
}

function showPopWindowOLD(linkto, strWidth, strHeight) {
    var windowName;
    var blnFulScreen = false;
    if ((strWidth == undefined || strWidth == null) &&
        (strHeight == undefined || strHeight == null)) {
        blnFulScreen = true;
        strWidth = screen.availWidth;
        strHeight = screen.availHeight;
    }

    strWidth = strWidth == undefined || strWidth == null ? "800" : strWidth;
    strWidth = "1200"

    strHeight = strHeight == undefined || strHeight == null ? "600" : strHeight;
    strHeight = "600"
    windowName = window.open(linkto, '_blank', 'resizable=yes,scrollbars=yes,height=' + strHeight + ',width=' + strWidth);


    windowName.focus();
}

function showPopWindow(linkto, w, h) {
    var wid = w ? w : 800;
    var hi = h ? h : 800;
    var LeftPosition = (screen.width) ? (screen.width - wid) / 2 : 100;
    var TopPosition = (screen.height) ? (screen.height - hi) / 2 : 100;

    var settings = 'width=' + wid + ',height=' + hi + ',top=' + TopPosition + ',left=' + LeftPosition + ',scrollbars=yes,location=no,directories=no,status=no,menubar=no,toolbar=no,resizable=no';
    window.open(linkto, "", settings);
}

function showPopWindowInNewTab(linkto, w, h) {
    // Fixes dual-screen position Most browsers
    var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : window.screenX;
    var dualScreenTop = window.screenTop != undefined ? window.screenTop : window.screenY;

    var width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
    var height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;
    var systemZoom = width / window.screen.availWidth;
    var left = (width - w) / 2 / systemZoom + dualScreenLeft;
    var top = (height - h) / 2 / systemZoom + dualScreenTop;

    var wid = (w ? w : 800) / systemZoom;
    var hi = (h ? h : 600) / systemZoom;
    eval("newWindow" + '=window.open(linkto, linkto, "resizable=yes,toolbar=yes,width=" + wid + ",height=" + hi + ",menubar=yes,status=yes,scrollbars=yes,dependent=yes,left=" + left + ",top=" + top + ",location=yes");');

}

function showTroubleShootingInNewTab() {
    showPopWindowInNewTab("/FrequentlyAskedQuestions/TroubleShooting");
}

function getLinkDataSrc(objLinkInfo) {
    return objLinkInfo.linkDataIframeSrcWithVin.length > 0 ?
        objLinkInfo.linkDataIframeSrcWithVin :
        (objLinkInfo.noNeedOfFixUrlParameters) ?
            objLinkInfo.linkDataIframeSrc :
            fixUrlParameters(objLinkInfo.linkDataIframeSrc);
}

function getLinkInfo(linkId) {
    var objLinkInfo = {};
    objLinkInfo.linkId = getShowTabLinkId(linkId);

    objLinkInfo.linkSelector = "#" + objLinkInfo.linkId;

    var objLink = $(objLinkInfo.linkSelector)

    var linkHref = $(objLink).attr("href");
    objLinkInfo.linkIframeSelector = linkHref + " iframe";

    objLinkInfo.linkDataPopUp = $(objLink).attr("data-popUp");

    if (linkId == "subTabMenuItemLink-tab-637647240358291762" && $(objLink)[0].baseURI.split("?")[1] != undefined) {
        objLinkInfo.linkDataIframeSrc = $(objLink).attr("data-iframe-src") + '?' + $(objLink)[0].baseURI.split("?")[1];
        objLinkInfo.linkDataIframeSrcWithVin = "";

    } else {
        objLinkInfo.linkDataIframeSrc = $(objLink).attr("data-iframe-src");
        objLinkInfo.linkDataIframeSrc = objLinkInfo.linkDataIframeSrc == undefined ||
            objLinkInfo.linkDataIframeSrc == null ?
            "" : objLinkInfo.linkDataIframeSrc;


        objLinkInfo.linkDataIframeSrcWithVin = $(objLink).attr("data-iframe-src-withvin");
        objLinkInfo.linkDataIframeSrcWithVin = objLinkInfo.linkDataIframeSrcWithVin == undefined ||
            objLinkInfo.linkDataIframeSrcWithVin == null ?
            "" : objLinkInfo.linkDataIframeSrcWithVin;
    }


    objLinkInfo.noNeedOfFixUrlParameters = (objLinkInfo.linkDataIframeSrc.indexOf("[") == -1 &&
        objLinkInfo.linkDataIframeSrc.indexOf("]") == -1) ?
        true :
        false;

    objLinkInfo.linkIframeSrc = $(objLinkInfo.linkIframeSelector).attr("src");
    objLinkInfo.linkIframeSrc = (objLinkInfo.linkIframeSrc) ? objLinkInfo.linkIframeSrc : "";

    objLinkInfo.linkIframeScrollTop = $(objLinkInfo.linkIframeSelector).attr("data-scrolltop");
    objLinkInfo.linkIframeScrollTop = (objLinkInfo.linkIframeScrollTop) ? objLinkInfo.linkIframeScrollTop : 0;

    return objLinkInfo;
}

function setLinkDataPanelIframeSrcChanged(linkSelector, strDataPanelIframeSrcChanged) {
    var linkDivSelector = linkSelector.replace("Link", "Div");
    //strDataPanelIframeSrcChanged vaue must be either "false" or "true"
    $(linkDivSelector).attr(strDataPanelIframeSrcChanged);
}

function getLinkDataPanelIframeSrcChanged(linkSelector) {
    var linkDivSelector = linkSelector.replace("Link", "Div");
    var strDataPanelIframeSrcChanged = $(linkDivSelector).attr("data-panel-iframe-src-changed");
    strDataPanelIframeSrcChanged = strDataPanelIframeSrcChanged == undefined ||
        strDataPanelIframeSrcChanged == null ?
        "false" : strDataPanelIframeSrcChanged;
    return strDataPanelIframeSrcChanged;
}

function getShowTabLinkId(linkId) {
    switch (linkId) {
        case "mainTabLink-ADMINTOOLS":
            return getActiveAdminTabLinkId();
        case "mainTabLink-HOME":
            return getActiveHomeTabLinkId();
        default:
            return linkId;
    }
}

function getDefaultMainTabLinkId() {
    if (!vidParams) {
        return $("#mainTabsUl > li[data-default-tab='true']").attr("id").replace("Li", "Link");
    }
    else {
        return "mainTabLink-VID";
    }
}

function getActiveMainTabLinkId() {
    return $("#mainTabsUl > li.active").attr("id").replace("Li", "Link");
}

function getActiveMainTabFrameId() {
    return $("#mainTabsUl > li.active").attr("id").replace("Li", "Frame");
}

function getActiveHomeTabLinkId() {
    return $("ul#homeTabsUl li.active").attr("id").replace("Li", "Link");
}

function getActiveAdminTabLinkId() {
    if ($("ul#adminTabsUl li.active").length > 0) {
        return $("ul#adminTabsUl li.active").attr("id").replace("Li", "Link");
    } else {
        return $("ul#adminTabsUl li:nth-child(1)").attr("id").replace("Li", "Link");
    }
}

function setTabFramesSizes() {
    var divHeight;
    var frameHeight;
    var frameTabType;
    $('iframe[data-iframe-type=tab]').each(function () {
        frameHeight = $("#footer").position().top - $("#mainTabs").position().top;
        frameTabType = $(this).attr("data-tab-type");
        if (frameTabType == "subTab") {
            divHeight = frameHeight - 75;
            frameHeight = divHeight - 10;
        }
        else if (frameTabType == "mainTab") {
            frameHeight = frameHeight - 30;
            divHeight = frameHeight;
        }
        frameHeight = frameHeight + "px";
        divHeight = divHeight + "px";
        $(this).parent().css({ "width": "100%", "height": divHeight });
        $(this).css({ "width": "100%", "height": frameHeight });
    });
}

function fixScrolling() {
    setTabFramesSizes();
}

function getModel(objVinInfoJson) {
    var strModel = objVinInfoJson.ModelName;
    strModel = strModel.substring(0, strModel.indexOf("-"));
    strModel = strModel.trim();
    return objVinInfoJson.ModelYear + " " + strModel;
}

function disableMainTabs(blnOasisError) {
    if (isFreeSubscription()) {
        if (top.ptsSession && top.ptsSession.year && top.ptsSession.year >= new Date().getFullYear()) {
            $('#mainTabs > ul > li').not('li#mainTabLi-HOME,li#mainTabLi-VID,li#mainTabLi-ADMINTOOLS').addClass('disabled');
            $('#mainTabs > ul > li.disabled ul.dropdown-menu').each(function () {
                $(this).hide();
            })
        }
        else {
            $('#mainTabs > ul > li').not('li#mainTabLi-HOME,li#mainTabLi-VID').addClass('disabled');
            // disableSltsSubtab();
        }
    }
    else
        $('#mainTabs > ul > li').not('li#mainTabLi-HOME,li#mainTabLi-VID,li#mainTabLi-ADMINTOOLS').addClass('disabled');
    $('#mainTabs > ul > li.disabled ul.dropdown-menu').each(function () {
        $(this).hide();
    })
    $('#mainTabs > ul li.disabled a').each(function () {
        $(this).addClass("disabled");
    })

    if (blnOasisError) {
        $('#mainTabs > ul > li#mainTabLi-OASIS').removeClass('disabled');
    }
}

function disableMainTab(mainTabLiId) {
    $('#' + mainTabLiId).addClass('disabled');
    $('#mainTabs > ul > li.disabled ul.dropdown-menu').each(function () {
        $(this).hide();
    })
    $('#mainTabs > ul li.disabled a').each(function () {
        $(this).addClass("disabled");
    })
}

function hideSubTab(mainSubTabLiId) {
    $('#mainTabLink-CONVEHICLE').addClass('disabled');
    $('#' + mainSubTabLiId).hide();
}
function showSubTab(mainSubTabLiId) {
    $('#' + mainSubTabLiId).show();
}

function enableMainTab(mainTabLiId) {
    $('#mainTabs > ul > li.disabled ul.dropdown-menu').each(function () {
        console.log($(this));
        $(this).show();
        $(this).attr("style", "display:;")
    })
    $('#' + mainTabLiId).removeClass('disabled');
}

function enableMainTabs() {
    if (getCookieByName("DisableTabsForBev") == "true") {
        return;
    }
    $('#mainTabs > ul li.disabled a').each(function () {
        $(this).removeClass("disabled");
    })
    $('#mainTabs > ul > li.disabled ul.dropdown-menu').each(function () {
        $(this).show();
        $(this).attr("style", "display:;")
    })
    if (isFreeSubscription()) {

        if (top.ptsSession && top.ptsSession.year && top.ptsSession.year >= new Date().getFullYear()) {
            $('#mainTabs > ul > li').not('li#mainTabLi-HOME,li#mainTabLi-VID,li#mainTabLi-ADMINTOOLS').removeClass('disabled');
        }
        else
            $('#mainTabs > ul > li').not('li#mainTabLi-HOME,li#mainTabLi-VID,li#mainTabLi-DIAGNOSTICS,li#mainTabLi-CONVEHICLE,li#mainTabLi-WSM,li#mainTabLi-EVTM,li#mainTabLi-PCED,li#mainTabLi-TIP,li#mainTabLi-PDI,li#mainTabLi-ADMINTOOLS').removeClass('disabled');
    }
    else
        $('#mainTabs > ul > li').not('li#mainTabLi-HOME,li#mainTabLi-VID,li#mainTabLi-ADMINTOOLS').removeClass('disabled');

    if (contentMarket === 'EU') {
        const goTo = window.sessionStorage.getItem("goTo");
        if (goTo == "checksheets") {

            const checksheetsElement = $('#mainTabMenuItemLink-tab-637788126342904750'); //Routine Service Checksheets
            if (checksheetsElement.length > 0) {
                checksheetsElement.click();
            } else {
                console.error('Checksheets element not found.');
            }

        }

        if (goTo == "DSR") {

            const homeElement = $('#mainTabLink-HOME');
            const dsrElement = $('#subTabMenuItemLink-tab-637564252203234205');   //DSR

            if (homeElement.length > 0) {
                if (dsrElement.length > 0) {

                    homeElement.click();
                    dsrElement.click();
                }
                else {
                    console.error('DSR element not found.');
                }
            }
        }


    }
    //disableToolboxTab();    //disable mainTabLi-PDI for now, until it will be ready
}

function disableSltsSubtab() {
    if ($('#subTabLi-SLTSWEB')) {
        $('#subTabLi-SLTSWEB').addClass('disabled');
        $('#subTabLi-SLTSWEB > ul.dropdown-menu').hide();
        $('#subTabLi-SLTSWEB > a').addClass("disabled");
    }
}

function loadWsmTab() {
    var objLinkInfo = getLinkInfo("mainTabLink-WSM");

    var currentWsmIframeScrollTop = $("#mainTabFrame-WSM").attr("data-scrolltop");
    currentWsmIframeScrollTop = (currentWsmIframeScrollTop) ? currentWsmIframeScrollTop : 0;

    var currentWsmIframeSrc = $("#mainTabFrame-WSM").attr("src");
    currentWsmIframeSrc = (currentWsmIframeSrc) ? currentWsmIframeSrc : "";

    var setWsmIframeSrc = getLinkDataSrc(objLinkInfo)
    if (currentWsmIframeSrc.length == 0 || currentWsmIframeSrc != setWsmIframeSrc)
        $("#mainTabFrame-WSM").attr('src', setWsmIframeSrc);
}

function disableToolboxTab() {
    disableMainTab("mainTabLi-TOOLBOX");
    $('#mainTabLi-TOOLBOX').attr("title", "This functionality is under development.");
}

function setMainTabsTitle(blnOnLoad, blnOasisError) {
    var freeSub = isFreeSubscription();
    //TODO localize the string
    var strVinSelectionTitle = "VIN or Vehicle Selection Required - Use the Vehicle ID tab to enter a VIN or select a vehicle.";
    var strVinRequiredTitle = "VIN Required - Use the Vehicle ID tab to enter a VIN.";

    setDefaultMainTabsTitle(freeSub, strVinSelectionTitle);
    if (blnOnLoad != true) {
        if (blnOasisError != true) {
            var strVin = (ptsSession.vinInfoJson && ptsSession.vinInfoJson.Vin) ? ptsSession.vinInfoJson.Vin : "";
            setDefaultMainTabsTitle(freeSub, "");
            if (strVin.length == 0) {
                if (freeSub)
                    $('li#mainTabLi-OASIS').attr("title", strVinRequiredTitle);
                else
                    $('li#mainTabLi-OASIS, li#mainTabLi-PDI').attr("title", strVinRequiredTitle);
            }
        }
    }
}

function setDefaultMainTabsTitle(freeSub, strTitle) {
    if (freeSub)
        $('#mainTabs > ul > li').not('li#mainTabLi-HOME,li#mainTabLi-VID').attr("title", strTitle);

    else
        $('#mainTabs > ul > li').not('li#mainTabLi-HOME,li#mainTabLi-VID,li#mainTabLi-ADMINTOOLS,mainTabLi-TOOLBOX').attr("title", strTitle);
}

function resetIframes() {
    $('#mainTabs iframe').not('#mainTabFrame-HOME,#subTabFrame-WELCOME,#mainTabFrame-VID').attr('src', '');
    $('a[data-iframe-src-withvin]').not('#mainTabLink-HOME,#subTabLink-WELCOME,#mainTabLink-VID').each(function () {
        $(this).attr('data-iframe-src-withvin', "");
    });
}

function resetIframe(tab, src) {
    if (src && src != "") {
        $('iframe#mainTabFrame-' + tab).attr('src', src);
        $('a#mainTabLink-' + tab).attr('data-iframe-src-withvin', src);
    }
    else {
        $('iframe#mainTabFrame-' + tab).attr('src', '');
        $('a#mainTabLink-' + tab).attr('data-iframe-src-withvin', '');
    }
}

function getVinFromVinInfoJson() {
    if (top.window.ptsSession &&
        top.window.ptsSession.vinInfoJson &&
        top.window.ptsSession.vinInfoJson.Vin &&
        top.window.ptsSession.vinInfoJson.Vin !== '')
        return top.window.ptsSession.vinInfoJson.Vin;
    else
        return "";
}

function getParamValue(strValue) {
    if (strValue == null) {
        return "";
    } else {
        return strValue
    }
}

function getTabLinkSelector(tabModuleName) {
    return "#mainTabLink-" + tabModuleName;
}

function getTabFrameSelector(tabModuleName) {
    return "#mainTabFrame-" + tabModuleName;
}

function getActiveMainTabWidth() {
    var activeMainTabId = $("#mainTabs > ul.nav-tabs > li.active").attr("id");
    var activeMainTabLinkFrameId = activeMainTabId.replace("Li", "Frame");

    return $("#" + activeMainTabLinkFrameId).css("width")
}

function getActiveMainTabHeight() {
    var activeMainTabId = $("#mainTabs > ul.nav-tabs > li.active").attr("id");
    var activeMainTabLinkFrameId = activeMainTabId.replace("Li", "Frame");

    return $("#" + activeMainTabLinkFrameId).css("height")
}

function showTab(tabModuleName) {
    var tabLinkId = "mainTabLink-" + tabModuleName;
    showTabPane(tabLinkId);
}

function showTsbTab() {
    //resetIframes();
    disableMainTabs();
    var idOfTabNextToOasis = $("#mainTabLi-TSBGSBSSM")[0].id;
    idOfTabNextToOasis = idOfTabNextToOasis.replace("Li", "Link");
    showTabPane(idOfTabNextToOasis);
    enableMainTabs();
    //disableMainTab("mainTabLi-OASIS");
    disableMainTab("mainTabLi-PDI");
    disableMainTab("mainTabLi-DIAGNOSTICS");
    disableMainTab("mainTabLi-CONVEHICLE");
    if (isFreeSubscription()) {
        if (top.ptsSession && top.ptsSession.year && top.ptsSession.year >= new Date().getFullYear() && top.ptsSession.vin && top.ptsSession.vin.length > 0) {
            enableMainTabs();
        }
        //   else {
        //  disableMainTab("mainTabLi-WSM");
        //  disableMainTab("mainTabLi-EVTM");
        //  disableMainTab("mainTabLi-PCED");
        //   disableMainTab("mainTabLi-TIP");
        // disableMainTab("mainTabLi-SLTS");
        //  }
    }
    setMainTabsTitle(false, false);
    showVin();
}

function printWsmContent() {
    $('#wsmContentFrame').focus();
    //window.document.getElementById("mainTabFrame-WSM").contentWindow.print();
    window.document.getElementById("mainTabFrame-WSM").contentWindow.document.execCommand('print', false, null);
    //$('#wsmContentFrame').print();

    //frm.focus();// focus on contentWindow is needed on some ie versions
    //frm.print();
    //return false;

    //window.print();
    //document.getElementById("wsmContentFrame")[0].contentWindow.print();
    //document.getElementById("wsmContentFrame").document.execCommand('print', false, null);
    //document.getElementById('wsmContentFrame').focus();
    //parent.frames["mainTabLink-WSM"].document.frames["wsmContentFrame"].document.execCommand('print', false, null);
    //window.parent.frames('#mainTabDiv-WSM').document.getElementById("wsmContentFrame").print();
    //window.print();
    //window.parent.frames["wsmContentFrame"].print();
    //document.execCommand('print', false, null);
    /*
    var prtContent = document.getElementById(wsmContentFrame);
    var WinPrint = window.open('', '', 'letf=100,top=100,width=600,height=600');
    alert(prtContent.innerHTML);
    WinPrint.document.write(prtContent.innerHTML);
    WinPrint.document.close();
    WinPrint.focus();
    WinPrint.print();
    WinPrint.close()*/
}

function getWsmFrameWindow() {
    var strActiveTabFrameId = getActiveMainTabFrameId();
    if (strActiveTabFrameId == "mainTabFrame-PCED") //caller in the context of PCED tab
        return top.document.getElementById("mainTabFrame-PCED").contentWindow;
    else if (strActiveTabFrameId == "mainTabFrame-PCED_SBD") //caller in the context of PCED/SBD tab
        return top.document.getElementById("mainTabFrame-PCED_SBD").contentWindow;
    else //caller in the context of WSM, or Diagnostic or PTS tab
        return top.document.getElementById("mainTabFrame-WSM").contentWindow;
}

function removeFdrsCommsFrame() {
    var eleActiveTabContentPanelDiv = getWsmContentPanelDiv();
    if (eleActiveTabContentPanelDiv) {
        $(eleActiveTabContentPanelDiv).find("#fdrsCommsFrame").each(function () {
            $(this).remove();
        });
    }
}
function getWsmContentPanelDiv() {
    var strActiveTabFrameId = getActiveMainTabFrameId();
    if (strActiveTabFrameId == "mainTabFrame-PCED") //caller in the context of PCED tab
        return top.document.getElementById("mainTabDiv-PCED");
    else if (strActiveTabFrameId == "mainTabFrame-PCED_SBD") //caller in the context of PCED/SBD tab
        return top.document.getElementById("mainTabDiv-PCED_SBD");
    else //caller in the context of WSM, or Diagnostic or PTS tab
        return top.document.getElementById("mainTabDiv-WSM");
}

function onClickTechCentralLink(strUrl) {
    if (strUrl != null) {
        $('#mainTabs > ul > li').css('pointer-events', 'none');
        $('.preloader-wrapper').show();

        AddBackButtonInWelcomePane();
        $("#subTabFrame-WELCOME").attr("src", strUrl);
        var strHeight = $("#subTabFrame-WELCOME").height() - 25;
        strHeight = strHeight + "px";
        $("#subTabFrame-WELCOME").css("height", strHeight);
    }
}

function onClickBackBarBackLink() {
    $("#backbar").remove();
    $("#subTabFrame-WELCOME").attr("src", $("#subTabLink-WELCOME").attr("data-iframe-src"));
    var strHeight = $("#subTabFrame-WELCOME").height() + 25;
    strHeight = strHeight + "px";
    $("#subTabFrame-WELCOME").css("height", strHeight);
}

function AddBackButtonInWelcomePane() {
    $("#backbar").remove(); //remove before we add
    var backbar = document.createElement("div");
    backbar.innerHTML = "Back"
    backbar.setAttribute("id", "backbar");
    backbar.setAttribute('onclick', 'javascript:onClickBackBarBackLink();');

    var container = document.getElementById('subTabFrame-WELCOME');
    container.parentNode.insertBefore(backbar, container);
}

function showLoading() {

    hideCancelBtn(); //US3244548
    $('#mainTabs > ul > li').css('pointer-events', 'none');
    $('.preloader-wrapper').show();
}

function hideLoading() {
    $('#mainTabs > ul > li').css('pointer-events', 'auto');
    $('.preloader-wrapper').fadeOut();
}

function setLoadingText(message) {
    $("#loaderStatus").text(message);
}

function setSecondaryLoadingText(message) {
    $("#loaderSecondaryStatus").text(message);
}
//US3244548
function showLoadingWithCancelBtn() {
    $('#mainTabs > ul > li').css('pointer-events', 'none');
    $('.preloader-wrapper').show();
    showCancelBtn();
}
$("#btnCancel").click(function () {
    console.log("In index.cshtml Calling End NetworkTest");
    endNetworkTest();
});
function showCancelBtn() {
    $("#btnCancel").show();

}
function hideCancelBtn() {
    $("#btnCancel").hide();

}
//End US3244548
function reset_timer() {
    clearTimeout(menuTimer);
    menuTimer = setTimeout(function () { reset_all_menus(); }, 10000);
}

function reset_all_menus() {
    hovering = false;
    clearTimeout(menuTimer);
    $('ul.nav-tabs li.hovering').each(function () { $(this).removeClass('hovering').removeClass('touched').find('iframe.shim').remove().end(); });
}

//catch clicks for the modal window for the FLAG (when users change locale) to check if the cookies are still valid
$('#myModal').on('show.bs.modal', function () {
    validPTSCookies();
})

//validPTSCookies: goes through few main cookies and checks if each one exist, if not, it will redirect the user to the login controller.
function validPTSCookies() {
    return true;
    //console.log('document.referrer:' + document.referrer);
    //validate cookies in pts
    var cookie_perms = 'TPS-PERM';
    var cookie_tpsmembership = 'TPS-MEMBERSHIP';
    var cookie_preferences = 'PREFERENCES';

    if (!isValidCookie(cookie_perms)) {
        var reason = 'TPS-PERM cookie is not valid';
        // no cookie, redirect to the login controller so it can take care of authorization logic
        redirectToLogin(reason);
    }
    if (!isValidCookie(cookie_tpsmembership)) {
        var reason = 'TPS-MEMBERSHIP cookie is not valid';
        // no cookie, redirect to the login controller so it can take care of authorization logic
        redirectToLogin(reason);
    }
    if (!isValidCookie(cookie_preferences)) {
        var reason = 'PREFERENCES cookie is not valid';
        // no cookie, redirect to the login controller so it can take care of authorization logic
        redirectToLogin(reason);
    }
    return true;
}
function isValidCookie(cookieName) {
    var cookieValue = Cookies.get(cookieName);
    if (typeof cookieValue == 'undefined') {
        return false;
    }
    return true;
}
function redirectToLogin(redirectReason) {
    top.location.href = '/login?reason=' + redirectReason;
}

function setCookie(cname, cvalue, exdays, ysnSet) {
    const d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    let expires = "expires=" + d.toUTCString();
    if (ysnSet) {
        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    }
    $('#mobileToDesktopFeatureDefined').popover('hide');

}

function getCookie(cname) {
    let name = cname + "=";
    let decodedCookie = decodeURIComponent(document.cookie);
    let ca = decodedCookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) == ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
        }
    }
    return "";
}

$(document).ready(function () {
    $(document).on('click', '#mainTabMenuItemLink-EZU', function (event) {
        event.preventDefault();
        $('#mainTabLink-HOME').click();
        $('#subTabLink-EZU').click();
    });


    function GetCookieValueFromRegex(RegexString) {
        try {
            var Regex = new RegExp(RegexString, 'mi');
            return document.cookie.match(Regex)[0]; //This will throw an error if no value is found
        }
        catch {
            return 'N/A'
        }
    }
    contentMarket = GetCookieValueFromRegex('(?<=MEMBERSHIP=.*?contentmkt=).*?(?=&|;)');


});