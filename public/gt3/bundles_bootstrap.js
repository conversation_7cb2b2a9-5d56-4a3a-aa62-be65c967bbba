!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],t):t(n.bootstrap={},n.jQuery,n.Popper)}(this,function(n,t,i){"use strict";function of(n,t){for(var i,r=0;r<t.length;r++)i=t[r],i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(n,i.key,i)}function g(n,t,i){return t&&of(n.prototype,t),i&&of(n,i),n}function v(n){for(var i,r,t=1;t<arguments.length;t++)i=null!=arguments[t]?arguments[t]:{},r=Object.keys(i),"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(i).filter(function(n){return Object.getOwnPropertyDescriptor(i,n).enumerable}))),r.forEach(function(t){var r,u,f;r=n;f=i[u=t];u in r?Object.defineProperty(r,u,{value:f,enumerable:!0,configurable:!0,writable:!0}):r[u]=f});return n}t=t&&t.hasOwnProperty("default")?t.default:t;i=i&&i.hasOwnProperty("default")?i.default:i;var y,dt,er,or,sf,sr,hf,cf,lf,ht,l,gt,hr,cr,lr,af,ni,vf,yf,ou,pf,wf,bf,su,hu,ti,e,ct,pi,nt,cu,kf,ar,df,wi,bi,gf,ne,tt,te,ut,ie,re,ue,fe,ee,oe,vr,se,he,ce,le,ae,lt,s,at,vt,ii,ve,yr,ye,ri,ft,ki,di,pr,lu,pe,we,au,ui,o,yt,gi,it,wr,be,ke,w,vu,et,de,ge,no,yu,to,nr,io,br,ro,uo,fo,eo,oo,so,ho,co,lo,ao,rt,r,pt,tr,p,vo,kr,yo,c,po,wo,pu,wt,ir,bo,ko,go,wu,bu,fi,u,ot,dr,b,ns,ku,ts,is,rs,us,ei,gr,fs,oi,si,es,os,hi,nu,ss,hs,rr,d,bt,tu,k,cs,du,ls,as,vs,ys,ps,ws,bs,ks,ur,a,st,iu,fr,ds,gu,gs,ru,nh,kt,th,ih,nf,uu,rh,tf,uh,fh,eh,oh,rf,ci,h,fu,li,sh,ai,hh,vi,ch,lh,uf,ah,vh,ff,ef,yh,ph,wh,yi,f=function(n){function r(i){var u=this,r=!1;return n(this).one(t.TRANSITION_END,function(){r=!0}),setTimeout(function(){r||t.triggerTransitionEnd(u)},i),this}var i="transitionend",t={TRANSITION_END:"bsTransitionEnd",getUID:function(n){for(;n+=~~(1e6*Math.random()),document.getElementById(n););return n},getSelectorFromElement:function(n){var t=n.getAttribute("data-target");t&&"#"!==t||(t=n.getAttribute("href")||"");try{return document.querySelector(t)?t:null}catch(n){return null}},getTransitionDurationFromElement:function(t){if(!t)return 0;var i=n(t).css("transition-duration");return parseFloat(i)?(i=i.split(",")[0],1e3*parseFloat(i)):0},reflow:function(n){return n.offsetHeight},triggerTransitionEnd:function(t){n(t).trigger(i)},supportsTransitionEnd:function(){return Boolean(i)},isElement:function(n){return(n[0]||n).nodeType},typeCheckConfig:function(n,i,r){var u,s;for(u in r)if(Object.prototype.hasOwnProperty.call(r,u)){var e=r[u],f=i[u],o=f&&t.isElement(f)?"element":(s=f,{}.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase());if(!new RegExp(e).test(o))throw new Error(n.toUpperCase()+': Option "'+u+'" provided type "'+o+'" but expected type "'+e+'".');}}};return n.fn.emulateTransitionEnd=r,n.event.special[t.TRANSITION_END]={bindType:i,delegateType:i,handle:function(t){if(n(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}},t}(t),bh=(dt="alert",or="."+(er="bs.alert"),sf=(y=t).fn[dt],sr={CLOSE:"close"+or,CLOSED:"closed"+or,CLICK_DATA_API:"click"+or+".data-api"},hf="alert",cf="fade",lf="show",ht=function(){function n(n){this._element=n}var t=n.prototype;return t.close=function(n){var t=this._element;n&&(t=this._getRootElement(n));this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},t.dispose=function(){y.removeData(this._element,er);this._element=null},t._getRootElement=function(n){var i=f.getSelectorFromElement(n),t=!1;return i&&(t=document.querySelector(i)),t||(t=y(n).closest("."+hf)[0]),t},t._triggerCloseEvent=function(n){var t=y.Event(sr.CLOSE);return y(n).trigger(t),t},t._removeElement=function(n){var i=this,t;(y(n).removeClass(lf),y(n).hasClass(cf))?(t=f.getTransitionDurationFromElement(n),y(n).one(f.TRANSITION_END,function(t){return i._destroyElement(n,t)}).emulateTransitionEnd(t)):this._destroyElement(n)},t._destroyElement=function(n){y(n).detach().trigger(sr.CLOSED).remove()},n._jQueryInterface=function(t){return this.each(function(){var r=y(this),i=r.data(er);i||(i=new n(this),r.data(er,i));"close"===t&&i[t](this)})},n._handleDismiss=function(n){return function(t){t&&t.preventDefault();n.close(this)}},g(n,null,[{key:"VERSION",get:function(){return"4.1.3"}}]),n}(),y(document).on(sr.CLICK_DATA_API,'[data-dismiss="alert"]',ht._handleDismiss(new ht)),y.fn[dt]=ht._jQueryInterface,y.fn[dt].Constructor=ht,y.fn[dt].noConflict=function(){return y.fn[dt]=sf,ht._jQueryInterface},ht),kh=(gt="button",cr="."+(hr="bs.button"),lr=".data-api",af=(l=t).fn[gt],ni="active",vf="btn",ou='[data-toggle^="button"]',pf='[data-toggle="buttons"]',wf="input",bf=".active",su=".btn",hu={CLICK_DATA_API:"click"+cr+lr,FOCUS_BLUR_DATA_API:(yf="focus")+cr+lr+" blur"+cr+lr},ti=function(){function n(n){this._element=n}var t=n.prototype;return t.toggle=function(){var i=!0,u=!0,t=l(this._element).closest(pf)[0],n,r;if(t&&(n=this._element.querySelector(wf),n)){if("radio"===n.type&&(n.checked&&this._element.classList.contains(ni)?i=!1:(r=t.querySelector(bf),r&&l(r).removeClass(ni))),i){if(n.hasAttribute("disabled")||t.hasAttribute("disabled")||n.classList.contains("disabled")||t.classList.contains("disabled"))return;n.checked=!this._element.classList.contains(ni);l(n).trigger("change")}n.focus();u=!1}u&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(ni));i&&l(this._element).toggleClass(ni)},t.dispose=function(){l.removeData(this._element,hr);this._element=null},n._jQueryInterface=function(t){return this.each(function(){var i=l(this).data(hr);i||(i=new n(this),l(this).data(hr,i));"toggle"===t&&i[t]()})},g(n,null,[{key:"VERSION",get:function(){return"4.1.3"}}]),n}(),l(document).on(hu.CLICK_DATA_API,ou,function(n){n.preventDefault();var t=n.target;l(t).hasClass(vf)||(t=l(t).closest(su));ti._jQueryInterface.call(l(t),"toggle")}).on(hu.FOCUS_BLUR_DATA_API,ou,function(n){var t=l(n.target).closest(su)[0];l(t).toggleClass(yf,/^focus(in)?$/.test(n.type))}),l.fn[gt]=ti._jQueryInterface,l.fn[gt].Constructor=ti,l.fn[gt].noConflict=function(){return l.fn[gt]=af,ti._jQueryInterface},ti),dh=(ct="carousel",nt="."+(pi="bs.carousel"),cu=".data-api",kf=(e=t).fn[ct],ar={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0},df={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean"},wi="next",bi="prev",gf="left",ne="right",tt={SLIDE:"slide"+nt,SLID:"slid"+nt,KEYDOWN:"keydown"+nt,MOUSEENTER:"mouseenter"+nt,MOUSELEAVE:"mouseleave"+nt,TOUCHEND:"touchend"+nt,LOAD_DATA_API:"load"+nt+cu,CLICK_DATA_API:"click"+nt+cu},te="carousel",ut="active",ie="slide",re="carousel-item-right",ue="carousel-item-left",fe="carousel-item-next",ee="carousel-item-prev",oe=".active",vr=".active.carousel-item",se=".carousel-item",he=".carousel-item-next, .carousel-item-prev",ce=".carousel-indicators",le="[data-slide], [data-slide-to]",ae='[data-ride="carousel"]',lt=function(){function t(n,t){this._items=null;this._interval=null;this._activeElement=null;this._isPaused=!1;this._isSliding=!1;this.touchTimeout=null;this._config=this._getConfig(t);this._element=e(n)[0];this._indicatorsElement=this._element.querySelector(ce);this._addEventListeners()}var n=t.prototype;return n.next=function(){this._isSliding||this._slide(wi)},n.nextWhenVisible=function(){!document.hidden&&e(this._element).is(":visible")&&"hidden"!==e(this._element).css("visibility")&&this.next()},n.prev=function(){this._isSliding||this._slide(bi)},n.pause=function(n){n||(this._isPaused=!0);this._element.querySelector(he)&&(f.triggerTransitionEnd(this._element),this.cycle(!0));clearInterval(this._interval);this._interval=null},n.cycle=function(n){n||(this._isPaused=!1);this._interval&&(clearInterval(this._interval),this._interval=null);this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},n.to=function(n){var r=this,t,i;if(this._activeElement=this._element.querySelector(vr),t=this._getItemIndex(this._activeElement),!(n>this._items.length-1||n<0))if(this._isSliding)e(this._element).one(tt.SLID,function(){return r.to(n)});else{if(t===n)return this.pause(),void this.cycle();i=t<n?wi:bi;this._slide(i,this._items[n])}},n.dispose=function(){e(this._element).off(nt);e.removeData(this._element,pi);this._items=null;this._config=null;this._element=null;this._interval=null;this._isPaused=null;this._isSliding=null;this._activeElement=null;this._indicatorsElement=null},n._getConfig=function(n){return n=v({},ar,n),f.typeCheckConfig(ct,n,df),n},n._addEventListeners=function(){var n=this;this._config.keyboard&&e(this._element).on(tt.KEYDOWN,function(t){return n._keydown(t)});"hover"===this._config.pause&&(e(this._element).on(tt.MOUSEENTER,function(t){return n.pause(t)}).on(tt.MOUSELEAVE,function(t){return n.cycle(t)}),"ontouchstart"in document.documentElement&&e(this._element).on(tt.TOUCHEND,function(){n.pause();n.touchTimeout&&clearTimeout(n.touchTimeout);n.touchTimeout=setTimeout(function(t){return n.cycle(t)},500+n._config.interval)}))},n._keydown=function(n){if(!/input|textarea/i.test(n.target.tagName))switch(n.which){case 37:n.preventDefault();this.prev();break;case 39:n.preventDefault();this.next()}},n._getItemIndex=function(n){return this._items=n&&n.parentNode?[].slice.call(n.parentNode.querySelectorAll(se)):[],this._items.indexOf(n)},n._getItemByDirection=function(n,t){var u=n===wi,f=n===bi,i=this._getItemIndex(t),e=this._items.length-1,r;return(f&&0===i||u&&i===e)&&!this._config.wrap?t:(r=(i+(n===bi?-1:1))%this._items.length,-1===r?this._items[this._items.length-1]:this._items[r])},n._triggerSlideEvent=function(n,t){var r=this._getItemIndex(n),u=this._getItemIndex(this._element.querySelector(vr)),i=e.Event(tt.SLIDE,{relatedTarget:n,direction:t,from:u,to:r});return e(this._element).trigger(i),i},n._setActiveIndicatorElement=function(n){var i,t;this._indicatorsElement&&(i=[].slice.call(this._indicatorsElement.querySelectorAll(oe)),e(i).removeClass(ut),t=this._indicatorsElement.children[this._getItemIndex(n)],t&&e(t).addClass(ut))},n._slide=function(n,t){var u,o,s,c=this,r=this._element.querySelector(vr),v=this._getItemIndex(r),i=t||r&&this._getItemByDirection(n,r),y=this._getItemIndex(i),l=Boolean(this._interval),h,a;(n===wi?(u=ue,o=fe,s=gf):(u=re,o=ee,s=ne),i&&e(i).hasClass(ut))?this._isSliding=!1:!this._triggerSlideEvent(i,s).isDefaultPrevented()&&r&&i&&(this._isSliding=!0,l&&this.pause(),this._setActiveIndicatorElement(i),h=e.Event(tt.SLID,{relatedTarget:i,direction:s,from:v,to:y}),e(this._element).hasClass(ie)?(e(i).addClass(o),f.reflow(i),e(r).addClass(u),e(i).addClass(u),a=f.getTransitionDurationFromElement(r),e(r).one(f.TRANSITION_END,function(){e(i).removeClass(u+" "+o).addClass(ut);e(r).removeClass(ut+" "+o+" "+u);c._isSliding=!1;setTimeout(function(){return e(c._element).trigger(h)},0)}).emulateTransitionEnd(a)):(e(r).removeClass(ut),e(i).addClass(ut),this._isSliding=!1,e(this._element).trigger(h)),l&&this.cycle())},t._jQueryInterface=function(n){return this.each(function(){var i=e(this).data(pi),r=v({},ar,e(this).data()),u;if("object"==typeof n&&(r=v({},r,n)),u="string"==typeof n?n:r.slide,i||(i=new t(this,r),e(this).data(pi,i)),"number"==typeof n)i.to(n);else if("string"==typeof u){if("undefined"==typeof i[u])throw new TypeError('No method named "'+u+'"');i[u]()}else r.interval&&(i.pause(),i.cycle())})},t._dataApiClickHandler=function(n){var o=f.getSelectorFromElement(this),i,u,r;o&&(i=e(o)[0],i&&e(i).hasClass(te)&&(u=v({},e(i).data(),e(this).data()),r=this.getAttribute("data-slide-to"),r&&(u.interval=!1),t._jQueryInterface.call(e(i),u),r&&e(i).data(pi).to(r),n.preventDefault()))},g(t,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return ar}}]),t}(),e(document).on(tt.CLICK_DATA_API,le,lt._dataApiClickHandler),e(window).on(tt.LOAD_DATA_API,function(){for(var t,i=[].slice.call(document.querySelectorAll(ae)),n=0,r=i.length;n<r;n++)t=e(i[n]),lt._jQueryInterface.call(t,t.data())}),e.fn[ct]=lt._jQueryInterface,e.fn[ct].Constructor=lt,e.fn[ct].noConflict=function(){return e.fn[ct]=kf,lt._jQueryInterface},lt),gh=(at="collapse",ii="."+(vt="bs.collapse"),ve=(s=t).fn[at],yr={toggle:!0,parent:""},ye={toggle:"boolean",parent:"(string|element)"},ri={SHOW:"show"+ii,SHOWN:"shown"+ii,HIDE:"hide"+ii,HIDDEN:"hidden"+ii,CLICK_DATA_API:"click"+ii+".data-api"},ft="show",ki="collapse",di="collapsing",pr="collapsed",lu="width",pe="height",we=".show, .collapsing",au='[data-toggle="collapse"]',ui=function(){function t(n,t){this._isTransitioning=!1;this._element=n;this._config=this._getConfig(t);this._triggerArray=s.makeArray(document.querySelectorAll('[data-toggle="collapse"][href="#'+n.id+'"],[data-toggle="collapse"][data-target="#'+n.id+'"]'));for(var u=[].slice.call(document.querySelectorAll(au)),i=0,o=u.length;i<o;i++){var e=u[i],r=f.getSelectorFromElement(e),h=[].slice.call(document.querySelectorAll(r)).filter(function(t){return t===n});null!==r&&0<h.length&&(this._selector=r,this._triggerArray.push(e))}this._parent=this._config.parent?this._getParent():null;this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray);this._config.toggle&&this.toggle()}var n=t.prototype;return n.toggle=function(){s(this._element).hasClass(ft)?this.hide():this.show()},n.show=function(){var n,u,r=this,e,i,o,h;this._isTransitioning||s(this._element).hasClass(ft)||(this._parent&&0===(n=[].slice.call(this._parent.querySelectorAll(we)).filter(function(n){return n.getAttribute("data-parent")===r._config.parent})).length&&(n=null),n&&(u=s(n).not(this._selector).data(vt))&&u._isTransitioning)||(e=s.Event(ri.SHOW),(s(this._element).trigger(e),e.isDefaultPrevented())||(n&&(t._jQueryInterface.call(s(n).not(this._selector),"hide"),u||s(n).data(vt,null)),i=this._getDimension(),s(this._element).removeClass(ki).addClass(di),this._element.style[i]=0,this._triggerArray.length&&s(this._triggerArray).removeClass(pr).attr("aria-expanded",!0),this.setTransitioning(!0),o="scroll"+(i[0].toUpperCase()+i.slice(1)),h=f.getTransitionDurationFromElement(this._element),s(this._element).one(f.TRANSITION_END,function(){s(r._element).removeClass(di).addClass(ki).addClass(ft);r._element.style[i]="";r.setTransitioning(!1);s(r._element).trigger(ri.SHOWN)}).emulateTransitionEnd(h),this._element.style[i]=this._element[o]+"px"))},n.hide=function(){var o=this,i,n,r,t,u,e,h;if(!this._isTransitioning&&s(this._element).hasClass(ft)&&(i=s.Event(ri.HIDE),s(this._element).trigger(i),!i.isDefaultPrevented())){if(n=this._getDimension(),this._element.style[n]=this._element.getBoundingClientRect()[n]+"px",f.reflow(this._element),s(this._element).addClass(di).removeClass(ki).removeClass(ft),r=this._triggerArray.length,0<r)for(t=0;t<r;t++)u=this._triggerArray[t],e=f.getSelectorFromElement(u),null!==e&&(s([].slice.call(document.querySelectorAll(e))).hasClass(ft)||s(u).addClass(pr).attr("aria-expanded",!1));this.setTransitioning(!0);this._element.style[n]="";h=f.getTransitionDurationFromElement(this._element);s(this._element).one(f.TRANSITION_END,function(){o.setTransitioning(!1);s(o._element).removeClass(di).addClass(ki).trigger(ri.HIDDEN)}).emulateTransitionEnd(h)}},n.setTransitioning=function(n){this._isTransitioning=n},n.dispose=function(){s.removeData(this._element,vt);this._config=null;this._parent=null;this._element=null;this._triggerArray=null;this._isTransitioning=null},n._getConfig=function(n){return(n=v({},yr,n)).toggle=Boolean(n.toggle),f.typeCheckConfig(at,n,ye),n},n._getDimension=function(){return s(this._element).hasClass(lu)?lu:pe},n._getParent=function(){var u=this,n=null,i,r;return f.isElement(this._config.parent)?(n=this._config.parent,"undefined"!=typeof this._config.parent.jquery&&(n=this._config.parent[0])):n=document.querySelector(this._config.parent),i='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',r=[].slice.call(n.querySelectorAll(i)),s(r).each(function(n,i){u._addAriaAndCollapsedClass(t._getTargetFromElement(i),[i])}),n},n._addAriaAndCollapsedClass=function(n,t){if(n){var i=s(n).hasClass(ft);t.length&&s(t).toggleClass(pr,!i).attr("aria-expanded",i)}},t._getTargetFromElement=function(n){var t=f.getSelectorFromElement(n);return t?document.querySelector(t):null},t._jQueryInterface=function(n){return this.each(function(){var r=s(this),i=r.data(vt),u=v({},yr,r.data(),"object"==typeof n&&n?n:{});if(!i&&u.toggle&&/show|hide/.test(n)&&(u.toggle=!1),i||(i=new t(this,u),r.data(vt,i)),"string"==typeof n){if("undefined"==typeof i[n])throw new TypeError('No method named "'+n+'"');i[n]()}})},g(t,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return yr}}]),t}(),s(document).on(ri.CLICK_DATA_API,au,function(n){"A"===n.currentTarget.tagName&&n.preventDefault();var t=s(this),i=f.getSelectorFromElement(this),r=[].slice.call(document.querySelectorAll(i));s(r).each(function(){var n=s(this),i=n.data(vt)?"toggle":t.data();ui._jQueryInterface.call(n,i)})}),s.fn[at]=ui._jQueryInterface,s.fn[at].Constructor=ui,s.fn[at].noConflict=function(){return s.fn[at]=ve,ui._jQueryInterface},ui),nc=(yt="dropdown",it="."+(gi="bs.dropdown"),wr=".data-api",be=(o=t).fn[yt],ke=new RegExp("38|40|27"),w={HIDE:"hide"+it,HIDDEN:"hidden"+it,SHOW:"show"+it,SHOWN:"shown"+it,CLICK:"click"+it,CLICK_DATA_API:"click"+it+wr,KEYDOWN_DATA_API:"keydown"+it+wr,KEYUP_DATA_API:"keyup"+it+wr},vu="disabled",et="show",de="dropup",ge="dropright",no="dropleft",yu="dropdown-menu-right",to="position-static",nr='[data-toggle="dropdown"]',io=".dropdown form",br=".dropdown-menu",ro=".navbar-nav",uo=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",fo="top-start",eo="top-end",oo="bottom-start",so="bottom-end",ho="right-start",co="left-start",lo={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic"},ao={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string"},rt=function(){function n(n,t){this._element=n;this._popper=null;this._config=this._getConfig(t);this._menu=this._getMenuElement();this._inNavbar=this._detectNavbar();this._addEventListeners()}var t=n.prototype;return t.toggle=function(){var t,s,u,e,r;if(!this._element.disabled&&!o(this._element).hasClass(vu)&&(t=n._getParentFromElement(this._element),s=o(this._menu).hasClass(et),(n._clearMenus(),!s)&&(u={relatedTarget:this._element},e=o.Event(w.SHOW,u),o(t).trigger(e),!e.isDefaultPrevented()))){if(!this._inNavbar){if("undefined"==typeof i)throw new TypeError("Bootstrap dropdown require Popper.js (https://popper.js.org)");r=this._element;"parent"===this._config.reference?r=t:f.isElement(this._config.reference)&&(r=this._config.reference,"undefined"!=typeof this._config.reference.jquery&&(r=this._config.reference[0]));"scrollParent"!==this._config.boundary&&o(t).addClass(to);this._popper=new i(r,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===o(t).closest(ro).length&&o(document.body).children().on("mouseover",null,o.noop);this._element.focus();this._element.setAttribute("aria-expanded",!0);o(this._menu).toggleClass(et);o(t).toggleClass(et).trigger(o.Event(w.SHOWN,u))}},t.dispose=function(){o.removeData(this._element,gi);o(this._element).off(it);this._element=null;(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},t.update=function(){this._inNavbar=this._detectNavbar();null!==this._popper&&this._popper.scheduleUpdate()},t._addEventListeners=function(){var n=this;o(this._element).on(w.CLICK,function(t){t.preventDefault();t.stopPropagation();n.toggle()})},t._getConfig=function(n){return n=v({},this.constructor.Default,o(this._element).data(),n),f.typeCheckConfig(yt,n,this.constructor.DefaultType),n},t._getMenuElement=function(){if(!this._menu){var t=n._getParentFromElement(this._element);t&&(this._menu=t.querySelector(br))}return this._menu},t._getPlacement=function(){var t=o(this._element.parentNode),n=oo;return t.hasClass(de)?(n=fo,o(this._menu).hasClass(yu)&&(n=eo)):t.hasClass(ge)?n=ho:t.hasClass(no)?n=co:o(this._menu).hasClass(yu)&&(n=so),n},t._detectNavbar=function(){return 0<o(this._element).closest(".navbar").length},t._getPopperConfig=function(){var i=this,n={},t;return"function"==typeof this._config.offset?n.fn=function(n){return n.offsets=v({},n.offsets,i._config.offset(n.offsets)||{}),n}:n.offset=this._config.offset,t={placement:this._getPlacement(),modifiers:{offset:n,flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}},"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),t},n._jQueryInterface=function(t){return this.each(function(){var i=o(this).data(gi);if(i||(i=new n(this,"object"==typeof t?t:null),o(this).data(gi,i)),"string"==typeof t){if("undefined"==typeof i[t])throw new TypeError('No method named "'+t+'"');i[t]()}})},n._clearMenus=function(t){var h,e;if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var r=[].slice.call(document.querySelectorAll(nr)),i=0,c=r.length;i<c;i++){var u=n._getParentFromElement(r[i]),s=o(r[i]).data(gi),f={relatedTarget:r[i]};(t&&"click"===t.type&&(f.clickEvent=t),s)&&(h=s._menu,!o(u).hasClass(et)||t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&o.contains(u,t.target)||(e=o.Event(w.HIDE,f),o(u).trigger(e),e.isDefaultPrevented()||("ontouchstart"in document.documentElement&&o(document.body).children().off("mouseover",null,o.noop),r[i].setAttribute("aria-expanded","false"),o(h).removeClass(et),o(u).removeClass(et).trigger(o.Event(w.HIDDEN,f)))))}},n._getParentFromElement=function(n){var t,i=f.getSelectorFromElement(n);return i&&(t=document.querySelector(i)),t||n.parentNode},n._dataApiKeydownHandler=function(t){var u,f,r,i,e;(/input|textarea/i.test(t.target.tagName)?32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||o(t.target).closest(br).length):!ke.test(t.which))||(t.preventDefault(),t.stopPropagation(),this.disabled||o(this).hasClass(vu))||(u=n._getParentFromElement(this),f=o(u).hasClass(et),(f||27===t.which&&32===t.which)&&(!f||27!==t.which&&32!==t.which)?(r=[].slice.call(u.querySelectorAll(uo)),0!==r.length&&(i=r.indexOf(t.target),38===t.which&&0<i&&i--,40===t.which&&i<r.length-1&&i++,i<0&&(i=0),r[i].focus())):(27===t.which&&(e=u.querySelector(nr),o(e).trigger("focus")),o(this).trigger("click")))},g(n,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return lo}},{key:"DefaultType",get:function(){return ao}}]),n}(),o(document).on(w.KEYDOWN_DATA_API,nr,rt._dataApiKeydownHandler).on(w.KEYDOWN_DATA_API,br,rt._dataApiKeydownHandler).on(w.CLICK_DATA_API+" "+w.KEYUP_DATA_API,rt._clearMenus).on(w.CLICK_DATA_API,nr,function(n){n.preventDefault();n.stopPropagation();rt._jQueryInterface.call(o(this),"toggle")}).on(w.CLICK_DATA_API,io,function(n){n.stopPropagation()}),o.fn[yt]=rt._jQueryInterface,o.fn[yt].Constructor=rt,o.fn[yt].noConflict=function(){return o.fn[yt]=be,rt._jQueryInterface},rt),tc=(pt="modal",p="."+(tr="bs.modal"),vo=(r=t).fn[pt],kr={backdrop:!0,keyboard:!0,focus:!0,show:!0},yo={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},c={HIDE:"hide"+p,HIDDEN:"hidden"+p,SHOW:"show"+p,SHOWN:"shown"+p,FOCUSIN:"focusin"+p,RESIZE:"resize"+p,CLICK_DISMISS:"click.dismiss"+p,KEYDOWN_DISMISS:"keydown.dismiss"+p,MOUSEUP_DISMISS:"mouseup.dismiss"+p,MOUSEDOWN_DISMISS:"mousedown.dismiss"+p,CLICK_DATA_API:"click"+p+".data-api"},po="modal-scrollbar-measure",wo="modal-backdrop",pu="modal-open",wt="fade",ir="show",bo=".modal-dialog",ko='[data-toggle="modal"]',go='[data-dismiss="modal"]',wu=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",bu=".sticky-top",fi=function(){function t(n,t){this._config=this._getConfig(t);this._element=n;this._dialog=n.querySelector(bo);this._backdrop=null;this._isShown=!1;this._isBodyOverflowing=!1;this._ignoreBackdropClick=!1;this._scrollbarWidth=0}var n=t.prototype;return n.toggle=function(n){return this._isShown?this.hide():this.show(n)},n.show=function(n){var t=this,i;this._isTransitioning||this._isShown||(r(this._element).hasClass(wt)&&(this._isTransitioning=!0),i=r.Event(c.SHOW,{relatedTarget:n}),r(this._element).trigger(i),this._isShown||i.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),r(document.body).addClass(pu),this._setEscapeEvent(),this._setResizeEvent(),r(this._element).on(c.CLICK_DISMISS,go,function(n){return t.hide(n)}),r(this._dialog).on(c.MOUSEDOWN_DISMISS,function(){r(t._element).one(c.MOUSEUP_DISMISS,function(n){r(n.target).is(t._element)&&(t._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return t._showElement(n)})))},n.hide=function(n){var e=this,t,i,u;(n&&n.preventDefault(),!this._isTransitioning&&this._isShown)&&(t=r.Event(c.HIDE),(r(this._element).trigger(t),this._isShown&&!t.isDefaultPrevented())&&(this._isShown=!1,i=r(this._element).hasClass(wt),(i&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),r(document).off(c.FOCUSIN),r(this._element).removeClass(ir),r(this._element).off(c.CLICK_DISMISS),r(this._dialog).off(c.MOUSEDOWN_DISMISS),i)?(u=f.getTransitionDurationFromElement(this._element),r(this._element).one(f.TRANSITION_END,function(n){return e._hideModal(n)}).emulateTransitionEnd(u)):this._hideModal()))},n.dispose=function(){r.removeData(this._element,tr);r(window,document,this._element,this._backdrop).off(p);this._config=null;this._element=null;this._dialog=null;this._backdrop=null;this._isShown=null;this._isBodyOverflowing=null;this._ignoreBackdropClick=null;this._scrollbarWidth=null},n.handleUpdate=function(){this._adjustDialog()},n._getConfig=function(n){return n=v({},kr,n),f.typeCheckConfig(pt,n,yo),n},n._showElement=function(n){var t=this,u=r(this._element).hasClass(wt),e,i,o;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element);this._element.style.display="block";this._element.removeAttribute("aria-hidden");this._element.scrollTop=0;u&&f.reflow(this._element);r(this._element).addClass(ir);this._config.focus&&this._enforceFocus();e=r.Event(c.SHOWN,{relatedTarget:n});i=function(){t._config.focus&&t._element.focus();t._isTransitioning=!1;r(t._element).trigger(e)};u?(o=f.getTransitionDurationFromElement(this._element),r(this._dialog).one(f.TRANSITION_END,i).emulateTransitionEnd(o)):i()},n._enforceFocus=function(){var n=this;r(document).off(c.FOCUSIN).on(c.FOCUSIN,function(t){document!==t.target&&n._element!==t.target&&0===r(n._element).has(t.target).length&&n._element.focus()})},n._setEscapeEvent=function(){var n=this;this._isShown&&this._config.keyboard?r(this._element).on(c.KEYDOWN_DISMISS,function(t){27===t.which&&(t.preventDefault(),n.hide())}):this._isShown||r(this._element).off(c.KEYDOWN_DISMISS)},n._setResizeEvent=function(){var n=this;this._isShown?r(window).on(c.RESIZE,function(t){return n.handleUpdate(t)}):r(window).off(c.RESIZE)},n._hideModal=function(){var n=this;this._element.style.display="none";this._element.setAttribute("aria-hidden",!0);this._isTransitioning=!1;this._showBackdrop(function(){r(document.body).removeClass(pu);n._resetAdjustments();n._resetScrollbar();r(n._element).trigger(c.HIDDEN)})},n._removeBackdrop=function(){this._backdrop&&(r(this._backdrop).remove(),this._backdrop=null)},n._showBackdrop=function(n){var t=this,i=r(this._element).hasClass(wt)?wt:"",e,u,o;if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=wo,i&&this._backdrop.classList.add(i),r(this._backdrop).appendTo(document.body),r(this._element).on(c.CLICK_DISMISS,function(n){t._ignoreBackdropClick?t._ignoreBackdropClick=!1:n.target===n.currentTarget&&("static"===t._config.backdrop?t._element.focus():t.hide())}),i&&f.reflow(this._backdrop),r(this._backdrop).addClass(ir),!n)return;if(!i)return void n();e=f.getTransitionDurationFromElement(this._backdrop);r(this._backdrop).one(f.TRANSITION_END,n).emulateTransitionEnd(e)}else!this._isShown&&this._backdrop?(r(this._backdrop).removeClass(ir),u=function(){t._removeBackdrop();n&&n()},r(this._element).hasClass(wt)?(o=f.getTransitionDurationFromElement(this._backdrop),r(this._backdrop).one(f.TRANSITION_END,u).emulateTransitionEnd(o)):u()):n&&n()},n._adjustDialog=function(){var n=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&n&&(this._element.style.paddingLeft=this._scrollbarWidth+"px");this._isBodyOverflowing&&!n&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},n._resetAdjustments=function(){this._element.style.paddingLeft="";this._element.style.paddingRight=""},n._checkScrollbar=function(){var n=document.body.getBoundingClientRect();this._isBodyOverflowing=n.left+n.right<window.innerWidth;this._scrollbarWidth=this._getScrollbarWidth()},n._setScrollbar=function(){var n=this,t,i,u,f;this._isBodyOverflowing&&(t=[].slice.call(document.querySelectorAll(wu)),i=[].slice.call(document.querySelectorAll(bu)),r(t).each(function(t,i){var u=i.style.paddingRight,f=r(i).css("padding-right");r(i).data("padding-right",u).css("padding-right",parseFloat(f)+n._scrollbarWidth+"px")}),r(i).each(function(t,i){var u=i.style.marginRight,f=r(i).css("margin-right");r(i).data("margin-right",u).css("margin-right",parseFloat(f)-n._scrollbarWidth+"px")}),u=document.body.style.paddingRight,f=r(document.body).css("padding-right"),r(document.body).data("padding-right",u).css("padding-right",parseFloat(f)+this._scrollbarWidth+"px"))},n._resetScrollbar=function(){var i=[].slice.call(document.querySelectorAll(wu)),n,t;r(i).each(function(n,t){var i=r(t).data("padding-right");r(t).removeData("padding-right");t.style.paddingRight=i||""});n=[].slice.call(document.querySelectorAll(""+bu));r(n).each(function(n,t){var i=r(t).data("margin-right");"undefined"!=typeof i&&r(t).css("margin-right",i).removeData("margin-right")});t=r(document.body).data("padding-right");r(document.body).removeData("padding-right");document.body.style.paddingRight=t||""},n._getScrollbarWidth=function(){var n=document.createElement("div"),t;return n.className=po,document.body.appendChild(n),t=n.getBoundingClientRect().width-n.clientWidth,document.body.removeChild(n),t},t._jQueryInterface=function(n,i){return this.each(function(){var u=r(this).data(tr),f=v({},kr,r(this).data(),"object"==typeof n&&n?n:{});if(u||(u=new t(this,f),r(this).data(tr,u)),"string"==typeof n){if("undefined"==typeof u[n])throw new TypeError('No method named "'+n+'"');u[n](i)}else f.show&&u.show(i)})},g(t,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return kr}}]),t}(),r(document).on(c.CLICK_DATA_API,ko,function(n){var t,i=this,u=f.getSelectorFromElement(this),e,o;u&&(t=document.querySelector(u));e=r(t).data(tr)?"toggle":v({},r(t).data(),r(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||n.preventDefault();o=r(t).one(c.SHOW,function(n){n.isDefaultPrevented()||o.one(c.HIDDEN,function(){r(i).is(":visible")&&i.focus()})});fi._jQueryInterface.call(r(t),e,this)}),r.fn[pt]=fi._jQueryInterface,r.fn[pt].Constructor=fi,r.fn[pt].noConflict=function(){return r.fn[pt]=vo,fi._jQueryInterface},fi),eu=(ot="tooltip",b="."+(dr="bs.tooltip"),ns=(u=t).fn[ot],ku="bs-tooltip",ts=new RegExp("(^|\\s)"+ku+"\\S+","g"),us={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"><\/div><div class="tooltip-inner"><\/div><\/div>',trigger:"hover focus",title:"",delay:0,html:!(rs={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"}),selector:!(is={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)"}),placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent"},gr="out",fs={HIDE:"hide"+b,HIDDEN:"hidden"+b,SHOW:(ei="show")+b,SHOWN:"shown"+b,INSERTED:"inserted"+b,CLICK:"click"+b,FOCUSIN:"focusin"+b,FOCUSOUT:"focusout"+b,MOUSEENTER:"mouseenter"+b,MOUSELEAVE:"mouseleave"+b},oi="fade",si="show",es=".tooltip-inner",os=".arrow",hi="hover",nu="focus",ss="click",hs="manual",rr=function(){function t(n,t){if("undefined"==typeof i)throw new TypeError("Bootstrap tooltips require Popper.js (https://popper.js.org)");this._isEnabled=!0;this._timeout=0;this._hoverState="";this._activeTrigger={};this._popper=null;this.element=n;this.config=this._getConfig(t);this.tip=null;this._setListeners()}var n=t.prototype;return n.enable=function(){this._isEnabled=!0},n.disable=function(){this._isEnabled=!1},n.toggleEnabled=function(){this._isEnabled=!this._isEnabled},n.toggle=function(n){if(this._isEnabled)if(n){var i=this.constructor.DATA_KEY,t=u(n.currentTarget).data(i);t||(t=new this.constructor(n.currentTarget,this._getDelegateConfig()),u(n.currentTarget).data(i,t));t._activeTrigger.click=!t._activeTrigger.click;t._isWithActiveTrigger()?t._enter(null,t):t._leave(null,t)}else{if(u(this.getTipElement()).hasClass(si))return void this._leave(null,this);this._enter(null,this)}},n.dispose=function(){clearTimeout(this._timeout);u.removeData(this.element,this.constructor.DATA_KEY);u(this.element).off(this.constructor.EVENT_KEY);u(this.element).closest(".modal").off("hide.bs.modal");this.tip&&u(this.tip).remove();this._isEnabled=null;this._timeout=null;this._hoverState=null;(this._activeTrigger=null)!==this._popper&&this._popper.destroy();this._popper=null;this.element=null;this.config=null;this.tip=null},n.show=function(){var n=this,r,h,t,e,c,o,l,s,a;if("none"===u(this.element).css("display"))throw new Error("Please use show on visible elements");if(r=u.Event(this.constructor.Event.SHOW),this.isWithContent()&&this._isEnabled){if(u(this.element).trigger(r),h=u.contains(this.element.ownerDocument.documentElement,this.element),r.isDefaultPrevented()||!h)return;t=this.getTipElement();e=f.getUID(this.constructor.NAME);t.setAttribute("id",e);this.element.setAttribute("aria-describedby",e);this.setContent();this.config.animation&&u(t).addClass(oi);c="function"==typeof this.config.placement?this.config.placement.call(this,t,this.element):this.config.placement;o=this._getAttachment(c);this.addAttachmentClass(o);l=!1===this.config.container?document.body:u(document).find(this.config.container);u(t).data(this.constructor.DATA_KEY,this);u.contains(this.element.ownerDocument.documentElement,this.tip)||u(t).appendTo(l);u(this.element).trigger(this.constructor.Event.INSERTED);this._popper=new i(this.element,t,{placement:o,modifiers:{offset:{offset:this.config.offset},flip:{behavior:this.config.fallbackPlacement},arrow:{element:os},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&n._handlePopperPlacementChange(t)},onUpdate:function(t){n._handlePopperPlacementChange(t)}});u(t).addClass(si);"ontouchstart"in document.documentElement&&u(document.body).children().on("mouseover",null,u.noop);s=function(){n.config.animation&&n._fixTransition();var t=n._hoverState;n._hoverState=null;u(n.element).trigger(n.constructor.Event.SHOWN);t===gr&&n._leave(null,n)};u(this.tip).hasClass(oi)?(a=f.getTransitionDurationFromElement(this.tip),u(this.tip).one(f.TRANSITION_END,s).emulateTransitionEnd(a)):s()}},n.hide=function(n){var t=this,i=this.getTipElement(),r=u.Event(this.constructor.Event.HIDE),e=function(){t._hoverState!==ei&&i.parentNode&&i.parentNode.removeChild(i);t._cleanTipClass();t.element.removeAttribute("aria-describedby");u(t.element).trigger(t.constructor.Event.HIDDEN);null!==t._popper&&t._popper.destroy();n&&n()},o;(u(this.element).trigger(r),r.isDefaultPrevented())||((u(i).removeClass(si),"ontouchstart"in document.documentElement&&u(document.body).children().off("mouseover",null,u.noop),this._activeTrigger[ss]=!1,this._activeTrigger[nu]=!1,this._activeTrigger[hi]=!1,u(this.tip).hasClass(oi))?(o=f.getTransitionDurationFromElement(i),u(i).one(f.TRANSITION_END,e).emulateTransitionEnd(o)):e(),this._hoverState="")},n.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},n.isWithContent=function(){return Boolean(this.getTitle())},n.addAttachmentClass=function(n){u(this.getTipElement()).addClass(ku+"-"+n)},n.getTipElement=function(){return this.tip=this.tip||u(this.config.template)[0],this.tip},n.setContent=function(){var n=this.getTipElement();this.setElementContent(u(n.querySelectorAll(es)),this.getTitle());u(n).removeClass(oi+" "+si)},n.setElementContent=function(n,t){var i=this.config.html;"object"==typeof t&&(t.nodeType||t.jquery)?i?u(t).parent().is(n)||n.empty().append(t):n.text(u(t).text()):n[i?"html":"text"](t)},n.getTitle=function(){var n=this.element.getAttribute("data-original-title");return n||(n="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),n},n._getAttachment=function(n){return rs[n.toUpperCase()]},n._setListeners=function(){var n=this;this.config.trigger.split(" ").forEach(function(t){if("click"===t)u(n.element).on(n.constructor.Event.CLICK,n.config.selector,function(t){return n.toggle(t)});else if(t!==hs){var i=t===hi?n.constructor.Event.MOUSEENTER:n.constructor.Event.FOCUSIN,r=t===hi?n.constructor.Event.MOUSELEAVE:n.constructor.Event.FOCUSOUT;u(n.element).on(i,n.config.selector,function(t){return n._enter(t)}).on(r,n.config.selector,function(t){return n._leave(t)})}u(n.element).closest(".modal").on("hide.bs.modal",function(){return n.hide()})});this.config.selector?this.config=v({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},n._fixTitle=function(){var n=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==n)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},n._enter=function(n,t){var i=this.constructor.DATA_KEY;(t=t||u(n.currentTarget).data(i))||(t=new this.constructor(n.currentTarget,this._getDelegateConfig()),u(n.currentTarget).data(i,t));n&&(t._activeTrigger["focusin"===n.type?nu:hi]=!0);u(t.getTipElement()).hasClass(si)||t._hoverState===ei?t._hoverState=ei:(clearTimeout(t._timeout),t._hoverState=ei,t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){t._hoverState===ei&&t.show()},t.config.delay.show):t.show())},n._leave=function(n,t){var i=this.constructor.DATA_KEY;(t=t||u(n.currentTarget).data(i))||(t=new this.constructor(n.currentTarget,this._getDelegateConfig()),u(n.currentTarget).data(i,t));n&&(t._activeTrigger["focusout"===n.type?nu:hi]=!1);t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState=gr,t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){t._hoverState===gr&&t.hide()},t.config.delay.hide):t.hide())},n._isWithActiveTrigger=function(){for(var n in this._activeTrigger)if(this._activeTrigger[n])return!0;return!1},n._getConfig=function(n){return"number"==typeof(n=v({},this.constructor.Default,u(this.element).data(),"object"==typeof n&&n?n:{})).delay&&(n.delay={show:n.delay,hide:n.delay}),"number"==typeof n.title&&(n.title=n.title.toString()),"number"==typeof n.content&&(n.content=n.content.toString()),f.typeCheckConfig(ot,n,this.constructor.DefaultType),n},n._getDelegateConfig=function(){var t={},n;if(this.config)for(n in this.config)this.constructor.Default[n]!==this.config[n]&&(t[n]=this.config[n]);return t},n._cleanTipClass=function(){var t=u(this.getTipElement()),n=t.attr("class").match(ts);null!==n&&n.length&&t.removeClass(n.join(""))},n._handlePopperPlacementChange=function(n){var t=n.instance;this.tip=t.popper;this._cleanTipClass();this.addAttachmentClass(this._getAttachment(n.placement))},n._fixTransition=function(){var n=this.getTipElement(),t=this.config.animation;null===n.getAttribute("x-placement")&&(u(n).removeClass(oi),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},t._jQueryInterface=function(n){return this.each(function(){var i=u(this).data(dr),r="object"==typeof n&&n;if((i||!/dispose|hide/.test(n))&&(i||(i=new t(this,r),u(this).data(dr,i)),"string"==typeof n)){if("undefined"==typeof i[n])throw new TypeError('No method named "'+n+'"');i[n]()}})},g(t,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return us}},{key:"NAME",get:function(){return ot}},{key:"DATA_KEY",get:function(){return dr}},{key:"Event",get:function(){return fs}},{key:"EVENT_KEY",get:function(){return b}},{key:"DefaultType",get:function(){return is}}]),t}(),u.fn[ot]=rr._jQueryInterface,u.fn[ot].Constructor=rr,u.fn[ot].noConflict=function(){return u.fn[ot]=ns,rr._jQueryInterface},rr),ic=(bt="popover",k="."+(tu="bs.popover"),cs=(d=t).fn[bt],du="bs-popover",ls=new RegExp("(^|\\s)"+du+"\\S+","g"),as=v({},eu.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"><\/div><h3 class="popover-header"><\/h3><div class="popover-body"><\/div><\/div>'}),vs=v({},eu.DefaultType,{content:"(string|element|function)"}),ys="fade",ws=".popover-header",bs=".popover-body",ks={HIDE:"hide"+k,HIDDEN:"hidden"+k,SHOW:(ps="show")+k,SHOWN:"shown"+k,INSERTED:"inserted"+k,CLICK:"click"+k,FOCUSIN:"focusin"+k,FOCUSOUT:"focusout"+k,MOUSEENTER:"mouseenter"+k,MOUSELEAVE:"mouseleave"+k},ur=function(n){function i(){return n.apply(this,arguments)||this}var r,u,t;return u=n,(r=i).prototype=Object.create(u.prototype),(r.prototype.constructor=r).__proto__=u,t=i.prototype,t.isWithContent=function(){return this.getTitle()||this._getContent()},t.addAttachmentClass=function(n){d(this.getTipElement()).addClass(du+"-"+n)},t.getTipElement=function(){return this.tip=this.tip||d(this.config.template)[0],this.tip},t.setContent=function(){var t=d(this.getTipElement()),n;this.setElementContent(t.find(ws),this.getTitle());n=this._getContent();"function"==typeof n&&(n=n.call(this.element));this.setElementContent(t.find(bs),n);t.removeClass(ys+" "+ps)},t._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},t._cleanTipClass=function(){var t=d(this.getTipElement()),n=t.attr("class").match(ls);null!==n&&0<n.length&&t.removeClass(n.join(""))},i._jQueryInterface=function(n){return this.each(function(){var t=d(this).data(tu),r="object"==typeof n?n:null;if((t||!/destroy|hide/.test(n))&&(t||(t=new i(this,r),d(this).data(tu,t)),"string"==typeof n)){if("undefined"==typeof t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},g(i,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return as}},{key:"NAME",get:function(){return bt}},{key:"DATA_KEY",get:function(){return tu}},{key:"Event",get:function(){return ks}},{key:"EVENT_KEY",get:function(){return k}},{key:"DefaultType",get:function(){return vs}}]),i}(eu),d.fn[bt]=ur._jQueryInterface,d.fn[bt].Constructor=ur,d.fn[bt].noConflict=function(){return d.fn[bt]=cs,ur._jQueryInterface},ur),rc=(st="scrollspy",fr="."+(iu="bs.scrollspy"),ds=(a=t).fn[st],gu={offset:10,method:"auto",target:""},gs={offset:"number",method:"string",target:"(string|element)"},ru={ACTIVATE:"activate"+fr,SCROLL:"scroll"+fr,LOAD_DATA_API:"load"+fr+".data-api"},nh="dropdown-item",kt="active",th='[data-spy="scroll"]',ih=".active",nf=".nav, .list-group",uu=".nav-link",rh=".nav-item",tf=".list-group-item",uh=".dropdown",fh=".dropdown-item",eh=".dropdown-toggle",oh="offset",rf="position",ci=function(){function t(n,t){var i=this;this._element=n;this._scrollElement="BODY"===n.tagName?window:n;this._config=this._getConfig(t);this._selector=this._config.target+" "+uu+","+this._config.target+" "+tf+","+this._config.target+" "+fh;this._offsets=[];this._targets=[];this._activeTarget=null;this._scrollHeight=0;a(this._scrollElement).on(ru.SCROLL,function(n){return i._process(n)});this.refresh();this._process()}var n=t.prototype;return n.refresh=function(){var n=this,i=this._scrollElement===this._scrollElement.window?oh:rf,t="auto"===this._config.method?i:this._config.method,r=t===rf?this._getScrollTop():0;this._offsets=[];this._targets=[];this._scrollHeight=this._getScrollHeight();[].slice.call(document.querySelectorAll(this._selector)).map(function(n){var i,u=f.getSelectorFromElement(n),e;return(u&&(i=document.querySelector(u)),i)&&(e=i.getBoundingClientRect(),e.width||e.height)?[a(i)[t]().top+r,u]:null}).filter(function(n){return n}).sort(function(n,t){return n[0]-t[0]}).forEach(function(t){n._offsets.push(t[0]);n._targets.push(t[1])})},n.dispose=function(){a.removeData(this._element,iu);a(this._scrollElement).off(fr);this._element=null;this._scrollElement=null;this._config=null;this._selector=null;this._offsets=null;this._targets=null;this._activeTarget=null;this._scrollHeight=null},n._getConfig=function(n){if("string"!=typeof(n=v({},gu,"object"==typeof n&&n?n:{})).target){var t=a(n.target).attr("id");t||(t=f.getUID(st),a(n.target).attr("id",t));n.target="#"+t}return f.typeCheckConfig(st,n,gs),n},n._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},n._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},n._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},n._process=function(){var t=this._getScrollTop()+this._config.offset,r=this._getScrollHeight(),u=this._config.offset+r-this._getOffsetHeight(),i,n;if(this._scrollHeight!==r&&this.refresh(),u<=t)i=this._targets[this._targets.length-1],this._activeTarget!==i&&this._activate(i);else{if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(n=this._offsets.length;n--;)this._activeTarget!==this._targets[n]&&t>=this._offsets[n]&&("undefined"==typeof this._offsets[n+1]||t<this._offsets[n+1])&&this._activate(this._targets[n])}},n._activate=function(n){var i,t;this._activeTarget=n;this._clear();i=this._selector.split(",");i=i.map(function(t){return t+'[data-target="'+n+'"],'+t+'[href="'+n+'"]'});t=a([].slice.call(document.querySelectorAll(i.join(","))));t.hasClass(nh)?(t.closest(uh).find(eh).addClass(kt),t.addClass(kt)):(t.addClass(kt),t.parents(nf).prev(uu+", "+tf).addClass(kt),t.parents(nf).prev(rh).children(uu).addClass(kt));a(this._scrollElement).trigger(ru.ACTIVATE,{relatedTarget:n})},n._clear=function(){var n=[].slice.call(document.querySelectorAll(this._selector));a(n).filter(ih).removeClass(kt)},t._jQueryInterface=function(n){return this.each(function(){var i=a(this).data(iu);if(i||(i=new t(this,"object"==typeof n&&n),a(this).data(iu,i)),"string"==typeof n){if("undefined"==typeof i[n])throw new TypeError('No method named "'+n+'"');i[n]()}})},g(t,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return gu}}]),t}(),a(window).on(ru.LOAD_DATA_API,function(){for(var i,n=[].slice.call(document.querySelectorAll(th)),t=n.length;t--;)i=a(n[t]),ci._jQueryInterface.call(i,i.data())}),a.fn[st]=ci._jQueryInterface,a.fn[st].Constructor=ci,a.fn[st].noConflict=function(){return a.fn[st]=ds,ci._jQueryInterface},ci),uc=(li="."+(fu="bs.tab"),sh=(h=t).fn.tab,ai={HIDE:"hide"+li,HIDDEN:"hidden"+li,SHOW:"show"+li,SHOWN:"shown"+li,CLICK_DATA_API:"click"+li+".data-api"},hh="dropdown-menu",vi="active",ch="disabled",lh="fade",uf="show",ah=".dropdown",vh=".nav, .list-group",ff=".active",ef="> li > .active",yh='[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',ph=".dropdown-toggle",wh="> .dropdown-menu .active",yi=function(){function n(n){this._element=n}var t=n.prototype;return t.show=function(){var s=this,i,n,t,r,c,u,e,o;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&h(this._element).hasClass(vi)||h(this._element).hasClass(ch)||(t=h(this._element).closest(vh)[0],r=f.getSelectorFromElement(this._element),t&&(c="UL"===t.nodeName?ef:ff,n=(n=h.makeArray(h(t).find(c)))[n.length-1]),u=h.Event(ai.HIDE,{relatedTarget:this._element}),e=h.Event(ai.SHOW,{relatedTarget:n}),(n&&h(n).trigger(u),h(this._element).trigger(e),e.isDefaultPrevented()||u.isDefaultPrevented())||(r&&(i=document.querySelector(r)),this._activate(this._element,t),o=function(){var t=h.Event(ai.HIDDEN,{relatedTarget:s._element}),i=h.Event(ai.SHOWN,{relatedTarget:n});h(n).trigger(t);h(s._element).trigger(i)},i?this._activate(i,i.parentNode,o):o()))},t.dispose=function(){h.removeData(this._element,fu);this._element=null},t._activate=function(n,t,i){var o=this,r=("UL"===t.nodeName?h(t).find(ef):h(t).children(ff))[0],s=i&&r&&h(r).hasClass(lh),u=function(){return o._transitionComplete(n,r,i)},e;r&&s?(e=f.getTransitionDurationFromElement(r),h(r).one(f.TRANSITION_END,u).emulateTransitionEnd(e)):u()},t._transitionComplete=function(n,t,i){var r,u,e;t&&(h(t).removeClass(uf+" "+vi),r=h(t.parentNode).find(wh)[0],r&&h(r).removeClass(vi),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1));(h(n).addClass(vi),"tab"===n.getAttribute("role")&&n.setAttribute("aria-selected",!0),f.reflow(n),h(n).addClass(uf),n.parentNode&&h(n.parentNode).hasClass(hh))&&(u=h(n).closest(ah)[0],u&&(e=[].slice.call(u.querySelectorAll(ph)),h(e).addClass(vi)),n.setAttribute("aria-expanded",!0));i&&i()},n._jQueryInterface=function(t){return this.each(function(){var r=h(this),i=r.data(fu);if(i||(i=new n(this),r.data(fu,i)),"string"==typeof t){if("undefined"==typeof i[t])throw new TypeError('No method named "'+t+'"');i[t]()}})},g(n,null,[{key:"VERSION",get:function(){return"4.1.3"}}]),n}(),h(document).on(ai.CLICK_DATA_API,yh,function(n){n.preventDefault();yi._jQueryInterface.call(h(this),"show")}),h.fn.tab=yi._jQueryInterface,h.fn.tab.Constructor=yi,h.fn.tab.noConflict=function(){return h.fn.tab=sh,yi._jQueryInterface},yi);!function(n){if("undefined"==typeof n)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var t=n.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1===t[0]&&9===t[1]&&t[2]<1||4<=t[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0");}(t);n.Util=f;n.Alert=bh;n.Button=kh;n.Carousel=dh;n.Collapse=gh;n.Dropdown=nc;n.Modal=tc;n.Popover=ic;n.Scrollspy=rc;n.Tab=uc;n.Tooltip=eu;Object.defineProperty(n,"__esModule",{value:!0})})