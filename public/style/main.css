
body{
    background-color: rgb(250,251,255);
  font-family: 'Roboto', sans-serif;
 
}
.pageWrap{
  text-align:center;
  border-radius: 100%;
  border: 5px solid white;
  position: absolute;
    top: -50px;
    left: 40%;
}

.checkmark-circle {
  width: 100px;
  height: 100px;
  position: relative;
  display: inline-block;
  vertical-align: top;
}
.checkmark-circle .background {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #2EB150;
  position: absolute;
}
.checkmark-circle .checkmark {
  border-radius: 5px;
}
.checkmark-circle .checkmark.draw:after {
  animation-delay: 100ms;
  animation-duration: 1.6s;
  animation-timing-function: ease;
  animation-name: checkmark;
  transform: scaleX(-1) rotate(135deg);
  animation-fill-mode: forwards;
}
.checkmark-circle .checkmark:after {
  opacity: 1;
  height: 50px ;
  width: 20px ;
  transform-origin: left top;
  border-right: 8px solid white;
  border-top: 8px solid white;
  border-radius: 2.5px !important;
  content: '';
  left: 22px;
  top: 50px;
  position: absolute;
}

@-webkit-keyframes checkmark {
  0% {
    height: 0;
    width: 0;
    opacity: 1;
  }
  20% {
    height: 0;
    width: 20px;
    opacity: 1;
  }
  40% {
    height: 50px;
    width: 20px;
    opacity: 1;
  }
  100% {
    height: 50px;
    width: 20px;
    opacity: 1;
  }
}
@-moz-keyframes checkmark {
  0% {
    height: 0;
    width: 0;
    opacity: 1;
  }
  20% {
    height: 0;
    width: 20px;
    opacity: 1;
  }
  40% {
    height: 50px;
    width: 20px;
    opacity: 1;
  }
  100% {
    height: 50px;
    width: 20px;
    opacity: 1;
  }
}
@keyframes checkmark {
  0% {
    height: 0;
    width: 0;
    opacity: 1;
  }
  20% {
    height: 0;
    width: 20px;
    opacity: 1;
  }
  40% {
    height: 50px;
    width: 20px;
    opacity: 1;
  }
  100% {
    height: 50px;
    width: 20px;
    opacity: 1;
  }
}


.bg {
  
    background-color: #95D4ED;
    width: 680px;
    overflow: hidden;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 40px;
    font-family: 'Roboto';
    margin-top: 150px;
    border-radius:10px;
    font-family: 'Roboto';
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)
    
  }

  @media (max-width: 500px)
  {
      .bg {
          width: auto;
          height: auto;
          background-color: 0;
          box-shadow: none;
      }

      body {
          background-color: #95D4ED;
      }

      .pageWrap{
          top: -50px;
          left: 36%;
      }

      
  }

  @media (max-width: 375px)
  {
    .pageWrap{
        top: -50px;
        left: 30%;
    }
  }

  .card {
  
    background-color: white;
    width: 100%;
    margin-top: 40px;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 80px 30px 45px 30px;
    text-align: center;
    position: relative;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)
  }

  .card .msg {
    
    text-transform: uppercase;
    color: #55585b;
    font-size: 18px;
    margin: 50px 0 20px 0;
    
    
  }

  .card .submsg {
    color: #3679d2;
    font-size: 19px;
    font-weight: 400;
    margin-top: 0px;
    
  }

  .card tags {
    
    clear: both;
    padding-top: 15px;
    
  }

  .card .tags .tag {
    
    text-transform: uppercase;
    background-color: #f8f6f6;
    box-sizing: border-box;
    padding: 3px 5px;
    border-radius: 3px;
    font-size: 18px;
    color: #625151;
  }
    
    


  