var VueMobileVehicle = {};
var once = false;
jQuery(window).load(function () {
    if (!once) {
        once = true;
        console.log("int VueMobileVehicle");
        VueMobileVehicle.vehicleManufacturersList = new Vue({
            el: "#vehicle-mobile-manufacturers",
            data: {
                manufacturers: [],
                popularManufacturers: [],
                normalManufacturers: [],
                isReady: false,
                selectedManufacturer: 0,
            },
            methods: {
                reloadList: function () {
                    beforeAjaxLoader();
                    this.isReady = false;
                    this.$http
                        .get(getRoute("model-selection-manufacturers"))
                        .then(function (response) {
                            this.manufacturers = response.body;
                            this.popularManufacturers =
                                this.manufacturers.filter(function (
                                    manufacturer
                                ) {
                                    return manufacturer.ocurrences > 0;
                                });
                            this.normalManufacturers =
                                this.manufacturers.filter(function (
                                    manufacturer
                                ) {
                                    return !manufacturer.ocurrences;
                                });
                            this.isReady = true;
                            afterAjaxLoader();
                        });
                },
                selectManufacturer: function () {
                    VueMobileVehicle.vehicleModelsList.reloadList(
                        this.selectedManufacturer
                    );
                    if (searchByEngine) {
                        VueMobileVehicle.vehicleEngineList.reloadList(
                            this.selectedManufacturer
                        );
                    }
                },
            },
        });

        VueMobileVehicle.vehicleModelsList = new Vue({
            el: "#vehicle-mobile-models",
            data: {
                models: [],
                popularModels: [],
                normalModels: [],
                isReady: false,
                selectedModel: 0,
            },
            methods: {
                reloadList: function (id) {
                    beforeAjaxLoader();
                    this.isReady = false;
                    this.$http
                        .get(getRoute("model-selection-models", id))
                        .then(function (response) {
                            this.models = response.body;
                            this.popularModels = this.models.filter(function (
                                model
                            ) {
                                return model.ocurrences > 0;
                            });
                            this.normalModels = this.models.filter(function (
                                model
                            ) {
                                return !model.ocurrences;
                            });
                            this.isReady = true;
                            afterAjaxLoader();
                        });
                },
                selectModel: function () {
                    beforeAjaxLoader();
                    saveInJobFolder(0, {
                        manufacturer_id:
                            VueMobileVehicle.vehicleManufacturersList
                                .selectedManufacturer,
                        model_id: this.selectedModel,
                    });
                },
            },
        });

        VueMobileVehicle.vehicleEngineList = new Vue({
            el: "#vehicle-mobile-engines",
            data: {
                engines: [],
                isReady: false,
                selectedEngine: 0,
                engine: false,
            },
            methods: {
                reloadList: function (id) {
                    beforeAjaxLoader();
                    this.isReady = false;
                    this.manufacturer = id;
                    this.$http
                        .get("/vehicles/select/engines/" + id)
                        .then(function (response) {
                            this.engines = response.body;
                            this.isReady = true;
                            afterAjaxLoader();
                        });
                },
                selectEngine: function () {
                    VueMobileVehicle.vehicleEngineModelList.reloadList(
                        this.selectedEngine,
                        this.manufacturer
                    );
                },
            },
        });

        VueMobileVehicle.vehicleEngineModelList = new Vue({
            el: "#vehicle-mobile-engine-models",
            data: {
                models: [],
                isReady: false,
                selectedModel: 0,
            },
            methods: {
                reloadList: function (engine, manufacturer) {
                    beforeAjaxLoader();
                    this.isReady = false;
                    this.$http
                        .get(
                            "/vehicles/select/models-for-engine/" +
                                manufacturer +
                                "/" +
                                encodeURIComponent(encodeURIComponent(engine))
                        )
                        .then(function (response) {
                            this.models = response.body;
                            this.isReady = true;
                            afterAjaxLoader();
                        });
                },
                selectModel: function () {
                    beforeAjaxLoader();
                    saveInJobFolder(0, { mid: this.selectedModel });
                },
            },
        });
    }
});
