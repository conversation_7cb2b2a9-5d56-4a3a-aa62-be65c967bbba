function getRoute(name) {
    var url = "/" + Drupal.settings.routes[name];
    var argumentCount = arguments.length;
    if (argumentCount > 1) {
        for (var i = 1; i < argumentCount; i++) {
            url = url.replace("%", arguments[i]);
        }
    }
    return url;
}

function getCurrentMid() {
    try {
        var midRegEx = new RegExp("[A-Z]{2}[A-Z-][0-9]{5}");
        var mids = midRegEx.exec(window.location.pathname);
        return mids.length > 0 ? mids[0].replace("/", "") : "";
    } catch (err) {
        return document.querySelector("#vehicle_id").innerText;
    }
    return "";
}

var _GET = (function () {
    var parameters = {};
    var parametersRegEx = /[?&]([^=&]+)(=?)([^&]*)/g;
    while ((match = parametersRegEx.exec(location.search))) {
        parameters[decodeURIComponent(match[1])] =
            match[2] == "=" ? decodeURIComponent(match[3]) : true;
    }
    return parameters;
})();
