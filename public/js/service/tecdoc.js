setInterval(myMethod, 200);


var hasRedirect = false;
function myMethod() {
    // console.log("rep");
    //redirect if login screen

    if (
        hasRedirect == false &&
        window.location.href.includes("/login") &&
        !window.location.href.includes("homecheckdrcar")
    ) {
        hasRedirect = true;
        var newLink = window.location.href;
        newLink = newLink.replace("login", "login?screen=homecheckdrcar");
        window.location.replace(newLink);
    }

    onRemove();
}

var debuggerbutton = document.getElementById("seventag_container_debugger");
if (debuggerbutton) {
    debuggerbutton.parentElement.removeChild(debuggerbutton);
}
var account_button = document.getElementById("account-button");
if (account_button) {
    account_button.parentElement.removeChild(account_button);
}
var logoutbutton = document.getElementById("logout");
if (logoutbutton) {
    logoutbutton.parentElement.removeChild(logoutbutton);
}

var toastbtn = document.getElementsByTagName("p-toast");
if (toastbtn.length > 0) {
    toastbtn[0].parentElement.removeChild(toastbtn[0]);
}

function onRemove() {
    var debuggerbutton = document.getElementById("seventag_container_debugger");
    if (debuggerbutton) {
        debuggerbutton.parentElement.removeChild(debuggerbutton);
    }
    var account_button = document.getElementById("account-button");
    if (account_button) {
        account_button.parentElement.removeChild(account_button);
    }
    var logoutbutton = document.getElementById("logout");
    if (logoutbutton) {
        logoutbutton.parentElement.removeChild(logoutbutton);
    }

    var toastbtn = document.getElementsByTagName("p-toast");
    if (toastbtn.length > 0) {
        toastbtn[0].parentElement.removeChild(toastbtn[0]);
    }

    var dot3 = document.getElementsByClassName("hidden-from-1024");
    if (dot3.length > 0) {
        dot3[0].parentElement.removeChild(dot3[0]);
    }

    document.getElementsByTagName("html")[0].style.display = "block";
}

function nani() {
    window.onload = function () {
        document.getElementsByTagName("html")[0].style.display = "none";
        document.title = "TecDoc";
        onRemove();
    };
}

function getElementByXpath(path) {
    return document.evaluate(
        path,
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
    ).singleNodeValue;
}
