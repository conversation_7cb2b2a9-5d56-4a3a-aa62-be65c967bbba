window.localStorage.setItem("i18nextLng", "en");

setInterval(myMethod, 200);

function myMethod() {
    onRemove();
}
function onRemove() {
    window.localStorage.setItem("i18nextLng", "en");

    var menus = document.querySelectorAll(".MuiBottomNavigationAction-root");
    if (menus && menus.length >= 4) {
        menus[4].remove();
    }

    var option1 = document.querySelector("._container_13mdf_1");
    if (option1) {
        option1.remove();
    }
    var option2 = document.querySelector("._menuContainer_9oldn_28");
    if (option2) {
        option2.remove();
    }


    
    var logout1 = document.querySelector("._container_c1pq0_9");
    if (logout1) {
        logout1.remove();
    }

    if (window.location.href.includes("pl24-app/menu")) {
        var main1 = document.querySelector(".MuiList-root");
        if (main1) {
            main1.remove();
        }
    }

    

    if (window.location.href.includes("pl24-app/login")) {
        var main2 = document.querySelector(".MuiList-root");
        if (main2) {
            main2.remove();
        }
    }
}
