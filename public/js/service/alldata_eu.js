(function () {
    function runChecker() {
        const checkInterval = setInterval(function () {
            const navResults = document.querySelector(
                ".navigation-results-module"
            );
            const hasContent = navResults && navResults.children.length > 0;

            if (hasContent) {
                try {
                    if (
                        typeof G3 !== "undefined" &&
                        G3.Utils &&
                        typeof G3.Utils.hideSpin === "function"
                    ) {
                        G3.Utils.hideSpin();
                    }

                    if (typeof $ !== "undefined") {
                        $(".navigation-results-module")
                            .stop(true, true)
                            .fadeIn(200);
                        $(".container-results-module")
                            .stop(true, true)
                            .fadeIn(200);
                    }

                    //   clearInterval(checkInterval);
                } catch (err) {
                    console.error("L alldata_eu.js:", err);
                }
            }

            const breadcrumbLinks = document.querySelectorAll(
                ".breadcrum-content a"
            );

            breadcrumbLinks.forEach(function (link) {
                link.addEventListener("click", function (e) {
                    e.preventDefault(); 
                    e.stopPropagation(); 
                    link.style.pointerEvents = "none"; 
                });
            });
        }, 1000);
    }

    // Đảm bảo chỉ chạy sau khi DOM đã load
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", runChecker);
    } else {
        runChecker();
    }
})();
