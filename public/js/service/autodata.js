setInterval(myMethod, 500);

function myMethod() {
    onRemove();
    replaceInfoTech();
}

function replaceInfoTech() {
    var manufacturer_id_meta = document.querySelector(
        'meta[name="manufacturer_id"]'
    );
    var model_id_meta = document.querySelector('meta[name="model_id"]');

    var techIn = document.querySelector(".technical-information a");
    if (
        techIn != null &&
        manufacturer_id_meta != null &&
        model_id_meta != null
    ) {
        var linkTo = techIn.getAttribute("href");
        if (linkTo.includes('/w1/vehicles')) {
            var manufacturer_id = manufacturer_id_meta.content;
            var model_id = model_id_meta.content;
            techIn.setAttribute(
                "href",
                "/w1/vehicles/" + manufacturer_id + "/" + model_id
            );
        }
    }
}

function onRemove() {
    var logo = document.getElementById("logo");
    if (logo) {
        logo.setAttribute("class", "ko");
    }

    var header = document.querySelector("#header");
    if (header) {
        header.style.zIndex = 1;
    }
}

function enginesubmit() {
    var mid = jQuery("form#engine-code-selection > #vehicle_id").val();
    jQuery(".left-column > .ajax_loader").show();
    jQuery.ajax({
        url: getRoute("engine-selection-engine-code", mid),
        type: "POST",
        dataType: "json",
        data: jQuery("form#engine-code-selection").serialize(),
        success: function (data) {
            jQuery("#loaderDiv").removeClass("loaderShow");
            if (data["action"] == 1) {
                window.location.href = data["back"];
            } else {
                alert(data["message"]);
                jQuery(".left-column > .ajax_loader").hide();
                return;
            }
        },
    });
    return false;
}
