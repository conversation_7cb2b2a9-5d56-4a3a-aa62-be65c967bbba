setInterval(myMethod, 200);

function myMethod() {
    onRemove();
}
function onRemove() {
    var feedback = document.getElementById("feedback");
    if (feedback) {
        feedback.parentElement.removeChild(feedback);
    }
    var settings = document.getElementById("settings");
    if (settings) {
        settings.parentElement.removeChild(settings);
    }
    var logout = document.getElementById("logout");
    if (logout) {
        logout.parentElement.removeChild(logout);
    }

    var manageRelationships = document.getElementById("manageRelationships");
    if (manageRelationships!= null) {
        manageRelationships.parentElement.removeChild(manageRelationships);
    }

    var activeSessions = document.getElementById("activeSessions");
    if (activeSessions!= null) {
        activeSessions.parentElement.removeChild(activeSessions);
    }

    var oemPermissions = document.querySelector("#ContentRegion > footer > a.oemPermissions");
    if (oemPermissions != null) {
        oemPermissions.style.display = "none";
    }

    // var contentQC = document.querySelector("section#content");
    // if (contentQC!= null) {
    //     contentQC.style.display = "none";
    // }
}
