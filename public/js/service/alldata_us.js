function checkAndRemoveElement() {
    var profile = document.querySelector(".overlaypanel.profile-options", 0);

    if (profile != null) {
        profile.remove();
    }
    var qualtricsFeedback = document.querySelector("#qualtricsFeedback", 0);

    if (qualtricsFeedback != null) {
        qualtricsFeedback.remove();
    }

    var library = document.querySelector("#library", 0);
    if (library != null) {
        library.remove();
    }

    var elementsSkipThisFixedPosition = document.querySelectorAll(
        ".SkipThisFixedPosition"
    );

    elementsSkipThisFixedPosition.forEach(function (element) {
        element.classList.remove("SkipThisFixedPosition");
    });

    var options = document.querySelector(".overlaypanel.help-options", 0);

    if (options != null) {
        options.remove();
    }

    var lineHot = document.querySelector(".walkme-icon-image-div", 0);

    if (lineHot != null) {
        lineHot.remove();
    }
     
    var bookmark = document.querySelector("div.wrapper.bookmarks", 0);
    if (bookmark != null) {
        bookmark.remove();
    }

    var convert = document.querySelector("div.wrapper.convert", 0);
    if (convert != null) {
        convert.remove();
    }

    var findInserd = document.querySelector(
        ".walkme-custom-icon-outer-div.walkme-launcher-id-785532.walkme-custom-launcher-outer-div",
        0
    );
    if (findInserd != null) {
        findInserd.remove();
    }

    var mobileMenu = document.querySelector(".app-icon-menu-bars", 0);
    if (mobileMenu != null) {
        mobileMenu.remove();
    }

    var mobileMenu = document.querySelector(".app-icon-menu-bars", 0);
    if (mobileMenu != null) {
        mobileMenu.remove();
    }

    var menu123s = document.querySelectorAll(".ad-product-tile");
    if (menu123s != null && menu123s[2] != null) {
        menu123s[2].remove();
    }
    if (menu123s != null && menu123s[1] != null) {
        menu123s[1].remove();
    }

    let element = document.getElementById(
        "ad-uib-product-home-page-header-left-nav-item"
    );
    if (element != null) {
        let newElement = document.createElement("a");
        newElement.href = "/";
        newElement.textContent = "Home";
        element.replaceWith(newElement);
    }

    var menuOptions = document.querySelector(".app-icon-app-switcher", 0);
    if (menuOptions != null) {
        menuOptions.remove();
    }

    let logoElement = document.querySelector(".logo.ng-star-inserted");
    if (logoElement) {
        let newElement = document.createElement("div");
        newElement.innerHTML =
            '<a href="/"><i class="app-icon-ALLDATA ng-star-inserted"><!----><!----><!----></i></a>';
        logoElement.replaceWith(newElement.firstChild);
    }

    let headerNavItem = document.querySelector(
        ".ad-uib-product-home-page-header-nav-item.zoomed"
    );

    if (headerNavItem) {
        let newElement = document.createElement("div");
        newElement.innerHTML =
            '<i class="app-icon-ALLDATA ng-star-inserted"><!----><!----><!----></i>';
        headerNavItem.replaceWith(newElement.firstChild);
    }

    // var lineHot2 = document.querySelector("#app-header > div > div.right-section.ng-star-inserted > div.walkme-to-remove.walkme-icon-root-Launcher-697415.walkme-not-embed.walkme-launcher-container.walkme-launcher-container-id-697415")
    // if (lineHot2 != null) {
    //     lineHot2.remove();
    // }

    // var child3 = document.querySelector(
    //     "#app-header > div > div.right-section.ng-star-inserted > div:nth-child(3)"
    // );
    // if (child3 != null) {
    //     child3.remove();
    // }

    // var child4 = document.querySelector(
    //     "#app-header > div > div.right-section.ng-star-inserted > div:nth-child(3)"
    // );
    // if (child4 != null) {
    //     child4.remove();
    // }

    var elementToReplaceEmail = document.querySelector(
        ".header-content #top-menu-0"
    );

    if (elementToReplaceEmail) {
        var newParagraph = document.createElement("p");
        newParagraph.textContent = elementToReplaceEmail.textContent;

        newParagraph.style =
            "font-size: 12px;line-height: 24px;margin-top: 8px;display: inline-block;color: #2d73bb;";

        elementToReplaceEmail.parentNode.replaceChild(
            newParagraph,
            elementToReplaceEmail
        );
    }
}

var intervalId = setInterval(checkAndRemoveElement, 300);
