!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(t.document)return e(t);throw new Error("jQuery requires a window with a document")}:e(t)}("undefined"!=typeof window?window:this,(function(t,e){"use strict";function i(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item}function n(t){return null!=t&&t===t.window}var s=[],o=Object.getPrototypeOf,r=s.slice,a=s.flat?function(t){return s.flat.call(t)}:function(t){return s.concat.apply([],t)},l=s.push,h=s.indexOf,c={},u=c.toString,d=c.hasOwnProperty,p=d.toString,f=p.call(Object),g={},m=t.document,v={type:!0,src:!0,nonce:!0,noModule:!0};function _(t,e,i){var n,s,o=(i=i||m).createElement("script");if(o.text=t,e)for(n in v)(s=e[n]||e.getAttribute&&e.getAttribute(n))&&o.setAttribute(n,s);i.head.appendChild(o).parentNode.removeChild(o)}function b(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?c[u.call(t)]||"object":typeof t}var y="3.7.1",w=/HTML$/i,x=function(t,e){return new x.fn.init(t,e)};function C(t){var e=!!t&&"length"in t&&t.length,s=b(t);return!i(t)&&!n(t)&&("array"===s||0===e||"number"==typeof e&&0<e&&e-1 in t)}function S(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}x.fn=x.prototype={jquery:y,constructor:x,length:0,toArray:function(){return r.call(this)},get:function(t){return null==t?r.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){return(t=x.merge(this.constructor(),t)).prevObject=this,t},each:function(t){return x.each(this,t)},map:function(t){return this.pushStack(x.map(this,(function(e,i){return t.call(e,i,e)})))},slice:function(){return this.pushStack(r.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(x.grep(this,(function(t,e){return(e+1)%2})))},odd:function(){return this.pushStack(x.grep(this,(function(t,e){return e%2})))},eq:function(t){var e=this.length;return t=+t+(t<0?e:0),this.pushStack(0<=t&&t<e?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:s.sort,splice:s.splice},x.extend=x.fn.extend=function(){var t,e,n,s,o,r=arguments[0]||{},a=1,l=arguments.length,h=!1;for("boolean"==typeof r&&(h=r,r=arguments[a]||{},a++),"object"==typeof r||i(r)||(r={}),a===l&&(r=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)n=t[e],"__proto__"!==e&&r!==n&&(h&&n&&(x.isPlainObject(n)||(s=Array.isArray(n)))?(o=r[e],o=s&&!Array.isArray(o)?[]:s||x.isPlainObject(o)?o:{},s=!1,r[e]=x.extend(h,o,n)):void 0!==n&&(r[e]=n));return r},x.extend({expando:"jQuery"+(y+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){return!(!t||"[object Object]"!==u.call(t)||(t=o(t))&&("function"!=typeof(t=d.call(t,"constructor")&&t.constructor)||p.call(t)!==f))},isEmptyObject:function(t){for(var e in t)return!1;return!0},globalEval:function(t,e,i){_(t,{nonce:e&&e.nonce},i)},each:function(t,e){var i,n=0;if(C(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},text:function(t){var e,i="",n=0,s=t.nodeType;if(!s)for(;e=t[n++];)i+=x.text(e);return 1===s||11===s?t.textContent:9===s?t.documentElement.textContent:3===s||4===s?t.nodeValue:i},makeArray:function(t,e){return e=e||[],null!=t&&(C(Object(t))?x.merge(e,"string"==typeof t?[t]:t):l.call(e,t)),e},inArray:function(t,e,i){return null==e?-1:h.call(e,t,i)},isXMLDoc:function(t){var e=t&&t.namespaceURI;return t=t&&(t.ownerDocument||t).documentElement,!w.test(e||t&&t.nodeName||"HTML")},merge:function(t,e){for(var i=+e.length,n=0,s=t.length;n<i;n++)t[s++]=e[n];return t.length=s,t},grep:function(t,e,i){for(var n=[],s=0,o=t.length,r=!i;s<o;s++)!e(t[s],s)!=r&&n.push(t[s]);return n},map:function(t,e,i){var n,s,o=0,r=[];if(C(t))for(n=t.length;o<n;o++)null!=(s=e(t[o],o,i))&&r.push(s);else for(o in t)null!=(s=e(t[o],o,i))&&r.push(s);return a(r)},guid:1,support:g}),"function"==typeof Symbol&&(x.fn[Symbol.iterator]=s[Symbol.iterator]),x.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){c["[object "+e+"]"]=e.toLowerCase()}));var k=s.pop,E=s.sort,D=s.splice,T="[\\x20\\t\\r\\n\\f]",M=new RegExp("^"+T+"+|((?:^|[^\\\\])(?:\\\\.)*)"+T+"+$","g"),I=(x.contains=function(t,e){return t===(e=e&&e.parentNode)||!(!e||1!==e.nodeType||!(t.contains?t.contains(e):t.compareDocumentPosition&&16&t.compareDocumentPosition(e)))},/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g);function A(t,e){return e?"\0"===t?"ï¿½":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}x.escapeSelector=function(t){return(t+"").replace(I,A)};var P,O,F,H,V,N,W,R,B,L,z=m,j=l,G=j,q=x.expando,U=0,$=0,Y=_t(),K=_t(),X=_t(),J=_t(),Q=function(t,e){return t===e&&(V=!0),0},Z="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",tt="\\["+T+"*("+(y="(?:\\\\[\\da-fA-F]{1,6}"+T+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+")+")(?:"+T+"*([*^$|!~]?=)"+T+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+y+"))|)"+T+"*\\]",et=":("+y+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+tt+")*)|.*)\\)|)",it=new RegExp(T+"+","g"),nt=new RegExp("^"+T+"*,"+T+"*"),st=new RegExp("^"+T+"*([>+~]|"+T+")"+T+"*"),ot=new RegExp(T+"|>"),rt=new RegExp(et),at=new RegExp("^"+y+"$"),lt={ID:new RegExp("^#("+y+")"),CLASS:new RegExp("^\\.("+y+")"),TAG:new RegExp("^("+y+"|[*])"),ATTR:new RegExp("^"+tt),PSEUDO:new RegExp("^"+et),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+T+"*(even|odd|(([+-]|)(\\d*)n|)"+T+"*(?:([+-]|)"+T+"*(\\d+)|))"+T+"*\\)|)","i"),bool:new RegExp("^(?:"+Z+")$","i"),needsContext:new RegExp("^"+T+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+T+"*((?:-\\d)?\\d*)"+T+"*\\)|)(?=[^-]|$)","i")},ht=/^(?:input|select|textarea|button)$/i,ct=/^h\d$/i,ut=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,dt=/[+~]/,pt=new RegExp("\\\\[\\da-fA-F]{1,6}"+T+"?|\\\\([^\\r\\n\\f])","g"),ft=function(t,e){return t="0x"+t.slice(1)-65536,e||(t<0?String.fromCharCode(65536+t):String.fromCharCode(t>>10|55296,1023&t|56320))},gt=function(){St()},mt=Tt((function(t){return!0===t.disabled&&S(t,"fieldset")}),{dir:"parentNode",next:"legend"});try{G.apply(s=r.call(z.childNodes),z.childNodes),s[z.childNodes.length].nodeType}catch(y){G={apply:function(t,e){j.apply(t,r.call(e))},call:function(t){j.apply(t,r.call(arguments,1))}}}function vt(t,e,i,n){var s,o,r,a,l,h,c=e&&e.ownerDocument,u=e?e.nodeType:9;if(i=i||[],"string"!=typeof t||!t||1!==u&&9!==u&&11!==u)return i;if(!n&&(St(e),e=e||N,R)){if(11!==u&&(a=ut.exec(t)))if(s=a[1]){if(9===u){if(!(h=e.getElementById(s)))return i;if(h.id===s)return G.call(i,h),i}else if(c&&(h=c.getElementById(s))&&vt.contains(e,h)&&h.id===s)return G.call(i,h),i}else{if(a[2])return G.apply(i,e.getElementsByTagName(t)),i;if((s=a[3])&&e.getElementsByClassName)return G.apply(i,e.getElementsByClassName(s)),i}if(!(J[t+" "]||B&&B.test(t))){if(h=t,c=e,1===u&&(ot.test(t)||st.test(t))){for((c=dt.test(t)&&Ct(e.parentNode)||e)==e&&g.scope||((r=e.getAttribute("id"))?r=x.escapeSelector(r):e.setAttribute("id",r=q)),o=(l=Et(t)).length;o--;)l[o]=(r?"#"+r:":scope")+" "+Dt(l[o]);h=l.join(",")}try{return G.apply(i,c.querySelectorAll(h)),i}catch(e){J(t,!0)}finally{r===q&&e.removeAttribute("id")}}}return Ot(t.replace(M,"$1"),e,i,n)}function _t(){var t=[];return function e(i,n){return t.push(i+" ")>O.cacheLength&&delete e[t.shift()],e[i+" "]=n}}function bt(t){return t[q]=!0,t}function yt(t){var e=N.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e)}}function wt(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&mt(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function xt(t){return bt((function(e){return e=+e,bt((function(i,n){for(var s,o=t([],i.length,e),r=o.length;r--;)i[s=o[r]]&&(i[s]=!(n[s]=i[s]))}))}))}function Ct(t){return t&&void 0!==t.getElementsByTagName&&t}function St(t){return(t=t?t.ownerDocument||t:z)!=N&&9===t.nodeType&&t.documentElement&&(W=(N=t).documentElement,R=!x.isXMLDoc(N),L=W.matches||W.webkitMatchesSelector||W.msMatchesSelector,W.msMatchesSelector&&z!=N&&(t=N.defaultView)&&t.top!==t&&t.addEventListener("unload",gt),g.getById=yt((function(t){return W.appendChild(t).id=x.expando,!N.getElementsByName||!N.getElementsByName(x.expando).length})),g.disconnectedMatch=yt((function(t){return L.call(t,"*")})),g.scope=yt((function(){return N.querySelectorAll(":scope")})),g.cssHas=yt((function(){try{N.querySelector(":has(*,:jqfake)")}catch(t){return 1}})),g.getById?(O.filter.ID=function(t){var e=t.replace(pt,ft);return function(t){return t.getAttribute("id")===e}},O.find.ID=function(t,e){if(void 0!==e.getElementById&&R)return(e=e.getElementById(t))?[e]:[]}):(O.filter.ID=function(t){var e=t.replace(pt,ft);return function(t){return(t=void 0!==t.getAttributeNode&&t.getAttributeNode("id"))&&t.value===e}},O.find.ID=function(t,e){if(void 0!==e.getElementById&&R){var i,n,s,o=e.getElementById(t);if(o){if((i=o.getAttributeNode("id"))&&i.value===t)return[o];for(s=e.getElementsByName(t),n=0;o=s[n++];)if((i=o.getAttributeNode("id"))&&i.value===t)return[o]}return[]}}),O.find.TAG=function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},O.find.CLASS=function(t,e){if(void 0!==e.getElementsByClassName&&R)return e.getElementsByClassName(t)},B=[],yt((function(t){var e;W.appendChild(t).innerHTML="<a id='"+q+"' href='' disabled='disabled'></a><select id='"+q+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||B.push("\\["+T+"*(?:value|"+Z+")"),t.querySelectorAll("[id~="+q+"-]").length||B.push("~="),t.querySelectorAll("a#"+q+"+*").length||B.push(".#.+[+~]"),t.querySelectorAll(":checked").length||B.push(":checked"),(e=N.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),W.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&B.push(":enabled",":disabled"),(e=N.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||B.push("\\["+T+"*name"+T+"*="+T+"*(?:''|\"\")")})),g.cssHas||B.push(":has"),B=B.length&&new RegExp(B.join("|")),Q=function(t,e){var i;return t===e?(V=!0,0):(i=!t.compareDocumentPosition-!e.compareDocumentPosition)||(1&(i=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!g.sortDetached&&e.compareDocumentPosition(t)===i?t===N||t.ownerDocument==z&&vt.contains(z,t)?-1:e===N||e.ownerDocument==z&&vt.contains(z,e)?1:H?h.call(H,t)-h.call(H,e):0:4&i?-1:1)}),N}for(P in vt.matches=function(t,e){return vt(t,null,null,e)},vt.matchesSelector=function(t,e){if(St(t),R&&!J[e+" "]&&(!B||!B.test(e)))try{var i=L.call(t,e);if(i||g.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){J(e,!0)}return 0<vt(e,N,null,[t]).length},vt.contains=function(t,e){return(t.ownerDocument||t)!=N&&St(t),x.contains(t,e)},vt.attr=function(t,e){var i;return(t.ownerDocument||t)!=N&&St(t),void 0!==(i=(i=O.attrHandle[e.toLowerCase()])&&d.call(O.attrHandle,e.toLowerCase())?i(t,e,!R):void 0)?i:t.getAttribute(e)},vt.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},x.uniqueSort=function(t){var e,i=[],n=0,s=0;if(V=!g.sortStable,H=!g.sortStable&&r.call(t,0),E.call(t,Q),V){for(;e=t[s++];)e===t[s]&&(n=i.push(s));for(;n--;)D.call(t,i[n],1)}return H=null,t},x.fn.uniqueSort=function(){return this.pushStack(x.uniqueSort(r.apply(this)))},(O=x.expr={cacheLength:50,createPseudo:bt,match:lt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(pt,ft),t[3]=(t[3]||t[4]||t[5]||"").replace(pt,ft),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||vt.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&vt.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return lt.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&rt.test(i)&&(e=(e=Et(i,!0))&&i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(pt,ft).toLowerCase();return"*"===t?function(){return!0}:function(t){return S(t,e)}},CLASS:function(t){var e=Y[t+" "];return e||(e=new RegExp("(^|"+T+")"+t+"("+T+"|$)"))&&Y(t,(function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")}))},ATTR:function(t,e,i){return function(n){return null==(n=vt.attr(n,t))?"!="===e:!e||(n+="","="===e?n===i:"!="===e?n!==i:"^="===e?i&&0===n.indexOf(i):"*="===e?i&&-1<n.indexOf(i):"$="===e?i&&n.slice(-i.length)===i:"~="===e?-1<(" "+n.replace(it," ")+" ").indexOf(i):"|="===e&&(n===i||n.slice(0,i.length+1)===i+"-"))}},CHILD:function(t,e,i,n,s){var o="nth"!==t.slice(0,3),r="last"!==t.slice(-4),a="of-type"===e;return 1===n&&0===s?function(t){return!!t.parentNode}:function(e,i,l){var h,c,u,d,p,f=o!=r?"nextSibling":"previousSibling",g=e.parentNode,m=a&&e.nodeName.toLowerCase(),v=!l&&!a,_=!1;if(g){if(o){for(;f;){for(u=e;u=u[f];)if(a?S(u,m):1===u.nodeType)return!1;p=f="only"===t&&!p&&"nextSibling"}return!0}if(p=[r?g.firstChild:g.lastChild],r&&v){for(_=(d=(h=(c=g[q]||(g[q]={}))[t]||[])[0]===U&&h[1])&&h[2],u=d&&g.childNodes[d];u=++d&&u&&u[f]||(_=d=0,p.pop());)if(1===u.nodeType&&++_&&u===e){c[t]=[U,d,_];break}}else if(!1===(_=v?d=(h=(c=e[q]||(e[q]={}))[t]||[])[0]===U&&h[1]:_))for(;(u=++d&&u&&u[f]||(_=d=0,p.pop()))&&((a?!S(u,m):1!==u.nodeType)||!++_||(v&&((c=u[q]||(u[q]={}))[t]=[U,_]),u!==e)););return(_-=s)===n||_%n==0&&0<=_/n}}},PSEUDO:function(t,e){var i,n=O.pseudos[t]||O.setFilters[t.toLowerCase()]||vt.error("unsupported pseudo: "+t);return n[q]?n(e):1<n.length?(i=[t,t,"",e],O.setFilters.hasOwnProperty(t.toLowerCase())?bt((function(t,i){for(var s,o=n(t,e),r=o.length;r--;)t[s=h.call(t,o[r])]=!(i[s]=o[r])})):function(t){return n(t,0,i)}):n}},pseudos:{not:bt((function(t){var e=[],i=[],n=Pt(t.replace(M,"$1"));return n[q]?bt((function(t,e,i,s){for(var o,r=n(t,null,s,[]),a=t.length;a--;)(o=r[a])&&(t[a]=!(e[a]=o))})):function(t,s,o){return e[0]=t,n(e,null,o,i),e[0]=null,!i.pop()}})),has:bt((function(t){return function(e){return 0<vt(t,e).length}})),contains:bt((function(t){return t=t.replace(pt,ft),function(e){return-1<(e.textContent||x.text(e)).indexOf(t)}})),lang:bt((function(t){return at.test(t||"")||vt.error("unsupported lang: "+t),t=t.replace(pt,ft).toLowerCase(),function(e){var i;do{if(i=R?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(i=i.toLowerCase())===t||0===i.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}})),target:function(e){var i=t.location&&t.location.hash;return i&&i.slice(1)===e.id},root:function(t){return t===W},focus:function(t){return t===function(){try{return N.activeElement}catch(t){}}()&&N.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:wt(!1),disabled:wt(!0),checked:function(t){return S(t,"input")&&!!t.checked||S(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!O.pseudos.empty(t)},header:function(t){return ct.test(t.nodeName)},input:function(t){return ht.test(t.nodeName)},button:function(t){return S(t,"input")&&"button"===t.type||S(t,"button")},text:function(t){return S(t,"input")&&"text"===t.type&&(null==(t=t.getAttribute("type"))||"text"===t.toLowerCase())},first:xt((function(){return[0]})),last:xt((function(t,e){return[e-1]})),eq:xt((function(t,e,i){return[i<0?i+e:i]})),even:xt((function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t})),odd:xt((function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t})),lt:xt((function(t,e,i){for(var n=i<0?i+e:e<i?e:i;0<=--n;)t.push(n);return t})),gt:xt((function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t}))}}).pseudos.nth=O.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})O.pseudos[P]=function(t){return function(e){return S(e,"input")&&e.type===t}}(P);for(P in{submit:!0,reset:!0})O.pseudos[P]=function(t){return function(e){return(S(e,"input")||S(e,"button"))&&e.type===t}}(P);function kt(){}function Et(t,e){var i,n,s,o,r,a,l,h=K[t+" "];if(h)return e?0:h.slice(0);for(r=t,a=[],l=O.preFilter;r;){for(o in i&&!(n=nt.exec(r))||(n&&(r=r.slice(n[0].length)||r),a.push(s=[])),i=!1,(n=st.exec(r))&&(i=n.shift(),s.push({value:i,type:n[0].replace(M," ")}),r=r.slice(i.length)),O.filter)!(n=lt[o].exec(r))||l[o]&&!(n=l[o](n))||(i=n.shift(),s.push({value:i,type:o,matches:n}),r=r.slice(i.length));if(!i)break}return e?r.length:r?vt.error(t):K(t,a).slice(0)}function Dt(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function Tt(t,e,i){var n=e.dir,s=e.next,o=s||n,r=i&&"parentNode"===o,a=$++;return e.first?function(e,i,s){for(;e=e[n];)if(1===e.nodeType||r)return t(e,i,s);return!1}:function(e,i,l){var h,c,u=[U,a];if(l){for(;e=e[n];)if((1===e.nodeType||r)&&t(e,i,l))return!0}else for(;e=e[n];)if(1===e.nodeType||r)if(c=e[q]||(e[q]={}),s&&S(e,s))e=e[n]||e;else{if((h=c[o])&&h[0]===U&&h[1]===a)return u[2]=h[2];if((c[o]=u)[2]=t(e,i,l))return!0}return!1}}function Mt(t){return 1<t.length?function(e,i,n){for(var s=t.length;s--;)if(!t[s](e,i,n))return!1;return!0}:t[0]}function It(t,e,i,n,s){for(var o,r=[],a=0,l=t.length,h=null!=e;a<l;a++)!(o=t[a])||i&&!i(o,n,s)||(r.push(o),h&&e.push(a));return r}function At(t,e,i,n,s,o){return n&&!n[q]&&(n=At(n)),s&&!s[q]&&(s=At(s,o)),bt((function(o,r,a,l){var c,u,d,p,f=[],g=[],m=r.length,v=o||function(t,e,i){for(var n=0,s=e.length;n<s;n++)vt(t,e[n],i);return i}(e||"*",a.nodeType?[a]:a,[]),_=!t||!o&&e?v:It(v,f,t,a,l);if(i?i(_,p=s||(o?t:m||n)?[]:r,a,l):p=_,n)for(c=It(p,g),n(c,[],a,l),u=c.length;u--;)(d=c[u])&&(p[g[u]]=!(_[g[u]]=d));if(o){if(s||t){if(s){for(c=[],u=p.length;u--;)(d=p[u])&&c.push(_[u]=d);s(null,p=[],c,l)}for(u=p.length;u--;)(d=p[u])&&-1<(c=s?h.call(o,d):f[u])&&(o[c]=!(r[c]=d))}}else p=It(p===r?p.splice(m,p.length):p),s?s(null,r,p,l):G.apply(r,p)}))}function Pt(t,e){var i,n=[],s=[],o=X[t+" "];if(!o){for(i=(e=e||Et(t)).length;i--;)((o=function t(e){for(var i,n,s,o=e.length,r=O.relative[e[0].type],a=r||O.relative[" "],l=r?1:0,c=Tt((function(t){return t===i}),a,!0),u=Tt((function(t){return-1<h.call(i,t)}),a,!0),d=[function(t,e,n){return t=!r&&(n||e!=F)||((i=e).nodeType?c:u)(t,e,n),i=null,t}];l<o;l++)if(n=O.relative[e[l].type])d=[Tt(Mt(d),n)];else{if((n=O.filter[e[l].type].apply(null,e[l].matches))[q]){for(s=++l;s<o&&!O.relative[e[s].type];s++);return At(1<l&&Mt(d),1<l&&Dt(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(M,"$1"),n,l<s&&t(e.slice(l,s)),s<o&&t(e=e.slice(s)),s<o&&Dt(e))}d.push(n)}return Mt(d)}(e[i]))[q]?n:s).push(o);(o=X(t,function(t,e){function i(i,o,r,a,l){var h,c,u,d=0,p="0",f=i&&[],g=[],m=F,v=i||s&&O.find.TAG("*",l),_=U+=null==m?1:Math.random()||.1,b=v.length;for(l&&(F=o==N||o||l);p!==b&&null!=(h=v[p]);p++){if(s&&h){for(c=0,o||h.ownerDocument==N||(St(h),r=!R);u=t[c++];)if(u(h,o||N,r)){G.call(a,h);break}l&&(U=_)}n&&((h=!u&&h)&&d--,i)&&f.push(h)}if(d+=p,n&&p!==d){for(c=0;u=e[c++];)u(f,g,o,r);if(i){if(0<d)for(;p--;)f[p]||g[p]||(g[p]=k.call(a));g=It(g)}G.apply(a,g),l&&!i&&0<g.length&&1<d+e.length&&x.uniqueSort(a)}return l&&(U=_,F=m),f}var n=0<e.length,s=0<t.length;return n?bt(i):i}(s,n))).selector=t}return o}function Ot(t,e,i,n){var s,o,r,a,l,h="function"==typeof t&&t,c=!n&&Et(t=h.selector||t);if(i=i||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(r=o[0]).type&&9===e.nodeType&&R&&O.relative[o[1].type]){if(!(e=(O.find.ID(r.matches[0].replace(pt,ft),e)||[])[0]))return i;h&&(e=e.parentNode),t=t.slice(o.shift().value.length)}for(s=lt.needsContext.test(t)?0:o.length;s--&&(r=o[s],!O.relative[a=r.type]);)if((l=O.find[a])&&(n=l(r.matches[0].replace(pt,ft),dt.test(o[0].type)&&Ct(e.parentNode)||e))){if(o.splice(s,1),t=n.length&&Dt(o))break;return G.apply(i,n),i}}return(h||Pt(t,c))(n,e,!R,i,!e||dt.test(t)&&Ct(e.parentNode)||e),i}function Ft(t,e,i){for(var n=[],s=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(s&&x(t).is(i))break;n.push(t)}return n}function Ht(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i}kt.prototype=O.filters=O.pseudos,O.setFilters=new kt,g.sortStable=q.split("").sort(Q).join("")===q,St(),g.sortDetached=yt((function(t){return 1&t.compareDocumentPosition(N.createElement("fieldset"))})),x.find=vt,x.expr[":"]=x.expr.pseudos,x.unique=x.uniqueSort,vt.compile=Pt,vt.select=Ot,vt.setDocument=St,vt.tokenize=Et,vt.escape=x.escapeSelector,vt.getText=x.text,vt.isXML=x.isXMLDoc,vt.selectors=x.expr,vt.support=x.support,vt.uniqueSort=x.uniqueSort;var Vt=x.expr.match.needsContext,Nt=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function Wt(t,e,n){return i(e)?x.grep(t,(function(t,i){return!!e.call(t,i,t)!==n})):e.nodeType?x.grep(t,(function(t){return t===e!==n})):"string"!=typeof e?x.grep(t,(function(t){return-1<h.call(e,t)!==n})):x.filter(e,t,n)}x.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?x.find.matchesSelector(n,t)?[n]:[]:x.find.matches(t,x.grep(e,(function(t){return 1===t.nodeType})))},x.fn.extend({find:function(t){var e,i,n=this.length,s=this;if("string"!=typeof t)return this.pushStack(x(t).filter((function(){for(e=0;e<n;e++)if(x.contains(s[e],this))return!0})));for(i=this.pushStack([]),e=0;e<n;e++)x.find(t,s[e],i);return 1<n?x.uniqueSort(i):i},filter:function(t){return this.pushStack(Wt(this,t||[],!1))},not:function(t){return this.pushStack(Wt(this,t||[],!0))},is:function(t){return!!Wt(this,"string"==typeof t&&Vt.test(t)?x(t):t||[],!1).length}});var Rt,Bt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,Lt=((x.fn.init=function(t,e,n){if(t){if(n=n||Rt,"string"!=typeof t)return t.nodeType?(this[0]=t,this.length=1,this):i(t)?void 0!==n.ready?n.ready(t):t(x):x.makeArray(t,this);if(!(s="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:Bt.exec(t))||!s[1]&&e)return(!e||e.jquery?e||n:this.constructor(e)).find(t);if(s[1]){if(e=e instanceof x?e[0]:e,x.merge(this,x.parseHTML(s[1],e&&e.nodeType?e.ownerDocument||e:m,!0)),Nt.test(s[1])&&x.isPlainObject(e))for(var s in e)i(this[s])?this[s](e[s]):this.attr(s,e[s])}else(n=m.getElementById(s[2]))&&(this[0]=n,this.length=1)}return this}).prototype=x.fn,Rt=x(m),/^(?:parents|prev(?:Until|All))/),zt={children:!0,contents:!0,next:!0,prev:!0};function jt(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}x.fn.extend({has:function(t){var e=x(t,this),i=e.length;return this.filter((function(){for(var t=0;t<i;t++)if(x.contains(this,e[t]))return!0}))},closest:function(t,e){var i,n=0,s=this.length,o=[],r="string"!=typeof t&&x(t);if(!Vt.test(t))for(;n<s;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(r?-1<r.index(i):1===i.nodeType&&x.find.matchesSelector(i,t))){o.push(i);break}return this.pushStack(1<o.length?x.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?h.call(x(t),this[0]):h.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(x.uniqueSort(x.merge(this.get(),x(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),x.each({parent:function(t){return(t=t.parentNode)&&11!==t.nodeType?t:null},parents:function(t){return Ft(t,"parentNode")},parentsUntil:function(t,e,i){return Ft(t,"parentNode",i)},next:function(t){return jt(t,"nextSibling")},prev:function(t){return jt(t,"previousSibling")},nextAll:function(t){return Ft(t,"nextSibling")},prevAll:function(t){return Ft(t,"previousSibling")},nextUntil:function(t,e,i){return Ft(t,"nextSibling",i)},prevUntil:function(t,e,i){return Ft(t,"previousSibling",i)},siblings:function(t){return Ht((t.parentNode||{}).firstChild,t)},children:function(t){return Ht(t.firstChild)},contents:function(t){return null!=t.contentDocument&&o(t.contentDocument)?t.contentDocument:(S(t,"template")&&(t=t.content||t),x.merge([],t.childNodes))}},(function(t,e){x.fn[t]=function(i,n){var s=x.map(this,e,i);return(n="Until"!==t.slice(-5)?i:n)&&"string"==typeof n&&(s=x.filter(n,s)),1<this.length&&(zt[t]||x.uniqueSort(s),Lt.test(t))&&s.reverse(),this.pushStack(s)}}));var Gt=/[^\x20\t\r\n\f]+/g;function qt(t){return t}function Ut(t){throw t}function $t(t,e,n,s){var o;try{t&&i(o=t.promise)?o.call(t).done(e).fail(n):t&&i(o=t.then)?o.call(t,e,n):e.apply(void 0,[t].slice(s))}catch(t){n.apply(void 0,[t])}}x.Callbacks=function(t){var e,n;function s(){for(l=l||t.once,a=o=!0;c.length;u=-1)for(r=c.shift();++u<h.length;)!1===h[u].apply(r[0],r[1])&&t.stopOnFalse&&(u=h.length,r=!1);t.memory||(r=!1),o=!1,l&&(h=r?[]:"")}t="string"==typeof t?(e=t,n={},x.each(e.match(Gt)||[],(function(t,e){n[e]=!0})),n):x.extend({},t);var o,r,a,l,h=[],c=[],u=-1,d={add:function(){return h&&(r&&!o&&(u=h.length-1,c.push(r)),function e(n){x.each(n,(function(n,s){i(s)?t.unique&&d.has(s)||h.push(s):s&&s.length&&"string"!==b(s)&&e(s)}))}(arguments),r)&&!o&&s(),this},remove:function(){return x.each(arguments,(function(t,e){for(var i;-1<(i=x.inArray(e,h,i));)h.splice(i,1),i<=u&&u--})),this},has:function(t){return t?-1<x.inArray(t,h):0<h.length},empty:function(){return h=h&&[],this},disable:function(){return l=c=[],h=r="",this},disabled:function(){return!h},lock:function(){return l=c=[],r||o||(h=r=""),this},locked:function(){return!!l},fireWith:function(t,e){return l||(e=[t,(e=e||[]).slice?e.slice():e],c.push(e),o)||s(),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!a}};return d},x.extend({Deferred:function(e){var n=[["notify","progress",x.Callbacks("memory"),x.Callbacks("memory"),2],["resolve","done",x.Callbacks("once memory"),x.Callbacks("once memory"),0,"resolved"],["reject","fail",x.Callbacks("once memory"),x.Callbacks("once memory"),1,"rejected"]],s="pending",o={state:function(){return s},always:function(){return r.done(arguments).fail(arguments),this},catch:function(t){return o.then(null,t)},pipe:function(){var t=arguments;return x.Deferred((function(e){x.each(n,(function(n,s){var o=i(t[s[4]])&&t[s[4]];r[s[1]]((function(){var t=o&&o.apply(this,arguments);t&&i(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[s[0]+"With"](this,o?[t]:arguments)}))})),t=null})).promise()},then:function(e,s,o){var r=0;function a(e,n,s,o){return function(){function l(){var t,l;if(!(e<r)){if((t=s.apply(h,c))===n.promise())throw new TypeError("Thenable self-resolution");i(l=t&&("object"==typeof t||"function"==typeof t)&&t.then)?o?l.call(t,a(r,n,qt,o),a(r,n,Ut,o)):(r++,l.call(t,a(r,n,qt,o),a(r,n,Ut,o),a(r,n,qt,n.notifyWith))):(s!==qt&&(h=void 0,c=[t]),(o||n.resolveWith)(h,c))}}var h=this,c=arguments,u=o?l:function(){try{l()}catch(t){x.Deferred.exceptionHook&&x.Deferred.exceptionHook(t,u.error),r<=e+1&&(s!==Ut&&(h=void 0,c=[t]),n.rejectWith(h,c))}};e?u():(x.Deferred.getErrorHook?u.error=x.Deferred.getErrorHook():x.Deferred.getStackHook&&(u.error=x.Deferred.getStackHook()),t.setTimeout(u))}}return x.Deferred((function(t){n[0][3].add(a(0,t,i(o)?o:qt,t.notifyWith)),n[1][3].add(a(0,t,i(e)?e:qt)),n[2][3].add(a(0,t,i(s)?s:Ut))})).promise()},promise:function(t){return null!=t?x.extend(t,o):o}},r={};return x.each(n,(function(t,e){var i=e[2],a=e[5];o[e[1]]=i.add,a&&i.add((function(){s=a}),n[3-t][2].disable,n[3-t][3].disable,n[0][2].lock,n[0][3].lock),i.add(e[3].fire),r[e[0]]=function(){return r[e[0]+"With"](this===r?void 0:this,arguments),this},r[e[0]+"With"]=i.fireWith})),o.promise(r),e&&e.call(r,r),r},when:function(t){function e(t){return function(e){o[t]=this,a[t]=1<arguments.length?r.call(arguments):e,--n||l.resolveWith(o,a)}}var n=arguments.length,s=n,o=Array(s),a=r.call(arguments),l=x.Deferred();if(n<=1&&($t(t,l.done(e(s)).resolve,l.reject,!n),"pending"===l.state()||i(a[s]&&a[s].then)))return l.then();for(;s--;)$t(a[s],e(s),l.reject);return l.promise()}});var Yt=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/,Kt=(x.Deferred.exceptionHook=function(e,i){t.console&&t.console.warn&&e&&Yt.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,i)},x.readyException=function(e){t.setTimeout((function(){throw e}))},x.Deferred());function Xt(){m.removeEventListener("DOMContentLoaded",Xt),t.removeEventListener("load",Xt),x.ready()}function Jt(t,e,n,s,o,r,a){var l=0,h=t.length,c=null==n;if("object"===b(n))for(l in o=!0,n)Jt(t,e,l,n[l],!0,r,a);else if(void 0!==s&&(o=!0,i(s)||(a=!0),e=c?a?(e.call(t,s),null):(c=e,function(t,e,i){return c.call(x(t),i)}):e))for(;l<h;l++)e(t[l],n,a?s:s.call(t[l],l,e(t[l],n)));return o?t:c?e.call(t):h?e(t[0],n):r}x.fn.ready=function(t){return Kt.then(t).catch((function(t){x.readyException(t)})),this},x.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--x.readyWait:x.isReady)||(x.isReady=!0)!==t&&0<--x.readyWait||Kt.resolveWith(m,[x])}}),x.ready.then=Kt.then,"complete"===m.readyState||"loading"!==m.readyState&&!m.documentElement.doScroll?t.setTimeout(x.ready):(m.addEventListener("DOMContentLoaded",Xt),t.addEventListener("load",Xt));var Qt=/^-ms-/,Zt=/-([a-z])/g;function te(t,e){return e.toUpperCase()}function ee(t){return t.replace(Qt,"ms-").replace(Zt,te)}function ie(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType}function ne(){this.expando=x.expando+ne.uid++}ne.uid=1,ne.prototype={cache:function(t){var e=t[this.expando];return e||(e={},ie(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,i){var n,s=this.cache(t);if("string"==typeof e)s[ee(e)]=i;else for(n in e)s[ee(n)]=e[n];return s},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][ee(e)]},access:function(t,e,i){return void 0===e||e&&"string"==typeof e&&void 0===i?this.get(t,e):(this.set(t,e,i),void 0!==i?i:e)},remove:function(t,e){var i,n=t[this.expando];if(void 0!==n){if(void 0!==e){i=(e=Array.isArray(e)?e.map(ee):(e=ee(e))in n?[e]:e.match(Gt)||[]).length;for(;i--;)delete n[e[i]]}void 0!==e&&!x.isEmptyObject(n)||(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){return void 0!==(t=t[this.expando])&&!x.isEmptyObject(t)}};var se=new ne,oe=new ne,re=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ae=/[A-Z]/g;function le(t,e,i){var n,s;if(void 0===i&&1===t.nodeType)if(n="data-"+e.replace(ae,"-$&").toLowerCase(),"string"==typeof(i=t.getAttribute(n))){try{i="true"===(s=i)||"false"!==s&&("null"===s?null:s===+s+""?+s:re.test(s)?JSON.parse(s):s)}catch(t){}oe.set(t,e,i)}else i=void 0;return i}function he(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&pe(t)&&"none"===x.css(t,"display")}x.extend({hasData:function(t){return oe.hasData(t)||se.hasData(t)},data:function(t,e,i){return oe.access(t,e,i)},removeData:function(t,e){oe.remove(t,e)},_data:function(t,e,i){return se.access(t,e,i)},_removeData:function(t,e){se.remove(t,e)}}),x.fn.extend({data:function(t,e){var i,n,s,o=this[0],r=o&&o.attributes;if(void 0!==t)return"object"==typeof t?this.each((function(){oe.set(this,t)})):Jt(this,(function(e){var i;if(o&&void 0===e)return void 0!==(i=oe.get(o,t))||void 0!==(i=le(o,t))?i:void 0;this.each((function(){oe.set(this,t,e)}))}),null,e,1<arguments.length,null,!0);if(this.length&&(s=oe.get(o),1===o.nodeType)&&!se.get(o,"hasDataAttrs")){for(i=r.length;i--;)r[i]&&0===(n=r[i].name).indexOf("data-")&&(n=ee(n.slice(5)),le(o,n,s[n]));se.set(o,"hasDataAttrs",!0)}return s},removeData:function(t){return this.each((function(){oe.remove(this,t)}))}}),x.extend({queue:function(t,e,i){var n;if(t)return n=se.get(t,e=(e||"fx")+"queue"),i&&(!n||Array.isArray(i)?n=se.access(t,e,x.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=x.queue(t,e),n=i.length,s=i.shift(),o=x._queueHooks(t,e);"inprogress"===s&&(s=i.shift(),n--),s&&("fx"===e&&i.unshift("inprogress"),delete o.stop,s.call(t,(function(){x.dequeue(t,e)}),o)),!n&&o&&o.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return se.get(t,i)||se.access(t,i,{empty:x.Callbacks("once memory").add((function(){se.remove(t,[e+"queue",i])}))})}}),x.fn.extend({queue:function(t,e){var i=2;return"string"!=typeof t&&(e=t,t="fx",i--),arguments.length<i?x.queue(this[0],t):void 0===e?this:this.each((function(){var i=x.queue(this,t,e);x._queueHooks(this,t),"fx"===t&&"inprogress"!==i[0]&&x.dequeue(this,t)}))},dequeue:function(t){return this.each((function(){x.dequeue(this,t)}))},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){function i(){--s||o.resolveWith(r,[r])}var n,s=1,o=x.Deferred(),r=this,a=this.length;for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(n=se.get(r[a],t+"queueHooks"))&&n.empty&&(s++,n.empty.add(i));return i(),o.promise(e)}}),y=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source;var ce=new RegExp("^(?:([+-])=|)("+y+")([a-z%]*)$","i"),ue=["Top","Right","Bottom","Left"],de=m.documentElement,pe=function(t){return x.contains(t.ownerDocument,t)},fe={composed:!0};function ge(t,e,i,n){var s,o,r=20,a=n?function(){return n.cur()}:function(){return x.css(t,e,"")},l=a(),h=i&&i[3]||(x.cssNumber[e]?"":"px"),c=t.nodeType&&(x.cssNumber[e]||"px"!==h&&+l)&&ce.exec(x.css(t,e));if(c&&c[3]!==h){for(h=h||c[3],c=+(l/=2)||1;r--;)x.style(t,e,c+h),(1-o)*(1-(o=a()/l||.5))<=0&&(r=0),c/=o;x.style(t,e,(c*=2)+h),i=i||[]}return i&&(c=+c||+l||0,s=i[1]?c+(i[1]+1)*i[2]:+i[2],n)&&(n.unit=h,n.start=c,n.end=s),s}de.getRootNode&&(pe=function(t){return x.contains(t.ownerDocument,t)||t.getRootNode(fe)===t.ownerDocument});var me={};function ve(t,e){for(var i,n,s,o,r,a=[],l=0,h=t.length;l<h;l++)(n=t[l]).style&&(i=n.style.display,e?("none"===i&&(a[l]=se.get(n,"display")||null,a[l]||(n.style.display="")),""===n.style.display&&he(n)&&(a[l]=(r=o=void 0,o=(s=n).ownerDocument,s=s.nodeName,(r=me[s])||(o=o.body.appendChild(o.createElement(s)),r=x.css(o,"display"),o.parentNode.removeChild(o),me[s]=r="none"===r?"block":r),r))):"none"!==i&&(a[l]="none",se.set(n,"display",i)));for(l=0;l<h;l++)null!=a[l]&&(t[l].style.display=a[l]);return t}x.fn.extend({show:function(){return ve(this,!0)},hide:function(){return ve(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each((function(){he(this)?x(this).show():x(this).hide()}))}});var _e=/^(?:checkbox|radio)$/i,be=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ye=/^$|^module$|\/(?:java|ecma)script/i,we=(tt=m.createDocumentFragment().appendChild(m.createElement("div")),(et=m.createElement("input")).setAttribute("type","radio"),et.setAttribute("checked","checked"),et.setAttribute("name","t"),tt.appendChild(et),g.checkClone=tt.cloneNode(!0).cloneNode(!0).lastChild.checked,tt.innerHTML="<textarea>x</textarea>",g.noCloneChecked=!!tt.cloneNode(!0).lastChild.defaultValue,tt.innerHTML="<option></option>",g.option=!!tt.lastChild,{thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]});function xe(t,e){var i=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[];return void 0===e||e&&S(t,e)?x.merge([t],i):i}function Ce(t,e){for(var i=0,n=t.length;i<n;i++)se.set(t[i],"globalEval",!e||se.get(e[i],"globalEval"))}we.tbody=we.tfoot=we.colgroup=we.caption=we.thead,we.th=we.td,g.option||(we.optgroup=we.option=[1,"<select multiple='multiple'>","</select>"]);var Se=/<|&#?\w+;/;function ke(t,e,i,n,s){for(var o,r,a,l,h,c=e.createDocumentFragment(),u=[],d=0,p=t.length;d<p;d++)if((o=t[d])||0===o)if("object"===b(o))x.merge(u,o.nodeType?[o]:o);else if(Se.test(o)){for(r=r||c.appendChild(e.createElement("div")),a=(be.exec(o)||["",""])[1].toLowerCase(),a=we[a]||we._default,r.innerHTML=a[1]+x.htmlPrefilter(o)+a[2],h=a[0];h--;)r=r.lastChild;x.merge(u,r.childNodes),(r=c.firstChild).textContent=""}else u.push(e.createTextNode(o));for(c.textContent="",d=0;o=u[d++];)if(n&&-1<x.inArray(o,n))s&&s.push(o);else if(l=pe(o),r=xe(c.appendChild(o),"script"),l&&Ce(r),i)for(h=0;o=r[h++];)ye.test(o.type||"")&&i.push(o);return c}var Ee=/^([^.]*)(?:\.(.+)|)/;function De(){return!0}function Te(){return!1}function Me(t,e,i,n,s,o){var r,a;if("object"==typeof e){for(a in"string"!=typeof i&&(n=n||i,i=void 0),e)Me(t,a,i,n,e[a],o);return t}if(null==n&&null==s?(s=i,n=i=void 0):null==s&&("string"==typeof i?(s=n,n=void 0):(s=n,n=i,i=void 0)),!1===s)s=Te;else if(!s)return t;return 1===o&&(r=s,(s=function(t){return x().off(t),r.apply(this,arguments)}).guid=r.guid||(r.guid=x.guid++)),t.each((function(){x.event.add(this,e,s,n,i)}))}function Ie(t,e,i){i?(se.set(t,e,!1),x.event.add(t,e,{namespace:!1,handler:function(t){var i,n=se.get(this,e);if(1&t.isTrigger&&this[e]){if(n)(x.event.special[e]||{}).delegateType&&t.stopPropagation();else if(n=r.call(arguments),se.set(this,e,n),this[e](),i=se.get(this,e),se.set(this,e,!1),n!==i)return t.stopImmediatePropagation(),t.preventDefault(),i}else n&&(se.set(this,e,x.event.trigger(n[0],n.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=De)}})):void 0===se.get(t,e)&&x.event.add(t,e,De)}x.event={global:{},add:function(t,e,i,n,s){var o,r,a,l,h,c,u,d,p,f=se.get(t);if(ie(t))for(i.handler&&(i=(o=i).handler,s=o.selector),s&&x.find.matchesSelector(de,s),i.guid||(i.guid=x.guid++),a=(a=f.events)||(f.events=Object.create(null)),r=(r=f.handle)||(f.handle=function(e){return void 0!==x&&x.event.triggered!==e.type?x.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(Gt)||[""]).length;l--;)u=p=(d=Ee.exec(e[l])||[])[1],d=(d[2]||"").split(".").sort(),u&&(h=x.event.special[u]||{},u=(s?h.delegateType:h.bindType)||u,h=x.event.special[u]||{},p=x.extend({type:u,origType:p,data:n,handler:i,guid:i.guid,selector:s,needsContext:s&&x.expr.match.needsContext.test(s),namespace:d.join(".")},o),(c=a[u])||((c=a[u]=[]).delegateCount=0,h.setup&&!1!==h.setup.call(t,n,d,r))||t.addEventListener&&t.addEventListener(u,r),h.add&&(h.add.call(t,p),p.handler.guid||(p.handler.guid=i.guid)),s?c.splice(c.delegateCount++,0,p):c.push(p),x.event.global[u]=!0)},remove:function(t,e,i,n,s){var o,r,a,l,h,c,u,d,p,f,g,m=se.hasData(t)&&se.get(t);if(m&&(l=m.events)){for(h=(e=(e||"").match(Gt)||[""]).length;h--;)if(p=g=(a=Ee.exec(e[h])||[])[1],f=(a[2]||"").split(".").sort(),p){for(u=x.event.special[p]||{},d=l[p=(n?u.delegateType:u.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),r=o=d.length;o--;)c=d[o],!s&&g!==c.origType||i&&i.guid!==c.guid||a&&!a.test(c.namespace)||n&&n!==c.selector&&("**"!==n||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,u.remove&&u.remove.call(t,c));r&&!d.length&&(u.teardown&&!1!==u.teardown.call(t,f,m.handle)||x.removeEvent(t,p,m.handle),delete l[p])}else for(p in l)x.event.remove(t,p+e[h],i,n,!0);x.isEmptyObject(l)&&se.remove(t,"handle events")}},dispatch:function(t){var e,i,n,s,o,r=new Array(arguments.length),a=x.event.fix(t),l=(t=(se.get(this,"events")||Object.create(null))[a.type]||[],x.event.special[a.type]||{});for(r[0]=a,e=1;e<arguments.length;e++)r[e]=arguments[e];if(a.delegateTarget=this,!l.preDispatch||!1!==l.preDispatch.call(this,a)){for(o=x.event.handlers.call(this,a,t),e=0;(n=o[e++])&&!a.isPropagationStopped();)for(a.currentTarget=n.elem,i=0;(s=n.handlers[i++])&&!a.isImmediatePropagationStopped();)a.rnamespace&&!1!==s.namespace&&!a.rnamespace.test(s.namespace)||(a.handleObj=s,a.data=s.data,void 0!==(s=((x.event.special[s.origType]||{}).handle||s.handler).apply(n.elem,r))&&!1===(a.result=s)&&(a.preventDefault(),a.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,a),a.result}},handlers:function(t,e){var i,n,s,o,r,a=[],l=e.delegateCount,h=t.target;if(l&&h.nodeType&&!("click"===t.type&&1<=t.button))for(;h!==this;h=h.parentNode||this)if(1===h.nodeType&&("click"!==t.type||!0!==h.disabled)){for(o=[],r={},i=0;i<l;i++)void 0===r[s=(n=e[i]).selector+" "]&&(r[s]=n.needsContext?-1<x(s,this).index(h):x.find(s,this,null,[h]).length),r[s]&&o.push(n);o.length&&a.push({elem:h,handlers:o})}return h=this,l<e.length&&a.push({elem:h,handlers:e.slice(l)}),a},addProp:function(t,e){Object.defineProperty(x.Event.prototype,t,{enumerable:!0,configurable:!0,get:i(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[x.expando]?t:new x.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){return t=this||t,_e.test(t.type)&&t.click&&S(t,"input")&&Ie(t,"click",!0),!1},trigger:function(t){return t=this||t,_e.test(t.type)&&t.click&&S(t,"input")&&Ie(t,"click"),!0},_default:function(t){return t=t.target,_e.test(t.type)&&t.click&&S(t,"input")&&se.get(t,"click")||S(t,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},x.removeEvent=function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)},x.Event=function(t,e){if(!(this instanceof x.Event))return new x.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?De:Te,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&x.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[x.expando]=!0},x.Event.prototype={constructor:x.Event,isDefaultPrevented:Te,isPropagationStopped:Te,isImmediatePropagationStopped:Te,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=De,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=De,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=De,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},x.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},x.event.addProp),x.each({focus:"focusin",blur:"focusout"},(function(t,e){function i(t){var i,n;m.documentMode?(i=se.get(this,"handle"),(n=x.event.fix(t)).type="focusin"===t.type?"focus":"blur",n.isSimulated=!0,i(t),n.target===n.currentTarget&&i(n)):x.event.simulate(e,t.target,x.event.fix(t))}x.event.special[t]={setup:function(){var n;if(Ie(this,t,!0),!m.documentMode)return!1;(n=se.get(this,e))||this.addEventListener(e,i),se.set(this,e,(n||0)+1)},trigger:function(){return Ie(this,t),!0},teardown:function(){var t;if(!m.documentMode)return!1;(t=se.get(this,e)-1)?se.set(this,e,t):(this.removeEventListener(e,i),se.remove(this,e))},_default:function(e){return se.get(e.target,t)},delegateType:e},x.event.special[e]={setup:function(){var n=this.ownerDocument||this.document||this,s=m.documentMode?this:n,o=se.get(s,e);o||(m.documentMode?this.addEventListener(e,i):n.addEventListener(t,i,!0)),se.set(s,e,(o||0)+1)},teardown:function(){var n=this.ownerDocument||this.document||this,s=m.documentMode?this:n,o=se.get(s,e)-1;o?se.set(s,e,o):(m.documentMode?this.removeEventListener(e,i):n.removeEventListener(t,i,!0),se.remove(s,e))}}})),x.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(t,e){x.event.special[t]={delegateType:e,bindType:e,handle:function(t){var i,n=t.relatedTarget,s=t.handleObj;return n&&(n===this||x.contains(this,n))||(t.type=s.origType,i=s.handler.apply(this,arguments),t.type=e),i}}})),x.fn.extend({on:function(t,e,i,n){return Me(this,t,e,i,n)},one:function(t,e,i,n){return Me(this,t,e,i,n,1)},off:function(t,e,i){var n,s;if(t&&t.preventDefault&&t.handleObj)n=t.handleObj,x(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler);else{if("object"!=typeof t)return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=Te),this.each((function(){x.event.remove(this,t,i,e)}));for(s in t)this.off(s,e,t[s])}return this}});var Ae=/<script|<style|<link/i,Pe=/checked\s*(?:[^=]|=\s*.checked.)/i,Oe=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Fe(t,e){return S(t,"table")&&S(11!==e.nodeType?e:e.firstChild,"tr")&&x(t).children("tbody")[0]||t}function He(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Ve(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Ne(t,e){var i,n,s,o;if(1===e.nodeType){if(se.hasData(t)&&(o=se.get(t).events))for(s in se.remove(e,"handle events"),o)for(i=0,n=o[s].length;i<n;i++)x.event.add(e,s,o[s][i]);oe.hasData(t)&&(t=oe.access(t),t=x.extend({},t),oe.set(e,t))}}function We(t,e,n,s){e=a(e);var o,r,l,h,c,u,d=0,p=t.length,f=p-1,m=e[0],v=i(m);if(v||1<p&&"string"==typeof m&&!g.checkClone&&Pe.test(m))return t.each((function(i){var o=t.eq(i);v&&(e[0]=m.call(this,i,o.html())),We(o,e,n,s)}));if(p&&(r=(o=ke(e,t[0].ownerDocument,!1,t,s)).firstChild,1===o.childNodes.length&&(o=r),r||s)){for(h=(l=x.map(xe(o,"script"),He)).length;d<p;d++)c=o,d!==f&&(c=x.clone(c,!0,!0),h)&&x.merge(l,xe(c,"script")),n.call(t[d],c,d);if(h)for(u=l[l.length-1].ownerDocument,x.map(l,Ve),d=0;d<h;d++)c=l[d],ye.test(c.type||"")&&!se.access(c,"globalEval")&&x.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?x._evalUrl&&!c.noModule&&x._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):_(c.textContent.replace(Oe,""),c,u))}return t}function Re(t,e,i){for(var n,s=e?x.filter(e,t):t,o=0;null!=(n=s[o]);o++)i||1!==n.nodeType||x.cleanData(xe(n)),n.parentNode&&(i&&pe(n)&&Ce(xe(n,"script")),n.parentNode.removeChild(n));return t}function Be(e){var i=e.ownerDocument.defaultView;return(i=i&&i.opener?i:t).getComputedStyle(e)}function Le(t,e,i){var n,s={};for(n in e)s[n]=t.style[n],t.style[n]=e[n];for(n in i=i.call(t),e)t.style[n]=s[n];return i}x.extend({htmlPrefilter:function(t){return t},clone:function(t,e,i){var n,s,o,r,a,l,h,c=t.cloneNode(!0),u=pe(t);if(!(g.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||x.isXMLDoc(t)))for(r=xe(c),n=0,s=(o=xe(t)).length;n<s;n++)a=o[n],"input"===(h=(l=r[n]).nodeName.toLowerCase())&&_e.test(a.type)?l.checked=a.checked:"input"!==h&&"textarea"!==h||(l.defaultValue=a.defaultValue);if(e)if(i)for(o=o||xe(t),r=r||xe(c),n=0,s=o.length;n<s;n++)Ne(o[n],r[n]);else Ne(t,c);return 0<(r=xe(c,"script")).length&&Ce(r,!u&&xe(t,"script")),c},cleanData:function(t){for(var e,i,n,s=x.event.special,o=0;void 0!==(i=t[o]);o++)if(ie(i)){if(e=i[se.expando]){if(e.events)for(n in e.events)s[n]?x.event.remove(i,n):x.removeEvent(i,n,e.handle);i[se.expando]=void 0}i[oe.expando]&&(i[oe.expando]=void 0)}}}),x.fn.extend({detach:function(t){return Re(this,t,!0)},remove:function(t){return Re(this,t)},text:function(t){return Jt(this,(function(t){return void 0===t?x.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)}))}),null,t,arguments.length)},append:function(){return We(this,arguments,(function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Fe(this,t).appendChild(t)}))},prepend:function(){return We(this,arguments,(function(t){var e;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(e=Fe(this,t)).insertBefore(t,e.firstChild)}))},before:function(){return We(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this)}))},after:function(){return We(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)}))},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(x.cleanData(xe(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map((function(){return x.clone(this,t,e)}))},html:function(t){return Jt(this,(function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Ae.test(t)&&!we[(be.exec(t)||["",""])[1].toLowerCase()]){t=x.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&(x.cleanData(xe(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)}),null,t,arguments.length)},replaceWith:function(){var t=[];return We(this,arguments,(function(e){var i=this.parentNode;x.inArray(this,t)<0&&(x.cleanData(xe(this)),i)&&i.replaceChild(e,this)}),t)}}),x.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(t,e){x.fn[t]=function(t){for(var i,n=[],s=x(t),o=s.length-1,r=0;r<=o;r++)i=r===o?this:this.clone(!0),x(s[r])[e](i),l.apply(n,i.get());return this.pushStack(n)}}));var ze,je,Ge,qe,Ue,$e,Ye,Ke,Xe=new RegExp("^("+y+")(?!px)[a-z%]+$","i"),Je=/^--/,Qe=new RegExp(ue.join("|"),"i");function Ze(){var e;Ke&&(Ye.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",Ke.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",de.appendChild(Ye).appendChild(Ke),e=t.getComputedStyle(Ke),ze="1%"!==e.top,$e=12===ti(e.marginLeft),Ke.style.right="60%",qe=36===ti(e.right),je=36===ti(e.width),Ke.style.position="absolute",Ge=12===ti(Ke.offsetWidth/3),de.removeChild(Ye),Ke=null)}function ti(t){return Math.round(parseFloat(t))}function ei(t,e,i){var n,s=Je.test(e),o=t.style;return(i=i||Be(t))&&(n=i.getPropertyValue(e)||i[e],""!==(n=s?n&&(n.replace(M,"$1")||void 0):n)||pe(t)||(n=x.style(t,e)),!g.pixelBoxStyles())&&Xe.test(n)&&Qe.test(e)&&(s=o.width,t=o.minWidth,e=o.maxWidth,o.minWidth=o.maxWidth=o.width=n,n=i.width,o.width=s,o.minWidth=t,o.maxWidth=e),void 0!==n?n+"":n}function ii(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}Ye=m.createElement("div"),(Ke=m.createElement("div")).style&&(Ke.style.backgroundClip="content-box",Ke.cloneNode(!0).style.backgroundClip="",g.clearCloneStyle="content-box"===Ke.style.backgroundClip,x.extend(g,{boxSizingReliable:function(){return Ze(),je},pixelBoxStyles:function(){return Ze(),qe},pixelPosition:function(){return Ze(),ze},reliableMarginLeft:function(){return Ze(),$e},scrollboxSize:function(){return Ze(),Ge},reliableTrDimensions:function(){var e,i,n;return null==Ue&&(e=m.createElement("table"),i=m.createElement("tr"),n=m.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",i.style.cssText="box-sizing:content-box;border:1px solid",i.style.height="1px",n.style.height="9px",n.style.display="block",de.appendChild(e).appendChild(i).appendChild(n),n=t.getComputedStyle(i),Ue=parseInt(n.height,10)+parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10)===i.offsetHeight,de.removeChild(e)),Ue}}));var ni=["Webkit","Moz","ms"],si=m.createElement("div").style,oi={};function ri(t){return x.cssProps[t]||oi[t]||(t in si?t:oi[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),i=ni.length;i--;)if((t=ni[i]+e)in si)return t}(t)||t)}var ai=/^(none|table(?!-c[ea]).+)/,li={position:"absolute",visibility:"hidden",display:"block"},hi={letterSpacing:"0",fontWeight:"400"};function ci(t,e,i){var n=ce.exec(e);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):e}function ui(t,e,i,n,s,o){var r="width"===e?1:0,a=0,l=0,h=0;if(i===(n?"border":"content"))return 0;for(;r<4;r+=2)"margin"===i&&(h+=x.css(t,i+ue[r],!0,s)),n?("content"===i&&(l-=x.css(t,"padding"+ue[r],!0,s)),"margin"!==i&&(l-=x.css(t,"border"+ue[r]+"Width",!0,s))):(l+=x.css(t,"padding"+ue[r],!0,s),"padding"!==i?l+=x.css(t,"border"+ue[r]+"Width",!0,s):a+=x.css(t,"border"+ue[r]+"Width",!0,s));return!n&&0<=o&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-l-a-.5))||0),l+h}function di(t,e,i){var n=Be(t),s=(!g.boxSizingReliable()||i)&&"border-box"===x.css(t,"boxSizing",!1,n),o=s,r=ei(t,e,n),a="offset"+e[0].toUpperCase()+e.slice(1);if(Xe.test(r)){if(!i)return r;r="auto"}return(!g.boxSizingReliable()&&s||!g.reliableTrDimensions()&&S(t,"tr")||"auto"===r||!parseFloat(r)&&"inline"===x.css(t,"display",!1,n))&&t.getClientRects().length&&(s="border-box"===x.css(t,"boxSizing",!1,n),o=a in t)&&(r=t[a]),(r=parseFloat(r)||0)+ui(t,e,i||(s?"border":"content"),o,n,r)+"px"}function pi(t,e,i,n,s){return new pi.prototype.init(t,e,i,n,s)}x.extend({cssHooks:{opacity:{get:function(t,e){if(e)return""===(e=ei(t,"opacity"))?"1":e}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var s,o,r,a=ee(e),l=Je.test(e),h=t.style;if(l||(e=ri(a)),r=x.cssHooks[e]||x.cssHooks[a],void 0===i)return r&&"get"in r&&void 0!==(s=r.get(t,!1,n))?s:h[e];"string"==(o=typeof i)&&(s=ce.exec(i))&&s[1]&&(i=ge(t,e,s),o="number"),null==i||i!=i||("number"!==o||l||(i+=s&&s[3]||(x.cssNumber[a]?"":"px")),g.clearCloneStyle||""!==i||0!==e.indexOf("background")||(h[e]="inherit"),r&&"set"in r&&void 0===(i=r.set(t,i,n)))||(l?h.setProperty(e,i):h[e]=i)}},css:function(t,e,i,n){var s,o=ee(e);return Je.test(e)||(e=ri(o)),"normal"===(s=void 0===(s=(o=x.cssHooks[e]||x.cssHooks[o])&&"get"in o?o.get(t,!0,i):s)?ei(t,e,n):s)&&e in hi&&(s=hi[e]),(""===i||i)&&(o=parseFloat(s),!0===i||isFinite(o))?o||0:s}}),x.each(["height","width"],(function(t,e){x.cssHooks[e]={get:function(t,i,n){if(i)return!ai.test(x.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?di(t,e,n):Le(t,li,(function(){return di(t,e,n)}))},set:function(t,i,n){var s=Be(t),o=!g.scrollboxSize()&&"absolute"===s.position,r=(o||n)&&"border-box"===x.css(t,"boxSizing",!1,s);return n=n?ui(t,e,n,r,s):0,r&&o&&(n-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(s[e])-ui(t,e,"border",!1,s)-.5)),n&&(r=ce.exec(i))&&"px"!==(r[3]||"px")&&(t.style[e]=i,i=x.css(t,e)),ci(0,i,n)}}})),x.cssHooks.marginLeft=ii(g.reliableMarginLeft,(function(t,e){if(e)return(parseFloat(ei(t,"marginLeft"))||t.getBoundingClientRect().left-Le(t,{marginLeft:0},(function(){return t.getBoundingClientRect().left})))+"px"})),x.each({margin:"",padding:"",border:"Width"},(function(t,e){x.cssHooks[t+e]={expand:function(i){for(var n=0,s={},o="string"==typeof i?i.split(" "):[i];n<4;n++)s[t+ue[n]+e]=o[n]||o[n-2]||o[0];return s}},"margin"!==t&&(x.cssHooks[t+e].set=ci)})),x.fn.extend({css:function(t,e){return Jt(this,(function(t,e,i){var n,s,o={},r=0;if(Array.isArray(e)){for(n=Be(t),s=e.length;r<s;r++)o[e[r]]=x.css(t,e[r],!1,n);return o}return void 0!==i?x.style(t,e,i):x.css(t,e)}),t,e,1<arguments.length)}}),((x.Tween=pi).prototype={constructor:pi,init:function(t,e,i,n,s,o){this.elem=t,this.prop=i,this.easing=s||x.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=o||(x.cssNumber[i]?"":"px")},cur:function(){var t=pi.propHooks[this.prop];return(t&&t.get?t:pi.propHooks._default).get(this)},run:function(t){var e,i=pi.propHooks[this.prop];return this.options.duration?this.pos=e=x.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(i&&i.set?i:pi.propHooks._default).set(this),this}}).init.prototype=pi.prototype,(pi.propHooks={_default:{get:function(t){return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(t=x.css(t.elem,t.prop,""))&&"auto"!==t?t:0},set:function(t){x.fx.step[t.prop]?x.fx.step[t.prop](t):1!==t.elem.nodeType||!x.cssHooks[t.prop]&&null==t.elem.style[ri(t.prop)]?t.elem[t.prop]=t.now:x.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=pi.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},x.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},x.fx=pi.prototype.init,x.fx.step={};var fi,gi,mi=/^(?:toggle|show|hide)$/,vi=/queueHooks$/;function _i(){gi&&(!1===m.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(_i):t.setTimeout(_i,x.fx.interval),x.fx.tick())}function bi(){return t.setTimeout((function(){fi=void 0})),fi=Date.now()}function yi(t,e){var i,n=0,s={height:t};for(e=e?1:0;n<4;n+=2-e)s["margin"+(i=ue[n])]=s["padding"+i]=t;return e&&(s.opacity=s.width=t),s}function wi(t,e,i){for(var n,s=(xi.tweeners[e]||[]).concat(xi.tweeners["*"]),o=0,r=s.length;o<r;o++)if(n=s[o].call(i,e,t))return n}function xi(t,e,n){var s,o,r,a,l,h,c,u=0,d=xi.prefilters.length,p=x.Deferred().always((function(){delete f.elem})),f=function(){if(!o){for(var e=fi||bi(),i=1-((e=Math.max(0,g.startTime+g.duration-e))/g.duration||0),n=0,s=g.tweens.length;n<s;n++)g.tweens[n].run(i);if(p.notifyWith(t,[g,i,e]),i<1&&s)return e;s||p.notifyWith(t,[g,1,0]),p.resolveWith(t,[g])}return!1},g=p.promise({elem:t,props:x.extend({},e),opts:x.extend(!0,{specialEasing:{},easing:x.easing._default},n),originalProperties:e,originalOptions:n,startTime:fi||bi(),duration:n.duration,tweens:[],createTween:function(e,i){return i=x.Tween(t,g.opts,e,i,g.opts.specialEasing[e]||g.opts.easing),g.tweens.push(i),i},stop:function(e){var i=0,n=e?g.tweens.length:0;if(!o){for(o=!0;i<n;i++)g.tweens[i].run(1);e?(p.notifyWith(t,[g,1,0]),p.resolveWith(t,[g,e])):p.rejectWith(t,[g,e])}return this}}),m=g.props,v=m,_=g.opts.specialEasing;for(r in v)if(l=_[a=ee(r)],h=v[r],Array.isArray(h)&&(l=h[1],h=v[r]=h[0]),r!==a&&(v[a]=h,delete v[r]),(c=x.cssHooks[a])&&"expand"in c)for(r in h=c.expand(h),delete v[a],h)r in v||(v[r]=h[r],_[r]=l);else _[a]=l;for(;u<d;u++)if(s=xi.prefilters[u].call(g,t,m,g.opts))return i(s.stop)&&(x._queueHooks(g.elem,g.opts.queue).stop=s.stop.bind(s)),s;return x.map(m,wi,g),i(g.opts.start)&&g.opts.start.call(t,g),g.progress(g.opts.progress).done(g.opts.done,g.opts.complete).fail(g.opts.fail).always(g.opts.always),x.fx.timer(x.extend(f,{elem:t,anim:g,queue:g.opts.queue})),g}x.Animation=x.extend(xi,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return ge(i.elem,t,ce.exec(e),i),i}]},tweener:function(t,e){for(var n,s=0,o=(t=i(t)?(e=t,["*"]):t.match(Gt)).length;s<o;s++)n=t[s],xi.tweeners[n]=xi.tweeners[n]||[],xi.tweeners[n].unshift(e)},prefilters:[function(t,e,i){var n,s,o,r,a,l,h,c="width"in e||"height"in e,u=this,d={},p=t.style,f=t.nodeType&&he(t),g=se.get(t,"fxshow");for(n in i.queue||(null==(r=x._queueHooks(t,"fx")).unqueued&&(r.unqueued=0,a=r.empty.fire,r.empty.fire=function(){r.unqueued||a()}),r.unqueued++,u.always((function(){u.always((function(){r.unqueued--,x.queue(t,"fx").length||r.empty.fire()}))}))),e)if(s=e[n],mi.test(s)){if(delete e[n],o=o||"toggle"===s,s===(f?"hide":"show")){if("show"!==s||!g||void 0===g[n])continue;f=!0}d[n]=g&&g[n]||x.style(t,n)}if((l=!x.isEmptyObject(e))||!x.isEmptyObject(d))for(n in c&&1===t.nodeType&&(i.overflow=[p.overflow,p.overflowX,p.overflowY],null==(h=g&&g.display)&&(h=se.get(t,"display")),"none"===(c=x.css(t,"display"))&&(h?c=h:(ve([t],!0),h=t.style.display||h,c=x.css(t,"display"),ve([t]))),"inline"===c||"inline-block"===c&&null!=h)&&"none"===x.css(t,"float")&&(l||(u.done((function(){p.display=h})),null==h&&(c=p.display,h="none"===c?"":c)),p.display="inline-block"),i.overflow&&(p.overflow="hidden",u.always((function(){p.overflow=i.overflow[0],p.overflowX=i.overflow[1],p.overflowY=i.overflow[2]}))),l=!1,d)l||(g?"hidden"in g&&(f=g.hidden):g=se.access(t,"fxshow",{display:h}),o&&(g.hidden=!f),f&&ve([t],!0),u.done((function(){for(n in f||ve([t]),se.remove(t,"fxshow"),d)x.style(t,n,d[n])}))),l=wi(f?g[n]:0,n,u),n in g||(g[n]=l.start,f&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?xi.prefilters.unshift(t):xi.prefilters.push(t)}}),x.speed=function(t,e,n){var s=t&&"object"==typeof t?x.extend({},t):{complete:n||!n&&e||i(t)&&t,duration:t,easing:n&&e||e&&!i(e)&&e};return x.fx.off?s.duration=0:"number"!=typeof s.duration&&(s.duration in x.fx.speeds?s.duration=x.fx.speeds[s.duration]:s.duration=x.fx.speeds._default),null!=s.queue&&!0!==s.queue||(s.queue="fx"),s.old=s.complete,s.complete=function(){i(s.old)&&s.old.call(this),s.queue&&x.dequeue(this,s.queue)},s},x.fn.extend({fadeTo:function(t,e,i,n){return this.filter(he).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(t,e,i,n){function s(){var e=xi(this,x.extend({},t),r);(o||se.get(this,"finish"))&&e.stop(!0)}var o=x.isEmptyObject(t),r=x.speed(e,i,n);return s.finish=s,o||!1===r.queue?this.each(s):this.queue(r.queue,s)},stop:function(t,e,i){function n(t){var e=t.stop;delete t.stop,e(i)}return"string"!=typeof t&&(i=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each((function(){var e=!0,s=null!=t&&t+"queueHooks",o=x.timers,r=se.get(this);if(s)r[s]&&r[s].stop&&n(r[s]);else for(s in r)r[s]&&r[s].stop&&vi.test(s)&&n(r[s]);for(s=o.length;s--;)o[s].elem!==this||null!=t&&o[s].queue!==t||(o[s].anim.stop(i),e=!1,o.splice(s,1));!e&&i||x.dequeue(this,t)}))},finish:function(t){return!1!==t&&(t=t||"fx"),this.each((function(){var e,i=se.get(this),n=i[t+"queue"],s=i[t+"queueHooks"],o=x.timers,r=n?n.length:0;for(i.finish=!0,x.queue(this,t,[]),s&&s.stop&&s.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<r;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete i.finish}))}}),x.each(["toggle","show","hide"],(function(t,e){var i=x.fn[e];x.fn[e]=function(t,n,s){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(yi(e,!0),t,n,s)}})),x.each({slideDown:yi("show"),slideUp:yi("hide"),slideToggle:yi("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(t,e){x.fn[t]=function(t,i,n){return this.animate(e,t,i,n)}})),x.timers=[],x.fx.tick=function(){var t,e=0,i=x.timers;for(fi=Date.now();e<i.length;e++)(t=i[e])()||i[e]!==t||i.splice(e--,1);i.length||x.fx.stop(),fi=void 0},x.fx.timer=function(t){x.timers.push(t),x.fx.start()},x.fx.interval=13,x.fx.start=function(){gi||(gi=!0,_i())},x.fx.stop=function(){gi=null},x.fx.speeds={slow:600,fast:200,_default:400},x.fn.delay=function(e,i){return e=x.fx&&x.fx.speeds[e]||e,this.queue(i=i||"fx",(function(i,n){var s=t.setTimeout(i,e);n.stop=function(){t.clearTimeout(s)}}))},et=m.createElement("input"),tt=m.createElement("select").appendChild(m.createElement("option")),et.type="checkbox",g.checkOn=""!==et.value,g.optSelected=tt.selected,(et=m.createElement("input")).value="t",et.type="radio",g.radioValue="t"===et.value;var Ci,Si=x.expr.attrHandle,ki=(x.fn.extend({attr:function(t,e){return Jt(this,x.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each((function(){x.removeAttr(this,t)}))}}),x.extend({attr:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?x.prop(t,e,i):(1===o&&x.isXMLDoc(t)||(s=x.attrHooks[e.toLowerCase()]||(x.expr.match.bool.test(e)?Ci:void 0)),void 0!==i?null===i?void x.removeAttr(t,e):s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:(t.setAttribute(e,i+""),i):s&&"get"in s&&null!==(n=s.get(t,e))||null!=(n=x.find.attr(t,e))?n:void 0)},attrHooks:{type:{set:function(t,e){var i;if(!g.radioValue&&"radio"===e&&S(t,"input"))return i=t.value,t.setAttribute("type",e),i&&(t.value=i),e}}},removeAttr:function(t,e){var i,n=0,s=e&&e.match(Gt);if(s&&1===t.nodeType)for(;i=s[n++];)t.removeAttribute(i)}}),Ci={set:function(t,e,i){return!1===e?x.removeAttr(t,i):t.setAttribute(i,i),i}},x.each(x.expr.match.bool.source.match(/\w+/g),(function(t,e){var i=Si[e]||x.find.attr;Si[e]=function(t,e,n){var s,o,r=e.toLowerCase();return n||(o=Si[r],Si[r]=s,s=null!=i(t,e,n)?r:null,Si[r]=o),s}})),/^(?:input|select|textarea|button)$/i),Ei=/^(?:a|area)$/i;function Di(t){return(t.match(Gt)||[]).join(" ")}function Ti(t){return t.getAttribute&&t.getAttribute("class")||""}function Mi(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(Gt)||[]}function Ii(t){t.stopPropagation()}x.fn.extend({prop:function(t,e){return Jt(this,x.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each((function(){delete this[x.propFix[t]||t]}))}}),x.extend({prop:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&x.isXMLDoc(t)||(e=x.propFix[e]||e,s=x.propHooks[e]),void 0!==i?s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:t[e]=i:s&&"get"in s&&null!==(n=s.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=x.find.attr(t,"tabindex");return e?parseInt(e,10):ki.test(t.nodeName)||Ei.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),g.optSelected||(x.propHooks.selected={get:function(t){return(t=t.parentNode)&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(t){(t=t.parentNode)&&(t.selectedIndex,t.parentNode)&&t.parentNode.selectedIndex}}),x.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){x.propFix[this.toLowerCase()]=this})),x.fn.extend({addClass:function(t){var e,n,s,o,r,a;return i(t)?this.each((function(e){x(this).addClass(t.call(this,e,Ti(this)))})):(e=Mi(t)).length?this.each((function(){if(s=Ti(this),n=1===this.nodeType&&" "+Di(s)+" "){for(r=0;r<e.length;r++)o=e[r],n.indexOf(" "+o+" ")<0&&(n+=o+" ");a=Di(n),s!==a&&this.setAttribute("class",a)}})):this},removeClass:function(t){var e,n,s,o,r,a;return i(t)?this.each((function(e){x(this).removeClass(t.call(this,e,Ti(this)))})):arguments.length?(e=Mi(t)).length?this.each((function(){if(s=Ti(this),n=1===this.nodeType&&" "+Di(s)+" "){for(r=0;r<e.length;r++)for(o=e[r];-1<n.indexOf(" "+o+" ");)n=n.replace(" "+o+" "," ");a=Di(n),s!==a&&this.setAttribute("class",a)}})):this:this.attr("class","")},toggleClass:function(t,e){var n,s,o,r,a=typeof t,l="string"==a||Array.isArray(t);return i(t)?this.each((function(i){x(this).toggleClass(t.call(this,i,Ti(this),e),e)})):"boolean"==typeof e&&l?e?this.addClass(t):this.removeClass(t):(n=Mi(t),this.each((function(){if(l)for(r=x(this),o=0;o<n.length;o++)s=n[o],r.hasClass(s)?r.removeClass(s):r.addClass(s);else void 0!==t&&"boolean"!=a||((s=Ti(this))&&se.set(this,"__className__",s),this.setAttribute&&this.setAttribute("class",!s&&!1!==t&&se.get(this,"__className__")||""))})))},hasClass:function(t){for(var e,i=0,n=" "+t+" ";e=this[i++];)if(1===e.nodeType&&-1<(" "+Di(Ti(e))+" ").indexOf(n))return!0;return!1}});var Ai=/\r/g,Pi=(x.fn.extend({val:function(t){var e,n,s,o=this[0];return arguments.length?(s=i(t),this.each((function(i){1!==this.nodeType||(null==(i=s?t.call(this,i,x(this).val()):t)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=x.map(i,(function(t){return null==t?"":t+""}))),(e=x.valHooks[this.type]||x.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,i,"value"))||(this.value=i)}))):o?(e=x.valHooks[o.type]||x.valHooks[o.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(o,"value"))?n:"string"==typeof(n=o.value)?n.replace(Ai,""):null==n?"":n:void 0}}),x.extend({valHooks:{option:{get:function(t){var e=x.find.attr(t,"value");return null!=e?e:Di(x.text(t))}},select:{get:function(t){for(var e,i=t.options,n=t.selectedIndex,s="select-one"===t.type,o=s?null:[],r=s?n+1:i.length,a=n<0?r:s?n:0;a<r;a++)if(((e=i[a]).selected||a===n)&&!e.disabled&&(!e.parentNode.disabled||!S(e.parentNode,"optgroup"))){if(e=x(e).val(),s)return e;o.push(e)}return o},set:function(t,e){for(var i,n,s=t.options,o=x.makeArray(e),r=s.length;r--;)((n=s[r]).selected=-1<x.inArray(x.valHooks.option.get(n),o))&&(i=!0);return i||(t.selectedIndex=-1),o}}}}),x.each(["radio","checkbox"],(function(){x.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<x.inArray(x(t).val(),e)}},g.checkOn||(x.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})})),t.location),Oi={guid:Date.now()},Fi=/\?/,Hi=(x.parseXML=function(e){var i,n;if(!e||"string"!=typeof e)return null;try{i=(new t.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=i&&i.getElementsByTagName("parsererror")[0],i&&!n||x.error("Invalid XML: "+(n?x.map(n.childNodes,(function(t){return t.textContent})).join("\n"):e)),i},/^(?:focusinfocus|focusoutblur)$/),Vi=(x.extend(x.event,{trigger:function(e,s,o,r){var a,l,h,c,u,p,f,g=[o||m],v=d.call(e,"type")?e.type:e,_=d.call(e,"namespace")?e.namespace.split("."):[],b=f=l=o=o||m;if(3!==o.nodeType&&8!==o.nodeType&&!Hi.test(v+x.event.triggered)&&(-1<v.indexOf(".")&&(v=(_=v.split(".")).shift(),_.sort()),c=v.indexOf(":")<0&&"on"+v,(e=e[x.expando]?e:new x.Event(v,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=_.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+_.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=o),s=null==s?[e]:x.makeArray(s,[e]),p=x.event.special[v]||{},r||!p.trigger||!1!==p.trigger.apply(o,s))){if(!r&&!p.noBubble&&!n(o)){for(h=p.delegateType||v,Hi.test(h+v)||(b=b.parentNode);b;b=b.parentNode)g.push(b),l=b;l===(o.ownerDocument||m)&&g.push(l.defaultView||l.parentWindow||t)}for(a=0;(b=g[a++])&&!e.isPropagationStopped();)f=b,e.type=1<a?h:p.bindType||v,(u=(se.get(b,"events")||Object.create(null))[e.type]&&se.get(b,"handle"))&&u.apply(b,s),(u=c&&b[c])&&u.apply&&ie(b)&&(e.result=u.apply(b,s),!1===e.result)&&e.preventDefault();return e.type=v,r||e.isDefaultPrevented()||p._default&&!1!==p._default.apply(g.pop(),s)||!ie(o)||c&&i(o[v])&&!n(o)&&((l=o[c])&&(o[c]=null),x.event.triggered=v,e.isPropagationStopped()&&f.addEventListener(v,Ii),o[v](),e.isPropagationStopped()&&f.removeEventListener(v,Ii),x.event.triggered=void 0,l)&&(o[c]=l),e.result}},simulate:function(t,e,i){i=x.extend(new x.Event,i,{type:t,isSimulated:!0}),x.event.trigger(i,null,e)}}),x.fn.extend({trigger:function(t,e){return this.each((function(){x.event.trigger(t,e,this)}))},triggerHandler:function(t,e){var i=this[0];if(i)return x.event.trigger(t,e,i,!0)}}),/\[\]$/),Ni=/\r?\n/g,Wi=/^(?:submit|button|image|reset|file)$/i,Ri=/^(?:input|select|textarea|keygen)/i;x.param=function(t,e){function n(t,e){e=i(e)?e():e,o[o.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==e?"":e)}var s,o=[];if(null==t)return"";if(Array.isArray(t)||t.jquery&&!x.isPlainObject(t))x.each(t,(function(){n(this.name,this.value)}));else for(s in t)!function t(e,i,n,s){if(Array.isArray(i))x.each(i,(function(i,o){n||Vi.test(e)?s(e,o):t(e+"["+("object"==typeof o&&null!=o?i:"")+"]",o,n,s)}));else if(n||"object"!==b(i))s(e,i);else for(var o in i)t(e+"["+o+"]",i[o],n,s)}(s,t[s],e,n);return o.join("&")},x.fn.extend({serialize:function(){return x.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var t=x.prop(this,"elements");return t?x.makeArray(t):this})).filter((function(){var t=this.type;return this.name&&!x(this).is(":disabled")&&Ri.test(this.nodeName)&&!Wi.test(t)&&(this.checked||!_e.test(t))})).map((function(t,e){var i=x(this).val();return null==i?null:Array.isArray(i)?x.map(i,(function(t){return{name:e.name,value:t.replace(Ni,"\r\n")}})):{name:e.name,value:i.replace(Ni,"\r\n")}})).get()}});var Bi=/%20/g,Li=/#.*$/,zi=/([?&])_=[^&]*/,ji=/^(.*?):[ \t]*([^\r\n]*)$/gm,Gi=/^(?:GET|HEAD)$/,qi=/^\/\//,Ui={},$i={},Yi="*/".concat("*"),Ki=m.createElement("a");function Xi(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var s,o=0,r=e.toLowerCase().match(Gt)||[];if(i(n))for(;s=r[o++];)"+"===s[0]?(s=s.slice(1)||"*",(t[s]=t[s]||[]).unshift(n)):(t[s]=t[s]||[]).push(n)}}function Ji(t,e,i,n){var s={},o=t===$i;function r(a){var l;return s[a]=!0,x.each(t[a]||[],(function(t,a){return"string"!=typeof(a=a(e,i,n))||o||s[a]?o?!(l=a):void 0:(e.dataTypes.unshift(a),r(a),!1)})),l}return r(e.dataTypes[0])||!s["*"]&&r("*")}function Qi(t,e){var i,n,s=x.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((s[i]?t:n=n||{})[i]=e[i]);return n&&x.extend(!0,t,n),t}Ki.href=Pi.href,x.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Pi.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Pi.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Yi,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":x.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Qi(Qi(t,x.ajaxSettings),e):Qi(x.ajaxSettings,t)},ajaxPrefilter:Xi(Ui),ajaxTransport:Xi($i),ajax:function(e,i){"object"==typeof e&&(i=e,e=void 0);var n,s,o,r,a,l,h,c,u=x.ajaxSetup({},i=i||{}),d=u.context||u,p=u.context&&(d.nodeType||d.jquery)?x(d):x.event,f=x.Deferred(),g=x.Callbacks("once memory"),v=u.statusCode||{},_={},b={},y="canceled",w={readyState:0,getResponseHeader:function(t){var e;if(l){if(!r)for(r={};e=ji.exec(o);)r[e[1].toLowerCase()+" "]=(r[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=r[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return l?o:null},setRequestHeader:function(t,e){return null==l&&(t=b[t.toLowerCase()]=b[t.toLowerCase()]||t,_[t]=e),this},overrideMimeType:function(t){return null==l&&(u.mimeType=t),this},statusCode:function(t){if(t)if(l)w.always(t[w.status]);else for(var e in t)v[e]=[v[e],t[e]];return this},abort:function(t){return t=t||y,n&&n.abort(t),C(0,t),this}};if(f.promise(w),u.url=((e||u.url||Pi.href)+"").replace(qi,Pi.protocol+"//"),u.type=i.method||i.type||u.method||u.type,u.dataTypes=(u.dataType||"*").toLowerCase().match(Gt)||[""],null==u.crossDomain){e=m.createElement("a");try{e.href=u.url,e.href=e.href,u.crossDomain=Ki.protocol+"//"+Ki.host!=e.protocol+"//"+e.host}catch(e){u.crossDomain=!0}}if(u.data&&u.processData&&"string"!=typeof u.data&&(u.data=x.param(u.data,u.traditional)),Ji(Ui,u,i,w),!l){for(c in(h=x.event&&u.global)&&0==x.active++&&x.event.trigger("ajaxStart"),u.type=u.type.toUpperCase(),u.hasContent=!Gi.test(u.type),s=u.url.replace(Li,""),u.hasContent?u.data&&u.processData&&0===(u.contentType||"").indexOf("application/x-www-form-urlencoded")&&(u.data=u.data.replace(Bi,"+")):(e=u.url.slice(s.length),u.data&&(u.processData||"string"==typeof u.data)&&(s+=(Fi.test(s)?"&":"?")+u.data,delete u.data),!1===u.cache&&(s=s.replace(zi,"$1"),e=(Fi.test(s)?"&":"?")+"_="+Oi.guid+++e),u.url=s+e),u.ifModified&&(x.lastModified[s]&&w.setRequestHeader("If-Modified-Since",x.lastModified[s]),x.etag[s])&&w.setRequestHeader("If-None-Match",x.etag[s]),(u.data&&u.hasContent&&!1!==u.contentType||i.contentType)&&w.setRequestHeader("Content-Type",u.contentType),w.setRequestHeader("Accept",u.dataTypes[0]&&u.accepts[u.dataTypes[0]]?u.accepts[u.dataTypes[0]]+("*"!==u.dataTypes[0]?", "+Yi+"; q=0.01":""):u.accepts["*"]),u.headers)w.setRequestHeader(c,u.headers[c]);if(u.beforeSend&&(!1===u.beforeSend.call(d,w,u)||l))return w.abort();if(y="abort",g.add(u.complete),w.done(u.success),w.fail(u.error),n=Ji($i,u,i,w)){if(w.readyState=1,h&&p.trigger("ajaxSend",[w,u]),l)return w;u.async&&0<u.timeout&&(a=t.setTimeout((function(){w.abort("timeout")}),u.timeout));try{l=!1,n.send(_,C)}catch(e){if(l)throw e;C(-1,e)}}else C(-1,"No Transport")}return w;function C(e,i,r,c){var m,_,b,y=i;l||(l=!0,a&&t.clearTimeout(a),n=void 0,o=c||"",w.readyState=0<e?4:0,c=200<=e&&e<300||304===e,r&&(b=function(t,e,i){for(var n,s,o,r,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=t.mimeType||e.getResponseHeader("Content-Type"));if(n)for(s in a)if(a[s]&&a[s].test(n)){l.unshift(s);break}if(l[0]in i)o=l[0];else{for(s in i){if(!l[0]||t.converters[s+" "+l[0]]){o=s;break}r=r||s}o=o||r}if(o)return o!==l[0]&&l.unshift(o),i[o]}(u,w,r)),!c&&-1<x.inArray("script",u.dataTypes)&&x.inArray("json",u.dataTypes)<0&&(u.converters["text script"]=function(){}),b=function(t,e,i,n){var s,o,r,a,l,h={},c=t.dataTypes.slice();if(c[1])for(r in t.converters)h[r.toLowerCase()]=t.converters[r];for(o=c.shift();o;)if(t.responseFields[o]&&(i[t.responseFields[o]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(r=h[l+" "+o]||h["* "+o]))for(s in h)if((a=s.split(" "))[1]===o&&(r=h[l+" "+a[0]]||h["* "+a[0]])){!0===r?r=h[s]:!0!==h[s]&&(o=a[0],c.unshift(a[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+o}}}return{state:"success",data:e}}(u,b,w,c),c?(u.ifModified&&((r=w.getResponseHeader("Last-Modified"))&&(x.lastModified[s]=r),r=w.getResponseHeader("etag"))&&(x.etag[s]=r),204===e||"HEAD"===u.type?y="nocontent":304===e?y="notmodified":(y=b.state,m=b.data,c=!(_=b.error))):(_=y,!e&&y||(y="error",e<0&&(e=0))),w.status=e,w.statusText=(i||y)+"",c?f.resolveWith(d,[m,y,w]):f.rejectWith(d,[w,y,_]),w.statusCode(v),v=void 0,h&&p.trigger(c?"ajaxSuccess":"ajaxError",[w,u,c?m:_]),g.fireWith(d,[w,y]),h&&(p.trigger("ajaxComplete",[w,u]),--x.active||x.event.trigger("ajaxStop")))}},getJSON:function(t,e,i){return x.get(t,e,i,"json")},getScript:function(t,e){return x.get(t,void 0,e,"script")}}),x.each(["get","post"],(function(t,e){x[e]=function(t,n,s,o){return i(n)&&(o=o||s,s=n,n=void 0),x.ajax(x.extend({url:t,type:e,dataType:o,data:n,success:s},x.isPlainObject(t)&&t))}})),x.ajaxPrefilter((function(t){for(var e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")})),x._evalUrl=function(t,e,i){return x.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){x.globalEval(t,e,i)}})},x.fn.extend({wrapAll:function(t){return this[0]&&(i(t)&&(t=t.call(this[0])),t=x(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t})).append(this)),this},wrapInner:function(t){return i(t)?this.each((function(e){x(this).wrapInner(t.call(this,e))})):this.each((function(){var e=x(this),i=e.contents();i.length?i.wrapAll(t):e.append(t)}))},wrap:function(t){var e=i(t);return this.each((function(i){x(this).wrapAll(e?t.call(this,i):t)}))},unwrap:function(t){return this.parent(t).not("body").each((function(){x(this).replaceWith(this.childNodes)})),this}}),x.expr.pseudos.hidden=function(t){return!x.expr.pseudos.visible(t)},x.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},x.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var Zi={0:200,1223:204},tn=x.ajaxSettings.xhr(),en=(g.cors=!!tn&&"withCredentials"in tn,g.ajax=tn=!!tn,x.ajaxTransport((function(e){var i,n;if(g.cors||tn&&!e.crossDomain)return{send:function(s,o){var r,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)a[r]=e.xhrFields[r];for(r in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||s["X-Requested-With"]||(s["X-Requested-With"]="XMLHttpRequest"),s)a.setRequestHeader(r,s[r]);i=function(t){return function(){i&&(i=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Zi[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=i(),n=a.onerror=a.ontimeout=i("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&t.setTimeout((function(){i&&n()}))},i=i("abort");try{a.send(e.hasContent&&e.data||null)}catch(s){if(i)throw s}},abort:function(){i&&i()}}})),x.ajaxPrefilter((function(t){t.crossDomain&&(t.contents.script=!1)})),x.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return x.globalEval(t),t}}}),x.ajaxPrefilter("script",(function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")})),x.ajaxTransport("script",(function(t){var e,i;if(t.crossDomain||t.scriptAttrs)return{send:function(n,s){e=x("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",i=function(t){e.remove(),i=null,t&&s("error"===t.type?404:200,t.type)}),m.head.appendChild(e[0])},abort:function(){i&&i()}}})),[]),nn=/(=)\?(?=&|$)|\?\?/,sn=(x.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=en.pop()||x.expando+"_"+Oi.guid++;return this[t]=!0,t}}),x.ajaxPrefilter("json jsonp",(function(e,n,s){var o,r,a,l=!1!==e.jsonp&&(nn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(e.data)&&"data");if(l||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=i(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,l?e[l]=e[l].replace(nn,"$1"+o):!1!==e.jsonp&&(e.url+=(Fi.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return a||x.error(o+" was not called"),a[0]},e.dataTypes[0]="json",r=t[o],t[o]=function(){a=arguments},s.always((function(){void 0===r?x(t).removeProp(o):t[o]=r,e[o]&&(e.jsonpCallback=n.jsonpCallback,en.push(o)),a&&i(r)&&r(a[0]),a=r=void 0})),"script"})),g.createHTMLDocument=((y=m.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===y.childNodes.length),x.parseHTML=function(t,e,i){var n;return"string"!=typeof t?[]:("boolean"==typeof e&&(i=e,e=!1),e||(g.createHTMLDocument?((n=(e=m.implementation.createHTMLDocument("")).createElement("base")).href=m.location.href,e.head.appendChild(n)):e=m),n=!i&&[],(i=Nt.exec(t))?[e.createElement(i[1])]:(i=ke([t],e,n),n&&n.length&&x(n).remove(),x.merge([],i.childNodes)))},x.fn.load=function(t,e,n){var s,o,r,a=this,l=t.indexOf(" ");return-1<l&&(s=Di(t.slice(l)),t=t.slice(0,l)),i(e)?(n=e,e=void 0):e&&"object"==typeof e&&(o="POST"),0<a.length&&x.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done((function(t){r=arguments,a.html(s?x("<div>").append(x.parseHTML(t)).find(s):t)})).always(n&&function(t,e){a.each((function(){n.apply(this,r||[t.responseText,e,t])}))}),this},x.expr.pseudos.animated=function(t){return x.grep(x.timers,(function(e){return t===e.elem})).length},x.offset={setOffset:function(t,e,n){var s,o,r,a,l=x.css(t,"position"),h=x(t),c={};"static"===l&&(t.style.position="relative"),r=h.offset(),s=x.css(t,"top"),a=x.css(t,"left"),l=("absolute"===l||"fixed"===l)&&-1<(s+a).indexOf("auto")?(o=(l=h.position()).top,l.left):(o=parseFloat(s)||0,parseFloat(a)||0),null!=(e=i(e)?e.call(t,n,x.extend({},r)):e).top&&(c.top=e.top-r.top+o),null!=e.left&&(c.left=e.left-r.left+l),"using"in e?e.using.call(t,c):h.css(c)}},x.fn.extend({offset:function(t){var e,i;return arguments.length?void 0===t?this:this.each((function(e){x.offset.setOffset(this,t,e)})):(i=this[0])?i.getClientRects().length?(e=i.getBoundingClientRect(),i=i.ownerDocument.defaultView,{top:e.top+i.pageYOffset,left:e.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,i,n=this[0],s={top:0,left:0};if("fixed"===x.css(n,"position"))e=n.getBoundingClientRect();else{for(e=this.offset(),i=n.ownerDocument,t=n.offsetParent||i.documentElement;t&&(t===i.body||t===i.documentElement)&&"static"===x.css(t,"position");)t=t.parentNode;t&&t!==n&&1===t.nodeType&&((s=x(t).offset()).top+=x.css(t,"borderTopWidth",!0),s.left+=x.css(t,"borderLeftWidth",!0))}return{top:e.top-s.top-x.css(n,"marginTop",!0),left:e.left-s.left-x.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var t=this.offsetParent;t&&"static"===x.css(t,"position");)t=t.offsetParent;return t||de}))}}),x.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(t,e){var i="pageYOffset"===e;x.fn[t]=function(s){return Jt(this,(function(t,s,o){var r;if(n(t)?r=t:9===t.nodeType&&(r=t.defaultView),void 0===o)return r?r[e]:t[s];r?r.scrollTo(i?r.pageXOffset:o,i?o:r.pageYOffset):t[s]=o}),t,s,arguments.length)}})),x.each(["top","left"],(function(t,e){x.cssHooks[e]=ii(g.pixelPosition,(function(t,i){if(i)return i=ei(t,e),Xe.test(i)?x(t).position()[e]+"px":i}))})),x.each({Height:"height",Width:"width"},(function(t,e){x.each({padding:"inner"+t,content:e,"":"outer"+t},(function(i,s){x.fn[s]=function(o,r){var a=arguments.length&&(i||"boolean"!=typeof o),l=i||(!0===o||!0===r?"margin":"border");return Jt(this,(function(e,i,o){var r;return n(e)?0===s.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+t],r["scroll"+t],e.body["offset"+t],r["offset"+t],r["client"+t])):void 0===o?x.css(e,i,l):x.style(e,i,o,l)}),e,a?o:void 0,a)}}))})),x.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(t,e){x.fn[e]=function(t){return this.on(e,t)}})),x.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),x.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(t,e){x.fn[e]=function(t,i){return 0<arguments.length?this.on(e,null,t,i):this.trigger(e)}})),/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g),on=(x.proxy=function(t,e){var n,s;if("string"==typeof e&&(s=t[e],e=t,t=s),i(t))return n=r.call(arguments,2),(s=function(){return t.apply(e||this,n.concat(r.call(arguments)))}).guid=t.guid=t.guid||x.guid++,s},x.holdReady=function(t){t?x.readyWait++:x.ready(!0)},x.isArray=Array.isArray,x.parseJSON=JSON.parse,x.nodeName=S,x.isFunction=i,x.isWindow=n,x.camelCase=ee,x.type=b,x.now=Date.now,x.isNumeric=function(t){var e=x.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},x.trim=function(t){return null==t?"":(t+"").replace(sn,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],(function(){return x})),t.jQuery),rn=t.$;return x.noConflict=function(e){return t.$===x&&(t.$=rn),e&&t.jQuery===x&&(t.jQuery=on),x},void 0===e&&(t.jQuery=t.$=x),x})),function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}((function(t){"use strict";t.ui=t.ui||{},t.ui.version="1.13.3";var e,i,n,s,o,r,a,l,h,c,u=0,d=Array.prototype.hasOwnProperty,p=Array.prototype.slice;function f(t,e,i){return[parseFloat(t[0])*(h.test(t[0])?e/100:1),parseFloat(t[1])*(h.test(t[1])?i/100:1)]}function g(e,i){return parseInt(t.css(e,i),10)||0}function m(t){return null!=t&&t===t.window}t.cleanData=(e=t.cleanData,function(i){for(var n,s,o=0;null!=(s=i[o]);o++)(n=t._data(s,"events"))&&n.remove&&t(s).triggerHandler("remove");e(i)}),t.widget=function(e,i,n){var s,o,r,a={},l=e.split(".")[0],h=l+"-"+(e=e.split(".")[1]);return n||(n=i,i=t.Widget),Array.isArray(n)&&(n=t.extend.apply(null,[{}].concat(n))),t.expr.pseudos[h.toLowerCase()]=function(e){return!!t.data(e,h)},t[l]=t[l]||{},s=t[l][e],o=t[l][e]=function(t,e){if(!this||!this._createWidget)return new o(t,e);arguments.length&&this._createWidget(t,e)},t.extend(o,s,{version:n.version,_proto:t.extend({},n),_childConstructors:[]}),(r=new i).options=t.widget.extend({},r.options),t.each(n,(function(t,e){function n(){return i.prototype[t].apply(this,arguments)}function s(e){return i.prototype[t].apply(this,e)}a[t]="function"!=typeof e?e:function(){var t,i=this._super,o=this._superApply;return this._super=n,this._superApply=s,t=e.apply(this,arguments),this._super=i,this._superApply=o,t}})),o.prototype=t.widget.extend(r,{widgetEventPrefix:s&&r.widgetEventPrefix||e},a,{constructor:o,namespace:l,widgetName:e,widgetFullName:h}),s?(t.each(s._childConstructors,(function(e,i){var n=i.prototype;t.widget(n.namespace+"."+n.widgetName,o,i._proto)})),delete s._childConstructors):i._childConstructors.push(o),t.widget.bridge(e,o),o},t.widget.extend=function(e){for(var i,n,s=p.call(arguments,1),o=0,r=s.length;o<r;o++)for(i in s[o])n=s[o][i],d.call(s[o],i)&&void 0!==n&&(t.isPlainObject(n)?e[i]=t.isPlainObject(e[i])?t.widget.extend({},e[i],n):t.widget.extend({},n):e[i]=n);return e},t.widget.bridge=function(e,i){var n=i.prototype.widgetFullName||e;t.fn[e]=function(s){var o="string"==typeof s,r=p.call(arguments,1),a=this;return o?this.length||"instance"!==s?this.each((function(){var i,o=t.data(this,n);return"instance"===s?(a=o,!1):o?"function"!=typeof o[s]||"_"===s.charAt(0)?t.error("no such method '"+s+"' for "+e+" widget instance"):(i=o[s].apply(o,r))!==o&&void 0!==i?(a=i&&i.jquery?a.pushStack(i.get()):i,!1):void 0:t.error("cannot call methods on "+e+" prior to initialization; attempted to call method '"+s+"'")})):a=void 0:(r.length&&(s=t.widget.extend.apply(null,[s].concat(r))),this.each((function(){var e=t.data(this,n);e?(e.option(s||{}),e._init&&e._init()):t.data(this,n,new i(s,this))}))),a}},t.Widget=function(){},t.Widget._childConstructors=[],t.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{classes:{},disabled:!1,create:null},_createWidget:function(e,i){i=t(i||this.defaultElement||this)[0],this.element=t(i),this.uuid=u++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=t(),this.hoverable=t(),this.focusable=t(),this.classesElementLookup={},i!==this&&(t.data(i,this.widgetFullName,this),this._on(!0,this.element,{remove:function(t){t.target===i&&this.destroy()}}),this.document=t(i.style?i.ownerDocument:i.document||i),this.window=t(this.document[0].defaultView||this.document[0].parentWindow)),this.options=t.widget.extend({},this.options,this._getCreateOptions(),e),this._create(),this.options.disabled&&this._setOptionDisabled(this.options.disabled),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:function(){return{}},_getCreateEventData:t.noop,_create:t.noop,_init:t.noop,destroy:function(){var e=this;this._destroy(),t.each(this.classesElementLookup,(function(t,i){e._removeClass(i,t)})),this.element.off(this.eventNamespace).removeData(this.widgetFullName),this.widget().off(this.eventNamespace).removeAttr("aria-disabled"),this.bindings.off(this.eventNamespace)},_destroy:t.noop,widget:function(){return this.element},option:function(e,i){var n,s,o,r=e;if(0===arguments.length)return t.widget.extend({},this.options);if("string"==typeof e)if(r={},e=(n=e.split(".")).shift(),n.length){for(s=r[e]=t.widget.extend({},this.options[e]),o=0;o<n.length-1;o++)s[n[o]]=s[n[o]]||{},s=s[n[o]];if(e=n.pop(),1===arguments.length)return void 0===s[e]?null:s[e];s[e]=i}else{if(1===arguments.length)return void 0===this.options[e]?null:this.options[e];r[e]=i}return this._setOptions(r),this},_setOptions:function(t){for(var e in t)this._setOption(e,t[e]);return this},_setOption:function(t,e){return"classes"===t&&this._setOptionClasses(e),this.options[t]=e,"disabled"===t&&this._setOptionDisabled(e),this},_setOptionClasses:function(e){var i,n,s;for(i in e)s=this.classesElementLookup[i],e[i]!==this.options.classes[i]&&s&&s.length&&(n=t(s.get()),this._removeClass(s,i),n.addClass(this._classes({element:n,keys:i,classes:e,add:!0})))},_setOptionDisabled:function(t){this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!!t),t&&(this._removeClass(this.hoverable,null,"ui-state-hover"),this._removeClass(this.focusable,null,"ui-state-focus"))},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_classes:function(e){var i=[],n=this;function s(s,o){for(var r,a=0;a<s.length;a++)r=n.classesElementLookup[s[a]]||t(),r=e.add?(function(){var i=[];e.element.each((function(e,s){t.map(n.classesElementLookup,(function(t){return t})).some((function(t){return t.is(s)}))||i.push(s)})),n._on(t(i),{remove:"_untrackClassesElement"})}(),t(t.uniqueSort(r.get().concat(e.element.get())))):t(r.not(e.element).get()),n.classesElementLookup[s[a]]=r,i.push(s[a]),o&&e.classes[s[a]]&&i.push(e.classes[s[a]])}return(e=t.extend({element:this.element,classes:this.options.classes||{}},e)).keys&&s(e.keys.match(/\S+/g)||[],!0),e.extra&&s(e.extra.match(/\S+/g)||[]),i.join(" ")},_untrackClassesElement:function(e){var i=this;t.each(i.classesElementLookup,(function(n,s){-1!==t.inArray(e.target,s)&&(i.classesElementLookup[n]=t(s.not(e.target).get()))})),this._off(t(e.target))},_removeClass:function(t,e,i){return this._toggleClass(t,e,i,!1)},_addClass:function(t,e,i){return this._toggleClass(t,e,i,!0)},_toggleClass:function(t,e,i,n){var s="string"==typeof t||null===t;return(e={extra:s?e:i,keys:s?t:e,element:s?this.element:t,add:n="boolean"==typeof n?n:i}).element.toggleClass(this._classes(e),n),this},_on:function(e,i,n){var s,o=this;"boolean"!=typeof e&&(n=i,i=e,e=!1),n?(i=s=t(i),this.bindings=this.bindings.add(i)):(n=i,i=this.element,s=this.widget()),t.each(n,(function(n,r){function a(){if(e||!0!==o.options.disabled&&!t(this).hasClass("ui-state-disabled"))return("string"==typeof r?o[r]:r).apply(o,arguments)}"string"!=typeof r&&(a.guid=r.guid=r.guid||a.guid||t.guid++);var l=(n=n.match(/^([\w:-]*)\s*(.*)$/))[1]+o.eventNamespace;(n=n[2])?s.on(l,n,a):i.on(l,a)}))},_off:function(e,i){i=(i||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.off(i),this.bindings=t(this.bindings.not(e).get()),this.focusable=t(this.focusable.not(e).get()),this.hoverable=t(this.hoverable.not(e).get())},_delay:function(t,e){var i=this;return setTimeout((function(){return("string"==typeof t?i[t]:t).apply(i,arguments)}),e||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){this._addClass(t(e.currentTarget),null,"ui-state-hover")},mouseleave:function(e){this._removeClass(t(e.currentTarget),null,"ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){this._addClass(t(e.currentTarget),null,"ui-state-focus")},focusout:function(e){this._removeClass(t(e.currentTarget),null,"ui-state-focus")}})},_trigger:function(e,i,n){var s,o,r=this.options[e];if(n=n||{},(i=t.Event(i)).type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),i.target=this.element[0],o=i.originalEvent)for(s in o)s in i||(i[s]=o[s]);return this.element.trigger(i,n),!("function"==typeof r&&!1===r.apply(this.element[0],[i].concat(n))||i.isDefaultPrevented())}},t.each({show:"fadeIn",hide:"fadeOut"},(function(e,i){t.Widget.prototype["_"+e]=function(n,s,o){var r,a=(s="string"==typeof s?{effect:s}:s)?!0!==s&&"number"!=typeof s&&s.effect||i:e;"number"==typeof(s=s||{})?s={duration:s}:!0===s&&(s={}),r=!t.isEmptyObject(s),s.complete=o,s.delay&&n.delay(s.delay),r&&t.effects&&t.effects.effect[a]?n[e](s):a!==e&&n[a]?n[a](s.duration,s.easing,o):n.queue((function(i){t(this)[e](),o&&o.call(n[0]),i()}))}})),t.widget,n=Math.max,s=Math.abs,o=/left|center|right/,r=/top|center|bottom/,a=/[\+\-]\d+(\.[\d]+)?%?/,l=/^\w+/,h=/%$/,c=t.fn.position,t.position={scrollbarWidth:function(){var e,n,s;return void 0!==i?i:(s=(n=t("<div style='display:block;position:absolute;width:200px;height:200px;overflow:hidden;'><div style='height:300px;width:auto;'></div></div>")).children()[0],t("body").append(n),e=s.offsetWidth,n.css("overflow","scroll"),e===(s=s.offsetWidth)&&(s=n[0].clientWidth),n.remove(),i=e-s)},getScrollInfo:function(e){var i=e.isWindow||e.isDocument?"":e.element.css("overflow-x"),n=e.isWindow||e.isDocument?"":e.element.css("overflow-y");return i="scroll"===i||"auto"===i&&e.width<e.element[0].scrollWidth,{width:"scroll"===n||"auto"===n&&e.height<e.element[0].scrollHeight?t.position.scrollbarWidth():0,height:i?t.position.scrollbarWidth():0}},getWithinInfo:function(e){var i=t(e||window),n=m(i[0]),s=!!i[0]&&9===i[0].nodeType;return{element:i,isWindow:n,isDocument:s,offset:n||s?{left:0,top:0}:t(e).offset(),scrollLeft:i.scrollLeft(),scrollTop:i.scrollTop(),width:i.outerWidth(),height:i.outerHeight()}}},t.fn.position=function(e){var i,h,u,d,p,v,_,b,y,w,x,C;return e&&e.of?(v="string"==typeof(e=t.extend({},e)).of?t(document).find(e.of):t(e.of),_=t.position.getWithinInfo(e.within),b=t.position.getScrollInfo(_),y=(e.collision||"flip").split(" "),w={},C=9===(C=(x=v)[0]).nodeType?{width:x.width(),height:x.height(),offset:{top:0,left:0}}:m(C)?{width:x.width(),height:x.height(),offset:{top:x.scrollTop(),left:x.scrollLeft()}}:C.preventDefault?{width:0,height:0,offset:{top:C.pageY,left:C.pageX}}:{width:x.outerWidth(),height:x.outerHeight(),offset:x.offset()},v[0].preventDefault&&(e.at="left top"),h=C.width,u=C.height,p=t.extend({},d=C.offset),t.each(["my","at"],(function(){var t,i,n=(e[this]||"").split(" ");(n=1===n.length?o.test(n[0])?n.concat(["center"]):r.test(n[0])?["center"].concat(n):["center","center"]:n)[0]=o.test(n[0])?n[0]:"center",n[1]=r.test(n[1])?n[1]:"center",t=a.exec(n[0]),i=a.exec(n[1]),w[this]=[t?t[0]:0,i?i[0]:0],e[this]=[l.exec(n[0])[0],l.exec(n[1])[0]]})),1===y.length&&(y[1]=y[0]),"right"===e.at[0]?p.left+=h:"center"===e.at[0]&&(p.left+=h/2),"bottom"===e.at[1]?p.top+=u:"center"===e.at[1]&&(p.top+=u/2),i=f(w.at,h,u),p.left+=i[0],p.top+=i[1],this.each((function(){var o,r,a=t(this),l=a.outerWidth(),c=a.outerHeight(),m=g(this,"marginLeft"),x=g(this,"marginTop"),C=l+m+g(this,"marginRight")+b.width,S=c+x+g(this,"marginBottom")+b.height,k=t.extend({},p),E=f(w.my,a.outerWidth(),a.outerHeight());"right"===e.my[0]?k.left-=l:"center"===e.my[0]&&(k.left-=l/2),"bottom"===e.my[1]?k.top-=c:"center"===e.my[1]&&(k.top-=c/2),k.left+=E[0],k.top+=E[1],o={marginLeft:m,marginTop:x},t.each(["left","top"],(function(n,s){t.ui.position[y[n]]&&t.ui.position[y[n]][s](k,{targetWidth:h,targetHeight:u,elemWidth:l,elemHeight:c,collisionPosition:o,collisionWidth:C,collisionHeight:S,offset:[i[0]+E[0],i[1]+E[1]],my:e.my,at:e.at,within:_,elem:a})})),e.using&&(r=function(t){var i=d.left-k.left,o=i+h-l,r=d.top-k.top,p=r+u-c,f={target:{element:v,left:d.left,top:d.top,width:h,height:u},element:{element:a,left:k.left,top:k.top,width:l,height:c},horizontal:o<0?"left":0<i?"right":"center",vertical:p<0?"top":0<r?"bottom":"middle"};h<l&&s(i+o)<h&&(f.horizontal="center"),u<c&&s(r+p)<u&&(f.vertical="middle"),n(s(i),s(o))>n(s(r),s(p))?f.important="horizontal":f.important="vertical",e.using.call(this,t,f)}),a.offset(t.extend(k,{using:r}))}))):c.apply(this,arguments)},t.ui.position={fit:{left:function(t,e){var i,s=(o=e.within).isWindow?o.scrollLeft:o.offset.left,o=o.width,r=t.left-e.collisionPosition.marginLeft,a=s-r,l=r+e.collisionWidth-o-s;e.collisionWidth>o?0<a&&l<=0?(i=t.left+a+e.collisionWidth-o-s,t.left+=a-i):t.left=!(0<l&&a<=0)&&l<a?s+o-e.collisionWidth:s:0<a?t.left+=a:0<l?t.left-=l:t.left=n(t.left-r,t.left)},top:function(t,e){var i,s=(s=e.within).isWindow?s.scrollTop:s.offset.top,o=e.within.height,r=t.top-e.collisionPosition.marginTop,a=s-r,l=r+e.collisionHeight-o-s;e.collisionHeight>o?0<a&&l<=0?(i=t.top+a+e.collisionHeight-o-s,t.top+=a-i):t.top=!(0<l&&a<=0)&&l<a?s+o-e.collisionHeight:s:0<a?t.top+=a:0<l?t.top-=l:t.top=n(t.top-r,t.top)}},flip:{left:function(t,e){var i=(o=e.within).offset.left+o.scrollLeft,n=o.width,o=o.isWindow?o.scrollLeft:o.offset.left,r=(a=t.left-e.collisionPosition.marginLeft)-o,a=a+e.collisionWidth-n-o,l="left"===e.my[0]?-e.elemWidth:"right"===e.my[0]?e.elemWidth:0,h="left"===e.at[0]?e.targetWidth:"right"===e.at[0]?-e.targetWidth:0,c=-2*e.offset[0];r<0?((n=t.left+l+h+c+e.collisionWidth-n-i)<0||n<s(r))&&(t.left+=l+h+c):0<a&&(0<(i=t.left-e.collisionPosition.marginLeft+l+h+c-o)||s(i)<a)&&(t.left+=l+h+c)},top:function(t,e){var i=(o=e.within).offset.top+o.scrollTop,n=o.height,o=o.isWindow?o.scrollTop:o.offset.top,r=(a=t.top-e.collisionPosition.marginTop)-o,a=a+e.collisionHeight-n-o,l="top"===e.my[1]?-e.elemHeight:"bottom"===e.my[1]?e.elemHeight:0,h="top"===e.at[1]?e.targetHeight:"bottom"===e.at[1]?-e.targetHeight:0,c=-2*e.offset[1];r<0?((n=t.top+l+h+c+e.collisionHeight-n-i)<0||n<s(r))&&(t.top+=l+h+c):0<a&&(0<(i=t.top-e.collisionPosition.marginTop+l+h+c-o)||s(i)<a)&&(t.top+=l+h+c)}},flipfit:{left:function(){t.ui.position.flip.left.apply(this,arguments),t.ui.position.fit.left.apply(this,arguments)},top:function(){t.ui.position.flip.top.apply(this,arguments),t.ui.position.fit.top.apply(this,arguments)}}},t.ui.position,t.extend(t.expr.pseudos,{data:t.expr.createPseudo?t.expr.createPseudo((function(e){return function(i){return!!t.data(i,e)}})):function(e,i,n){return!!t.data(e,n[3])}}),t.fn.extend({disableSelection:(v="onselectstart"in document.createElement("div")?"selectstart":"mousedown",function(){return this.on(v+".ui-disableSelection",(function(t){t.preventDefault()}))}),enableSelection:function(){return this.off(".ui-disableSelection")}});var v,_=t,b={},y=b.toString,w=/^([\-+])=\s*(\d+\.?\d*)/,x=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[t[1],t[2],t[3],t[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[2.55*t[1],2.55*t[2],2.55*t[3],t[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?/,parse:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16),t[4]?(parseInt(t[4],16)/255).toFixed(2):1]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?/,parse:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16),t[4]?(parseInt(t[4]+t[4],16)/255).toFixed(2):1]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(t){return[t[1],t[2]/100,t[3]/100,t[4]]}}],C=_.Color=function(t,e,i,n){return new _.Color.fn.parse(t,e,i,n)},S={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},k={byte:{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},E=C.support={},D=_("<p>")[0],T=_.each;function M(t){return null==t?t+"":"object"==typeof t?b[y.call(t)]||"object":typeof t}function I(t,e,i){var n=k[e.type]||{};return null==t?i||!e.def?null:e.def:(t=n.floor?~~t:parseFloat(t),isNaN(t)?e.def:n.mod?(t+n.mod)%n.mod:Math.min(n.max,Math.max(0,t)))}function A(t){var e=C(),i=e._rgba=[];return t=t.toLowerCase(),T(x,(function(n,s){var o=(o=s.re.exec(t))&&s.parse(o);if(s=s.space||"rgba",o)return o=e[s](o),e[S[s].cache]=o[S[s].cache],i=e._rgba=o._rgba,!1})),i.length?("0,0,0,0"===i.join()&&_.extend(i,j.transparent),e):j[t]}function P(t,e,i){return 6*(i=(i+1)%1)<1?t+(e-t)*i*6:2*i<1?e:3*i<2?t+(e-t)*(2/3-i)*6:t}D.style.cssText="background-color:rgba(1,1,1,.5)",E.rgba=-1<D.style.backgroundColor.indexOf("rgba"),T(S,(function(t,e){e.cache="_"+t,e.props.alpha={idx:3,type:"percent",def:1}})),_.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){b["[object "+e+"]"]=e.toLowerCase()})),(C.fn=_.extend(C.prototype,{parse:function(t,e,i,n){if(void 0===t)return this._rgba=[null,null,null,null],this;(t.jquery||t.nodeType)&&(t=_(t).css(e),e=void 0);var s=this,o=M(t),r=this._rgba=[];return void 0!==e&&(t=[t,e,i,n],o="array"),"string"===o?this.parse(A(t)||j._default):"array"===o?(T(S.rgba.props,(function(e,i){r[i.idx]=I(t[i.idx],i)})),this):"object"===o?(T(S,t instanceof C?function(e,i){t[i.cache]&&(s[i.cache]=t[i.cache].slice())}:function(e,i){var n=i.cache;T(i.props,(function(e,o){if(!s[n]&&i.to){if("alpha"===e||null==t[e])return;s[n]=i.to(s._rgba)}s[n][o.idx]=I(t[e],o,!0)})),s[n]&&_.inArray(null,s[n].slice(0,3))<0&&(null==s[n][3]&&(s[n][3]=1),i.from)&&(s._rgba=i.from(s[n]))}),this):void 0},is:function(t){var e=C(t),i=!0,n=this;return T(S,(function(t,s){var o,r=e[s.cache];return r&&(o=n[s.cache]||s.to&&s.to(n._rgba)||[],T(s.props,(function(t,e){if(null!=r[e.idx])return i=r[e.idx]===o[e.idx]}))),i})),i},_space:function(){var t=[],e=this;return T(S,(function(i,n){e[n.cache]&&t.push(i)})),t.pop()},transition:function(t,e){t=(r=C(t))._space();var i=S[t],n=0===this.alpha()?C("transparent"):this,s=n[i.cache]||i.to(n._rgba),o=s.slice(),r=r[i.cache];return T(i.props,(function(t,i){var n=i.idx,a=s[n],l=r[n],h=k[i.type]||{};null!==l&&(null===a?o[n]=l:(h.mod&&(l-a>h.mod/2?a+=h.mod:a-l>h.mod/2&&(a-=h.mod)),o[n]=I((l-a)*e+a,i)))})),this[t](o)},blend:function(t){var e,i,n;return 1===this._rgba[3]?this:(e=this._rgba.slice(),i=e.pop(),n=C(t)._rgba,C(_.map(e,(function(t,e){return(1-i)*n[e]+i*t}))))},toRgbaString:function(){var t="rgba(",e=_.map(this._rgba,(function(t,e){return null!=t?t:2<e?1:0}));return 1===e[3]&&(e.pop(),t="rgb("),t+e.join()+")"},toHslaString:function(){var t="hsla(",e=_.map(this.hsla(),(function(t,e){return null==t&&(t=2<e?1:0),e&&e<3?Math.round(100*t)+"%":t}));return 1===e[3]&&(e.pop(),t="hsl("),t+e.join()+")"},toHexString:function(t){var e=this._rgba.slice(),i=e.pop();return t&&e.push(~~(255*i)),"#"+_.map(e,(function(t){return 1===(t=(t||0).toString(16)).length?"0"+t:t})).join("")},toString:function(){return 0===this._rgba[3]?"transparent":this.toRgbaString()}})).parse.prototype=C.fn,S.hsla.to=function(t){var e,i,n,s,o,r,a,l;return null==t[0]||null==t[1]||null==t[2]?[null,null,null,t[3]]:(e=t[0]/255,i=t[1]/255,n=t[2]/255,t=t[3],s=(l=Math.max(e,i,n))-(a=Math.min(e,i,n)),r=.5*(o=l+a),a=a===l?0:e===l?60*(i-n)/s+360:i===l?60*(n-e)/s+120:60*(e-i)/s+240,l=0==s?0:r<=.5?s/o:s/(2-o),[Math.round(a)%360,l,r,null==t?1:t])},S.hsla.from=function(t){var e,i,n;return null==t[0]||null==t[1]||null==t[2]?[null,null,null,t[3]]:(e=t[0]/360,n=t[1],i=t[2],t=t[3],n=2*i-(i=i<=.5?i*(1+n):i+n-i*n),[Math.round(255*P(n,i,e+1/3)),Math.round(255*P(n,i,e)),Math.round(255*P(n,i,e-1/3)),t])},T(S,(function(t,e){var i=e.props,n=e.cache,s=e.to,o=e.from;C.fn[t]=function(t){var e,r,a;return s&&!this[n]&&(this[n]=s(this._rgba)),void 0===t?this[n].slice():(e=M(t),r="array"===e||"object"===e?t:arguments,a=this[n].slice(),T(i,(function(t,i){null==(t=r["object"===e?t:i.idx])&&(t=a[i.idx]),a[i.idx]=I(t,i)})),o?((t=C(o(a)))[n]=a,t):C(a))},T(i,(function(e,i){C.fn[e]||(C.fn[e]=function(n){var s=M(n),o="alpha"===e?this._hsla?"hsla":"rgba":t,r=this[o](),a=r[i.idx];return"undefined"===s?a:("function"===s&&(s=M(n=n.call(this,a))),null==n&&i.empty?this:("string"===s&&(s=w.exec(n))&&(n=a+parseFloat(s[2])*("+"===s[1]?1:-1)),r[i.idx]=n,this[o](r)))})}))})),(C.hook=function(t){t=t.split(" "),T(t,(function(t,e){_.cssHooks[e]={set:function(t,i){var n,s,o="";if("transparent"!==i&&("string"!==M(i)||(n=A(i)))){if(i=C(n||i),!E.rgba&&1!==i._rgba[3]){for(s="backgroundColor"===e?t.parentNode:t;(""===o||"transparent"===o)&&s&&s.style;)try{o=_.css(s,"backgroundColor"),s=s.parentNode}catch(t){}i=i.blend(o&&"transparent"!==o?o:"_default")}i=i.toRgbaString()}try{t.style[e]=i}catch(t){}}},_.fx.step[e]=function(t){t.colorInit||(t.start=C(t.elem,e),t.end=C(t.end),t.colorInit=!0),_.cssHooks[e].set(t.elem,t.start.transition(t.end,t.pos))}}))})("backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor"),_.cssHooks.borderColor={expand:function(t){var e={};return T(["Top","Right","Bottom","Left"],(function(i,n){e["border"+n+"Color"]=t})),e}};var O,F,H,V,N,W,R,B,L,z,j=_.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"},G="ui-effects-",q="ui-effects-style",U="ui-effects-animated";function $(t){var e,i,n=t.ownerDocument.defaultView?t.ownerDocument.defaultView.getComputedStyle(t,null):t.currentStyle,s={};if(n&&n.length&&n[0]&&n[n[0]])for(i=n.length;i--;)"string"==typeof n[e=n[i]]&&(s[e.replace(/-([\da-z])/gi,(function(t,e){return e.toUpperCase()}))]=n[e]);else for(e in n)"string"==typeof n[e]&&(s[e]=n[e]);return s}function Y(e,i,n,s){return e={effect:e=t.isPlainObject(e)?(i=e).effect:e},"function"==typeof(i=null==i?{}:i)&&(s=i,n=null,i={}),"number"!=typeof i&&!t.fx.speeds[i]||(s=n,n=i,i={}),"function"==typeof n&&(s=n,n=null),i&&t.extend(e,i),n=n||i.duration,e.duration=t.fx.off?0:"number"==typeof n?n:n in t.fx.speeds?t.fx.speeds[n]:t.fx.speeds._default,e.complete=s||i.complete,e}function K(e){return!e||"number"==typeof e||t.fx.speeds[e]||"string"==typeof e&&!t.effects.effect[e]||"function"==typeof e||"object"==typeof e&&!e.effect}function X(t,e){var i=e.outerWidth();return e=e.outerHeight(),t=/^rect\((-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto)\)$/.exec(t)||["",0,i,e,0],{top:parseFloat(t[1])||0,right:"auto"===t[2]?i:parseFloat(t[2]),bottom:"auto"===t[3]?e:parseFloat(t[3]),left:parseFloat(t[4])||0}}t.effects={effect:{}},V=["add","remove","toggle"],N={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1},t.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],(function(e,i){t.fx.step[i]=function(t){("none"!==t.end&&!t.setAttr||1===t.pos&&!t.setAttr)&&(_.style(t.elem,i,t.end),t.setAttr=!0)}})),t.fn.addBack||(t.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),t.effects.animateClass=function(e,i,n,s){var o=t.speed(i,n,s);return this.queue((function(){var i=t(this),n=i.attr("class")||"",s=(s=o.children?i.find("*").addBack():i).map((function(){return{el:t(this),start:$(this)}})),r=function(){t.each(V,(function(t,n){e[n]&&i[n+"Class"](e[n])}))};r(),s=s.map((function(){return this.end=$(this.el[0]),this.diff=function(e,i){var n,s,o={};for(n in i)s=i[n],e[n]===s||N[n]||!t.fx.step[n]&&isNaN(parseFloat(s))||(o[n]=s);return o}(this.start,this.end),this})),i.attr("class",n),s=s.map((function(){var e=this,i=t.Deferred(),n=t.extend({},o,{queue:!1,complete:function(){i.resolve(e)}});return this.el.animate(this.diff,n),i.promise()})),t.when.apply(t,s.get()).done((function(){r(),t.each(arguments,(function(){var e=this.el;t.each(this.diff,(function(t){e.css(t,"")}))})),o.complete.call(i[0])}))}))},t.fn.extend({addClass:(H=t.fn.addClass,function(e,i,n,s){return i?t.effects.animateClass.call(this,{add:e},i,n,s):H.apply(this,arguments)}),removeClass:(F=t.fn.removeClass,function(e,i,n,s){return 1<arguments.length?t.effects.animateClass.call(this,{remove:e},i,n,s):F.apply(this,arguments)}),toggleClass:(O=t.fn.toggleClass,function(e,i,n,s,o){return"boolean"==typeof i||void 0===i?n?t.effects.animateClass.call(this,i?{add:e}:{remove:e},n,s,o):O.apply(this,arguments):t.effects.animateClass.call(this,{toggle:e},i,n,s)}),switchClass:function(e,i,n,s,o){return t.effects.animateClass.call(this,{add:i,remove:e},n,s,o)}}),t.expr&&t.expr.pseudos&&t.expr.pseudos.animated&&(t.expr.pseudos.animated=(W=t.expr.pseudos.animated,function(e){return!!t(e).data(U)||W(e)})),!1!==t.uiBackCompat&&t.extend(t.effects,{save:function(t,e){for(var i=0,n=e.length;i<n;i++)null!==e[i]&&t.data(G+e[i],t[0].style[e[i]])},restore:function(t,e){for(var i,n=0,s=e.length;n<s;n++)null!==e[n]&&(i=t.data(G+e[n]),t.css(e[n],i))},setMode:function(t,e){return"toggle"===e?t.is(":hidden")?"show":"hide":e},createWrapper:function(e){if(e.parent().is(".ui-effects-wrapper"))return e.parent();var i={width:e.outerWidth(!0),height:e.outerHeight(!0),float:e.css("float")},n=t("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),s={width:e.width(),height:e.height()},o=document.activeElement;try{o.id}catch(n){o=document.body}return e.wrap(n),e[0]!==o&&!t.contains(e[0],o)||t(o).trigger("focus"),n=e.parent(),"static"===e.css("position")?(n.css({position:"relative"}),e.css({position:"relative"})):(t.extend(i,{position:e.css("position"),zIndex:e.css("z-index")}),t.each(["top","left","bottom","right"],(function(t,n){i[n]=e.css(n),isNaN(parseInt(i[n],10))&&(i[n]="auto")})),e.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),e.css(s),n.css(i).show()},removeWrapper:function(e){var i=document.activeElement;return e.parent().is(".ui-effects-wrapper")&&(e.parent().replaceWith(e),e[0]!==i&&!t.contains(e[0],i)||t(i).trigger("focus")),e}}),t.extend(t.effects,{version:"1.13.3",define:function(e,i,n){return n||(n=i,i="effect"),t.effects.effect[e]=n,t.effects.effect[e].mode=i,n},scaledDimensions:function(t,e,i){var n;return 0===e?{height:0,width:0,outerHeight:0,outerWidth:0}:(n="horizontal"!==i?(e||100)/100:1,i="vertical"!==i?(e||100)/100:1,{height:t.height()*i,width:t.width()*n,outerHeight:t.outerHeight()*i,outerWidth:t.outerWidth()*n})},clipToBox:function(t){return{width:t.clip.right-t.clip.left,height:t.clip.bottom-t.clip.top,left:t.clip.left,top:t.clip.top}},unshift:function(t,e,i){var n=t.queue();1<e&&n.splice.apply(n,[1,0].concat(n.splice(e,i))),t.dequeue()},saveStyle:function(t){t.data(q,t[0].style.cssText)},restoreStyle:function(t){t[0].style.cssText=t.data(q)||"",t.removeData(q)},mode:function(t,e){return t=t.is(":hidden"),"toggle"===e&&(e=t?"show":"hide"),(t?"hide"===e:"show"===e)?"none":e},getBaseline:function(t,e){var i,n;switch(t[0]){case"top":i=0;break;case"middle":i=.5;break;case"bottom":i=1;break;default:i=t[0]/e.height}switch(t[1]){case"left":n=0;break;case"center":n=.5;break;case"right":n=1;break;default:n=t[1]/e.width}return{x:n,y:i}},createPlaceholder:function(e){var i,n=e.css("position"),s=e.position();return e.css({marginTop:e.css("marginTop"),marginBottom:e.css("marginBottom"),marginLeft:e.css("marginLeft"),marginRight:e.css("marginRight")}).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()),/^(static|relative)/.test(n)&&(n="absolute",i=t("<"+e[0].nodeName+">").insertAfter(e).css({display:/^(inline|ruby)/.test(e.css("display"))?"inline-block":"block",visibility:"hidden",marginTop:e.css("marginTop"),marginBottom:e.css("marginBottom"),marginLeft:e.css("marginLeft"),marginRight:e.css("marginRight"),float:e.css("float")}).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()).addClass("ui-effects-placeholder"),e.data(G+"placeholder",i)),e.css({position:n,left:s.left,top:s.top}),i},removePlaceholder:function(t){var e=G+"placeholder",i=t.data(e);i&&(i.remove(),t.removeData(e))},cleanUp:function(e){t.effects.restoreStyle(e),t.effects.removePlaceholder(e)},setTransition:function(e,i,n,s){return s=s||{},t.each(i,(function(t,i){var o=e.cssUnit(i);0<o[0]&&(s[i]=o[0]*n+o[1])})),s}}),t.fn.extend({effect:function(){function e(e){var i=t(this),n=t.effects.mode(i,l)||s;i.data(U,!0),h.push(n),s&&("show"===n||n===s&&"hide"===n)&&i.show(),s&&"none"===n||t.effects.saveStyle(i),"function"==typeof e&&e()}var i=Y.apply(this,arguments),n=t.effects.effect[i.effect],s=n.mode,o=i.queue,r=o||"fx",a=i.complete,l=i.mode,h=[];return t.fx.off||!n?l?this[l](i.duration,a):this.each((function(){a&&a.call(this)})):!1===o?this.each(e).each(c):this.queue(r,e).queue(r,c);function c(e){var o=t(this);function r(){"function"==typeof a&&a.call(o[0]),"function"==typeof e&&e()}i.mode=h.shift(),!1===t.uiBackCompat||s?"none"===i.mode?(o[l](),r()):n.call(o[0],i,(function(){o.removeData(U),t.effects.cleanUp(o),"hide"===i.mode&&o.hide(),r()})):(o.is(":hidden")?"hide"===l:"show"===l)?(o[l](),r()):n.call(o[0],i,r)}},show:(L=t.fn.show,function(t){return K(t)?L.apply(this,arguments):((t=Y.apply(this,arguments)).mode="show",this.effect.call(this,t))}),hide:(B=t.fn.hide,function(t){return K(t)?B.apply(this,arguments):((t=Y.apply(this,arguments)).mode="hide",this.effect.call(this,t))}),toggle:(R=t.fn.toggle,function(t){return K(t)||"boolean"==typeof t?R.apply(this,arguments):((t=Y.apply(this,arguments)).mode="toggle",this.effect.call(this,t))}),cssUnit:function(e){var i=this.css(e),n=[];return t.each(["em","px","%","pt"],(function(t,e){0<i.indexOf(e)&&(n=[parseFloat(i),e])})),n},cssClip:function(t){return t?this.css("clip","rect("+t.top+"px "+t.right+"px "+t.bottom+"px "+t.left+"px)"):X(this.css("clip"),this)},transfer:function(e,i){var n=t(this),s="fixed"===(l=t(e.to)).css("position"),o=t("body"),r=s?o.scrollTop():0,a=(o=s?o.scrollLeft():0,{top:(a=l.offset()).top-r,left:a.left-o,height:l.innerHeight(),width:l.innerWidth()}),l=n.offset(),h=t("<div class='ui-effects-transfer'></div>");h.appendTo("body").addClass(e.className).css({top:l.top-r,left:l.left-o,height:n.innerHeight(),width:n.innerWidth(),position:s?"fixed":"absolute"}).animate(a,e.duration,e.easing,(function(){h.remove(),"function"==typeof i&&i()}))}}),t.fx.step.clip=function(e){e.clipInit||(e.start=t(e.elem).cssClip(),"string"==typeof e.end&&(e.end=X(e.end,e.elem)),e.clipInit=!0),t(e.elem).cssClip({top:e.pos*(e.end.top-e.start.top)+e.start.top,right:e.pos*(e.end.right-e.start.right)+e.start.right,bottom:e.pos*(e.end.bottom-e.start.bottom)+e.start.bottom,left:e.pos*(e.end.left-e.start.left)+e.start.left})},z={},t.each(["Quad","Cubic","Quart","Quint","Expo"],(function(t,e){z[e]=function(e){return Math.pow(e,t+2)}})),t.extend(z,{Sine:function(t){return 1-Math.cos(t*Math.PI/2)},Circ:function(t){return 1-Math.sqrt(1-t*t)},Elastic:function(t){return 0===t||1===t?t:-Math.pow(2,8*(t-1))*Math.sin((80*(t-1)-7.5)*Math.PI/15)},Back:function(t){return t*t*(3*t-2)},Bounce:function(t){for(var e,i=4;t<((e=Math.pow(2,--i))-1)/11;);return 1/Math.pow(4,3-i)-7.5625*Math.pow((3*e-2)/22-t,2)}}),t.each(z,(function(e,i){t.easing["easeIn"+e]=i,t.easing["easeOut"+e]=function(t){return 1-i(1-t)},t.easing["easeInOut"+e]=function(t){return t<.5?i(2*t)/2:1-i(-2*t+2)/2}})),D=t.effects,t.effects.define("blind","hide",(function(e,i){var n={up:["bottom","top"],vertical:["bottom","top"],down:["top","bottom"],left:["right","left"],horizontal:["right","left"],right:["left","right"]},s=t(this),o=e.direction||"up",r=s.cssClip(),a={clip:t.extend({},r)},l=t.effects.createPlaceholder(s);a.clip[n[o][0]]=a.clip[n[o][1]],"show"===e.mode&&(s.cssClip(a.clip),l&&l.css(t.effects.clipToBox(a)),a.clip=r),l&&l.animate(t.effects.clipToBox(a),e.duration,e.easing),s.animate(a,{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("bounce",(function(e,i){var n,s,o=t(this),r="hide"===(a=e.mode),a="show"===a,l=e.direction||"up",h=e.distance,c=e.times||5,u=2*c+(a||r?1:0),d=e.duration/u,p=e.easing,f="up"===l||"down"===l?"top":"left",g="up"===l||"left"===l,m=0;for(e=o.queue().length,t.effects.createPlaceholder(o),l=o.css(f),h=h||o["top"==f?"outerHeight":"outerWidth"]()/3,a&&((s={opacity:1})[f]=l,o.css("opacity",0).css(f,g?2*-h:2*h).animate(s,d,p)),r&&(h/=Math.pow(2,c-1)),(s={})[f]=l;m<c;m++)(n={})[f]=(g?"-=":"+=")+h,o.animate(n,d,p).animate(s,d,p),h=r?2*h:h/2;r&&((n={opacity:0})[f]=(g?"-=":"+=")+h,o.animate(n,d,p)),o.queue(i),t.effects.unshift(o,e,1+u)})),t.effects.define("clip","hide",(function(e,i){var n={},s=t(this),o=(r="both"===(a=e.direction||"vertical"))||"horizontal"===a,r=r||"vertical"===a,a=s.cssClip();n.clip={top:r?(a.bottom-a.top)/2:a.top,right:o?(a.right-a.left)/2:a.right,bottom:r?(a.bottom-a.top)/2:a.bottom,left:o?(a.right-a.left)/2:a.left},t.effects.createPlaceholder(s),"show"===e.mode&&(s.cssClip(n.clip),n.clip=a),s.animate(n,{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("drop","hide",(function(e,i){var n,s,o=t(this),r="show"===e.mode,a="up"===(s=e.direction||"left")||"down"===s?"top":"left",l="+="==(s="up"===s||"left"===s?"-=":"+=")?"-=":"+=",h={opacity:0};t.effects.createPlaceholder(o),n=e.distance||o["top"==a?"outerHeight":"outerWidth"](!0)/2,h[a]=s+n,r&&(o.css(h),h[a]=l+n,h.opacity=1),o.animate(h,{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("explode","hide",(function(e,i){var n,s,o,r,a,l,h=e.pieces?Math.round(Math.sqrt(e.pieces)):3,c=h,u=t(this),d="show"===e.mode,p=u.show().css("visibility","hidden").offset(),f=Math.ceil(u.outerWidth()/c),g=Math.ceil(u.outerHeight()/h),m=[];function v(){m.push(this),m.length===h*c&&(u.css({visibility:"visible"}),t(m).remove(),i())}for(n=0;n<h;n++)for(r=p.top+n*g,l=n-(h-1)/2,s=0;s<c;s++)o=p.left+s*f,a=s-(c-1)/2,u.clone().appendTo("body").wrap("<div></div>").css({position:"absolute",visibility:"visible",left:-s*f,top:-n*g}).parent().addClass("ui-effects-explode").css({position:"absolute",overflow:"hidden",width:f,height:g,left:o+(d?a*f:0),top:r+(d?l*g:0),opacity:d?0:1}).animate({left:o+(d?0:a*f),top:r+(d?0:l*g),opacity:d?1:0},e.duration||500,e.easing,v)})),t.effects.define("fade","toggle",(function(e,i){var n="show"===e.mode;t(this).css("opacity",n?0:1).animate({opacity:n?1:0},{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("fold","hide",(function(e,i){var n=t(this),s="show"===(o=e.mode),o="hide"===o,r=e.size||15,a=/([0-9]+)%/.exec(r),l=e.horizFirst?["right","bottom"]:["bottom","right"],h=e.duration/2,c=t.effects.createPlaceholder(n),u=n.cssClip(),d={clip:t.extend({},u)},p={clip:t.extend({},u)},f=[u[l[0]],u[l[1]]],g=n.queue().length;a&&(r=parseInt(a[1],10)/100*f[o?0:1]),d.clip[l[0]]=r,p.clip[l[0]]=r,p.clip[l[1]]=0,s&&(n.cssClip(p.clip),c&&c.css(t.effects.clipToBox(p)),p.clip=u),n.queue((function(i){c&&c.animate(t.effects.clipToBox(d),h,e.easing).animate(t.effects.clipToBox(p),h,e.easing),i()})).animate(d,h,e.easing).animate(p,h,e.easing).queue(i),t.effects.unshift(n,g,4)})),t.effects.define("highlight","show",(function(e,i){var n=t(this),s={backgroundColor:n.css("backgroundColor")};"hide"===e.mode&&(s.opacity=0),t.effects.saveStyle(n),n.css({backgroundImage:"none",backgroundColor:e.color||"#ffff99"}).animate(s,{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),t.effects.define("size",(function(e,i){var n,s=t(this),o=["fontSize"],r=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],a=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],l=e.mode,h="effect"!==l,c=e.scale||"both",u=e.origin||["middle","center"],d=s.css("position"),p=s.position(),f=t.effects.scaledDimensions(s),g=e.from||f,m=e.to||t.effects.scaledDimensions(s,0);t.effects.createPlaceholder(s),"show"===l&&(l=g,g=m,m=l),n={from:{y:g.height/f.height,x:g.width/f.width},to:{y:m.height/f.height,x:m.width/f.width}},"box"!==c&&"both"!==c||(n.from.y!==n.to.y&&(g=t.effects.setTransition(s,r,n.from.y,g),m=t.effects.setTransition(s,r,n.to.y,m)),n.from.x!==n.to.x&&(g=t.effects.setTransition(s,a,n.from.x,g),m=t.effects.setTransition(s,a,n.to.x,m))),"content"!==c&&"both"!==c||n.from.y!==n.to.y&&(g=t.effects.setTransition(s,o,n.from.y,g),m=t.effects.setTransition(s,o,n.to.y,m)),u&&(l=t.effects.getBaseline(u,f),g.top=(f.outerHeight-g.outerHeight)*l.y+p.top,g.left=(f.outerWidth-g.outerWidth)*l.x+p.left,m.top=(f.outerHeight-m.outerHeight)*l.y+p.top,m.left=(f.outerWidth-m.outerWidth)*l.x+p.left),delete g.outerHeight,delete g.outerWidth,s.css(g),"content"!==c&&"both"!==c||(r=r.concat(["marginTop","marginBottom"]).concat(o),a=a.concat(["marginLeft","marginRight"]),s.find("*[width]").each((function(){var i=t(this),s={height:(o=t.effects.scaledDimensions(i)).height*n.from.y,width:o.width*n.from.x,outerHeight:o.outerHeight*n.from.y,outerWidth:o.outerWidth*n.from.x},o={height:o.height*n.to.y,width:o.width*n.to.x,outerHeight:o.height*n.to.y,outerWidth:o.width*n.to.x};n.from.y!==n.to.y&&(s=t.effects.setTransition(i,r,n.from.y,s),o=t.effects.setTransition(i,r,n.to.y,o)),n.from.x!==n.to.x&&(s=t.effects.setTransition(i,a,n.from.x,s),o=t.effects.setTransition(i,a,n.to.x,o)),h&&t.effects.saveStyle(i),i.css(s),i.animate(o,e.duration,e.easing,(function(){h&&t.effects.restoreStyle(i)}))}))),s.animate(m,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){var e=s.offset();0===m.opacity&&s.css("opacity",g.opacity),h||(s.css("position","static"===d?"relative":d).offset(e),t.effects.saveStyle(s)),i()}})})),t.effects.define("scale",(function(e,i){var n=t(this),s=e.mode;s=parseInt(e.percent,10)||(0===parseInt(e.percent,10)||"effect"!==s?0:100),n=t.extend(!0,{from:t.effects.scaledDimensions(n),to:t.effects.scaledDimensions(n,s,e.direction||"both"),origin:e.origin||["middle","center"]},e),e.fade&&(n.from.opacity=1,n.to.opacity=0),t.effects.effect.size.call(this,n,i)})),t.effects.define("puff","hide",(function(e,i){e=t.extend(!0,{},e,{fade:!0,percent:parseInt(e.percent,10)||150}),t.effects.effect.scale.call(this,e,i)})),t.effects.define("pulsate","show",(function(e,i){var n=t(this),s="show"===(h=e.mode),o=2*(e.times||5)+(s||"hide"===h?1:0),r=e.duration/o,a=0,l=1,h=n.queue().length;for(!s&&n.is(":visible")||(n.css("opacity",0).show(),a=1);l<o;l++)n.animate({opacity:a},r,e.easing),a=1-a;n.animate({opacity:a},r,e.easing),n.queue(i),t.effects.unshift(n,h,1+o)})),t.effects.define("shake",(function(e,i){var n=1,s=t(this),o=e.direction||"left",r=e.distance||20,a=e.times||3,l=2*a+1,h=Math.round(e.duration/l),c="up"===o||"down"===o?"top":"left",u=(o="up"===o||"left"===o,{}),d={},p={},f=s.queue().length;for(t.effects.createPlaceholder(s),u[c]=(o?"-=":"+=")+r,d[c]=(o?"+=":"-=")+2*r,p[c]=(o?"-=":"+=")+2*r,s.animate(u,h,e.easing);n<a;n++)s.animate(d,h,e.easing).animate(p,h,e.easing);s.animate(d,h,e.easing).animate(u,h/2,e.easing).queue(i),t.effects.unshift(s,f,1+l)})),t.effects.define("slide","show",(function(e,i){var n,s,o=t(this),r={up:["bottom","top"],down:["top","bottom"],left:["right","left"],right:["left","right"]},a=e.mode,l=e.direction||"left",h="up"===l||"down"===l?"top":"left",c="up"===l||"left"===l,u=e.distance||o["top"==h?"outerHeight":"outerWidth"](!0),d={};t.effects.createPlaceholder(o),n=o.cssClip(),s=o.position()[h],d[h]=(c?-1:1)*u+s,d.clip=o.cssClip(),d.clip[r[l][1]]=d.clip[r[l][0]],"show"===a&&(o.cssClip(d.clip),o.css(h,d[h]),d.clip=n,d[h]=s),o.animate(d,{queue:!1,duration:e.duration,easing:e.easing,complete:i})})),D=!1!==t.uiBackCompat?t.effects.define("transfer",(function(e,i){t(this).transfer(e,i)})):D,t.ui.focusable=function(e,i){var n,s,o,r=e.nodeName.toLowerCase();return"area"===r?(o=(n=e.parentNode).name,!(!e.href||!o||"map"!==n.nodeName.toLowerCase())&&0<(n=t("img[usemap='#"+o+"']")).length&&n.is(":visible")):(/^(input|select|textarea|button|object)$/.test(r)?(s=!e.disabled)&&(o=t(e).closest("fieldset")[0])&&(s=!o.disabled):s="a"===r&&e.href||i,s&&t(e).is(":visible")&&function(t){for(var e=t.css("visibility");"inherit"===e;)e=(t=t.parent()).css("visibility");return"visible"===e}(t(e)))},t.extend(t.expr.pseudos,{focusable:function(e){return t.ui.focusable(e,null!=t.attr(e,"tabindex"))}}),t.ui.focusable,t.fn._form=function(){return"string"==typeof this[0].form?this.closest("form"):t(this[0].form)},t.ui.formResetMixin={_formResetHandler:function(){var e=t(this);setTimeout((function(){var i=e.data("ui-form-reset-instances");t.each(i,(function(){this.refresh()}))}))},_bindFormResetHandler:function(){var t;this.form=this.element._form(),this.form.length&&((t=this.form.data("ui-form-reset-instances")||[]).length||this.form.on("reset.ui-form-reset",this._formResetHandler),t.push(this),this.form.data("ui-form-reset-instances",t))},_unbindFormResetHandler:function(){var e;this.form.length&&((e=this.form.data("ui-form-reset-instances")).splice(t.inArray(this,e),1),e.length?this.form.data("ui-form-reset-instances",e):this.form.removeData("ui-form-reset-instances").off("reset.ui-form-reset"))}},t.expr.pseudos||(t.expr.pseudos=t.expr[":"]),t.uniqueSort||(t.uniqueSort=t.unique),t.escapeSelector||(J=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g,Q=function(t,e){return e?"\0"===t?"ï¿½":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t},t.escapeSelector=function(t){return(t+"").replace(J,Q)}),t.fn.even&&t.fn.odd||t.fn.extend({even:function(){return this.filter((function(t){return t%2==0}))},odd:function(){return this.filter((function(t){return t%2==1}))}}),t.ui.keyCode={BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38},t.fn.labels=function(){var e,i,n;return this.length?this[0].labels&&this[0].labels.length?this.pushStack(this[0].labels):(i=this.eq(0).parents("label"),(e=this.attr("id"))&&(n=(n=this.eq(0).parents().last()).add((n.length?n:this).siblings()),e="label[for='"+t.escapeSelector(e)+"']",i=i.add(n.find(e).addBack(e))),this.pushStack(i)):this.pushStack([])},t.fn.scrollParent=function(e){var i=this.css("position"),n="absolute"===i,s=e?/(auto|scroll|hidden)/:/(auto|scroll)/;return e=this.parents().filter((function(){var e=t(this);return(!n||"static"!==e.css("position"))&&s.test(e.css("overflow")+e.css("overflow-y")+e.css("overflow-x"))})).eq(0),"fixed"!==i&&e.length?e:t(this[0].ownerDocument||document)},t.extend(t.expr.pseudos,{tabbable:function(e){var i=t.attr(e,"tabindex"),n=null!=i;return(!n||0<=i)&&t.ui.focusable(e,n)}}),t.fn.extend({uniqueId:(Z=0,function(){return this.each((function(){this.id||(this.id="ui-id-"+ ++Z)}))}),removeUniqueId:function(){return this.each((function(){/^ui-id-\d+$/.test(this.id)&&t(this).removeAttr("id")}))}}),t.widget("ui.accordion",{version:"1.13.3",options:{active:0,animate:{},classes:{"ui-accordion-header":"ui-corner-top","ui-accordion-header-collapsed":"ui-corner-all","ui-accordion-content":"ui-corner-bottom"},collapsible:!1,event:"click",header:function(t){return t.find("> li > :first-child").add(t.find("> :not(li)").even())},heightStyle:"auto",icons:{activeHeader:"ui-icon-triangle-1-s",header:"ui-icon-triangle-1-e"},activate:null,beforeActivate:null},hideProps:{borderTopWidth:"hide",borderBottomWidth:"hide",paddingTop:"hide",paddingBottom:"hide",height:"hide"},showProps:{borderTopWidth:"show",borderBottomWidth:"show",paddingTop:"show",paddingBottom:"show",height:"show"},_create:function(){var e=this.options;this.prevShow=this.prevHide=t(),this._addClass("ui-accordion","ui-widget ui-helper-reset"),this.element.attr("role","tablist"),e.collapsible||!1!==e.active&&null!=e.active||(e.active=0),this._processPanels(),e.active<0&&(e.active+=this.headers.length),this._refresh()},_getCreateEventData:function(){return{header:this.active,panel:this.active.length?this.active.next():t()}},_createIcons:function(){var e,i=this.options.icons;i&&(e=t("<span>"),this._addClass(e,"ui-accordion-header-icon","ui-icon "+i.header),e.prependTo(this.headers),e=this.active.children(".ui-accordion-header-icon"),this._removeClass(e,i.header)._addClass(e,null,i.activeHeader)._addClass(this.headers,"ui-accordion-icons"))},_destroyIcons:function(){this._removeClass(this.headers,"ui-accordion-icons"),this.headers.children(".ui-accordion-header-icon").remove()},_destroy:function(){var t;this.element.removeAttr("role"),this.headers.removeAttr("role aria-expanded aria-selected aria-controls tabIndex").removeUniqueId(),this._destroyIcons(),t=this.headers.next().css("display","").removeAttr("role aria-hidden aria-labelledby").removeUniqueId(),"content"!==this.options.heightStyle&&t.css("height","")},_setOption:function(t,e){"active"===t?this._activate(e):("event"===t&&(this.options.event&&this._off(this.headers,this.options.event),this._setupEvents(e)),this._super(t,e),"collapsible"!==t||e||!1!==this.options.active||this._activate(0),"icons"===t&&(this._destroyIcons(),e)&&this._createIcons())},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",t),this._toggleClass(null,"ui-state-disabled",!!t),this._toggleClass(this.headers.add(this.headers.next()),null,"ui-state-disabled",!!t)},_keydown:function(e){if(!e.altKey&&!e.ctrlKey){var i=t.ui.keyCode,n=this.headers.length,s=this.headers.index(e.target),o=!1;switch(e.keyCode){case i.RIGHT:case i.DOWN:o=this.headers[(s+1)%n];break;case i.LEFT:case i.UP:o=this.headers[(s-1+n)%n];break;case i.SPACE:case i.ENTER:this._eventHandler(e);break;case i.HOME:o=this.headers[0];break;case i.END:o=this.headers[n-1]}o&&(t(e.target).attr("tabIndex",-1),t(o).attr("tabIndex",0),t(o).trigger("focus"),e.preventDefault())}},_panelKeyDown:function(e){e.keyCode===t.ui.keyCode.UP&&e.ctrlKey&&t(e.currentTarget).prev().trigger("focus")},refresh:function(){var e=this.options;this._processPanels(),!1===e.active&&!0===e.collapsible||!this.headers.length?(e.active=!1,this.active=t()):!1===e.active?this._activate(0):this.active.length&&!t.contains(this.element[0],this.active[0])?this.headers.length===this.headers.find(".ui-state-disabled").length?(e.active=!1,this.active=t()):this._activate(Math.max(0,e.active-1)):e.active=this.headers.index(this.active),this._destroyIcons(),this._refresh()},_processPanels:function(){var t=this.headers,e=this.panels;"function"==typeof this.options.header?this.headers=this.options.header(this.element):this.headers=this.element.find(this.options.header),this._addClass(this.headers,"ui-accordion-header ui-accordion-header-collapsed","ui-state-default"),this.panels=this.headers.next().filter(":not(.ui-accordion-content-active)").hide(),this._addClass(this.panels,"ui-accordion-content","ui-helper-reset ui-widget-content"),e&&(this._off(t.not(this.headers)),this._off(e.not(this.panels)))},_refresh:function(){var e,i=this.options,n=i.heightStyle,s=this.element.parent();this.active=this._findActive(i.active),this._addClass(this.active,"ui-accordion-header-active","ui-state-active")._removeClass(this.active,"ui-accordion-header-collapsed"),this._addClass(this.active.next(),"ui-accordion-content-active"),this.active.next().show(),this.headers.attr("role","tab").each((function(){var e=t(this),i=e.uniqueId().attr("id"),n=e.next(),s=n.uniqueId().attr("id");e.attr("aria-controls",s),n.attr("aria-labelledby",i)})).next().attr("role","tabpanel"),this.headers.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}).next().attr({"aria-hidden":"true"}).hide(),this.active.length?this.active.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}).next().attr({"aria-hidden":"false"}):this.headers.eq(0).attr("tabIndex",0),this._createIcons(),this._setupEvents(i.event),"fill"===n?(e=s.height(),this.element.siblings(":visible").each((function(){var i=t(this),n=i.css("position");"absolute"!==n&&"fixed"!==n&&(e-=i.outerHeight(!0))})),this.headers.each((function(){e-=t(this).outerHeight(!0)})),this.headers.next().each((function(){t(this).height(Math.max(0,e-t(this).innerHeight()+t(this).height()))})).css("overflow","auto")):"auto"===n&&(e=0,this.headers.next().each((function(){var i=t(this).is(":visible");i||t(this).show(),e=Math.max(e,t(this).css("height","").height()),i||t(this).hide()})).height(e))},_activate:function(e){(e=this._findActive(e)[0])!==this.active[0]&&(e=e||this.active[0],this._eventHandler({target:e,currentTarget:e,preventDefault:t.noop}))},_findActive:function(e){return"number"==typeof e?this.headers.eq(e):t()},_setupEvents:function(e){var i={keydown:"_keydown"};e&&t.each(e.split(" "),(function(t,e){i[e]="_eventHandler"})),this._off(this.headers.add(this.headers.next())),this._on(this.headers,i),this._on(this.headers.next(),{keydown:"_panelKeyDown"}),this._hoverable(this.headers),this._focusable(this.headers)},_eventHandler:function(e){var i=this.options,n=this.active,s=t(e.currentTarget),o=s[0]===n[0],r=o&&i.collapsible,a=r?t():s.next(),l=n.next();l={oldHeader:n,oldPanel:l,newHeader:r?t():s,newPanel:a},e.preventDefault(),o&&!i.collapsible||!1===this._trigger("beforeActivate",e,l)||(i.active=!r&&this.headers.index(s),this.active=o?t():s,this._toggle(l),this._removeClass(n,"ui-accordion-header-active","ui-state-active"),i.icons&&(a=n.children(".ui-accordion-header-icon"),this._removeClass(a,null,i.icons.activeHeader)._addClass(a,null,i.icons.header)),o)||(this._removeClass(s,"ui-accordion-header-collapsed")._addClass(s,"ui-accordion-header-active","ui-state-active"),i.icons&&(e=s.children(".ui-accordion-header-icon"),this._removeClass(e,null,i.icons.header)._addClass(e,null,i.icons.activeHeader)),this._addClass(s.next(),"ui-accordion-content-active"))},_toggle:function(e){var i=e.newPanel,n=this.prevShow.length?this.prevShow:e.oldPanel;this.prevShow.add(this.prevHide).stop(!0,!0),this.prevShow=i,this.prevHide=n,this.options.animate?this._animate(i,n,e):(n.hide(),i.show(),this._toggleComplete(e)),n.attr({"aria-hidden":"true"}),n.prev().attr({"aria-selected":"false","aria-expanded":"false"}),i.length&&n.length?n.prev().attr({tabIndex:-1,"aria-expanded":"false"}):i.length&&this.headers.filter((function(){return 0===parseInt(t(this).attr("tabIndex"),10)})).attr("tabIndex",-1),i.attr("aria-hidden","false").prev().attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_animate:function(t,e,i){function n(){o._toggleComplete(i)}var s,o=this,r=0,a=t.css("box-sizing"),l=t.length&&(!e.length||t.index()<e.index()),h=this.options.animate||{},c=(c="string"==typeof(l=l&&h.down||h)?l:c)||l.easing||h.easing,u=(u="number"==typeof l?l:u)||l.duration||h.duration;return e.length?t.length?(s=t.show().outerHeight(),e.animate(this.hideProps,{duration:u,easing:c,step:function(t,e){e.now=Math.round(t)}}),void t.hide().animate(this.showProps,{duration:u,easing:c,complete:n,step:function(t,i){i.now=Math.round(t),"height"!==i.prop?"content-box"===a&&(r+=i.now):"content"!==o.options.heightStyle&&(i.now=Math.round(s-e.outerHeight()-r),r=0)}})):e.animate(this.hideProps,u,c,n):t.animate(this.showProps,u,c,n)},_toggleComplete:function(t){var e=t.oldPanel,i=e.prev();this._removeClass(e,"ui-accordion-content-active"),this._removeClass(i,"ui-accordion-header-active")._addClass(i,"ui-accordion-header-collapsed"),e.length&&(e.parent()[0].className=e.parent()[0].className),this._trigger("activate",null,t)}}),t.ui.safeActiveElement=function(t){var e;try{e=t.activeElement}catch(i){e=t.body}return(e=e||t.body).nodeName?e:t.body},t.widget("ui.menu",{version:"1.13.3",defaultElement:"<ul>",delay:300,options:{icons:{submenu:"ui-icon-caret-1-e"},items:"> *",menus:"ul",position:{my:"left top",at:"right top"},role:"menu",blur:null,focus:null,select:null},_create:function(){this.activeMenu=this.element,this.mouseHandled=!1,this.lastMousePosition={x:null,y:null},this.element.uniqueId().attr({role:this.options.role,tabIndex:0}),this._addClass("ui-menu","ui-widget ui-widget-content"),this._on({"mousedown .ui-menu-item":function(t){t.preventDefault(),this._activateItem(t)},"click .ui-menu-item":function(e){var i=t(e.target),n=t(t.ui.safeActiveElement(this.document[0]));!this.mouseHandled&&i.not(".ui-state-disabled").length&&(this.select(e),e.isPropagationStopped()||(this.mouseHandled=!0),i.has(".ui-menu").length?this.expand(e):!this.element.is(":focus")&&n.closest(".ui-menu").length&&(this.element.trigger("focus",[!0]),this.active)&&1===this.active.parents(".ui-menu").length&&clearTimeout(this.timer))},"mouseenter .ui-menu-item":"_activateItem","mousemove .ui-menu-item":"_activateItem",mouseleave:"collapseAll","mouseleave .ui-menu":"collapseAll",focus:function(t,e){var i=this.active||this._menuItems().first();e||this.focus(t,i)},blur:function(e){this._delay((function(){t.contains(this.element[0],t.ui.safeActiveElement(this.document[0]))||this.collapseAll(e)}))},keydown:"_keydown"}),this.refresh(),this._on(this.document,{click:function(t){this._closeOnDocumentClick(t)&&this.collapseAll(t,!0),this.mouseHandled=!1}})},_activateItem:function(e){var i,n;this.previousFilter||e.clientX===this.lastMousePosition.x&&e.clientY===this.lastMousePosition.y||(this.lastMousePosition={x:e.clientX,y:e.clientY},i=t(e.target).closest(".ui-menu-item"),n=t(e.currentTarget),i[0]!==n[0])||n.is(".ui-state-active")||(this._removeClass(n.siblings().children(".ui-state-active"),null,"ui-state-active"),this.focus(e,n))},_destroy:function(){var e=this.element.find(".ui-menu-item").removeAttr("role aria-disabled").children(".ui-menu-item-wrapper").removeUniqueId().removeAttr("tabIndex role aria-haspopup");this.element.removeAttr("aria-activedescendant").find(".ui-menu").addBack().removeAttr("role aria-labelledby aria-expanded aria-hidden aria-disabled tabIndex").removeUniqueId().show(),e.children().each((function(){var e=t(this);e.data("ui-menu-submenu-caret")&&e.remove()}))},_keydown:function(e){var i,n,s,o=!0;switch(e.keyCode){case t.ui.keyCode.PAGE_UP:this.previousPage(e);break;case t.ui.keyCode.PAGE_DOWN:this.nextPage(e);break;case t.ui.keyCode.HOME:this._move("first","first",e);break;case t.ui.keyCode.END:this._move("last","last",e);break;case t.ui.keyCode.UP:this.previous(e);break;case t.ui.keyCode.DOWN:this.next(e);break;case t.ui.keyCode.LEFT:this.collapse(e);break;case t.ui.keyCode.RIGHT:this.active&&!this.active.is(".ui-state-disabled")&&this.expand(e);break;case t.ui.keyCode.ENTER:case t.ui.keyCode.SPACE:this._activate(e);break;case t.ui.keyCode.ESCAPE:this.collapse(e);break;default:i=this.previousFilter||"",s=o=!1,n=96<=e.keyCode&&e.keyCode<=105?(e.keyCode-96).toString():String.fromCharCode(e.keyCode),clearTimeout(this.filterTimer),n===i?s=!0:n=i+n,i=this._filterMenuItems(n),(i=s&&-1!==i.index(this.active.next())?this.active.nextAll(".ui-menu-item"):i).length||(n=String.fromCharCode(e.keyCode),i=this._filterMenuItems(n)),i.length?(this.focus(e,i),this.previousFilter=n,this.filterTimer=this._delay((function(){delete this.previousFilter}),1e3)):delete this.previousFilter}o&&e.preventDefault()},_activate:function(t){this.active&&!this.active.is(".ui-state-disabled")&&(this.active.children("[aria-haspopup='true']").length?this.expand(t):this.select(t))},refresh:function(){var e,i,n=this,s=this.options.icons.submenu,o=this.element.find(this.options.menus);this._toggleClass("ui-menu-icons",null,!!this.element.find(".ui-icon").length),e=o.filter(":not(.ui-menu)").hide().attr({role:this.options.role,"aria-hidden":"true","aria-expanded":"false"}).each((function(){var e=t(this),i=e.prev(),o=t("<span>").data("ui-menu-submenu-caret",!0);n._addClass(o,"ui-menu-icon","ui-icon "+s),i.attr("aria-haspopup","true").prepend(o),e.attr("aria-labelledby",i.attr("id"))})),this._addClass(e,"ui-menu","ui-widget ui-widget-content ui-front"),(e=o.add(this.element).find(this.options.items)).not(".ui-menu-item").each((function(){var e=t(this);n._isDivider(e)&&n._addClass(e,"ui-menu-divider","ui-widget-content")})),i=(o=e.not(".ui-menu-item, .ui-menu-divider")).children().not(".ui-menu").uniqueId().attr({tabIndex:-1,role:this._itemRole()}),this._addClass(o,"ui-menu-item")._addClass(i,"ui-menu-item-wrapper"),e.filter(".ui-state-disabled").attr("aria-disabled","true"),this.active&&!t.contains(this.element[0],this.active[0])&&this.blur()},_itemRole:function(){return{menu:"menuitem",listbox:"option"}[this.options.role]},_setOption:function(t,e){var i;"icons"===t&&(i=this.element.find(".ui-menu-icon"),this._removeClass(i,null,this.options.icons.submenu)._addClass(i,null,e.submenu)),this._super(t,e)},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",String(t)),this._toggleClass(null,"ui-state-disabled",!!t)},focus:function(t,e){var i;this.blur(t,t&&"focus"===t.type),this._scrollIntoView(e),this.active=e.first(),i=this.active.children(".ui-menu-item-wrapper"),this._addClass(i,null,"ui-state-active"),this.options.role&&this.element.attr("aria-activedescendant",i.attr("id")),i=this.active.parent().closest(".ui-menu-item").children(".ui-menu-item-wrapper"),this._addClass(i,null,"ui-state-active"),t&&"keydown"===t.type?this._close():this.timer=this._delay((function(){this._close()}),this.delay),(i=e.children(".ui-menu")).length&&t&&/^mouse/.test(t.type)&&this._startOpening(i),this.activeMenu=e.parent(),this._trigger("focus",t,{item:e})},_scrollIntoView:function(e){var i,n,s;this._hasScroll()&&(i=parseFloat(t.css(this.activeMenu[0],"borderTopWidth"))||0,n=parseFloat(t.css(this.activeMenu[0],"paddingTop"))||0,i=e.offset().top-this.activeMenu.offset().top-i-n,n=this.activeMenu.scrollTop(),s=this.activeMenu.height(),e=e.outerHeight(),i<0?this.activeMenu.scrollTop(n+i):s<i+e&&this.activeMenu.scrollTop(n+i-s+e))},blur:function(t,e){e||clearTimeout(this.timer),this.active&&(this._removeClass(this.active.children(".ui-menu-item-wrapper"),null,"ui-state-active"),this._trigger("blur",t,{item:this.active}),this.active=null)},_startOpening:function(t){clearTimeout(this.timer),"true"===t.attr("aria-hidden")&&(this.timer=this._delay((function(){this._close(),this._open(t)}),this.delay))},_open:function(e){var i=t.extend({of:this.active},this.options.position);clearTimeout(this.timer),this.element.find(".ui-menu").not(e.parents(".ui-menu")).hide().attr("aria-hidden","true"),e.show().removeAttr("aria-hidden").attr("aria-expanded","true").position(i)},collapseAll:function(e,i){clearTimeout(this.timer),this.timer=this._delay((function(){var n=i?this.element:t(e&&e.target).closest(this.element.find(".ui-menu"));n.length||(n=this.element),this._close(n),this.blur(e),this._removeClass(n.find(".ui-state-active"),null,"ui-state-active"),this.activeMenu=n}),i?0:this.delay)},_close:function(t){(t=t||(this.active?this.active.parent():this.element)).find(".ui-menu").hide().attr("aria-hidden","true").attr("aria-expanded","false")},_closeOnDocumentClick:function(e){return!t(e.target).closest(".ui-menu").length},_isDivider:function(t){return!/[^\-\u2014\u2013\s]/.test(t.text())},collapse:function(t){var e=this.active&&this.active.parent().closest(".ui-menu-item",this.element);e&&e.length&&(this._close(),this.focus(t,e))},expand:function(t){var e=this.active&&this._menuItems(this.active.children(".ui-menu")).first();e&&e.length&&(this._open(e.parent()),this._delay((function(){this.focus(t,e)})))},next:function(t){this._move("next","first",t)},previous:function(t){this._move("prev","last",t)},isFirstItem:function(){return this.active&&!this.active.prevAll(".ui-menu-item").length},isLastItem:function(){return this.active&&!this.active.nextAll(".ui-menu-item").length},_menuItems:function(t){return(t||this.element).find(this.options.items).filter(".ui-menu-item")},_move:function(t,e,i){var n;(n=this.active?"first"===t||"last"===t?this.active["first"===t?"prevAll":"nextAll"](".ui-menu-item").last():this.active[t+"All"](".ui-menu-item").first():n)&&n.length&&this.active||(n=this._menuItems(this.activeMenu)[e]()),this.focus(i,n)},nextPage:function(e){var i,n,s;this.active?this.isLastItem()||(this._hasScroll()?(n=this.active.offset().top,s=this.element.innerHeight(),0===t.fn.jquery.indexOf("3.2.")&&(s+=this.element[0].offsetHeight-this.element.outerHeight()),this.active.nextAll(".ui-menu-item").each((function(){return(i=t(this)).offset().top-n-s<0})),this.focus(e,i)):this.focus(e,this._menuItems(this.activeMenu)[this.active?"last":"first"]())):this.next(e)},previousPage:function(e){var i,n,s;this.active?this.isFirstItem()||(this._hasScroll()?(n=this.active.offset().top,s=this.element.innerHeight(),0===t.fn.jquery.indexOf("3.2.")&&(s+=this.element[0].offsetHeight-this.element.outerHeight()),this.active.prevAll(".ui-menu-item").each((function(){return 0<(i=t(this)).offset().top-n+s})),this.focus(e,i)):this.focus(e,this._menuItems(this.activeMenu).first())):this.next(e)},_hasScroll:function(){return this.element.outerHeight()<this.element.prop("scrollHeight")},select:function(e){this.active=this.active||t(e.target).closest(".ui-menu-item");var i={item:this.active};this.active.has(".ui-menu").length||this.collapseAll(e,!0),this._trigger("select",e,i)},_filterMenuItems:function(e){e=e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&");var i=new RegExp("^"+e,"i");return this.activeMenu.find(this.options.items).filter(".ui-menu-item").filter((function(){return i.test(String.prototype.trim.call(t(this).children(".ui-menu-item-wrapper").text()))}))}}),t.widget("ui.autocomplete",{version:"1.13.3",defaultElement:"<input>",options:{appendTo:null,autoFocus:!1,delay:300,minLength:1,position:{my:"left top",at:"left bottom",collision:"none"},source:null,change:null,close:null,focus:null,open:null,response:null,search:null,select:null},requestIndex:0,pending:0,liveRegionTimer:null,_create:function(){var e,i,n,s="textarea"===(o=this.element[0].nodeName.toLowerCase()),o="input"===o;this.isMultiLine=s||!o&&this._isContentEditable(this.element),this.valueMethod=this.element[s||o?"val":"text"],this.isNewMenu=!0,this._addClass("ui-autocomplete-input"),this.element.attr("autocomplete","off"),this._on(this.element,{keydown:function(s){if(this.element.prop("readOnly"))i=n=e=!0;else{i=n=e=!1;var o=t.ui.keyCode;switch(s.keyCode){case o.PAGE_UP:e=!0,this._move("previousPage",s);break;case o.PAGE_DOWN:e=!0,this._move("nextPage",s);break;case o.UP:e=!0,this._keyEvent("previous",s);break;case o.DOWN:e=!0,this._keyEvent("next",s);break;case o.ENTER:this.menu.active&&(e=!0,s.preventDefault(),this.menu.select(s));break;case o.TAB:this.menu.active&&this.menu.select(s);break;case o.ESCAPE:this.menu.element.is(":visible")&&(this.isMultiLine||this._value(this.term),this.close(s),s.preventDefault());break;default:i=!0,this._searchTimeout(s)}}},keypress:function(n){if(e)e=!1,this.isMultiLine&&!this.menu.element.is(":visible")||n.preventDefault();else if(!i){var s=t.ui.keyCode;switch(n.keyCode){case s.PAGE_UP:this._move("previousPage",n);break;case s.PAGE_DOWN:this._move("nextPage",n);break;case s.UP:this._keyEvent("previous",n);break;case s.DOWN:this._keyEvent("next",n)}}},input:function(t){n?(n=!1,t.preventDefault()):this._searchTimeout(t)},focus:function(){this.selectedItem=null,this.previous=this._value()},blur:function(t){clearTimeout(this.searching),this.close(t),this._change(t)}}),this._initSource(),this.menu=t("<ul>").appendTo(this._appendTo()).menu({role:null}).hide().attr({unselectable:"on"}).menu("instance"),this._addClass(this.menu.element,"ui-autocomplete","ui-front"),this._on(this.menu.element,{mousedown:function(t){t.preventDefault()},menufocus:function(e,i){var n,s;this.isNewMenu&&(this.isNewMenu=!1,e.originalEvent)&&/^mouse/.test(e.originalEvent.type)?(this.menu.blur(),this.document.one("mousemove",(function(){t(e.target).trigger(e.originalEvent)}))):(s=i.item.data("ui-autocomplete-item"),!1!==this._trigger("focus",e,{item:s})&&e.originalEvent&&/^key/.test(e.originalEvent.type)&&this._value(s.value),(n=i.item.attr("aria-label")||s.value)&&String.prototype.trim.call(n).length&&(clearTimeout(this.liveRegionTimer),this.liveRegionTimer=this._delay((function(){this.liveRegion.html(t("<div>").text(n))}),100)))},menuselect:function(e,i){var n=i.item.data("ui-autocomplete-item"),s=this.previous;this.element[0]!==t.ui.safeActiveElement(this.document[0])&&(this.element.trigger("focus"),this.previous=s,this._delay((function(){this.previous=s,this.selectedItem=n}))),!1!==this._trigger("select",e,{item:n})&&this._value(n.value),this.term=this._value(),this.close(e),this.selectedItem=n}}),this.liveRegion=t("<div>",{role:"status","aria-live":"assertive","aria-relevant":"additions"}).appendTo(this.document[0].body),this._addClass(this.liveRegion,null,"ui-helper-hidden-accessible"),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_destroy:function(){clearTimeout(this.searching),this.element.removeAttr("autocomplete"),this.menu.element.remove(),this.liveRegion.remove()},_setOption:function(t,e){this._super(t,e),"source"===t&&this._initSource(),"appendTo"===t&&this.menu.element.appendTo(this._appendTo()),"disabled"===t&&e&&this.xhr&&this.xhr.abort()},_isEventTargetInWidget:function(e){var i=this.menu.element[0];return e.target===this.element[0]||e.target===i||t.contains(i,e.target)},_closeOnClickOutside:function(t){this._isEventTargetInWidget(t)||this.close()},_appendTo:function(){var e=this.options.appendTo;return(e=(e=e&&(e.jquery||e.nodeType?t(e):this.document.find(e).eq(0)))&&e[0]?e:this.element.closest(".ui-front, dialog")).length?e:this.document[0].body},_initSource:function(){var e,i,n=this;Array.isArray(this.options.source)?(e=this.options.source,this.source=function(i,n){n(t.ui.autocomplete.filter(e,i.term))}):"string"==typeof this.options.source?(i=this.options.source,this.source=function(e,s){n.xhr&&n.xhr.abort(),n.xhr=t.ajax({url:i,data:e,dataType:"json",success:function(t){s(t)},error:function(){s([])}})}):this.source=this.options.source},_searchTimeout:function(t){clearTimeout(this.searching),this.searching=this._delay((function(){var e=this.term===this._value(),i=this.menu.element.is(":visible"),n=t.altKey||t.ctrlKey||t.metaKey||t.shiftKey;e&&(i||n)||(this.selectedItem=null,this.search(null,t))}),this.options.delay)},search:function(t,e){return t=null!=t?t:this._value(),this.term=this._value(),t.length<this.options.minLength?this.close(e):!1!==this._trigger("search",e)?this._search(t):void 0},_search:function(t){this.pending++,this._addClass("ui-autocomplete-loading"),this.cancelSearch=!1,this.source({term:t},this._response())},_response:function(){var t=++this.requestIndex;return function(e){t===this.requestIndex&&this.__response(e),this.pending--,this.pending||this._removeClass("ui-autocomplete-loading")}.bind(this)},__response:function(t){t=t&&this._normalize(t),this._trigger("response",null,{content:t}),!this.options.disabled&&t&&t.length&&!this.cancelSearch?(this._suggest(t),this._trigger("open")):this._close()},close:function(t){this.cancelSearch=!0,this._close(t)},_close:function(t){this._off(this.document,"mousedown"),this.menu.element.is(":visible")&&(this.menu.element.hide(),this.menu.blur(),this.isNewMenu=!0,this._trigger("close",t))},_change:function(t){this.previous!==this._value()&&this._trigger("change",t,{item:this.selectedItem})},_normalize:function(e){return e.length&&e[0].label&&e[0].value?e:t.map(e,(function(e){return"string"==typeof e?{label:e,value:e}:t.extend({},e,{label:e.label||e.value,value:e.value||e.label})}))},_suggest:function(e){var i=this.menu.element.empty();this._renderMenu(i,e),this.isNewMenu=!0,this.menu.refresh(),i.show(),this._resizeMenu(),i.position(t.extend({of:this.element},this.options.position)),this.options.autoFocus&&this.menu.next(),this._on(this.document,{mousedown:"_closeOnClickOutside"})},_resizeMenu:function(){var t=this.menu.element;t.outerWidth(Math.max(t.width("").outerWidth()+1,this.element.outerWidth()))},_renderMenu:function(e,i){var n=this;t.each(i,(function(t,i){n._renderItemData(e,i)}))},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-autocomplete-item",e)},_renderItem:function(e,i){return t("<li>").append(t("<div>").text(i.label)).appendTo(e)},_move:function(t,e){this.menu.element.is(":visible")?this.menu.isFirstItem()&&/^previous/.test(t)||this.menu.isLastItem()&&/^next/.test(t)?(this.isMultiLine||this._value(this.term),this.menu.blur()):this.menu[t](e):this.search(null,e)},widget:function(){return this.menu.element},_value:function(){return this.valueMethod.apply(this.element,arguments)},_keyEvent:function(t,e){this.isMultiLine&&!this.menu.element.is(":visible")||(this._move(t,e),e.preventDefault())},_isContentEditable:function(t){var e;return!!t.length&&("inherit"===(e=t.prop("contentEditable"))?this._isContentEditable(t.parent()):"true"===e)}}),t.extend(t.ui.autocomplete,{escapeRegex:function(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")},filter:function(e,i){var n=new RegExp(t.ui.autocomplete.escapeRegex(i),"i");return t.grep(e,(function(t){return n.test(t.label||t.value||t)}))}}),t.widget("ui.autocomplete",t.ui.autocomplete,{options:{messages:{noResults:"No search results.",results:function(t){return t+(1<t?" results are":" result is")+" available, use up and down arrow keys to navigate."}}},__response:function(e){var i;this._superApply(arguments),this.options.disabled||this.cancelSearch||(i=e&&e.length?this.options.messages.results(e.length):this.options.messages.noResults,clearTimeout(this.liveRegionTimer),this.liveRegionTimer=this._delay((function(){this.liveRegion.html(t("<div>").text(i))}),100))}}),t.ui.autocomplete;var J,Q,Z,tt,et,it=/ui-corner-([a-z]){2,6}/g;function nt(){this._curInst=null,this._keyEvent=!1,this._disabledInputs=[],this._datepickerShowing=!1,this._inDialog=!1,this._mainDivId="ui-datepicker-div",this._inlineClass="ui-datepicker-inline",this._appendClass="ui-datepicker-append",this._triggerClass="ui-datepicker-trigger",this._dialogClass="ui-datepicker-dialog",this._disableClass="ui-datepicker-disabled",this._unselectableClass="ui-datepicker-unselectable",this._currentClass="ui-datepicker-current-day",this._dayOverClass="ui-datepicker-days-cell-over",this.regional=[],this.regional[""]={closeText:"Done",prevText:"Prev",nextText:"Next",currentText:"Today",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],weekHeader:"Wk",dateFormat:"mm/dd/yy",firstDay:0,isRTL:!1,showMonthAfterYear:!1,yearSuffix:"",selectMonthLabel:"Select month",selectYearLabel:"Select year"},this._defaults={showOn:"focus",showAnim:"fadeIn",showOptions:{},defaultDate:null,appendText:"",buttonText:"...",buttonImage:"",buttonImageOnly:!1,hideIfNoPrevNext:!1,navigationAsDateFormat:!1,gotoCurrent:!1,changeMonth:!1,changeYear:!1,yearRange:"c-10:c+10",showOtherMonths:!1,selectOtherMonths:!1,showWeek:!1,calculateWeek:this.iso8601Week,shortYearCutoff:"+10",minDate:null,maxDate:null,duration:"fast",beforeShowDay:null,beforeShow:null,onSelect:null,onChangeMonthYear:null,onClose:null,onUpdateDatepicker:null,numberOfMonths:1,showCurrentAtPos:0,stepMonths:1,stepBigMonths:12,altField:"",altFormat:"",constrainInput:!0,showButtonPanel:!1,autoSize:!1,disabled:!1},t.extend(this._defaults,this.regional[""]),this.regional.en=t.extend(!0,{},this.regional[""]),this.regional["en-US"]=t.extend(!0,{},this.regional.en),this.dpDiv=st(t("<div id='"+this._mainDivId+"' class='ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>"))}function st(e){var i="button, .ui-datepicker-prev, .ui-datepicker-next, .ui-datepicker-calendar td a";return e.on("mouseout",i,(function(){t(this).removeClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&t(this).removeClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&t(this).removeClass("ui-datepicker-next-hover")})).on("mouseover",i,ot)}function ot(){t.datepicker._isDisabledDatepicker((et.inline?et.dpDiv.parent():et.input)[0])||(t(this).parents(".ui-datepicker-calendar").find("a").removeClass("ui-state-hover"),t(this).addClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&t(this).addClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&t(this).addClass("ui-datepicker-next-hover"))}function rt(e,i){for(var n in t.extend(e,i),i)null==i[n]&&(e[n]=i[n])}t.widget("ui.controlgroup",{version:"1.13.3",defaultElement:"<div>",options:{direction:"horizontal",disabled:null,onlyVisible:!0,items:{button:"input[type=button], input[type=submit], input[type=reset], button, a",controlgroupLabel:".ui-controlgroup-label",checkboxradio:"input[type='checkbox'], input[type='radio']",selectmenu:"select",spinner:".ui-spinner-input"}},_create:function(){this._enhance()},_enhance:function(){this.element.attr("role","toolbar"),this.refresh()},_destroy:function(){this._callChildMethod("destroy"),this.childWidgets.removeData("ui-controlgroup-data"),this.element.removeAttr("role"),this.options.items.controlgroupLabel&&this.element.find(this.options.items.controlgroupLabel).find(".ui-controlgroup-label-contents").contents().unwrap()},_initWidgets:function(){var e=this,i=[];t.each(this.options.items,(function(n,s){var o,r={};s&&("controlgroupLabel"===n?((o=e.element.find(s)).each((function(){var e=t(this);e.children(".ui-controlgroup-label-contents").length||e.contents().wrapAll("<span class='ui-controlgroup-label-contents'></span>")})),e._addClass(o,null,"ui-widget ui-widget-content ui-state-default"),i=i.concat(o.get())):t.fn[n]&&(r=e["_"+n+"Options"]?e["_"+n+"Options"]("middle"):{classes:{}},e.element.find(s).each((function(){var s=t(this),o=s[n]("instance"),a=t.widget.extend({},r);"button"===n&&s.parent(".ui-spinner").length||((o=o||s[n]()[n]("instance"))&&(a.classes=e._resolveClassesValues(a.classes,o)),s[n](a),a=s[n]("widget"),t.data(a[0],"ui-controlgroup-data",o||s[n]("instance")),i.push(a[0]))}))))})),this.childWidgets=t(t.uniqueSort(i)),this._addClass(this.childWidgets,"ui-controlgroup-item")},_callChildMethod:function(e){this.childWidgets.each((function(){var i=t(this).data("ui-controlgroup-data");i&&i[e]&&i[e]()}))},_updateCornerClass:function(t,e){e=this._buildSimpleOptions(e,"label").classes.label,this._removeClass(t,null,"ui-corner-top ui-corner-bottom ui-corner-left ui-corner-right ui-corner-all"),this._addClass(t,null,e)},_buildSimpleOptions:function(t,e){var i="vertical"===this.options.direction,n={classes:{}};return n.classes[e]={middle:"",first:"ui-corner-"+(i?"top":"left"),last:"ui-corner-"+(i?"bottom":"right"),only:"ui-corner-all"}[t],n},_spinnerOptions:function(t){return(t=this._buildSimpleOptions(t,"ui-spinner")).classes["ui-spinner-up"]="",t.classes["ui-spinner-down"]="",t},_buttonOptions:function(t){return this._buildSimpleOptions(t,"ui-button")},_checkboxradioOptions:function(t){return this._buildSimpleOptions(t,"ui-checkboxradio-label")},_selectmenuOptions:function(t){var e="vertical"===this.options.direction;return{width:e&&"auto",classes:{middle:{"ui-selectmenu-button-open":"","ui-selectmenu-button-closed":""},first:{"ui-selectmenu-button-open":"ui-corner-"+(e?"top":"tl"),"ui-selectmenu-button-closed":"ui-corner-"+(e?"top":"left")},last:{"ui-selectmenu-button-open":e?"":"ui-corner-tr","ui-selectmenu-button-closed":"ui-corner-"+(e?"bottom":"right")},only:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"}}[t]}},_resolveClassesValues:function(e,i){var n={};return t.each(e,(function(t){var s=i.options.classes[t]||"";s=String.prototype.trim.call(s.replace(it,"")),n[t]=(s+" "+e[t]).replace(/\s+/g," ")})),n},_setOption:function(t,e){"direction"===t&&this._removeClass("ui-controlgroup-"+this.options.direction),this._super(t,e),"disabled"===t?this._callChildMethod(e?"disable":"enable"):this.refresh()},refresh:function(){var e,i=this;this._addClass("ui-controlgroup ui-controlgroup-"+this.options.direction),"horizontal"===this.options.direction&&this._addClass(null,"ui-helper-clearfix"),this._initWidgets(),e=this.childWidgets,(e=this.options.onlyVisible?e.filter(":visible"):e).length&&(t.each(["first","last"],(function(t,n){var s,o=e[n]().data("ui-controlgroup-data");o&&i["_"+o.widgetName+"Options"]?((s=i["_"+o.widgetName+"Options"](1===e.length?"only":n)).classes=i._resolveClassesValues(s.classes,o),o.element[o.widgetName](s)):i._updateCornerClass(e[n](),n)})),this._callChildMethod("refresh"))}}),t.widget("ui.checkboxradio",[t.ui.formResetMixin,{version:"1.13.3",options:{disabled:null,label:null,icon:!0,classes:{"ui-checkboxradio-label":"ui-corner-all","ui-checkboxradio-icon":"ui-corner-all"}},_getCreateOptions:function(){var e,i=this._super()||{};return this._readType(),e=this.element.labels(),this.label=t(e[e.length-1]),this.label.length||t.error("No label found for checkboxradio widget"),this.originalLabel="",(e=this.label.contents().not(this.element[0])).length&&(this.originalLabel+=e.clone().wrapAll("<div></div>").parent().html()),this.originalLabel&&(i.label=this.originalLabel),null!=(e=this.element[0].disabled)&&(i.disabled=e),i},_create:function(){var t=this.element[0].checked;this._bindFormResetHandler(),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled),this._setOption("disabled",this.options.disabled),this._addClass("ui-checkboxradio","ui-helper-hidden-accessible"),this._addClass(this.label,"ui-checkboxradio-label","ui-button ui-widget"),"radio"===this.type&&this._addClass(this.label,"ui-checkboxradio-radio-label"),this.options.label&&this.options.label!==this.originalLabel?this._updateLabel():this.originalLabel&&(this.options.label=this.originalLabel),this._enhance(),t&&this._addClass(this.label,"ui-checkboxradio-checked","ui-state-active"),this._on({change:"_toggleClasses",focus:function(){this._addClass(this.label,null,"ui-state-focus ui-visual-focus")},blur:function(){this._removeClass(this.label,null,"ui-state-focus ui-visual-focus")}})},_readType:function(){var e=this.element[0].nodeName.toLowerCase();this.type=this.element[0].type,"input"===e&&/radio|checkbox/.test(this.type)||t.error("Can't create checkboxradio on element.nodeName="+e+" and element.type="+this.type)},_enhance:function(){this._updateIcon(this.element[0].checked)},widget:function(){return this.label},_getRadioGroup:function(){var e=this.element[0].name,i="input[name='"+t.escapeSelector(e)+"']";return e?(this.form.length?t(this.form[0].elements).filter(i):t(i).filter((function(){return 0===t(this)._form().length}))).not(this.element):t([])},_toggleClasses:function(){var e=this.element[0].checked;this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",e),this.options.icon&&"checkbox"===this.type&&this._toggleClass(this.icon,null,"ui-icon-check ui-state-checked",e)._toggleClass(this.icon,null,"ui-icon-blank",!e),"radio"===this.type&&this._getRadioGroup().each((function(){var e=t(this).checkboxradio("instance");e&&e._removeClass(e.label,"ui-checkboxradio-checked","ui-state-active")}))},_destroy:function(){this._unbindFormResetHandler(),this.icon&&(this.icon.remove(),this.iconSpace.remove())},_setOption:function(t,e){"label"===t&&!e||(this._super(t,e),"disabled"===t?(this._toggleClass(this.label,null,"ui-state-disabled",e),this.element[0].disabled=e):this.refresh())},_updateIcon:function(e){var i="ui-icon ui-icon-background ";this.options.icon?(this.icon||(this.icon=t("<span>"),this.iconSpace=t("<span> </span>"),this._addClass(this.iconSpace,"ui-checkboxradio-icon-space")),"checkbox"===this.type?(i+=e?"ui-icon-check ui-state-checked":"ui-icon-blank",this._removeClass(this.icon,null,e?"ui-icon-blank":"ui-icon-check")):i+="ui-icon-blank",this._addClass(this.icon,"ui-checkboxradio-icon",i),e||this._removeClass(this.icon,null,"ui-icon-check ui-state-checked"),this.icon.prependTo(this.label).after(this.iconSpace)):void 0!==this.icon&&(this.icon.remove(),this.iconSpace.remove(),delete this.icon)},_updateLabel:function(){var t=this.label.contents().not(this.element[0]);this.icon&&(t=t.not(this.icon[0])),(t=this.iconSpace?t.not(this.iconSpace[0]):t).remove(),this.label.append(this.options.label)},refresh:function(){var t=this.element[0].checked,e=this.element[0].disabled;this._updateIcon(t),this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",t),null!==this.options.label&&this._updateLabel(),e!==this.options.disabled&&this._setOptions({disabled:e})}}]),t.ui.checkboxradio,t.widget("ui.button",{version:"1.13.3",defaultElement:"<button>",options:{classes:{"ui-button":"ui-corner-all"},disabled:null,icon:null,iconPosition:"beginning",label:null,showLabel:!0},_getCreateOptions:function(){var t,e=this._super()||{};return this.isInput=this.element.is("input"),null!=(t=this.element[0].disabled)&&(e.disabled=t),this.originalLabel=this.isInput?this.element.val():this.element.html(),this.originalLabel&&(e.label=this.originalLabel),e},_create:function(){!this.option.showLabel&!this.options.icon&&(this.options.showLabel=!0),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled||!1),this.hasTitle=!!this.element.attr("title"),this.options.label&&this.options.label!==this.originalLabel&&(this.isInput?this.element.val(this.options.label):this.element.html(this.options.label)),this._addClass("ui-button","ui-widget"),this._setOption("disabled",this.options.disabled),this._enhance(),this.element.is("a")&&this._on({keyup:function(e){e.keyCode===t.ui.keyCode.SPACE&&(e.preventDefault(),this.element[0].click?this.element[0].click():this.element.trigger("click"))}})},_enhance:function(){this.element.is("button")||this.element.attr("role","button"),this.options.icon&&(this._updateIcon("icon",this.options.icon),this._updateTooltip())},_updateTooltip:function(){this.title=this.element.attr("title"),this.options.showLabel||this.title||this.element.attr("title",this.options.label)},_updateIcon:function(e,i){var n=(e="iconPosition"!==e)?this.options.iconPosition:i,s="top"===n||"bottom"===n;this.icon?e&&this._removeClass(this.icon,null,this.options.icon):(this.icon=t("<span>"),this._addClass(this.icon,"ui-button-icon","ui-icon"),this.options.showLabel||this._addClass("ui-button-icon-only")),e&&this._addClass(this.icon,null,i),this._attachIcon(n),s?(this._addClass(this.icon,null,"ui-widget-icon-block"),this.iconSpace&&this.iconSpace.remove()):(this.iconSpace||(this.iconSpace=t("<span> </span>"),this._addClass(this.iconSpace,"ui-button-icon-space")),this._removeClass(this.icon,null,"ui-wiget-icon-block"),this._attachIconSpace(n))},_destroy:function(){this.element.removeAttr("role"),this.icon&&this.icon.remove(),this.iconSpace&&this.iconSpace.remove(),this.hasTitle||this.element.removeAttr("title")},_attachIconSpace:function(t){this.icon[/^(?:end|bottom)/.test(t)?"before":"after"](this.iconSpace)},_attachIcon:function(t){this.element[/^(?:end|bottom)/.test(t)?"append":"prepend"](this.icon)},_setOptions:function(t){var e=(void 0===t.showLabel?this.options:t).showLabel,i=(void 0===t.icon?this.options:t).icon;e||i||(t.showLabel=!0),this._super(t)},_setOption:function(t,e){"icon"===t&&(e?this._updateIcon(t,e):this.icon&&(this.icon.remove(),this.iconSpace)&&this.iconSpace.remove()),"iconPosition"===t&&this._updateIcon(t,e),"showLabel"===t&&(this._toggleClass("ui-button-icon-only",null,!e),this._updateTooltip()),"label"===t&&(this.isInput?this.element.val(e):(this.element.html(e),this.icon&&(this._attachIcon(this.options.iconPosition),this._attachIconSpace(this.options.iconPosition)))),this._super(t,e),"disabled"===t&&(this._toggleClass(null,"ui-state-disabled",e),this.element[0].disabled=e)&&this.element.trigger("blur")},refresh:function(){var t=this.element.is("input, button")?this.element[0].disabled:this.element.hasClass("ui-button-disabled");t!==this.options.disabled&&this._setOptions({disabled:t}),this._updateTooltip()}}),!1!==t.uiBackCompat&&(t.widget("ui.button",t.ui.button,{options:{text:!0,icons:{primary:null,secondary:null}},_create:function(){this.options.showLabel&&!this.options.text&&(this.options.showLabel=this.options.text),!this.options.showLabel&&this.options.text&&(this.options.text=this.options.showLabel),this.options.icon||!this.options.icons.primary&&!this.options.icons.secondary?this.options.icon&&(this.options.icons.primary=this.options.icon):this.options.icons.primary?this.options.icon=this.options.icons.primary:(this.options.icon=this.options.icons.secondary,this.options.iconPosition="end"),this._super()},_setOption:function(t,e){"text"===t?this._super("showLabel",e):("showLabel"===t&&(this.options.text=e),"icon"===t&&(this.options.icons.primary=e),"icons"===t&&(e.primary?(this._super("icon",e.primary),this._super("iconPosition","beginning")):e.secondary&&(this._super("icon",e.secondary),this._super("iconPosition","end"))),this._superApply(arguments))}}),t.fn.button=(tt=t.fn.button,function(e){var i="string"==typeof e,n=Array.prototype.slice.call(arguments,1),s=this;return i?this.length||"instance"!==e?this.each((function(){var i,o=t(this).attr("type");return o=t.data(this,"ui-"+("checkbox"!==o&&"radio"!==o?"button":"checkboxradio")),"instance"===e?(s=o,!1):o?"function"!=typeof o[e]||"_"===e.charAt(0)?t.error("no such method '"+e+"' for button widget instance"):(i=o[e].apply(o,n))!==o&&void 0!==i?(s=i&&i.jquery?s.pushStack(i.get()):i,!1):void 0:t.error("cannot call methods on button prior to initialization; attempted to call method '"+e+"'")})):s=void 0:(n.length&&(e=t.widget.extend.apply(null,[e].concat(n))),this.each((function(){var i="checkbox"!==(i=t(this).attr("type"))&&"radio"!==i?"button":"checkboxradio",n=t.data(this,"ui-"+i);n?(n.option(e||{}),n._init&&n._init()):"button"==i?tt.call(t(this),e):t(this).checkboxradio(t.extend({icon:!1},e))}))),s}),t.fn.buttonset=function(){return t.ui.controlgroup||t.error("Controlgroup widget missing"),"option"===arguments[0]&&"items"===arguments[1]&&arguments[2]?this.controlgroup.apply(this,[arguments[0],"items.button",arguments[2]]):"option"===arguments[0]&&"items"===arguments[1]?this.controlgroup.apply(this,[arguments[0],"items.button"]):("object"==typeof arguments[0]&&arguments[0].items&&(arguments[0].items={button:arguments[0].items}),this.controlgroup.apply(this,arguments))}),t.ui.button,t.extend(t.ui,{datepicker:{version:"1.13.3"}}),t.extend(nt.prototype,{markerClassName:"hasDatepicker",maxRows:4,_widgetDatepicker:function(){return this.dpDiv},setDefaults:function(t){return rt(this._defaults,t||{}),this},_attachDatepicker:function(e,i){var n,s=e.nodeName.toLowerCase(),o="div"===s||"span"===s;e.id||(this.uuid+=1,e.id="dp"+this.uuid),(n=this._newInst(t(e),o)).settings=t.extend({},i||{}),"input"===s?this._connectDatepicker(e,n):o&&this._inlineDatepicker(e,n)},_newInst:function(e,i){return{id:e[0].id.replace(/([^A-Za-z0-9_\-])/g,"\\\\$1"),input:e,selectedDay:0,selectedMonth:0,selectedYear:0,drawMonth:0,drawYear:0,inline:i,dpDiv:i?st(t("<div class='"+this._inlineClass+" ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>")):this.dpDiv}},_connectDatepicker:function(e,i){var n=t(e);i.append=t([]),i.trigger=t([]),n.hasClass(this.markerClassName)||(this._attachments(n,i),n.addClass(this.markerClassName).on("keydown",this._doKeyDown).on("keypress",this._doKeyPress).on("keyup",this._doKeyUp),this._autoSize(i),t.data(e,"datepicker",i),i.settings.disabled&&this._disableDatepicker(e))},_attachments:function(e,i){var n,s=this._get(i,"appendText"),o=this._get(i,"isRTL");i.append&&i.append.remove(),s&&(i.append=t("<span>").addClass(this._appendClass).text(s),e[o?"before":"after"](i.append)),e.off("focus",this._showDatepicker),i.trigger&&i.trigger.remove(),"focus"!==(s=this._get(i,"showOn"))&&"both"!==s||e.on("focus",this._showDatepicker),"button"!==s&&"both"!==s||(s=this._get(i,"buttonText"),n=this._get(i,"buttonImage"),this._get(i,"buttonImageOnly")?i.trigger=t("<img>").addClass(this._triggerClass).attr({src:n,alt:s,title:s}):(i.trigger=t("<button type='button'>").addClass(this._triggerClass),n?i.trigger.html(t("<img>").attr({src:n,alt:s,title:s})):i.trigger.text(s)),e[o?"before":"after"](i.trigger),i.trigger.on("click",(function(){return t.datepicker._datepickerShowing&&t.datepicker._lastInput===e[0]?t.datepicker._hideDatepicker():(t.datepicker._datepickerShowing&&t.datepicker._lastInput!==e[0]&&t.datepicker._hideDatepicker(),t.datepicker._showDatepicker(e[0])),!1})))},_autoSize:function(t){var e,i,n,s,o,r;this._get(t,"autoSize")&&!t.inline&&(o=new Date(2009,11,20),(r=this._get(t,"dateFormat")).match(/[DM]/)&&(o.setMonth((e=function(t){for(s=n=i=0;s<t.length;s++)t[s].length>i&&(i=t[s].length,n=s);return n})(this._get(t,r.match(/MM/)?"monthNames":"monthNamesShort"))),o.setDate(e(this._get(t,r.match(/DD/)?"dayNames":"dayNamesShort"))+20-o.getDay())),t.input.attr("size",this._formatDate(t,o).length))},_inlineDatepicker:function(e,i){var n=t(e);n.hasClass(this.markerClassName)||(n.addClass(this.markerClassName).append(i.dpDiv),t.data(e,"datepicker",i),this._setDate(i,this._getDefaultDate(i),!0),this._updateDatepicker(i),this._updateAlternate(i),i.settings.disabled&&this._disableDatepicker(e),i.dpDiv.css("display","block"))},_dialogDatepicker:function(e,i,n,s,o){var r,a=this._dialogInst;return a||(this.uuid+=1,r="dp"+this.uuid,this._dialogInput=t("<input type='text' id='"+r+"' style='position: absolute; top: -100px; width: 0px;'/>"),this._dialogInput.on("keydown",this._doKeyDown),t("body").append(this._dialogInput),(a=this._dialogInst=this._newInst(this._dialogInput,!1)).settings={},t.data(this._dialogInput[0],"datepicker",a)),rt(a.settings,s||{}),i=i&&i.constructor===Date?this._formatDate(a,i):i,this._dialogInput.val(i),this._pos=o?o.length?o:[o.pageX,o.pageY]:null,this._pos||(r=document.documentElement.clientWidth,s=document.documentElement.clientHeight,i=document.documentElement.scrollLeft||document.body.scrollLeft,o=document.documentElement.scrollTop||document.body.scrollTop,this._pos=[r/2-100+i,s/2-150+o]),this._dialogInput.css("left",this._pos[0]+20+"px").css("top",this._pos[1]+"px"),a.settings.onSelect=n,this._inDialog=!0,this.dpDiv.addClass(this._dialogClass),this._showDatepicker(this._dialogInput[0]),t.blockUI&&t.blockUI(this.dpDiv),t.data(this._dialogInput[0],"datepicker",a),this},_destroyDatepicker:function(e){var i,n=t(e),s=t.data(e,"datepicker");n.hasClass(this.markerClassName)&&(i=e.nodeName.toLowerCase(),t.removeData(e,"datepicker"),"input"===i?(s.append.remove(),s.trigger.remove(),n.removeClass(this.markerClassName).off("focus",this._showDatepicker).off("keydown",this._doKeyDown).off("keypress",this._doKeyPress).off("keyup",this._doKeyUp)):"div"!==i&&"span"!==i||n.removeClass(this.markerClassName).empty(),et===s)&&(et=null,this._curInst=null)},_enableDatepicker:function(e){var i,n=t(e),s=t.data(e,"datepicker");n.hasClass(this.markerClassName)&&("input"===(i=e.nodeName.toLowerCase())?(e.disabled=!1,s.trigger.filter("button").each((function(){this.disabled=!1})).end().filter("img").css({opacity:"1.0",cursor:""})):"div"!==i&&"span"!==i||((s=n.children("."+this._inlineClass)).children().removeClass("ui-state-disabled"),s.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!1)),this._disabledInputs=t.map(this._disabledInputs,(function(t){return t===e?null:t})))},_disableDatepicker:function(e){var i,n=t(e),s=t.data(e,"datepicker");n.hasClass(this.markerClassName)&&("input"===(i=e.nodeName.toLowerCase())?(e.disabled=!0,s.trigger.filter("button").each((function(){this.disabled=!0})).end().filter("img").css({opacity:"0.5",cursor:"default"})):"div"!==i&&"span"!==i||((s=n.children("."+this._inlineClass)).children().addClass("ui-state-disabled"),s.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!0)),this._disabledInputs=t.map(this._disabledInputs,(function(t){return t===e?null:t})),this._disabledInputs[this._disabledInputs.length]=e)},_isDisabledDatepicker:function(t){if(t)for(var e=0;e<this._disabledInputs.length;e++)if(this._disabledInputs[e]===t)return!0;return!1},_getInst:function(e){try{return t.data(e,"datepicker")}catch(e){throw"Missing instance data for this datepicker"}},_optionDatepicker:function(e,i,n){var s,o,r=this._getInst(e);if(2===arguments.length&&"string"==typeof i)return"defaults"===i?t.extend({},t.datepicker._defaults):r?"all"===i?t.extend({},r.settings):this._get(r,i):null;s=i||{},"string"==typeof i&&((s={})[i]=n),r&&(this._curInst===r&&this._hideDatepicker(),i=this._getDateDatepicker(e,!0),n=this._getMinMaxDate(r,"min"),o=this._getMinMaxDate(r,"max"),rt(r.settings,s),null!==n&&void 0!==s.dateFormat&&void 0===s.minDate&&(r.settings.minDate=this._formatDate(r,n)),null!==o&&void 0!==s.dateFormat&&void 0===s.maxDate&&(r.settings.maxDate=this._formatDate(r,o)),"disabled"in s&&(s.disabled?this._disableDatepicker(e):this._enableDatepicker(e)),this._attachments(t(e),r),this._autoSize(r),this._setDate(r,i),this._updateAlternate(r),this._updateDatepicker(r))},_changeDatepicker:function(t,e,i){this._optionDatepicker(t,e,i)},_refreshDatepicker:function(t){(t=this._getInst(t))&&this._updateDatepicker(t)},_setDateDatepicker:function(t,e){(t=this._getInst(t))&&(this._setDate(t,e),this._updateDatepicker(t),this._updateAlternate(t))},_getDateDatepicker:function(t,e){return(t=this._getInst(t))&&!t.inline&&this._setDateFromField(t,e),t?this._getDate(t):null},_doKeyDown:function(e){var i,n,s=t.datepicker._getInst(e.target),o=!0,r=s.dpDiv.is(".ui-datepicker-rtl");if(s._keyEvent=!0,t.datepicker._datepickerShowing)switch(e.keyCode){case 9:t.datepicker._hideDatepicker(),o=!1;break;case 13:return(n=t("td."+t.datepicker._dayOverClass+":not(."+t.datepicker._currentClass+")",s.dpDiv))[0]&&t.datepicker._selectDay(e.target,s.selectedMonth,s.selectedYear,n[0]),(n=t.datepicker._get(s,"onSelect"))?(i=t.datepicker._formatDate(s),n.apply(s.input?s.input[0]:null,[i,s])):t.datepicker._hideDatepicker(),!1;case 27:t.datepicker._hideDatepicker();break;case 33:t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(s,"stepBigMonths"):-t.datepicker._get(s,"stepMonths"),"M");break;case 34:t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(s,"stepBigMonths"):+t.datepicker._get(s,"stepMonths"),"M");break;case 35:(e.ctrlKey||e.metaKey)&&t.datepicker._clearDate(e.target),o=e.ctrlKey||e.metaKey;break;case 36:(e.ctrlKey||e.metaKey)&&t.datepicker._gotoToday(e.target),o=e.ctrlKey||e.metaKey;break;case 37:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,r?1:-1,"D"),o=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(s,"stepBigMonths"):-t.datepicker._get(s,"stepMonths"),"M");break;case 38:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,-7,"D"),o=e.ctrlKey||e.metaKey;break;case 39:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,r?-1:1,"D"),o=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(s,"stepBigMonths"):+t.datepicker._get(s,"stepMonths"),"M");break;case 40:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,7,"D"),o=e.ctrlKey||e.metaKey;break;default:o=!1}else 36===e.keyCode&&e.ctrlKey?t.datepicker._showDatepicker(this):o=!1;o&&(e.preventDefault(),e.stopPropagation())},_doKeyPress:function(e){var i,n=t.datepicker._getInst(e.target);if(t.datepicker._get(n,"constrainInput"))return n=t.datepicker._possibleChars(t.datepicker._get(n,"dateFormat")),i=String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),e.ctrlKey||e.metaKey||i<" "||!n||-1<n.indexOf(i)},_doKeyUp:function(e){if((e=t.datepicker._getInst(e.target)).input.val()!==e.lastVal)try{t.datepicker.parseDate(t.datepicker._get(e,"dateFormat"),e.input?e.input.val():null,t.datepicker._getFormatConfig(e))&&(t.datepicker._setDateFromField(e),t.datepicker._updateAlternate(e),t.datepicker._updateDatepicker(e))}catch(e){}return!0},_showDatepicker:function(e){var i,n,s,o;"input"!==(e=e.target||e).nodeName.toLowerCase()&&(e=t("input",e.parentNode)[0]),t.datepicker._isDisabledDatepicker(e)||t.datepicker._lastInput===e||(o=t.datepicker._getInst(e),t.datepicker._curInst&&t.datepicker._curInst!==o&&(t.datepicker._curInst.dpDiv.stop(!0,!0),o)&&t.datepicker._datepickerShowing&&t.datepicker._hideDatepicker(t.datepicker._curInst.input[0]),!1===(n=(n=t.datepicker._get(o,"beforeShow"))?n.apply(e,[e,o]):{}))||(rt(o.settings,n),o.lastVal=null,t.datepicker._lastInput=e,t.datepicker._setDateFromField(o),t.datepicker._inDialog&&(e.value=""),t.datepicker._pos||(t.datepicker._pos=t.datepicker._findPos(e),t.datepicker._pos[1]+=e.offsetHeight),i=!1,t(e).parents().each((function(){return!(i|="fixed"===t(this).css("position"))})),n={left:t.datepicker._pos[0],top:t.datepicker._pos[1]},t.datepicker._pos=null,o.dpDiv.empty(),o.dpDiv.css({position:"absolute",display:"block",top:"-1000px"}),t.datepicker._updateDatepicker(o),n=t.datepicker._checkOffset(o,n,i),o.dpDiv.css({position:t.datepicker._inDialog&&t.blockUI?"static":i?"fixed":"absolute",display:"none",left:n.left+"px",top:n.top+"px"}),o.inline)||(n=t.datepicker._get(o,"showAnim"),s=t.datepicker._get(o,"duration"),o.dpDiv.css("z-index",function(t){for(var e;t.length&&t[0]!==document;){if(("absolute"===(e=t.css("position"))||"relative"===e||"fixed"===e)&&(e=parseInt(t.css("zIndex"),10),!isNaN(e))&&0!==e)return e;t=t.parent()}return 0}(t(e))+1),t.datepicker._datepickerShowing=!0,t.effects&&t.effects.effect[n]?o.dpDiv.show(n,t.datepicker._get(o,"showOptions"),s):o.dpDiv[n||"show"](n?s:null),t.datepicker._shouldFocusInput(o)&&o.input.trigger("focus"),t.datepicker._curInst=o)},_updateDatepicker:function(e){this.maxRows=4,(et=e).dpDiv.empty().append(this._generateHTML(e)),this._attachHandlers(e);var i,n=this._getNumberOfMonths(e),s=n[1],o=e.dpDiv.find("."+this._dayOverClass+" a"),r=t.datepicker._get(e,"onUpdateDatepicker");0<o.length&&ot.apply(o.get(0)),e.dpDiv.removeClass("ui-datepicker-multi-2 ui-datepicker-multi-3 ui-datepicker-multi-4").width(""),1<s&&e.dpDiv.addClass("ui-datepicker-multi-"+s).css("width",17*s+"em"),e.dpDiv[(1!==n[0]||1!==n[1]?"add":"remove")+"Class"]("ui-datepicker-multi"),e.dpDiv[(this._get(e,"isRTL")?"add":"remove")+"Class"]("ui-datepicker-rtl"),e===t.datepicker._curInst&&t.datepicker._datepickerShowing&&t.datepicker._shouldFocusInput(e)&&e.input.trigger("focus"),e.yearshtml&&(i=e.yearshtml,setTimeout((function(){i===e.yearshtml&&e.yearshtml&&e.dpDiv.find("select.ui-datepicker-year").first().replaceWith(e.yearshtml),i=e.yearshtml=null}),0)),r&&r.apply(e.input?e.input[0]:null,[e])},_shouldFocusInput:function(t){return t.input&&t.input.is(":visible")&&!t.input.is(":disabled")&&!t.input.is(":focus")},_checkOffset:function(e,i,n){var s=e.dpDiv.outerWidth(),o=e.dpDiv.outerHeight(),r=e.input?e.input.outerWidth():0,a=e.input?e.input.outerHeight():0,l=document.documentElement.clientWidth+(n?0:t(document).scrollLeft()),h=document.documentElement.clientHeight+(n?0:t(document).scrollTop());return i.left-=this._get(e,"isRTL")?s-r:0,i.left-=n&&i.left===e.input.offset().left?t(document).scrollLeft():0,i.top-=n&&i.top===e.input.offset().top+a?t(document).scrollTop():0,i.left-=Math.min(i.left,i.left+s>l&&s<l?Math.abs(i.left+s-l):0),i.top-=Math.min(i.top,i.top+o>h&&o<h?Math.abs(o+a):0),i},_findPos:function(e){for(var i=this._getInst(e),n=this._get(i,"isRTL");e&&("hidden"===e.type||1!==e.nodeType||t.expr.pseudos.hidden(e));)e=e[n?"previousSibling":"nextSibling"];return[(i=t(e).offset()).left,i.top]},_hideDatepicker:function(e){var i,n,s=this._curInst;!s||e&&s!==t.data(e,"datepicker")||this._datepickerShowing&&(e=this._get(s,"showAnim"),n=this._get(s,"duration"),i=function(){t.datepicker._tidyDialog(s)},t.effects&&(t.effects.effect[e]||t.effects[e])?s.dpDiv.hide(e,t.datepicker._get(s,"showOptions"),n,i):s.dpDiv["slideDown"===e?"slideUp":"fadeIn"===e?"fadeOut":"hide"](e?n:null,i),e||i(),this._datepickerShowing=!1,(n=this._get(s,"onClose"))&&n.apply(s.input?s.input[0]:null,[s.input?s.input.val():"",s]),this._lastInput=null,this._inDialog&&(this._dialogInput.css({position:"absolute",left:"0",top:"-100px"}),t.blockUI)&&(t.unblockUI(),t("body").append(this.dpDiv)),this._inDialog=!1)},_tidyDialog:function(t){t.dpDiv.removeClass(this._dialogClass).off(".ui-datepicker-calendar")},_checkExternalClick:function(e){var i;t.datepicker._curInst&&(e=t(e.target),i=t.datepicker._getInst(e[0]),!(e[0].id===t.datepicker._mainDivId||0!==e.parents("#"+t.datepicker._mainDivId).length||e.hasClass(t.datepicker.markerClassName)||e.closest("."+t.datepicker._triggerClass).length||!t.datepicker._datepickerShowing||t.datepicker._inDialog&&t.blockUI)||e.hasClass(t.datepicker.markerClassName)&&t.datepicker._curInst!==i)&&t.datepicker._hideDatepicker()},_adjustDate:function(e,i,n){e=t(e);var s=this._getInst(e[0]);this._isDisabledDatepicker(e[0])||(this._adjustInstDate(s,i,n),this._updateDatepicker(s))},_gotoToday:function(e){e=t(e);var i,n=this._getInst(e[0]);this._get(n,"gotoCurrent")&&n.currentDay?(n.selectedDay=n.currentDay,n.drawMonth=n.selectedMonth=n.currentMonth,n.drawYear=n.selectedYear=n.currentYear):(i=new Date,n.selectedDay=i.getDate(),n.drawMonth=n.selectedMonth=i.getMonth(),n.drawYear=n.selectedYear=i.getFullYear()),this._notifyChange(n),this._adjustDate(e)},_selectMonthYear:function(e,i,n){e=t(e);var s=this._getInst(e[0]);s["selected"+("M"===n?"Month":"Year")]=s["draw"+("M"===n?"Month":"Year")]=parseInt(i.options[i.selectedIndex].value,10),this._notifyChange(s),this._adjustDate(e)},_selectDay:function(e,i,n,s){var o=t(e);t(s).hasClass(this._unselectableClass)||this._isDisabledDatepicker(o[0])||((o=this._getInst(o[0])).selectedDay=o.currentDay=parseInt(t("a",s).attr("data-date")),o.selectedMonth=o.currentMonth=i,o.selectedYear=o.currentYear=n,this._selectDate(e,this._formatDate(o,o.currentDay,o.currentMonth,o.currentYear)))},_clearDate:function(e){e=t(e),this._selectDate(e,"")},_selectDate:function(e,i){var n;e=t(e),e=this._getInst(e[0]),i=null!=i?i:this._formatDate(e),e.input&&e.input.val(i),this._updateAlternate(e),(n=this._get(e,"onSelect"))?n.apply(e.input?e.input[0]:null,[i,e]):e.input&&e.input.trigger("change"),e.inline?this._updateDatepicker(e):(this._hideDatepicker(),this._lastInput=e.input[0],"object"!=typeof e.input[0]&&e.input.trigger("focus"),this._lastInput=null)},_updateAlternate:function(e){var i,n,s=this._get(e,"altField");s&&(n=this._get(e,"altFormat")||this._get(e,"dateFormat"),i=this._getDate(e),n=this.formatDate(n,i,this._getFormatConfig(e)),t(document).find(s).val(n))},noWeekends:function(t){return[0<(t=t.getDay())&&t<6,""]},iso8601Week:function(t){var e;return(t=new Date(t.getTime())).setDate(t.getDate()+4-(t.getDay()||7)),e=t.getTime(),t.setMonth(0),t.setDate(1),Math.floor(Math.round((e-t)/864e5)/7)+1},parseDate:function(e,i,n){if(null==e||null==i)throw"Invalid arguments";if(""===(i="object"==typeof i?i.toString():i+""))return null;for(var s,o,r=0,a="string"!=typeof(a=(n?n.shortYearCutoff:null)||this._defaults.shortYearCutoff)?a:(new Date).getFullYear()%100+parseInt(a,10),l=(n?n.dayNamesShort:null)||this._defaults.dayNamesShort,h=(n?n.dayNames:null)||this._defaults.dayNames,c=(n?n.monthNamesShort:null)||this._defaults.monthNamesShort,u=(n?n.monthNames:null)||this._defaults.monthNames,d=-1,p=-1,f=-1,g=-1,m=!1,v=function(t){return(t=w+1<e.length&&e.charAt(w+1)===t)&&w++,t},_=function(t){var e=v(t);if(e="@"===t?14:"!"===t?20:"y"===t&&e?4:"o"===t?3:2,t=new RegExp("^\\d{"+("y"===t?e:1)+","+e+"}"),e=i.substring(r).match(t))return r+=e[0].length,parseInt(e[0],10);throw"Missing number at position "+r},b=function(e,n,s){var o=-1;if(e=t.map(v(e)?s:n,(function(t,e){return[[e,t]]})).sort((function(t,e){return-(t[1].length-e[1].length)})),t.each(e,(function(t,e){var n=e[1];if(i.substr(r,n.length).toLowerCase()===n.toLowerCase())return o=e[0],r+=n.length,!1})),-1!==o)return o+1;throw"Unknown name at position "+r},y=function(){if(i.charAt(r)!==e.charAt(w))throw"Unexpected literal at position "+r;r++},w=0;w<e.length;w++)if(m)"'"!==e.charAt(w)||v("'")?y():m=!1;else switch(e.charAt(w)){case"d":f=_("d");break;case"D":b("D",l,h);break;case"o":g=_("o");break;case"m":p=_("m");break;case"M":p=b("M",c,u);break;case"y":d=_("y");break;case"@":d=(o=new Date(_("@"))).getFullYear(),p=o.getMonth()+1,f=o.getDate();break;case"!":d=(o=new Date((_("!")-this._ticksTo1970)/1e4)).getFullYear(),p=o.getMonth()+1,f=o.getDate();break;case"'":v("'")?y():m=!0;break;default:y()}if(r<i.length&&(n=i.substr(r),!/^\s+/.test(n)))throw"Extra/unparsed characters found in date: "+n;if(-1===d?d=(new Date).getFullYear():d<100&&(d+=(new Date).getFullYear()-(new Date).getFullYear()%100+(d<=a?0:-100)),-1<g)for(p=1,f=g;!(f<=(s=this._getDaysInMonth(d,p-1)));)p++,f-=s;if((o=this._daylightSavingAdjust(new Date(d,p-1,f))).getFullYear()!==d||o.getMonth()+1!==p||o.getDate()!==f)throw"Invalid date";return o},ATOM:"yy-mm-dd",COOKIE:"D, dd M yy",ISO_8601:"yy-mm-dd",RFC_822:"D, d M y",RFC_850:"DD, dd-M-y",RFC_1036:"D, d M y",RFC_1123:"D, d M yy",RFC_2822:"D, d M yy",RSS:"D, d M y",TICKS:"!",TIMESTAMP:"@",W3C:"yy-mm-dd",_ticksTo1970:24*(718685+Math.floor(492.5)-Math.floor(19.7)+Math.floor(4.925))*60*60*1e7,formatDate:function(t,e,i){if(!e)return"";function n(t,e,i){var n=""+e;if(c(t))for(;n.length<i;)n="0"+n;return n}function s(t,e,i,n){return(c(t)?n:i)[e]}var o,r=(i?i.dayNamesShort:null)||this._defaults.dayNamesShort,a=(i?i.dayNames:null)||this._defaults.dayNames,l=(i?i.monthNamesShort:null)||this._defaults.monthNamesShort,h=(i?i.monthNames:null)||this._defaults.monthNames,c=function(e){return(e=o+1<t.length&&t.charAt(o+1)===e)&&o++,e},u="",d=!1;if(e)for(o=0;o<t.length;o++)if(d)"'"!==t.charAt(o)||c("'")?u+=t.charAt(o):d=!1;else switch(t.charAt(o)){case"d":u+=n("d",e.getDate(),2);break;case"D":u+=s("D",e.getDay(),r,a);break;case"o":u+=n("o",Math.round((new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime()-new Date(e.getFullYear(),0,0).getTime())/864e5),3);break;case"m":u+=n("m",e.getMonth()+1,2);break;case"M":u+=s("M",e.getMonth(),l,h);break;case"y":u+=c("y")?e.getFullYear():(e.getFullYear()%100<10?"0":"")+e.getFullYear()%100;break;case"@":u+=e.getTime();break;case"!":u+=1e4*e.getTime()+this._ticksTo1970;break;case"'":c("'")?u+="'":d=!0;break;default:u+=t.charAt(o)}return u},_possibleChars:function(t){for(var e="",i=!1,n=function(e){return(e=s+1<t.length&&t.charAt(s+1)===e)&&s++,e},s=0;s<t.length;s++)if(i)"'"!==t.charAt(s)||n("'")?e+=t.charAt(s):i=!1;else switch(t.charAt(s)){case"d":case"m":case"y":case"@":e+="0123456789";break;case"D":case"M":return null;case"'":n("'")?e+="'":i=!0;break;default:e+=t.charAt(s)}return e},_get:function(t,e){return(void 0!==t.settings[e]?t.settings:this._defaults)[e]},_setDateFromField:function(t,e){if(t.input.val()!==t.lastVal){var i=this._get(t,"dateFormat"),n=t.lastVal=t.input?t.input.val():null,s=this._getDefaultDate(t),o=s,r=this._getFormatConfig(t);try{o=this.parseDate(i,n,r)||s}catch(t){n=e?"":n}t.selectedDay=o.getDate(),t.drawMonth=t.selectedMonth=o.getMonth(),t.drawYear=t.selectedYear=o.getFullYear(),t.currentDay=n?o.getDate():0,t.currentMonth=n?o.getMonth():0,t.currentYear=n?o.getFullYear():0,this._adjustInstDate(t)}},_getDefaultDate:function(t){return this._restrictMinMax(t,this._determineDate(t,this._get(t,"defaultDate"),new Date))},_determineDate:function(e,i,n){var s,o=null==i||""===i?n:"string"==typeof i?function(i){try{return t.datepicker.parseDate(t.datepicker._get(e,"dateFormat"),i,t.datepicker._getFormatConfig(e))}catch(i){}for(var n=(i.toLowerCase().match(/^c/)?t.datepicker._getDate(e):null)||new Date,s=n.getFullYear(),o=n.getMonth(),r=n.getDate(),a=/([+\-]?[0-9]+)\s*(d|D|w|W|m|M|y|Y)?/g,l=a.exec(i);l;){switch(l[2]||"d"){case"d":case"D":r+=parseInt(l[1],10);break;case"w":case"W":r+=7*parseInt(l[1],10);break;case"m":case"M":o+=parseInt(l[1],10),r=Math.min(r,t.datepicker._getDaysInMonth(s,o));break;case"y":case"Y":s+=parseInt(l[1],10),r=Math.min(r,t.datepicker._getDaysInMonth(s,o))}l=a.exec(i)}return new Date(s,o,r)}(i):"number"==typeof i?isNaN(i)?n:(o=i,(s=new Date).setDate(s.getDate()+o),s):new Date(i.getTime());return(o=o&&"Invalid Date"===o.toString()?n:o)&&(o.setHours(0),o.setMinutes(0),o.setSeconds(0),o.setMilliseconds(0)),this._daylightSavingAdjust(o)},_daylightSavingAdjust:function(t){return t?(t.setHours(12<t.getHours()?t.getHours()+2:0),t):null},_setDate:function(t,e,i){var n=!e,s=t.selectedMonth,o=t.selectedYear;e=this._restrictMinMax(t,this._determineDate(t,e,new Date)),t.selectedDay=t.currentDay=e.getDate(),t.drawMonth=t.selectedMonth=t.currentMonth=e.getMonth(),t.drawYear=t.selectedYear=t.currentYear=e.getFullYear(),s===t.selectedMonth&&o===t.selectedYear||i||this._notifyChange(t),this._adjustInstDate(t),t.input&&t.input.val(n?"":this._formatDate(t))},_getDate:function(t){return!t.currentYear||t.input&&""===t.input.val()?null:this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay))},_attachHandlers:function(e){var i=this._get(e,"stepMonths"),n="#"+e.id.replace(/\\\\/g,"\\");e.dpDiv.find("[data-handler]").map((function(){var e={prev:function(){t.datepicker._adjustDate(n,-i,"M")},next:function(){t.datepicker._adjustDate(n,+i,"M")},hide:function(){t.datepicker._hideDatepicker()},today:function(){t.datepicker._gotoToday(n)},selectDay:function(){return t.datepicker._selectDay(n,+this.getAttribute("data-month"),+this.getAttribute("data-year"),this),!1},selectMonth:function(){return t.datepicker._selectMonthYear(n,this,"M"),!1},selectYear:function(){return t.datepicker._selectMonthYear(n,this,"Y"),!1}};t(this).on(this.getAttribute("data-event"),e[this.getAttribute("data-handler")])}))},_generateHTML:function(e){var i,n,s,o,r,a,l,h,c,u,d,p,f,g,m,v,_,b,y,w,x,C,S,k,E,D,T,M,I,A,P,O,F=new Date,H=this._daylightSavingAdjust(new Date(F.getFullYear(),F.getMonth(),F.getDate())),V=this._get(e,"isRTL"),N=(F=this._get(e,"showButtonPanel"),this._get(e,"hideIfNoPrevNext")),W=this._get(e,"navigationAsDateFormat"),R=this._getNumberOfMonths(e),B=this._get(e,"showCurrentAtPos"),L=this._get(e,"stepMonths"),z=1!==R[0]||1!==R[1],j=this._daylightSavingAdjust(e.currentDay?new Date(e.currentYear,e.currentMonth,e.currentDay):new Date(9999,9,9)),G=this._getMinMaxDate(e,"min"),q=this._getMinMaxDate(e,"max"),U=e.drawMonth-B,$=e.drawYear;if(U<0&&(U+=12,$--),q)for(i=this._daylightSavingAdjust(new Date(q.getFullYear(),q.getMonth()-R[0]*R[1]+1,q.getDate())),i=G&&i<G?G:i;this._daylightSavingAdjust(new Date($,U,1))>i;)--U<0&&(U=11,$--);for(e.drawMonth=U,e.drawYear=$,B=this._get(e,"prevText"),B=W?this.formatDate(B,this._daylightSavingAdjust(new Date($,U-L,1)),this._getFormatConfig(e)):B,n=this._canAdjustMonth(e,-1,$,U)?t("<a>").attr({class:"ui-datepicker-prev ui-corner-all","data-handler":"prev","data-event":"click",title:B}).append(t("<span>").addClass("ui-icon ui-icon-circle-triangle-"+(V?"e":"w")).text(B))[0].outerHTML:N?"":t("<a>").attr({class:"ui-datepicker-prev ui-corner-all ui-state-disabled",title:B}).append(t("<span>").addClass("ui-icon ui-icon-circle-triangle-"+(V?"e":"w")).text(B))[0].outerHTML,B=this._get(e,"nextText"),B=W?this.formatDate(B,this._daylightSavingAdjust(new Date($,U+L,1)),this._getFormatConfig(e)):B,s=this._canAdjustMonth(e,1,$,U)?t("<a>").attr({class:"ui-datepicker-next ui-corner-all","data-handler":"next","data-event":"click",title:B}).append(t("<span>").addClass("ui-icon ui-icon-circle-triangle-"+(V?"w":"e")).text(B))[0].outerHTML:N?"":t("<a>").attr({class:"ui-datepicker-next ui-corner-all ui-state-disabled",title:B}).append(t("<span>").attr("class","ui-icon ui-icon-circle-triangle-"+(V?"w":"e")).text(B))[0].outerHTML,L=this._get(e,"currentText"),N=this._get(e,"gotoCurrent")&&e.currentDay?j:H,L=W?this.formatDate(L,N,this._getFormatConfig(e)):L,B="",e.inline||(B=t("<button>").attr({type:"button",class:"ui-datepicker-close ui-state-default ui-priority-primary ui-corner-all","data-handler":"hide","data-event":"click"}).text(this._get(e,"closeText"))[0].outerHTML),W="",F&&(W=t("<div class='ui-datepicker-buttonpane ui-widget-content'>").append(V?B:"").append(this._isInRange(e,N)?t("<button>").attr({type:"button",class:"ui-datepicker-current ui-state-default ui-priority-secondary ui-corner-all","data-handler":"today","data-event":"click"}).text(L):"").append(V?"":B)[0].outerHTML),o=parseInt(this._get(e,"firstDay"),10),o=isNaN(o)?0:o,r=this._get(e,"showWeek"),a=this._get(e,"dayNames"),l=this._get(e,"dayNamesMin"),h=this._get(e,"monthNames"),c=this._get(e,"monthNamesShort"),u=this._get(e,"beforeShowDay"),d=this._get(e,"showOtherMonths"),p=this._get(e,"selectOtherMonths"),f=this._getDefaultDate(e),g="",v=0;v<R[0];v++){for(_="",this.maxRows=4,b=0;b<R[1];b++){if(y=this._daylightSavingAdjust(new Date($,U,e.selectedDay)),w=" ui-corner-all",x="",z){if(x+="<div class='ui-datepicker-group",1<R[1])switch(b){case 0:x+=" ui-datepicker-group-first",w=" ui-corner-"+(V?"right":"left");break;case R[1]-1:x+=" ui-datepicker-group-last",w=" ui-corner-"+(V?"left":"right");break;default:x+=" ui-datepicker-group-middle",w=""}x+="'>"}for(x+="<div class='ui-datepicker-header ui-widget-header ui-helper-clearfix"+w+"'>"+(/all|left/.test(w)&&0===v?V?s:n:"")+(/all|right/.test(w)&&0===v?V?n:s:"")+this._generateMonthYearHeader(e,U,$,G,q,0<v||0<b,h,c)+"</div><table class='ui-datepicker-calendar'><thead><tr>",C=r?"<th class='ui-datepicker-week-col'>"+this._get(e,"weekHeader")+"</th>":"",m=0;m<7;m++)C+="<th scope='col'"+(5<=(m+o+6)%7?" class='ui-datepicker-week-end'":"")+"><span title='"+a[S=(m+o)%7]+"'>"+l[S]+"</span></th>";for(x+=C+"</tr></thead><tbody>",E=this._getDaysInMonth($,U),$===e.selectedYear&&U===e.selectedMonth&&(e.selectedDay=Math.min(e.selectedDay,E)),k=(this._getFirstDayOfMonth($,U)-o+7)%7,E=Math.ceil((k+E)/7),D=z&&this.maxRows>E?this.maxRows:E,this.maxRows=D,T=this._daylightSavingAdjust(new Date($,U,1-k)),M=0;M<D;M++){for(x+="<tr>",I=r?"<td class='ui-datepicker-week-col'>"+this._get(e,"calculateWeek")(T)+"</td>":"",m=0;m<7;m++)A=u?u.apply(e.input?e.input[0]:null,[T]):[!0,""],O=(P=T.getMonth()!==U)&&!p||!A[0]||G&&T<G||q&&q<T,I+="<td class='"+(5<=(m+o+6)%7?" ui-datepicker-week-end":"")+(P?" ui-datepicker-other-month":"")+(T.getTime()===y.getTime()&&U===e.selectedMonth&&e._keyEvent||f.getTime()===T.getTime()&&f.getTime()===y.getTime()?" "+this._dayOverClass:"")+(O?" "+this._unselectableClass+" ui-state-disabled":"")+(P&&!d?"":" "+A[1]+(T.getTime()===j.getTime()?" "+this._currentClass:"")+(T.getTime()===H.getTime()?" ui-datepicker-today":""))+"'"+(P&&!d||!A[2]?"":" title='"+A[2].replace(/'/g,"&#39;")+"'")+(O?"":" data-handler='selectDay' data-event='click' data-month='"+T.getMonth()+"' data-year='"+T.getFullYear()+"'")+">"+(P&&!d?"&#xa0;":O?"<span class='ui-state-default'>"+T.getDate()+"</span>":"<a class='ui-state-default"+(T.getTime()===H.getTime()?" ui-state-highlight":"")+(T.getTime()===j.getTime()?" ui-state-active":"")+(P?" ui-priority-secondary":"")+"' href='#' aria-current='"+(T.getTime()===j.getTime()?"true":"false")+"' data-date='"+T.getDate()+"'>"+T.getDate()+"</a>")+"</td>",T.setDate(T.getDate()+1),T=this._daylightSavingAdjust(T);x+=I+"</tr>"}11<++U&&(U=0,$++),_+=x+="</tbody></table>"+(z?"</div>"+(0<R[0]&&b===R[1]-1?"<div class='ui-datepicker-row-break'></div>":""):"")}g+=_}return g+=W,e._keyEvent=!1,g},_generateMonthYearHeader:function(t,e,i,n,s,o,r,a){var l,h,c,u,d,p,f=this._get(t,"changeMonth"),g=this._get(t,"changeYear"),m=this._get(t,"showMonthAfterYear"),v=this._get(t,"selectMonthLabel"),_=this._get(t,"selectYearLabel"),b="<div class='ui-datepicker-title'>",y="";if(o||!f)y+="<span class='ui-datepicker-month'>"+r[e]+"</span>";else{for(l=n&&n.getFullYear()===i,h=s&&s.getFullYear()===i,y+="<select class='ui-datepicker-month' aria-label='"+v+"' data-handler='selectMonth' data-event='change'>",c=0;c<12;c++)(!l||c>=n.getMonth())&&(!h||c<=s.getMonth())&&(y+="<option value='"+c+"'"+(c===e?" selected='selected'":"")+">"+a[c]+"</option>");y+="</select>"}if(m||(b+=y+(!o&&f&&g?"":"&#xa0;")),!t.yearshtml)if(t.yearshtml="",o||!g)b+="<span class='ui-datepicker-year'>"+i+"</span>";else{for(r=this._get(t,"yearRange").split(":"),u=(new Date).getFullYear(),d=(v=function(t){return t=t.match(/c[+\-].*/)?i+parseInt(t.substring(1),10):t.match(/[+\-].*/)?u+parseInt(t,10):parseInt(t,10),isNaN(t)?u:t})(r[0]),p=Math.max(d,v(r[1]||"")),d=n?Math.max(d,n.getFullYear()):d,p=s?Math.min(p,s.getFullYear()):p,t.yearshtml+="<select class='ui-datepicker-year' aria-label='"+_+"' data-handler='selectYear' data-event='change'>";d<=p;d++)t.yearshtml+="<option value='"+d+"'"+(d===i?" selected='selected'":"")+">"+d+"</option>";t.yearshtml+="</select>",b+=t.yearshtml,t.yearshtml=null}return b+=this._get(t,"yearSuffix"),m&&(b+=(!o&&f&&g?"":"&#xa0;")+y),b+"</div>"},_adjustInstDate:function(t,e,i){var n=t.selectedYear+("Y"===i?e:0),s=t.selectedMonth+("M"===i?e:0);e=Math.min(t.selectedDay,this._getDaysInMonth(n,s))+("D"===i?e:0),n=this._restrictMinMax(t,this._daylightSavingAdjust(new Date(n,s,e))),t.selectedDay=n.getDate(),t.drawMonth=t.selectedMonth=n.getMonth(),t.drawYear=t.selectedYear=n.getFullYear(),"M"!==i&&"Y"!==i||this._notifyChange(t)},_restrictMinMax:function(t,e){var i=(i=this._getMinMaxDate(t,"min"))&&e<i?i:e;return(t=this._getMinMaxDate(t,"max"))&&t<i?t:i},_notifyChange:function(t){var e=this._get(t,"onChangeMonthYear");e&&e.apply(t.input?t.input[0]:null,[t.selectedYear,t.selectedMonth+1,t])},_getNumberOfMonths:function(t){return null==(t=this._get(t,"numberOfMonths"))?[1,1]:"number"==typeof t?[1,t]:t},_getMinMaxDate:function(t,e){return this._determineDate(t,this._get(t,e+"Date"),null)},_getDaysInMonth:function(t,e){return 32-this._daylightSavingAdjust(new Date(t,e,32)).getDate()},_getFirstDayOfMonth:function(t,e){return new Date(t,e,1).getDay()},_canAdjustMonth:function(t,e,i,n){var s=this._getNumberOfMonths(t);return i=this._daylightSavingAdjust(new Date(i,n+(e<0?e:s[0]*s[1]),1)),e<0&&i.setDate(this._getDaysInMonth(i.getFullYear(),i.getMonth())),this._isInRange(t,i)},_isInRange:function(t,e){var i,n=this._getMinMaxDate(t,"min"),s=this._getMinMaxDate(t,"max"),o=null,r=null;return(t=this._get(t,"yearRange"))&&(t=t.split(":"),i=(new Date).getFullYear(),o=parseInt(t[0],10),r=parseInt(t[1],10),t[0].match(/[+\-].*/)&&(o+=i),t[1].match(/[+\-].*/))&&(r+=i),(!n||e.getTime()>=n.getTime())&&(!s||e.getTime()<=s.getTime())&&(!o||e.getFullYear()>=o)&&(!r||e.getFullYear()<=r)},_getFormatConfig:function(t){var e=this._get(t,"shortYearCutoff");return{shortYearCutoff:"string"!=typeof e?e:(new Date).getFullYear()%100+parseInt(e,10),dayNamesShort:this._get(t,"dayNamesShort"),dayNames:this._get(t,"dayNames"),monthNamesShort:this._get(t,"monthNamesShort"),monthNames:this._get(t,"monthNames")}},_formatDate:function(t,e,i,n){return e||(t.currentDay=t.selectedDay,t.currentMonth=t.selectedMonth,t.currentYear=t.selectedYear),n=e?"object"==typeof e?e:this._daylightSavingAdjust(new Date(n,i,e)):this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay)),this.formatDate(this._get(t,"dateFormat"),n,this._getFormatConfig(t))}}),t.fn.datepicker=function(e){if(!this.length)return this;t.datepicker.initialized||(t(document).on("mousedown",t.datepicker._checkExternalClick),t.datepicker.initialized=!0),0===t("#"+t.datepicker._mainDivId).length&&t("body").append(t.datepicker.dpDiv);var i=Array.prototype.slice.call(arguments,1);return"string"==typeof e&&("isDisabled"===e||"getDate"===e||"widget"===e)||"option"===e&&2===arguments.length&&"string"==typeof arguments[1]?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this[0]].concat(i)):this.each((function(){"string"==typeof e?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this].concat(i)):t.datepicker._attachDatepicker(this,e)}))},t.datepicker=new nt,t.datepicker.initialized=!1,t.datepicker.uuid=(new Date).getTime(),t.datepicker.version="1.13.3",t.datepicker,t.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase());var at,lt=!1;function ht(t,e,i){return e<=t&&t<e+i}function ct(t){return function(){var e=this.element.val();t.apply(this,arguments),this._refresh(),e!==this.element.val()&&this._trigger("change")}}t(document).on("mouseup",(function(){lt=!1})),t.widget("ui.mouse",{version:"1.13.3",options:{cancel:"input, textarea, button, select, option",distance:1,delay:0},_mouseInit:function(){var e=this;this.element.on("mousedown."+this.widgetName,(function(t){return e._mouseDown(t)})).on("click."+this.widgetName,(function(i){if(!0===t.data(i.target,e.widgetName+".preventClickEvent"))return t.removeData(i.target,e.widgetName+".preventClickEvent"),i.stopImmediatePropagation(),!1})),this.started=!1},_mouseDestroy:function(){this.element.off("."+this.widgetName),this._mouseMoveDelegate&&this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(e){var i,n,s;if(!lt)return this._mouseMoved=!1,this._mouseStarted&&this._mouseUp(e),n=1===(this._mouseDownEvent=e).which,s=!("string"!=typeof(i=this).options.cancel||!e.target.nodeName)&&t(e.target).closest(this.options.cancel).length,n&&!s&&this._mouseCapture(e)&&(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout((function(){i.mouseDelayMet=!0}),this.options.delay)),this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(e),!this._mouseStarted)?e.preventDefault():(!0===t.data(e.target,this.widgetName+".preventClickEvent")&&t.removeData(e.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(t){return i._mouseMove(t)},this._mouseUpDelegate=function(t){return i._mouseUp(t)},this.document.on("mousemove."+this.widgetName,this._mouseMoveDelegate).on("mouseup."+this.widgetName,this._mouseUpDelegate),e.preventDefault(),lt=!0)),!0},_mouseMove:function(e){if(this._mouseMoved){if(t.ui.ie&&(!document.documentMode||document.documentMode<9)&&!e.button)return this._mouseUp(e);if(!e.which)if(e.originalEvent.altKey||e.originalEvent.ctrlKey||e.originalEvent.metaKey||e.originalEvent.shiftKey)this.ignoreMissingWhich=!0;else if(!this.ignoreMissingWhich)return this._mouseUp(e)}return(e.which||e.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(e),e.preventDefault()):(this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,e),this._mouseStarted?this._mouseDrag(e):this._mouseUp(e)),!this._mouseStarted)},_mouseUp:function(e){this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,e.target===this._mouseDownEvent.target&&t.data(e.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(e)),this._mouseDelayTimer&&(clearTimeout(this._mouseDelayTimer),delete this._mouseDelayTimer),this.ignoreMissingWhich=!1,lt=!1,e.preventDefault()},_mouseDistanceMet:function(t){return Math.max(Math.abs(this._mouseDownEvent.pageX-t.pageX),Math.abs(this._mouseDownEvent.pageY-t.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}}),t.ui.plugin={add:function(e,i,n){var s,o=t.ui[e].prototype;for(s in n)o.plugins[s]=o.plugins[s]||[],o.plugins[s].push([i,n[s]])},call:function(t,e,i,n){var s,o=t.plugins[e];if(o&&(n||t.element[0].parentNode&&11!==t.element[0].parentNode.nodeType))for(s=0;s<o.length;s++)t.options[o[s][0]]&&o[s][1].apply(t.element,i)}},t.ui.safeBlur=function(e){e&&"body"!==e.nodeName.toLowerCase()&&t(e).trigger("blur")},t.widget("ui.draggable",t.ui.mouse,{version:"1.13.3",widgetEventPrefix:"drag",options:{addClasses:!0,appendTo:"parent",axis:!1,connectToSortable:!1,containment:!1,cursor:"auto",cursorAt:!1,grid:!1,handle:!1,helper:"original",iframeFix:!1,opacity:!1,refreshPositions:!1,revert:!1,revertDuration:500,scope:"default",scroll:!0,scrollSensitivity:20,scrollSpeed:20,snap:!1,snapMode:"both",snapTolerance:20,stack:!1,zIndex:!1,drag:null,start:null,stop:null},_create:function(){"original"===this.options.helper&&this._setPositionRelative(),this.options.addClasses&&this._addClass("ui-draggable"),this._setHandleClassName(),this._mouseInit()},_setOption:function(t,e){this._super(t,e),"handle"===t&&(this._removeHandleClassName(),this._setHandleClassName())},_destroy:function(){(this.helper||this.element).is(".ui-draggable-dragging")?this.destroyOnClear=!0:(this._removeHandleClassName(),this._mouseDestroy())},_mouseCapture:function(e){var i=this.options;return!(this.helper||i.disabled||0<t(e.target).closest(".ui-resizable-handle").length||(this.handle=this._getHandle(e),!this.handle)||(this._blurActiveElement(e),this._blockFrames(!0===i.iframeFix?"iframe":i.iframeFix),0))},_blockFrames:function(e){this.iframeBlocks=this.document.find(e).map((function(){var e=t(this);return t("<div>").css("position","absolute").appendTo(e.parent()).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()).offset(e.offset())[0]}))},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_blurActiveElement:function(e){var i=t.ui.safeActiveElement(this.document[0]);t(e.target).closest(i).length||t.ui.safeBlur(i)},_mouseStart:function(e){var i=this.options;return this.helper=this._createHelper(e),this._addClass(this.helper,"ui-draggable-dragging"),this._cacheHelperProportions(),t.ui.ddmanager&&(t.ui.ddmanager.current=this),this._cacheMargins(),this.cssPosition=this.helper.css("position"),this.scrollParent=this.helper.scrollParent(!0),this.offsetParent=this.helper.offsetParent(),this.hasFixedAncestor=0<this.helper.parents().filter((function(){return"fixed"===t(this).css("position")})).length,this.positionAbs=this.element.offset(),this._refreshOffsets(e),this.originalPosition=this.position=this._generatePosition(e,!1),this.originalPageX=e.pageX,this.originalPageY=e.pageY,i.cursorAt&&this._adjustOffsetFromHelper(i.cursorAt),this._setContainment(),!1===this._trigger("start",e)?(this._clear(),!1):(this._cacheHelperProportions(),t.ui.ddmanager&&!i.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this._mouseDrag(e,!0),t.ui.ddmanager&&t.ui.ddmanager.dragStart(this,e),!0)},_refreshOffsets:function(t){this.offset={top:this.positionAbs.top-this.margins.top,left:this.positionAbs.left-this.margins.left,scroll:!1,parent:this._getParentOffset(),relative:this._getRelativeOffset()},this.offset.click={left:t.pageX-this.offset.left,top:t.pageY-this.offset.top}},_mouseDrag:function(e,i){if(this.hasFixedAncestor&&(this.offset.parent=this._getParentOffset()),this.position=this._generatePosition(e,!0),this.positionAbs=this._convertPositionTo("absolute"),!i){if(i=this._uiHash(),!1===this._trigger("drag",e,i))return this._mouseUp(new t.Event("mouseup",e)),!1;this.position=i.position}return this.helper[0].style.left=this.position.left+"px",this.helper[0].style.top=this.position.top+"px",t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),!1},_mouseStop:function(e){var i=this,n=!1;return t.ui.ddmanager&&!this.options.dropBehaviour&&(n=t.ui.ddmanager.drop(this,e)),this.dropped&&(n=this.dropped,this.dropped=!1),"invalid"===this.options.revert&&!n||"valid"===this.options.revert&&n||!0===this.options.revert||"function"==typeof this.options.revert&&this.options.revert.call(this.element,n)?t(this.helper).animate(this.originalPosition,parseInt(this.options.revertDuration,10),(function(){!1!==i._trigger("stop",e)&&i._clear()})):!1!==this._trigger("stop",e)&&this._clear(),!1},_mouseUp:function(e){return this._unblockFrames(),t.ui.ddmanager&&t.ui.ddmanager.dragStop(this,e),this.handleElement.is(e.target)&&this.element.trigger("focus"),t.ui.mouse.prototype._mouseUp.call(this,e)},cancel:function(){return this.helper.is(".ui-draggable-dragging")?this._mouseUp(new t.Event("mouseup",{target:this.element[0]})):this._clear(),this},_getHandle:function(e){return!this.options.handle||!!t(e.target).closest(this.element.find(this.options.handle)).length},_setHandleClassName:function(){this.handleElement=this.options.handle?this.element.find(this.options.handle):this.element,this._addClass(this.handleElement,"ui-draggable-handle")},_removeHandleClassName:function(){this._removeClass(this.handleElement,"ui-draggable-handle")},_createHelper:function(e){var i=this.options,n="function"==typeof i.helper;return(e=n?t(i.helper.apply(this.element[0],[e])):"clone"===i.helper?this.element.clone().removeAttr("id"):this.element).parents("body").length||e.appendTo("parent"===i.appendTo?this.element[0].parentNode:i.appendTo),n&&e[0]===this.element[0]&&this._setPositionRelative(),e[0]===this.element[0]||/(fixed|absolute)/.test(e.css("position"))||e.css("position","absolute"),e},_setPositionRelative:function(){/^(?:r|a|f)/.test(this.element.css("position"))||(this.element[0].style.position="relative")},_adjustOffsetFromHelper:function(t){"string"==typeof t&&(t=t.split(" ")),"left"in(t=Array.isArray(t)?{left:+t[0],top:+t[1]||0}:t)&&(this.offset.click.left=t.left+this.margins.left),"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left),"top"in t&&(this.offset.click.top=t.top+this.margins.top),"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_isRootNode:function(t){return/(html|body)/i.test(t.tagName)||t===this.document[0]},_getParentOffset:function(){var e=this.offsetParent.offset(),i=this.document[0];return"absolute"===this.cssPosition&&this.scrollParent[0]!==i&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),{top:(e=this._isRootNode(this.offsetParent[0])?{top:0,left:0}:e).top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){var t,e;return"relative"!==this.cssPosition?{top:0,left:0}:(t=this.element.position(),e=this._isRootNode(this.scrollParent[0]),{top:t.top-(parseInt(this.helper.css("top"),10)||0)+(e?0:this.scrollParent.scrollTop()),left:t.left-(parseInt(this.helper.css("left"),10)||0)+(e?0:this.scrollParent.scrollLeft())})},_cacheMargins:function(){this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i=this.options,n=this.document[0];this.relativeContainer=null,i.containment?"window"===i.containment?this.containment=[t(window).scrollLeft()-this.offset.relative.left-this.offset.parent.left,t(window).scrollTop()-this.offset.relative.top-this.offset.parent.top,t(window).scrollLeft()+t(window).width()-this.helperProportions.width-this.margins.left,t(window).scrollTop()+(t(window).height()||n.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]:"document"===i.containment?this.containment=[0,0,t(n).width()-this.helperProportions.width-this.margins.left,(t(n).height()||n.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]:i.containment.constructor===Array?this.containment=i.containment:("parent"===i.containment&&(i.containment=this.helper[0].parentNode),(i=(n=t(i.containment))[0])&&(e=/(scroll|auto)/.test(n.css("overflow")),this.containment=[(parseInt(n.css("borderLeftWidth"),10)||0)+(parseInt(n.css("paddingLeft"),10)||0),(parseInt(n.css("borderTopWidth"),10)||0)+(parseInt(n.css("paddingTop"),10)||0),(e?Math.max(i.scrollWidth,i.offsetWidth):i.offsetWidth)-(parseInt(n.css("borderRightWidth"),10)||0)-(parseInt(n.css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left-this.margins.right,(e?Math.max(i.scrollHeight,i.offsetHeight):i.offsetHeight)-(parseInt(n.css("borderBottomWidth"),10)||0)-(parseInt(n.css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top-this.margins.bottom],this.relativeContainer=n)):this.containment=null},_convertPositionTo:function(t,e){e=e||this.position,t="absolute"===t?1:-1;var i=this._isRootNode(this.scrollParent[0]);return{top:e.top+this.offset.relative.top*t+this.offset.parent.top*t-("fixed"===this.cssPosition?-this.offset.scroll.top:i?0:this.offset.scroll.top)*t,left:e.left+this.offset.relative.left*t+this.offset.parent.left*t-("fixed"===this.cssPosition?-this.offset.scroll.left:i?0:this.offset.scroll.left)*t}},_generatePosition:function(t,e){var i,n=this.options,s=this._isRootNode(this.scrollParent[0]),o=t.pageX,r=t.pageY;return s&&this.offset.scroll||(this.offset.scroll={top:this.scrollParent.scrollTop(),left:this.scrollParent.scrollLeft()}),{top:(r=e&&(this.containment&&(i=this.relativeContainer?(e=this.relativeContainer.offset(),[this.containment[0]+e.left,this.containment[1]+e.top,this.containment[2]+e.left,this.containment[3]+e.top]):this.containment,t.pageX-this.offset.click.left<i[0]&&(o=i[0]+this.offset.click.left),t.pageY-this.offset.click.top<i[1]&&(r=i[1]+this.offset.click.top),t.pageX-this.offset.click.left>i[2]&&(o=i[2]+this.offset.click.left),t.pageY-this.offset.click.top>i[3])&&(r=i[3]+this.offset.click.top),n.grid&&(e=n.grid[1]?this.originalPageY+Math.round((r-this.originalPageY)/n.grid[1])*n.grid[1]:this.originalPageY,r=!i||e-this.offset.click.top>=i[1]||e-this.offset.click.top>i[3]?e:e-this.offset.click.top>=i[1]?e-n.grid[1]:e+n.grid[1],t=n.grid[0]?this.originalPageX+Math.round((o-this.originalPageX)/n.grid[0])*n.grid[0]:this.originalPageX,o=!i||t-this.offset.click.left>=i[0]||t-this.offset.click.left>i[2]?t:t-this.offset.click.left>=i[0]?t-n.grid[0]:t+n.grid[0]),"y"===n.axis&&(o=this.originalPageX),"x"===n.axis)?this.originalPageY:r)-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.offset.scroll.top:s?0:this.offset.scroll.top),left:o-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.offset.scroll.left:s?0:this.offset.scroll.left)}},_clear:function(){this._removeClass(this.helper,"ui-draggable-dragging"),this.helper[0]===this.element[0]||this.cancelHelperRemoval||this.helper.remove(),this.helper=null,this.cancelHelperRemoval=!1,this.destroyOnClear&&this.destroy()},_trigger:function(e,i,n){return n=n||this._uiHash(),t.ui.plugin.call(this,e,[i,n,this],!0),/^(drag|start|stop)/.test(e)&&(this.positionAbs=this._convertPositionTo("absolute"),n.offset=this.positionAbs),t.Widget.prototype._trigger.call(this,e,i,n)},plugins:{},_uiHash:function(){return{helper:this.helper,position:this.position,originalPosition:this.originalPosition,offset:this.positionAbs}}}),t.ui.plugin.add("draggable","connectToSortable",{start:function(e,i,n){var s=t.extend({},i,{item:n.element});n.sortables=[],t(n.options.connectToSortable).each((function(){var i=t(this).sortable("instance");i&&!i.options.disabled&&(n.sortables.push(i),i.refreshPositions(),i._trigger("activate",e,s))}))},stop:function(e,i,n){var s=t.extend({},i,{item:n.element});n.cancelHelperRemoval=!1,t.each(n.sortables,(function(){var t=this;t.isOver?(t.isOver=0,n.cancelHelperRemoval=!0,t.cancelHelperRemoval=!1,t._storedCSS={position:t.placeholder.css("position"),top:t.placeholder.css("top"),left:t.placeholder.css("left")},t._mouseStop(e),t.options.helper=t.options._helper):(t.cancelHelperRemoval=!0,t._trigger("deactivate",e,s))}))},drag:function(e,i,n){t.each(n.sortables,(function(){var s=!1,o=this;o.positionAbs=n.positionAbs,o.helperProportions=n.helperProportions,o.offset.click=n.offset.click,o._intersectsWith(o.containerCache)&&(s=!0,t.each(n.sortables,(function(){return this.positionAbs=n.positionAbs,this.helperProportions=n.helperProportions,this.offset.click=n.offset.click,s=(this===o||!this._intersectsWith(this.containerCache)||!t.contains(o.element[0],this.element[0]))&&s}))),s?(o.isOver||(o.isOver=1,n._parent=i.helper.parent(),o.currentItem=i.helper.appendTo(o.element).data("ui-sortable-item",!0),o.options._helper=o.options.helper,o.options.helper=function(){return i.helper[0]},e.target=o.currentItem[0],o._mouseCapture(e,!0),o._mouseStart(e,!0,!0),o.offset.click.top=n.offset.click.top,o.offset.click.left=n.offset.click.left,o.offset.parent.left-=n.offset.parent.left-o.offset.parent.left,o.offset.parent.top-=n.offset.parent.top-o.offset.parent.top,n._trigger("toSortable",e),n.dropped=o.element,t.each(n.sortables,(function(){this.refreshPositions()})),n.currentItem=n.element,o.fromOutside=n),o.currentItem&&(o._mouseDrag(e),i.position=o.position)):o.isOver&&(o.isOver=0,o.cancelHelperRemoval=!0,o.options._revert=o.options.revert,o.options.revert=!1,o._trigger("out",e,o._uiHash(o)),o._mouseStop(e,!0),o.options.revert=o.options._revert,o.options.helper=o.options._helper,o.placeholder&&o.placeholder.remove(),i.helper.appendTo(n._parent),n._refreshOffsets(e),i.position=n._generatePosition(e,!0),n._trigger("fromSortable",e),n.dropped=!1,t.each(n.sortables,(function(){this.refreshPositions()})))}))}}),t.ui.plugin.add("draggable","cursor",{start:function(e,i,n){var s=t("body");n=n.options,s.css("cursor")&&(n._cursor=s.css("cursor")),s.css("cursor",n.cursor)},stop:function(e,i,n){(n=n.options)._cursor&&t("body").css("cursor",n._cursor)}}),t.ui.plugin.add("draggable","opacity",{start:function(e,i,n){i=t(i.helper),n=n.options,i.css("opacity")&&(n._opacity=i.css("opacity")),i.css("opacity",n.opacity)},stop:function(e,i,n){(n=n.options)._opacity&&t(i.helper).css("opacity",n._opacity)}}),t.ui.plugin.add("draggable","scroll",{start:function(t,e,i){i.scrollParentNotHidden||(i.scrollParentNotHidden=i.helper.scrollParent(!1)),i.scrollParentNotHidden[0]!==i.document[0]&&"HTML"!==i.scrollParentNotHidden[0].tagName&&(i.overflowOffset=i.scrollParentNotHidden.offset())},drag:function(e,i,n){var s=n.options,o=!1,r=n.scrollParentNotHidden[0],a=n.document[0];r!==a&&"HTML"!==r.tagName?(s.axis&&"x"===s.axis||(n.overflowOffset.top+r.offsetHeight-e.pageY<s.scrollSensitivity?r.scrollTop=o=r.scrollTop+s.scrollSpeed:e.pageY-n.overflowOffset.top<s.scrollSensitivity&&(r.scrollTop=o=r.scrollTop-s.scrollSpeed)),s.axis&&"y"===s.axis||(n.overflowOffset.left+r.offsetWidth-e.pageX<s.scrollSensitivity?r.scrollLeft=o=r.scrollLeft+s.scrollSpeed:e.pageX-n.overflowOffset.left<s.scrollSensitivity&&(r.scrollLeft=o=r.scrollLeft-s.scrollSpeed))):(s.axis&&"x"===s.axis||(e.pageY-t(a).scrollTop()<s.scrollSensitivity?o=t(a).scrollTop(t(a).scrollTop()-s.scrollSpeed):t(window).height()-(e.pageY-t(a).scrollTop())<s.scrollSensitivity&&(o=t(a).scrollTop(t(a).scrollTop()+s.scrollSpeed))),s.axis&&"y"===s.axis||(e.pageX-t(a).scrollLeft()<s.scrollSensitivity?o=t(a).scrollLeft(t(a).scrollLeft()-s.scrollSpeed):t(window).width()-(e.pageX-t(a).scrollLeft())<s.scrollSensitivity&&(o=t(a).scrollLeft(t(a).scrollLeft()+s.scrollSpeed)))),!1!==o&&t.ui.ddmanager&&!s.dropBehaviour&&t.ui.ddmanager.prepareOffsets(n,e)}}),t.ui.plugin.add("draggable","snap",{start:function(e,i,n){var s=n.options;n.snapElements=[],t(s.snap.constructor!==String?s.snap.items||":data(ui-draggable)":s.snap).each((function(){var e=t(this),i=e.offset();this!==n.element[0]&&n.snapElements.push({item:this,width:e.outerWidth(),height:e.outerHeight(),top:i.top,left:i.left})}))},drag:function(e,i,n){for(var s,o,r,a,l,h,c,u,d,p=n.options,f=p.snapTolerance,g=i.offset.left,m=g+n.helperProportions.width,v=i.offset.top,_=v+n.helperProportions.height,b=n.snapElements.length-1;0<=b;b--)h=(l=n.snapElements[b].left-n.margins.left)+n.snapElements[b].width,u=(c=n.snapElements[b].top-n.margins.top)+n.snapElements[b].height,m<l-f||h+f<g||_<c-f||u+f<v||!t.contains(n.snapElements[b].item.ownerDocument,n.snapElements[b].item)?(n.snapElements[b].snapping&&n.options.snap.release&&n.options.snap.release.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[b].item})),n.snapElements[b].snapping=!1):("inner"!==p.snapMode&&(s=Math.abs(c-_)<=f,o=Math.abs(u-v)<=f,r=Math.abs(l-m)<=f,a=Math.abs(h-g)<=f,s&&(i.position.top=n._convertPositionTo("relative",{top:c-n.helperProportions.height,left:0}).top),o&&(i.position.top=n._convertPositionTo("relative",{top:u,left:0}).top),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l-n.helperProportions.width}).left),a)&&(i.position.left=n._convertPositionTo("relative",{top:0,left:h}).left),d=s||o||r||a,"outer"!==p.snapMode&&(s=Math.abs(c-v)<=f,o=Math.abs(u-_)<=f,r=Math.abs(l-g)<=f,a=Math.abs(h-m)<=f,s&&(i.position.top=n._convertPositionTo("relative",{top:c,left:0}).top),o&&(i.position.top=n._convertPositionTo("relative",{top:u-n.helperProportions.height,left:0}).top),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l}).left),a)&&(i.position.left=n._convertPositionTo("relative",{top:0,left:h-n.helperProportions.width}).left),!n.snapElements[b].snapping&&(s||o||r||a||d)&&n.options.snap.snap&&n.options.snap.snap.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[b].item})),n.snapElements[b].snapping=s||o||r||a||d)}}),t.ui.plugin.add("draggable","stack",{start:function(e,i,n){var s;n=n.options,n=t.makeArray(t(n.stack)).sort((function(e,i){return(parseInt(t(e).css("zIndex"),10)||0)-(parseInt(t(i).css("zIndex"),10)||0)})),n.length&&(s=parseInt(t(n[0]).css("zIndex"),10)||0,t(n).each((function(e){t(this).css("zIndex",s+e)})),this.css("zIndex",s+n.length))}}),t.ui.plugin.add("draggable","zIndex",{start:function(e,i,n){i=t(i.helper),n=n.options,i.css("zIndex")&&(n._zIndex=i.css("zIndex")),i.css("zIndex",n.zIndex)},stop:function(e,i,n){(n=n.options)._zIndex&&t(i.helper).css("zIndex",n._zIndex)}}),t.ui.draggable,t.widget("ui.resizable",t.ui.mouse,{version:"1.13.3",widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,classes:{"ui-resizable-se":"ui-icon ui-icon-gripsmall-diagonal-se"},containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:90,resize:null,start:null,stop:null},_num:function(t){return parseFloat(t)||0},_isNumber:function(t){return!isNaN(parseFloat(t))},_hasScroll:function(e,i){if("hidden"===t(e).css("overflow"))return!1;var n=!1;if(0<e[i=i&&"left"===i?"scrollLeft":"scrollTop"])return!0;try{e[i]=1,n=0<e[i],e[i]=0}catch(e){}return n},_create:function(){var e,i=this.options,n=this;this._addClass("ui-resizable"),t.extend(this,{_aspectRatio:!!i.aspectRatio,aspectRatio:i.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],_helper:i.helper||i.ghost||i.animate?i.helper||"ui-resizable-helper":null}),this.element[0].nodeName.match(/^(canvas|textarea|input|select|button|img)$/i)&&(this.element.wrap(t("<div class='ui-wrapper'></div>").css({overflow:"hidden",position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("ui-resizable",this.element.resizable("instance")),this.elementIsWrapper=!0,e={marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom"),marginLeft:this.originalElement.css("marginLeft")},this.element.css(e),this.originalElement.css("margin",0),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,display:"block"})),this.originalElement.css(e),this._proportionallyResize()),this._setupHandles(),i.autoHide&&t(this.element).on("mouseenter",(function(){i.disabled||(n._removeClass("ui-resizable-autohide"),n._handles.show())})).on("mouseleave",(function(){i.disabled||n.resizing||(n._addClass("ui-resizable-autohide"),n._handles.hide())})),this._mouseInit()},_destroy:function(){function e(e){t(e).removeData("resizable").removeData("ui-resizable").off(".resizable")}var i;return this._mouseDestroy(),this._addedHandles.remove(),this.elementIsWrapper&&(e(this.element),i=this.element,this.originalElement.css({position:i.css("position"),width:i.outerWidth(),height:i.outerHeight(),top:i.css("top"),left:i.css("left")}).insertAfter(i),i.remove()),this.originalElement.css("resize",this.originalResizeStyle),e(this.originalElement),this},_setOption:function(t,e){switch(this._super(t,e),t){case"handles":this._removeHandles(),this._setupHandles();break;case"aspectRatio":this._aspectRatio=!!e}},_setupHandles:function(){var e,i,n,s,o,r=this.options,a=this;if(this.handles=r.handles||(t(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se"),this._handles=t(),this._addedHandles=t(),this.handles.constructor===String)for("all"===this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw"),n=this.handles.split(","),this.handles={},i=0;i<n.length;i++)s="ui-resizable-"+(e=String.prototype.trim.call(n[i])),o=t("<div>"),this._addClass(o,"ui-resizable-handle "+s),o.css({zIndex:r.zIndex}),this.handles[e]=".ui-resizable-"+e,this.element.children(this.handles[e]).length||(this.element.append(o),this._addedHandles=this._addedHandles.add(o));this._renderAxis=function(e){var i,n,s;for(i in e=e||this.element,this.handles)this.handles[i].constructor===String?this.handles[i]=this.element.children(this.handles[i]).first().show():(this.handles[i].jquery||this.handles[i].nodeType)&&(this.handles[i]=t(this.handles[i]),this._on(this.handles[i],{mousedown:a._mouseDown})),this.elementIsWrapper&&this.originalElement[0].nodeName.match(/^(textarea|input|select|button)$/i)&&(s=t(this.handles[i],this.element),s=/sw|ne|nw|se|n|s/.test(i)?s.outerHeight():s.outerWidth(),n=["padding",/ne|nw|n/.test(i)?"Top":/se|sw|s/.test(i)?"Bottom":/^e$/.test(i)?"Right":"Left"].join(""),e.css(n,s),this._proportionallyResize()),this._handles=this._handles.add(this.handles[i])},this._renderAxis(this.element),this._handles=this._handles.add(this.element.find(".ui-resizable-handle")),this._handles.disableSelection(),this._handles.on("mouseover",(function(){a.resizing||(this.className&&(o=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)),a.axis=o&&o[1]?o[1]:"se")})),r.autoHide&&(this._handles.hide(),this._addClass("ui-resizable-autohide"))},_removeHandles:function(){this._addedHandles.remove()},_mouseCapture:function(e){var i,n,s=!1;for(i in this.handles)(n=t(this.handles[i])[0])!==e.target&&!t.contains(n,e.target)||(s=!0);return!this.options.disabled&&s},_mouseStart:function(e){var i,n,s=this.options,o=this.element;return this.resizing=!0,this._renderProxy(),i=this._num(this.helper.css("left")),n=this._num(this.helper.css("top")),s.containment&&(i+=t(s.containment).scrollLeft()||0,n+=t(s.containment).scrollTop()||0),this.offset=this.helper.offset(),this.position={left:i,top:n},this.size=this._helper?{width:this.helper.width(),height:this.helper.height()}:{width:o.width(),height:o.height()},this.originalSize=this._helper?{width:o.outerWidth(),height:o.outerHeight()}:{width:o.width(),height:o.height()},this.sizeDiff={width:o.outerWidth()-o.width(),height:o.outerHeight()-o.height()},this.originalPosition={left:i,top:n},this.originalMousePosition={left:e.pageX,top:e.pageY},this.aspectRatio="number"==typeof s.aspectRatio?s.aspectRatio:this.originalSize.width/this.originalSize.height||1,o=t(".ui-resizable-"+this.axis).css("cursor"),t("body").css("cursor","auto"===o?this.axis+"-resize":o),this._addClass("ui-resizable-resizing"),this._propagate("start",e),!0},_mouseDrag:function(e){var i=this.originalMousePosition,n=this.axis,s=e.pageX-i.left||0;return i=e.pageY-i.top||0,n=this._change[n],this._updatePrevProperties(),n&&(n=n.apply(this,[e,s,i]),this._updateVirtualBoundaries(e.shiftKey),(this._aspectRatio||e.shiftKey)&&(n=this._updateRatio(n,e)),n=this._respectSize(n,e),this._updateCache(n),this._propagate("resize",e),s=this._applyChanges(),!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize(),t.isEmptyObject(s)||(this._updatePrevProperties(),this._trigger("resize",e,this.ui()),this._applyChanges())),!1},_mouseStop:function(e){this.resizing=!1;var i,n,s,o=this.options,r=this;return this._helper&&(n=(i=(n=this._proportionallyResizeElements).length&&/textarea/i.test(n[0].nodeName))&&this._hasScroll(n[0],"left")?0:r.sizeDiff.height,i=i?0:r.sizeDiff.width,i={width:r.helper.width()-i,height:r.helper.height()-n},n=parseFloat(r.element.css("left"))+(r.position.left-r.originalPosition.left)||null,s=parseFloat(r.element.css("top"))+(r.position.top-r.originalPosition.top)||null,o.animate||this.element.css(t.extend(i,{top:s,left:n})),r.helper.height(r.size.height),r.helper.width(r.size.width),this._helper)&&!o.animate&&this._proportionallyResize(),t("body").css("cursor","auto"),this._removeClass("ui-resizable-resizing"),this._propagate("stop",e),this._helper&&this.helper.remove(),!1},_updatePrevProperties:function(){this.prevPosition={top:this.position.top,left:this.position.left},this.prevSize={width:this.size.width,height:this.size.height}},_applyChanges:function(){var t={};return this.position.top!==this.prevPosition.top&&(t.top=this.position.top+"px"),this.position.left!==this.prevPosition.left&&(t.left=this.position.left+"px"),this.helper.css(t),this.size.width!==this.prevSize.width&&(t.width=this.size.width+"px",this.helper.width(t.width)),this.size.height!==this.prevSize.height&&(t.height=this.size.height+"px",this.helper.height(t.height)),t},_updateVirtualBoundaries:function(t){var e,i,n,s=this.options;s={minWidth:this._isNumber(s.minWidth)?s.minWidth:0,maxWidth:this._isNumber(s.maxWidth)?s.maxWidth:1/0,minHeight:this._isNumber(s.minHeight)?s.minHeight:0,maxHeight:this._isNumber(s.maxHeight)?s.maxHeight:1/0},(this._aspectRatio||t)&&(t=s.minHeight*this.aspectRatio,i=s.minWidth/this.aspectRatio,e=s.maxHeight*this.aspectRatio,n=s.maxWidth/this.aspectRatio,s.minWidth<t&&(s.minWidth=t),s.minHeight<i&&(s.minHeight=i),e<s.maxWidth&&(s.maxWidth=e),n<s.maxHeight)&&(s.maxHeight=n),this._vBoundaries=s},_updateCache:function(t){this.offset=this.helper.offset(),this._isNumber(t.left)&&(this.position.left=t.left),this._isNumber(t.top)&&(this.position.top=t.top),this._isNumber(t.height)&&(this.size.height=t.height),this._isNumber(t.width)&&(this.size.width=t.width)},_updateRatio:function(t){var e=this.position,i=this.size,n=this.axis;return this._isNumber(t.height)?t.width=t.height*this.aspectRatio:this._isNumber(t.width)&&(t.height=t.width/this.aspectRatio),"sw"===n&&(t.left=e.left+(i.width-t.width),t.top=null),"nw"===n&&(t.top=e.top+(i.height-t.height),t.left=e.left+(i.width-t.width)),t},_respectSize:function(t){var e=this._vBoundaries,i=this.axis,n=this._isNumber(t.width)&&e.maxWidth&&e.maxWidth<t.width,s=this._isNumber(t.height)&&e.maxHeight&&e.maxHeight<t.height,o=this._isNumber(t.width)&&e.minWidth&&e.minWidth>t.width,r=this._isNumber(t.height)&&e.minHeight&&e.minHeight>t.height,a=this.originalPosition.left+this.originalSize.width,l=this.originalPosition.top+this.originalSize.height,h=/sw|nw|w/.test(i);return i=/nw|ne|n/.test(i),o&&(t.width=e.minWidth),r&&(t.height=e.minHeight),n&&(t.width=e.maxWidth),s&&(t.height=e.maxHeight),o&&h&&(t.left=a-e.minWidth),n&&h&&(t.left=a-e.maxWidth),r&&i&&(t.top=l-e.minHeight),s&&i&&(t.top=l-e.maxHeight),t.width||t.height||t.left||!t.top?t.width||t.height||t.top||!t.left||(t.left=null):t.top=null,t},_getPaddingPlusBorderDimensions:function(t){for(var e=0,i=[],n=[t.css("borderTopWidth"),t.css("borderRightWidth"),t.css("borderBottomWidth"),t.css("borderLeftWidth")],s=[t.css("paddingTop"),t.css("paddingRight"),t.css("paddingBottom"),t.css("paddingLeft")];e<4;e++)i[e]=parseFloat(n[e])||0,i[e]+=parseFloat(s[e])||0;return{height:i[0]+i[2],width:i[1]+i[3]}},_proportionallyResize:function(){if(this._proportionallyResizeElements.length)for(var t,e=0,i=this.helper||this.element;e<this._proportionallyResizeElements.length;e++)t=this._proportionallyResizeElements[e],this.outerDimensions||(this.outerDimensions=this._getPaddingPlusBorderDimensions(t)),t.css({height:i.height()-this.outerDimensions.height||0,width:i.width()-this.outerDimensions.width||0})},_renderProxy:function(){var e=this.element,i=this.options;this.elementOffset=e.offset(),this._helper?(this.helper=this.helper||t("<div></div>").css({overflow:"hidden"}),this._addClass(this.helper,this._helper),this.helper.css({width:this.element.outerWidth(),height:this.element.outerHeight(),position:"absolute",left:this.elementOffset.left+"px",top:this.elementOffset.top+"px",zIndex:++i.zIndex}),this.helper.appendTo("body").disableSelection()):this.helper=this.element},_change:{e:function(t,e){return{width:this.originalSize.width+e}},w:function(t,e){var i=this.originalSize;return{left:this.originalPosition.left+e,width:i.width-e}},n:function(t,e,i){var n=this.originalSize;return{top:this.originalPosition.top+i,height:n.height-i}},s:function(t,e,i){return{height:this.originalSize.height+i}},se:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},sw:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[e,i,n]))},ne:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},nw:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[e,i,n]))}},_propagate:function(e,i){t.ui.plugin.call(this,e,[i,this.ui()]),"resize"!==e&&this._trigger(e,i,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}}),t.ui.plugin.add("resizable","animate",{stop:function(e){var i=t(this).resizable("instance"),n=i.options,s=i._proportionallyResizeElements,o=(r=s.length&&/textarea/i.test(s[0].nodeName))&&i._hasScroll(s[0],"left")?0:i.sizeDiff.height,r=r?0:i.sizeDiff.width,a=(r={width:i.size.width-r,height:i.size.height-o},o=parseFloat(i.element.css("left"))+(i.position.left-i.originalPosition.left)||null,parseFloat(i.element.css("top"))+(i.position.top-i.originalPosition.top)||null);i.element.animate(t.extend(r,a&&o?{top:a,left:o}:{}),{duration:n.animateDuration,easing:n.animateEasing,step:function(){var n={width:parseFloat(i.element.css("width")),height:parseFloat(i.element.css("height")),top:parseFloat(i.element.css("top")),left:parseFloat(i.element.css("left"))};s&&s.length&&t(s[0]).css({width:n.width,height:n.height}),i._updateCache(n),i._propagate("resize",e)}})}}),t.ui.plugin.add("resizable","containment",{start:function(){var e,i,n,s,o=t(this).resizable("instance"),r=o.options,a=o.element;(a=(r=r.containment)instanceof t?r.get(0):/parent/.test(r)?a.parent().get(0):r)&&(o.containerElement=t(a),/document/.test(r)||r===document?(o.containerOffset={left:0,top:0},o.containerPosition={left:0,top:0},o.parentData={element:t(document),left:0,top:0,width:t(document).width(),height:t(document).height()||document.body.parentNode.scrollHeight}):(e=t(a),i=[],t(["Top","Right","Left","Bottom"]).each((function(t,n){i[t]=o._num(e.css("padding"+n))})),o.containerOffset=e.offset(),o.containerPosition=e.position(),o.containerSize={height:e.innerHeight()-i[3],width:e.innerWidth()-i[1]},r=o.containerOffset,s=o.containerSize.height,n=o.containerSize.width,n=o._hasScroll(a,"left")?a.scrollWidth:n,s=o._hasScroll(a)?a.scrollHeight:s,o.parentData={element:a,left:r.left,top:r.top,width:n,height:s}))},resize:function(e){var i=t(this).resizable("instance"),n=i.options,s=i.containerOffset,o=i.position,r=(e=i._aspectRatio||e.shiftKey,{top:0,left:0}),a=i.containerElement,l=!0;a[0]!==document&&/static/.test(a.css("position"))&&(r=s),o.left<(i._helper?s.left:0)&&(i.size.width=i.size.width+(i._helper?i.position.left-s.left:i.position.left-r.left),e&&(i.size.height=i.size.width/i.aspectRatio,l=!1),i.position.left=n.helper?s.left:0),o.top<(i._helper?s.top:0)&&(i.size.height=i.size.height+(i._helper?i.position.top-s.top:i.position.top),e&&(i.size.width=i.size.height*i.aspectRatio,l=!1),i.position.top=i._helper?s.top:0),a=i.containerElement.get(0)===i.element.parent().get(0),n=/relative|absolute/.test(i.containerElement.css("position")),a&&n?(i.offset.left=i.parentData.left+i.position.left,i.offset.top=i.parentData.top+i.position.top):(i.offset.left=i.element.offset().left,i.offset.top=i.element.offset().top),o=Math.abs(i.sizeDiff.width+(i._helper?i.offset.left-r.left:i.offset.left-s.left)),a=Math.abs(i.sizeDiff.height+(i._helper?i.offset.top-r.top:i.offset.top-s.top)),o+i.size.width>=i.parentData.width&&(i.size.width=i.parentData.width-o,e)&&(i.size.height=i.size.width/i.aspectRatio,l=!1),a+i.size.height>=i.parentData.height&&(i.size.height=i.parentData.height-a,e)&&(i.size.width=i.size.height*i.aspectRatio,l=!1),l||(i.position.left=i.prevPosition.left,i.position.top=i.prevPosition.top,i.size.width=i.prevSize.width,i.size.height=i.prevSize.height)},stop:function(){var e=t(this).resizable("instance"),i=e.options,n=e.containerOffset,s=e.containerPosition,o=e.containerElement,r=(l=t(e.helper)).offset(),a=l.outerWidth()-e.sizeDiff.width,l=l.outerHeight()-e.sizeDiff.height;e._helper&&!i.animate&&/relative/.test(o.css("position"))&&t(this).css({left:r.left-s.left-n.left,width:a,height:l}),e._helper&&!i.animate&&/static/.test(o.css("position"))&&t(this).css({left:r.left-s.left-n.left,width:a,height:l})}}),t.ui.plugin.add("resizable","alsoResize",{start:function(){var e=t(this).resizable("instance").options;t(e.alsoResize).each((function(){var e=t(this);e.data("ui-resizable-alsoresize",{width:parseFloat(e.css("width")),height:parseFloat(e.css("height")),left:parseFloat(e.css("left")),top:parseFloat(e.css("top"))})}))},resize:function(e,i){var n=t(this).resizable("instance"),s=n.options,o=n.originalSize,r=n.originalPosition,a={height:n.size.height-o.height||0,width:n.size.width-o.width||0,top:n.position.top-r.top||0,left:n.position.left-r.left||0};t(s.alsoResize).each((function(){var e=t(this),n=t(this).data("ui-resizable-alsoresize"),s={},o=e.parents(i.originalElement[0]).length?["width","height"]:["width","height","top","left"];t.each(o,(function(t,e){var i=(n[e]||0)+(a[e]||0);i&&0<=i&&(s[e]=i||null)})),e.css(s)}))},stop:function(){t(this).removeData("ui-resizable-alsoresize")}}),t.ui.plugin.add("resizable","ghost",{start:function(){var e=t(this).resizable("instance"),i=e.size;e.ghost=e.originalElement.clone(),e.ghost.css({opacity:.25,display:"block",position:"relative",height:i.height,width:i.width,margin:0,left:0,top:0}),e._addClass(e.ghost,"ui-resizable-ghost"),!1!==t.uiBackCompat&&"string"==typeof e.options.ghost&&e.ghost.addClass(this.options.ghost),e.ghost.appendTo(e.helper)},resize:function(){var e=t(this).resizable("instance");e.ghost&&e.ghost.css({position:"relative",height:e.size.height,width:e.size.width})},stop:function(){var e=t(this).resizable("instance");e.ghost&&e.helper&&e.helper.get(0).removeChild(e.ghost.get(0))}}),t.ui.plugin.add("resizable","grid",{resize:function(){var e,i=t(this).resizable("instance"),n=i.options,s=i.size,o=i.originalSize,r=i.originalPosition,a=i.axis,l="number"==typeof n.grid?[n.grid,n.grid]:n.grid,h=l[0]||1,c=l[1]||1,u=Math.round((s.width-o.width)/h)*h,d=(s=Math.round((s.height-o.height)/c)*c,o.width+u),p=o.height+s,f=n.maxWidth&&n.maxWidth<d,g=n.maxHeight&&n.maxHeight<p,m=n.minWidth&&n.minWidth>d,v=n.minHeight&&n.minHeight>p;n.grid=l,m&&(d+=h),v&&(p+=c),f&&(d-=h),g&&(p-=c),/^(se|s|e)$/.test(a)?(i.size.width=d,i.size.height=p):/^(ne)$/.test(a)?(i.size.width=d,i.size.height=p,i.position.top=r.top-s):/^(sw)$/.test(a)?(i.size.width=d,i.size.height=p,i.position.left=r.left-u):((p-c<=0||d-h<=0)&&(e=i._getPaddingPlusBorderDimensions(this)),0<p-c?(i.size.height=p,i.position.top=r.top-s):(p=c-e.height,i.size.height=p,i.position.top=r.top+o.height-p),0<d-h?(i.size.width=d,i.position.left=r.left-u):(d=h-e.width,i.size.width=d,i.position.left=r.left+o.width-d))}}),t.ui.resizable,t.widget("ui.dialog",{version:"1.13.3",options:{appendTo:"body",autoOpen:!0,buttons:[],classes:{"ui-dialog":"ui-corner-all","ui-dialog-titlebar":"ui-corner-all"},closeOnEscape:!0,closeText:"Close",draggable:!0,hide:null,height:"auto",maxHeight:null,maxWidth:null,minHeight:150,minWidth:150,modal:!1,position:{my:"center",at:"center",of:window,collision:"fit",using:function(e){var i=t(this).css(e).offset().top;i<0&&t(this).css("top",e.top-i)}},resizable:!0,show:null,title:null,width:300,beforeClose:null,close:null,drag:null,dragStart:null,dragStop:null,focus:null,open:null,resize:null,resizeStart:null,resizeStop:null},sizeRelatedOptions:{buttons:!0,height:!0,maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0,width:!0},resizableRelatedOptions:{maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0},_create:function(){this.originalCss={display:this.element[0].style.display,width:this.element[0].style.width,minHeight:this.element[0].style.minHeight,maxHeight:this.element[0].style.maxHeight,height:this.element[0].style.height},this.originalPosition={parent:this.element.parent(),index:this.element.parent().children().index(this.element)},this.originalTitle=this.element.attr("title"),null==this.options.title&&null!=this.originalTitle&&(this.options.title=this.originalTitle),this.options.disabled&&(this.options.disabled=!1),this._createWrapper(),this.element.show().removeAttr("title").appendTo(this.uiDialog),this._addClass("ui-dialog-content","ui-widget-content"),this._createTitlebar(),this._createButtonPane(),this.options.draggable&&t.fn.draggable&&this._makeDraggable(),this.options.resizable&&t.fn.resizable&&this._makeResizable(),this._isOpen=!1,this._trackFocus()},_init:function(){this.options.autoOpen&&this.open()},_appendTo:function(){var e=this.options.appendTo;return e&&(e.jquery||e.nodeType)?t(e):this.document.find(e||"body").eq(0)},_destroy:function(){var t,e=this.originalPosition;this._untrackInstance(),this._destroyOverlay(),this.element.removeUniqueId().css(this.originalCss).detach(),this.uiDialog.remove(),this.originalTitle&&this.element.attr("title",this.originalTitle),(t=e.parent.children().eq(e.index)).length&&t[0]!==this.element[0]?t.before(this.element):e.parent.append(this.element)},widget:function(){return this.uiDialog},disable:t.noop,enable:t.noop,close:function(e){var i=this;this._isOpen&&!1!==this._trigger("beforeClose",e)&&(this._isOpen=!1,this._focusedElement=null,this._destroyOverlay(),this._untrackInstance(),this.opener.filter(":focusable").trigger("focus").length||t.ui.safeBlur(t.ui.safeActiveElement(this.document[0])),this._hide(this.uiDialog,this.options.hide,(function(){i._trigger("close",e)})))},isOpen:function(){return this._isOpen},moveToTop:function(){this._moveToTop()},_moveToTop:function(e,i){var n=!1,s=this.uiDialog.siblings(".ui-front:visible").map((function(){return+t(this).css("z-index")})).get();return(s=Math.max.apply(null,s))>=+this.uiDialog.css("z-index")&&(this.uiDialog.css("z-index",s+1),n=!0),n&&!i&&this._trigger("focus",e),n},open:function(){var e=this;this._isOpen?this._moveToTop()&&this._focusTabbable():(this._isOpen=!0,this.opener=t(t.ui.safeActiveElement(this.document[0])),this._size(),this._position(),this._createOverlay(),this._moveToTop(null,!0),this.overlay&&this.overlay.css("z-index",this.uiDialog.css("z-index")-1),this._show(this.uiDialog,this.options.show,(function(){e._focusTabbable(),e._trigger("focus")})),this._makeFocusTarget(),this._trigger("open"))},_focusTabbable:function(){var t=this._focusedElement;(t=(t=(t=(t=(t=t||this.element.find("[autofocus]")).length?t:this.element.find(":tabbable")).length?t:this.uiDialogButtonPane.find(":tabbable")).length?t:this.uiDialogTitlebarClose.filter(":tabbable")).length?t:this.uiDialog).eq(0).trigger("focus")},_restoreTabbableFocus:function(){var e=t.ui.safeActiveElement(this.document[0]);this.uiDialog[0]===e||t.contains(this.uiDialog[0],e)||this._focusTabbable()},_keepFocus:function(t){t.preventDefault(),this._restoreTabbableFocus(),this._delay(this._restoreTabbableFocus)},_createWrapper:function(){this.uiDialog=t("<div>").hide().attr({tabIndex:-1,role:"dialog"}).appendTo(this._appendTo()),this._addClass(this.uiDialog,"ui-dialog","ui-widget ui-widget-content ui-front"),this._on(this.uiDialog,{keydown:function(e){var i,n,s;this.options.closeOnEscape&&!e.isDefaultPrevented()&&e.keyCode&&e.keyCode===t.ui.keyCode.ESCAPE?(e.preventDefault(),this.close(e)):e.keyCode!==t.ui.keyCode.TAB||e.isDefaultPrevented()||(i=this.uiDialog.find(":tabbable"),n=i.first(),s=i.last(),e.target!==s[0]&&e.target!==this.uiDialog[0]||e.shiftKey?e.target!==n[0]&&e.target!==this.uiDialog[0]||!e.shiftKey||(this._delay((function(){s.trigger("focus")})),e.preventDefault()):(this._delay((function(){n.trigger("focus")})),e.preventDefault()))},mousedown:function(t){this._moveToTop(t)&&this._focusTabbable()}}),this.element.find("[aria-describedby]").length||this.uiDialog.attr({"aria-describedby":this.element.uniqueId().attr("id")})},_createTitlebar:function(){var e;this.uiDialogTitlebar=t("<div>"),this._addClass(this.uiDialogTitlebar,"ui-dialog-titlebar","ui-widget-header ui-helper-clearfix"),this._on(this.uiDialogTitlebar,{mousedown:function(e){t(e.target).closest(".ui-dialog-titlebar-close")||this.uiDialog.trigger("focus")}}),this.uiDialogTitlebarClose=t("<button type='button'></button>").button({label:t("<a>").text(this.options.closeText).html(),icon:"ui-icon-closethick",showLabel:!1}).appendTo(this.uiDialogTitlebar),this._addClass(this.uiDialogTitlebarClose,"ui-dialog-titlebar-close"),this._on(this.uiDialogTitlebarClose,{click:function(t){t.preventDefault(),this.close(t)}}),e=t("<span>").uniqueId().prependTo(this.uiDialogTitlebar),this._addClass(e,"ui-dialog-title"),this._title(e),this.uiDialogTitlebar.prependTo(this.uiDialog),this.uiDialog.attr({"aria-labelledby":e.attr("id")})},_title:function(t){this.options.title?t.text(this.options.title):t.html("&#160;")},_createButtonPane:function(){this.uiDialogButtonPane=t("<div>"),this._addClass(this.uiDialogButtonPane,"ui-dialog-buttonpane","ui-widget-content ui-helper-clearfix"),this.uiButtonSet=t("<div>").appendTo(this.uiDialogButtonPane),this._addClass(this.uiButtonSet,"ui-dialog-buttonset"),this._createButtons()},_createButtons:function(){var e=this,i=this.options.buttons;this.uiDialogButtonPane.remove(),this.uiButtonSet.empty(),t.isEmptyObject(i)||Array.isArray(i)&&!i.length?this._removeClass(this.uiDialog,"ui-dialog-buttons"):(t.each(i,(function(i,n){var s;n=t.extend({type:"button"},n="function"==typeof n?{click:n,text:i}:n),s=n.click,i={icon:n.icon,iconPosition:n.iconPosition,showLabel:n.showLabel,icons:n.icons,text:n.text},delete n.click,delete n.icon,delete n.iconPosition,delete n.showLabel,delete n.icons,"boolean"==typeof n.text&&delete n.text,t("<button></button>",n).button(i).appendTo(e.uiButtonSet).on("click",(function(){s.apply(e.element[0],arguments)}))})),this._addClass(this.uiDialog,"ui-dialog-buttons"),this.uiDialogButtonPane.appendTo(this.uiDialog))},_makeDraggable:function(){var e=this,i=this.options;function n(t){return{position:t.position,offset:t.offset}}this.uiDialog.draggable({cancel:".ui-dialog-content, .ui-dialog-titlebar-close",handle:".ui-dialog-titlebar",containment:"document",start:function(i,s){e._addClass(t(this),"ui-dialog-dragging"),e._blockFrames(),e._trigger("dragStart",i,n(s))},drag:function(t,i){e._trigger("drag",t,n(i))},stop:function(s,o){var r=o.offset.left-e.document.scrollLeft(),a=o.offset.top-e.document.scrollTop();i.position={my:"left top",at:"left"+(0<=r?"+":"")+r+" top"+(0<=a?"+":"")+a,of:e.window},e._removeClass(t(this),"ui-dialog-dragging"),e._unblockFrames(),e._trigger("dragStop",s,n(o))}})},_makeResizable:function(){var e=this,i=this.options,n=i.resizable,s=this.uiDialog.css("position");function o(t){return{originalPosition:t.originalPosition,originalSize:t.originalSize,position:t.position,size:t.size}}n="string"==typeof n?n:"n,e,s,w,se,sw,ne,nw",this.uiDialog.resizable({cancel:".ui-dialog-content",containment:"document",alsoResize:this.element,maxWidth:i.maxWidth,maxHeight:i.maxHeight,minWidth:i.minWidth,minHeight:this._minHeight(),handles:n,start:function(i,n){e._addClass(t(this),"ui-dialog-resizing"),e._blockFrames(),e._trigger("resizeStart",i,o(n))},resize:function(t,i){e._trigger("resize",t,o(i))},stop:function(n,s){var r=(a=e.uiDialog.offset()).left-e.document.scrollLeft(),a=a.top-e.document.scrollTop();i.height=e.uiDialog.height(),i.width=e.uiDialog.width(),i.position={my:"left top",at:"left"+(0<=r?"+":"")+r+" top"+(0<=a?"+":"")+a,of:e.window},e._removeClass(t(this),"ui-dialog-resizing"),e._unblockFrames(),e._trigger("resizeStop",n,o(s))}}).css("position",s)},_trackFocus:function(){this._on(this.widget(),{focusin:function(e){this._makeFocusTarget(),this._focusedElement=t(e.target)}})},_makeFocusTarget:function(){this._untrackInstance(),this._trackingInstances().unshift(this)},_untrackInstance:function(){var e=this._trackingInstances(),i=t.inArray(this,e);-1!==i&&e.splice(i,1)},_trackingInstances:function(){var t=this.document.data("ui-dialog-instances");return t||this.document.data("ui-dialog-instances",t=[]),t},_minHeight:function(){var t=this.options;return"auto"===t.height?t.minHeight:Math.min(t.minHeight,t.height)},_position:function(){var t=this.uiDialog.is(":visible");t||this.uiDialog.show(),this.uiDialog.position(this.options.position),t||this.uiDialog.hide()},_setOptions:function(e){var i=this,n=!1,s={};t.each(e,(function(t,e){i._setOption(t,e),t in i.sizeRelatedOptions&&(n=!0),t in i.resizableRelatedOptions&&(s[t]=e)})),n&&(this._size(),this._position()),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option",s)},_setOption:function(e,i){var n,s=this.uiDialog;"disabled"!==e&&(this._super(e,i),"appendTo"===e&&this.uiDialog.appendTo(this._appendTo()),"buttons"===e&&this._createButtons(),"closeText"===e&&this.uiDialogTitlebarClose.button({label:t("<a>").text(""+this.options.closeText).html()}),"draggable"===e&&((n=s.is(":data(ui-draggable)"))&&!i&&s.draggable("destroy"),!n)&&i&&this._makeDraggable(),"position"===e&&this._position(),"resizable"===e&&((n=s.is(":data(ui-resizable)"))&&!i&&s.resizable("destroy"),n&&"string"==typeof i&&s.resizable("option","handles",i),n||!1===i||this._makeResizable()),"title"===e)&&this._title(this.uiDialogTitlebar.find(".ui-dialog-title"))},_size:function(){var t,e,i,n=this.options;this.element.show().css({width:"auto",minHeight:0,maxHeight:"none",height:0}),n.minWidth>n.width&&(n.width=n.minWidth),t=this.uiDialog.css({height:"auto",width:n.width}).outerHeight(),e=Math.max(0,n.minHeight-t),i="number"==typeof n.maxHeight?Math.max(0,n.maxHeight-t):"none","auto"===n.height?this.element.css({minHeight:e,maxHeight:i,height:"auto"}):this.element.height(Math.max(0,n.height-t)),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option","minHeight",this._minHeight())},_blockFrames:function(){this.iframeBlocks=this.document.find("iframe").map((function(){var e=t(this);return t("<div>").css({position:"absolute",width:e.outerWidth(),height:e.outerHeight()}).appendTo(e.parent()).offset(e.offset())[0]}))},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_allowInteraction:function(e){return!!t(e.target).closest(".ui-dialog").length||!!t(e.target).closest(".ui-datepicker").length},_createOverlay:function(){var e,i;this.options.modal&&(e=t.fn.jquery.substring(0,4),i=!0,this._delay((function(){i=!1})),this.document.data("ui-dialog-overlays")||this.document.on("focusin.ui-dialog",function(t){var n;i||(n=this._trackingInstances()[0])._allowInteraction(t)||(t.preventDefault(),n._focusTabbable(),"3.4."!==e&&"3.5."!==e&&"3.6."!==e)||n._delay(n._restoreTabbableFocus)}.bind(this)),this.overlay=t("<div>").appendTo(this._appendTo()),this._addClass(this.overlay,null,"ui-widget-overlay ui-front"),this._on(this.overlay,{mousedown:"_keepFocus"}),this.document.data("ui-dialog-overlays",(this.document.data("ui-dialog-overlays")||0)+1))},_destroyOverlay:function(){var t;this.options.modal&&this.overlay&&((t=this.document.data("ui-dialog-overlays")-1)?this.document.data("ui-dialog-overlays",t):(this.document.off("focusin.ui-dialog"),this.document.removeData("ui-dialog-overlays")),this.overlay.remove(),this.overlay=null)}}),!1!==t.uiBackCompat&&t.widget("ui.dialog",t.ui.dialog,{options:{dialogClass:""},_createWrapper:function(){this._super(),this.uiDialog.addClass(this.options.dialogClass)},_setOption:function(t,e){"dialogClass"===t&&this.uiDialog.removeClass(this.options.dialogClass).addClass(e),this._superApply(arguments)}}),t.ui.dialog,t.widget("ui.droppable",{version:"1.13.3",widgetEventPrefix:"drop",options:{accept:"*",addClasses:!0,greedy:!1,scope:"default",tolerance:"intersect",activate:null,deactivate:null,drop:null,out:null,over:null},_create:function(){var t,e=this.options,i=e.accept;this.isover=!1,this.isout=!0,this.accept="function"==typeof i?i:function(t){return t.is(i)},this.proportions=function(){if(!arguments.length)return t=t||{width:this.element[0].offsetWidth,height:this.element[0].offsetHeight};t=arguments[0]},this._addToManager(e.scope),e.addClasses&&this._addClass("ui-droppable")},_addToManager:function(e){t.ui.ddmanager.droppables[e]=t.ui.ddmanager.droppables[e]||[],t.ui.ddmanager.droppables[e].push(this)},_splice:function(t){for(var e=0;e<t.length;e++)t[e]===this&&t.splice(e,1)},_destroy:function(){var e=t.ui.ddmanager.droppables[this.options.scope];this._splice(e)},_setOption:function(e,i){var n;"accept"===e?this.accept="function"==typeof i?i:function(t){return t.is(i)}:"scope"===e&&(n=t.ui.ddmanager.droppables[this.options.scope],this._splice(n),this._addToManager(i)),this._super(e,i)},_activate:function(e){var i=t.ui.ddmanager.current;this._addActiveClass(),i&&this._trigger("activate",e,this.ui(i))},_deactivate:function(e){var i=t.ui.ddmanager.current;this._removeActiveClass(),i&&this._trigger("deactivate",e,this.ui(i))},_over:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this._addHoverClass(),this._trigger("over",e,this.ui(i)))},_out:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this._removeHoverClass(),this._trigger("out",e,this.ui(i)))},_drop:function(e,i){var n=i||t.ui.ddmanager.current,s=!1;return!(!n||(n.currentItem||n.element)[0]===this.element[0]||(this.element.find(":data(ui-droppable)").not(".ui-draggable-dragging").each((function(){var i=t(this).droppable("instance");if(i.options.greedy&&!i.options.disabled&&i.options.scope===n.options.scope&&i.accept.call(i.element[0],n.currentItem||n.element)&&t.ui.intersect(n,t.extend(i,{offset:i.element.offset()}),i.options.tolerance,e))return!(s=!0)})),s)||!this.accept.call(this.element[0],n.currentItem||n.element))&&(this._removeActiveClass(),this._removeHoverClass(),this._trigger("drop",e,this.ui(n)),this.element)},ui:function(t){return{draggable:t.currentItem||t.element,helper:t.helper,position:t.position,offset:t.positionAbs}},_addHoverClass:function(){this._addClass("ui-droppable-hover")},_removeHoverClass:function(){this._removeClass("ui-droppable-hover")},_addActiveClass:function(){this._addClass("ui-droppable-active")},_removeActiveClass:function(){this._removeClass("ui-droppable-active")}}),t.ui.intersect=function(t,e,i,n){if(!e.offset)return!1;var s=(t.positionAbs||t.position.absolute).left+t.margins.left,o=(t.positionAbs||t.position.absolute).top+t.margins.top,r=s+t.helperProportions.width,a=o+t.helperProportions.height,l=e.offset.left,h=e.offset.top,c=l+e.proportions().width,u=h+e.proportions().height;switch(i){case"fit":return l<=s&&r<=c&&h<=o&&a<=u;case"intersect":return l<s+t.helperProportions.width/2&&r-t.helperProportions.width/2<c&&h<o+t.helperProportions.height/2&&a-t.helperProportions.height/2<u;case"pointer":return ht(n.pageY,h,e.proportions().height)&&ht(n.pageX,l,e.proportions().width);case"touch":return(h<=o&&o<=u||h<=a&&a<=u||o<h&&u<a)&&(l<=s&&s<=c||l<=r&&r<=c||s<l&&c<r);default:return!1}},!(t.ui.ddmanager={current:null,droppables:{default:[]},prepareOffsets:function(e,i){var n,s,o=t.ui.ddmanager.droppables[e.options.scope]||[],r=i?i.type:null,a=(e.currentItem||e.element).find(":data(ui-droppable)").addBack();t:for(n=0;n<o.length;n++)if(!(o[n].options.disabled||e&&!o[n].accept.call(o[n].element[0],e.currentItem||e.element))){for(s=0;s<a.length;s++)if(a[s]===o[n].element[0]){o[n].proportions().height=0;continue t}o[n].visible="none"!==o[n].element.css("display"),o[n].visible&&("mousedown"===r&&o[n]._activate.call(o[n],i),o[n].offset=o[n].element.offset(),o[n].proportions({width:o[n].element[0].offsetWidth,height:o[n].element[0].offsetHeight}))}},drop:function(e,i){var n=!1;return t.each((t.ui.ddmanager.droppables[e.options.scope]||[]).slice(),(function(){this.options&&(!this.options.disabled&&this.visible&&t.ui.intersect(e,this,this.options.tolerance,i)&&(n=this._drop.call(this,i)||n),!this.options.disabled)&&this.visible&&this.accept.call(this.element[0],e.currentItem||e.element)&&(this.isout=!0,this.isover=!1,this._deactivate.call(this,i))})),n},dragStart:function(e,i){e.element.parentsUntil("body").on("scroll.droppable",(function(){e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)}))},drag:function(e,i){e.options.refreshPositions&&t.ui.ddmanager.prepareOffsets(e,i),t.each(t.ui.ddmanager.droppables[e.options.scope]||[],(function(){var n,s,o,r;this.options.disabled||this.greedyChild||!this.visible||(r=!(r=t.ui.intersect(e,this,this.options.tolerance,i))&&this.isover?"isout":r&&!this.isover?"isover":null)&&(this.options.greedy&&(s=this.options.scope,(o=this.element.parents(":data(ui-droppable)").filter((function(){return t(this).droppable("instance").options.scope===s}))).length)&&((n=t(o[0]).droppable("instance")).greedyChild="isover"===r),n&&"isover"===r&&(n.isover=!1,n.isout=!0,n._out.call(n,i)),this[r]=!0,this["isout"===r?"isover":"isout"]=!1,this["isover"===r?"_over":"_out"].call(this,i),n)&&"isout"===r&&(n.isout=!1,n.isover=!0,n._over.call(n,i))}))},dragStop:function(e,i){e.element.parentsUntil("body").off("scroll.droppable"),e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)}})!==t.uiBackCompat&&t.widget("ui.droppable",t.ui.droppable,{options:{hoverClass:!1,activeClass:!1},_addActiveClass:function(){this._super(),this.options.activeClass&&this.element.addClass(this.options.activeClass)},_removeActiveClass:function(){this._super(),this.options.activeClass&&this.element.removeClass(this.options.activeClass)},_addHoverClass:function(){this._super(),this.options.hoverClass&&this.element.addClass(this.options.hoverClass)},_removeHoverClass:function(){this._super(),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass)}}),t.ui.droppable,t.widget("ui.progressbar",{version:"1.13.3",options:{classes:{"ui-progressbar":"ui-corner-all","ui-progressbar-value":"ui-corner-left","ui-progressbar-complete":"ui-corner-right"},max:100,value:0,change:null,complete:null},min:0,_create:function(){this.oldValue=this.options.value=this._constrainedValue(),this.element.attr({role:"progressbar","aria-valuemin":this.min}),this._addClass("ui-progressbar","ui-widget ui-widget-content"),this.valueDiv=t("<div>").appendTo(this.element),this._addClass(this.valueDiv,"ui-progressbar-value","ui-widget-header"),this._refreshValue()},_destroy:function(){this.element.removeAttr("role aria-valuemin aria-valuemax aria-valuenow"),this.valueDiv.remove()},value:function(t){if(void 0===t)return this.options.value;this.options.value=this._constrainedValue(t),this._refreshValue()},_constrainedValue:function(t){return void 0===t&&(t=this.options.value),this.indeterminate=!1===t,"number"!=typeof t&&(t=0),!this.indeterminate&&Math.min(this.options.max,Math.max(this.min,t))},_setOptions:function(t){var e=t.value;delete t.value,this._super(t),this.options.value=this._constrainedValue(e),this._refreshValue()},_setOption:function(t,e){"max"===t&&(e=Math.max(this.min,e)),this._super(t,e)},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",t),this._toggleClass(null,"ui-state-disabled",!!t)},_percentage:function(){return this.indeterminate?100:100*(this.options.value-this.min)/(this.options.max-this.min)},_refreshValue:function(){var e=this.options.value,i=this._percentage();this.valueDiv.toggle(this.indeterminate||e>this.min).width(i.toFixed(0)+"%"),this._toggleClass(this.valueDiv,"ui-progressbar-complete",null,e===this.options.max)._toggleClass("ui-progressbar-indeterminate",null,this.indeterminate),this.indeterminate?(this.element.removeAttr("aria-valuenow"),this.overlayDiv||(this.overlayDiv=t("<div>").appendTo(this.valueDiv),this._addClass(this.overlayDiv,"ui-progressbar-overlay"))):(this.element.attr({"aria-valuemax":this.options.max,"aria-valuenow":e}),this.overlayDiv&&(this.overlayDiv.remove(),this.overlayDiv=null)),this.oldValue!==e&&(this.oldValue=e,this._trigger("change")),e===this.options.max&&this._trigger("complete")}}),t.widget("ui.selectable",t.ui.mouse,{version:"1.13.3",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var e=this;this._addClass("ui-selectable"),this.dragged=!1,this.refresh=function(){e.elementPos=t(e.element[0]).offset(),e.selectees=t(e.options.filter,e.element[0]),e._addClass(e.selectees,"ui-selectee"),e.selectees.each((function(){var i=t(this),n={left:(n=i.offset()).left-e.elementPos.left,top:n.top-e.elementPos.top};t.data(this,"selectable-item",{element:this,$element:i,left:n.left,top:n.top,right:n.left+i.outerWidth(),bottom:n.top+i.outerHeight(),startselected:!1,selected:i.hasClass("ui-selected"),selecting:i.hasClass("ui-selecting"),unselecting:i.hasClass("ui-unselecting")})}))},this.refresh(),this._mouseInit(),this.helper=t("<div>"),this._addClass(this.helper,"ui-selectable-helper")},_destroy:function(){this.selectees.removeData("selectable-item"),this._mouseDestroy()},_mouseStart:function(e){var i=this,n=this.options;this.opos=[e.pageX,e.pageY],this.elementPos=t(this.element[0]).offset(),this.options.disabled||(this.selectees=t(n.filter,this.element[0]),this._trigger("start",e),t(n.appendTo).append(this.helper),this.helper.css({left:e.pageX,top:e.pageY,width:0,height:0}),n.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each((function(){var n=t.data(this,"selectable-item");n.startselected=!0,e.metaKey||e.ctrlKey||(i._removeClass(n.$element,"ui-selected"),n.selected=!1,i._addClass(n.$element,"ui-unselecting"),n.unselecting=!0,i._trigger("unselecting",e,{unselecting:n.element}))})),t(e.target).parents().addBack().each((function(){var n,s=t.data(this,"selectable-item");if(s)return n=!e.metaKey&&!e.ctrlKey||!s.$element.hasClass("ui-selected"),i._removeClass(s.$element,n?"ui-unselecting":"ui-selected")._addClass(s.$element,n?"ui-selecting":"ui-unselecting"),s.unselecting=!n,s.selecting=n,(s.selected=n)?i._trigger("selecting",e,{selecting:s.element}):i._trigger("unselecting",e,{unselecting:s.element}),!1})))},_mouseDrag:function(e){var i,n,s,o,r,a,l;if(this.dragged=!0,!this.options.disabled)return s=(n=this).options,o=this.opos[0],r=this.opos[1],a=e.pageX,l=e.pageY,a<o&&(i=a,a=o,o=i),l<r&&(i=l,l=r,r=i),this.helper.css({left:o,top:r,width:a-o,height:l-r}),this.selectees.each((function(){var i=t.data(this,"selectable-item"),h=!1,c={};i&&i.element!==n.element[0]&&(c.left=i.left+n.elementPos.left,c.right=i.right+n.elementPos.left,c.top=i.top+n.elementPos.top,c.bottom=i.bottom+n.elementPos.top,"touch"===s.tolerance?h=!(a<c.left||c.right<o||l<c.top||c.bottom<r):"fit"===s.tolerance&&(h=o<c.left&&c.right<a&&r<c.top&&c.bottom<l),h?(i.selected&&(n._removeClass(i.$element,"ui-selected"),i.selected=!1),i.unselecting&&(n._removeClass(i.$element,"ui-unselecting"),i.unselecting=!1),i.selecting||(n._addClass(i.$element,"ui-selecting"),i.selecting=!0,n._trigger("selecting",e,{selecting:i.element}))):(i.selecting&&((e.metaKey||e.ctrlKey)&&i.startselected?(n._removeClass(i.$element,"ui-selecting"),i.selecting=!1,n._addClass(i.$element,"ui-selected"),i.selected=!0):(n._removeClass(i.$element,"ui-selecting"),i.selecting=!1,i.startselected&&(n._addClass(i.$element,"ui-unselecting"),i.unselecting=!0),n._trigger("unselecting",e,{unselecting:i.element}))),!i.selected||e.metaKey||e.ctrlKey||i.startselected||(n._removeClass(i.$element,"ui-selected"),i.selected=!1,n._addClass(i.$element,"ui-unselecting"),i.unselecting=!0,n._trigger("unselecting",e,{unselecting:i.element}))))})),!1},_mouseStop:function(e){var i=this;return this.dragged=!1,t(".ui-unselecting",this.element[0]).each((function(){var n=t.data(this,"selectable-item");i._removeClass(n.$element,"ui-unselecting"),n.unselecting=!1,n.startselected=!1,i._trigger("unselected",e,{unselected:n.element})})),t(".ui-selecting",this.element[0]).each((function(){var n=t.data(this,"selectable-item");i._removeClass(n.$element,"ui-selecting")._addClass(n.$element,"ui-selected"),n.selecting=!1,n.selected=!0,n.startselected=!0,i._trigger("selected",e,{selected:n.element})})),this._trigger("stop",e),this.helper.remove(),!1}}),t.widget("ui.selectmenu",[t.ui.formResetMixin,{version:"1.13.3",defaultElement:"<select>",options:{appendTo:null,classes:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"},disabled:null,icons:{button:"ui-icon-triangle-1-s"},position:{my:"left top",at:"left bottom",collision:"none"},width:!1,change:null,close:null,focus:null,open:null,select:null},_create:function(){var e=this.element.uniqueId().attr("id");this.ids={element:e,button:e+"-button",menu:e+"-menu"},this._drawButton(),this._drawMenu(),this._bindFormResetHandler(),this._rendered=!1,this.menuItems=t()},_drawButton:function(){var e,i=this,n=this._parseOption(this.element.find("option:selected"),this.element[0].selectedIndex);this.labels=this.element.labels().attr("for",this.ids.button),this._on(this.labels,{click:function(t){this.button.trigger("focus"),t.preventDefault()}}),this.element.hide(),this.button=t("<span>",{tabindex:this.options.disabled?-1:0,id:this.ids.button,role:"combobox","aria-expanded":"false","aria-autocomplete":"list","aria-owns":this.ids.menu,"aria-haspopup":"true",title:this.element.attr("title")}).insertAfter(this.element),this._addClass(this.button,"ui-selectmenu-button ui-selectmenu-button-closed","ui-button ui-widget"),e=t("<span>").appendTo(this.button),this._addClass(e,"ui-selectmenu-icon","ui-icon "+this.options.icons.button),this.buttonItem=this._renderButtonItem(n).appendTo(this.button),!1!==this.options.width&&this._resizeButton(),this._on(this.button,this._buttonEvents),this.button.one("focusin",(function(){i._rendered||i._refreshMenu()}))},_drawMenu:function(){var e=this;this.menu=t("<ul>",{"aria-hidden":"true","aria-labelledby":this.ids.button,id:this.ids.menu}),this.menuWrap=t("<div>").append(this.menu),this._addClass(this.menuWrap,"ui-selectmenu-menu","ui-front"),this.menuWrap.appendTo(this._appendTo()),this.menuInstance=this.menu.menu({classes:{"ui-menu":"ui-corner-bottom"},role:"listbox",select:function(t,i){t.preventDefault(),e._setSelection(),e._select(i.item.data("ui-selectmenu-item"),t)},focus:function(t,i){i=i.item.data("ui-selectmenu-item"),null!=e.focusIndex&&i.index!==e.focusIndex&&(e._trigger("focus",t,{item:i}),e.isOpen||e._select(i,t)),e.focusIndex=i.index,e.button.attr("aria-activedescendant",e.menuItems.eq(i.index).attr("id"))}}).menu("instance"),this.menuInstance._off(this.menu,"mouseleave"),this.menuInstance._closeOnDocumentClick=function(){return!1},this.menuInstance._isDivider=function(){return!1}},refresh:function(){this._refreshMenu(),this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(this._getSelectedItem().data("ui-selectmenu-item")||{})),null===this.options.width&&this._resizeButton()},_refreshMenu:function(){var t=this.element.find("option");this.menu.empty(),this._parseOptions(t),this._renderMenu(this.menu,this.items),this.menuInstance.refresh(),this.menuItems=this.menu.find("li").not(".ui-selectmenu-optgroup").find(".ui-menu-item-wrapper"),this._rendered=!0,t.length&&(t=this._getSelectedItem(),this.menuInstance.focus(null,t),this._setAria(t.data("ui-selectmenu-item")),this._setOption("disabled",this.element.prop("disabled")))},open:function(t){this.options.disabled||(this._rendered?(this._removeClass(this.menu.find(".ui-state-active"),null,"ui-state-active"),this.menuInstance.focus(null,this._getSelectedItem())):this._refreshMenu(),this.menuItems.length&&(this.isOpen=!0,this._toggleAttr(),this._resizeMenu(),this._position(),this._on(this.document,this._documentClick),this._trigger("open",t)))},_position:function(){this.menuWrap.position(t.extend({of:this.button},this.options.position))},close:function(t){this.isOpen&&(this.isOpen=!1,this._toggleAttr(),this.range=null,this._off(this.document),this._trigger("close",t))},widget:function(){return this.button},menuWidget:function(){return this.menu},_renderButtonItem:function(e){var i=t("<span>");return this._setText(i,e.label),this._addClass(i,"ui-selectmenu-text"),i},_renderMenu:function(e,i){var n=this,s="";t.each(i,(function(i,o){var r;o.optgroup!==s&&(r=t("<li>",{text:o.optgroup}),n._addClass(r,"ui-selectmenu-optgroup","ui-menu-divider"+(o.element.parent("optgroup").prop("disabled")?" ui-state-disabled":"")),r.appendTo(e),s=o.optgroup),n._renderItemData(e,o)}))},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-selectmenu-item",e)},_renderItem:function(e,i){var n=t("<li>"),s=t("<div>",{title:i.element.attr("title")});return i.disabled&&this._addClass(n,null,"ui-state-disabled"),i.hidden?n.prop("hidden",!0):this._setText(s,i.label),n.append(s).appendTo(e)},_setText:function(t,e){e?t.text(e):t.html("&#160;")},_move:function(t,e){var i,n=".ui-menu-item";this.isOpen?i=this.menuItems.eq(this.focusIndex).parent("li"):(i=this.menuItems.eq(this.element[0].selectedIndex).parent("li"),n+=":not(.ui-state-disabled)"),(i="first"===t||"last"===t?i["first"===t?"prevAll":"nextAll"](n).eq(-1):i[t+"All"](n).eq(0)).length&&this.menuInstance.focus(e,i)},_getSelectedItem:function(){return this.menuItems.eq(this.element[0].selectedIndex).parent("li")},_toggle:function(t){this[this.isOpen?"close":"open"](t)},_setSelection:function(){var t;this.range&&(window.getSelection?((t=window.getSelection()).removeAllRanges(),t.addRange(this.range)):this.range.select(),this.button.trigger("focus"))},_documentClick:{mousedown:function(e){!this.isOpen||t(e.target).closest(".ui-selectmenu-menu, #"+t.escapeSelector(this.ids.button)).length||this.close(e)}},_buttonEvents:{mousedown:function(){var t;window.getSelection?(t=window.getSelection()).rangeCount&&(this.range=t.getRangeAt(0)):this.range=document.selection.createRange()},click:function(t){this._setSelection(),this._toggle(t)},keydown:function(e){var i=!0;switch(e.keyCode){case t.ui.keyCode.TAB:case t.ui.keyCode.ESCAPE:this.close(e),i=!1;break;case t.ui.keyCode.ENTER:this.isOpen&&this._selectFocusedItem(e);break;case t.ui.keyCode.UP:e.altKey?this._toggle(e):this._move("prev",e);break;case t.ui.keyCode.DOWN:e.altKey?this._toggle(e):this._move("next",e);break;case t.ui.keyCode.SPACE:this.isOpen?this._selectFocusedItem(e):this._toggle(e);break;case t.ui.keyCode.LEFT:this._move("prev",e);break;case t.ui.keyCode.RIGHT:this._move("next",e);break;case t.ui.keyCode.HOME:case t.ui.keyCode.PAGE_UP:this._move("first",e);break;case t.ui.keyCode.END:case t.ui.keyCode.PAGE_DOWN:this._move("last",e);break;default:this.menu.trigger(e),i=!1}i&&e.preventDefault()}},_selectFocusedItem:function(t){var e=this.menuItems.eq(this.focusIndex).parent("li");e.hasClass("ui-state-disabled")||this._select(e.data("ui-selectmenu-item"),t)},_select:function(t,e){var i=this.element[0].selectedIndex;this.element[0].selectedIndex=t.index,this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(t)),this._setAria(t),this._trigger("select",e,{item:t}),t.index!==i&&this._trigger("change",e,{item:t}),this.close(e)},_setAria:function(t){t=this.menuItems.eq(t.index).attr("id"),this.button.attr({"aria-labelledby":t,"aria-activedescendant":t}),this.menu.attr("aria-activedescendant",t)},_setOption:function(t,e){var i;"icons"===t&&(i=this.button.find("span.ui-icon"),this._removeClass(i,null,this.options.icons.button)._addClass(i,null,e.button)),this._super(t,e),"appendTo"===t&&this.menuWrap.appendTo(this._appendTo()),"width"===t&&this._resizeButton()},_setOptionDisabled:function(t){this._super(t),this.menuInstance.option("disabled",t),this.button.attr("aria-disabled",t),this._toggleClass(this.button,null,"ui-state-disabled",t),this.element.prop("disabled",t),t?(this.button.attr("tabindex",-1),this.close()):this.button.attr("tabindex",0)},_appendTo:function(){var e=this.options.appendTo;return(e=(e=e&&(e.jquery||e.nodeType?t(e):this.document.find(e).eq(0)))&&e[0]?e:this.element.closest(".ui-front, dialog")).length?e:this.document[0].body},_toggleAttr:function(){this.button.attr("aria-expanded",this.isOpen),this._removeClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"closed":"open"))._addClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"open":"closed"))._toggleClass(this.menuWrap,"ui-selectmenu-open",null,this.isOpen),this.menu.attr("aria-hidden",!this.isOpen)},_resizeButton:function(){var t=this.options.width;!1===t?this.button.css("width",""):(null===t&&(t=this.element.show().outerWidth(),this.element.hide()),this.button.outerWidth(t))},_resizeMenu:function(){this.menu.outerWidth(Math.max(this.button.outerWidth(),this.menu.width("").outerWidth()+1))},_getCreateOptions:function(){var t=this._super();return t.disabled=this.element.prop("disabled"),t},_parseOptions:function(e){var i=this,n=[];e.each((function(e,s){n.push(i._parseOption(t(s),e))})),this.items=n},_parseOption:function(t,e){var i=t.parent("optgroup");return{element:t,index:e,value:t.val(),label:t.text(),hidden:i.prop("hidden")||t.prop("hidden"),optgroup:i.attr("label")||"",disabled:i.prop("disabled")||t.prop("disabled")}},_destroy:function(){this._unbindFormResetHandler(),this.menuWrap.remove(),this.button.remove(),this.element.show(),this.element.removeUniqueId(),this.labels.attr("for",this.ids.element)}}]),t.widget("ui.slider",t.ui.mouse,{version:"1.13.3",widgetEventPrefix:"slide",options:{animate:!1,classes:{"ui-slider":"ui-corner-all","ui-slider-handle":"ui-corner-all","ui-slider-range":"ui-corner-all ui-widget-header"},distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},numPages:5,_create:function(){this._keySliding=!1,this._mouseSliding=!1,this._animateOff=!0,this._handleIndex=null,this._detectOrientation(),this._mouseInit(),this._calculateNewMax(),this._addClass("ui-slider ui-slider-"+this.orientation,"ui-widget ui-widget-content"),this._refresh(),this._animateOff=!1},_refresh:function(){this._createRange(),this._createHandles(),this._setupEvents(),this._refreshValue()},_createHandles:function(){var e,i=this.options,n=this.element.find(".ui-slider-handle"),s=[],o=i.values&&i.values.length||1;for(n.length>o&&(n.slice(o).remove(),n=n.slice(0,o)),e=n.length;e<o;e++)s.push("<span tabindex='0'></span>");this.handles=n.add(t(s.join("")).appendTo(this.element)),this._addClass(this.handles,"ui-slider-handle","ui-state-default"),this.handle=this.handles.eq(0),this.handles.each((function(e){t(this).data("ui-slider-handle-index",e).attr("tabIndex",0)}))},_createRange:function(){var e=this.options;e.range?(!0===e.range&&(e.values?e.values.length&&2!==e.values.length?e.values=[e.values[0],e.values[0]]:Array.isArray(e.values)&&(e.values=e.values.slice(0)):e.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?(this._removeClass(this.range,"ui-slider-range-min ui-slider-range-max"),this.range.css({left:"",bottom:""})):(this.range=t("<div>").appendTo(this.element),this._addClass(this.range,"ui-slider-range")),"min"!==e.range&&"max"!==e.range||this._addClass(this.range,"ui-slider-range-"+e.range)):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){this._off(this.handles),this._on(this.handles,this._handleEvents),this._hoverable(this.handles),this._focusable(this.handles)},_destroy:function(){this.handles.remove(),this.range&&this.range.remove(),this._mouseDestroy()},_mouseCapture:function(e){var i,n,s,o,r,a,l=this,h=this.options;return!h.disabled&&(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),r={x:e.pageX,y:e.pageY},i=this._normValueFromMouse(r),n=this._valueMax()-this._valueMin()+1,this.handles.each((function(e){var r=Math.abs(i-l.values(e));(r<n||n===r&&(e===l._lastChangedValue||l.values(e)===h.min))&&(n=r,s=t(this),o=e)})),!1!==this._start(e,o))&&(this._mouseSliding=!0,this._handleIndex=o,this._addClass(s,null,"ui-state-active"),s.trigger("focus"),r=s.offset(),a=!t(e.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=a?{left:0,top:0}:{left:e.pageX-r.left-s.width()/2,top:e.pageY-r.top-s.height()/2-(parseInt(s.css("borderTopWidth"),10)||0)-(parseInt(s.css("borderBottomWidth"),10)||0)+(parseInt(s.css("marginTop"),10)||0)},this.handles.hasClass("ui-state-hover")||this._slide(e,o,i),this._animateOff=!0)},_mouseStart:function(){return!0},_mouseDrag:function(t){var e={x:t.pageX,y:t.pageY};return e=this._normValueFromMouse(e),this._slide(t,this._handleIndex,e),!1},_mouseStop:function(t){return this._removeClass(this.handles,null,"ui-state-active"),this._mouseSliding=!1,this._stop(t,this._handleIndex),this._change(t,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(t){var e;return(t=1<(t=(t="horizontal"===this.orientation?(e=this.elementSize.width,t.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(e=this.elementSize.height,t.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0)))/e)?1:t)<0&&(t=0),"vertical"===this.orientation&&(t=1-t),e=this._valueMax()-this._valueMin(),t=this._valueMin()+t*e,this._trimAlignValue(t)},_uiHash:function(t,e,i){var n={handle:this.handles[t],handleIndex:t,value:void 0!==e?e:this.value()};return this._hasMultipleValues()&&(n.value=void 0!==e?e:this.values(t),n.values=i||this.values()),n},_hasMultipleValues:function(){return this.options.values&&this.options.values.length},_start:function(t,e){return this._trigger("start",t,this._uiHash(e))},_slide:function(t,e,i){var n,s=this.value(),o=this.values();this._hasMultipleValues()&&(n=this.values(e?0:1),s=this.values(e),2===this.options.values.length&&!0===this.options.range&&(i=0===e?Math.min(n,i):Math.max(n,i)),o[e]=i),i!==s&&!1!==this._trigger("slide",t,this._uiHash(e,i,o))&&(this._hasMultipleValues()?this.values(e,i):this.value(i))},_stop:function(t,e){this._trigger("stop",t,this._uiHash(e))},_change:function(t,e){this._keySliding||this._mouseSliding||(this._lastChangedValue=e,this._trigger("change",t,this._uiHash(e)))},value:function(t){if(!arguments.length)return this._value();this.options.value=this._trimAlignValue(t),this._refreshValue(),this._change(null,0)},values:function(t,e){var i,n,s;if(1<arguments.length)this.options.values[t]=this._trimAlignValue(e),this._refreshValue(),this._change(null,t);else{if(!arguments.length)return this._values();if(!Array.isArray(t))return this._hasMultipleValues()?this._values(t):this.value();for(i=this.options.values,n=t,s=0;s<i.length;s+=1)i[s]=this._trimAlignValue(n[s]),this._change(null,s);this._refreshValue()}},_setOption:function(t,e){var i,n=0;switch("range"===t&&!0===this.options.range&&("min"===e?(this.options.value=this._values(0),this.options.values=null):"max"===e&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),Array.isArray(this.options.values)&&(n=this.options.values.length),this._super(t,e),t){case"orientation":this._detectOrientation(),this._removeClass("ui-slider-horizontal ui-slider-vertical")._addClass("ui-slider-"+this.orientation),this._refreshValue(),this.options.range&&this._refreshRange(e),this.handles.css("horizontal"===e?"bottom":"left","");break;case"value":this._animateOff=!0,this._refreshValue(),this._change(null,0),this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),i=n-1;0<=i;i--)this._change(null,i);this._animateOff=!1;break;case"step":case"min":case"max":this._animateOff=!0,this._calculateNewMax(),this._refreshValue(),this._animateOff=!1;break;case"range":this._animateOff=!0,this._refresh(),this._animateOff=!1}},_setOptionDisabled:function(t){this._super(t),this._toggleClass(null,"ui-state-disabled",!!t)},_value:function(){var t=this.options.value;return this._trimAlignValue(t)},_values:function(t){var e,i;if(arguments.length)return t=this.options.values[t],this._trimAlignValue(t);if(this._hasMultipleValues()){for(e=this.options.values.slice(),i=0;i<e.length;i+=1)e[i]=this._trimAlignValue(e[i]);return e}return[]},_trimAlignValue:function(t){var e,i;return t<=this._valueMin()?this._valueMin():t>=this._valueMax()?this._valueMax():(e=0<this.options.step?this.options.step:1,i=t-(t=(t-this._valueMin())%e),2*Math.abs(t)>=e&&(i+=0<t?e:-e),parseFloat(i.toFixed(5)))},_calculateNewMax:function(){var t=this.options.max,e=this._valueMin(),i=this.options.step;(t=Math.round((t-e)/i)*i+e)>this.options.max&&(t-=i),this.max=parseFloat(t.toFixed(this._precision()))},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min?Math.max(t,this._precisionOf(this.options.min)):t},_precisionOf:function(t){var e=(t=t.toString()).indexOf(".");return-1===e?0:t.length-e-1},_valueMin:function(){return this.options.min},_valueMax:function(){return this.max},_refreshRange:function(t){"vertical"===t&&this.range.css({width:"",left:""}),"horizontal"===t&&this.range.css({height:"",bottom:""})},_refreshValue:function(){var e,i,n,s,o,r=this.options.range,a=this.options,l=this,h=!this._animateOff&&a.animate,c={};this._hasMultipleValues()?this.handles.each((function(n){i=(l.values(n)-l._valueMin())/(l._valueMax()-l._valueMin())*100,c["horizontal"===l.orientation?"left":"bottom"]=i+"%",t(this).stop(1,1)[h?"animate":"css"](c,a.animate),!0===l.options.range&&("horizontal"===l.orientation?(0===n&&l.range.stop(1,1)[h?"animate":"css"]({left:i+"%"},a.animate),1===n&&l.range[h?"animate":"css"]({width:i-e+"%"},{queue:!1,duration:a.animate})):(0===n&&l.range.stop(1,1)[h?"animate":"css"]({bottom:i+"%"},a.animate),1===n&&l.range[h?"animate":"css"]({height:i-e+"%"},{queue:!1,duration:a.animate}))),e=i})):(n=this.value(),s=this._valueMin(),o=this._valueMax(),i=o!==s?(n-s)/(o-s)*100:0,c["horizontal"===this.orientation?"left":"bottom"]=i+"%",this.handle.stop(1,1)[h?"animate":"css"](c,a.animate),"min"===r&&"horizontal"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({width:i+"%"},a.animate),"max"===r&&"horizontal"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({width:100-i+"%"},a.animate),"min"===r&&"vertical"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({height:i+"%"},a.animate),"max"===r&&"vertical"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({height:100-i+"%"},a.animate))},_handleEvents:{keydown:function(e){var i,n,s,o=t(e.target).data("ui-slider-handle-index");switch(e.keyCode){case t.ui.keyCode.HOME:case t.ui.keyCode.END:case t.ui.keyCode.PAGE_UP:case t.ui.keyCode.PAGE_DOWN:case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(e.preventDefault(),this._keySliding||(this._keySliding=!0,this._addClass(t(e.target),null,"ui-state-active"),!1!==this._start(e,o)))break;return}switch(s=this.options.step,i=n=this._hasMultipleValues()?this.values(o):this.value(),e.keyCode){case t.ui.keyCode.HOME:n=this._valueMin();break;case t.ui.keyCode.END:n=this._valueMax();break;case t.ui.keyCode.PAGE_UP:n=this._trimAlignValue(i+(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.PAGE_DOWN:n=this._trimAlignValue(i-(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:if(i===this._valueMax())return;n=this._trimAlignValue(i+s);break;case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(i===this._valueMin())return;n=this._trimAlignValue(i-s)}this._slide(e,o,n)},keyup:function(e){var i=t(e.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(e,i),this._change(e,i),this._removeClass(t(e.target),null,"ui-state-active"))}}}),t.widget("ui.sortable",t.ui.mouse,{version:"1.13.3",widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1e3,activate:null,beforeStop:null,change:null,deactivate:null,out:null,over:null,receive:null,remove:null,sort:null,start:null,stop:null,update:null},_isOverAxis:function(t,e,i){return e<=t&&t<e+i},_isFloating:function(t){return/left|right/.test(t.css("float"))||/inline|table-cell/.test(t.css("display"))},_create:function(){this.containerCache={},this._addClass("ui-sortable"),this.refresh(),this.offset=this.element.offset(),this._mouseInit(),this._setHandleClassName(),this.ready=!0},_setOption:function(t,e){this._super(t,e),"handle"===t&&this._setHandleClassName()},_setHandleClassName:function(){var e=this;this._removeClass(this.element.find(".ui-sortable-handle"),"ui-sortable-handle"),t.each(this.items,(function(){e._addClass(this.instance.options.handle?this.item.find(this.instance.options.handle):this.item,"ui-sortable-handle")}))},_destroy:function(){this._mouseDestroy();for(var t=this.items.length-1;0<=t;t--)this.items[t].item.removeData(this.widgetName+"-item");return this},_mouseCapture:function(e,i){var n=null,s=!1,o=this;return!(this.reverting||this.options.disabled||"static"===this.options.type||(this._refreshItems(e),t(e.target).parents().each((function(){if(t.data(this,o.widgetName+"-item")===o)return n=t(this),!1})),!(n=t.data(e.target,o.widgetName+"-item")===o?t(e.target):n))||this.options.handle&&!i&&(t(this.options.handle,n).find("*").addBack().each((function(){this===e.target&&(s=!0)})),!s)||(this.currentItem=n,this._removeCurrentsFromItems(),0))},_mouseStart:function(e,i,n){var s,o,r=this.options;if((this.currentContainer=this).refreshPositions(),this.appendTo=t("parent"!==r.appendTo?r.appendTo:this.currentItem.parent()),this.helper=this._createHelper(e),this._cacheHelperProportions(),this._cacheMargins(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},t.extend(this.offset,{click:{left:e.pageX-this.offset.left,top:e.pageY-this.offset.top},relative:this._getRelativeOffset()}),this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),r.cursorAt&&this._adjustOffsetFromHelper(r.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!==this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),this.scrollParent=this.placeholder.scrollParent(),t.extend(this.offset,{parent:this._getParentOffset()}),r.containment&&this._setContainment(),r.cursor&&"auto"!==r.cursor&&(o=this.document.find("body"),this.storedCursor=o.css("cursor"),o.css("cursor",r.cursor),this.storedStylesheet=t("<style>*{ cursor: "+r.cursor+" !important; }</style>").appendTo(o)),r.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",r.zIndex)),r.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",r.opacity)),this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",e,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions(),!n)for(s=this.containers.length-1;0<=s;s--)this.containers[s]._trigger("activate",e,this._uiHash(this));return t.ui.ddmanager&&(t.ui.ddmanager.current=this),t.ui.ddmanager&&!r.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this.dragging=!0,this._addClass(this.helper,"ui-sortable-helper"),this.helper.parent().is(this.appendTo)||(this.helper.detach().appendTo(this.appendTo),this.offset.parent=this._getParentOffset()),this.position=this.originalPosition=this._generatePosition(e),this.originalPageX=e.pageX,this.originalPageY=e.pageY,this.lastPositionAbs=this.positionAbs=this._convertPositionTo("absolute"),this._mouseDrag(e),!0},_scroll:function(t){var e=this.options,i=!1;return this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-t.pageY<e.scrollSensitivity?this.scrollParent[0].scrollTop=i=this.scrollParent[0].scrollTop+e.scrollSpeed:t.pageY-this.overflowOffset.top<e.scrollSensitivity&&(this.scrollParent[0].scrollTop=i=this.scrollParent[0].scrollTop-e.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-t.pageX<e.scrollSensitivity?this.scrollParent[0].scrollLeft=i=this.scrollParent[0].scrollLeft+e.scrollSpeed:t.pageX-this.overflowOffset.left<e.scrollSensitivity&&(this.scrollParent[0].scrollLeft=i=this.scrollParent[0].scrollLeft-e.scrollSpeed)):(t.pageY-this.document.scrollTop()<e.scrollSensitivity?i=this.document.scrollTop(this.document.scrollTop()-e.scrollSpeed):this.window.height()-(t.pageY-this.document.scrollTop())<e.scrollSensitivity&&(i=this.document.scrollTop(this.document.scrollTop()+e.scrollSpeed)),t.pageX-this.document.scrollLeft()<e.scrollSensitivity?i=this.document.scrollLeft(this.document.scrollLeft()-e.scrollSpeed):this.window.width()-(t.pageX-this.document.scrollLeft())<e.scrollSensitivity&&(i=this.document.scrollLeft(this.document.scrollLeft()+e.scrollSpeed))),i},_mouseDrag:function(e){var i,n,s,o,r=this.options;for(this.position=this._generatePosition(e),this.positionAbs=this._convertPositionTo("absolute"),this.options.axis&&"y"===this.options.axis||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&"x"===this.options.axis||(this.helper[0].style.top=this.position.top+"px"),r.scroll&&!1!==this._scroll(e)&&(this._refreshItemPositions(!0),t.ui.ddmanager)&&!r.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this.dragDirection={vertical:this._getDragVerticalDirection(),horizontal:this._getDragHorizontalDirection()},i=this.items.length-1;0<=i;i--)if(s=(n=this.items[i]).item[0],(o=this._intersectsWithPointer(n))&&n.instance===this.currentContainer&&!(s===this.currentItem[0]||this.placeholder[1===o?"next":"prev"]()[0]===s||t.contains(this.placeholder[0],s)||"semi-dynamic"===this.options.type&&t.contains(this.element[0],s))){if(this.direction=1===o?"down":"up","pointer"!==this.options.tolerance&&!this._intersectsWithSides(n))break;this._rearrange(e,n),this._trigger("change",e,this._uiHash());break}return this._contactContainers(e),t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),this._trigger("sort",e,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(e,i){var n,s,o,r;if(e)return t.ui.ddmanager&&!this.options.dropBehaviour&&t.ui.ddmanager.drop(this,e),this.options.revert?(s=(n=this).placeholder.offset(),r={},(o=this.options.axis)&&"x"!==o||(r.left=s.left-this.offset.parent.left-this.margins.left+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollLeft)),o&&"y"!==o||(r.top=s.top-this.offset.parent.top-this.margins.top+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollTop)),this.reverting=!0,t(this.helper).animate(r,parseInt(this.options.revert,10)||500,(function(){n._clear(e)}))):this._clear(e,i),!1},cancel:function(){if(this.dragging){this._mouseUp(new t.Event("mouseup",{target:null})),"original"===this.options.helper?(this.currentItem.css(this._storedCSS),this._removeClass(this.currentItem,"ui-sortable-helper")):this.currentItem.show();for(var e=this.containers.length-1;0<=e;e--)this.containers[e]._trigger("deactivate",null,this._uiHash(this)),this.containers[e].containerCache.over&&(this.containers[e]._trigger("out",null,this._uiHash(this)),this.containers[e].containerCache.over=0)}return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),"original"!==this.options.helper&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),t.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?t(this.domPosition.prev).after(this.currentItem):t(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},t(i).each((function(){var i=(t(e.item||this).attr(e.attribute||"id")||"").match(e.expression||/(.+)[\-=_](.+)/);i&&n.push((e.key||i[1]+"[]")+"="+(e.key&&e.expression?i[1]:i[2]))})),!n.length&&e.key&&n.push(e.key+"="),n.join("&")},toArray:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},i.each((function(){n.push(t(e.item||this).attr(e.attribute||"id")||"")})),n},_intersectsWith:function(t){var e=this.positionAbs.left,i=e+this.helperProportions.width,n=this.positionAbs.top,s=n+this.helperProportions.height,o=t.left,r=o+t.width,a=t.top,l=a+t.height,h=this.offset.click.top,c=this.offset.click.left;return h="x"===this.options.axis||a<n+h&&n+h<l,c="y"===this.options.axis||o<e+c&&e+c<r,"pointer"===this.options.tolerance||this.options.forcePointerForContainers||"pointer"!==this.options.tolerance&&this.helperProportions[this.floating?"width":"height"]>t[this.floating?"width":"height"]?h&&c:o<e+this.helperProportions.width/2&&i-this.helperProportions.width/2<r&&a<n+this.helperProportions.height/2&&s-this.helperProportions.height/2<l},_intersectsWithPointer:function(t){var e="x"===this.options.axis||this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top,t.height);return t="y"===this.options.axis||this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left,t.width),!(!e||!t)&&(e=this.dragDirection.vertical,t=this.dragDirection.horizontal,this.floating?"right"===t||"down"===e?2:1:e&&("down"===e?2:1))},_intersectsWithSides:function(t){var e=this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top+t.height/2,t.height),i=(t=this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left+t.width/2,t.width),this.dragDirection.vertical),n=this.dragDirection.horizontal;return this.floating&&n?"right"===n&&t||"left"===n&&!t:i&&("down"===i&&e||"up"===i&&!e)},_getDragVerticalDirection:function(){var t=this.positionAbs.top-this.lastPositionAbs.top;return 0!=t&&(0<t?"down":"up")},_getDragHorizontalDirection:function(){var t=this.positionAbs.left-this.lastPositionAbs.left;return 0!=t&&(0<t?"right":"left")},refresh:function(t){return this._refreshItems(t),this._setHandleClassName(),this.refreshPositions(),this},_connectWith:function(){var t=this.options;return t.connectWith.constructor===String?[t.connectWith]:t.connectWith},_getItemsAsjQuery:function(e){var i,n,s,o,r=[],a=[],l=this._connectWith();if(l&&e)for(i=l.length-1;0<=i;i--)for(n=(s=t(l[i],this.document[0])).length-1;0<=n;n--)(o=t.data(s[n],this.widgetFullName))&&o!==this&&!o.options.disabled&&a.push(["function"==typeof o.options.items?o.options.items.call(o.element):t(o.options.items,o.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),o]);function h(){r.push(this)}for(a.push(["function"==typeof this.options.items?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):t(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]),i=a.length-1;0<=i;i--)a[i][0].each(h);return t(r)},_removeCurrentsFromItems:function(){var e=this.currentItem.find(":data("+this.widgetName+"-item)");this.items=t.grep(this.items,(function(t){for(var i=0;i<e.length;i++)if(e[i]===t.item[0])return!1;return!0}))},_refreshItems:function(e){this.items=[],this.containers=[this];var i,n,s,o,r,a,l,h,c=this.items,u=[["function"==typeof this.options.items?this.options.items.call(this.element[0],e,{item:this.currentItem}):t(this.options.items,this.element),this]],d=this._connectWith();if(d&&this.ready)for(i=d.length-1;0<=i;i--)for(n=(s=t(d[i],this.document[0])).length-1;0<=n;n--)(o=t.data(s[n],this.widgetFullName))&&o!==this&&!o.options.disabled&&(u.push(["function"==typeof o.options.items?o.options.items.call(o.element[0],e,{item:this.currentItem}):t(o.options.items,o.element),o]),this.containers.push(o));for(i=u.length-1;0<=i;i--)for(r=u[i][1],h=(a=u[i][n=0]).length;n<h;n++)(l=t(a[n])).data(this.widgetName+"-item",r),c.push({item:l,instance:r,width:0,height:0,left:0,top:0})},_refreshItemPositions:function(e){for(var i,n,s=this.items.length-1;0<=s;s--)i=this.items[s],this.currentContainer&&i.instance!==this.currentContainer&&i.item[0]!==this.currentItem[0]||(n=this.options.toleranceElement?t(this.options.toleranceElement,i.item):i.item,e||(i.width=n.outerWidth(),i.height=n.outerHeight()),n=n.offset(),i.left=n.left,i.top=n.top)},refreshPositions:function(t){var e,i;if(this.floating=!!this.items.length&&("x"===this.options.axis||this._isFloating(this.items[0].item)),this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset()),this._refreshItemPositions(t),this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);else for(e=this.containers.length-1;0<=e;e--)i=this.containers[e].element.offset(),this.containers[e].containerCache.left=i.left,this.containers[e].containerCache.top=i.top,this.containers[e].containerCache.width=this.containers[e].element.outerWidth(),this.containers[e].containerCache.height=this.containers[e].element.outerHeight();return this},_createPlaceholder:function(e){var i,n,s=(e=e||this).options;s.placeholder&&s.placeholder.constructor!==String||(i=s.placeholder,n=e.currentItem[0].nodeName.toLowerCase(),s.placeholder={element:function(){var s=t("<"+n+">",e.document[0]);return e._addClass(s,"ui-sortable-placeholder",i||e.currentItem[0].className)._removeClass(s,"ui-sortable-helper"),"tbody"===n?e._createTrPlaceholder(e.currentItem.find("tr").eq(0),t("<tr>",e.document[0]).appendTo(s)):"tr"===n?e._createTrPlaceholder(e.currentItem,s):"img"===n&&s.attr("src",e.currentItem.attr("src")),i||s.css("visibility","hidden"),s},update:function(t,o){i&&!s.forcePlaceholderSize||(o.height()&&(!s.forcePlaceholderSize||"tbody"!==n&&"tr"!==n)||o.height(e.currentItem.innerHeight()-parseInt(e.currentItem.css("paddingTop")||0,10)-parseInt(e.currentItem.css("paddingBottom")||0,10)),o.width())||o.width(e.currentItem.innerWidth()-parseInt(e.currentItem.css("paddingLeft")||0,10)-parseInt(e.currentItem.css("paddingRight")||0,10))}}),e.placeholder=t(s.placeholder.element.call(e.element,e.currentItem)),e.currentItem.after(e.placeholder),s.placeholder.update(e,e.placeholder)},_createTrPlaceholder:function(e,i){var n=this;e.children().each((function(){t("<td>&#160;</td>",n.document[0]).attr("colspan",t(this).attr("colspan")||1).appendTo(i)}))},_contactContainers:function(e){for(var i,n,s,o,r,a,l,h,c,u=null,d=null,p=this.containers.length-1;0<=p;p--)t.contains(this.currentItem[0],this.containers[p].element[0])||(this._intersectsWith(this.containers[p].containerCache)?u&&t.contains(this.containers[p].element[0],u.element[0])||(u=this.containers[p],d=p):this.containers[p].containerCache.over&&(this.containers[p]._trigger("out",e,this._uiHash(this)),this.containers[p].containerCache.over=0));if(u)if(1===this.containers.length)this.containers[d].containerCache.over||(this.containers[d]._trigger("over",e,this._uiHash(this)),this.containers[d].containerCache.over=1);else{for(n=1e4,s=null,o=(h=u.floating||this._isFloating(this.currentItem))?"left":"top",r=h?"width":"height",c=h?"pageX":"pageY",i=this.items.length-1;0<=i;i--)t.contains(this.containers[d].element[0],this.items[i].item[0])&&this.items[i].item[0]!==this.currentItem[0]&&(a=this.items[i].item.offset()[o],l=!1,e[c]-a>this.items[i][r]/2&&(l=!0),Math.abs(e[c]-a)<n)&&(n=Math.abs(e[c]-a),s=this.items[i],this.direction=l?"up":"down");(s||this.options.dropOnEmpty)&&(this.currentContainer===this.containers[d]?this.currentContainer.containerCache.over||(this.containers[d]._trigger("over",e,this._uiHash()),this.currentContainer.containerCache.over=1):(s?this._rearrange(e,s,null,!0):this._rearrange(e,null,this.containers[d].element,!0),this._trigger("change",e,this._uiHash()),this.containers[d]._trigger("change",e,this._uiHash(this)),this.currentContainer=this.containers[d],this.options.placeholder.update(this.currentContainer,this.placeholder),this.scrollParent=this.placeholder.scrollParent(),this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this.containers[d]._trigger("over",e,this._uiHash(this)),this.containers[d].containerCache.over=1))}},_createHelper:function(e){var i=this.options;return(e="function"==typeof i.helper?t(i.helper.apply(this.element[0],[e,this.currentItem])):"clone"===i.helper?this.currentItem.clone():this.currentItem).parents("body").length||this.appendTo[0].appendChild(e[0]),e[0]===this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),e[0].style.width&&!i.forceHelperSize||e.width(this.currentItem.width()),e[0].style.height&&!i.forceHelperSize||e.height(this.currentItem.height()),e},_adjustOffsetFromHelper:function(t){"string"==typeof t&&(t=t.split(" ")),"left"in(t=Array.isArray(t)?{left:+t[0],top:+t[1]||0}:t)&&(this.offset.click.left=t.left+this.margins.left),"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left),"top"in t&&(this.offset.click.top=t.top+this.margins.top),"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var e=this.offsetParent.offset();return"absolute"===this.cssPosition&&this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),{top:(e=this.offsetParent[0]===this.document[0].body||this.offsetParent[0].tagName&&"html"===this.offsetParent[0].tagName.toLowerCase()&&t.ui.ie?{top:0,left:0}:e).top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){var t;return"relative"===this.cssPosition?{top:(t=this.currentItem.position()).top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:t.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}:{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i,n=this.options;"parent"===n.containment&&(n.containment=this.helper[0].parentNode),"document"!==n.containment&&"window"!==n.containment||(this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,"document"===n.containment?this.document.width():this.window.width()-this.helperProportions.width-this.margins.left,("document"===n.containment?this.document.height()||document.body.parentNode.scrollHeight:this.window.height()||this.document[0].body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]),/^(document|window|parent)$/.test(n.containment)||(e=t(n.containment)[0],n=t(n.containment).offset(),i="hidden"!==t(e).css("overflow"),this.containment=[n.left+(parseInt(t(e).css("borderLeftWidth"),10)||0)+(parseInt(t(e).css("paddingLeft"),10)||0)-this.margins.left,n.top+(parseInt(t(e).css("borderTopWidth"),10)||0)+(parseInt(t(e).css("paddingTop"),10)||0)-this.margins.top,n.left+(i?Math.max(e.scrollWidth,e.offsetWidth):e.offsetWidth)-(parseInt(t(e).css("borderLeftWidth"),10)||0)-(parseInt(t(e).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,n.top+(i?Math.max(e.scrollHeight,e.offsetHeight):e.offsetHeight)-(parseInt(t(e).css("borderTopWidth"),10)||0)-(parseInt(t(e).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top])},_convertPositionTo:function(e,i){i=i||this.position,e="absolute"===e?1:-1;var n="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,s=/(html|body)/i.test(n[0].tagName);return{top:i.top+this.offset.relative.top*e+this.offset.parent.top*e-("fixed"===this.cssPosition?-this.scrollParent.scrollTop():s?0:n.scrollTop())*e,left:i.left+this.offset.relative.left*e+this.offset.parent.left*e-("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():s?0:n.scrollLeft())*e}},_generatePosition:function(e){var i=this.options,n=e.pageX,s=e.pageY,o="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,r=/(html|body)/i.test(o[0].tagName);return"relative"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&this.scrollParent[0]!==this.offsetParent[0]||(this.offset.relative=this._getRelativeOffset()),this.originalPosition&&(this.containment&&(e.pageX-this.offset.click.left<this.containment[0]&&(n=this.containment[0]+this.offset.click.left),e.pageY-this.offset.click.top<this.containment[1]&&(s=this.containment[1]+this.offset.click.top),e.pageX-this.offset.click.left>this.containment[2]&&(n=this.containment[2]+this.offset.click.left),e.pageY-this.offset.click.top>this.containment[3])&&(s=this.containment[3]+this.offset.click.top),i.grid)&&(e=this.originalPageY+Math.round((s-this.originalPageY)/i.grid[1])*i.grid[1],s=!this.containment||e-this.offset.click.top>=this.containment[1]&&e-this.offset.click.top<=this.containment[3]?e:e-this.offset.click.top>=this.containment[1]?e-i.grid[1]:e+i.grid[1],e=this.originalPageX+Math.round((n-this.originalPageX)/i.grid[0])*i.grid[0],n=!this.containment||e-this.offset.click.left>=this.containment[0]&&e-this.offset.click.left<=this.containment[2]?e:e-this.offset.click.left>=this.containment[0]?e-i.grid[0]:e+i.grid[0]),{top:s-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.scrollParent.scrollTop():r?0:o.scrollTop()),left:n-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():r?0:o.scrollLeft())}},_rearrange:function(t,e,i,n){i?i[0].appendChild(this.placeholder[0]):e.item[0].parentNode.insertBefore(this.placeholder[0],"down"===this.direction?e.item[0]:e.item[0].nextSibling),this.counter=this.counter?++this.counter:1;var s=this.counter;this._delay((function(){s===this.counter&&this.refreshPositions(!n)}))},_clear:function(t,e){this.reverting=!1;var i,n=[];if(!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem),this._noFinalSort=null,this.helper[0]===this.currentItem[0]){for(i in this._storedCSS)"auto"!==this._storedCSS[i]&&"static"!==this._storedCSS[i]||(this._storedCSS[i]="");this.currentItem.css(this._storedCSS),this._removeClass(this.currentItem,"ui-sortable-helper")}else this.currentItem.show();function s(t,e,i){return function(n){i._trigger(t,n,e._uiHash(e))}}for(this.fromOutside&&!e&&n.push((function(t){this._trigger("receive",t,this._uiHash(this.fromOutside))})),!this.fromOutside&&this.domPosition.prev===this.currentItem.prev().not(".ui-sortable-helper")[0]&&this.domPosition.parent===this.currentItem.parent()[0]||e||n.push((function(t){this._trigger("update",t,this._uiHash())})),this===this.currentContainer||e||(n.push((function(t){this._trigger("remove",t,this._uiHash())})),n.push(function(t){return function(e){t._trigger("receive",e,this._uiHash(this))}}.call(this,this.currentContainer)),n.push(function(t){return function(e){t._trigger("update",e,this._uiHash(this))}}.call(this,this.currentContainer))),i=this.containers.length-1;0<=i;i--)e||n.push(s("deactivate",this,this.containers[i])),this.containers[i].containerCache.over&&(n.push(s("out",this,this.containers[i])),this.containers[i].containerCache.over=0);if(this.storedCursor&&(this.document.find("body").css("cursor",this.storedCursor),this.storedStylesheet.remove()),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex","auto"===this._storedZIndex?"":this._storedZIndex),this.dragging=!1,e||this._trigger("beforeStop",t,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.cancelHelperRemoval||(this.helper[0]!==this.currentItem[0]&&this.helper.remove(),this.helper=null),!e){for(i=0;i<n.length;i++)n[i].call(this,t);this._trigger("stop",t,this._uiHash())}return this.fromOutside=!1,!this.cancelHelperRemoval},_trigger:function(){!1===t.Widget.prototype._trigger.apply(this,arguments)&&this.cancel()},_uiHash:function(e){var i=e||this;return{helper:i.helper,placeholder:i.placeholder||t([]),position:i.position,originalPosition:i.originalPosition,offset:i.positionAbs,item:i.currentItem,sender:e?e.element:null}}}),t.widget("ui.spinner",{version:"1.13.3",defaultElement:"<input>",widgetEventPrefix:"spin",options:{classes:{"ui-spinner":"ui-corner-all","ui-spinner-down":"ui-corner-br","ui-spinner-up":"ui-corner-tr"},culture:null,icons:{down:"ui-icon-triangle-1-s",up:"ui-icon-triangle-1-n"},incremental:!0,max:null,min:null,numberFormat:null,page:10,step:1,change:null,spin:null,start:null,stop:null},_create:function(){this._setOption("max",this.options.max),this._setOption("min",this.options.min),this._setOption("step",this.options.step),""!==this.value()&&this._value(this.element.val(),!0),this._draw(),this._on(this._events),this._refresh(),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_getCreateOptions:function(){var e=this._super(),i=this.element;return t.each(["min","max","step"],(function(t,n){var s=i.attr(n);null!=s&&s.length&&(e[n]=s)})),e},_events:{keydown:function(t){this._start(t)&&this._keydown(t)&&t.preventDefault()},keyup:"_stop",focus:function(){this.previous=this.element.val()},blur:function(t){this.cancelBlur?delete this.cancelBlur:(this._stop(),this._refresh(),this.previous!==this.element.val()&&this._trigger("change",t))},mousewheel:function(e,i){var n=t.ui.safeActiveElement(this.document[0]);if(this.element[0]===n&&i){if(!this.spinning&&!this._start(e))return!1;this._spin((0<i?1:-1)*this.options.step,e),clearTimeout(this.mousewheelTimer),this.mousewheelTimer=this._delay((function(){this.spinning&&this._stop(e)}),100),e.preventDefault()}},"mousedown .ui-spinner-button":function(e){var i;function n(){this.element[0]!==t.ui.safeActiveElement(this.document[0])&&(this.element.trigger("focus"),this.previous=i,this._delay((function(){this.previous=i})))}i=this.element[0]===t.ui.safeActiveElement(this.document[0])?this.previous:this.element.val(),e.preventDefault(),n.call(this),this.cancelBlur=!0,this._delay((function(){delete this.cancelBlur,n.call(this)})),!1!==this._start(e)&&this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e)},"mouseup .ui-spinner-button":"_stop","mouseenter .ui-spinner-button":function(e){if(t(e.currentTarget).hasClass("ui-state-active"))return!1!==this._start(e)&&void this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e)},"mouseleave .ui-spinner-button":"_stop"},_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap("<span>").parent().append("<a></a><a></a>")},_draw:function(){this._enhance(),this._addClass(this.uiSpinner,"ui-spinner","ui-widget ui-widget-content"),this._addClass("ui-spinner-input"),this.element.attr("role","spinbutton"),this.buttons=this.uiSpinner.children("a").attr("tabIndex",-1).attr("aria-hidden",!0).button({classes:{"ui-button":""}}),this._removeClass(this.buttons,"ui-corner-all"),this._addClass(this.buttons.first(),"ui-spinner-button ui-spinner-up"),this._addClass(this.buttons.last(),"ui-spinner-button ui-spinner-down"),this.buttons.first().button({icon:this.options.icons.up,showLabel:!1}),this.buttons.last().button({icon:this.options.icons.down,showLabel:!1}),this.buttons.height()>Math.ceil(.5*this.uiSpinner.height())&&0<this.uiSpinner.height()&&this.uiSpinner.height(this.uiSpinner.height())},_keydown:function(e){var i=this.options,n=t.ui.keyCode;switch(e.keyCode){case n.UP:return this._repeat(null,1,e),!0;case n.DOWN:return this._repeat(null,-1,e),!0;case n.PAGE_UP:return this._repeat(null,i.page,e),!0;case n.PAGE_DOWN:return this._repeat(null,-i.page,e),!0}return!1},_start:function(t){return!(!this.spinning&&!1===this._trigger("start",t))&&(this.counter||(this.counter=1),this.spinning=!0)},_repeat:function(t,e,i){t=t||500,clearTimeout(this.timer),this.timer=this._delay((function(){this._repeat(40,e,i)}),t),this._spin(e*this.options.step,i)},_spin:function(t,e){var i=this.value()||0;this.counter||(this.counter=1),i=this._adjustValue(i+t*this._increment(this.counter)),this.spinning&&!1===this._trigger("spin",e,{value:i})||(this._value(i),this.counter++)},_increment:function(t){var e=this.options.incremental;return e?"function"==typeof e?e(t):Math.floor(t*t*t/5e4-t*t/500+17*t/200+1):1},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min?Math.max(t,this._precisionOf(this.options.min)):t},_precisionOf:function(t){var e=(t=t.toString()).indexOf(".");return-1===e?0:t.length-e-1},_adjustValue:function(t){var e=this.options,i=null!==e.min?e.min:0,n=t-i;return t=i+Math.round(n/e.step)*e.step,t=parseFloat(t.toFixed(this._precision())),null!==e.max&&t>e.max?e.max:null!==e.min&&t<e.min?e.min:t},_stop:function(t){this.spinning&&(clearTimeout(this.timer),clearTimeout(this.mousewheelTimer),this.counter=0,this.spinning=!1,this._trigger("stop",t))},_setOption:function(t,e){var i;"culture"===t||"numberFormat"===t?(i=this._parse(this.element.val()),this.options[t]=e,this.element.val(this._format(i))):("max"!==t&&"min"!==t&&"step"!==t||"string"==typeof e&&(e=this._parse(e)),"icons"===t&&(i=this.buttons.first().find(".ui-icon"),this._removeClass(i,null,this.options.icons.up),this._addClass(i,null,e.up),i=this.buttons.last().find(".ui-icon"),this._removeClass(i,null,this.options.icons.down),this._addClass(i,null,e.down)),this._super(t,e))},_setOptionDisabled:function(t){this._super(t),this._toggleClass(this.uiSpinner,null,"ui-state-disabled",!!t),this.element.prop("disabled",!!t),this.buttons.button(t?"disable":"enable")},_setOptions:ct((function(t){this._super(t)})),_parse:function(t){return""===(t="string"==typeof t&&""!==t?window.Globalize&&this.options.numberFormat?Globalize.parseFloat(t,10,this.options.culture):+t:t)||isNaN(t)?null:t},_format:function(t){return""===t?"":window.Globalize&&this.options.numberFormat?Globalize.format(t,this.options.numberFormat,this.options.culture):t},_refresh:function(){this.element.attr({"aria-valuemin":this.options.min,"aria-valuemax":this.options.max,"aria-valuenow":this._parse(this.element.val())})},isValid:function(){var t=this.value();return null!==t&&t===this._adjustValue(t)},_value:function(t,e){var i;""!==t&&null!==(i=this._parse(t))&&(e||(i=this._adjustValue(i)),t=this._format(i)),this.element.val(t),this._refresh()},_destroy:function(){this.element.prop("disabled",!1).removeAttr("autocomplete role aria-valuemin aria-valuemax aria-valuenow"),this.uiSpinner.replaceWith(this.element)},stepUp:ct((function(t){this._stepUp(t)})),_stepUp:function(t){this._start()&&(this._spin((t||1)*this.options.step),this._stop())},stepDown:ct((function(t){this._stepDown(t)})),_stepDown:function(t){this._start()&&(this._spin((t||1)*-this.options.step),this._stop())},pageUp:ct((function(t){this._stepUp((t||1)*this.options.page)})),pageDown:ct((function(t){this._stepDown((t||1)*this.options.page)})),value:function(t){if(!arguments.length)return this._parse(this.element.val());ct(this._value).call(this,t)},widget:function(){return this.uiSpinner}}),!1!==t.uiBackCompat&&t.widget("ui.spinner",t.ui.spinner,{_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap(this._uiSpinnerHtml()).parent().append(this._buttonHtml())},_uiSpinnerHtml:function(){return"<span>"},_buttonHtml:function(){return"<a></a><a></a>"}}),t.ui.spinner,t.widget("ui.tabs",{version:"1.13.3",delay:300,options:{active:null,classes:{"ui-tabs":"ui-corner-all","ui-tabs-nav":"ui-corner-all","ui-tabs-panel":"ui-corner-bottom","ui-tabs-tab":"ui-corner-top"},collapsible:!1,event:"click",heightStyle:"content",hide:null,show:null,activate:null,beforeActivate:null,beforeLoad:null,load:null},_isLocal:(at=/#.*$/,function(t){var e=t.href.replace(at,""),i=location.href.replace(at,"");try{e=decodeURIComponent(e)}catch(t){}try{i=decodeURIComponent(i)}catch(t){}return 1<t.hash.length&&e===i}),_create:function(){var e=this,i=this.options;this.running=!1,this._addClass("ui-tabs","ui-widget ui-widget-content"),this._toggleClass("ui-tabs-collapsible",null,i.collapsible),this._processTabs(),i.active=this._initialActive(),Array.isArray(i.disabled)&&(i.disabled=t.uniqueSort(i.disabled.concat(t.map(this.tabs.filter(".ui-state-disabled"),(function(t){return e.tabs.index(t)})))).sort()),!1!==this.options.active&&this.anchors.length?this.active=this._findActive(i.active):this.active=t(),this._refresh(),this.active.length&&this.load(i.active)},_initialActive:function(){var e=this.options.active,i=this.options.collapsible,n=location.hash.substring(1);return null===e&&(n&&this.tabs.each((function(i,s){if(t(s).attr("aria-controls")===n)return e=i,!1})),null!==(e=null===e?this.tabs.index(this.tabs.filter(".ui-tabs-active")):e)&&-1!==e||(e=!!this.tabs.length&&0)),!1!==e&&-1===(e=this.tabs.index(this.tabs.eq(e)))&&(e=!i&&0),e=!i&&!1===e&&this.anchors.length?0:e},_getCreateEventData:function(){return{tab:this.active,panel:this.active.length?this._getPanelForTab(this.active):t()}},_tabKeydown:function(e){var i=t(t.ui.safeActiveElement(this.document[0])).closest("li"),n=this.tabs.index(i),s=!0;if(!this._handlePageNav(e)){switch(e.keyCode){case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:n++;break;case t.ui.keyCode.UP:case t.ui.keyCode.LEFT:s=!1,n--;break;case t.ui.keyCode.END:n=this.anchors.length-1;break;case t.ui.keyCode.HOME:n=0;break;case t.ui.keyCode.SPACE:return e.preventDefault(),clearTimeout(this.activating),void this._activate(n);case t.ui.keyCode.ENTER:return e.preventDefault(),clearTimeout(this.activating),void this._activate(n!==this.options.active&&n);default:return}e.preventDefault(),clearTimeout(this.activating),n=this._focusNextTab(n,s),e.ctrlKey||e.metaKey||(i.attr("aria-selected","false"),this.tabs.eq(n).attr("aria-selected","true"),this.activating=this._delay((function(){this.option("active",n)}),this.delay))}},_panelKeydown:function(e){this._handlePageNav(e)||e.ctrlKey&&e.keyCode===t.ui.keyCode.UP&&(e.preventDefault(),this.active.trigger("focus"))},_handlePageNav:function(e){return e.altKey&&e.keyCode===t.ui.keyCode.PAGE_UP?(this._activate(this._focusNextTab(this.options.active-1,!1)),!0):e.altKey&&e.keyCode===t.ui.keyCode.PAGE_DOWN?(this._activate(this._focusNextTab(this.options.active+1,!0)),!0):void 0},_findNextTab:function(e,i){for(var n=this.tabs.length-1;-1!==t.inArray(e=(e=n<e?0:e)<0?n:e,this.options.disabled);)e=i?e+1:e-1;return e},_focusNextTab:function(t,e){return t=this._findNextTab(t,e),this.tabs.eq(t).trigger("focus"),t},_setOption:function(t,e){"active"===t?this._activate(e):(this._super(t,e),"collapsible"===t&&(this._toggleClass("ui-tabs-collapsible",null,e),e||!1!==this.options.active||this._activate(0)),"event"===t&&this._setupEvents(e),"heightStyle"===t&&this._setupHeightStyle(e))},_sanitizeSelector:function(t){return t?t.replace(/[!"$%&'()*+,.\/:;<=>?@\[\]\^`{|}~]/g,"\\$&"):""},refresh:function(){var e=this.options,i=this.tablist.children(":has(a[href])");e.disabled=t.map(i.filter(".ui-state-disabled"),(function(t){return i.index(t)})),this._processTabs(),!1!==e.active&&this.anchors.length?this.active.length&&!t.contains(this.tablist[0],this.active[0])?this.tabs.length===e.disabled.length?(e.active=!1,this.active=t()):this._activate(this._findNextTab(Math.max(0,e.active-1),!1)):e.active=this.tabs.index(this.active):(e.active=!1,this.active=t()),this._refresh()},_refresh:function(){this._setOptionDisabled(this.options.disabled),this._setupEvents(this.options.event),this._setupHeightStyle(this.options.heightStyle),this.tabs.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}),this.panels.not(this._getPanelForTab(this.active)).hide().attr({"aria-hidden":"true"}),this.active.length?(this.active.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}),this._addClass(this.active,"ui-tabs-active","ui-state-active"),this._getPanelForTab(this.active).show().attr({"aria-hidden":"false"})):this.tabs.eq(0).attr("tabIndex",0)},_processTabs:function(){var e=this,i=this.tabs,n=this.anchors,s=this.panels;this.tablist=this._getList().attr("role","tablist"),this._addClass(this.tablist,"ui-tabs-nav","ui-helper-reset ui-helper-clearfix ui-widget-header"),this.tablist.on("mousedown"+this.eventNamespace,"> li",(function(e){t(this).is(".ui-state-disabled")&&e.preventDefault()})).on("focus"+this.eventNamespace,".ui-tabs-anchor",(function(){t(this).closest("li").is(".ui-state-disabled")&&this.blur()})),this.tabs=this.tablist.find("> li:has(a[href])").attr({role:"tab",tabIndex:-1}),this._addClass(this.tabs,"ui-tabs-tab","ui-state-default"),this.anchors=this.tabs.map((function(){return t("a",this)[0]})).attr({tabIndex:-1}),this._addClass(this.anchors,"ui-tabs-anchor"),this.panels=t(),this.anchors.each((function(i,n){var s,o,r,a=t(n).uniqueId().attr("id"),l=t(n).closest("li"),h=l.attr("aria-controls");e._isLocal(n)?(r=(s=n.hash).substring(1),o=e.element.find(e._sanitizeSelector(s))):(r=l.attr("aria-controls")||t({}).uniqueId()[0].id,(o=e.element.find(s="#"+r)).length||(o=e._createPanel(r)).insertAfter(e.panels[i-1]||e.tablist),o.attr("aria-live","polite")),o.length&&(e.panels=e.panels.add(o)),h&&l.data("ui-tabs-aria-controls",h),l.attr({"aria-controls":r,"aria-labelledby":a}),o.attr("aria-labelledby",a)})),this.panels.attr("role","tabpanel"),this._addClass(this.panels,"ui-tabs-panel","ui-widget-content"),i&&(this._off(i.not(this.tabs)),this._off(n.not(this.anchors)),this._off(s.not(this.panels)))},_getList:function(){return this.tablist||this.element.find("ol, ul").eq(0)},_createPanel:function(e){return t("<div>").attr("id",e).data("ui-tabs-destroy",!0)},_setOptionDisabled:function(e){var i,n;for(Array.isArray(e)&&(e.length?e.length===this.anchors.length&&(e=!0):e=!1),n=0;i=this.tabs[n];n++)i=t(i),!0===e||-1!==t.inArray(n,e)?(i.attr("aria-disabled","true"),this._addClass(i,null,"ui-state-disabled")):(i.removeAttr("aria-disabled"),this._removeClass(i,null,"ui-state-disabled"));this.options.disabled=e,this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!0===e)},_setupEvents:function(e){var i={};e&&t.each(e.split(" "),(function(t,e){i[e]="_eventHandler"})),this._off(this.anchors.add(this.tabs).add(this.panels)),this._on(!0,this.anchors,{click:function(t){t.preventDefault()}}),this._on(this.anchors,i),this._on(this.tabs,{keydown:"_tabKeydown"}),this._on(this.panels,{keydown:"_panelKeydown"}),this._focusable(this.tabs),this._hoverable(this.tabs)},_setupHeightStyle:function(e){var i,n=this.element.parent();"fill"===e?(i=n.height(),i-=this.element.outerHeight()-this.element.height(),this.element.siblings(":visible").each((function(){var e=t(this),n=e.css("position");"absolute"!==n&&"fixed"!==n&&(i-=e.outerHeight(!0))})),this.element.children().not(this.panels).each((function(){i-=t(this).outerHeight(!0)})),this.panels.each((function(){t(this).height(Math.max(0,i-t(this).innerHeight()+t(this).height()))})).css("overflow","auto")):"auto"===e&&(i=0,this.panels.each((function(){i=Math.max(i,t(this).height("").height())})).height(i))},_eventHandler:function(e){var i=this.options,n=this.active,s=t(e.currentTarget).closest("li"),o=s[0]===n[0],r=o&&i.collapsible,a=r?t():this._getPanelForTab(s),l=n.length?this._getPanelForTab(n):t();n={oldTab:n,oldPanel:l,newTab:r?t():s,newPanel:a},e.preventDefault(),s.hasClass("ui-state-disabled")||s.hasClass("ui-tabs-loading")||this.running||o&&!i.collapsible||!1===this._trigger("beforeActivate",e,n)||(i.active=!r&&this.tabs.index(s),this.active=o?t():s,this.xhr&&this.xhr.abort(),l.length||a.length||t.error("jQuery UI Tabs: Mismatching fragment identifier."),a.length&&this.load(this.tabs.index(s),e),this._toggle(e,n))},_toggle:function(e,i){var n=this,s=i.newPanel,o=i.oldPanel;function r(){n.running=!1,n._trigger("activate",e,i)}function a(){n._addClass(i.newTab.closest("li"),"ui-tabs-active","ui-state-active"),s.length&&n.options.show?n._show(s,n.options.show,r):(s.show(),r())}this.running=!0,o.length&&this.options.hide?this._hide(o,this.options.hide,(function(){n._removeClass(i.oldTab.closest("li"),"ui-tabs-active","ui-state-active"),a()})):(this._removeClass(i.oldTab.closest("li"),"ui-tabs-active","ui-state-active"),o.hide(),a()),o.attr("aria-hidden","true"),i.oldTab.attr({"aria-selected":"false","aria-expanded":"false"}),s.length&&o.length?i.oldTab.attr("tabIndex",-1):s.length&&this.tabs.filter((function(){return 0===t(this).attr("tabIndex")})).attr("tabIndex",-1),s.attr("aria-hidden","false"),i.newTab.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_activate:function(e){(e=this._findActive(e))[0]!==this.active[0]&&(e=(e=e.length?e:this.active).find(".ui-tabs-anchor")[0],this._eventHandler({target:e,currentTarget:e,preventDefault:t.noop}))},_findActive:function(e){return!1===e?t():this.tabs.eq(e)},_getIndex:function(e){return"string"==typeof e?this.anchors.index(this.anchors.filter("[href$='"+t.escapeSelector(e)+"']")):e},_destroy:function(){this.xhr&&this.xhr.abort(),this.tablist.removeAttr("role").off(this.eventNamespace),this.anchors.removeAttr("role tabIndex").removeUniqueId(),this.tabs.add(this.panels).each((function(){t.data(this,"ui-tabs-destroy")?t(this).remove():t(this).removeAttr("role tabIndex aria-live aria-busy aria-selected aria-labelledby aria-hidden aria-expanded")})),this.tabs.each((function(){var e=t(this),i=e.data("ui-tabs-aria-controls");i?e.attr("aria-controls",i).removeData("ui-tabs-aria-controls"):e.removeAttr("aria-controls")})),this.panels.show(),"content"!==this.options.heightStyle&&this.panels.css("height","")},enable:function(e){var i=this.options.disabled;!1!==i&&(i=void 0!==e&&(e=this._getIndex(e),Array.isArray(i)?t.map(i,(function(t){return t!==e?t:null})):t.map(this.tabs,(function(t,i){return i!==e?i:null}))),this._setOptionDisabled(i))},disable:function(e){var i=this.options.disabled;if(!0!==i){if(void 0===e)i=!0;else{if(e=this._getIndex(e),-1!==t.inArray(e,i))return;i=Array.isArray(i)?t.merge([e],i).sort():[e]}this._setOptionDisabled(i)}},load:function(e,i){function n(t,e){"abort"===e&&s.panels.stop(!1,!0),s._removeClass(o,"ui-tabs-loading"),r.removeAttr("aria-busy"),t===s.xhr&&delete s.xhr}e=this._getIndex(e);var s=this,o=this.tabs.eq(e),r=(e=o.find(".ui-tabs-anchor"),this._getPanelForTab(o)),a={tab:o,panel:r};this._isLocal(e[0])||(this.xhr=t.ajax(this._ajaxSettings(e,i,a)),this.xhr&&"canceled"!==this.xhr.statusText&&(this._addClass(o,"ui-tabs-loading"),r.attr("aria-busy","true"),this.xhr.done((function(t,e,o){setTimeout((function(){r.html(t),s._trigger("load",i,a),n(o,e)}),1)})).fail((function(t,e){setTimeout((function(){n(t,e)}),1)}))))},_ajaxSettings:function(e,i,n){var s=this;return{url:e.attr("href").replace(/#.*$/,""),beforeSend:function(e,o){return s._trigger("beforeLoad",i,t.extend({jqXHR:e,ajaxSettings:o},n))}}},_getPanelForTab:function(e){return e=t(e).attr("aria-controls"),this.element.find(this._sanitizeSelector("#"+e))}}),!1!==t.uiBackCompat&&t.widget("ui.tabs",t.ui.tabs,{_processTabs:function(){this._superApply(arguments),this._addClass(this.tabs,"ui-tab")}}),t.ui.tabs,t.widget("ui.tooltip",{version:"1.13.3",options:{classes:{"ui-tooltip":"ui-corner-all ui-widget-shadow"},content:function(){var e=t(this).attr("title");return t("<a>").text(e).html()},hide:!0,items:"[title]:not([disabled])",position:{my:"left top+15",at:"left bottom",collision:"flipfit flip"},show:!0,track:!1,close:null,open:null},_addDescribedBy:function(t,e){var i=(t.attr("aria-describedby")||"").split(/\s+/);i.push(e),t.data("ui-tooltip-id",e).attr("aria-describedby",String.prototype.trim.call(i.join(" ")))},_removeDescribedBy:function(e){var i=e.data("ui-tooltip-id"),n=(e.attr("aria-describedby")||"").split(/\s+/);-1!==(i=t.inArray(i,n))&&n.splice(i,1),e.removeData("ui-tooltip-id"),(n=String.prototype.trim.call(n.join(" ")))?e.attr("aria-describedby",n):e.removeAttr("aria-describedby")},_create:function(){this._on({mouseover:"open",focusin:"open"}),this.tooltips={},this.parents={},this.liveRegion=t("<div>").attr({role:"log","aria-live":"assertive","aria-relevant":"additions"}).appendTo(this.document[0].body),this._addClass(this.liveRegion,null,"ui-helper-hidden-accessible"),this.disabledTitles=t([])},_setOption:function(e,i){var n=this;this._super(e,i),"content"===e&&t.each(this.tooltips,(function(t,e){n._updateContent(e.element)}))},_setOptionDisabled:function(t){this[t?"_disable":"_enable"]()},_disable:function(){var e=this;t.each(this.tooltips,(function(i,n){var s=t.Event("blur");s.target=s.currentTarget=n.element[0],e.close(s,!0)})),this.disabledTitles=this.disabledTitles.add(this.element.find(this.options.items).addBack().filter((function(){var e=t(this);if(e.is("[title]"))return e.data("ui-tooltip-title",e.attr("title")).removeAttr("title")})))},_enable:function(){this.disabledTitles.each((function(){var e=t(this);e.data("ui-tooltip-title")&&e.attr("title",e.data("ui-tooltip-title"))})),this.disabledTitles=t([])},open:function(e){var i=this,n=t(e?e.target:this.element).closest(this.options.items);n.length&&!n.data("ui-tooltip-id")&&(n.attr("title")&&n.data("ui-tooltip-title",n.attr("title")),n.data("ui-tooltip-open",!0),e&&"mouseover"===e.type&&n.parents().each((function(){var e,n=t(this);n.data("ui-tooltip-open")&&((e=t.Event("blur")).target=e.currentTarget=this,i.close(e,!0)),n.attr("title")&&(n.uniqueId(),i.parents[this.id]={element:this,title:n.attr("title")},n.attr("title",""))})),this._registerCloseHandlers(e,n),this._updateContent(n,e))},_updateContent:function(t,e){var i=this.options.content,n=this,s=e?e.type:null;if("string"==typeof i||i.nodeType||i.jquery)return this._open(e,t,i);(i=i.call(t[0],(function(i){n._delay((function(){t.data("ui-tooltip-open")&&(e&&(e.type=s),this._open(e,t,i))}))})))&&this._open(e,t,i)},_open:function(e,i,n){var s,o,r,a=t.extend({},this.options.position);function l(t){a.of=t,s.is(":hidden")||s.position(a)}n&&((r=this._find(i))?r.tooltip.find(".ui-tooltip-content").html(n):(i.is("[title]")&&(e&&"mouseover"===e.type?i.attr("title",""):i.removeAttr("title")),r=this._tooltip(i),s=r.tooltip,this._addDescribedBy(i,s.attr("id")),s.find(".ui-tooltip-content").html(n),this.liveRegion.children().hide(),(r=t("<div>").html(s.find(".ui-tooltip-content").html())).removeAttr("name").find("[name]").removeAttr("name"),r.removeAttr("id").find("[id]").removeAttr("id"),r.appendTo(this.liveRegion),this.options.track&&e&&/^mouse/.test(e.type)?(this._on(this.document,{mousemove:l}),l(e)):s.position(t.extend({of:i},this.options.position)),s.hide(),this._show(s,this.options.show),this.options.track&&this.options.show&&this.options.show.delay&&(o=this.delayedShow=setInterval((function(){s.is(":visible")&&(l(a.of),clearInterval(o))}),13)),this._trigger("open",e,{tooltip:s})))},_registerCloseHandlers:function(e,i){var n={keyup:function(e){e.keyCode===t.ui.keyCode.ESCAPE&&((e=t.Event(e)).currentTarget=i[0],this.close(e,!0))}};i[0]!==this.element[0]&&(n.remove=function(){var t=this._find(i);t&&this._removeTooltip(t.tooltip)}),e&&"mouseover"!==e.type||(n.mouseleave="close"),e&&"focusin"!==e.type||(n.focusout="close"),this._on(!0,i,n)},close:function(e){var i,n=this,s=t(e?e.currentTarget:this.element),o=this._find(s);o?(i=o.tooltip,o.closing||(clearInterval(this.delayedShow),s.data("ui-tooltip-title")&&!s.attr("title")&&s.attr("title",s.data("ui-tooltip-title")),this._removeDescribedBy(s),o.hiding=!0,i.stop(!0),this._hide(i,this.options.hide,(function(){n._removeTooltip(t(this))})),s.removeData("ui-tooltip-open"),this._off(s,"mouseleave focusout keyup"),s[0]!==this.element[0]&&this._off(s,"remove"),this._off(this.document,"mousemove"),e&&"mouseleave"===e.type&&t.each(this.parents,(function(e,i){t(i.element).attr("title",i.title),delete n.parents[e]})),o.closing=!0,this._trigger("close",e,{tooltip:i}),o.hiding)||(o.closing=!1)):s.removeData("ui-tooltip-open")},_tooltip:function(e){var i=t("<div>").attr("role","tooltip"),n=t("<div>").appendTo(i),s=i.uniqueId().attr("id");return this._addClass(n,"ui-tooltip-content"),this._addClass(i,"ui-tooltip","ui-widget ui-widget-content"),i.appendTo(this._appendTo(e)),this.tooltips[s]={element:e,tooltip:i}},_find:function(t){return(t=t.data("ui-tooltip-id"))?this.tooltips[t]:null},_removeTooltip:function(t){clearInterval(this.delayedShow),t.remove(),delete this.tooltips[t.attr("id")]},_appendTo:function(t){return(t=t.closest(".ui-front, dialog")).length?t:this.document[0].body},_destroy:function(){var e=this;t.each(this.tooltips,(function(i,n){var s=t.Event("blur");n=n.element,s.target=s.currentTarget=n[0],e.close(s,!0),t("#"+i).remove(),n.data("ui-tooltip-title")&&(n.attr("title")||n.attr("title",n.data("ui-tooltip-title")),n.removeData("ui-tooltip-title"))})),this.liveRegion.remove()}}),!1!==t.uiBackCompat&&t.widget("ui.tooltip",t.ui.tooltip,{options:{tooltipClass:null},_tooltip:function(){var t=this._superApply(arguments);return this.options.tooltipClass&&t.tooltip.addClass(this.options.tooltipClass),t}}),t.ui.tooltip})),function(t){var e,i,n,s;function o(t,e){var i,n;1<t.originalEvent.touches.length||(t.preventDefault(),i=t.originalEvent.changedTouches[0],(n=document.createEvent("MouseEvents")).initMouseEvent(e,!0,!0,window,1,i.screenX,i.screenY,i.clientX,i.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(n))}t.support.touch="ontouchend"in document,t.support.touch&&(e=t.ui.mouse.prototype,i=e._mouseInit,n=e._mouseDestroy,e._touchStart=function(t){!s&&this._mouseCapture(t.originalEvent.changedTouches[0])&&(s=!0,this._touchMoved=!1,o(t,"mouseover"),o(t,"mousemove"),o(t,"mousedown"))},e._touchMove=function(t){s&&(this._touchMoved=!0,o(t,"mousemove"))},e._touchEnd=function(t){s&&(o(t,"mouseup"),o(t,"mouseout"),this._touchMoved||o(t,"click"),s=!1)},e._mouseInit=function(){this.element.bind({touchstart:t.proxy(this,"_touchStart"),touchmove:t.proxy(this,"_touchMove"),touchend:t.proxy(this,"_touchEnd")}),i.call(this)},e._mouseDestroy=function(){this.element.unbind({touchstart:t.proxy(this,"_touchStart"),touchmove:t.proxy(this,"_touchMove"),touchend:t.proxy(this,"_touchEnd")}),n.call(this)})}(jQuery),function(t,e,i){function n(n,s){s=s||n+i;var o=t(),r=n+"."+s+"-special-event";function a(e){t(o).each((function(){var i=t(this);this===e.target||i.has(e.target).length||i.triggerHandler(s,[e.target])}))}t.event.special[s]={setup:function(){1===(o=o.add(this)).length&&t(e).on(r,a)},teardown:function(){0===(o=o.not(this)).length&&t(e).off(r)},add:function(t){var e=t.handler;t.handler=function(t,i){t.target=i,e.apply(this,arguments)}}}}t.map("click dblclick mousemove mousedown mouseup mouseover mouseout change select submit keydown keypress keyup".split(" "),(function(t){n(t)})),n("focusin","focus"+i),n("focusout","blur"+i),t.addOutsideEvent=n}(jQuery,document,"outside");{function btoa(t){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",i=[],n=0;n<t.length;){var s,o=((s=((s=t.charCodeAt(n++))<<16)+(((a=t.charCodeAt(n++))||0)<<8)+((l=t.charCodeAt(n++))||0))&63<<18)>>18,r=(258048&s)>>12,a=isNaN(a)?64:(4032&s)>>6,l=isNaN(l)?64:63&s;i[i.length]=e.charAt(o),i[i.length]=e.charAt(r),i[i.length]=e.charAt(a),i[i.length]=e.charAt(l)}return i.join("")}}{function atob(t){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",i=t.length%4!=0,n=new RegExp("[^"+e+"]").test(t),s=/=/.test(t)&&(/=[^=]/.test(t)||/={3}/.test(t));if(i||n||s)throw new Error("Invalid base64 data");for(var o=[],r=0;r<t.length;){var a=e.indexOf(t.charAt(r++)),l=e.indexOf(t.charAt(r++)),h=e.indexOf(t.charAt(r++)),c=e.indexOf(t.charAt(r++));a=(a<<18)+(l<<12)+((63&h)<<6)+(63&c),l=64==h?-1:(65280&a)>>8,h=64==c?-1:255&a,o[o.length]=String.fromCharCode((a&255<<16)>>16),0<=l&&(o[o.length]=String.fromCharCode(l)),0<=h&&(o[o.length]=String.fromCharCode(h))}return o.join("")}}function TrimString(){var t=new String(this);return(t=t.replace(/^\s+/g,"")).replace(/\s+$/g,"")}function stringReplace(t,e){var i=new String(this);return""!=t&&""!=i&&null!=e&&(t=new RegExp(t,"g"),i=i.replace(t,e)),i}function RemoveFilenameExtension(t){var e=t.lastIndexOf("/"),i=t.lastIndexOf(".");return-1<i&&e<i?t.substring(0,i):t}function GetFilenamePath(t){var e=t.lastIndexOf("/");return-1<e?t.substring(0,e):t}function GetFullFilename(t){var e=t.lastIndexOf("/"),i=t.lastIndexOf(".");return-1<i&&e<i?t.substring(e+1):""}function CartesianPointObject(t,e,i){this.x=parseFloat(t),this.y=parseFloat(e),null==i&&(i=0),this.z=parseFloat(i),this.type="JYCartesianPointObject",this.distanceTo=CartesianPointObject_DistanceTo,this.horizontalDistanceTo=CartesianPointObject_HorizontalDistanceTo,this.verticalDistanceTo=CartesianPointObject_VerticalDistanceTo,this.zDistanceTo=CartesianPointObject_ZDistanceTo,this.className="JYCartesianPointObject",this.classVersion=parseFloat(1.1)}function CartesianPointObject_ToString(){return this.type+" ("+this.x+", "+this.y+", "+this.z+")"}function CartesianPointObject_DistanceTo(t){return Math.sqrt(Math.pow(t.x-this.x,2)+Math.pow(t.y-this.y,2)+Math.pow(t.z-this.z,2))}function CartesianPointObject_HorizontalDistanceTo(t){return Math.abs(t.x-this.x)}function CartesianPointObject_VerticalDistanceTo(t){return Math.abs(t.y-this.y)}function CartesianPointObject_ZDistanceTo(t){return Math.abs(t.z-this.z)}"object"!=typeof JSON&&(JSON={}),function(){"use strict";var rx_one=/^[\],:{}\s]*$/,rx_two=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,rx_three=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rx_four=/(?:^|:|,)(?:\s*\[)+/g,rx_escapable=/[\\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,rx_dangerous=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta,rep;function f(t){return t<10?"0"+t:t}function this_value(){return this.valueOf()}function quote(t){return rx_escapable.lastIndex=0,rx_escapable.test(t)?'"'+t.replace(rx_escapable,(function(t){var e=meta[t];return"string"==typeof e?e:"\\u"+("0000"+t.charCodeAt(0).toString(16)).slice(-4)}))+'"':'"'+t+'"'}function str(t,e){var i,n,s,o,r,a=gap,l=e[t];switch(l&&"object"==typeof l&&"function"==typeof l.toJSON&&(l=l.toJSON(t)),typeof(l="function"==typeof rep?rep.call(e,t,l):l)){case"string":return quote(l);case"number":return isFinite(l)?String(l):"null";case"boolean":case"null":return String(l);case"object":if(!l)return"null";if(gap+=indent,r=[],"[object Array]"===Object.prototype.toString.apply(l)){for(o=l.length,i=0;i<o;i+=1)r[i]=str(i,l)||"null";s=0===r.length?"[]":gap?"[\n"+gap+r.join(",\n"+gap)+"\n"+a+"]":"["+r.join(",")+"]"}else{if(rep&&"object"==typeof rep)for(o=rep.length,i=0;i<o;i+=1)"string"==typeof rep[i]&&(s=str(n=rep[i],l))&&r.push(quote(n)+(gap?": ":":")+s);else for(n in l)Object.prototype.hasOwnProperty.call(l,n)&&(s=str(n,l))&&r.push(quote(n)+(gap?": ":":")+s);s=0===r.length?"{}":gap?"{\n"+gap+r.join(",\n"+gap)+"\n"+a+"}":"{"+r.join(",")+"}"}return gap=a,s}}"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},Boolean.prototype.toJSON=this_value,Number.prototype.toJSON=this_value,String.prototype.toJSON=this_value),"function"!=typeof JSON.stringify&&(meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},JSON.stringify=function(t,e,i){var n;if(indent=gap="","number"==typeof i)for(n=0;n<i;n+=1)indent+=" ";else"string"==typeof i&&(indent=i);if(!(rep=e)||"function"==typeof e||"object"==typeof e&&"number"==typeof e.length)return str("",{"":t});throw new Error("JSON.stringify")}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){var j;function walk(t,e){var i,n,s=t[e];if(s&&"object"==typeof s)for(i in s)Object.prototype.hasOwnProperty.call(s,i)&&(void 0!==(n=walk(s,i))?s[i]=n:delete s[i]);return reviver.call(t,e,s)}if(text=String(text),rx_dangerous.lastIndex=0,rx_dangerous.test(text)&&(text=text.replace(rx_dangerous,(function(t){return"\\u"+("0000"+t.charCodeAt(0).toString(16)).slice(-4)}))),rx_one.test(text.replace(rx_two,"@").replace(rx_three,"]").replace(rx_four,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}(),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}((function(t){var e=/\+/g;function i(t){return s.raw?t:encodeURIComponent(t)}function n(i,n){return i=s.raw?i:function(t){0===t.indexOf('"')&&(t=t.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\"));try{t=decodeURIComponent(t.replace(e," "))}catch(t){return}try{return s.json?JSON.parse(t):t}catch(t){}}(i),t.isFunction(n)?n(i):i}var s=t.cookie=function(e,o,r){var a,l;if(void 0!==o&&!t.isFunction(o))return"number"==typeof(r=t.extend({},s.defaults,r)).expires&&(a=r.expires,(l=r.expires=new Date).setDate(l.getDate()+a)),document.cookie=[i(e),"=",(l=o,i(s.json?JSON.stringify(l):String(l))),r.expires?"; expires="+r.expires.toUTCString():"",r.path?"; path="+r.path:"",r.domain?"; domain="+r.domain:"",r.secure?"; secure":""].join("");for(var h=e?void 0:{},c=document.cookie?document.cookie.split("; "):[],u=0,d=c.length;u<d;u++){var p=(p=(f=c[u].split("=")).shift(),s.raw?p:decodeURIComponent(p)),f=f.join("=");if(e&&e===p){h=n(f,o);break}e||void 0===(f=n(f))||(h[p]=f)}return h};s.defaults={},t.removeCookie=function(e,i){return void 0!==t.cookie(e)&&(t.cookie(e,"",t.extend({},i,{expires:-1})),!0)}})),window.$repairInfo={jsonProxy:function(t){var e=this;this.serviceUrl=t,this.invoke=function(t){var i=e.serviceUrl;$.getJSON(i,null,t.success,t.fail)}},makeUnselectable:function(t){$(t).find("*").each((function(){$(this).attr("unselectable","on")}))}},function(t){t.fn.replaceTagName=function(e){for(var i=[],n=this.length;n--;){for(var s=document.createElement(e),o=this[n],r=o.attributes,a=r.length-1;0<=a;a--){var l=r[a];s.setAttribute(l.name,l.value)}t(o).after(s),t(s).append(o.innerHTML),t(o).remove(),i[n-1]=s}return t(i)},t.extend(t.expr[":"],{scrollable:function(e){return e.clientHeight<e.scrollHeight&&(-1!=t.inArray(t(e).css("overflowY"),["scroll","auto"])||-1!=t.inArray(t(e).css("overflow"),["scroll","auto"]))}}),t.fn.imagesLoaded=function(){var e;return($imgs=this.find('img[src!=""]')).length?(e=[],$imgs.each((function(){var i=t.Deferred(),n=(e.push(i),new Image);n.onload=function(){i.resolve()},n.src=this.src})),t.when.apply(t,e)):(new t.Deferred).resolve().promise()}}(jQuery),jQuery.each({getSelection:function(){var t=this.jquery?this[0]:this;return(("selectionStart"in t?function(){try{var e=t.selectionEnd-t.selectionStart;return{start:t.selectionStart,end:t.selectionEnd,length:e,text:t.value.substr(t.selectionStart,e)}}catch(e){return!1}}:document.selection&&function(){var e,i=document.selection.createRange(),n=0,s=0,o=0;if(null==i)return{start:0,end:t.value.length,length:0};try{if(t.createTextRange){var r,a=t.createTextRange(),l=a.duplicate();for(a.moveToBookmark(i.getBookmark()),l.setEndPoint("EndToStart",a),s=r=l.text.length,o=0;o<r;o++)13==l.text.charCodeAt(o)&&s--}}catch(e){return!1}for(n=e=i.text.length,o=0;o<e;o++)13==i.text.charCodeAt(o)&&n--;return{start:s,end:s+n,length:n,text:i.text}})||function(){return{start:0,end:t.value.length,length:0}})()},setSelection:function(t,e){var i,n=document.getElementById($(this).attr("id")),s=this;if(!n)return $(s);if(n.setSelectionRange)try{setTimeout((function(){n.focus(),n.setSelectionRange(t,e)}),300)}catch(n){try{setTimeout((function(){$(s).eq(0).setSelectionRange(t,e)}),200)}catch(n){debug(n)}}else n.createTextRange?setTimeout((function(){(i=n.createTextRange()).collapse(!0),i.moveEnd("character",e),i.moveStart("character",t),i.select()}),200):n.selectionStart&&setTimeout((function(){n.selectionStart=t,n.selectionEnd=e}),200);return $(this)},replaceSelection:function(){var t=this.jquery?this[0]:this,e=arguments[0]||"";return(("selectionStart"in t?function(){return t.value=t.value.substr(0,t.selectionStart)+e+t.value.substr(t.selectionEnd,t.value.length),this}:document.selection&&function(){return t.focus(),document.selection.createRange().text=e,this})||function(){return t.value+=e,this})()}},(function(t){jQuery.fn[t]=this})),function(){"use strict";var t=Namespace.register("PDConstants");t.MillisecondsPerMinute=6e4,t.MillisecondsPerHour=60*t.MillisecondsPerMinute,t.MillisecondsPerDay=24*t.MillisecondsPerHour,t.MillisecondsPerHalfDay=12*t.MillisecondsPerHour}(),function(){"use strict";Namespace.register("PDScripts.Convert").ajaxToPromise=function(t){var e=Promise.resolve(t);return e.xhr={abort:t.abort,getAllResponseHeaders:t.getAllResponseHeaders,getResponseHeader:t.getResponseHeader,progress:t.progress,readyState:t.readyState,statusCode:t.statusCode,statusText:t.statusText,responseText:t.responseText},e}}(),function(t){var e="object"==typeof self&&self.self===self&&self||"object"==typeof global&&global.global===global&&global;if("function"==typeof define&&define.amd)define(["underscore","jquery","exports"],(function(i,n,s){e.Backbone=t(e,s,i,n)}));else if("undefined"!=typeof exports){var i,n=require("underscore");try{i=require("jquery")}catch(i){}t(e,exports,n,i)}else e.Backbone=t(e,{},e._,e.jQuery||e.Zepto||e.ender||e.$)}((function(t,e,i,n){function s(t,e,n){i.each(e,(function(e,s){i[s]&&(t.prototype[s]=function(t,e,n){switch(t){case 1:return function(){return i[e](this[n])};case 2:return function(t){return i[e](this[n],t)};case 3:return function(t,s){return i[e](this[n],l(t,this),s)};case 4:return function(t,s,o){return i[e](this[n],l(t,this),s,o)};default:return function(){var t=a.call(arguments);return t.unshift(this[n]),i[e].apply(i,t)}}}(e,s,n))}))}function o(t,e,i){i=Math.min(Math.max(i,0),t.length);for(var n=Array(t.length-i),s=e.length,o=0;o<n.length;o++)n[o]=t[o+i];for(o=0;o<s;o++)t[o+i]=e[o];for(o=0;o<n.length;o++)t[o+s+i]=n[o]}var r=t.Backbone,a=Array.prototype.slice,l=(e.VERSION="1.3.3",e.$=n,e.noConflict=function(){return t.Backbone=r,this},e.emulateHTTP=!1,e.emulateJSON=!1,function(t,e){var n;return i.isFunction(t)?t:i.isObject(t)&&!e._isModel(t)?(n=i.matches(t),function(t){return n(t.attributes)}):i.isString(t)?function(e){return e.get(t)}:t}),h=(n=e.Events={},/\s+/),c=function(t,e,n,s,o){var r,a=0;if(n&&"object"==typeof n){void 0!==s&&"context"in o&&void 0===o.context&&(o.context=s);for(r=i.keys(n);a<r.length;a++)e=c(t,e,r[a],n[r[a]],o)}else if(n&&h.test(n))for(r=n.split(h);a<r.length;a++)e=t(e,r[a],s,o);else e=t(e,n,s,o);return e},u=(n.on=function(t,e,i){return u(this,t,e,i)},function(t,e,i,n,s){return t._events=c(d,t._events||{},e,i,{context:n,ctx:t,listening:s}),s&&((t._listeners||(t._listeners={}))[s.id]=s),t}),d=(n.listenTo=function(t,e,n){var s,o,r,a;return t&&(s=t._listenId||(t._listenId=i.uniqueId("l")),(a=(o=this._listeningTo||(this._listeningTo={}))[s])||(r=this._listenId||(this._listenId=i.uniqueId("l")),a=o[s]={obj:t,objId:s,id:r,listeningTo:o,count:0}),u(t,e,n,this,a)),this},function(t,e,i,n){var s,o;return i&&(e=t[e]||(t[e]=[]),s=n.context,o=n.ctx,(n=n.listening)&&n.count++,e.push({callback:i,context:s,ctx:s||o,listening:n})),t}),p=(n.off=function(t,e,i){return this._events&&(this._events=c(p,this._events,t,e,{context:i,listeners:this._listeners})),this},n.stopListening=function(t,e,n){var s=this._listeningTo;if(s)for(var o=t?[t._listenId]:i.keys(s),r=0;r<o.length;r++){var a=s[o[r]];if(!a)break;a.obj.off(e,n,this)}return this},function(t,e,n,s){if(t){var o,r=0,a=s.context,l=s.listeners;if(e||n||a){for(var h=e?[e]:i.keys(t);r<h.length;r++){var c=t[e=h[r]];if(!c)break;for(var u=[],d=0;d<c.length;d++){var p=c[d];n&&n!==p.callback&&n!==p.callback._callback||a&&a!==p.context?u.push(p):(o=p.listening)&&0==--o.count&&(delete l[o.id],delete o.listeningTo[o.objId])}u.length?t[e]=u:delete t[e]}return t}for(var f=i.keys(l);r<f.length;r++)delete l[(o=l[f[r]]).id],delete o.listeningTo[o.objId]}}),f=(n.once=function(t,e,n){var s=c(f,{},t,e,i.bind(this.off,this));return this.on(s,e="string"==typeof t&&null==n?void 0:e,n)},n.listenToOnce=function(t,e,n){return e=c(f,{},e,n,i.bind(this.stopListening,this,t)),this.listenTo(t,e)},function(t,e,n,s){var o;return n&&((o=t[e]=i.once((function(){s(e,o),n.apply(this,arguments)})))._callback=n),t}),g=(n.trigger=function(t){if(this._events){for(var e=Math.max(0,arguments.length-1),i=Array(e),n=0;n<e;n++)i[n]=arguments[n+1];c(g,this._events,t,void 0,i)}return this},function(t,e,i,n){var s,o;return t&&(s=t[e],o=t.all,s&&(o=o&&o.slice()),s&&m(s,n),o)&&m(o,[e].concat(n)),t}),m=function(t,e){var i,n=-1,s=t.length,o=e[0],r=e[1],a=e[2];switch(e.length){case 0:for(;++n<s;)(i=t[n]).callback.call(i.ctx);return;case 1:for(;++n<s;)(i=t[n]).callback.call(i.ctx,o);return;case 2:for(;++n<s;)(i=t[n]).callback.call(i.ctx,o,r);return;case 3:for(;++n<s;)(i=t[n]).callback.call(i.ctx,o,r,a);return;default:for(;++n<s;)(i=t[n]).callback.apply(i.ctx,e);return}},v=(n.bind=n.on,n.unbind=n.off,i.extend(e,n),e.Model=function(t,e){var n=t||{},s=(e=e||{},this.cid=i.uniqueId(this.cidPrefix),this.attributes={},e.collection&&(this.collection=e.collection),e.parse&&(n=this.parse(n,e)||{}),i.result(this,"defaults"));n=i.defaults(i.extend({},s,n),s),this.set(n,e),this.changed={},this.initialize.apply(this,arguments)}),_=(i.extend(v.prototype,n,{changed:null,validationError:null,idAttribute:"id",cidPrefix:"c",initialize:function(){},toJSON:function(t){return i.clone(this.attributes)},sync:function(){return e.sync.apply(this,arguments)},get:function(t){return this.attributes[t]},escape:function(t){return i.escape(this.get(t))},has:function(t){return null!=this.get(t)},matches:function(t){return!!i.iteratee(t,this)(this.attributes)},set:function(t,e,n){if(null!=t){var s;if("object"==typeof t?(s=t,n=e):(s={})[t]=e,!this._validate(s,n=n||{}))return!1;var o,r=n.unset,a=(t=n.silent,[]),l=this._changing,h=(this._changing=!0,l||(this._previousAttributes=i.clone(this.attributes),this.changed={}),this.attributes),c=this.changed,u=this._previousAttributes;for(o in s)e=s[o],i.isEqual(h[o],e)||a.push(o),i.isEqual(u[o],e)?delete c[o]:c[o]=e,r?delete h[o]:h[o]=e;if(this.idAttribute in s&&(this.id=this.get(this.idAttribute)),!t){a.length&&(this._pending=n);for(var d=0;d<a.length;d++)this.trigger("change:"+a[d],this,h[a[d]],n)}if(!l){if(!t)for(;this._pending;)n=this._pending,this._pending=!1,this.trigger("change",this,n);this._pending=!1,this._changing=!1}}return this},unset:function(t,e){return this.set(t,void 0,i.extend({},e,{unset:!0}))},clear:function(t){var e,n={};for(e in this.attributes)n[e]=void 0;return this.set(n,i.extend({},t,{unset:!0}))},hasChanged:function(t){return null==t?!i.isEmpty(this.changed):i.has(this.changed,t)},changedAttributes:function(t){if(!t)return!!this.hasChanged()&&i.clone(this.changed);var e,n=this._changing?this._previousAttributes:this.attributes,s={};for(e in t){var o=t[e];i.isEqual(n[e],o)||(s[e]=o)}return!!i.size(s)&&s},previous:function(t){return null!=t&&this._previousAttributes?this._previousAttributes[t]:null},previousAttributes:function(){return i.clone(this._previousAttributes)},fetch:function(t){t=i.extend({parse:!0},t);var e=this,n=t.success;return t.success=function(i){var s=t.parse?e.parse(i,t):i;if(!e.set(s,t))return!1;n&&n.call(t.context,e,i,t),e.trigger("sync",e,i,t)},H(this,t),this.sync("read",this,t)},save:function(t,e,n){null==t||"object"==typeof t?(s=t,n=e):(s={})[t]=e;var s,o=(n=i.extend({validate:!0,parse:!0},n)).wait;if(s&&!o){if(!this.set(s,n))return!1}else if(!this._validate(s,n))return!1;var r=this,a=n.success,l=this.attributes;return n.success=function(t){r.attributes=l;var e=n.parse?r.parse(t,n):t;if((e=o?i.extend({},s,e):e)&&!r.set(e,n))return!1;a&&a.call(n.context,r,t,n),r.trigger("sync",r,t,n)},H(this,n),s&&o&&(this.attributes=i.extend({},l,s)),"patch"!=(t=this.isNew()?"create":n.patch?"patch":"update")||n.attrs||(n.attrs=s),e=this.sync(t,this,n),this.attributes=l,e},destroy:function(t){function e(){n.stopListening(),n.trigger("destroy",n,n.collection,t)}t=t?i.clone(t):{};var n=this,s=t.success,o=t.wait,r=!(t.success=function(i){o&&e(),s&&s.call(t.context,n,i,t),n.isNew()||n.trigger("sync",n,i,t)});return this.isNew()?i.defer(t.success):(H(this,t),r=this.sync("delete",this,t)),o||e(),r},url:function(){var t,e=i.result(this,"urlRoot")||i.result(this.collection,"url")||F();return this.isNew()?e:(t=this.get(this.idAttribute),e.replace(/[^\/]$/,"$&/")+encodeURIComponent(t))},parse:function(t,e){return t},clone:function(){return new this.constructor(this.attributes)},isNew:function(){return!this.has(this.idAttribute)},isValid:function(t){return this._validate({},i.extend({},t,{validate:!0}))},_validate:function(t,e){return!(e.validate&&this.validate&&(t=i.extend({},this.attributes,t),(t=this.validationError=this.validate(t,e)||null)&&(this.trigger("invalid",this,t,i.extend(e,{validationError:t})),1)))}}),s(v,{keys:1,values:1,pairs:1,invert:1,pick:0,omit:0,chain:1,isEmpty:1},"attributes"),e.Collection=function(t,e){(e=e||{}).model&&(this.model=e.model),void 0!==e.comparator&&(this.comparator=e.comparator),this._reset(),this.initialize.apply(this,arguments),t&&this.reset(t,i.extend({silent:!0},e))}),b={add:!0,remove:!0,merge:!0},y={add:!0,remove:!1},w=(i.extend(_.prototype,n,{model:v,initialize:function(){},toJSON:function(t){return this.map((function(e){return e.toJSON(t)}))},sync:function(){return e.sync.apply(this,arguments)},add:function(t,e){return this.set(t,i.extend({merge:!1},e,y))},remove:function(t,e){e=i.extend({},e);var n=!i.isArray(t);return t=n?[t]:t.slice(),t=this._removeModels(t,e),!e.silent&&t.length&&(e.changes={added:[],merged:[],removed:t},this.trigger("update",this,e)),n?t[0]:t},set:function(t,e){if(null!=t){(e=i.extend({},b,e)).parse&&!this._isModel(t)&&(t=this.parse(t,e)||[]);for(var n=!i.isArray(t),s=(t=n?[t]:t.slice(),e.at),r=((s=(s=null!=s?+s:s)>this.length?this.length:s)<0&&(s+=this.length+1),[]),a=[],l=[],h=[],c={},u=e.add,d=e.merge,p=e.remove,f=!1,g=this.comparator&&null==s&&!1!==e.sort,m=i.isString(this.comparator)?this.comparator:null,v=0;v<t.length;v++){var _,y=t[v],w=this.get(y);w?(d&&y!==w&&(_=this._isModel(y)?y.attributes:y,e.parse&&(_=w.parse(_,e)),w.set(_,e),l.push(w),g)&&!f&&(f=w.hasChanged(m)),c[w.cid]||(c[w.cid]=!0,r.push(w)),t[v]=w):u&&(y=t[v]=this._prepareModel(y,e))&&(a.push(y),this._addReference(y,e),c[y.cid]=!0,r.push(y))}if(p){for(v=0;v<this.length;v++)c[(y=this.models[v]).cid]||h.push(y);h.length&&this._removeModels(h,e)}var x=!1;if(r.length&&!g&&u&&p?(x=this.length!==r.length||i.some(this.models,(function(t,e){return t!==r[e]})),this.models.length=0,o(this.models,r,0),this.length=this.models.length):a.length&&(g&&(f=!0),o(this.models,a,null==s?this.length:s),this.length=this.models.length),f&&this.sort({silent:!0}),!e.silent){for(v=0;v<a.length;v++)null!=s&&(e.index=s+v),(y=a[v]).trigger("add",y,this,e);(f||x)&&this.trigger("sort",this,e),(a.length||h.length||l.length)&&(e.changes={added:a,removed:h,merged:l},this.trigger("update",this,e))}return n?t[0]:t}},reset:function(t,e){e=e?i.clone(e):{};for(var n=0;n<this.models.length;n++)this._removeReference(this.models[n],e);return e.previousModels=this.models,this._reset(),t=this.add(t,i.extend({silent:!0},e)),e.silent||this.trigger("reset",this,e),t},push:function(t,e){return this.add(t,i.extend({at:this.length},e))},pop:function(t){var e=this.at(this.length-1);return this.remove(e,t)},unshift:function(t,e){return this.add(t,i.extend({at:0},e))},shift:function(t){var e=this.at(0);return this.remove(e,t)},slice:function(){return a.apply(this.models,arguments)},get:function(t){if(null!=t)return this._byId[t]||this._byId[this.modelId(t.attributes||t)]||t.cid&&this._byId[t.cid]},has:function(t){return null!=this.get(t)},at:function(t){return t<0&&(t+=this.length),this.models[t]},where:function(t,e){return this[e?"find":"filter"](t)},findWhere:function(t){return this.where(t,!0)},sort:function(t){var e=this.comparator;if(!e)throw new Error("Cannot sort a set without a comparator");t=t||{};var n=e.length;return i.isFunction(e)&&(e=i.bind(e,this)),1===n||i.isString(e)?this.models=this.sortBy(e):this.models.sort(e),t.silent||this.trigger("sort",this,t),this},pluck:function(t){return this.map(t+"")},fetch:function(t){var e=(t=i.extend({parse:!0},t)).success,n=this;return t.success=function(i){var s=t.reset?"reset":"set";n[s](i,t),e&&e.call(t.context,n,i,t),n.trigger("sync",n,i,t)},H(this,t),this.sync("read",this,t)},create:function(t,e){var n=(e=e?i.clone(e):{}).wait;if(!(t=this._prepareModel(t,e)))return!1;n||this.add(t,e);var s=this,o=e.success;return e.success=function(t,e,i){n&&s.add(t,i),o&&o.call(i.context,t,e,i)},t.save(null,e),t},parse:function(t,e){return t},clone:function(){return new this.constructor(this.models,{model:this.model,comparator:this.comparator})},modelId:function(t){return t[this.model.prototype.idAttribute||"id"]},_reset:function(){this.length=0,this.models=[],this._byId={}},_prepareModel:function(t,e){return this._isModel(t)?(t.collection||(t.collection=this),t):(t=new(((e=e?i.clone(e):{}).collection=this).model)(t,e)).validationError?(this.trigger("invalid",this,t.validationError,e),!1):t},_removeModels:function(t,e){for(var i=[],n=0;n<t.length;n++){var s,o,r=this.get(t[n]);r&&(s=this.indexOf(r),this.models.splice(s,1),this.length--,delete this._byId[r.cid],null!=(o=this.modelId(r.attributes))&&delete this._byId[o],e.silent||(e.index=s,r.trigger("remove",r,this,e)),i.push(r),this._removeReference(r,e))}return i},_isModel:function(t){return t instanceof v},_addReference:function(t,e){this._byId[t.cid]=t;var i=this.modelId(t.attributes);null!=i&&(this._byId[i]=t),t.on("all",this._onModelEvent,this)},_removeReference:function(t,e){delete this._byId[t.cid];var i=this.modelId(t.attributes);null!=i&&delete this._byId[i],this===t.collection&&delete t.collection,t.off("all",this._onModelEvent,this)},_onModelEvent:function(t,e,i,n){if(e){if(("add"===t||"remove"===t)&&i!==this)return;var s,o;"destroy"===t&&this.remove(e,n),"change"===t&&(s=this.modelId(e.previousAttributes()))!==(o=this.modelId(e.attributes))&&(null!=s&&delete this._byId[s],null!=o)&&(this._byId[o]=e)}this.trigger.apply(this,arguments)}}),s(_,{forEach:3,each:3,map:3,collect:3,reduce:0,foldl:0,inject:0,reduceRight:0,foldr:0,find:3,detect:3,filter:3,select:3,reject:3,every:3,all:3,some:3,any:3,include:3,includes:3,contains:3,invoke:0,max:3,min:3,toArray:1,size:1,first:3,head:3,take:3,initial:3,rest:3,tail:3,drop:3,last:3,without:0,difference:0,indexOf:3,shuffle:1,lastIndexOf:3,isEmpty:1,chain:1,sample:3,partition:3,groupBy:3,countBy:3,sortBy:3,indexBy:3,findIndex:3,findLastIndex:3},"models"),e.View=function(t){this.cid=i.uniqueId("view"),i.extend(this,i.pick(t,C)),this._ensureElement(),this.initialize.apply(this,arguments)}),x=/^(\S+)\s*(.*)$/,C=["model","collection","el","id","attributes","className","tagName","events"],S=(i.extend(w.prototype,n,{tagName:"div",$:function(t){return this.$el.find(t)},initialize:function(){},render:function(){return this},remove:function(){return this._removeElement(),this.stopListening(),this},_removeElement:function(){this.$el.remove()},setElement:function(t){return this.undelegateEvents(),this._setElement(t),this.delegateEvents(),this},_setElement:function(t){this.$el=t instanceof e.$?t:e.$(t),this.el=this.$el[0]},delegateEvents:function(t){if(t=t||i.result(this,"events"))for(var e in this.undelegateEvents(),t){var n=t[e];(n=i.isFunction(n)?n:this[n])&&(e=e.match(x),this.delegate(e[1],e[2],i.bind(n,this)))}return this},delegate:function(t,e,i){return this.$el.on(t+".delegateEvents"+this.cid,e,i),this},undelegateEvents:function(){return this.$el&&this.$el.off(".delegateEvents"+this.cid),this},undelegate:function(t,e,i){return this.$el.off(t+".delegateEvents"+this.cid,e,i),this},_createElement:function(t){return document.createElement(t)},_ensureElement:function(){var t;this.el?this.setElement(i.result(this,"el")):(t=i.extend({},i.result(this,"attributes")),this.id&&(t.id=i.result(this,"id")),this.className&&(t.class=i.result(this,"className")),this.setElement(this._createElement(i.result(this,"tagName"))),this._setAttributes(t))},_setAttributes:function(t){this.$el.attr(t)}}),e.sync=function(t,n,s){var o,r=S[t],a=(i.defaults(s=s||{},{emulateHTTP:e.emulateHTTP,emulateJSON:e.emulateJSON}),{type:r,dataType:"json"}),l=(s.url||(a.url=i.result(n,"url")||F()),null!=s.data||!n||"create"!==t&&"update"!==t&&"patch"!==t||(a.contentType="application/json",a.data=JSON.stringify(s.attrs||n.toJSON(s))),s.emulateJSON&&(a.contentType="application/x-www-form-urlencoded",a.data=a.data?{model:a.data}:{}),!s.emulateHTTP||"PUT"!==r&&"DELETE"!==r&&"PATCH"!==r||(a.type="POST",s.emulateJSON&&(a.data._method=r),o=s.beforeSend,s.beforeSend=function(t){if(t.setRequestHeader("X-HTTP-Method-Override",r),o)return o.apply(this,arguments)}),"GET"===a.type||s.emulateJSON||(a.processData=!1),s.error);return s.error=function(t,e,i){s.textStatus=e,s.errorThrown=i,l&&l.call(s.context,t,e,i)},t=s.xhr=e.ajax(i.extend(a,s)),n.trigger("request",n,t,s),t},{create:"POST",update:"PUT",patch:"PATCH",delete:"DELETE",read:"GET"}),k=(e.ajax=function(){return e.$.ajax.apply(e.$,arguments)},e.Router=function(t){(t=t||{}).routes&&(this.routes=t.routes),this._bindRoutes(),this.initialize.apply(this,arguments)}),E=/\((.*?)\)/g,D=/(\(\?)?:\w+/g,T=/\*\w+/g,M=/[\-{}\[\]+?.,\\\^$|#\s]/g,I=(i.extend(k.prototype,n,{initialize:function(){},route:function(t,n,s){i.isRegExp(t)||(t=this._routeToRegExp(t)),i.isFunction(n)&&(s=n,n=""),s=s||this[n];var o=this;return e.history.route(t,(function(i){i=o._extractParameters(t,i),!1!==o.execute(s,i,n)&&(o.trigger.apply(o,["route:"+n].concat(i)),o.trigger("route",n,i),e.history.trigger("route",o,n,i))})),this},execute:function(t,e,i){t&&t.apply(this,e)},navigate:function(t,i){return e.history.navigate(t,i),this},_bindRoutes:function(){if(this.routes){this.routes=i.result(this,"routes");for(var t,e=i.keys(this.routes);null!=(t=e.pop());)this.route(t,this.routes[t])}},_routeToRegExp:function(t){return t=t.replace(M,"\\$&").replace(E,"(?:$1)?").replace(D,(function(t,e){return e?t:"([^/?]+)"})).replace(T,"([^?]*?)"),new RegExp("^"+t+"(?:\\?([\\s\\S]*))?$")},_extractParameters:function(t,e){var n=t.exec(e).slice(1);return i.map(n,(function(t,e){return e===n.length-1?t||null:t?decodeURIComponent(t):null}))}}),e.History=function(){this.handlers=[],this.checkUrl=i.bind(this.checkUrl,this),"undefined"!=typeof window&&(this.location=window.location,this.history=window.history)}),A=/^[#\/]|\s+$/g,P=/^\/+|\/+$/g,O=/#.*$/,F=(I.started=!1,i.extend(I.prototype,n,{interval:50,atRoot:function(){return this.location.pathname.replace(/[^\/]$/,"$&/")===this.root&&!this.getSearch()},matchRoot:function(){return this.decodeFragment(this.location.pathname).slice(0,this.root.length-1)+"/"===this.root},decodeFragment:function(t){return decodeURI(t.replace(/%25/g,"%2525"))},getSearch:function(){var t=this.location.href.replace(/#.*/,"").match(/\?.+/);return t?t[0]:""},getHash:function(t){return(t=(t||this).location.href.match(/#(.*)$/))?t[1]:""},getPath:function(){var t=this.decodeFragment(this.location.pathname+this.getSearch()).slice(this.root.length-1);return"/"===t.charAt(0)?t.slice(1):t},getFragment:function(t){return(t=null==t?this._usePushState||!this._wantsHashChange?this.getPath():this.getHash():t).replace(A,"")},start:function(t){if(I.started)throw new Error("Backbone.history has already been started");if(I.started=!0,this.options=i.extend({root:"/"},this.options,t),this.root=this.options.root,this._wantsHashChange=!1!==this.options.hashChange,this._hasHashChange="onhashchange"in window&&(void 0===document.documentMode||7<document.documentMode),this._useHashChange=this._wantsHashChange&&this._hasHashChange,this._wantsPushState=!!this.options.pushState,this._hasPushState=!(!this.history||!this.history.pushState),this._usePushState=this._wantsPushState&&this._hasPushState,this.fragment=this.getFragment(),this.root=("/"+this.root+"/").replace(P,"/"),this._wantsHashChange&&this._wantsPushState){if(!this._hasPushState&&!this.atRoot())return t=this.root.slice(0,-1)||"/",this.location.replace(t+"#"+this.getPath()),!0;this._hasPushState&&this.atRoot()&&this.navigate(this.getHash(),{replace:!0})}if(this._hasHashChange||!this._wantsHashChange||this._usePushState||(this.iframe=document.createElement("iframe"),this.iframe.src="javascript:0",this.iframe.style.display="none",this.iframe.tabIndex=-1,(t=(t=document.body).insertBefore(this.iframe,t.firstChild).contentWindow).document.open(),t.document.close(),t.location.hash="#"+this.fragment),t=window.addEventListener||function(t,e){return attachEvent("on"+t,e)},this._usePushState?t("popstate",this.checkUrl,!1):this._useHashChange&&!this.iframe?t("hashchange",this.checkUrl,!1):this._wantsHashChange&&(this._checkUrlInterval=setInterval(this.checkUrl,this.interval)),!this.options.silent)return this.loadUrl()},stop:function(){var t=window.removeEventListener||function(t,e){return detachEvent("on"+t,e)};this._usePushState?t("popstate",this.checkUrl,!1):this._useHashChange&&!this.iframe&&t("hashchange",this.checkUrl,!1),this.iframe&&(document.body.removeChild(this.iframe),this.iframe=null),this._checkUrlInterval&&clearInterval(this._checkUrlInterval),I.started=!1},route:function(t,e){this.handlers.unshift({route:t,callback:e})},checkUrl:function(t){var e=this.getFragment();if((e=e===this.fragment&&this.iframe?this.getHash(this.iframe.contentWindow):e)===this.fragment)return!1;this.iframe&&this.navigate(e),this.loadUrl()},loadUrl:function(t){return!!this.matchRoot()&&(t=this.fragment=this.getFragment(t),i.some(this.handlers,(function(e){if(e.route.test(t))return e.callback(t),!0})))},navigate:function(t,e){if(!I.started)return!1;e&&!0!==e||(e={trigger:!!e}),t=this.getFragment(t||"");var i=this.root;if(i=(i=""!==t&&"?"!==t.charAt(0)?i:i.slice(0,-1)||"/")+t,t=this.decodeFragment(t.replace(O,"")),this.fragment!==t){if(this.fragment=t,this._usePushState)this.history[e.replace?"replaceState":"pushState"]({},document.title,i);else{if(!this._wantsHashChange)return this.location.assign(i);this._updateHash(this.location,t,e.replace),this.iframe&&t!==this.getHash(this.iframe.contentWindow)&&(i=this.iframe.contentWindow,e.replace||(i.document.open(),i.document.close()),this._updateHash(i.location,t,e.replace))}return e.trigger?this.loadUrl(t):void 0}},_updateHash:function(t,e,i){i?(i=t.href.replace(/(javascript:|#).*$/,""),t.replace(i+"#"+e)):t.hash="#"+e}}),e.history=new I,v.extend=_.extend=k.extend=w.extend=I.extend=function(t,e){var n=this,s=t&&i.has(t,"constructor")?t.constructor:function(){return n.apply(this,arguments)};return i.extend(s,n,e),s.prototype=i.create(n.prototype,t),(s.prototype.constructor=s).__super__=n.prototype,s},function(){throw new Error('A "url" property or function must be specified')}),H=function(t,e){var i=e.error;e.error=function(n){i&&i.call(e.context,t,n,e),t.trigger("error",t,n,e)}};return e})),function(t){"use strict";var e,i=Namespace.register("PDScripts.BootStrapData"),n=Namespace.register("PDConstants"),s=Namespace.using("PDScripts.Convert");_.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/\{\{(.+?)\}\}/g};try{e=localStorage}catch(n){}if(e)for(var o=(new Date).getTime(),r=e.length-1;0<=r;r--)try{var a=e.key(r),l=JSON.parse(e.getItem(a));(l.expiration&&l.expiration<o||l.buildNumber!==i.buildNumber)&&e.removeItem(a)}catch(n){}n={_cache:e||{},cacheExpiration:7*n.MillisecondsPerDay,parse:function(t,n){if(n&&(n=_.result(this,"url"),!_.isNull(t)&&!_.has(this._cache,n))){var s=JSON.stringify({data:t,buildNumber:i.buildNumber,expiration:(new Date).getTime()+this.cacheExpiration});try{this._cache[n]=s}catch(t){e.clear(),this._cache[n]=s}}return t},sync:function(e,i,n){var s,o,r;switch(e){case"read":try{var a=this._cache[_.result(this,"url")],l=!!n&&!1===n.cache,h=_.isFunction(this.url)?this.url():this.url,c=_.isUndefined(a)||_.isNull(a)||l?t.sync.call(this,e,i,n):(s=_.extend(n,{url:h}),o=a,(r=new Promise((function(t){_.defer(t)}))).then((function(){s.success(JSON.parse(o).data),$.event.trigger("ajaxComplete",[o,s])})),r)}catch(s){c=t.sync.call(this,e,i,n)}break;case"create":case"update":case"delete":c=t.sync.call(this,e,i,n)}return c}},t.ajax=function(){var e=t.$.ajax.apply(t.$,arguments);return s.ajaxToPromise(e)},t.CachedModel=t.Model.extend(n),t.CachedCollection=t.Collection.extend(n),_.extend(),n={modelMap:{},initialize:function(){var t=this,e={};_.each(this.modelMap,(function(i,n){t.get(n)||(e[n]=[])})),this.set.apply(this,[e,{silent:!0}])},set:function(e,i,n){var s,o,r,a,l,h;if(null!=e){if("object"==typeof e?(o=e,n=i):(o={})[e]=i,!this._validate(o,n=n||{}))return!1;for(s in n.unset,e=n.silent,r=[],a=this._changing,this._changing=!0,a||(this._previousAttributes=_.clone(this.attributes),this.changed={}),h=this.attributes,l=this._previousAttributes,this.idAttribute in o&&(this.id=o[this.idAttribute]),o)i=o[s],_.isEqual(h[s],i)||r.push(s),_.isEqual(l[s],i)?delete this.changed[s]:this.changed[s]=i,n.unset?delete h[s]:h[s]instanceof t.Model?h[s].set(i,n):h[s]instanceof t.Collection?h[s].reset(i,n):this.modelMap[s]?(n.parent=this,h[s]=new this.modelMap[s](i,n),h[s].on("all",(function(){var t=Array.prototype.slice.apply(arguments);t[0]="model:"+t[0],t.push(this),this.trigger.apply(this,t)}),this),this.modelMap[s]instanceof t.Collection&&(h[s].parent=this)):h[s]=i;if(!e){r.length&&(this._pending=!0);for(var c=0,u=r.length;c<u;c++)this.trigger("change:"+r[c],this,h[r[c]],n)}if(!a){if(!e)for(;this._pending;)this._pending=!1,this.trigger("change",this,n);this._pending=!1,this._changing=!1}}return this},toJSON:function(e){var i={};return _.each(this.attributes,(function(n,s){i[s]=n instanceof t.Collection||n instanceof t.Model?n.toJSON(e):n})),i}};var h=(t.ComplexModel=t.Model.extend(n),t.CachedComplexModel=t.CachedModel.extend(n),t.View),c=(n=h.extend({constructor:function(t){t&&t.parent&&(this.parent=t.parent),t=Array.prototype.slice.call(arguments),h.apply(this,t)}}),t.View=n,t.Model.prototype.save);function u(t,e){this._superCallObjects||(this._superCallObjects={});var i=function(t,e){for(var i=e;i&&i[t]===e[t];)i=i.constructor.__super__;return i}(t,this._superCallObjects[t]||this);if(i)return i=(this._superCallObjects[t]=i)[t].apply(this,e||[]),delete this._superCallObjects[t],i}_.extend(t.Model.prototype,{_validate:function(t,e){return!this.validate||!(t=this.validationError=this.validate(t,e)||null)||(this.trigger("invalid",this,t,_.extend(e,{validationError:t})),!1)},save:function(t,e,i){var n;return _.isObject(t)||null==t?(n=t,i=e):(n={})[t]=e,i=_.extend({},i,{forceUpdate:!1}),c.call(this,n,i)}}),t.CollectionOf=function(e,i){if(e.prototype instanceof t.Model)return t.Collection.extend(_.extend({model:e,initialize:function(t,e){e&&e.parent&&(this.parent=e.parent)}},i));throw"Requested Model is not a Backbone Model"},_.each(["Model","Collection","View","Router"],(function(e){t[e].prototype._super=u})),_.extend(t.History.prototype,{getHash:function(t){return(t=(t||this).location.href.match(/#(.*)$/))?this.decodeFragment(t[1]):""}})}(Backbone),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("underscore"),require("backbone")):"function"==typeof define&&define.amd?define(["underscore","backbone"],e):(t.Backbone=t.Backbone||{},t.Backbone.Radio=e(t._,t.Backbone))}(this,(function(t,e){"use strict";t="default"in t?t.default:t,e="default"in e?e.default:e;var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol?"symbol":typeof t},n=e.Radio,s=e.Radio={VERSION:"2.0.0",noConflict:function(){return e.Radio=n,this},DEBUG:!1,_debugText:function(t,e,i){return t+(i?" on the "+i+" channel":"")+': "'+e+'"'},debugLog:function(t,e,i){s.DEBUG&&console&&console.warn&&console.warn(s._debugText(t,e,i))}},o=/\s+/;s._eventsApi=function(e,n,s,r){if(s){var a={};if("object"===(void 0===s?"undefined":i(s))){for(var l in s){var h=e[n].apply(e,[l,s[l]].concat(r));o.test(l)?t.extend(a,h):a[l]=h}return a}if(o.test(s)){for(var c=s.split(o),u=0,d=c.length;u<d;u++)a[c[u]]=e[n].apply(e,[c[u]].concat(r));return a}}return!1},s._callHandler=function(t,e,i){var n=i[0],s=i[1],o=i[2];switch(i.length){case 0:return t.call(e);case 1:return t.call(e,n);case 2:return t.call(e,n,s);case 3:return t.call(e,n,s,o);default:return t.apply(e,i)}};var r,a,l={};function h(e){return l[e]||(l[e]=t.bind(s.log,s,e))}function c(e){return t.isFunction(e)?e:function(){return e}}t.extend(s,{log:function(e,i){var n;"undefined"!=typeof console&&(n=t.toArray(arguments).slice(2),console.log("["+e+'] "'+i+'"',n))},tuneIn:function(t){var e=s.channel(t);return e._tunedIn=!0,e.on("all",h(t)),this},tuneOut:function(t){var e=s.channel(t);return e._tunedIn=!1,e.off("all",h(t)),delete l[t],this}}),s.Requests={request:function(e){var i,n,o=t.toArray(arguments).slice(1),r=s._eventsApi(this,"request",e,o);return r||(r=this.channelName,i=this._requests,r&&this._tunedIn&&s.log.apply(this,[r,e].concat(o)),i&&(i[e]||i.default)?(n=i[e]||i.default,o=i[e]?o:arguments,s._callHandler(n.callback,n.context,o)):void s.debugLog("An unhandled request was fired",e,r))},reply:function(t,e,i){return s._eventsApi(this,"reply",t,[e,i])||(this._requests||(this._requests={}),this._requests[t]&&s.debugLog("A request was overwritten",t,this.channelName),this._requests[t]={callback:c(e),context:i||this}),this},replyOnce:function(e,i,n){var o,r;return s._eventsApi(this,"replyOnce",e,[i,n])?this:(o=this,r=t.once((function(){return o.stopReplying(e),c(i).apply(this,arguments)})),this.reply(e,r,n))},stopReplying:function(e,i,n){return s._eventsApi(this,"stopReplying",e)||(e||i||n?function(e,i,n,s){e=e||{};for(var o,r,a,l,h,c=i?[i]:t.keys(e),u=!1,d=0,p=c.length;d<p;d++)e[i=c[d]]&&(a=n,l=s,h=(o=e)[r=i],a&&a!==h.callback&&a!==h.callback._callback||l&&l!==h.context||(delete o[r],0)||(u=!0));return u}(this._requests,e,i,n)||s.debugLog("Attempted to remove the unregistered request",e,this.channelName):delete this._requests),this}},s._channels={},s.channel=function(t){if(t)return s._channels[t]||(s._channels[t]=new s.Channel(t));throw new Error("You must provide a name for the channel.")},s.Channel=function(t){this.channelName=t},t.extend(s.Channel.prototype,e.Events,s.Requests,{reset:function(){return this.off(),this.stopListening(),this.stopReplying(),this}});var u=[e.Events,s.Requests];return t.each(u,(function(e){t.each(e,(function(e,i){s[i]=function(e){return a=t.toArray(arguments).slice(1),(r=this.channel(e))[i].apply(r,a)}}))})),s.reset=function(e){e=e?[this._channels[e]]:this._channels,t.each(e,(function(t){t.reset()}))},s})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("backbone"),require("underscore"),require("backbone.radio")):"function"==typeof define&&define.amd?define(["backbone","underscore","backbone.radio"],e):t.Marionette=t.Mn=e(t.Backbone,t._,t.Backbone.Radio)}(this,(function(t,e,i){"use strict";function n(t){return function(e){for(var i=arguments.length,n=Array(1<i?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];return t.apply(e,n)}}function s(t){return document.documentElement.contains(t&&t.parentNode)}function o(t,i){var n=this;t&&e.each(i,(function(e){var i=t[e];void 0!==i&&(n[e]=i)}))}function r(t){if(t)return(this.options&&void 0!==this.options[t]?this.options:this)[t]}t="default"in t?t.default:t,e="default"in e?e.default:e,i="default"in i?i.default:i;var a=t.Model.extend,l=function t(i,n){e.isObject(i)&&(i=i.prev+" is going to be removed in the future. Please use "+i.next+" instead."+(i.url?" See: "+i.url:"")),!wt.DEV_MODE||void 0!==n&&n||t._cache[i]||(t._warn("Deprecation warning: "+i),t._cache[i]=!0)},h=(l._console="undefined"!=typeof console?console:{},l._warn=function(){return(l._console.warn||l._console.log||e.noop).apply(l._console,arguments)},l._cache={},function(t){var i=this;return e.reduce(t,(function(t,n,s){return(n=e.isFunction(n)?n:i[n])&&(t[s]=n),t}),{})}),c=/(^|:)(\w)/gi;function u(t,e,i){return i.toUpperCase()}var d=e.memoize((function(t){return"on"+t.replace(c,u)}));function p(t){for(var i=arguments.length,n=Array(1<i?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];t=d(t),t=r.call(this,t);var o=void 0;return e.isFunction(t)&&(o=t.apply(this,n)),this.trigger.apply(this,arguments),o}function f(t){for(var i=arguments.length,n=Array(1<i?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];return(e.isFunction(t.triggerMethod)?t.triggerMethod:p).apply(t,n)}function g(t,i,n){t._getImmediateChildren&&e.each(t._getImmediateChildren(),(function(t){n(t)&&f(t,i,t)}))}function m(t){return!t._isAttached}function v(t){return!!m(t)&&(t._isAttached=!0)}function _(t){return t._isAttached}function b(t){return!!_(t)&&!(t._isAttached=!1)}function y(t){t._isAttached&&t._isRendered&&f(t,"dom:refresh",t)}function w(t){t._isAttached&&t._isRendered&&f(t,"dom:remove",t)}function x(){g(this,"before:attach",m)}function C(){g(this,"attach",v),y(this)}function S(){g(this,"before:detach",_),w(this)}function k(){g(this,"detach",b)}function E(){w(this)}function D(){y(this)}function T(t){t._areViewEventsMonitored||(t._areViewEventsMonitored=!0,t.on({"before:attach":x,attach:C,"before:detach":S,detach:k,"before:render":E,render:D}))}var M=["description","fileName","lineNumber","name","message","number"],I=a.call(Error,{urlRoot:"http://marionettejs.com/docs/v3.3.1/",constructor:function(t,i){e.isObject(t)?t=(i=t).message:i=i||{},t=Error.call(this,t),e.extend(this,e.pick(t,M),e.pick(i,M)),this.captureStackTrace(),i.url&&(this.url=this.urlRoot+i.url)},captureStackTrace:function(){Error.captureStackTrace&&Error.captureStackTrace(this,I)},toString:function(){return this.name+": "+this.message+(this.url?" See: "+this.url:"")}});function A(t,i,n,s){if(i&&n){if(!e.isObject(n))throw new I({message:"Bindings must be an object.",url:"marionette.functions.html#marionettebindevents"});e.each(n,(function(n,o){var r,a,l,h,c;e.isString(n)?(r=t,a=i,l=o,c=s,h=(h=n).split(/\s+/),e.each(h,(function(t){var e=r[t];if(!e)throw new I('Method "'+t+'" was configured as an event handler, but does not exist.');r[c](a,l,e)}))):t[s](i,o,n)}))}}function P(t,e){return A(this,t,e,"listenTo"),this}function O(t,e){return A(this,t,e,"stopListening"),this}function F(t,i,n,s){if(i&&n){if(!e.isObject(n))throw new I({message:"Bindings must be an object.",url:"marionette.functions.html#marionettebindrequests"});n=h.call(t,n),i[s](n,t)}}function H(t,e){return F(this,t,e,"reply"),this}function V(t,e){return F(this,t,e,"stopReplying"),this}function N(t){this._setOptions(t),this.mergeOptions(t,L),this.cid=e.uniqueId(this.cidPrefix),this._initRadio(),this.initialize.apply(this,arguments)}function W(t){this.templateId=t}I.extend=a;var R={normalizeMethods:h,_setOptions:function(){for(var t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];this.options=e.extend.apply(e,[{},e.result(this,"options")].concat(i))},mergeOptions:o,getOption:r,bindEvents:P,unbindEvents:O},B={_initRadio:function(){if(t=e.result(this,"channelName")){if(!i)throw new I({name:"BackboneRadioMissing",message:'The dependency "backbone.radio" is missing.'});var t=this._channel=i.channel(t),n=e.result(this,"radioEvents");this.bindEvents(t,n),n=e.result(this,"radioRequests"),this.bindRequests(t,n),this.on("destroy",this._destroyRadio)}},_destroyRadio:function(){this._channel.stopReplying(null,null,this)},getChannel:function(){return this._channel},bindEvents:P,unbindEvents:O,bindRequests:H,unbindRequests:V},L=["channelName","radioEvents","radioRequests"],z=(N.extend=a,e.extend(N.prototype,t.Events,R,B,{cidPrefix:"mno",_isDestroyed:!1,isDestroyed:function(){return this._isDestroyed},initialize:function(){},destroy:function(){if(!this._isDestroyed){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];this.triggerMethod.apply(this,["before:destroy",this].concat(e)),this._isDestroyed=!0,this.triggerMethod.apply(this,["destroy",this].concat(e)),this.stopListening()}return this},triggerMethod:p}),B={createBuffer:function(){return document.createDocumentFragment()},appendChildren:function(e,i){t.$(e).append(i)},beforeEl:function(e,i){t.$(e).before(i)},replaceEl:function(t,e){var i;t!==e&&(i=e.parentNode)&&i.replaceChild(t,e)},detachContents:function(e){t.$(e).contents().detach()},setInnerContent:function(e,i){t.$(e).html(i)},detachEl:function(e){t.$(e).detach()},removeEl:function(e){t.$(e).remove()},findEls:function(e,i){return t.$(e,i)}},e.extend(W,{templateCaches:{},get:function(t,e){var i=this.templateCaches[t];return i||(i=new W(t),this.templateCaches[t]=i),i.load(e)},clear:function(){for(var t=void 0,e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];var s=i.length;if(0<s)for(t=0;t<s;t++)delete this.templateCaches[i[t]];else this.templateCaches={}}}),e.extend(W.prototype,B,{load:function(t){var e;return this.compiledTemplate||(e=this.loadTemplate(this.templateId,t),this.compiledTemplate=this.compileTemplate(e,t)),this.compiledTemplate},loadTemplate:function(t,e){var i=this.findEls(t);if(i.length)return i.html();throw new I({name:"NoTemplateError",message:'Could not find template: "'+t+'"'})},compileTemplate:function(t,i){return e.template(t,i)}}),e.invokeMap||e.invoke);function j(t,i){return e.chain(i).map((function(i,n){n=new(s=(s=i).behaviorClass||(e.isFunction(s)?s:(e.isFunction(wt.Behaviors.behaviorsLookup)?wt.Behaviors.behaviorsLookup(s,n):wt.Behaviors.behaviorsLookup)[n]))(i===s?{}:i,t);var s=j(t,e.result(n,"behaviors"));return[n].concat(s)})).flatten().value()}var G={_delegateEntityEvents:function(t,i){this._undelegateEntityEvents(t,i);var n=e.result(this,"modelEvents");P.call(this,t,n),t=e.result(this,"collectionEvents"),P.call(this,i,t)},_undelegateEntityEvents:function(t,i){var n=e.result(this,"modelEvents");O.call(this,t,n),t=e.result(this,"collectionEvents"),O.call(this,i,t)}},q=/^(\S+)\s*(.*)$/;function U(t){var i;return i=(t=t.match(q))[1],t=t[2],[i+e.uniqueId(".evt"),t].join(" ")}var $={childViewEventPrefix:!0,triggersStopPropagation:!0,triggersPreventDefault:!0};function Y(t,e){return t.replace(/@ui\.[a-zA-Z-_$0-9]*/g,(function(t){return e[t.slice(4)]}))}function K(t,i,n){return e.each(t,(function(s,o){e.isString(s)?t[o]=Y(s,i):e.isObject(s)&&e.isArray(n)&&(e.extend(s,K(e.pick(s,n),i)),e.each(n,(function(t){var n=s[t];e.isString(n)&&(s[t]=Y(n,i))})))})),t}var X={_getViewTriggers:function(t,i){return e.reduce(i,(function(i,n,s){var o,r,a,l;return i[s=U(s)]=(o=t,s=n,r=(s=e.isString(s)?{event:s}:s).event,a=!!s.preventDefault,$.triggersPreventDefault&&(a=!1!==s.preventDefault),l=!!s.stopPropagation,$.triggersStopPropagation&&(l=!1!==s.stopPropagation),function(t){a&&t.preventDefault(),l&&t.stopPropagation(),o.triggerMethod(r,o,t)}),i}),{})}},J={normalizeUIKeys:function(t){var i,n=this._getUIBindings();return i=n,e.reduce(t,(function(t,e,n){return t[Y(n,i)]=e,t}),{})},normalizeUIString:function(t){return Y(t,this._getUIBindings())},normalizeUIValues:function(t,e){return K(t,this._getUIBindings(),e)},_getUIBindings:function(){var t=e.result(this,"_uiBindings"),i=e.result(this,"ui");return t||i},_bindUIElements:function(){var t,i=this;this.ui&&(this._uiBindings||(this._uiBindings=this.ui),t=e.result(this,"_uiBindings"),this._ui={},e.each(t,(function(t,e){i._ui[e]=i.$(t)})),this.ui=this._ui)},_unbindUIElements:function(){var t=this;this.ui&&this._uiBindings&&(e.each(this.ui,(function(e,i){delete t.ui[i]})),this.ui=this._uiBindings,delete this._uiBindings,delete this._ui)},_getUI:function(t){return this._ui[t]}},Q={supportsRenderLifecycle:!0,supportsDestroyLifecycle:!0,_isDestroyed:!1,isDestroyed:function(){return!!this._isDestroyed},_isRendered:!1,isRendered:function(){return!!this._isRendered},_isAttached:!1,isAttached:function(){return!!this._isAttached},delegateEvents:function(i){this._proxyBehaviorViewProperties(),this._buildEventProxies();var n=this._getEvents(i);return void 0===i&&(this.events=n),i=e.extend({},this._getBehaviorEvents(),n,this._getBehaviorTriggers(),this.getTriggers()),t.View.prototype.delegateEvents.call(this,i),this},_getEvents:function(t){return t=t||this.events,e.isFunction(t)?this.normalizeUIKeys(t.call(this)):this.normalizeUIKeys(t)},getTriggers:function(){var t;if(this.triggers)return t=this.normalizeUIKeys(e.result(this,"triggers")),this._getViewTriggers(this,t)},delegateEntityEvents:function(){return this._delegateEntityEvents(this.model,this.collection),this._delegateBehaviorEntityEvents(),this},undelegateEntityEvents:function(){return this._undelegateEntityEvents(this.model,this.collection),this._undelegateBehaviorEntityEvents(),this},destroy:function(){if(!this._isDestroyed){for(var t=!!this._isAttached,e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];this.triggerMethod.apply(this,["before:destroy",this].concat(i)),t&&this.triggerMethod("before:detach",this),this.unbindUIElements(),this.removeEl(this.el),t&&(this._isAttached=!1,this.triggerMethod("detach",this)),this._removeChildren(),this._isDestroyed=!0,this._isRendered=!1,this._destroyBehaviors.apply(this,i),this.triggerMethod.apply(this,["destroy",this].concat(i)),this.stopListening()}return this},bindUIElements:function(){return this._bindUIElements(),this._bindBehaviorUIElements(),this},unbindUIElements:function(){return this._unbindUIElements(),this._unbindBehaviorUIElements(),this},getUI:function(t){return this._getUI(t)},childViewEventPrefix:function(){return!!$.childViewEventPrefix&&"childview"},triggerMethod:function(){var t=p.apply(this,arguments);return this._triggerEventOnBehaviors.apply(this,arguments),t},_buildEventProxies:function(){this._childViewEvents=e.result(this,"childViewEvents"),this._childViewTriggers=e.result(this,"childViewTriggers")},_proxyChildViewEvents:function(t){this.listenTo(t,"all",this._childViewEventHandler)},_childViewEventHandler:function(t){for(var i=this.normalizeMethods(this._childViewEvents),n=arguments.length,s=Array(1<n?n-1:0),o=1;o<n;o++)s[o-1]=arguments[o];void 0!==i&&e.isFunction(i[t])&&i[t].apply(this,s),(i=this._childViewTriggers)&&e.isString(i[t])&&this.triggerMethod.apply(this,[i[t]].concat(s)),!1!==(i=e.result(this,"childViewEventPrefix"))&&this.triggerMethod.apply(this,[i+":"+t].concat(s))}};function Z(t){t.supportsDestroyLifecycle||f(t,"before:destroy",t);var e=!!t._isAttached;e&&f(t,"before:detach",t),t.remove(),e&&(t._isAttached=!1,f(t,"detach",t)),t._isDestroyed=!0,t.supportsDestroyLifecycle||f(t,"destroy",t)}function tt(t,i){if(t instanceof it)return t;if(i=e.extend({},i),e.isString(t))return e.extend(i,{el:t}),nt(i);if(e.isFunction(t))return e.extend(i,{regionClass:t}),nt(i);if(e.isObject(t))return t.selector&&l("The selector option on a Region definition object is deprecated. Use el to pass a selector string"),e.extend(i,{el:t.selector},t),nt(i);throw new I({message:"Improper region configuration type.",url:"marionette.region.html#region-configuration-types"})}e.extend(Q,B,{_initBehaviors:function(){this._behaviors=this._getBehaviors()},_getBehaviors:function(){var t=e.result(this,"behaviors");return e.isObject(t)?j(this,t):{}},_getBehaviorTriggers:function(){var t=z(this._behaviors,"getTriggers");return e.reduce(t,(function(t,i){return e.extend(t,i)}),{})},_getBehaviorEvents:function(){var t=z(this._behaviors,"getEvents");return e.reduce(t,(function(t,i){return e.extend(t,i)}),{})},_proxyBehaviorViewProperties:function(){z(this._behaviors,"proxyViewProperties")},_delegateBehaviorEntityEvents:function(){z(this._behaviors,"delegateEntityEvents")},_undelegateBehaviorEntityEvents:function(){z(this._behaviors,"undelegateEntityEvents")},_destroyBehaviors:function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];z.apply(void 0,[this._behaviors,"destroy"].concat(e))},_removeBehavior:function(t){this._isDestroyed||(this._behaviors=e.without(this._behaviors,t))},_bindBehaviorUIElements:function(){z(this._behaviors,"bindUIElements")},_unbindBehaviorUIElements:function(){z(this._behaviors,"unbindUIElements")},_triggerEventOnBehaviors:function(){for(var t=this._behaviors,e=0,i=t&&t.length;e<i;e++)p.apply(t[e],arguments)}},R,G,X,J);var et=["allowMissingEl","parentEl","replaceElement"],it=N.extend({cidPrefix:"mnr",replaceElement:!1,_isReplaced:!1,_isSwappingView:!1,constructor:function(e){if(this._setOptions(e),this.mergeOptions(e,et),this._initEl=this.el=this.getOption("el"),this.el=this.el instanceof t.$?this.el[0]:this.el,!this.el)throw new I({name:"NoElError",message:'An "el" must be specified for a region.'});this.$el=this.getEl(this.el),N.call(this,e)},show:function(t,e){if(this._ensureElement(e))return(t=this._getView(t,e))!==this.currentView&&(this._isSwappingView=!!this.currentView,this.triggerMethod("before:show",this,t,e),t._isAttached||this.empty(e),this._setupChildView(t),this._renderView(t),this._attachView(t,e),this.currentView=t,this.triggerMethod("show",this,t,e),this._isSwappingView=!1),this},_setupChildView:function(t){T(t),this._proxyChildViewEvents(t),t.on("destroy",this._empty,this)},_proxyChildViewEvents:function(t){var e=this._parentView;e&&e._proxyChildViewEvents(t)},_renderView:function(t){t._isRendered||(t.supportsRenderLifecycle||f(t,"before:render",t),t.render(),t.supportsRenderLifecycle)||(t._isRendered=!0,f(t,"render",t))},_attachView:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=!t._isAttached&&s(this.el);i=void 0===i.replaceElement?!!e.result(this,"replaceElement"):!!i.replaceElement,n&&f(t,"before:attach",t),i?this._replaceEl(t):this.attachHtml(t),n&&(t._isAttached=!0,f(t,"attach",t))},_ensureElement:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};if(e.isObject(this.el)||(this.$el=this.getEl(this.el),this.el=this.$el[0]),this.$el&&0!==this.$el.length)return!0;if(void 0===t.allowMissingEl?e.result(this,"allowMissingEl"):t.allowMissingEl)return!1;throw new I('An "el" must exist in DOM for this region '+this.cid)},_getView:function(e){if(!e)throw new I({name:"ViewNotValid",message:"The view passed is undefined and therefore invalid. You must pass a view instance to show."});if(e._isDestroyed)throw new I({name:"ViewDestroyedError",message:'View (cid: "'+e.cid+'") has already been destroyed and cannot be used.'});return e instanceof t.View?e:(e=this._getViewOptions(e),new ht(e))},_getViewOptions:function(t){return e.isFunction(t)?{template:t}:e.isObject(t)?t:{template:function(){return t}}},getEl:function(t){return this.findEls(t,e.result(this,"parentEl"))},_replaceEl:function(t){this._restoreEl(),t.on("before:destroy",this._restoreEl,this),this.replaceEl(t.el,this.el),this._isReplaced=!0},_restoreEl:function(){var t;this._isReplaced&&(t=this.currentView)&&(this._detachView(t),this._isReplaced=!1)},isReplaced:function(){return!!this._isReplaced},isSwappingView:function(){return!!this._isSwappingView},attachHtml:function(t){this.appendChildren(this.el,t.el)},empty:function(){var t,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{allowMissingEl:!0},i=this.currentView;return i?((t=!e.preventDestroy)||l("The preventDestroy option is deprecated. Use Region#detachView"),this._empty(i,t)):this._ensureElement(e)&&this.detachHtml(),this},_empty:function(t,e){t.off("destroy",this._empty,this),this.triggerMethod("before:empty",this,t),this._restoreEl(),delete this.currentView,t._isDestroyed||(e?this.removeView(t):this._detachView(t),this._stopChildViewEvents(t)),this.triggerMethod("empty",this,t)},_stopChildViewEvents:function(t){this._parentView&&this._parentView.stopListening(t)},destroyView:function(t){return t._isDestroyed||(t.destroy?t.destroy():Z(t)),t},removeView:function(t){this.destroyView(t)},detachView:function(){var t=this.currentView;if(t)return this._empty(t),t},_detachView:function(t){var e=!!t._isAttached,i=this._isReplaced;e&&f(t,"before:detach",t),i?this.replaceEl(this.el,t.el):this.detachHtml(),e&&(t._isAttached=!1,f(t,"detach",t))},detachHtml:function(){this.detachContents(this.el)},hasView:function(){return!!this.currentView},reset:function(t){return this.empty(t),this.$el&&(this.el=this._initEl),delete this.$el,this},destroy:function(t){return this._isDestroyed?this:(this.reset(t),this._name&&this._parentView._removeReferences(this._name),delete this._parentView,delete this._name,N.prototype.destroy.apply(this,arguments))}});function nt(t){return new t.regionClass(e.omit(t,"regionClass"))}function st(t,i){e.each(ct,(function(n){t[n]=function(){var t=[e.values(e.result(this,i))].concat(e.toArray(arguments));return e[n].apply(e,t)}}))}function ot(t){this._views={},this._indexByModel={},this._indexByCustom={},this._updateLength(),e.each(t,e.bind(this.add,this))}function rt(){this._init()}e.extend(it.prototype,B),B={regionClass:it,_initRegions:function(){this.regions=this.regions||{},this._regions={},this.addRegions(e.result(this,"regions"))},_reInitRegions:function(){z(this._regions,"reset")},addRegion:function(t,e){var i={};return i[t]=e,this.addRegions(i)[t]},addRegions:function(t){if(!e.isEmpty(t))return t=this.normalizeUIValues(t,["selector","el"]),this.regions=e.extend({},this.regions,t),this._addRegions(t)},_addRegions:function(t){var i=this,n={regionClass:this.regionClass,parentEl:e.partial(e.result,this,"el")};return e.reduce(t,(function(t,e,s){return t[s]=tt(e,n),i._addRegion(t[s],s),t}),{})},_addRegion:function(t,e){this.triggerMethod("before:add:region",this,e,t),t._parentView=this,t._name=e,this._regions[e]=t,this.triggerMethod("add:region",this,e,t)},removeRegion:function(t){var e=this._regions[t];return this._removeRegion(e,t),e},removeRegions:function(){var t=this._getRegions();return e.each(this._regions,e.bind(this._removeRegion,this)),t},_removeRegion:function(t,e){this.triggerMethod("before:remove:region",this,e,t),t.destroy(),this.triggerMethod("remove:region",this,e,t)},_removeReferences:function(t){delete this.regions[t],delete this._regions[t]},emptyRegions:function(){var t=this.getRegions();return z(t,"empty"),t},hasRegion:function(t){return!!this.getRegion(t)},getRegion:function(t){return this._isRendered||this.render(),this._regions[t]},_getRegions:function(){return e.clone(this._regions)},getRegions:function(){return this._isRendered||this.render(),this._getRegions()},showChildView:function(t,e){t=this.getRegion(t);for(var i=arguments.length,n=Array(2<i?i-2:0),s=2;s<i;s++)n[s-2]=arguments[s];return t.show.apply(t,[e].concat(n))},detachChildView:function(t){return this.getRegion(t).detachView()},getChildView:function(t){return this.getRegion(t).currentView}};var at={render:function(t,i){if(t)return(e.isFunction(t)?t:W.get(t))(i);throw new I({name:"TemplateNotFoundError",message:"Cannot render the template since its false, null or undefined."})}},lt=["behaviors","childViewEventPrefix","childViewEvents","childViewTriggers","collectionEvents","events","modelEvents","regionClass","regions","template","templateContext","triggers","ui"],ht=t.View.extend({constructor:function(i){this.render=e.bind(this.render,this),this._setOptions(i),this.mergeOptions(i,lt),T(this),this._initBehaviors(),this._initRegions(),(i=Array.prototype.slice.call(arguments))[0]=this.options,t.View.prototype.constructor.apply(this,i),this.delegateEntityEvents(),this._triggerEventOnBehaviors("initialize",this)},serializeData:function(){return this.model||this.collection?this.model?this.serializeModel():{items:this.serializeCollection()}:{}},serializeModel:function(){return this.model?e.clone(this.model.attributes):{}},serializeCollection:function(){return this.collection?this.collection.map((function(t){return e.clone(t.attributes)})):{}},setElement:function(){var e=!!this.el;return t.View.prototype.setElement.apply(this,arguments),e&&(this._isRendered=!!this.$el.length,this._isAttached=s(this.el)),this._isRendered&&this.bindUIElements(),this},render:function(){return this._isDestroyed||(this.triggerMethod("before:render",this),this._isRendered&&this._reInitRegions(),this._renderTemplate(),this.bindUIElements(),this._isRendered=!0,this.triggerMethod("render",this)),this},_renderTemplate:function(){var t,e=this.getTemplate();!1===e?l("template:false is deprecated.  Use _.noop."):(t=this.mixinTemplateContext(this.serializeData()),e=this._renderHtml(e,t),this.attachElContent(e))},_renderHtml:function(t,e){return at.render(t,e,this)},getTemplate:function(){return this.template},mixinTemplateContext:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=e.result(this,"templateContext");return e.extend(t,i)},attachElContent:function(t){return this.setInnerContent(this.el,t),this},_removeChildren:function(){this.removeRegions()},_getImmediateChildren:function(){return e.chain(this._getRegions()).map("currentView").compact().value()}},{setRenderer:function(t){this.prototype._renderHtml=t}}),ct=(e.extend(ht.prototype,Q,B),["forEach","each","map","find","detect","filter","select","reject","every","all","some","any","include","contains","invoke","toArray","first","initial","rest","last","without","isEmpty","pluck","reduce","partition"]),ut=(st(ot.prototype,"_views"),e.extend(ot.prototype,{add:function(t,e){return this._add(t,e)._updateLength()},_add:function(t,e){var i=t.cid;return(this._views[i]=t).model&&(this._indexByModel[t.model.cid]=i),e&&(this._indexByCustom[e]=i),this},findByModel:function(t){return this.findByModelCid(t.cid)},findByModelCid:function(t){return t=this._indexByModel[t],this.findByCid(t)},findByCustom:function(t){return t=this._indexByCustom[t],this.findByCid(t)},findByIndex:function(t){return e.values(this._views)[t]},findByCid:function(t){return this._views[t]},remove:function(t){return this._remove(t)._updateLength()},_remove:function(t){var i=t.cid;return t.model&&delete this._indexByModel[t.model.cid],e.some(this._indexByCustom,e.bind((function(t,e){if(t===i)return delete this._indexByCustom[e],!0}),this)),delete this._views[i],this},_updateLength:function(){return this.length=e.size(this._views),this}}),["behaviors","childView","childViewEventPrefix","childViewEvents","childViewOptions","childViewTriggers","collectionEvents","events","filter","emptyView","emptyViewOptions","modelEvents","reorderOnSort","sort","triggers","ui","viewComparator"]),dt=t.View.extend({sort:!0,constructor:function(i){this.render=e.bind(this.render,this),this._setOptions(i),this.mergeOptions(i,ut),T(this),this._initBehaviors(),this.once("render",this._initialEvents),this._initChildViewStorage(),this._bufferedChildren=[],(i=Array.prototype.slice.call(arguments))[0]=this.options,t.View.prototype.constructor.apply(this,i),this.delegateEntityEvents(),this._triggerEventOnBehaviors("initialize",this)},_startBuffering:function(){this._isBuffering=!0},_endBuffering:function(){var t=this._isAttached?this._getImmediateChildren():[];this._isBuffering=!1,e.each(t,(function(t){f(t,"before:attach",t)})),this.attachBuffer(this,this._createBuffer()),e.each(t,(function(t){t._isAttached=!0,f(t,"attach",t)})),this._bufferedChildren=[]},_getImmediateChildren:function(){return e.values(this.children._views)},_initialEvents:function(){this.collection&&(this.listenTo(this.collection,"add",this._onCollectionAdd),this.listenTo(this.collection,"update",this._onCollectionUpdate),this.listenTo(this.collection,"reset",this.render),this.sort)&&this.listenTo(this.collection,"sort",this._sortViews)},_onCollectionAdd:function(t,i,n){n=void 0!==n.at&&(n.index||i.indexOf(t)),!this.filter&&!1!==n||(n=e.indexOf(this._filteredSortedModels(n),t)),this._shouldAddChild(t,n)&&(this._destroyEmptyView(),this._addChild(t,n))},_onCollectionUpdate:function(t,e){e=e.changes,this._removeChildModels(e.removed)},_removeChildModels:function(t){(t=this._getRemovedViews(t)).length&&(this.children._updateLength(),this._updateIndices(t,!1),this.isEmpty())&&this._showEmptyView()},_getRemovedViews:function(t){var i=this;return e.reduce(t,(function(t,e){return(e=e&&i.children.findByModel(e))&&!e._isDestroyed&&(i._removeChildView(e),t.push(e)),t}),[])},_removeChildView:function(t){this.triggerMethod("before:remove:child",this,t),this.children._remove(t),t.destroy?t.destroy():Z(t),this.stopListening(t),this.triggerMethod("remove:child",this,t)},setElement:function(){var e=!!this.el;return t.View.prototype.setElement.apply(this,arguments),e&&(this._isAttached=s(this.el)),this},render:function(){return this._isDestroyed||(this.triggerMethod("before:render",this),this._renderChildren(),this._isRendered=!0,this.triggerMethod("render",this)),this},setFilter:function(t){var e=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).preventRender,i=this._isRendered&&!this._isDestroyed,n=this.filter!==t;return i&&n&&!e?(i=this._filteredSortedModels(),this.filter=t,n=this._filteredSortedModels(),this._applyModelDeltas(n,i)):this.filter=t,this},removeFilter:function(t){return this.setFilter(null,t)},_applyModelDeltas:function(t,i){var n=this,s={};e.each(t,(function(t,e){n.children.findByModel(t)||n._onCollectionAdd(t,n.collection,{at:e}),s[t.cid]=!0})),t=e.filter(i,(function(t){return!s[t.cid]&&n.children.findByModel(t)})),this._removeChildModels(t)},reorder:function(){var t,i,n=this.children,s=this._filteredSortedModels();return!s.length&&this._showingEmptyView||(e.some(s,(function(t){return!n.findByModel(t)}))?this.render():(t=[],i=n.reduce((function(i,n){var o=e.indexOf(s,n.model);return-1===o?t.push(n.model):i[n._index=o]=n.el,i}),new Array(s.length)),this.triggerMethod("before:reorder",this),this._appendReorderedChildren(i),this._removeChildModels(t),this.triggerMethod("reorder",this))),this},resortView:function(){return this.reorderOnSort?this.reorder():this._renderChildren(),this},_sortViews:function(){var t=this,i=this._filteredSortedModels();e.find(i,(function(e,i){return!(e=t.children.findByModel(e))||e._index!==i}))&&this.resortView()},_emptyViewIndex:-1,_appendReorderedChildren:function(t){this.appendChildren(this.el,t)},_renderChildren:function(){this._isRendered&&(this._destroyEmptyView(),this._destroyChildren());var t=this._filteredSortedModels();this.isEmpty({processedModels:t})?this._showEmptyView():(this.triggerMethod("before:render:children",this),this._startBuffering(),this._showCollection(t),this._endBuffering(),this.triggerMethod("render:children",this))},_createView:function(t,e){var i=this._getChildView(t);return e=this._getChildViewOptions(t,e),this.buildChildView(t,i,e)},_setupChildView:function(t,e){T(t),this._proxyChildViewEvents(t),this.sort&&(t._index=e)},_showCollection:function(t){e.each(t,e.bind(this._addChild,this)),this.children._updateLength()},_filteredSortedModels:function(t){var e,i,n;return this.collection&&this.collection.length?(e=this.getViewComparator(),i=this.collection.models,t=Math.min(Math.max(t,0),i.length-1),e&&(n=void 0,t&&(n=i[t],i=i.slice(0,t).concat(i.slice(t+1))),i=this._sortModelsBy(i,e),n)&&i.splice(t,0,n),this._filterModels(i)):[]},getViewComparator:function(){return this.viewComparator},_filterModels:function(t){var i=this;return this.filter?e.filter(t,(function(t,e){return i._shouldAddChild(t,e)})):t},_sortModelsBy:function(t,i){return"string"==typeof i?e.sortBy(t,(function(t){return t.get(i)})):1===i.length?e.sortBy(t,e.bind(i,this)):e.clone(t).sort(e.bind(i,this))},_showEmptyView:function(){var i,n,s=this._getEmptyView();s&&!this._showingEmptyView&&(this._showingEmptyView=!0,n=new t.Model,i=this.emptyViewOptions||this.childViewOptions,e.isFunction(i)&&(i=i.call(this,n,this._emptyViewIndex)),n=this.buildChildView(n,s,i),this.triggerMethod("before:render:empty",this,n),this.addChildView(n,0),this.triggerMethod("render:empty",this,n))},_destroyEmptyView:function(){this._showingEmptyView&&(this.triggerMethod("before:remove:empty",this),this._destroyChildren(),delete this._showingEmptyView,this.triggerMethod("remove:empty",this))},_getEmptyView:function(){var t=this.emptyView;if(t)return this._getView(t)},_getChildView:function(t){var e=this.childView;if(!e)throw new I({name:"NoChildViewError",message:'A "childView" must be specified'});if(e=this._getView(e,t))return e;throw new I({name:"InvalidChildViewError",message:'"childView" must be a view class or a function that returns a view class'})},_getView:function(i,n){return i.prototype instanceof t.View||i===t.View?i:e.isFunction(i)?i.call(this,n):void 0},_addChild:function(t,e){return t=this._createView(t,e),this.addChildView(t,e),t},_getChildViewOptions:function(t,i){return e.isFunction(this.childViewOptions)?this.childViewOptions(t,i):this.childViewOptions},addChildView:function(t,e){return this.triggerMethod("before:add:child",this,t),this._setupChildView(t,e),this._isBuffering?this.children._add(t):(this._updateIndices(t,!0),this.children.add(t)),this._renderView(t),this._attachView(t,e),this.triggerMethod("add:child",this,t),t},_updateIndices:function(t,i){var n;this.sort&&(i?(n=e.isArray(t)?e.max(t,"_index"):t,e.isObject(n)&&this.children.each((function(t){t._index>=n._index&&(t._index+=1)}))):e.each(e.sortBy(this.children._views,"_index"),(function(t,e){t._index=e})))},_renderView:function(t){t._isRendered||(t.supportsRenderLifecycle||f(t,"before:render",t),t.render(),t.supportsRenderLifecycle)||(t._isRendered=!0,f(t,"render",t))},_attachView:function(t,e){var i=!t._isAttached&&!this._isBuffering&&this._isAttached;i&&f(t,"before:attach",t),this.attachHtml(this,t,e),i&&(t._isAttached=!0,f(t,"attach",t))},buildChildView:function(t,i,n){return new i(e.extend({model:t},n))},removeChildView:function(t){return t&&!t._isDestroyed&&(this._removeChildView(t),this.children._updateLength(),this._updateIndices(t,!1)),t},isEmpty:function(t){var i=void 0;return 0===(i=e.result(t,"processedModels")?t.processedModels:(i=this.collection?this.collection.models:[],this._filterModels(i))).length},attachBuffer:function(t,e){this.appendChildren(t.el,e)},_createBuffer:function(){var t=this,i=this.createBuffer();return e.each(this._bufferedChildren,(function(e){t.appendChildren(i,e.el)})),i},attachHtml:function(t,e,i){t._isBuffering?t._bufferedChildren.splice(i,0,e):t._insertBefore(e,i)||t._insertAfter(e)},_insertBefore:function(t,e){var i=void 0;return!!(i=this.sort&&e<this.children.length-1?this.children.find((function(t){return t._index===e+1})):i)&&(this.beforeEl(i.el,t.el),!0)},_insertAfter:function(t){this.appendChildren(this.el,t.el)},_initChildViewStorage:function(){this.children=new ot},_removeChildren:function(){this._destroyChildren()},_destroyChildren:function(t){this.children.length&&(this.triggerMethod("before:destroy:children",this),this.children.each(e.bind(this._removeChildView,this)),this.children._updateLength(),this.triggerMethod("destroy:children",this))},_shouldAddChild:function(t,i){var n=this.filter;return!e.isFunction(n)||n.call(this,t,i,this.collection)}});function pt(t,e){return e.model&&e.model.get(t)}e.extend(dt.prototype,Q),st(rt.prototype,"_views"),e.extend(rt.prototype,{_init:function(){this._views=[],this._viewsByCid={},this._indexByModel={},this._updateLength()},_add:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this._views.length,i=t.cid;(this._viewsByCid[i]=t).model&&(this._indexByModel[t.model.cid]=i),this._views.splice(e,0,t),this._updateLength()},_sort:function(t){return"string"==typeof t?(t=e.partial(pt,t),this._sortBy(t)):1===t.length?this._sortBy(t):this._views.sort(t)},_sortBy:function(t){return t=e.sortBy(this._views,t),this._set(t),t},_set:function(t){this._views.length=0,this._views.push.apply(this._views,t.slice(0)),this._updateLength()},findByModel:function(t){return this.findByModelCid(t.cid)},findByModelCid:function(t){return t=this._indexByModel[t],this.findByCid(t)},findByIndex:function(t){return this._views[t]},findIndexByView:function(t){return this._views.indexOf(t)},findByCid:function(t){return this._viewsByCid[t]},_remove:function(t){this._viewsByCid[t.cid]&&(t.model&&delete this._indexByModel[t.model.cid],delete this._viewsByCid[t.cid],t=this.findIndexByView(t),this._views.splice(t,1),this._updateLength())},_updateLength:function(){this.length=this._views.length}});var ft=["behaviors","childView","childViewEventPrefix","childViewEvents","childViewOptions","childViewTriggers","collectionEvents","emptyView","emptyViewOptions","events","modelEvents","sortWithCollection","triggers","ui","viewComparator","viewFilter"],gt=(B=t.View.extend({sortWithCollection:!0,constructor:function(e){this._setOptions(e),this.mergeOptions(e,ft),T(this),this.once("render",this._initialEvents),this._initChildViewStorage(),this._initBehaviors(),(e=Array.prototype.slice.call(arguments))[0]=this.options,t.View.prototype.constructor.apply(this,e),this._initEmptyRegion(),this.delegateEntityEvents(),this._triggerEventOnBehaviors("initialize",this)},_initChildViewStorage:function(){this.children=new rt},_initEmptyRegion:function(){this.emptyRegion=new it({el:this.el}),this.emptyRegion._parentView=this},_initialEvents:function(){this.listenTo(this.collection,{sort:this._onCollectionSort,reset:this._onCollectionReset,update:this._onCollectionUpdate})},_onCollectionSort:function(){var t=this;this.sortWithCollection&&this.collection.length===this.children.length&&!this.collection.some((function(e){return!t.children.findByModel(e)}))&&this.sort()},_onCollectionReset:function(){this.render()},_onCollectionUpdate:function(t,e){e=e.changes;var i=this._removeChildModels(e.removed);this._addChildModels(e.added),this._detachChildren(i),this._showChildren(),this._removeChildViews(i)},_removeChildModels:function(t){return e.map(t,e.bind(this._removeChildModel,this))},_removeChildModel:function(t){return t=this.children.findByModel(t),this._removeChild(t),t},_removeChild:function(t){this.triggerMethod("before:remove:child",this,t),this.children._remove(t),this.triggerMethod("remove:child",this,t)},_addChildModels:function(t){return e.map(t,e.bind(this._addChildModel,this))},_addChildModel:function(t){return t=this._createChildView(t),this._addChild(t),t},_createChildView:function(t){var e=this._getChildView(t),i=this._getChildViewOptions(t);return this.buildChildView(t,e,i)},_addChild:function(t,e){this.triggerMethod("before:add:child",this,t),this._setupChildView(t),this.children._add(t,e),this.triggerMethod("add:child",this,t)},_getChildView:function(t){var e=this.childView;if(!e)throw new I({name:"NoChildViewError",message:'A "childView" must be specified'});if(e=this._getView(e,t))return e;throw new I({name:"InvalidChildViewError",message:'"childView" must be a view class or a function that returns a view class'})},_getView:function(i,n){return i.prototype instanceof t.View||i===t.View?i:e.isFunction(i)?i.call(this,n):void 0},_getChildViewOptions:function(t){return e.isFunction(this.childViewOptions)?this.childViewOptions(t):this.childViewOptions},buildChildView:function(t,i,n){return new i(e.extend({model:t},n))},_setupChildView:function(t){T(t),t.on("destroy",this.removeChildView,this),this._proxyChildViewEvents(t)},_getImmediateChildren:function(){return this.children._views},setElement:function(){var e=!!this.el;return t.View.prototype.setElement.apply(this,arguments),e&&(this._isAttached=s(this.el)),this},render:function(){return this._isDestroyed||(this.triggerMethod("before:render",this),this._destroyChildren(),this.children._init(),this.collection&&this._addChildModels(this.collection.models),this._showChildren(),this._isRendered=!0,this.triggerMethod("render",this)),this},sort:function(){return this._isDestroyed||this.children.length&&this._showChildren(),this},_showChildren:function(){this.isEmpty()?this._showEmptyView():(this._sortChildren(),this.filter())},isEmpty:function(t){return t||!this.children.length},_showEmptyView:function(){var t,e=this._getEmptyView();e&&(t=this._getEmptyViewOptions(),this.emptyRegion.show(new e(t)))},_getEmptyView:function(){var t=this.emptyView;if(t)return this._getView(t)},_destroyEmptyView:function(){this.emptyRegion.hasView()&&this.emptyRegion.empty()},_getEmptyViewOptions:function(){var t=this.emptyViewOptions||this.childViewOptions;return e.isFunction(t)?t.call(this):t},_sortChildren:function(){this.triggerMethod("before:sort",this);var t=this.getComparator();e.isFunction(t)&&(t=t.bind(this)),this.children._sort(t),this.triggerMethod("sort",this)},setComparator:function(t){var e=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).preventRender;return e=this.viewComparator!==t&&!e,this.viewComparator=t,e&&this.sort(),this},removeComparator:function(t){return this.setComparator(null,t)},getComparator:function(){return this.viewComparator||this._viewComparator},_viewComparator:function(t){if(this.collection)return this.collection.indexOf(t.model)},filter:function(){var t;return this._isDestroyed||this.children.length&&(t=this._filterChildren(),this._renderChildren(t)),this},_filterChildren:function(){var t=this._getFilter();return t?(this.triggerMethod("before:filter",this),t=this.children.partition(e.bind(t,this)),this._detachChildren(t[1]),this.triggerMethod("filter",this),t[0]):this.children._views},_getFilter:function(){var t,i=this.getFilter();if(!i)return!1;if(e.isFunction(i))return i;if(e.isObject(i))return t=e.matches(i),function(e){return t(e.model&&e.model.attributes)};if(e.isString(i))return function(t){return t.model&&t.model.get(i)};throw new I({name:"InvalidViewFilterError",message:'"viewFilter" must be a function, predicate object literal, a string indicating a model attribute, or falsy'})},getFilter:function(){return this.viewFilter},setFilter:function(t){var e=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).preventRender;return e=this.viewFilter!==t&&!e,this.viewFilter=t,e&&this.filter(),this},removeFilter:function(t){return this.setFilter(null,t)},_detachChildren:function(t){e.each(t,e.bind(this._detachChildView,this))},_detachChildView:function(t){var e=!!t._isAttached;e&&f(t,"before:detach",t),this.detachHtml(t),e&&(t._isAttached=!1,f(t,"detach",t))},detachHtml:function(t){this.detachEl(t.el)},_renderChildren:function(t){var e;this.isEmpty(!t.length)?this._showEmptyView():(this._destroyEmptyView(),this.triggerMethod("before:render:children",this,t),e=this._getBuffer(t),this._attachChildren(e,t),this.triggerMethod("render:children",this,t))},_attachChildren:function(t,i){var n=!!this._isAttached;e.each(i=n?i:[],(function(t){t._isAttached||f(t,"before:attach",t)})),this.attachHtml(this,t),e.each(i,(function(t){t._isAttached||(t._isAttached=!0,f(t,"attach",t))}))},_getBuffer:function(t){var i=this,n=this.createBuffer();return e.each(t,(function(t){i._renderChildView(t),i.appendChildren(n,t.el)})),n},_renderChildView:function(t){t._isRendered||(t.supportsRenderLifecycle||f(t,"before:render",t),t.render(),t.supportsRenderLifecycle)||(t._isRendered=!0,f(t,"render",t))},attachHtml:function(t,e){this.appendChildren(t.el,e)},addChildView:function(t,e){return t&&!t._isDestroyed&&(this._addChild(t,e),this._showChildren()),t},detachChildView:function(t){return this.removeChildView(t,{shouldDetach:!0}),t},removeChildView:function(t,e){return t&&(this._removeChildView(t,e),this._removeChild(t),this.isEmpty())&&this._showEmptyView(),t},_removeChildViews:function(t){e.each(t,e.bind(this._removeChildView,this))},_removeChildView:function(t){var e=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).shouldDetach;t.off("destroy",this.removeChildView,this),e?this._detachChildView(t):this._destroyChildView(t),this.stopListening(t)},_destroyChildView:function(t){t._isDestroyed||(t.destroy?t.destroy():Z(t))},_removeChildren:function(){this._destroyChildren(),this.emptyRegion.destroy()},_destroyChildren:function(){this.children&&this.children.length&&(this.triggerMethod("before:destroy:children",this),this.children.each(e.bind(this._removeChildView,this)),this.triggerMethod("destroy:children",this))}}),e.extend(B.prototype,Q),["childViewContainer","template","templateContext"]),mt=(Q=dt.extend({constructor:function(t){l("CompositeView is deprecated. Convert to View at your earliest convenience"),this.mergeOptions(t,gt),dt.prototype.constructor.apply(this,arguments)},_initialEvents:function(){this.collection&&(this.listenTo(this.collection,"add",this._onCollectionAdd),this.listenTo(this.collection,"update",this._onCollectionUpdate),this.listenTo(this.collection,"reset",this.renderChildren),this.sort)&&this.listenTo(this.collection,"sort",this._sortViews)},_getChildView:function(t){var e=this.childView;if(!e)return this.constructor;if(e=this._getView(e,t))return e;throw new I({name:"InvalidChildViewError",message:'"childView" must be a view class or a function that returns a view class'})},serializeData:function(){return this.serializeModel()},render:function(){return this._isDestroyed||(this._isRendering=!0,this.resetChildViewContainer(),this.triggerMethod("before:render",this),this._renderTemplate(),this.bindUIElements(),this.renderChildren(),this._isRendering=!1,this._isRendered=!0,this.triggerMethod("render",this)),this},renderChildren:function(){(this._isRendered||this._isRendering)&&dt.prototype._renderChildren.call(this)},attachBuffer:function(t,e){t=this.getChildViewContainer(t),this.appendChildren(t,e)},_insertAfter:function(t){var e=this.getChildViewContainer(this,t);this.appendChildren(e,t.el)},_appendReorderedChildren:function(t){var e=this.getChildViewContainer(this);this.appendChildren(e,t)},getChildViewContainer:function(t,i){if(t.$childViewContainer)return t.$childViewContainer;var n=void 0;if(t.childViewContainer){var s=e.result(t,"childViewContainer");if((n="@"===s.charAt(0)&&t.ui?t.ui[s.substr(4)]:this.findEls(s,t.$el)).length<=0)throw new I({name:"ChildViewContainerMissingError",message:'The specified "childViewContainer" was not found: '+t.childViewContainer})}else n=t.$el;return t.$childViewContainer=n},resetChildViewContainer:function(){this.$childViewContainer&&(this.$childViewContainer=void 0)}}),e.pick(ht.prototype,"serializeModel","getTemplate","_renderTemplate","_renderHtml","mixinTemplateContext","attachElContent")),vt=(e.extend(Q.prototype,mt),["collectionEvents","events","modelEvents","triggers","ui"]),_t=(mt=N.extend({cidPrefix:"mnb",constructor:function(t,i){this.view=i,this.defaults&&l("Behavior defaults are deprecated. For similar functionality set options on the Behavior class."),this.defaults=e.clone(e.result(this,"defaults",{})),this._setOptions(this.defaults,t),this.mergeOptions(this.options,vt),this.ui=e.extend({},e.result(this,"ui"),e.result(i,"ui")),N.apply(this,arguments)},$:function(){return this.view.$.apply(this.view,arguments)},destroy:function(){return this.stopListening(),this.view._removeBehavior(this),this},proxyViewProperties:function(){return this.$el=this.view.$el,this.el=this.view.el,this},bindUIElements:function(){return this._bindUIElements(),this},unbindUIElements:function(){return this._unbindUIElements(),this},getUI:function(t){return this._getUI(t)},delegateEntityEvents:function(){return this._delegateEntityEvents(this.view.model,this.view.collection),this},undelegateEntityEvents:function(){return this._undelegateEntityEvents(this.view.model,this.view.collection),this},getEvents:function(){var t=this,i=this.normalizeUIKeys(e.result(this,"events"));return e.reduce(i,(function(i,n,s){if(n=e.isFunction(n)?n:t[n])return i[s=U(s)]=e.bind(n,t),i}),{})},getTriggers:function(){var t;if(this.triggers)return t=this.normalizeUIKeys(e.result(this,"triggers")),this._getViewTriggers(this.view,t)}}),e.extend(mt.prototype,G,X,J),["region","regionClass"]),bt=(G=N.extend({cidPrefix:"mna",constructor:function(t){this._setOptions(t),this.mergeOptions(t,_t),this._initRegion(),N.prototype.constructor.apply(this,arguments)},regionClass:it,_initRegion:function(){var t,e=this.region;e&&(t={regionClass:this.regionClass},this._region=tt(e,t))},getRegion:function(){return this._region},showView:function(t){for(var e=this.getRegion(),i=arguments.length,n=Array(1<i?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];return e.show.apply(e,[t].concat(n))},getView:function(){return this.getRegion().currentView},start:function(t){return this.triggerMethod("before:start",this,t),this.triggerMethod("start",this,t),this}}),["appRoutes","controller"]);X=t.Router.extend({constructor:function(e){this._setOptions(e),this.mergeOptions(e,bt),t.Router.apply(this,arguments),e=this.appRoutes;var i=this._getController();this.processAppRoutes(i,e),this.on("route",this._processOnRoute,this)},appRoute:function(t,e){var i=this._getController();return this._addAppRoute(i,t,e),this},_processOnRoute:function(t,i){var n;e.isFunction(this.onRoute)&&(n=e.invert(this.appRoutes)[t],this.onRoute(t,n,i))},processAppRoutes:function(t,i){var n,s=this;return i&&(n=e.keys(i).reverse(),e.each(n,(function(e){s._addAppRoute(t,e,i[e])}))),this},_getController:function(){return this.controller},_addAppRoute:function(t,i,n){var s=t[n];if(!s)throw new I('Method "'+n+'" was not found on the controller');this.route(i,n,e.bind(s,t))},triggerMethod:p}),e.extend(X.prototype,R);var yt=t.Marionette,wt=t.Marionette={noConflict:function(){return t.Marionette=yt,this}};return wt.bindEvents=n(P),wt.unbindEvents=n(O),wt.bindRequests=n(H),wt.unbindRequests=n(V),wt.mergeOptions=n(o),wt.getOption=n(r),wt.normalizeMethods=n(h),wt.extend=a,wt.isNodeAttached=s,wt.deprecate=l,wt.triggerMethod=n(p),wt.triggerMethodOn=f,wt.isEnabled=function(t){return!!$[t]},wt.setEnabled=function(t,e){return $[t]=e},wt.monitorViewEvents=T,wt.Behaviors={},wt.Behaviors.behaviorsLookup=function(){throw new I({message:"You must define where your behaviors are stored.",url:"marionette.behaviors.md#behaviorslookup"})},wt.Application=G,wt.AppRouter=X,wt.Renderer=at,wt.TemplateCache=W,wt.View=ht,wt.CollectionView=dt,wt.NextCollectionView=B,wt.CompositeView=Q,wt.Behavior=mt,wt.Region=it,wt.Error=I,wt.Object=N,wt.DEV_MODE=!1,wt.FEATURES=$,wt.VERSION="3.3.1",wt})),function(){"use strict";var t=Namespace.using("PDScripts");t.Module=function(t,e,i){this.moduleName=t,this.options=_.extend({},this.options,i),this.initialize=i.initialize||this.initialize,this.submodules={},this.application=e,_.isFunction(this.initialize)&&this.initialize(t,e,this.options)},t.Module.extend=Backbone.Model.extend,_.extend(t.Module.prototype,Backbone.Events,{startWithParent:!0,initialize:function(){},start:function(e){if(!this._isInitialized){if(t.Config&&t.Config[this.moduleName]){var i,n=_.clone(t.Config[this.moduleName]);for(i in n)this.hasOwnProperty(i)&&(this[i]=n[i],n[i]=void 0,delete n[i]);this.config=_.extend(this.config||{},n)}else this.config=this.config||{};_.each(this.submodules,(function(t){t.startWithParent&&t.start(e)})),this.triggerMethod(this,"before:start",e),this._isInitialized=!0,this.triggerMethod(this,"start",e)}},stop:function(){this._isInitialized&&(this._isInitialized=!1,this.triggerMethod(this,"before:stop"),_.invoke(this.submodules,"stop"),this.triggerMethod(this,"stop"))},addDefinition:function(t,e){this._runModuleDefinition(t,e)},_runModuleDefinition:function(t,e){t&&(e=_.flatten([this,this.application,Backbone,Backbone.Marionette,Backbone.$,_,e]),t.apply(this,e))},triggerMethod:Backbone.Marionette.triggerMethod,triggerMethodOn:Backbone.Marionette.triggerMethodOn}),_.extend(t.Module,{create:function(t,e,i){var n=t,s=_.drop(arguments,3),o=(e=e.split(".")).length,r=[];return r[o-1]=i,_.each(e,(function(e,o){var a=n;n=this._getModule(a,e,t,i),this._addModuleDefinition(a,n,r[o],s)}),this),n},_getModule:function(t,e,i,n){var s=_.extend({},n),o=(n=this.getClass(n),t[e]);return o||(o=new n(e,i,s),t[e]=o,t.submodules&&(t.submodules[e]=o)),o},getClass:function(e){var i=t.Module;return e?e.prototype instanceof i?e:e.moduleClass||i:i},_addModuleDefinition:function(t,e,i,n){var s=this._getDefine(i);i=this._getStartWithParent(i,e),s&&e.addDefinition(s,n),this._addStartWithParent(t,e,i)},_getStartWithParent:function(e,i){var n;return _.isFunction(e)&&e.prototype instanceof t.Module?(n=i.constructor.prototype.startWithParent,!!_.isUndefined(n)||n):!_.isObject(e)||(n=e.startWithParent,!!_.isUndefined(n))||n},_getDefine:function(e){return!_.isFunction(e)||e.prototype instanceof t.Module?_.isObject(e)?e.define:null:e},_addStartWithParent:function(t,e,i){e.startWithParent=e.startWithParent&&i,e.startWithParent&&!e.startWithParentIsConfigured&&(e.startWithParentIsConfigured=!0,e.listenToOnce(PD,"before:start",(function(t,i){e.startWithParent&&e.start(i)})))}})}(),function(){"use strict";_.extend(Marionette.Renderer,{render:function(t,e){return $(document).trigger({type:"spinner",mode:"start"}),t=_.isFunction(t)?t:Marionette.TemplateCache.get(t),$(document).trigger({type:"spinner",mode:"stop"}),t(e)}}),_.extend(Backbone.Marionette.TemplateCache.prototype,{loadTemplate:function(t){t=(t=_.isFunction(t)?t():t)||"";var e=_.str.startsWith(t,"#")?Backbone.$(t).html():t;if(_.isString(e))return _.str.trim(e);throw(e=new Error("Could not find template: '"+t+"'")).name="NoTemplateError",e}}),_.extend(Backbone.Marionette.CollectionView.prototype,{buildChildView:function(t,e,i){return new e(_.extend({model:t,parent:this},i))}});var t=Namespace.register("PDScripts.Module");Backbone.Marionette.Application.prototype.module=function(e,i){i=t.getClass(i);var n=_.toArray(arguments);return n.unshift(this),i.create.apply(i,n)}}(),function(t){"function"==typeof define&&define.amd?define(["underscore","jquery","backbone"],t):"undefined"!=typeof module&&module.exports?module.exports=t(require("underscore"),require("jquery"),require("backbone")):t(_,jQuery,Backbone)}((function(t,e,i){if(i)return i.ModelBinder=function(){t.bindAll.apply(t,[this].concat(t.functions(this)))},i.ModelBinder.SetOptions=function(t){i.ModelBinder.options=t},i.ModelBinder.VERSION="1.1.0",i.ModelBinder.Constants={},i.ModelBinder.Constants.ModelToView="ModelToView",i.ModelBinder.Constants.ViewToModel="ViewToModel",t.extend(i.ModelBinder.prototype,{bind:function(t,i,n,s){this.unbind(),this._model=t,this._rootEl=i,this._setOptions(s),this._model||this._throwException("model must be specified"),this._rootEl||this._throwException("rootEl must be specified"),n?(this._attributeBindings=e.extend(!0,{},n),this._initializeAttributeBindings(),this._initializeElBindings()):this._initializeDefaultBindings(),this._bindModelToView(),this._bindViewToModel()},bindCustomTriggers:function(t,e,i,n,s){this._triggers=i,this.bind(t,e,n,s)},unbind:function(){this._unbindModelToView(),this._unbindViewToModel(),this._attributeBindings&&(delete this._attributeBindings,this._attributeBindings=void 0)},_setOptions:function(e){this._options=t.extend({boundAttribute:"name"},i.ModelBinder.options,e),this._options.modelSetOptions||(this._options.modelSetOptions={}),this._options.modelSetOptions.changeSource="ModelBinder",this._options.changeTriggers||(this._options.changeTriggers={"":"change","[contenteditable]":"blur"}),this._options.initialCopyDirection||(this._options.initialCopyDirection=i.ModelBinder.Constants.ModelToView)},_initializeAttributeBindings:function(){var e,i,n,s;for(e in this._attributeBindings){for(i=this._attributeBindings[e],t.isString(i)?n={elementBindings:[{selector:i}]}:t.isArray(i)?n={elementBindings:i}:t.isObject(i)?n={elementBindings:[i]}:this._throwException("Unsupported type passed to Model Binder "+n),s=0;s<n.elementBindings.length;s++)n.elementBindings[s].attributeBinding=n;n.attributeName=e,this._attributeBindings[e]=n}},_initializeDefaultBindings:function(){var t,i,n,s,o;for(this._attributeBindings={},i=e("["+this._options.boundAttribute+"]",this._rootEl),t=0;t<i.length;t++)n=i[t],s=e(n).attr(this._options.boundAttribute),this._attributeBindings[s]?this._attributeBindings[s].elementBindings.push({attributeBinding:this._attributeBindings[s],boundEls:[n]}):((o={attributeName:s}).elementBindings=[{attributeBinding:o,boundEls:[n]}],this._attributeBindings[s]=o)},_initializeElBindings:function(){var t,i,n,s,o,r,a;for(t in this._attributeBindings)for(i=this._attributeBindings[t],n=0;n<i.elementBindings.length;n++)if(0===(o=""===(s=i.elementBindings[n]).selector?e(this._rootEl):e(s.selector,this._rootEl)).length)this._throwException("Bad binding found. No elements returned for binding selector "+s.selector);else for(s.boundEls=[],r=0;r<o.length;r++)a=o[r],s.boundEls.push(a)},_bindModelToView:function(){this._model.on("change",this._onModelChange,this),this._options.initialCopyDirection===i.ModelBinder.Constants.ModelToView&&this.copyModelAttributesToView()},copyModelAttributesToView:function(e){var i,n;for(i in this._attributeBindings)void 0!==e&&-1===t.indexOf(e,i)||(n=this._attributeBindings[i],this._copyModelToView(n))},copyViewValuesToModel:function(){var t,i,n,s,o,r;for(t in this._attributeBindings)for(i=this._attributeBindings[t],n=0;n<i.elementBindings.length;n++)if(s=i.elementBindings[n],this._isBindingUserEditable(s))if(this._isBindingRadioGroup(s))(r=this._getRadioButtonGroupCheckedEl(s))&&this._copyViewToModel(s,r);else for(o=0;o<s.boundEls.length;o++)r=e(s.boundEls[o]),this._isElUserEditable(r)&&this._copyViewToModel(s,r)},_unbindModelToView:function(){this._model&&(this._model.off("change",this._onModelChange),this._model=void 0)},_bindViewToModel:function(){t.each(this._options.changeTriggers,(function(t,i){e(this._rootEl).on(t,i,this._onElChanged)}),this),this._options.initialCopyDirection===i.ModelBinder.Constants.ViewToModel&&this.copyViewValuesToModel()},_unbindViewToModel:function(){this._options&&this._options.changeTriggers&&t.each(this._options.changeTriggers,(function(t,i){e(this._rootEl).off(t,i,this._onElChanged)}),this)},_onElChanged:function(t){for(var i,n=e(t.target)[0],s=this._getElBindings(n),o=0;o<s.length;o++)i=s[o],this._isBindingUserEditable(i)&&this._copyViewToModel(i,n)},_isBindingUserEditable:function(t){return void 0===t.elAttribute||"text"===t.elAttribute||"html"===t.elAttribute},_isElUserEditable:function(t){return t.attr("contenteditable")||t.is("input")||t.is("select")||t.is("textarea")},_isBindingRadioGroup:function(t){for(var i=0<t.boundEls.length,n=0;n<t.boundEls.length;n++)if("radio"!==e(t.boundEls[n]).attr("type")){i=!1;break}return i},_getRadioButtonGroupCheckedEl:function(t){for(var i,n=0;n<t.boundEls.length;n++)if("radio"===(i=e(t.boundEls[n])).attr("type")&&i.prop("checked"))return i},_getElBindings:function(t){var e,i,n,s,o,r=[];for(e in this._attributeBindings)for(i=this._attributeBindings[e],n=0;n<i.elementBindings.length;n++)for(s=i.elementBindings[n],o=0;o<s.boundEls.length;o++)s.boundEls[o]===t&&r.push(s);return r},_onModelChange:function(){var t,e;for(t in this._model.changedAttributes())(e=this._attributeBindings[t])&&this._copyModelToView(e)},_copyModelToView:function(t){for(var n,s,o,r,a=this._model.get(t.attributeName),l=0;l<t.elementBindings.length;l++)for(n=t.elementBindings[l],s=0;s<n.boundEls.length;s++)(o=n.boundEls[s])._isSetting||(r=this._getConvertedValue(i.ModelBinder.Constants.ModelToView,n,a),this._setEl(e(o),n,r))},_setEl:function(t,e,i){e.elAttribute?this._setElAttribute(t,e,i):this._setElValue(t,i)},_setElAttribute:function(e,n,s){switch(n.elAttribute){case"html":e.html(s);break;case"text":e.text(s);break;case"enabled":e.prop("disabled",!s);break;case"displayed":e[s?"show":"hide"]();break;case"hidden":e[s?"hide":"show"]();break;case"css":e.css(n.cssAttribute,s);break;case"class":var o=this._model.previous(n.attributeBinding.attributeName),r=this._model.get(n.attributeBinding.attributeName);t.isUndefined(o)&&t.isUndefined(r)||(o=this._getConvertedValue(i.ModelBinder.Constants.ModelToView,n,o),e.removeClass(o)),s&&e.addClass(s);break;default:e.attr(n.elAttribute,s)}},_setElValue:function(t,e){if(t.attr("type"))switch(t.attr("type")){case"radio":t.prop("checked",t.val()===e);break;case"checkbox":t.prop("checked",!!e);break;case"file":break;default:t.val(e)}else t.is("input")||t.is("select")||t.is("textarea")?t.val(e||(0===e?"0":"")):t.text(e||(0===e?"0":""))},_copyViewToModel:function(t,n){var s;n._isSetting||(n._isSetting=!0,s=this._setModel(t,e(n)),n._isSetting=!1,s&&t.converter&&(s=this._model.get(t.attributeBinding.attributeName),s=this._getConvertedValue(i.ModelBinder.Constants.ModelToView,t,s),this._setEl(e(n),t,s)))},_getElValue:function(t,e){return"checkbox"!==e.attr("type")?void 0!==e.attr("contenteditable")?e.html():e.val():!!e.prop("checked")},_setModel:function(t,e){var n={};return e=this._getElValue(t,e),e=this._getConvertedValue(i.ModelBinder.Constants.ViewToModel,t,e),n[t.attributeBinding.attributeName]=e,this._model.set(n,this._options.modelSetOptions)},_getConvertedValue:function(t,e,i){return e.converter?i=e.converter(t,i,e.attributeBinding.attributeName,this._model,e.boundEls):this._options.converter&&(i=this._options.converter(t,i,e.attributeBinding.attributeName,this._model,e.boundEls)),i},_throwException:function(t){if(!this._options.suppressThrows)throw t;"undefined"!=typeof console&&console.error&&console.error(t)}}),i.ModelBinder.CollectionConverter=function(e){if(this._collection=e,!this._collection)throw"Collection must be defined";t.bindAll(this,"convert")},t.extend(i.ModelBinder.CollectionConverter.prototype,{convert:function(t,e){return t===i.ModelBinder.Constants.ModelToView?e?e.id:void 0:this._collection.get(e)}}),i.ModelBinder.createDefaultBindings=function(t,i,n,s){for(var o={},r=e("["+i+"]",t),a=0;a<r.length;a++){var l=r[a];o[l=e(l).attr(i)]||(o[l]={selector:"["+i+'="'+l+'"]'},n&&(o[l].converter=n),s&&(o[l].elAttribute=s))}return o},i.ModelBinder.combineBindings=function(e,i){return t.each(i,(function(t,i){var n={selector:t.selector};t.converter&&(n.converter=t.converter),t.elAttribute&&(n.elAttribute=t.elAttribute),e[i]?e[i]=[e[i],n]:e[i]=n})),e},i.ModelBinder;throw"Please include Backbone.js before Backbone.ModelBinder.js"})),function(){"use strict";Backbone.ModelBinder.SetOptions({boundAttribute:"data-bindto"}),_.extend(Backbone.ModelBinder.prototype,{copyViewToModel:function(t){for(var e,i=this._getElBindings(t),n=0;n<i.length;n++)e=i[n],this._isBindingUserEditable(e)&&this._copyViewToModel(e,t)}}),Backbone.ModelBinder.prototype._setElValue=_.wrap(Backbone.ModelBinder.prototype._setElValue,(function(t){var e=_.toArray(arguments).slice(1);t.apply(this,e),e[0].trigger("set")}))}(),function(){"use strict";Backbone.ModelBinder.Converters={currency:function(t,e,n,s,o){return s&&s.isStrict&&_.isEmpty(e)&&t===Backbone.ModelBinder.Constants.ViewToModel?e:(_.isEmpty(e)||(e=e.replace("$","")),i[t](e,o))},percent:function(e,i,n,s,o){return o=0===o?0:o||2,_.isEmpty(i)||(i=i.replace("%","")),t(e,i,n,s,o)},floatOneDecimal:function(t,i,n,s){return e(i,t,1)},floatTwoDecimal:function(t,i,n,s){return e(i,t,2)},percentThreeDecimal:function(t,e,i,n){return Backbone.ModelBinder.Converters.percent(t,e,i,n,3)},getKeyedFormatter:function(t,e){if(_.isObject(e))return function(i,n,s,o){return e[o.get(t)](i,n,s,o)};throw"map parameter should be an object."}};var t=function(t,e,i,n,s){var o=Math.pow(10,s);return o=Math.round(parseFloat(e)*o)/o,_.isFinite(o)?"ViewToModel"===t?o:o.toFixed(s):$.trim(e)},e=function(t,e,i){var n=Math.pow(10,i);return n=Math.round(parseFloat(t)*n)/n,_.isFinite(n)?e===Backbone.ModelBinder.Constants.ViewToModel?n:n.toFixed(i):$.trim(t)},i={ModelToView:function(t,e){return e=0===e?0:2,(t=parseFloat(t||0).toFixed(e)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},ViewToModel:function(t){return t=t?t.toString():"0",t=parseFloat(_.str.replaceAll(t,",","")),_.isFinite(t)?t:$.trim(t)}}}(),Backbone.Validation=function(t){"use strict";var e,i,n,s,o={forceUpdate:!1,selector:"name",labelFormatter:"sentenceCase",valid:Function.prototype,invalid:Function.prototype},r={formatLabel:function(t,e){return _[o.labelFormatter](t,e)},format:function(){var t=Array.prototype.slice.call(arguments);return t.shift().replace(/\{(\d+)\}/g,(function(e,i){return void 0!==t[i]?t[i]:e}))}},a=function(e,i,n){return i=i||{},n=n||"",t.each(e,(function(s,o){e.hasOwnProperty(o)&&(s&&t.isArray(s)?t.forEach(s,(function(t,e){a(t,i,n+o+"."+e+"."),i[n+o+"."+e]=t})):s&&"object"==typeof s&&s.constructor===Object&&a(s,i,n+o+"."),i[n+o]=s)})),i},l=(e=function(e,i){return i=i||t.keys(t.result(e,"validation")||{}),t.reduce(i,(function(t,e){return t[e]=void 0,t}),{})},i=function(e,i){if(e=e.attributes,t.isFunction(e)?e=e(i):t.isString(e)&&t.isFunction(b[e])&&(e=b[e](i)),t.isArray(e))return e},n=function(e,i,n,s){return t.reduce(function(e,i){return e=e.validation&&t.result(e,"validation")[i]||{},(t.isFunction(e)||t.isString(e))&&(e={fn:e}),t.isArray(e)||(e=[e]),t.reduce(e,(function(e,i){return t.each(t.without(t.keys(i),"msg"),(function(t){e.push({fn:y[t],val:i[t],msg:i.msg})})),e}),[])}(e,i),(function(o,a){var l=t.extend({},r,y);return!1!==(l=a.fn.call(l,n,i,a.val,e,s))&&!1!==o&&(l&&!o?t.result(a,"msg")||l:o)}),"")},s=function(e,i,s){var o,r={},a=!0,l=t.clone(i);return t.each(s,(function(t,i){(o=n(e,i,t,l))&&(r[i]=o,a=!1)})),{invalidAttrs:r,isValid:a}},{version:"0.11.3",configure:function(e){t.extend(o,e)},bind:function(e,i){var n=(i=t.extend({},o,g,i)).model||e.model,s=i.collection||e.collection;if(void 0===n&&void 0===s)throw"Before you execute the binding your view must have a model or a collection.\nSee http://thedersen.com/projects/backbone-validation/#using-form-model-validation for more information.";n?c(e,n,i):s&&(s.each((function(t){c(e,t,i)})),s.bind("add",d,{view:e,options:i}),s.bind("remove",p))},unbind:function(e,i){var n=(i=t.extend({},i)).model||e.model;i=i.collection||e.collection,n?u(n,e):i&&(i.each((function(t){u(t,e)})),i.unbind("add",d),i.unbind("remove",p))},mixin:h(null,o)});function h(o,r){return{preValidate:function(e,i){var s,o=this,r={};return t.isObject(e)?(t.each(e,(function(t,e){(s=o.preValidate(e,t))&&(r[e]=s)})),t.isEmpty(r)?void 0:r):n(this,e,i,t.extend({},this.attributes))},isValid:function(e){var s,l,h,c;return e=e||i(r,o),t.isString(e)?l=[e]:t.isArray(e)&&(l=e),l&&(s=a(this.attributes),t.each(this.associatedViews,(function(e){t.each(l,(function(i){(h=n(this,i,s[i],t.extend({},this.attributes)))?(r.invalid(e,i,h,r.selector),(c=c||{})[i]=h):r.valid(e,i,r.selector)}),this)}),this)),(c=!0===e?this.validate():c)&&this.trigger("invalid",this,c,{validationError:c}),l?!c:!this.validation||this._isValid},validate:function(n,l){var h=this,c=!n,u=t.extend({},r,l),d=e(h,i(r,o)),p=(l=t.extend({},d,h.attributes,n),l=!u.validate&&n||l,a(l)),f=n?a(n):p,g=s(h,l,t.pick(p,t.keys(d)));if(!u.validate&&n){var m,v=t.keys(n);for(m in d)t.contains(v,m)||delete d[m]}if(h._isValid=g.isValid,t.each(h.associatedViews,(function(e){t.each(d,(function(t,i){var n=g.invalidAttrs.hasOwnProperty(i),s=f.hasOwnProperty(i);n||u.valid(e,i,u.selector),n&&(s||c)&&u.invalid(e,i,g.invalidAttrs[i],u.selector)}))})),t.defer((function(){h.trigger("validated",h._isValid,h,g.invalidAttrs),h.trigger("validated:"+(h._isValid?"valid":"invalid"),h,g.invalidAttrs)})),!u.forceUpdate&&0<t.intersection(t.keys(g.invalidAttrs),t.keys(f)).length)return g.invalidAttrs}}}function c(e,i,n){i.associatedViews?i.associatedViews.push(e):i.associatedViews=[e],t.extend(i,h(e,n))}function u(e,i){i&&e.associatedViews&&1<e.associatedViews.length?e.associatedViews=t.without(e.associatedViews,i):(delete e.validate,delete e.preValidate,delete e.isValid,delete e.associatedViews)}function d(t){c(this.view,t,this.options)}function p(t){u(t)}var f,g=l.callbacks={valid:function(t,e,i){t.$("["+i+'~="'+e+'"]').removeClass("invalid").removeAttr("data-error")},invalid:function(t,e,i,n){t.$("["+n+'~="'+e+'"]').addClass("invalid").attr("data-error",i)}},m=l.patterns={digits:/^\d+$/,number:/^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/,email:/^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))$/i,url:/^(https?|ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i},v=l.messages={required:"{0} is required",acceptance:"{0} must be accepted",min:"{0} must be greater than or equal to {1}",max:"{0} must be less than or equal to {1}",range:"{0} must be between {1} and {2}",length:"{0} must be {1} characters",minLength:"{0} must be at least {1} characters",maxLength:"{0} must be at most {1} characters",rangeLength:"{0} must be between {1} and {2} characters",oneOf:"{0} must be one of: {1}",equalTo:"{0} must be the same as {1}",digits:"{0} must only contain digits",number:"{0} must be a number",email:"{0} must be a valid email",url:"{0} must be a valid url",inlinePattern:"{0} is invalid"},_=l.labelFormatters={none:function(t){return t},sentenceCase:function(t){return t.replace(/(?:^\w|[A-Z]|\b\w)/g,(function(t,e){return 0===e?t.toUpperCase():" "+t.toLowerCase()})).replace(/_/g," ")},label:function(t,e){return e.labels&&e.labels[t]||_.sentenceCase(t,e)}},b=l.attributeLoaders={inputNames:function(t){var e=[];return t&&t.$("form [name]").each((function(){/^(?:input|select|textarea)$/i.test(this.nodeName)&&this.name&&"submit"!==this.type&&-1===e.indexOf(this.name)&&e.push(this.name)})),e}},y=l.validators=(f=String.prototype.trim?function(t){return null===t?"":String.prototype.trim.call(t)}:function(t){return null===t?"":t.toString().replace(/^\s+/,"").replace(/\s+$/,"")},{fn:function(e,i,n,s,o){return(n=t.isString(n)?s[n]:n).call(s,e,i,o)},required:function(e,i,n,s,o){return!(!(o=t.isFunction(n)?n.call(s,e,i,o):n)&&!x(e))&&(o&&!x(e)?this.format(v.required,this.formatLabel(i,s)):void 0)},acceptance:function(e,i,n,s){if("true"!==e&&(!t.isBoolean(e)||!1===e))return this.format(v.acceptance,this.formatLabel(i,s))},min:function(t,e,i,n){if(!w(t)||t<i)return this.format(v.min,this.formatLabel(e,n),i)},max:function(t,e,i,n){if(!w(t)||i<t)return this.format(v.max,this.formatLabel(e,n),i)},range:function(t,e,i,n){if(!w(t)||t<i[0]||t>i[1])return this.format(v.range,this.formatLabel(e,n),i[0],i[1])},length:function(e,i,n,s){if(!t.isString(e)||e.length!==n)return this.format(v.length,this.formatLabel(i,s),n)},minLength:function(e,i,n,s){if(!t.isString(e)||e.length<n)return this.format(v.minLength,this.formatLabel(i,s),n)},maxLength:function(e,i,n,s){if(!t.isString(e)||e.length>n)return this.format(v.maxLength,this.formatLabel(i,s),n)},rangeLength:function(e,i,n,s){if(!t.isString(e)||e.length<n[0]||e.length>n[1])return this.format(v.rangeLength,this.formatLabel(i,s),n[0],n[1])},oneOf:function(e,i,n,s){if(!t.include(n,e))return this.format(v.oneOf,this.formatLabel(i,s),n.join(", "))},equalTo:function(t,e,i,n,s){if(t!==s[i])return this.format(v.equalTo,this.formatLabel(e,n),this.formatLabel(i,n))},pattern:function(t,e,i,n){if(!x(t)||!t.toString().match(m[i]||i))return this.format(v[i]||v.inlinePattern,this.formatLabel(e,n),i)}});function w(e){return t.isNumber(e)||t.isString(e)&&e.match(m.number)}function x(e){return!(t.isNull(e)||t.isUndefined(e)||t.isString(e)&&""===f(e)||t.isArray(e)&&t.isEmpty(e))}return t.each(y,(function(e,i){y[i]=t.bind(y[i],t.extend({},r,y))})),l}(_),function(t,e){"use strict";function i(i){return t.isNumber(i)||t.isString(i)&&i.match(e.Validation.patterns.number)}var n,s=window.Resources.Shared,o=t.str.format,r={lessThanAttr:function(t,e){return t<e},greaterThanAttr:function(t,e){return e<t},lessThanEqualAttr:function(t,e){return t<=e},greaterThanEqualAttr:function(t,e){return e<=t}};for(n in e.Validation.configure({selector:"data-bindto",labelFormatter:"label"}),e.Validation.bind=t.wrap(e.Validation.bind,(function(e){e.apply(this,t.toArray(arguments).slice(1)),e=arguments[1];var i=t.extend({},{selector:"data-bindto"},arguments[2]||{});e.$el.on("focusin focusout","textarea["+i.selector+"],input["+i.selector+"],.tox-tinymce["+i.selector+"]",(function(t){$(t.target).prev("span.tooltip.invalid")["focusin"===t.type?"addClass":"removeClass"]("focused")}))})),e.Validation.unbind=t.wrap(e.Validation.unbind,(function(e){e.apply(this,t.toArray(arguments).slice(1)),e=arguments[1];var i=t.extend({},{selector:"data-bindto"},arguments[2]||{});e.$el.off("focusin focusout","textarea["+i.selector+"],input["+i.selector+"],.tox-tinymce["+i.selector+"]"),e.$el.find(".invalid").removeClass("invalid").prev("span.tooltip").remove()})),t.extend(e.Validation.callbacks,{valid:function(t,e,i){i=(t=t.$("["+i+"~="+e+"]")).prev("span.tooltip.invalid"),t.removeClass("invalid"),i.remove()},invalid:function(t,e,i,n){var s;n=(t=t.$("["+n+"~="+e+"]")).filter("input,textarea,.tox-tinymce"),e=$(n.prev("span.tooltip.invalid")[0]||'<span class="tooltip invalid"></span>'),s=e,n.each((function(t,e){s.css($(e).position())})),t.addClass("invalid"),n.before(e.text(i))}}),t.extend(e.Validation.messages,{required:s.ValidationRequired,acceptance:s.ValidationAccept,min:s.ValidationMin,max:s.ValidationMax,range:s.ValidationRange,length:s.ValidationLength,minLength:s.ValidationMinLength,maxLength:s.ValidationMaxLength,rangeLength:s.ValidationRangeLength,oneOf:s.ValidationOneOf,equalTo:s.ValidationEqualTo,pattern:s.ValidationPattern,object:s.ValidationObject,email:s.ValidationEmail,phoneNumber:s.ValidationPhoneNumber,nan:s.ValidationNan,number:s.ValidationNumber,badrelation:s.ValidationBadRelation,lessThanAttr:s.ValidationLessThan,greaterThanAttr:s.ValidationGreaterThan,lessThanEqualAttr:s.ValidationMax,greaterThanEqualAttr:s.ValidationMin}),r)n=function(n){var s={};return s[n]=function(s,a,l,h,c){var u=n,d=c[a]||h.get(a),p=c[l]||h.get(l);return i(d)?i(p)?(c.validatingRelation||((c={})[l]=p,c[a]=d,c.validatingRelation=!0,t.defer(t.bind(h._validate,h),c,{})),h=parseFloat(d),c=parseFloat(p),r[u](h,c)?void 0:o(e.Validation.messages[u],a,l)):o(e.Validation.messages.badrelation,a):o(e.Validation.messages.number,a)},s}(n),t.extend(e.Validation.validators,n);t.extend(e.Validation.validators,{keyed:function(i,n,s,o,r){var a=o.get(s.keyfield);if(s=function(i,n){if(n)return t.isFunction(n)?n:t.isString(n)?i[n]:(t.isArray(n)||(n=[n]),t.reduce(n,(function(i,n){return t.each(t.without(t.keys(n),"msg"),(function(t){i.push({fn:e.Validation.validators[t],val:n[t],msg:n.msg})})),i}),[]))}(o,s[a]),s)return t.isFunction(s)?s.call(o,i,n,r):t.reduce(s,(function(t,s){var a=s.fn.call(e.Validation.validators,i,n,s.val,o,r);return!1!==a&&!1!==t&&(a&&!t?s.msg||a:t)}),"")},triggerOther:function(e,i,n,s,o){var r={};t.isArray(n)||(n=[n]);for(var a=0;a<n.length;a++)r[n[a]]=s.get(n[a]);t.defer(t.bind(s._validate,s),r,{})},maxLength:function(i,n,s){if(!t.str.isBlank(i)&&t.str.trim(i).length>s)return o(e.Validation.messages.maxLength,n,s)},number:function(t,e,i){if(Boolean(isNumber(t))!==i)return format(messages[i?"number":"nan"],e)},phoneNumber:function(t,i){if(!"(?(d{3}))?[- ]?(d{3})[- ]?(d{4})$".test(t))return e.Validation.messages.phoneNumber}})}(_,Backbone),function(t){t.fn.UItoTop=function(e){function i(){var e=t(this).scrollTop();void 0===document.body.style.maxHeight&&t(s).css({position:"absolute",top:t(this).scrollTop()+t(this).height()}),t(s,r).css({top:t(this).offset().top+t(this).height()-22}),e>n.min?t(s,r).fadeIn(n.inDelay):t(s,r).fadeOut(n.Outdelay)}var n=t.extend({min:300,inDelay:600,outDelay:400,containerID:"toTop",containerHoverID:"toTopHover",scrollSpeed:1200,easingType:"swing"},e),s="."+n.containerID,o="."+n.containerHoverID,r=this;t(s).hide(),t(s,r).off().on("click",(function(){r.animate({scrollTop:0},n.scrollSpeed,n.easingType),t("."+n.containerHoverID,this).stop().animate({opacity:0},n.inDelay,n.easingType)})).hover((function(){t(o,this).stop().animate({opacity:1},600,"linear")}),(function(){t(o,this).stop().animate({opacity:0},700,"linear")})),this.off("scroll",i).on("scroll",i)}}(jQuery),String.prototype.Trim=TrimString,String.prototype.Replace=stringReplace,CartesianPointObject.prototype.toString=CartesianPointObject_ToString;var LIBRARY_CARTESIANPOINT_LOADED=!0;function GeometryEngine(){this.distanceBetweenPoints=GeometryEngine__DistanceBetweenPoints,this.midpointBetweenPoints=GeometryEngine__MidpointBetweenPoints,this.slope=GeometryEngine__CalculateSlopeBetweenPoints,this.type="JYGeometryCalculation Library",this.typeName="JYGeometryCalculation Library",this.classVersion=parseFloat(1.1)}function GeometryEngine__DistanceBetweenPoints(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)+Math.pow(e.z-t.z,2))}function GeometryEngine__MidpointBetweenPoints(t,e){return new CartesianPointObject((e.x+t.x)/2,(e.y+t.y)/2,(e.z+t.z)/2)}function GeometryEngine__CalculateSlopeBetweenPoints(t,e){return t.x==e.x?null:(e.y-t.y)/(e.x-t.x)}function GeometryEngine__GetAngleRelativeToXAxis(t,e){}GeometryEngine.prototype.getMidpointBetweenPoints=GeometryEngine__MidpointBetweenPoints,GeometryEngine.prototype.getDistanceBetweenPoints=GeometryEngine__DistanceBetweenPoints,GeometryEngine.prototype.getSlopeFromPoints=GeometryEngine__CalculateSlopeBetweenPoints;var LIBRARY_GEOMETRY_LOADED=!0;function SVGWireSegmentCollectionEngine(t,e){this.svgDocument=t,this.wireSegmentFragments=new Array,this.wireSegments=new Array,this.circuitTypes=new Array,this.type="JYSVGWireSegmentCollectionEngine",this.variableName=e,this.getMaxWireSegmentWidth=SVGWireSegmentCollectionEngine_GetMaxWireSegmentWidth,this.getWireSegment=SVGWireSegmentCollectionEngine_GetWireSegmentObject,this.addWireSegment=SVGWireSegmentCollectionEngine_AddWireSegmentObject,this.addWireSegmentFragment=SVGWireSegmentCollectionEngine_AddWireSegmentFragmentObject,this.addCircuitType=SVGWireSegmentCollectionEngine_AddCircuitTypeObject,this.classVersion=parseFloat(1.01)}function SVGWireSegmentCollectionEngine_AddWireSegmentObject(t){this.wireSegments[this.wireSegments.length]=t}function SVGWireSegmentCollectionEngine_AddWireSegmentFragmentObject(t){this.wireSegmentFragments[this.wireSegmentFragments.length]=t}function SVGWireSegmentCollectionEngine_AddCircuitTypeObject(t){this.circuitTypes[this.circuitTypes.length]=t}function SVGWireSegmentCollectionEngine_GetWireSegmentObject(t){for(var e=this.wireSegments.length,i=0;i<e;i++)if(this.wireSegments[i].id==t)return this.wireSegments[i];return null}function SVGWireSegmentCollectionEngine_GetMaxWireSegmentWidth(){var t,e=0,i=this.wireSegments.length,n=0;for(e=0;e<i;e++)n<(t=this.wireSegments[e].width)&&(n=t);return n}function SVGWireSegmentCollectionEngine_BuildWireSegmentObjects(t){var e,i,n,s,o,r,a=0,l=0,h=t.wireSegments.length,c=t.wireSegmentFragments.length,u=new GeometryEngine;for(l=0;l<c;l++)for(i=t.wireSegmentFragments[l],a=0;a<h;a++)n=2*(e=t.wireSegments[a]).mainGraphic.width,s=t.wireSegments[a].mainGraphic.points[0],o=i.points[0],r=i.points[i.points.length-1],(u.distanceBetweenPoints(s,o)<=n||u.distanceBetweenPoints(s,r)<=n)&&(e.addGraphic(i),t.wireSegmentFragments[l]=null);SVGWireSegmentCollectionEngine_CompressArray(t.wireSegmentFragments),t.wireSegmentFragments.length}function SVGWireSegmentCollectionEngine_ConvertSVGListToArray(t){var e=new Array,i=t.length;if(t)for(x=0;x<i;x++)e[x]=lstGroups.item(x);return e}function SVGWireSegmentCollectionEngine_CompressArray(t){for(var e=0;e<t.length;)null==t[e]?t.splice(e,1):e++}function SVGWireSegmentCollectionEngine_GetDwg2SvgMitchellWiring(t){var e=null;if(null==t||""==t)if(svgElement)try{e=svgElement.getElementById("layer_GRAPHICS")}catch(t){e=svgDocument.getElementById("layer_GRAPHICS")}else e=this.svgDocument.getElementById("layer_GRAPHICS");else e=this.svgDocument.getElementById(t);var i=new Array,n=(i[i.length]=2.5,i[i.length]=1.65,new Array);if(n[n.length]=.85,n[n.length]=.625,n[n.length]=.65,n[n.length]=.63,e){var s,o,r,a,l,h,c,u=null,d=null,p=0,f=!1;if(e&&0<(u=e.getElementsByTagName("path")).length)for(a=0;a<u.length;a++)for(o=(d=u.item(a)).getAttribute("id")+"",s=d.getAttribute("data-wid"),0==o.length&&d.setAttribute("id","JY"+(p+=1)),r=new SVGWireSegmentGraphic(d,nSVG_MARKUP_SOURCE_DWG2SVGPRO),l=0;l<i.length;l++){if(r.width==i[l]){f=!0,(c=new SVGWireSegmentObject(r)).wid=s,(c.collectionEngine=this).wireSegments[this.wireSegments.length]=c;break}if(!f)for(h=0;h<n.length;h++)if(r.width==n[h]){this.wireSegmentFragments[this.wireSegmentFragments.length]=r;break}f=!1}SVGWireSegmentCollectionEngine_BuildWireSegmentObjects(this)}}function SVGWireSegmentCircuitTypeObject(t,e,i){this.name=t,this.color=e,this.id=null==i?"":i,this.type="JYSVGCircuitTypeObject",this.classVersion=parseFloat(1)}function SVGWireSegmentCircuitTypeObject_ToString(){return this.name+":"+this.color}SVGWireSegmentCollectionEngine.prototype.getMitchellWiringDWG2SVG=SVGWireSegmentCollectionEngine_GetDwg2SvgMitchellWiring,SVGWireSegmentCircuitTypeObject.prototype.toString=SVGWireSegmentCircuitTypeObject_ToString;var sSVGWireSegmentGraphicObject_UNIQUEIDPREFIX="JMY_",nSVGWireSegmentGraphicObject_UNIQUEID=0,nSVG_MARKUP_SOURCE_UNKNOWN=0,nSVG_MARKUP_SOURCE_SAVAGE=1,nSVG_MARKUP_SOURCE_SPX=2,nSVG_MARKUP_SOURCE_HONDA_ILLUSTRATOR=3,nSVG_MARKUP_SOURCE_MACKTRUCKS_SPX=4,nSVG_MARKUP_SOURCE_DWG2SVGPRO=5,nSVG_MARKUP_SOURCE_VOLVOTRUCKS_SPX=6;function SVGWireSegmentGraphic(t,e){null==e&&(e=nSVG_MARKUP_SOURCE_DWG2SVGPRO),"line"==t.nodeName&&(t=SVGWireSegmentGraphic__ConvertLineElementToPathElement(t)),this.id="",this.graphicElement=t,this.width=null,this.isVisible=null,this.points=new Array,this.markupSourceNumber=e,this.wid="",this.type="JYSVGWireSegmentGraphicObject",this.hide=SVGWireSegmentGraphic__HideGraphic,this.show=SVGWireSegmentGraphic__ShowGraphic,this.classVersion=parseFloat(1.3),SVGWireSegmentGraphic__SetIdForObject(this),SVGWireSegmentGraphic__SetWidth(this),SVGWireSegmentGraphic_SetPathPoints(this),this.show()}function SVGWireSegmentGraphic__SetWidth(t){var e=t.graphicElement.parentNode.getAttributeNS(null,"id")+"";0==e.indexOf("Lwpline_")||-1==e.indexOf("POLYLINE_")?t.width=SVGWireSegmentGraphic__SetWidthForLwpline(t):t.width=SVGWireSegmentGraphic__SetWidthForPOLYLINE(t)}function SVGWireSegmentGraphic__SetWidthForLwpline(t){try{var e=SVGWireSegmentGraphic__GetGraphicStyleProperty(t.graphicElement,"stroke-width"),i=parseFloat(e),n=t.graphicElement.parentNode}catch(t){}for(;isNaN(i)&&n;)e=SVGWireSegmentGraphic__GetGraphicStyleProperty(n,"stroke-width"),i=parseFloat(e),n=n.parentNode;return isNaN(i)?0:i}function SVGWireSegmentGraphic__SetWidthForPOLYLINE(t){var e,i=ParsePath(t.graphicElement.getAttributeNS(null,"d")+""),n=Number.MAX_VALUE,s=null,o=0;if(i.length<=1)return 0;for(s=i[o],o=1;o<i.length;o++)e=i[o],nDistance=s.distanceTo(e),nDistance=Math.round(1e4*nDistance)/1e4,n=Math.min(n,nDistance),s=e;return n==Number.MAX_VALUE?0:n}function SVGWireSegmentGraphic__ShowGraphic(){SVGWireSegmentGraphic__SetPresentationAttribute(this.graphicElement,"display","inline"),SVGWireSegmentGraphic__SetPresentationAttribute(this.graphicElement,"visibility","visible"),this.isVisible=!0}function SVGWireSegmentGraphic__HideGraphic(){SVGWireSegmentGraphic__SetPresentationAttribute(this.graphicElement,"display","none"),SVGWireSegmentGraphic__SetPresentationAttribute(this.graphicElement,"visibility","hidden"),this.isVisible=!1}function SVGWireSegmentGraphic__GetGraphicStyleProperty(t,e){var i;try{i=t.getAttributeNS(null,e)}catch(t){i=""}if(""==i)try{i=document.defaultView.getComputedStyle(document.getElementById(t.Id),null).getPropertyValue(e)}catch(t){i="1"}return""==i&&alert("Error: Cannot determine property value of Wire Segement Graphic object"),i}function SVGWireSegmentGraphic__SetPresentationAttribute(t,e,i){t.setAttributeNS(null,e,i)}function SVGWireSegmentGraphic__SetIdForObject(t){var e=t.graphicElement.getAttributeNS(null,"id")+"";t.id=e&&null!=e?e:sSVGWireSegmentGraphicObject_UNIQUEIDPREFIX+nSVGWireSegmentGraphicObject_UNIQUEID+++""}function SVGWireSegmentGraphic_SetPathPoints(t){var e=t.graphicElement.getAttributeNS(null,"d").Trim();t.points=ParsePath(e)}function ParsePath(t){for(var e,i=new Array,n=new Array,s=new Array,o=0,r=(t=t.Trim()).length,a="";o<r;)XisInSetY(e=t.charAt(o),"MLVHQASCZ",!0)?(""!=a&&(s.push(parseFloat(a)),a=""),i.push(e)):XisInSetY(e,", ")?""!=a&&(s.push(parseFloat(a)),a=""):a+=e,o++;""!=a&&(s.push(parseFloat(a)),a=""),i.reverse(),s.reverse();for(var l,h,c=0;c<i.length;){switch((sPenMode=i.pop()).toUpperCase()){case"M":case"L":l=s.pop(),h=s.pop(),n.push(new CartesianPointObject(l,h,0));break;case"V":l=n[n.length-1].x,h=s.pop(),n.push(new CartesianPointObject(l,h,0));break;case"H":l=s.pop(),h=n[n.length-1].y,n.push(new CartesianPointObject(l,h,0));break;case"C":s.pop(),s.pop(),s.pop(),s.pop(),s.pop(),s.pop();break;case"S":case"Q":case"A":s.pop(),s.pop(),s.pop(),s.pop();break;case"T":s.pop(),s.pop()}c++}return n}function XisInSetY(t,e,i){return i&&(t=t.toUpperCase(),e=e.toUpperCase()),""==t||t==e||-1<e.indexOf(t)}function SVGWireSegmentGraphic__ConvertLineElementToPathElement(t){var e="M"+t.getAttribute("x1")+" "+t.getAttribute("y1")+" L"+t.getAttribute("x2")+" "+t.getAttribute("y2"),i=t.getStyle().getPropertyValue("stroke-width");return SVGWireSegmentGraphic__CreatePathElement(e,"",t.getStyle().getPropertyValue("stroke"),i,"1.0")}function SVGWireSegmentGraphic__CreatePathElement(t,e,i,n,s){var o=elePathToFollow.ownerDocument.createElementNS(sSVGNS_SVGWIRESEGMENTANIMATION,"path");return o.setAttributeNS(null,"style",e),o.setAttributeNS(null,"stroke",i),o.setAttributeNS(null,"stroke-width",n),o.setAttributeNS(null,"opacity",s),o.setAttributeNS(null,"d",t),o}var sSVGNS="http://www.w3.org/2000/svg",sTEMP_WIRE_OBJECT_ID_PREFIX="JMY",sMOUSETRIGGER_PATHID_SUFFIX="_MOUSEING",sHIGHLIGHTING_PATHID_SUFFIX="_HIGHLIGHTING",sDEFAULT_MOUSEOVER_COLOR="blue",nDEFAULT_MOUSEOVER_OPACITY=.85,sDEFAULT_WIRE_HIGHLIGHTS_COLOR="yellow",nDEFAULT_WIRE_HIGHLIGHTING_OPACITY=.67,nDEFAULT_ALL_HIGHLIGHTING_WIDTH=.25,sDEFAULT_ALL_HIGHLIGHTING_STYLE="fill:none;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;",nSVGWireSegmentHighlighter_UNIQUE_ID=Date.parse(new Date)/1e3+1;function SVGWireSegmentHighlighter(t,e,i){this.id=null==t||""==t?"highlighter_"+(Date.parse(new Date)/1e3+nSVGWireSegmentHighlighter_UNIQUE_ID++):t,this.name=this.id,this.wireCollection=i,this.color=sDEFAULT_WIRE_HIGHLIGHTS_COLOR,this.width=null,this.intensity=nDEFAULT_WIRE_HIGHLIGHTING_OPACITY,this.isEnabled=!0,this.isDisabled=!this.isEnabled,this.highlightsApplied=!1,this.variableName=e,this.mouseoverColor=sDEFAULT_MOUSEOVER_COLOR,this.mouseoverIntensity=nDEFAULT_MOUSEOVER_OPACITY,this.stylez=sDEFAULT_ALL_HIGHLIGHTING_STYLE,this.type="JYSVGWireSegmentHighlighter",this.applyHighlightingTo=SVGWireSegmentHighlightingEngine__ApplyHighlightingTo,this.setStylez=SVGWireSegmentHighlighter_SetStyleProperty,this.setHighlightingColor=SVGWireSegmentHighlighter_SetCurrentHighlightColor,this.setActiveHighlightColor=SVGWireSegmentHighlighter_SetActiveHighlightColor,this.setActiveIntensity=SVGWireSegmentHighlighter_SetActiveHighlightingOpacity,this.setActiveWidth=SVGWireSegmentHighlighter_SetActiveWidth,this.setActiveMouseoverColor=SVGWireSegmentHighlighter_SetActiveMouseHighlightColor,this.highlight=SVGWireSegmentHighlightingEngine__SHOW_HIGHLIGHTING,this.unhighlight=SVGWireSegmentHighlightingEngine__HIDE_HIGHLIGHTING,this.enable=SVGWireSegmentHighlighter_SetEnableHighlighter,this.disable=SVGWireSegmentHighlighter_SetDisableHighlighter,this.classVersion=parseFloat(1.3),this.setHighlightingColor(sDEFAULT_WIRE_HIGHLIGHTS_COLOR),this.setActiveWidth(10*i.getMaxWireSegmentWidth()),this.setActiveIntensity(nDEFAULT_WIRE_HIGHLIGHTING_OPACITY),this.setActiveMouseoverColor(sDEFAULT_MOUSEOVER_COLOR)}function SVGWireSegmentHighlightingEngine__ApplyHighlightingTo(t,e){var i,n,s=null,o=0,r=t.wireSegments.length;if(0<r){for(o=0;o<r;o++)(oWireSegment=t.wireSegments[o]).highlighter=this,oWireSegment.highlighterEngine=this,n=SVGWireSegmentHighlightingEngine__CreateWireHighlightPathElement(oWireSegment,oWireSegment.id+sHIGHLIGHTING_PATHID_SUFFIX),e?oWireSegment.mainGraphic.graphicElement.parentNode.insertBefore(n,oWireSegment.mainGraphic.graphicElement):0<(s=oWireSegment.mainGraphic.graphicElement.ownerDocument.documentElement.getElementsByTagName("rect")).length?(s=s.item(0),oWireSegment.mainGraphic.graphicElement.ownerDocument.documentElement.insertBefore(n,s.nextSibling)):oWireSegment.mainGraphic.graphicElement.ownerDocument.documentElement.insertBefore(n,oWireSegment.mainGraphic.graphicElement.ownerDocument.documentElement.firstChild),i=SVGWireSegmentHighlightingEngine__CreateTriggerMouseoverPathElement(oWireSegment,oWireSegment.id+sMOUSETRIGGER_PATHID_SUFFIX,oWireSegment.collectionEngine.variableName),oWireSegment.mainGraphic.graphicElement.ownerDocument.documentElement.appendChild(i),oWireSegment._highlightsPath=n,oWireSegment._triggerPath=i,SVGWireSegmentHighlightingEngine__INITIALIZE_HIGHLIGHT_STATE(oWireSegment);this.highlightsApplied=!0}this.wireCollection=t}function SVGWireSegmentHighlightingEngine__INITIALIZE_HIGHLIGHT_STATE(t){"inline"==t._highlightsPath.getAttribute("display")?t.isHighlighted=!0:(t.isHighlighted=!1,t._highlightsPath.setAttributeNS(null,"display","none"))}function SVGWireSegmentHighlighter_SetEnableDisableProperty(t,e){var i,n,s=0;if(t.wireCollection)for(i=t.wireCollection.wireSegments.length,s=0;s<i;s++)n=t.wireCollection.wireSegments[s],e?(n.isHighlighted&&n._highlightsPath.setAttributeNS(null,"display","inline"),n._triggerPath.setAttributeNS(null,"display","inline")):(n._highlightsPath.setAttributeNS(null,"display","none"),n._triggerPath.setAttributeNS(null,"display","none"));t.isEnabled=e,t.isDisabled=!this.isEnabled}function SVGWireSegmentHighlighter_SetEnableHighlighter(){SVGWireSegmentHighlighter_SetEnableDisableProperty(oMe=this,!0)}function SVGWireSegmentHighlighter_SetDisableHighlighter(){SVGWireSegmentHighlighter_SetEnableDisableProperty(oMe=this,!1)}function SVGWireSegmentHighlightingEngine__SHOW_HIGHLIGHTING(t,e){null==e||""==e?(t._highlightsPath.setAttributeNS(null,"stroke",this.color),t.highlightColor=this.color):(t._highlightsPath.setAttributeNS(null,"stroke",e),t.highlightColor=e),t._highlightsPath.setAttributeNS(null,"display","inline"),t.isHighlighted=!0}function SVGWireSegmentHighlightingEngine__HIDE_HIGHLIGHTING(t){t._highlightsPath.setAttributeNS(null,"display","none"),t.isHighlighted=!1}function SVGWireSegmentHighlighter_SetCurrentHighlightColor(t){this.color=null==t||""==t?sDEFAULT_WIRE_HIGHLIGHTS_COLOR:t}function SVGWireSegmentHighlighter_SetActiveHighlightColor(t){var e,i,n=null==t||""==t?sDEFAULT_WIRE_HIGHLIGHTS_COLOR:t;if(this.wireCollection&&this.highlightsApplied)for(e=this.wireCollection.wireSegments.length,i=0;i<e;i++)(oWireSegment=this.wireCollection.wireSegments[i])._highlightsPath.setAttributeNS(null,"stroke",n),oWireSegment.highlightColor=n;this.color=n}function SVGWireSegmentHighlighter_SetActiveMouseHighlightColor(t){var e,i,n=null==t||""==t?sDEFAULT_MOUSEOVER_COLOR:t;if(this.wireCollection&&this.highlightsApplied)for(e=this.wireCollection.wireSegments.length,i=0;i<e;i++)(oWireSegment=this.wireCollection.wireSegments[i])._triggerPath.setAttributeNS(null,"stroke",n);this.mouseoverColor=n}function SVGWireSegmentHighlighter_SetActiveWidth(t){if(!(null==t||t<=0)){if(nNewWidth=t,this.wireCollection&&this.highlightsApplied)for(var e=this.wireCollection.wireSegments.length,i=0;i<e;i++)(oWireSegment=this.wireCollection.wireSegments[i])._highlightsPath.setAttributeNS(null,"stroke-width",nNewWidth+""),oWireSegment._triggerPath.setAttributeNS(null,"stroke-width",nNewWidth+"");this.width=nNewWidth,this.mouseoverWidth=nNewWidth}}function SVGWireSegmentHighlighter_SetActiveHighlightingOpacity(t){var e,i,n=null==t||t<=0?nDEFAULT_WIRE_HIGHLIGHTING_OPACITY:t;if(this.wireCollection&&this.highlightsApplied)for(e=this.wireCollection.wireSegments.length,i=0;i<e;i++)(oWireSegment=this.wireCollection.wireSegments[i])._highlightsPath.setAttributeNS(null,"opacity",n+"");this.intensity=n}function SVGWireSegmentHighlighter_SetStyleProperty(t){return-1==(t=null==t||""==t?sDEFAULT_ALL_HIGHLIGHTING_STYLE:t).indexOf("fill:none")?"fill:none;"+t:t}function SVGWireSegmentHighlightingEngine__CreateTriggerMouseoverPathElement(t,e,i){var n=SVGWireSegmentHighlightingEngine__CreateGenericPathElement(t.mainGraphic.graphicElement,t.highlighter.stylez,t.highlighter.mouseoverColor,t.highlighter.width,"0.0");return t.collectionEngine.variableName,t.highlighter.variableName,n.setAttributeNS(null,"onmouseout","this.setAttributeNS(null,'opacity', '0.0'); false;"),n.setAttributeNS(null,"onmouseover","this.setAttributeNS(null,'opacity', '"+nDEFAULT_MOUSEOVER_OPACITY+"'); false;"),n.setAttributeNS(null,"id",e),n}function SVGWireSegmentHighlightingEngine__CreateWireHighlightPathElement(t,e){return(t=SVGWireSegmentHighlightingEngine__CreateGenericPathElement(t.mainGraphic.graphicElement,t.highlighter.stylez,t.highlighter.color,t.highlighter.width,t.highlighter.intensity)).setAttributeNS(null,"display","none"),t.setAttributeNS(null,"id",e),t}function SVGWireSegmentHighlightingEngine__CreateGenericPathElement(t,e,i,n,s){var o=t.ownerDocument.createElementNS(sSVGNS,"path");return o.setAttributeNS(null,"style",e),o.setAttributeNS(null,"stroke",i),o.setAttributeNS(null,"stroke-width",n),o.setAttributeNS(null,"opacity",s),"polyline"==t.nodeName?o.setAttributeNS(null,"d","M"+t.getAttribute("points")):o.setAttributeNS(null,"d",t.getAttribute("d")),o}function SVGWireSegmentHighlightingEngine__HighlightsClick(evt,sWireCollectionEngineVariableName,sHighlighterVariableName){var sClickedId=this.getAttribute("id"),sAssociatedWireId=sClickedId.substring(0,sClickedId.indexOf(sMOUSETRIGGER_PATHID_SUFFIX)),oWireCollectionEngineREF=eval(sWireCollectionEngineVariableName),oWireSegment=oWireCollectionEngineREF.getWireSegment(sAssociatedWireId),oHighlighterREF=eval(sHighlighterVariableName);return oWireSegment&&(oWireSegment.isHighlighted?oHighlighterREF.unhighlight(oWireSegment):oHighlighterREF.highlight(oWireSegment)),sAssociatedWireId}function SVGWireSegmentObject(t,e){this.id=null,this.graphics=new Array,this.isVisible=null,this.isHidden=null,this.mainGraphic=null,this.width=t.width,this.circuitType=e||new SVGWireSegmentCircuitTypeObject("generic","red"),this.type="JYSVGWireSegmentObject",this.addGraphic=SVGWireSegmentObject__AddSVGWireSegmentGraphic,this.show=SVGWireSegmentObject__ShowWire,this.hide=SVGWireSegmentObject__HideWire,this.shout=SVGWireSegmentObject___AlertShout,this.classVersion=parseFloat(1),this.addGraphic(t),this.isVisibile=!0,this.isHidden=!this.isVisibile}function SVGWireSegmentObject__INITIALIZE(t){}function SVGWireSegmentObject__AddSVGWireSegmentGraphic(t){this.graphics[this.graphics.length]=t,this.mainGraphic=SVGWireSegmentObject__SetIdForObject(this),this.width=SVGWireSegmentObject__GetTotalWidth(this)}function SVGWireSegmentObject__ShowWire(){SVGWireSegmentObject__SetVisibility(this.graphics,!0),this.isVisible=!0,this.isHidden=!1}function SVGWireSegmentObject__HideWire(){SVGWireSegmentObject__SetVisibility(this.graphics,!1),this.isVisible=!1,this.isHidden=!0}function SVGWireSegmentObject__SetVisibility(t,e){var i=t.length,n=0,s=null;for(n=0;n<i;n++)s=t[n],e?s.show():s.hide()}function SVGWireSegmentObject__GetWidestGraphicElement(t){var e=0,i=t.graphics.length,n=-99999,s=null;for(e=0;e<i;e++)t.graphics[e].width>n&&(n=t.graphics[e].width,s=t.graphics[e]);return s}function SVGWireSegmentObject__SetIdForObject(t){var e=SVGWireSegmentObject__GetWidestGraphicElement(t);return t.id=e.id,e}function SVGWireSegmentObject__GetTotalWidth(t){var e=0,i=t.graphics.length,n=0;for(e=0;e<i;e++)n+=t.graphics[e].width;return n}function SVGWireSegmentObject___AlertShout(){var t=0,e="---- Wire Segment Object ----",i=this.graphics.length;for(e=e+"id: "+this.id+"\nTotal graphics: "+i+"\n",t=0;t<i;t++)e+="   id = "+this.graphics[t].id+" :: width = "+this.graphics[t].width+"\n";e=(e=(e+="Visibility:"+this.isVisible+"\n")+"Hidden: "+this.isHidden+"\n")+"Main graphic: id = "+this.mainGraphic.id+" :: width = "+this.mainGraphic.width+"\n",this.width}