<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAgencyWebThemesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('agency_web_themes', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('agency_id')->unsigned()->index();
            $table->foreign('agency_id')->references('id')->on('agencies')->onDelete('cascade');

            $table->string("title")->default("DrCar")->nullable();
            $table->longText("description")->default("Repair Documentation Full Data.")->nullable();
            $table->string("color_main_1")->default("")->nullable();
            $table->string("theme_style")->default("")->nullable();
            $table->string("background_color")->default("")->nullable();
            $table->string("logo_url")->default("")->nullable();
            $table->string("background_url")->default("")->nullable();
            $table->string("favicon_url")->default("")->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('agency_web_themes');
    }
}
