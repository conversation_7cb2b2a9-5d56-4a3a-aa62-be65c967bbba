<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdminSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('admin_settings', function (Blueprint $table) {
            $table->id();

            $table->boolean('allow_renewal_extension')->default(0)->nullable();
            $table->string('version_ios')->nullable();
            $table->string('version_android')->nullable();
            $table->string('version_window')->nullable();
            $table->string('noti_new')->nullable();
            $table->string('app_id_paypal')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admin_settings');
    }
}
