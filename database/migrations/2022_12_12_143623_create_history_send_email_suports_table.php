<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHistorySendEmailSuportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('history_send_email_suports', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('email_support_id')->unsigned()->index();
            $table->foreign('email_support_id')->references('id')->on('email_supports')->onDelete('cascade');

            $table->integer("type")->nullable(); // 0 g<PERSON>i thủ công , 1 g<PERSON>i theo l<PERSON>ch, 2 gửi theo điều kiện

            $table->string("title")->nullable();
            $table->string("description")->nullable();

            $table->longText("content")->nullable();
            
            $table->integer("total_user_sent")->nullable(); 
            $table->integer("total_user")->nullable(); 
            $table->longText("list_email_json")->nullable();
            /// ///  /// ///

            $table->integer("schedule_type")->nullable();
            $table->timestamp('send_time')->nullable();
            $table->integer('condition_time_send')->nullable();

            $table->integer("group_user_id")->nullable();
            $table->integer("send_to_gate")->nullable(); //0 email , 1 notification, 2 cả 2
            $table->string("group_user_name")->nullable();
            $table->integer("send_to")->nullable();
            $table->integer("send_to_condition")->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('history_send_email_suports');
    }
}
