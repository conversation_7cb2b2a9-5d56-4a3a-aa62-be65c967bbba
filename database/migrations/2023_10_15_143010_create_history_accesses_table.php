<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHistoryAccessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('history_accesses', function (Blueprint $table) {
            $table->id();

            $table->integer('user_id')->nullable();
            $table->text("tail_link")->nullable();
            $table->string("service")->default("")->nullable();
            $table->string("ip")->default("")->nullable();
            $table->string("cookie_use")->default("")->nullable();
            $table->text("use_agent")->nullable();
            $table->text("headers")->nullable();
            $table->string("method")->default("GET")->nullable();
            $table->boolean("is_mobile")->default(false)->nullable();
            
            $table->index('tail_link');
            $table->index('method');
            $table->index('service');
            $table->index('updated_at');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('history_accesses');
    }
}
