<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailSupportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_supports', function (Blueprint $table) {
            $table->id();
            $table->integer("type")->nullable(); // 0 gửi test , 1 gửi theo lịch, 2 gửi theo group
            $table->string("name")->nullable();
            $table->string("title")->nullable();
            $table->string("description")->nullable();

            $table->longText("content")->nullable();


            $table->integer("schedule_type")->nullable();
            $table->timestamp('send_time')->nullable();
            $table->integer('condition_time_send')->nullable();

            $table->integer("group_user_id")->nullable();
            $table->string("group_user_name")->nullable();
            $table->integer("send_to_gate")->nullable(); //0 email , 1 notification, 2 cả 2
            $table->integer("send_to")->nullable();
            $table->integer("send_to_condition")->nullable();
            $table->boolean("running")->default(0)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_supports');
    }
}
