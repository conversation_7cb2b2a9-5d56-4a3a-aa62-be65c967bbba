<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHistoryStatusServer3sTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('history_status_server3s', function (Blueprint $table) {
            $table->id();

            $table->integer('status')->default(2)->nullable(); // 1 error 2 ok
            $table->string('service')->nullable();
            $table->string('fixer')->nullable(); //thủ công , bằng server bên kia
            
            $table->longText('note')->nullable();  //thông tin thêm

            $table->longText('json_info')->nullable();  //thông tin thêm

            

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('history_status_server3s');
    }
}
