<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWebDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('web_data', function (Blueprint $table) {
            $table->id();

            $table->text("tail_link")->default("")->nullable();
            $table->string("language")->default("")->nullable();
            $table->string("service")->default("")->nullable();
            $table->string("method")->default("GET")->nullable();
            $table->longText("content")->default("")->nullable();
            $table->string("content_type")->default("")->nullable();
            $table->integer("status_code")->nullable();

            $table->index('tail_link');
            $table->index('language');
            $table->index('method');
            $table->index('service');
            $table->index('content_type');
            $table->index('updated_at');


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('web_data');
    }
}
