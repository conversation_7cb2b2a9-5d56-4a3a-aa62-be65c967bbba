<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUToGroupMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('u_to_group_messages', function (Blueprint $table) {
            $table->string('id')->primary();

            $table->unsignedBigInteger('user_id')->unsigned()->index()->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->nullable();

            $table->unsignedBigInteger('to_group_id')->unsigned()->index()->nullable();
            $table->foreign('to_group_id')->references('id')->on('groups')->onDelete('cascade')->nullable();

            $table->longText('content')->nullable();;
            $table->longText('images_json')->nullable();

            $table->longText('chat_key')->nullable();



            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('u_to_group_messages');
    }
}
