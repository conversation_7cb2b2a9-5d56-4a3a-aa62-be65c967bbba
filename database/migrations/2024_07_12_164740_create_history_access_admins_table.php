<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHistoryAccessAdminsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('history_access_admins', function (Blueprint $table) {
            $table->id();

            $table->string("method")->nullable();
            $table->string("url")->nullable();
            $table->string("ip")->nullable();
            $table->string("user_agent")->nullable();
            $table->string("referrer")->nullable();
            $table->text("headers")->nullable();
            $table->text("data")->nullable();
            $table->integer("user_id")->nullable();
            $table->integer("agency_id")->nullable();
            $table->string("agency_code")->nullable();
            $table->string("status_code")->nullable();
            $table->double("execution_time")->nullable();
            $table->string("response")->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('history_access_admins');
    }
}
