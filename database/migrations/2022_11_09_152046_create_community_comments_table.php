<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCommunityCommentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('community_comments', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('community_post_id')->unsigned()->index();
            $table->foreign('community_post_id')->references('id')->on('community_posts')->onDelete('cascade');

            $table->unsignedBigInteger('user_id')->unsigned()->index()->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->nullable();

            $table->longText("images_json")->nullable();

            $table->integer("status")->default(1)->nullable(); // trạng thái 1 chờ duyệt, 0 đã duyệt, 2 đã ẩn

            $table->longText("content")->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('community_comments');
    }
}
