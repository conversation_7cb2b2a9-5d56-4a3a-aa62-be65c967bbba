<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRenewalHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('renewal_histories', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('user_id')->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');


            $table->integer('month')->default(12)->nullable();


            $table->string('base_service', 255)->nullable();
            $table->string('service', 255)->nullable();
            $table->double('price')->default(0)->nullable();
            $table->double('paid')->default(0)->nullable();

            $table->timestamp('before_expiration_date')->nullable();
            $table->timestamp('after_expiration_date')->nullable();

            $table->integer('extension_type')->default(0)->nullable();

            $table->integer('extender_user_id')->nullable();

            $table->integer("references_id")->nullable();
            $table->string("references_value", 255)->nullable();

            $table->unique(["references_value", 'service'], "uni2")->nullable();

            $table->string("product_id")->nullable();
            $table->string("pay_from")->nullable();
            $table->longText("json_data")->nullable();

            $table->boolean('paid_for_admin')->default(0)->nullable();
            $table->integer("device_login_id")->nullable();

            $table->double('net_price')->default(0)->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('renewal_histories');
    }
}
