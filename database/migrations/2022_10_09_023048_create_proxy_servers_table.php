<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProxyServersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('proxy_servers', function (Blueprint $table) {
            $table->id();

            $table->string('name')->nullable(); 

            $table->boolean('is_use')->default(0)->nullable();
            $table->string('service')->nullable(); //ALLDATAEU //ALLDATAUS //AUTODATA

            $table->longText('cookies_str')->nullable();
            $table->longText('proxy')->nullable();

            $table->string('user_name')->nullable();
            $table->string('user_id')->nullable();

            $table->string('email')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('site_name')->nullable();
            

            $table->boolean('is_ok')->nullable();
            

            $table->longText('error_mess')->nullable();
            $table->longText('note')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('proxy_servers');
    }
}
