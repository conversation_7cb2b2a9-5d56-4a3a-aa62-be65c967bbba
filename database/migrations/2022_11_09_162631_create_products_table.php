<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();

            $table->string("name")->nullable();
            $table->string("name_str_filter")->nullable();

            $table->double("before_price")->default(0)->nullable();
            $table->double("price")->default(0)->nullable();

            $table->string("link")->nullable();
            $table->string("image_url")->nullable();
            $table->integer('status')->default('0')->nullable(); //0 hiện, 1 ẩn

            $table->boolean('is_show_home')->default('0')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_sells');
    }
}
