<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGroupUserConditionItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('group_user_condition_items', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('group_user_id')->unsigned()->index();
            $table->foreign('group_user_id')->references('id')->on('group_users')->onDelete('cascade');


            $table->string("compare_field")->default("")->nullable();
            $table->string("compare_expression")->default("")->nullable();
            $table->string("compare_value")->default("")->nullable();
            $table->string("compare_merge_condition")->default("")->nullable();



            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('group_user_condition_items');
    }
}
