<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDeviceLoginsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('device_logins', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('user_id')->unsigned()->index()->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->nullable();

            
            $table->string("is_main")->nullable();
            $table->string("ip_using")->nullable();
            $table->string("address")->nullable();

            $table->string("device_id")->nullable();
            $table->string("model_name")->nullable();
            $table->string("app_version")->nullable();

            $table->string("platform")->nullable();
            $table->timestamp('last_visit_time')->nullable();
            $table->timestamp('expiry_use')->nullable();
            $table->timestamp('login_time')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('device_logins');
    }
}
