<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('toyota_models', function (Blueprint $table) {
            $table->id();

            $table->string("division");
            $table->integer("year");
            $table->string("model_id");
            $table->string("model_name")->nullable();
            $table->string("rm_id")->nullable();
            $table->string("wd_id")->nullable();

            $table->unique(["division", 'year', 'model_id']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('toyota_models');
    }
};
