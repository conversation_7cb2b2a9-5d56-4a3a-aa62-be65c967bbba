<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBlockedIpsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('blocked_ips', function (Blueprint $table) {
            $table->id();

            $table->integer('user_id')->nullable();
            $table->text("tail_link")->default("")->nullable();

            $table->string("ip")->default("")->nullable();
            $table->text("headers")->default("")->nullable();
            $table->text("form_data")->default("")->nullable();
            $table->string("method")->default("GET")->nullable();

            $table->index('ip');
            $table->index('tail_link');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('blocked_ips');
    }
}
