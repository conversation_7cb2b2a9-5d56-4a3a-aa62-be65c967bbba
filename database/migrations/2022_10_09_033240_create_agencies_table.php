<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAgenciesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('agencies', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('user_id')->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->string("agency_code")->unique();
            $table->string("sub_domain")->unique();
            $table->string("domain")->nullable();
            $table->string("name")->nullable();
            $table->boolean("allow_renew")->nullable();
            $table->longText("note")->nullable();


            $table->boolean("hidden_price")->default(true)->nullable();
            $table->boolean("allow_alldata")->default(true)->nullable();
            $table->boolean("allow_autodata")->default(true)->nullable();
            $table->boolean("allow_haynespro")->default(true)->nullable();
            $table->boolean('allow_haynespro_truck')->default(true)->nullable();
            $table->boolean('allow_identifix')->default(true)->nullable();
            $table->boolean('allow_mitchell_repair_center')->default(true)->nullable();
            $table->boolean('allow_partslink24')->default(true)->nullable();
            $table->boolean('allow_kdsgds')->default(true)->nullable();
            $table->boolean('allow_etka')->default(true)->nullable();
            $table->boolean('allow_tecdoc')->default(true)->nullable();
            $table->boolean('allow_ford_pts')->default(true)->nullable();

            $table->boolean('enable_customer_add_device')->default(true)->nullable();

            $table->boolean('not_back_home_web_agency')->default(true)->nullable();


            $table->string("autodata_language")->nullable();
            $table->longText("json_list_cookie_manager")->nullable();


            $table->double('price_12_month_device')->default(50)->nullable();

            $table->double('price_package')->default(0)->nullable();
            $table->double('price_1_month')->default(0)->nullable();
            $table->double('price_6_month')->default(0)->nullable();
            $table->double('price_12_month')->default(0)->nullable();

            $table->string("package_list")->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('agencies');
    }
}
