<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrderPlansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_plans', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('user_id')->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->string("order_code")->unique();

            $table->integer('month')->default(12)->nullable();
            $table->longText('service')->nullable();
            $table->longText('product_id')->nullable();
            $table->integer('plan_id')->default(0)->nullable();
            

            $table->integer('price')->default(100)->nullable();
            $table->string("payment_partner")->nullable();
            $table->integer('status')->default(0)->nullable();

            $table->integer('device_login_id')->nullable();
            
            $table->longText('plan_ids_json')->nullable();
            $table->longText('plans_json')->nullable();
            $table->string('code_voucher')->nullable();
            $table->double('before_price')->default(0)->nullable();
            $table->double('net_price')->default(0)->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_plans');
    }
}
