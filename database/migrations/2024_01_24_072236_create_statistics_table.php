<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStatisticsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('statistics', function (Blueprint $table) {
            $table->id();

            $table->integer('total_request')->default(0)->nullable();
            $table->integer('total_request_has_cache')->default(0)->nullable();
            $table->integer('total_request_not_get')->default(0)->nullable();

            $table->integer('total_alldata_eu_request')->default(0)->nullable();
            $table->integer('total_alldata_eu_request_has_cache')->default(0)->nullable();
            $table->integer('total_alldata_eu_request_not_get')->default(0)->nullable();

            $table->integer('total_alldata_us_request')->default(0)->nullable();
            $table->integer('total_alldata_us_request_has_cache')->default(0)->nullable();
            $table->integer('total_alldata_us_request_not_get')->default(0)->nullable();

            $table->integer('total_haynes_pro_request')->default(0)->nullable();
            $table->integer('total_haynes_pro_request_has_cache')->default(0)->nullable();
            $table->integer('total_haynes_pro_not_get')->default(0)->nullable();

            $table->integer('total_haynes_pro_truck_request')->default(0)->nullable();
            $table->integer('total_haynes_pro_truck_request_has_cache')->default(0)->nullable();
            $table->integer('total_haynes_pro_truck_request_not_get')->default(0)->nullable();

            $table->integer('total_identifix_request')->default(0)->nullable();
            $table->integer('total_identifix_request_has_cache')->default(0)->nullable();
            $table->integer('total_identifix_request_not_get')->default(0)->nullable();

            $table->integer('total_mitchell_prodemand_request')->default(0)->nullable();
            $table->integer('total_mitchell_prodemand_request_has_cache')->default(0)->nullable();
            $table->integer('total_mitchell_prodemand_request_not_get')->default(0)->nullable();

            $table->integer('total_partslink_request')->default(0)->nullable();
            $table->integer('total_partslink_request_has_cache')->default(0)->nullable();
            $table->integer('total_partslink_request_not_get')->default(0)->nullable();

            $table->integer('total_kdsgds_request')->default(0)->nullable();
            $table->integer('total_kdsgds_request_has_cache')->default(0)->nullable();
            $table->integer('total_kdsgds_request_not_get')->default(0)->nullable();

            $table->integer('total_etka_request')->default(0)->nullable();
            $table->integer('total_etka_request_has_cache')->default(0)->nullable();
            $table->integer('total_etka_request_not_get')->default(0)->nullable();

            $table->integer('total_tecdoc_request')->default(0)->nullable();
            $table->integer('total_tecdoc_request_has_cache')->default(0)->nullable();
            $table->integer('total_tecdoc_request_not_get')->default(0)->nullable();

            $table->integer('total_ford_pts_request')->default(0)->nullable();
            $table->integer('total_ford_pts_request_has_cache')->default(0)->nullable();
            $table->integer('total_ford_pts_request_not_get')->default(0)->nullable();

            $table->integer('total_toyota_tis_request')->default(0)->nullable();
            $table->integer('total_toyota_tis_request_has_cache')->default(0)->nullable();
            $table->integer('total_toyota_tis_request_not_get')->default(0)->nullable();
            

            $table->date('day_statistic')->nullable();


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('statistics');
    }
}
