<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCookieRotatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cookie_rotates', function (Blueprint $table) {
            $table->id();

            $table->string("service")->nullable();
            $table->string("run_times")->nullable();
            $table->boolean('enable')->default('1')->nullable();

            $table->string("proxy")->nullable();
            $table->string("note")->nullable();
            $table->integer("position")->default(1)->nullable(); 
            $table->longText("text_cookies")->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cookie_rotates');
    }
}
