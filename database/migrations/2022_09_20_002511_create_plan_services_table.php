<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlanServicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('plan_services', function (Blueprint $table) {
            $table->id();

            
            $table->boolean('pay_in_link')->default(0)->nullable();
            
            $table->integer('month')->default(12)->nullable();
            $table->longText('service')->nullable();
            $table->integer('price')->default(100)->nullable();
            $table->string('product_id')->default("")->nullable();
            $table->boolean('public')->default(0)->nullable();
            $table->string('apply')->default("APP")->nullable();


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('plan_services');
    }
}
