<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('js_logs', function (Blueprint $table) {
            $table->id();

            $table->integer('user_id')->nullable();
            $table->text("user_email")->nullable();
            $table->text("domain")->nullable();
            $table->string("ip")->default("")->nullable();
            $table->string("token_use")->default("")->nullable();
            $table->string("cookie_use")->default("")->nullable();
            $table->text("use_agent")->nullable();
            $table->text("headers")->nullable();
            $table->text('tail_link')->nullable();
            $table->text("full_link")->nullable();
          
      
            $table->index('updated_at');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('js_logs');
    }
};
