<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUToAdminMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('u_to_admin_messages', function (Blueprint $table) {
            $table->string('id')->primary();

            $table->integer('user_id')->nullable();

            $table->boolean('is_admin')->default(0)->nullable();

            $table->longText('content')->nullable();;
            $table->longText('images_json')->nullable();

            $table->longText('chat_key')->nullable();

            $table->boolean('seen')->default(0)->nullable();
            $table->boolean('is_remove')->default(0)->nullable();

            $table->string("ip_using")->nullable();
            $table->string("platform")->nullable();
            $table->string("device_id")->nullable();
            $table->string("model")->nullable();


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('u_to_admin_messages');
    }
}
