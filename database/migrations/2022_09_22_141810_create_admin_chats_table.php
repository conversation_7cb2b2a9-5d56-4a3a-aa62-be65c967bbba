<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdminChatsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('admin_chats', function (Blueprint $table) {
            $table->id();

            $table->integer('user_id')->nullable();

            $table->boolean('is_admin')->default(0)->nullable();

            $table->longText("last_mess")->nullable(); //html

            $table->boolean('seen')->default(0)->nullable();
            

            $table->string("ip_using")->nullable();
            $table->string("platform")->nullable();
            $table->string("device_id")->nullable();
            $table->string("model")->nullable();

            $table->unique(['device_id']);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admin_chats');
    }
}
