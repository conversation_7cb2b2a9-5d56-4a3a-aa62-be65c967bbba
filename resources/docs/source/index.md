---
title: API Reference

language_tabs:
- bash
- javascript

includes:

search: true

toc_footers:
- <a href='http://github.com/mpociot/documentarian'>Documentation Powered by Documentarian</a>
---
<!-- START_INFO -->
# Info

Welcome to the generated API reference.
[Get Postman Collection](http://localhost/docs/collection.json)

<!-- END_INFO -->

#Admin/Header


<!-- START_82af40d0d006046716e95b5296af65f2 -->
## Cập nhật thông tin Admin phần header

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/admin/info_admin" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"phone_number_1":"et","phone_number_2":"sit","stk_1":"et","name_tk_1":"explicabo","name_bank_1":"vero","stk_2":"explicabo","name_tk_2":"aspernatur","name_bank_2":"esse"}'

```

```javascript
const url = new URL(
    "http://localhost/api/admin/info_admin"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "phone_number_1": "et",
    "phone_number_2": "sit",
    "stk_1": "et",
    "name_tk_1": "explicabo",
    "name_bank_1": "vero",
    "stk_2": "explicabo",
    "name_tk_2": "aspernatur",
    "name_bank_2": "esse"
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`PUT api/admin/info_admin`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `phone_number_1` | required |  optional  | Số điện thoại 1
        `phone_number_2` | required |  optional  | Số điện thoại 2
        `stk_1` | required |  optional  | Số tài khoản 1
        `name_tk_1` | required |  optional  | Tên tài khoản bank 1
        `name_bank_1` | required |  optional  | Tên ngân hàng 1
        `stk_2` | required |  optional  | Số tài khoản 2
        `name_tk_2` | required |  optional  | Tên tài khoản bank 2
        `name_bank_2` | required |  optional  | Tên ngân hàng 2
    
<!-- END_82af40d0d006046716e95b5296af65f2 -->

#Admin/Quản lý bài đăng


<!-- START_a3b6ea8a46acccfac1318fa6d1e6d5c8 -->
## Thêm Cần mua cần bán

> Example request:

```bash
curl -X POST \
    "http://localhost/api/admin/posts" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"is_buy":"velit","name":"repudiandae","quantity":"dolorem","unit":"distinctio","price":"aut","status":"omnis","images":"qui"}'

```

```javascript
const url = new URL(
    "http://localhost/api/admin/posts"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "is_buy": "velit",
    "name": "repudiandae",
    "quantity": "dolorem",
    "unit": "distinctio",
    "price": "aut",
    "status": "omnis",
    "images": "qui"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST api/admin/posts`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `is_buy` | required |  optional  | Mua hay bán
        `name` | required |  optional  | tên sản phẩm
        `quantity` | required |  optional  | số lượng
        `unit` | required |  optional  | đơn vị
        `price` | required |  optional  | giá
        `status` | required |  optional  | (0 đang chờ xử lý, 1 đã duyệt, 2 đã ẩn)
        `images` | required |  optional  | List danh sách ảnh sp (VD: ["linl1", "link2"])
    
<!-- END_a3b6ea8a46acccfac1318fa6d1e6d5c8 -->

<!-- START_f19a06b0a2433879a0f9646a64f4ee41 -->
## Danh sách Cần mua cần bán

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/admin/posts?user_id=qui&is_buy=unde&search=ex" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "http://localhost/api/admin/posts"
);

let params = {
    "user_id": "qui",
    "is_buy": "unde",
    "search": "ex",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "code": 401,
    "msg_code": "NO_TOKEN",
    "msg": "Chưa đăng nhập bạn không có quyền truy cập",
    "success": false
}
```

### HTTP Request
`GET api/admin/posts`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `status` |  optional  | integer required trạng thái 0 chờ duyệt, 1 đã duyệt, 2 đã ẩn
#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `user_id` |  required  | Nếu là user thì tự động lấy ds thuộc user, còn admin thì truyền lên
    `is_buy` |  required  | Mua hay bán
    `search` |  required  | Tìm tên sản phẩm

<!-- END_f19a06b0a2433879a0f9646a64f4ee41 -->

<!-- START_7ac0140da7e1adff237ea390f3fd62ad -->
## Cập nhật Cần mua cần bán

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/admin/posts/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"name":"dignissimos","quantity":"quia","unit":"id","price":"consectetur","status":"libero","images":"sit"}'

```

```javascript
const url = new URL(
    "http://localhost/api/admin/posts/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "name": "dignissimos",
    "quantity": "quia",
    "unit": "id",
    "price": "consectetur",
    "status": "libero",
    "images": "sit"
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`PUT api/admin/posts/{post_id}`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `name` | required |  optional  | tên sản phẩm
        `quantity` | required |  optional  | số lượng
        `unit` | required |  optional  | đơn vị
        `price` | required |  optional  | giá
        `status` | required |  optional  | (0 đang chờ xử lý, 1 đã duyệt, 2 đã ẩn)
        `images` | required |  optional  | List danh sách ảnh sp (VD: ["linl1", "link2"])
    
<!-- END_7ac0140da7e1adff237ea390f3fd62ad -->

<!-- START_3cb5a2c028b63a177f4666124e1d76dd -->
## Xóa Cần mua cần bán

> Example request:

```bash
curl -X DELETE \
    "http://localhost/api/admin/posts/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "http://localhost/api/admin/posts/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`DELETE api/admin/posts/{post_id}`


<!-- END_3cb5a2c028b63a177f4666124e1d76dd -->

#Admin/Quản lý user


<!-- START_1fdf5c126c9b5b722e5044c3f680bf8e -->
## Danh sách user

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/admin/users" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"search":"quia"}'

```

```javascript
const url = new URL(
    "http://localhost/api/admin/users"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "search": "quia"
}

fetch(url, {
    method: "GET",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "code": 401,
    "msg_code": "NO_TOKEN",
    "msg": "Chưa đăng nhập bạn không có quyền truy cập",
    "success": false
}
```

### HTTP Request
`GET api/admin/users`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `search` | required |  optional  | Tìm theo tên hoặc sdt
    
<!-- END_1fdf5c126c9b5b722e5044c3f680bf8e -->

<!-- START_949939327bb740a28e52669f60a6e192 -->
## Cập nhật thông tin profile

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/admin/users/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"name":"dolore","avatar_image":18,"phone_number":"repellendus","store_name":"animi","email":"praesentium","password":"minima","province":5,"district":19,"wards":13,"sex":11,"address_detail":3}'

```

```javascript
const url = new URL(
    "http://localhost/api/admin/users/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "name": "dolore",
    "avatar_image": 18,
    "phone_number": "repellendus",
    "store_name": "animi",
    "email": "praesentium",
    "password": "minima",
    "province": 5,
    "district": 19,
    "wards": 13,
    "sex": 11,
    "address_detail": 3
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`PUT api/admin/users/{user_id}`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `name` | String |  optional  | Họ và tên
        `avatar_image` | integer |  required  | anh dai dien
        `phone_number` | string |  required  | Số điện thoại
        `store_name` | string |  required  | Ten cua hang
        `email` | string |  required  | Email
        `password` | string |  required  | Password
        `province` | integer |  required  | id province
        `district` | integer |  required  | id district
        `wards` | integer |  required  | id wards
        `sex` | integer |  required  | gioi tinh
        `address_detail` | integer |  required  | dia chi chi tiet
    
<!-- END_949939327bb740a28e52669f60a6e192 -->

<!-- START_9f20996e73f4d2dc513ea315bb4a41ef -->
## Xóa user

> Example request:

```bash
curl -X DELETE \
    "http://localhost/api/admin/users/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"search":"consequatur"}'

```

```javascript
const url = new URL(
    "http://localhost/api/admin/users/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "search": "consequatur"
}

fetch(url, {
    method: "DELETE",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`DELETE api/admin/users/{user_id}`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `search` | required |  optional  | Tìm theo tên hoặc sdt
    
<!-- END_9f20996e73f4d2dc513ea315bb4a41ef -->

#Admin/Đăng nhập


<!-- START_e9aa8e9cecac4d07efa45f1b2d470efb -->
## Login

> Example request:

```bash
curl -X POST \
    "http://localhost/api/admin/login" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"phone_number":"eum","password":"fugiat"}'

```

```javascript
const url = new URL(
    "http://localhost/api/admin/login"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "phone_number": "eum",
    "password": "fugiat"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST api/admin/login`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `phone_number` | string |  required  | Số điện thoại
        `password` | string |  required  | Password
    
<!-- END_e9aa8e9cecac4d07efa45f1b2d470efb -->

#User/Badges


<!-- START_9525acf6d3c4eff58e4dcb4cb45544e3 -->
## Badges

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/user/badges" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "http://localhost/api/user/badges"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "code": 200,
    "success": true,
    "msg_code": "THÀNH CÔNG",
    "msg": "THÀNH CÔNG",
    "data": {
        "phone_number_1": "**********",
        "phone_number_2": "**********",
        "stk_1": "***********",
        "name_tk_1": "HONG AONG",
        "name_bank_1": "BIDV",
        "stk_2": "*********",
        "name_tk_2": "HONG PHI",
        "name_bank_2": "TECHCOMBAK"
    }
}
```

### HTTP Request
`GET api/user/badges`


<!-- END_9525acf6d3c4eff58e4dcb4cb45544e3 -->

#User/Cần mua cần bán


<!-- START_bd942aa1d8744b3f0faf05e9e1f77468 -->
## Thêm Cần mua cần bán

> Example request:

```bash
curl -X POST \
    "http://localhost/api/user/posts" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"is_buy":"sapiente","name":"recusandae","quantity":"recusandae","unit":"minus","price":"id","status":"tempora","images":"maxime"}'

```

```javascript
const url = new URL(
    "http://localhost/api/user/posts"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "is_buy": "sapiente",
    "name": "recusandae",
    "quantity": "recusandae",
    "unit": "minus",
    "price": "id",
    "status": "tempora",
    "images": "maxime"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST api/user/posts`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `is_buy` | required |  optional  | Mua hay bán
        `name` | required |  optional  | tên sản phẩm
        `quantity` | required |  optional  | số lượng
        `unit` | required |  optional  | đơn vị
        `price` | required |  optional  | giá
        `status` | required |  optional  | (0 đang chờ xử lý, 1 đã duyệt, 2 đã ẩn)
        `images` | required |  optional  | List danh sách ảnh sp (VD: ["linl1", "link2"])
    
<!-- END_bd942aa1d8744b3f0faf05e9e1f77468 -->

<!-- START_45be296c3457ed9372892dc88fbcb373 -->
## Danh sách Cần mua cần bán

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/user/posts?user_id=tenetur&is_buy=quaerat&search=a" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "http://localhost/api/user/posts"
);

let params = {
    "user_id": "tenetur",
    "is_buy": "quaerat",
    "search": "a",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "code": 200,
    "success": true,
    "msg_code": "THÀNH CÔNG",
    "msg": "THÀNH CÔNG",
    "data": {
        "current_page": 1,
        "data": [],
        "first_page_url": "http:\/\/localhost\/api\/user\/posts?page=1",
        "from": null,
        "last_page": 1,
        "last_page_url": "http:\/\/localhost\/api\/user\/posts?page=1",
        "links": [
            {
                "url": null,
                "label": "&laquo; Previous",
                "active": false
            },
            {
                "url": "http:\/\/localhost\/api\/user\/posts?page=1",
                "label": "1",
                "active": true
            },
            {
                "url": null,
                "label": "Next &raquo;",
                "active": false
            }
        ],
        "next_page_url": null,
        "path": "http:\/\/localhost\/api\/user\/posts",
        "per_page": 30,
        "prev_page_url": null,
        "to": null,
        "total": 0
    }
}
```

### HTTP Request
`GET api/user/posts`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `status` |  optional  | integer required trạng thái 0 chờ duyệt, 1 đã duyệt, 2 đã ẩn
#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `user_id` |  required  | Nếu là user thì tự động lấy ds thuộc user, còn admin thì truyền lên
    `is_buy` |  required  | Mua hay bán
    `search` |  required  | Tìm tên sản phẩm

<!-- END_45be296c3457ed9372892dc88fbcb373 -->

<!-- START_f3ecef5d9a9a1ff81769c2738a4305a1 -->
## Cập nhật Cần mua cần bán

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/user/posts/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"name":"placeat","quantity":"nobis","unit":"ut","price":"pariatur","status":"labore","images":"repudiandae"}'

```

```javascript
const url = new URL(
    "http://localhost/api/user/posts/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "name": "placeat",
    "quantity": "nobis",
    "unit": "ut",
    "price": "pariatur",
    "status": "labore",
    "images": "repudiandae"
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`PUT api/user/posts/{post_id}`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `name` | required |  optional  | tên sản phẩm
        `quantity` | required |  optional  | số lượng
        `unit` | required |  optional  | đơn vị
        `price` | required |  optional  | giá
        `status` | required |  optional  | (0 đang chờ xử lý, 1 đã duyệt, 2 đã ẩn)
        `images` | required |  optional  | List danh sách ảnh sp (VD: ["linl1", "link2"])
    
<!-- END_f3ecef5d9a9a1ff81769c2738a4305a1 -->

<!-- START_656c7ce37b1078b79a8b5a319804e60a -->
## Xóa Cần mua cần bán

> Example request:

```bash
curl -X DELETE \
    "http://localhost/api/user/posts/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "http://localhost/api/user/posts/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`DELETE api/user/posts/{post_id}`


<!-- END_656c7ce37b1078b79a8b5a319804e60a -->

#User/Đăng ký


<!-- START_638687f1aca2f1e69b360d1516c7c1f9 -->
## Register

> Example request:

```bash
curl -X POST \
    "http://localhost/api/user/register" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"name":"voluptatem","phone_number":"cumque","store_name":"aut","email":"consequatur","password":"aut","province":18,"district":18,"wards":15,"sex":7,"avatar_image":6,"address_detail":2}'

```

```javascript
const url = new URL(
    "http://localhost/api/user/register"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "name": "voluptatem",
    "phone_number": "cumque",
    "store_name": "aut",
    "email": "consequatur",
    "password": "aut",
    "province": 18,
    "district": 18,
    "wards": 15,
    "sex": 7,
    "avatar_image": 6,
    "address_detail": 2
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST api/user/register`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `name` | string |  required  | Ten
        `phone_number` | string |  required  | Số điện thoại
        `store_name` | string |  required  | Ten cua hang
        `email` | string |  required  | Email
        `password` | string |  required  | Password
        `province` | integer |  required  | id province
        `district` | integer |  required  | id district
        `wards` | integer |  required  | id wards
        `sex` | integer |  required  | gioi tinh
        `avatar_image` | integer |  required  | anh dai dien
        `address_detail` | integer |  required  | dia chi chi tiet
    
<!-- END_638687f1aca2f1e69b360d1516c7c1f9 -->

#User/Đăng nhập


<!-- START_57e3b4272508c324659e49ba5758c70f -->
## Login

> Example request:

```bash
curl -X POST \
    "http://localhost/api/user/login" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"email_or_phone_number":"natus","password":"ea"}'

```

```javascript
const url = new URL(
    "http://localhost/api/user/login"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "email_or_phone_number": "natus",
    "password": "ea"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST api/user/login`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `email_or_phone_number` | string |  required  | (Username, email hoặc số điện thoại)
        `password` | string |  required  | Password
    
<!-- END_57e3b4272508c324659e49ba5758c70f -->

#Ussr/Thông tin cá nhân


<!-- START_a4251b7143964e3f7d9fb181a7b86ba5 -->
## Tạo Lấy thông tin profile

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/user/profile" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "http://localhost/api/user/profile"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (401):

```json
{
    "code": 401,
    "msg_code": "NO_TOKEN",
    "msg": "Chưa đăng nhập bạn không có quyền truy cập",
    "success": false
}
```

### HTTP Request
`GET api/user/profile`


<!-- END_a4251b7143964e3f7d9fb181a7b86ba5 -->

<!-- START_2a644d07e3a86fa75c0077fa54cc05b6 -->
## Cập nhật thông tin profile

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/user/profile" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"name":"ratione","avatar_image":13,"phone_number":"nam","store_name":"veniam","email":"aspernatur","password":"molestiae","province":2,"district":17,"wards":12,"sex":2,"address_detail":9}'

```

```javascript
const url = new URL(
    "http://localhost/api/user/profile"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "name": "ratione",
    "avatar_image": 13,
    "phone_number": "nam",
    "store_name": "veniam",
    "email": "aspernatur",
    "password": "molestiae",
    "province": 2,
    "district": 17,
    "wards": 12,
    "sex": 2,
    "address_detail": 9
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`PUT api/user/profile`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `name` | String |  optional  | Họ và tên
        `avatar_image` | integer |  required  | anh dai dien
        `phone_number` | string |  required  | Số điện thoại
        `store_name` | string |  required  | Ten cua hang
        `email` | string |  required  | Email
        `password` | string |  required  | Password
        `province` | integer |  required  | id province
        `district` | integer |  required  | id district
        `wards` | integer |  required  | id wards
        `sex` | integer |  required  | gioi tinh
        `address_detail` | integer |  required  | dia chi chi tiet
    
<!-- END_2a644d07e3a86fa75c0077fa54cc05b6 -->

#general


<!-- START_4dfafe7f87ec132be3c8990dd1fa9078 -->
## Return an empty response simply to trigger the storage of the CSRF cookie in the browser.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/sanctum/csrf-cookie" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json"
```

```javascript
const url = new URL(
    "http://localhost/sanctum/csrf-cookie"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`GET sanctum/csrf-cookie`


<!-- END_4dfafe7f87ec132be3c8990dd1fa9078 -->


