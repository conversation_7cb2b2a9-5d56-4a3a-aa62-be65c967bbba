.show-fake-browser {
    background-color: #f7f7fa;
    background-color: var(--rs-bg-well);
    border: 1px solid #e5e5ea;
    border: 1px solid var(--rs-border-primary);
    border-radius: 4px;
    -webkit-box-shadow: 0 0.1em 0.5em 0 rgb(0 0 0 / 28%);
    box-shadow: 0 0.1em 0.5em 0 rgb(0 0 0 / 28%);
    margin: 10px;
}

.show-fake-browser.login-page .rs-content {
    height: auto;
    /* margin: 20px;
    padding: 20px; */
}

 .buttonPlan:focus, .buttonPlan:hover {
    background-color: #103178;
    color:white;
    box-shadow: 0 0 0 0.1875rem white, 0 0 0 0.375rem #103178;
}
.buttonPlanSelected {
    background-color: #103178 !important;
    color:white !important;
    box-shadow: 0 0 0 0.1875rem white, 0 0 0 0.375rem #103178 !important;
}

.buttonPlan {

    
    width: 100%;
    margin: 0.2rem;
    padding: 0.45rem 0.5rem;
    border: none;
    border-radius: 0.1875rem;
    background: white;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.2);
    /* background-color: tomato; */
    background-color: transparent;
    /* color: black; */
    font-family: inherit;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.2rem;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 150ms ease-out;
}