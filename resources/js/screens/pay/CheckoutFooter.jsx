import React, { useState } from "react";
import { Button, Modal, Input, Loader } from "rsuite";

import { planRenewStore } from "../../store/planRenewStore";
import { alerts } from "../../utils/alerts";

const CheckoutFooter = (props) => {
    const plansSelected = props.plansSelected;
    const [open, setOpen] = useState(false);
    const [voucher, setVoucher] = useState("");

    const voucherApply = props.voucherApply;
    const setVoucherApply = props.setVoucherApply;

    const loadingCheckVoucher = planRenewStore(
        (state) => state.loadingCheckVoucher
    );
    const checkVoucher = planRenewStore((state) => state.checkVoucher);

    const handleCheckout = () => {
        props.openClickCheckout();
    };

    const handleApplyVoucher = () => {
        checkVoucher(voucher, () => {
            setVoucherApply(voucher);
            alerts.success("Voucher applied");
            props.onAppliedVoucher(voucher);
            setOpen(false);
        });
    };

    function convertToDollar(price, fixed = 2) {
        return removeTrailingZeros(price.toFixed(fixed));
    }

    function removeTrailingZeros(value) {
        return value.replace(/\.?0+$/, "");
    }

    function getTotalPrice() {
        let price = 0;
        plansSelected.forEach((element) => {
            price += element.price;
        });
        return convertToDollar(price);
    }

    return (
        <>
            {/* Footer cố định */}
            <div
                style={{
                    position: "fixed",
                    bottom: 0,
                    left: 0,
                    width: "100%",
                    background: "#fff",
                    borderTop: "1px solid #ddd",
                    boxShadow: "0 -2px 5px rgba(0, 0, 0, 0.1)",
                    zIndex: 1000,
                }}
            >
                <div
                    style={{
                        maxWidth: "800px",
                        margin: "0 auto",
                        display: "flex",
                        flexDirection: "column",
                        gap: "10px",
                        padding: "10px 20px",
                    }}
                >
                    {/* Danh sách dịch vụ đã mua */}

                    <div style={{ fontSize: "14px", color: "#555" }}>
                        <strong>Cart: </strong>
                        {plansSelected.map((service, index) => (
                            <span key={index} style={{ listStyleType: "disc" }}>
                                {service.name + ", "}
                            </span>
                        ))}
                    </div>

                    {/* Icon Voucher và Text Button */}
                    <div
                        style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                        }}
                    >
                        <div
                            style={{
                                display: "flex",
                                alignItems: "center",
                                gap: "10px",
                            }}
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width={24}
                                height={24}
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            >
                                {" "}
                                <path d="M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z" />{" "}
                                <path d="M7 16l3 -3l3 3" />{" "}
                                <path d="M8 13c-.789 0 -2 -.672 -2 -1.5s.711 -1.5 1.5 -1.5c1.128 -.02 2.077 1.17 2.5 3c.423 -1.83 1.372 -3.02 2.5 -3c.789 0 1.5 .672 1.5 1.5s-1.211 1.5 -2 1.5h-4z" />{" "}
                            </svg>
                            <Button
                                appearance="link"
                                onClick={() => setOpen(true)}
                                style={{ padding: 0 }}
                            >
                                {voucherApply != null && voucherApply != ""
                                    ? voucherApply
                                    : "Add voucher code"}
                            </Button>
                        </div>

                        {/* Hiển thị tổng tiền */}
                        <div
                            style={{
                                fontSize: "16px",
                                fontWeight: "bold",
                                color: "#333",
                            }}
                        >
                            Total:{" "}
                            <span style={{ color: "#007bff" }}>
                                {getTotalPrice()}$
                            </span>
                        </div>
                    </div>

                    <Button
                        disabled={plansSelected.length == 0}
                        appearance="primary"
                        color="blue"
                        size="lg"
                        onClick={handleCheckout}
                    >
                        Checkout
                    </Button>
                </div>
            </div>

            {/* Modal nhập voucher */}
            <Modal
                style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                }}
                backdrop="static"
                open={open}
                onClose={() => setOpen(false)}
                size="xs"
            >
                <Modal.Header>
                    <Modal.Title>Add voucher code</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Input
                        placeholder="Enter voucher code"
                        value={voucher}
                        onChange={(value) => setVoucher(value.toUpperCase())}
                    />
                </Modal.Body>

                {loadingCheckVoucher ? (
                    <Modal.Footer>
                        <Loader />
                    </Modal.Footer>
                ) : (
                    <Modal.Footer>
                        <Button
                            appearance="primary"
                            onClick={handleApplyVoucher}
                        >
                            Apply
                        </Button>
                        <Button
                            onClick={() => setOpen(false)}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                    </Modal.Footer>
                )}
            </Modal>
        </>
    );
};

export default CheckoutFooter;
