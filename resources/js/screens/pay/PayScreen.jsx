import React, { useEffect, useState } from "react";
import {
    Panel,
    Breadcrumb,
    Grid,
    Content,
    Stack,
    Divider,
    Avatar,
    VStack,
    Row,
    Col,
    Button,
    Container,
} from "rsuite";

import ReadyRoundIcon from "@rsuite/icons/ReadyRound";

import { RiHome4Line } from "react-icons/ri";
import { useMediaQuery, Placeholder } from "rsuite";
import { planRenewStore } from "../../store/planRenewStore";
import EleExpiry from "../../components/EleExpiry";
import { useMainStore } from "../../store/mainStore";
import CheckoutFooter from "./CheckoutFooter";
import MethodPayModal from "./MethodPayModal";

function PayScreen(props) {
    const getPlanServices = planRenewStore((state) => state.getPlanServices);
    const changePageType = useMainStore((state) => state.changePageType);
    const planServices = planRenewStore((state) => state.planServices);
    const [voucherApply, setVoucherApply] = useState("");
    const loadingPlanServices = planRenewStore(
        (state) => state.loadingPlanServices
    );
    const setPlansSelected = planRenewStore((state) => state.setPlansSelected);
    const plansSelected = planRenewStore((state) => state.plansSelected);
    const init = () => {
        getPlanServices(null, voucherApply);
    };
    const selectPlan = planRenewStore((state) => state.selectPlan);

    const [openMethodPay, setOpenMethodPay] = useState(false);

    useEffect(() => {
        init();
    }, []);

    const agentTheme = useMainStore((state) => state.agentTheme);

    const buildItemPlanTick = (itemPlan, isSelected, onClick) => (
        <Col xs={8}>
            <div
                className={
                    isSelected
                        ? "rs-radio-tile rs-radio-tile-checked"
                        : "rs-radio-tile"
                }
                style={{
                    padding: "0px 25px",
                    paddingTop:5,
                    paddingBottom:5,
                }}
                onClick={onClick}
            >
                <VStack
                    spacing={0}
                    style={{
                        padding: 0,
                        maxWidth: 125,
                        justifyItems: "center",
                        alignItems: "center",
                    }}
                >
                    <p>
                        {" "}
                        <b>{itemPlan.month ?? 0} month</b>
                    </p>

                    <Stack style={{ display: "flex" }} spacing={5}>
                        {itemPlan.discount != null && (
                            <del> ${itemPlan.discount.last_price}</del>
                        )}
                        <span> ${itemPlan.price}</span>
                    </Stack>

                    <p
                        style={{
                            fontSize: 12,
                        }}
                    >
                        {" "}
                        ($
                        {(itemPlan.price / itemPlan.month).toFixed(0)}
                        /month)
                    </p>
                </VStack>

                <div class="rs-radio-tile-mark">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 16 16"
                        fill="currentColor"
                        aria-hidden="true"
                        focusable="false"
                        class="rs-radio-tile-mark-icon rs-icon"
                        aria-label="check"
                        data-category="application"
                    >
                        <path d="M14.136 3.157a.5.5 0 0 1 .783.616l-.056.071-8.5 9a.5.5 0 0 1-.665.056l-.07-.064-4.5-5a.5.5 0 0 1 .677-.73l.066.061 4.136 4.596 8.129-8.605z"></path>
                    </svg>
                </div>
            </div>
        </Col>
    );

    return (
        <div>
            <MethodPayModal
                open={openMethodPay}
                setOpenMethodPay={(open) => {
                    if (open == false) {
                        getPlanServices(null, voucherApply, false);
                    }
                    setOpenMethodPay(open);
                }}
                codeVoucher={voucherApply}
            />
            <Content
                style={{
                    maxWidth: "1000px",
                    margin: "0 auto",
                    padding: "20px",
                }}
            >
                <Grid fluid alignitems="center" justifycontent="center">
                    <Panel
                        header={
                            <>
                                <h3 className="title">Buy program</h3>{" "}
                                <Breadcrumb>
                                    <Breadcrumb.Item href="/">
                                        <RiHome4Line /> Home
                                    </Breadcrumb.Item>
                                    <Breadcrumb.Item active>
                                        Buy program
                                    </Breadcrumb.Item>
                                </Breadcrumb>
                            </>
                        }
                    >
                        {agentTheme.contact_link != null &&
                            agentTheme.contact_link != "" && (
                                <div style={{ marginBottom: 20 }}>
                                    <a>
                                        Contact us{" "}
                                        <a
                                            href={agentTheme.contact_link}
                                            target="_blank"
                                        >
                                            <img
                                                src={
                                                    "/assets/images/whatsapp.png"
                                                }
                                                className="rs-avatar-image"
                                                height={40}
                                            ></img>
                                        </a>{" "}
                                        to buy or renew the service.
                                    </a>
                                </div>
                            )}

                        {loadingPlanServices ? (
                            <Placeholder.Paragraph rows={4} />
                        ) : (
                            <div>
                                <Panel bordered>
                                    {planServices.map((planRenew) => (
                                        <div>
                                            <Row className="show-grid">
                                                {/* <Avatar src={"avatarUrl"} alt={planRenew.name} /> */}

                                                <Col xs={24} md={10}>
                                                    <Stack
                                                        spacing={2}
                                                        direction="column"
                                                        alignItems="flex-start"
                                                    >
                                                        <div>
                                                            {planRenew.name}
                                                        </div>
                                                        <EleExpiry
                                                            key={planRenew.name}
                                                            time={
                                                                planRenew.expiry
                                                            }
                                                        />
                                                    </Stack>
                                                </Col>

                                                <Col xs={24} md={14}>
                                                    <div>
                                                        <Row>
                                                            {planRenew.plans.map(
                                                                (itemPlan) => {
                                                                    return buildItemPlanTick(
                                                                        itemPlan,
                                                                        plansSelected.some(
                                                                            (
                                                                                item
                                                                            ) =>
                                                                                item.id ===
                                                                                itemPlan.id
                                                                        ),
                                                                        () => {
                                                                            selectPlan(
                                                                                itemPlan,
                                                                                planRenew.name
                                                                            );
                                                                        }
                                                                    );
                                                                }
                                                            )}
                                                        </Row>
                                                    </div>
                                                </Col>
                                            </Row>

                                            <Divider />
                                        </div>
                                    ))}
                                </Panel>

                                <Container style={{ height: 120 }} />

                                <CheckoutFooter
                                    plansSelected={plansSelected}
                                    voucherApply={voucherApply}
                                    setVoucherApply={setVoucherApply}
                                    onAppliedVoucher={(voucher) => {
                                        getPlanServices(null, voucher, false);
                                    }}
                                    openClickCheckout={() => {
                                        setOpenMethodPay(true);
                                    }}
                                />

                                {/* <Stack wrap spacing={8}>
                                    <RadioTile
                                        icon={<Icon as={VscFile} />}
                                        label="1 months
                                "
                                        value="blank"
                                    >
                                        <Stack>1 months</Stack>
                                    </RadioTile>

                                    <RadioTile
                                        icon={<Icon as={VscFile} />}
                                        label="1 months
                                "
                                        value="blank"
                                    >
                                        <Stack>1 months</Stack>
                                    </RadioTile>

                                    <RadioTile
                                        icon={<Icon as={VscFile} />}
                                        label="1 months
                                "
                                        value="blank"
                                    >
                                        <Stack>1 months</Stack>
                                    </RadioTile>
                                </Stack> */}
                            </div>
                        )}
                    </Panel>
                </Grid>
            </Content>
        </div>
    );
}

export default PayScreen;
