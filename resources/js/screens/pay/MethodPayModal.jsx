import React, { useEffect, useState } from "react";
import { <PERSON>ton, Modal, List, Avatar } from "rsuite";
import { planRenewStore } from "../../store/planRenewStore";
import { useBadgesStore } from "../../store/badgesStore";

const MethodPayModal = (props) => {
    const getPaymentMethods = planRenewStore(
        (state) => state.getPaymentMethods
    );
    const paymentMethods = planRenewStore((state) => state.paymentMethods);
    const plansSelected = planRenewStore((state) => state.plansSelected);
    const user = useBadgesStore((state) => state.user);

    useEffect(() => {
        if (props.open == true) {
            getPaymentMethods();
        }
    }, [props.open]);

    return (
        <>
            {/* Modal chọn phương thức thanh toán */}
            <Modal
                style={{
                    display: "flex",
                    alignItems: "center",
                }}
                open={props.open}
                onClose={() => props.setOpenMethodPay(false)}
                size="md"
            >
                <Modal.Header>
                    <Modal.Title>SELECT A PAYMENT METHOD</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <List>
                        {paymentMethods.map((method, index) => {
                            const idArrayAsText = plansSelected.map((obj) =>
                                obj.id.toString()
                            );
                            const idString = idArrayAsText.join(",");

                            var urlPayment = method.payment_url
                                .replace("{{email}}", user?.email)
                                .replace("{{plan_ids}}", idString);

                                urlPayment = urlPayment +
                                '&code_voucher=' +
                                (props.codeVoucher ?? "");
                            return (
                                <List.Item
                                    key={index}
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        padding: "10px",
                                        borderBottom: "1px solid #f0f0f0",
                                    }}
                                >
                                    <img
                                        width={120}
                                        height={80}
                                        src={method.image}
                                        alt={method.name}
                                        size="xl"
                                        style={{
                                            marginRight: "15px",
                                            objectFit: "contain",
                                        }}
                                    />
                                    <div style={{ flex: 1 }}>
                                        <strong>{method.name}</strong>
                                        <p style={{ margin: 0, color: "#888" }}>
                                            {method.description}
                                        </p>
                                    </div>
                                    <a
                                        href={urlPayment}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="rs-btn rs-btn-primary"
                                    >
                                        Pay
                                    </a>
                                </List.Item>
                            );
                        })}
                    </List>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        onClick={() => props.setOpenMethodPay(false)}
                        appearance="subtle"
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default MethodPayModal;
