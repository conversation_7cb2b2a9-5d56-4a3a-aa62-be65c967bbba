import React, { useEffect, useState } from "react";
import {
    Content,
    SelectPicker,
    Button,
    Panel,
    Nav,
    Message,
    useToaster
} from "rsuite";
import { useBadgesStore } from "../../store/badgesStore";

function Toyota(props) {
    const user = useBadgesStore((state) => state.user);
    const toaster = useToaster();

    // State for form selections
    const [selectedDivision, setSelectedDivision] = useState(null);
    const [selectedYear, setSelectedYear] = useState(null);
    const [selectedModel, setSelectedModel] = useState(null);
    const [activeTab, setActiveTab] = useState('repair');
    const [searchResults, setSearchResults] = useState(null);
    const [loading, setLoading] = useState(false);

    // Data for dropdowns
    const divisions = [
        { label: 'LEXUS', value: 'lexus' },
        { label: 'TOYOTA', value: 'toyota' },
        { label: 'SCION', value: 'scion' }
    ];

    // Generate years from 1990 to 2025
    const years = [];
    for (let year = 2025; year >= 1990; year--) {
        years.push({ label: year.toString(), value: year });
    }

    // Demo models data based on division
    const getModelsForDivision = (division) => {
        const modelData = {
            lexus: [
                { label: 'ES 350', value: 'es350' },
                { label: 'RX 350', value: 'rx350' },
                { label: 'IS 250', value: 'is250' },
                { label: 'GS 350', value: 'gs350' },
                { label: 'LS 460', value: 'ls460' },
                { label: 'NX 200t', value: 'nx200t' }
            ],
            toyota: [
                { label: 'Camry', value: 'camry' },
                { label: 'Corolla', value: 'corolla' },
                { label: 'RAV4', value: 'rav4' },
                { label: 'Highlander', value: 'highlander' },
                { label: 'Prius', value: 'prius' },
                { label: 'Tacoma', value: 'tacoma' },
                { label: 'Sienna', value: 'sienna' }
            ],
            scion: [
                { label: 'tC', value: 'tc' },
                { label: 'xB', value: 'xb' },
                { label: 'xD', value: 'xd' },
                { label: 'FR-S', value: 'frs' },
                { label: 'iQ', value: 'iq' }
            ]
        };
        return modelData[division] || [];
    };

    // Demo search results
    const demoRepairManuals = [
        { id: 1, title: 'Engine Repair Manual', description: 'Complete engine overhaul procedures', pages: 245 },
        { id: 2, title: 'Transmission Service Manual', description: 'Automatic transmission repair guide', pages: 189 },
        { id: 3, title: 'Brake System Manual', description: 'Brake system diagnosis and repair', pages: 156 },
        { id: 4, title: 'Electrical System Manual', description: 'Electrical troubleshooting guide', pages: 298 },
        { id: 5, title: 'Air Conditioning Manual', description: 'A/C system service procedures', pages: 134 }
    ];

    const demoWiringDiagrams = [
        { id: 1, title: 'Engine Control System', description: 'ECU and sensor wiring diagrams', circuits: 45 },
        { id: 2, title: 'Lighting System', description: 'Headlight and taillight wiring', circuits: 23 },
        { id: 3, title: 'Audio System', description: 'Radio and speaker wiring diagrams', circuits: 18 },
        { id: 4, title: 'Power Distribution', description: 'Fuse box and relay wiring', circuits: 67 },
        { id: 5, title: 'Body Control Module', description: 'BCM and related circuits', circuits: 34 }
    ];

    const handleSearch = () => {
        if (!selectedDivision || !selectedYear || !selectedModel) {
            toaster.push(
                <Message type="warning">
                    Please select Division, Year, and Model before searching
                </Message>,
                { placement: 'topCenter' }
            );
            return;
        }

        setLoading(true);

        // Simulate API call
        setTimeout(() => {
            setSearchResults({
                repairManuals: demoRepairManuals,
                wiringDiagrams: demoWiringDiagrams
            });
            setLoading(false);
            toaster.push(
                <Message type="success">
                    Search completed! Found {demoRepairManuals.length} repair manuals and {demoWiringDiagrams.length} wiring diagrams
                </Message>,
                { placement: 'topCenter' }
            );
        }, 1500);
    };

    const handleDivisionChange = (value) => {
        setSelectedDivision(value);
        setSelectedModel(null); // Reset model when division changes
        setSearchResults(null); // Clear previous results
    };

    const init = () => {
        // Initialize component
    };

    useEffect(() => {
        init();
    }, []);

    return (
        <div>
            <Content
                style={{
                    maxWidth: "1200px",
                    margin: "0 auto",
                    padding: "20px",
                }}
            >
                <Panel header="Vehicle Selection" bordered style={{ maxWidth: '800px', margin: '0 auto' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                        <div>
                            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                                Division
                            </label>
                            <SelectPicker
                                data={divisions}
                                placeholder="Select Division"
                                style={{ width: '100%' }}
                                value={selectedDivision}
                                onChange={handleDivisionChange}
                                cleanable={false}
                                size="lg"
                            />
                        </div>

                        <div>
                            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                                Year
                            </label>
                            <SelectPicker
                                data={years}
                                placeholder="Select Year"
                                style={{ width: '100%' }}
                                value={selectedYear}
                                onChange={setSelectedYear}
                                cleanable={false}
                                size="lg"
                            />
                        </div>

                        <div>
                            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                                Model
                            </label>
                            <SelectPicker
                                data={getModelsForDivision(selectedDivision)}
                                placeholder="Select Model"
                                style={{ width: '100%' }}
                                value={selectedModel}
                                onChange={setSelectedModel}
                                disabled={!selectedDivision}
                                cleanable={false}
                                size="lg"
                            />
                        </div>

                        <div style={{ textAlign: 'center', marginTop: '10px' }}>
                            <Button
                                appearance="primary"
                                size="lg"
                                onClick={handleSearch}
                                loading={loading}
                                disabled={!selectedDivision || !selectedYear || !selectedModel}
                                style={{ width: '100%' }}
                            >
                                Search
                            </Button>
                        </div>
                    </div>
                </Panel>

                {searchResults && (
                    <Panel style={{ marginTop: '20px' }} bordered>
                        <Nav
                            appearance="tabs"
                            activeKey={activeTab}
                            onSelect={setActiveTab}
                            style={{ marginBottom: '20px' }}
                        >
                            <Nav.Item eventKey="repair">
                                Repair Manual ({searchResults.repairManuals.length})
                            </Nav.Item>
                            <Nav.Item eventKey="wiring">
                                Wiring Diagram ({searchResults.wiringDiagrams.length})
                            </Nav.Item>
                        </Nav>

                        {activeTab === 'repair' && (
                            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                                <Button size="lg" appearance="primary">
                                    View All Repair Manuals
                                </Button>
                            </div>
                        )}

                        {activeTab === 'wiring' && (
                            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                                <Button size="lg" appearance="primary">
                                    View All Wiring Diagrams
                                </Button>
                            </div>
                        )}
                    </Panel>
                )}
            </Content>
        </div>
    );
}

export default Toyota;
