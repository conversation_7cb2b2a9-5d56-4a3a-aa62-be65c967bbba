import React, { useEffect } from "react";
import {
    Panel,
    Breadcrumb,
    Grid,
    Content,
    Stack,
    Divider,
    Avatar,
    Button,
} from "rsuite";
import PageEndIcon from "@rsuite/icons/PageEnd";
import { RiHome4Line } from "react-icons/ri";
import { useMediaQuery, Placeholder } from "rsuite";
import { planRenewStore } from "../store/planRenewStore";
import EleExpiry from "../components/EleExpiry";
import { useMainStore } from "../store/mainStore";
const Header = (props) => {
    const { avatarUrl, title, subtitle, ...rest } = props;
    return (
        <Stack {...rest} spacing={10} alignItems="flex-start">
            <Stack spacing={2} direction="column" alignItems="flex-start">
                <h5>{title}</h5>
                <div
                    style={{ color: "var(--rs-text-secondary)", fontSize: 12 }}
                >
                    <Stack spacing={10}>
                        {" "}
                        Expiry: {"  "}
                        {"  "}
                        {subtitle}{" "}
                    </Stack>
                </div>
            </Stack>
        </Stack>
    );
};

function ServicePacks(props) {
    const [isInline] = useMediaQuery("xl");
    const getPlanServices = planRenewStore((state) => state.getPlanServices);
    const changePageType = useMainStore((state) => state.changePageType);
    const type = useMainStore((state) => state.type);
    const planServices = planRenewStore((state) => state.planServices);
    const loadingPlanServices = planRenewStore(
        (state) => state.loadingPlanServices
    );

    const init = () => {
        getPlanServices();
    };

    useEffect(() => {
        init();
    }, []);

    const agentTheme = useMainStore((state) => state.agentTheme);

    return (
        <div>
            <Content
                style={{
                    maxWidth: "1000px",
                    margin: "0 auto",
                    padding: "20px",
                }}
            >
                <Grid fluid alignitems="center" justifycontent="center">
                    <Panel
                        header={
                            <>
                                <h3 className="title">Renew your service</h3>{" "}
                                <Breadcrumb>
                                    <Breadcrumb.Item href="/">
                                        <RiHome4Line /> Home
                                    </Breadcrumb.Item>
                                    <Breadcrumb.Item active>
                                        Service Packs - Renewal History
                                    </Breadcrumb.Item>
                                </Breadcrumb>
                            </>
                        }
                    >
                        {type != "main" && agentTheme.contact_link != null &&
                            agentTheme.contact_link != "" && (
                                <div style={{ marginBottom: 20 }}>
                                    <a>
                                        Contact us{" "}
                                        <a
                                            href={agentTheme.contact_link}
                                            target="_blank"
                                        >
                                            <img
                                                src={
                                                    "/assets/images/whatsapp.png"
                                                }
                                                className="rs-avatar-image"
                                                height={40}
                                            ></img>
                                        </a>{" "}
                                        to buy or renew the service.
                                    </a>
                                </div>
                            )}

                        {type == "main" &&  (
                                <div style={{ marginBottom: 20 }}>
                                    <a>
                                        Contact us{" "}
                                        <a
                                            href={"https://wa.me/+84945238603"}
                                            target="_blank"
                                        >
                                            <img
                                                src={
                                                    "/assets/images/whatsapp.png"
                                                }
                                                className="rs-avatar-image"
                                                height={40}
                                            ></img>
                                        </a>{" "}
                                        to buy or renew the service.
                                    </a>
                                </div>
                            )}

                        {loadingPlanServices ? (
                            <Placeholder.Paragraph rows={4} />
                        ) : (
                            <div>
                                <Panel bordered>
                                    {planServices.map((planRenew) => (
                                        <div>
                                            <Stack
                                                spacing={10}
                                                alignItems="flex-start"
                                                justifyContent="space-between"
                                            >
                                                {/* <Avatar src={"avatarUrl"} alt={planRenew.name} /> */}
                                                <Stack
                                                    spacing={2}
                                                    direction="column"
                                                    alignItems="flex-start"
                                                >
                                                    <div>{planRenew.name}</div>
                                                    <EleExpiry
                                                        key={planRenew.name}
                                                        time={planRenew.expiry}
                                                    />
                                                </Stack>
                                                {type == "main" && (  <Button
                                                    onClick={() =>
                                                        changePageType(
                                                            "payment"
                                                        )
                                                    }
                                                    endIcon={<PageEndIcon />}
                                                    appearance="primary"
                                                    active
                                                >
                                                    Buy
                                                </Button> ) }
                                            </Stack>

                                            <Divider />
                                        </div>
                                    ))}
                                    Contact us to renew the service.
                                </Panel>

                                {/* <Stack wrap spacing={8}>
                                    <RadioTile
                                        icon={<Icon as={VscFile} />}
                                        label="1 months
                                "
                                        value="blank"
                                    >
                                        <Stack>1 months</Stack>
                                    </RadioTile>

                                    <RadioTile
                                        icon={<Icon as={VscFile} />}
                                        label="1 months
                                "
                                        value="blank"
                                    >
                                        <Stack>1 months</Stack>
                                    </RadioTile>

                                    <RadioTile
                                        icon={<Icon as={VscFile} />}
                                        label="1 months
                                "
                                        value="blank"
                                    >
                                        <Stack>1 months</Stack>
                                    </RadioTile>
                                </Stack> */}
                            </div>
                        )}
                    </Panel>
                </Grid>
            </Content>
        </div>
    );
}

export default ServicePacks;
