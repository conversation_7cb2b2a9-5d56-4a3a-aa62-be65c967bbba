import React, { useEffect } from "react";
import {
    Container,
    Content,
    Form,
    Button,
    Panel,
    Input,
    Stack,
    VStack,
    Divider,
    InputGroup,
    Loader,
    IconButton,
    Avatar,
} from "rsuite";
import ArowBackIcon from "@rsuite/icons/ArowBack";

import { useAuthStore } from "../store/authStore";
import { alerts } from "../utils/alerts";
import { FaRegEye, FaRegEyeSlash } from "react-icons/fa";
import { setToken } from "../utils/auth";
import { useBadgesStore } from "../store/badgesStore";
import { useMainStore } from "../store/mainStore";
import { validateEmail } from "../utils/helpers";

const Password = React.forwardRef((props, ref) => {
    const [visible, setVisible] = React.useState(false);

    const handleChange = () => {
        setVisible(!visible);
    };
    return (
        <InputGroup inside ref={ref} {...props}>
            <Input type={visible ? "text" : "password"} />
            <InputGroup.Button onClick={handleChange}>
                {visible ? <FaRegEye /> : <FaRegEyeSlash />}
            </InputGroup.Button>
        </InputGroup>
    );
});

function SignUp(props) {
    const login = useAuthStore((state) => state.login);
    const signup = useAuthStore((state) => state.signup);
    const loading = useAuthStore((state) => state.loading);
    const loadingSignUp = useAuthStore((state) => state.loadingSignUp);
    const getAllBadges = useBadgesStore((state) => state.getAllBadges);
    const [formValue, setFormValue] = React.useState({
        email: "",
        password: "",
    });

    const [isForgotPassword, setIsForgotPassword] = React.useState(false);
    const agentTheme = useMainStore((state) => state.agentTheme);
    const type = useMainStore((state) => state.type);
    const changePageType = useMainStore((state) => state.changePageType);
    const changeThemeToLight = useMainStore(
        (state) => state.changeThemeToLight
    );
    useEffect(() => {
        if (type == "main") {
            changeThemeToLight();
        }
    }, []);

    var [errorMessageFullName, setErrorMessageFullName] = React.useState();
    var [errorMessageEmail, setErrorMessageEmail] = React.useState();
    var [errorMessagePassword, setErrorMessagePassword] = React.useState();

    const handleSubmit = (event) => {
        var hasError = false;

        if (!formValue.name) {
            setErrorMessageFullName("This field is required");
            hasError = true;
        }
        if (!formValue.email) {
            setErrorMessageEmail("This field is required");
            hasError = true;
        } else if (!validateEmail(formValue.email)) {
            setErrorMessageEmail("Email invalid");
            hasError = true;
        }
        if (!formValue?.password?.target?.value) {
            setErrorMessagePassword("This field is required");
            hasError = true;
        } else if (formValue?.password?.target?.value.length < 6) {
            setErrorMessagePassword("Password must be more than 6 characters");
            hasError = true;
        }

        if (hasError == true) {
            return;
        }
        if (loading) return;
        event.preventDefault();

        signup(
            {
                name: formValue.name,
                email: formValue.email,
                password: formValue?.password?.target?.value,
            },
            (res) => {
                login(
                    {
                        username: formValue.email,
                        password: formValue?.password?.target?.value,
                    },
                    (res) => {
                        setToken(res.token);
                        setTimeout(function () {
                            getAllBadges();
                            changePageType(
                                "login"
                            );
                        }, 50);
                    },
                    (error) => {
                        alerts.error(error);
                    }
                );
            },
            (error) => {
                alerts.error(error);
            }
        );
    };

    const [errorPlacement, setErrorPlacement] = React.useState("bottomStart");

    return (
        <div>
            <Container>
                <Content>
                    <Stack
                        alignItems="center"
                        justifyContent="center"
                        style={{ height: "100%", paddingTop: 100 }}
                    >
                        <Panel
                            header={"Sign Up"}
                            bordered
                            style={{ width: 380 }}
                        >
                            <Form
                                fluid
                                formValue={formValue}
                                onSubmit={handleSubmit}
                                onChange={(e) => {
                                    setFormValue(e);
                                }}
                            >
                                <Stack
                                    alignItems="center"
                                    justifyContent="center"
                                >
                                   {agentTheme.logo_url != null &&
                                            type != "main" &&   agentTheme.logo_url != "" && (
                                                <div
                                                    class="circle-container"
                                                    style={{
                                                        width: "150px",
                                                        height: "150px",
                                                        overflow: "hidden",
                                                        borderRadius: "50%",
                                                        display: "flex",
                                                        justifyContent:
                                                            "center",
                                                        alignItems: "center",
                                                        background: "black",
                                                    }}
                                                >
                                                    <img
                                                        src={
                                                            agentTheme.logo_url
                                                        }
                                                        alt="Logo"
                                                        class="Logo"
                                                        style={{
                                                            width: "100%",
                                                            height: "100%",
                                                            objectFit: "contain",
                                                        }}
                                                    />
                                                </div>
                                            )}
                                </Stack>

                                <Form.Group>
                                    <Form.ControlLabel>
                                        Full name
                                    </Form.ControlLabel>
                                    <Form.Control
                                        name="name"
                                        errorMessage={errorMessageFullName}
                                        errorPlacement={errorPlacement}
                                        onChange={() => {
                                            setErrorMessageFullName("");
                                        }}
                                    />
                                </Form.Group>
                                <Form.Group>
                                    <Form.ControlLabel>
                                        Email address
                                    </Form.ControlLabel>
                                    <Form.Control
                                        name="email"
                                        errorMessage={errorMessageEmail}
                                        errorPlacement={errorPlacement}
                                        onChange={() => {
                                            setErrorMessageEmail("");
                                        }}
                                    />
                                </Form.Group>
                                <Form.Group
                                    style={{
                                        marginBottom: 5,
                                    }}
                                >
                                    <Form.ControlLabel>
                                        Password
                                    </Form.ControlLabel>
                                    <Form.Control
                                        name="password"
                                        autoComplete="off"
                                        accepter={Password}
                                        errorMessage={errorMessagePassword}
                                        errorPlacement={errorPlacement}
                                        onChange={() => {
                                            setErrorMessagePassword("");
                                        }}
                                    />
                                </Form.Group>

                                <VStack spacing={10}>
                                    <Stack
                                        textAlign="flex-start"
                                        style={{
                                            width: "100%",
                                            justifyContent: "flex-end",
                                            marginBottom: 15,
                                        }}
                                    ></Stack>
                                    <Button
                                        style={{
                                            backgroundColor:
                                                agentTheme.color_main_1,
                                        }}
                                        type="submit"
                                        onClick={handleSubmit}
                                        appearance="primary"
                                        disabled={loading || loadingSignUp}
                                        block
                                    >
                                        {loading || loadingSignUp ? (
                                            <Loader />
                                        ) : (
                                            "Create account"
                                        )}
                                    </Button>
                                </VStack>
                            </Form>
                            <Divider />{" "}
                            <Stack
                                textAlign="center"
                                style={{
                                    width: "100%",
                                    justifyContent: "flex-start",
                                }}
                            >
                                {" "}
                                <a
                                    onClick={() => {
                                        changePageType("login");
                                    }}
                                >
                                   ← Back to login
                                </a>
                            </Stack>
                        </Panel>
                    </Stack>
                </Content>
            </Container>
        </div>
    );
}

export default SignUp;
