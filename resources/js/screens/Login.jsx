import React, { useEffect } from "react";
import {
    Container,
    Content,
    Form,
    Button,
    Panel,
    Input,
    Stack,
    VStack,
    Divider,
    InputGroup,
    Loader,
    IconButton,
    Avatar,
} from "rsuite";
import ArowBackIcon from "@rsuite/icons/ArowBack";

import { useAuthStore } from "../store/authStore";
import { alerts } from "../utils/alerts";
import { FaRegEye, FaRegEyeSlash } from "react-icons/fa";
import { setToken } from "../utils/auth";
import { useBadgesStore } from "../store/badgesStore";
import { useMainStore } from "../store/mainStore";

const Password = React.forwardRef((props, ref) => {
    const [visible, setVisible] = React.useState(false);

    const handleChange = () => {
        setVisible(!visible);
    };
    return (
        <InputGroup inside ref={ref} {...props}>
            <Input type={visible ? "text" : "password"} />
            <InputGroup.Button onClick={handleChange}>
                {visible ? <FaRegEye /> : <FaRegEyeSlash />}
            </InputGroup.Button>
        </InputGroup>
    );
});

function Login(props) {
    const login = useAuthStore((state) => state.login);
    const loading = useAuthStore((state) => state.loading);
    const loadingResetPassword = useAuthStore(
        (state) => state.loadingResetPassword
    );
    const resetPassword = useAuthStore((state) => state.resetPassword);
    const getAllBadges = useBadgesStore((state) => state.getAllBadges);
    const [formValue, setFormValue] = React.useState({
        email: "",
        password: "",
    });

    const [isForgotPassword, setIsForgotPassword] = React.useState(false);
    const agentTheme = useMainStore((state) => state.agentTheme);
    const type = useMainStore((state) => state.type);
    const changePageType = useMainStore((state) => state.changePageType);


    const changeThemeToLight = useMainStore(
        (state) => state.changeThemeToLight
    );
    useEffect(() => {
        if (type == "main") {
            changeThemeToLight();
        }
    }, []);

    const handleSubmit = (event) => {
        if (loading) return;
        event.preventDefault();

        var hasError = false;
        if (!formValue.email) {
            setErrorMessageEmail("This field is required");
            hasError = true;
        }

        if (!formValue?.password?.target?.value) {
            setErrorMessagePassword("This field is required");
            hasError = true;
        }
        if (hasError == true) {
            return;
        }

        login(
            {
                username: formValue.email,
                password: formValue?.password?.target?.value,
            },
            (res) => {
                setToken(res.token);
                setTimeout(function () {
                    getAllBadges();
                }, 50);
            },
            (error) => {
                alerts.error(error);
            }
        );
    };

    const handleSubmitResetPassword = (event) => {
        if (loadingResetPassword) return;
        event.preventDefault();
        resetPassword(
            {
                email: formValue.email,
                type: "PASSWORD",
            },
            (res) => {
                setIsForgotPassword(false);
                alerts.success("Please check your email!");
            },
            (error) => {
                alerts.error(error);
            }
        );
    };

    var [errorMessageEmail, setErrorMessageEmail] = React.useState();
    var [errorMessagePassword, setErrorMessagePassword] = React.useState();

    const [errorPlacement, setErrorPlacement] = React.useState("bottomStart");

    return (
        <div>
            <Container>
                <Content>
                    <Stack
                        alignItems="center"
                        justifyContent="center"
                        style={{ height: "100%", paddingTop: 100 }}
                    >
                        {!isForgotPassword ? (
                            <Panel
                                header={"Sign in "}
                                bordered
                                style={{ width: 380 }}
                            >
                                <Form
                                    fluid
                                    formValue={formValue}
                                    onSubmit={handleSubmit}
                                    onChange={(e) => {
                                        setFormValue(e);
                                    }}
                                >
                                    <Stack
                                        alignItems="center"
                                        justifyContent="center"
                                    >
                                        {agentTheme.logo_url != null &&
                                          type != "main" &&  agentTheme.logo_url != "" && (
                                                <div
                                                    class="circle-container"
                                                    style={{
                                                        width: "150px",
                                                        height: "150px",
                                                        overflow: "hidden",
                                                        borderRadius: "50%",
                                                        display: "flex",
                                                        justifyContent:
                                                            "center",
                                                        alignItems: "center",
                                                        background: "black",
                                                    }}
                                                >
                                                    <img
                                                        src={
                                                            agentTheme.logo_url
                                                        }
                                                        alt="Logo"
                                                        class="Logo"
                                                        style={{
                                                            width: "100%",
                                                            height: "100%",
                                                            objectFit: "contain",
                                                        }}
                                                    />
                                                </div>
                                            )}
                                    </Stack>

                                    {/* <Avatar
                                                    size="lg"
                                                    circle
                                                    style={{
                                                        background: "black",
                                                        width: 100,
                                                        height: 100,
                                                    }}
                                                    src={agentTheme.logo_url}
                                                ></Avatar> */}
                                    <Form.Group>
                                        <Form.ControlLabel>
                                            Email address
                                        </Form.ControlLabel>
                                        <Form.Control
                                            name="email"
                                            errorMessage={errorMessageEmail}
                                            errorPlacement={errorPlacement}
                                            onChange={() => {
                                                setErrorMessageEmail("");
                                            }}
                                        />
                                    </Form.Group>
                                    <Form.Group
                                        style={{
                                            marginBottom: 5,
                                        }}
                                    >
                                        <Form.ControlLabel>
                                            Password
                                        </Form.ControlLabel>
                                        <Form.Control
                                            name="password"
                                            autoComplete="off"
                                            accepter={Password}
                                            errorMessage={errorMessagePassword}
                                            errorPlacement={errorPlacement}
                                            onChange={() => {
                                                setErrorMessagePassword("");
                                            }}
                                        />
                                    </Form.Group>

                                    <VStack spacing={10}>
                                        <Stack
                                            textAlign="flex-start"
                                            style={{
                                                width: "100%",
                                                justifyContent: "flex-end",
                                                marginBottom: 15,
                                            }}
                                        >
                                            {" "}
                                            <a onClick={setIsForgotPassword}>
                                                Forgot password?
                                            </a>
                                        </Stack>
                                        <Button
                                            style={{
                                                backgroundColor:
                                                    agentTheme.color_main_1,
                                            }}
                                            type="submit"
                                            onClick={handleSubmit}
                                            appearance="primary"
                                            disabled={loading}
                                            block
                                        >
                                            {loading ? <Loader /> : "Login"}
                                        </Button>

                                        {type == "main" && (
                                            <Stack
                                                textAlign="center"
                                                style={{
                                                    width: "100%",
                                                    justifyContent:
                                                        "flex-start",
                                                }}
                                            >
                                                {" "}
                                                <a
                                                    onClick={() => {
                                                        changePageType(
                                                            "signup"
                                                        );
                                                    }}
                                                >
                                                    Sign up
                                                </a>
                                            </Stack>
                                        )}
                                    </VStack>
                                </Form>
                                <Divider />{" "}
                                <a href={agentTheme.contact_link}>
                                    <span
                                        style={{
                                            textAlign: "center",
                                            display: "flex",
                                            justifyContent: "center",
                                        }}
                                    >
                                        {(
                                            agentTheme.title ??
                                            agentTheme.agent_code ??
                                            ""
                                        ).toUpperCase()}
                                    </span>
                                </a>{" "}
                            </Panel>
                        ) : (
                            <Panel
                                header={
                                    <IconButton
                                        onClick={() => {
                                            setIsForgotPassword(false);
                                        }}
                                        icon={<ArowBackIcon />}
                                        appearance="default"
                                    />
                                }
                                bordered
                                style={{ width: 400 }}
                            >
                                <h4>Reset your password</h4>
                                <br></br>
                                <Form
                                    fluid
                                    formValue={formValue}
                                    onSubmit={handleSubmit}
                                    onChange={(e) => {
                                        setFormValue(e);
                                    }}
                                >
                                    <Form.Group>
                                        <Form.ControlLabel>
                                            Email address
                                        </Form.ControlLabel>
                                        <Form.Control name="email" />
                                    </Form.Group>

                                    <VStack spacing={10}>
                                        <Button
                                            style={{
                                                backgroundColor:
                                                    agentTheme.color_main_1,
                                            }}
                                            type="submit"
                                            onClick={handleSubmitResetPassword}
                                            appearance="primary"
                                            disabled={loadingResetPassword}
                                            block
                                        >
                                            {loadingResetPassword ? (
                                                <Loader />
                                            ) : (
                                                "Send email reset password"
                                            )}
                                        </Button>
                                    </VStack>
                                </Form>

                                <Divider>
                                    {(
                                        agentTheme.agent_code ?? ""
                                    ).toUpperCase()}
                                </Divider>
                            </Panel>
                        )}
                    </Stack>
                </Content>
            </Container>
        </div>
    );
}

export default Login;
