import React, { useEffect } from "react";
import {
    Form,
    Panel,
    Breadcrumb,
    Input,
    ButtonToolbar,
    Button,
    Grid,
    Content,
    DatePicker,
    Checkbox,
} from "rsuite";
import { RiHome4Line } from "react-icons/ri";
import { Uploader, Message, Loader, useToaster } from "rsuite";
import AvatarIcon from "@rsuite/icons/legacy/Avatar";
import { useBadgesStore } from "../store/badgesStore";
import { useProfileStore } from "../store/profileStore";
import { alerts } from "../utils/alerts";
import { useMainStore } from "../store/mainStore";
import { getToken } from "../utils/auth";
import { getDomainWithSubdomain } from "../utils/constants";

const Textarea = React.forwardRef((props, ref) => (
    <Input {...props} as="textarea" ref={ref} />
));

function Profile(props) {
    const toaster = useToaster();
    const [uploading, setUploading] = React.useState(false);
    const [fileInfo, setFileInfo] = React.useState(null);
    const [userCurrent, setUserCurrent] = React.useState({});
    const user = useBadgesStore((state) => state.user);
    const updateProfile = useProfileStore((state) => state.updateProfile);
    const loadingUpdate = useProfileStore((state) => state.loadingUpdate);
    const backToHome = useMainStore((state) => state.backToHome);

    const init = () => {
        setFileInfo(user?.avatar_image);
        setUserCurrent({
            sex: user?.sex,
            date_of_birth:
                user?.date_of_birth != null
                    ? new Date(user?.date_of_birth)
                    : null,
            address_detail: user.address_detail,
            name: user.name,
        });
    };

    useEffect(() => {
        init();
    }, []);

    const changeSex = (sex) => {
        setUserCurrent({
            ...userCurrent,
            sex: sex,
        });
    };
    const handleSubmit = (event) => {
        if (loadingUpdate) return;
        event.preventDefault();

        updateProfile(
            {
                name: userCurrent.name,
                sex: userCurrent.sex,
                date_of_birth: userCurrent.date_of_birth,
                address_detail: userCurrent.address_detail,
                avatar_image: fileInfo,
            },
            () => {
                alerts.success("Saved");
            },
            (err) => {
                alerts.error(err);
            }
        );
    };

    function previewFile(file, callback) {
        const reader = new FileReader();
        reader.onloadend = () => {
            callback(reader.result);
        };
        reader.readAsDataURL(file);
    }

    return (
        <div>
            <Content
                style={{
                    maxWidth: "1200px",
                    margin: "0 auto",
                    padding: "20px",
                }}
            >
                <Grid fluid alignitems="center" justifycontent="center">
                    <Panel
                        header={
                            <>
                                <h3 className="title">Profile</h3>{" "}
                                <Breadcrumb>
                                    <Breadcrumb.Item href="/">
                                        <RiHome4Line /> Home
                                    </Breadcrumb.Item>
                                    <Breadcrumb.Item active>
                                        Profile
                                    </Breadcrumb.Item>
                                </Breadcrumb>
                            </>
                        }
                    >
                        <Form
                          
                            formValue={userCurrent}
                            onSubmit={handleSubmit}
                            onChange={setUserCurrent}
                        >
                            <Form.Group controlId="avatar">
                                <Form.ControlLabel>Avatar</Form.ControlLabel>
                                <Uploader
                                    name={"image"}
                                    headers={{
                                        "dr-token": getToken(),
                                    }}
                                    fileListVisible={false}
                                    listType="picture"
                                    action={
                                        getDomainWithSubdomain() +
                                        "/api/user/images"
                                    }
                                    onUpload={(file) => {
                                        setUploading(true);
                                        previewFile(file.blobFile, (value) => {
                                            setFileInfo(value);
                                        });
                                    }}
                                    onSuccess={(response, file) => {
                                        setUploading(false);
                                        toaster.push(
                                            <Message type="success">
                                                Uploaded successfully
                                            </Message>
                                        );
                                        setFileInfo(response.data);
                                    }}
                                    onError={() => {
                                        setFileInfo(null);
                                        setUploading(false);
                                        toaster.push(
                                            <Message type="error">
                                                Upload failed
                                            </Message>
                                        );
                                    }}
                                >
                                    <button style={{ width: 150, height: 150 }}>
                                        {uploading && (
                                            <Loader backdrop center />
                                        )}
                                        {fileInfo ? (
                                            <img
                                                src={fileInfo}
                                                width="100%"
                                                height="100%"
                                                style={{
                                                    objectFit: "cover",
                                                }}
                                            />
                                        ) : (
                                            <AvatarIcon
                                                style={{ fontSize: 80 }}
                                            />
                                        )}
                                    </button>
                                </Uploader>
                            </Form.Group>
                            <Form.Group controlId="name-6">
                                <Form.ControlLabel>Name</Form.ControlLabel>
                                <Form.Control name="name" />
                            </Form.Group>
                            <Form.Group controlId="sex">
                                <Form.ControlLabel>Sex</Form.ControlLabel>
                                <Checkbox
                                    onChange={(v) => changeSex(0)}
                                    checked={userCurrent.sex == 0}
                                >
                                    Male
                                </Checkbox>
                                <Checkbox
                                    onChange={(v) => changeSex(1)}
                                    checked={userCurrent.sex == 1}
                                >
                                    Female
                                </Checkbox>
                                <Checkbox
                                    onChange={(v) => changeSex(2)}
                                    checked={userCurrent.sex == 2}
                                >
                                    Other
                                </Checkbox>
                            </Form.Group>
                            <Form.Group controlId="dateofbirth">
                                <Form.ControlLabel>
                                    Date of birth
                                </Form.ControlLabel>
                                <DatePicker
                                    value={userCurrent.date_of_birth}
                                    onChange={(date) => {
                                        setUserCurrent({
                                            ...userCurrent,
                                            date_of_birth: date,
                                        });
                                    }}
                                />
                            </Form.Group>
                            <Form.Group controlId="address_detail">
                                <Form.ControlLabel>Address</Form.ControlLabel>
                                <Form.Control
                                    name="address_detail"
                                    rows={3}
                                    accepter={Textarea}
                                />
                            </Form.Group>
                            <Form.Group>
                                <ButtonToolbar>
                                    <Button
                                        appearance="primary"
                                        type="submit"
                                        disabled={loadingUpdate}
                                        onClick={handleSubmit}
                                    >
                                        {loadingUpdate ? <Loader /> : "Save"}
                                    </Button>
                                    <Button
                                        appearance="default"
                                        onClick={backToHome}
                                    >
                                        Cancel
                                    </Button>
                                </ButtonToolbar>
                            </Form.Group>
                        </Form>
                    </Panel>
                </Grid>
            </Content>
        </div>
    );
}

export default Profile;
