import React from "react";
import {
    Form,
    Panel,
    Breadcrumb,
    Input,
    ButtonToolbar,
    Button,
    Grid,
    Content,
    InputGroup,
    Loader,
} from "rsuite";
import EyeIcon from "@rsuite/icons/legacy/Eye";
import EyeSlashIcon from "@rsuite/icons/legacy/EyeSlash";
import { useProfileStore } from "../store/profileStore";
import { alerts } from "../utils/alerts";
import { useMainStore } from "../store/mainStore";
import { RiHome4Line } from "react-icons/ri";


function ChangePassword(props) {
    const [visible, setVisible] = React.useState(false);
    const [formValue, setFormValue] = React.useState({});
    const backToHome = useMainStore((state) => state.backToHome);

    const changePassword = useProfileStore((state) => state.changePassword);
    const loadingChange = useProfileStore((state) => state.loadingChange);

    const handleChange = () => {
        setVisible(!visible);
    };

    const handleSubmit = (event) => {
        if (loadingChange) return;
        event.preventDefault();

        changePassword(
            formValue,
            () => {
                alerts.success("Changed");
                backToHome()
            },
            (err) => {
                alerts.error(err);
            }
        );
    };

    return (
        <div>
            <Content
                style={{
                    maxWidth: "1200px",
                    margin: "0 auto",
                    padding: "20px",
                }}
            >
                <Grid fluid alignitems="center" justifycontent="center">
                    <Panel
                        header={
                            <>
                                <h3 className="title">Change password</h3>{" "}
                                <Breadcrumb>
                                    <Breadcrumb.Item href="/">
                                       <RiHome4Line /> Home
                                    </Breadcrumb.Item>
                                    <Breadcrumb.Item active>
                                        Change password
                                    </Breadcrumb.Item>
                                </Breadcrumb>
                            </>
                        }
                    >
                        <Form
                            layout="horizontal"
                            formValue={formValue}
                            onChange={setFormValue}
                            onSubmit={handleSubmit}
                        >
                            <Form.Group controlId="old_password">
                                <Form.ControlLabel>
                                    Old password
                                </Form.ControlLabel>
                                <InputGroup
                                    inside
                                    style={{
                                        width: 300,
                                    }}
                                >
                                    <Form.Control
                                        name="old_password"
                                        type={visible ? "text" : "password"}
                                    />

                                    <InputGroup.Button onClick={handleChange}>
                                        {visible ? (
                                            <EyeIcon />
                                        ) : (
                                            <EyeSlashIcon />
                                        )}
                                    </InputGroup.Button>
                                </InputGroup>
                            </Form.Group>

                            <Form.Group controlId="new_password">
                                <Form.ControlLabel>
                                    New password
                                </Form.ControlLabel>
                                <InputGroup
                                    inside
                                    style={{
                                        width: 300,
                                    }}
                                >
                                    <Form.Control
                                        name="new_password"
                                        type={visible ? "text" : "password"}
                                    />
                                    <InputGroup.Button onClick={handleChange}>
                                        {visible ? (
                                            <EyeIcon />
                                        ) : (
                                            <EyeSlashIcon />
                                        )}
                                    </InputGroup.Button>
                                </InputGroup>
                            </Form.Group>

                            <Form.Group>
                                <ButtonToolbar>
                                    <Button
                                        appearance="primary"
                                        type="submit"
                                        onClick={handleSubmit}
                                        disabled={loadingChange}
                                    >
                                        {loadingChange ? <Loader /> : "Save"}
                                    </Button>
                                    <Button
                                        appearance="default"
                                        onClick={backToHome}
                                    >
                                        Cancel
                                    </Button>
                                </ButtonToolbar>
                            </Form.Group>
                        </Form>
                    </Panel>
                </Grid>
            </Content>
        </div>
    );
}

export default ChangePassword;
