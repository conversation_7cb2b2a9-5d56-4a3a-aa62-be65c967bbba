import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Loader, <PERSON>ack, VStack, Container } from "rsuite";
import { useBadgesStore } from "../../store/badgesStore";
import { devicesStore } from "../../store/devicesStore";
import { useMainStore } from "../../store/mainStore";
import { badges } from "../../services/badges";

const DevicePayModal = (props) => {
    const user = useBadgesStore((state) => state.user);
    const badges = useBadgesStore((state) => state.badges);

    const getListPlanDevices = devicesStore(
        (state) => state.getListPlanDevices
    );
    const loadingPlanDevices = devicesStore(
        (state) => state.loadingPlanDevices
    );
    const agentTheme = useMainStore((state) => state.agentTheme);
    const dataPlanDevices = devicesStore((state) => state.dataPlanDevices);
    const type = useMainStore((state) => state.type);
    const [planSelected, setPlanSelected] = useState(null);

    const deviceIdCurrent = props.deviceIdCurrent;
    useEffect(() => {
        if (
            planSelected == null &&
            dataPlanDevices.list_month != null &&
            dataPlanDevices.list_month.length > 0
        ) {
            setNewPlan(dataPlanDevices.list_month[0]);
        }
    }, [props.open]);

    const setNewPlan = (item) => {
        setPlanSelected(item);
    };

    useEffect(() => {
        getListPlanDevices();
    }, []);

    const buildItemPlanTick = (itemPlan, isSelected, onClick) => (
        <Container>
            <div
                className={
                    isSelected
                        ? "rs-radio-tile rs-radio-tile-checked"
                        : "rs-radio-tile"
                }
                style={{
                    margin: "10px",
                    padding: "0px 25px",
                    paddingTop: 5,
                    paddingBottom: 5,
                }}
                onClick={onClick}
            >
                <VStack
                    spacing={0}
                    style={{
                        padding: 0,
                        maxWidth: 125,
                        justifyItems: "center",
                        alignItems: "center",
                        fontSize: 15,
                    }}
                >
                    <p>
                        {" "}
                        <b>{itemPlan.month ?? 0} month</b>
                    </p>

                    <Stack style={{ display: "flex" }} spacing={5}>
                        {itemPlan.discount != null && (
                            <del> ${itemPlan.discount.last_price}</del>
                        )}
                        <span> ${itemPlan.price}</span>
                    </Stack>

                    <p
                        style={{
                            fontSize: 12,
                        }}
                    >
                        {" "}
                        ($
                        {(itemPlan.price / itemPlan.month).toFixed(0)}
                        /month)
                    </p>
                </VStack>

                <div class="rs-radio-tile-mark">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 16 16"
                        fill="currentColor"
                        aria-hidden="true"
                        focusable="false"
                        class="rs-radio-tile-mark-icon rs-icon"
                        aria-label="check"
                        data-category="application"
                    >
                        <path d="M14.136 3.157a.5.5 0 0 1 .783.616l-.056.071-8.5 9a.5.5 0 0 1-.665.056l-.07-.064-4.5-5a.5.5 0 0 1 .677-.73l.066.061 4.136 4.596 8.129-8.605z"></path>
                    </svg>
                </div>
            </div>
        </Container>
    );

    return (
        <>
            <Modal
                style={{
                    display: "flex",
                    alignItems: "center",
                    justifyItems: "center",
                }}
                open={props.open}
                onClose={() => props.setOpen(false)}
                size="md"
            >
                <Modal.Header>
                    <Modal.Title>Add 1 device</Modal.Title>
                </Modal.Header>

                {loadingPlanDevices ? (
                    <Loader />
                ) : (
                    <>
                        {badges.enable_customer_add_device == false ? (
                            <Modal.Body>
                                {" "}
                                <div style={{ marginBottom: 20 }}>
                                    <Stack
                                        alignItems="center"
                                        justifyContent="center"
                                    >
                                        <a>
                                            Contact us{" "}
                                            <a
                                                href={agentTheme.contact_link}
                                                target="_blank"
                                            >
                                                <img
                                                    src={
                                                        "/assets/images/whatsapp.png"
                                                    }
                                                    className="rs-avatar-image"
                                                    height={40}
                                                ></img>
                                            </a>
                                            to buy device!
                                        </a>
                                    </Stack>
                                </div>
                            </Modal.Body>
                        ) : (
                            <Modal.Body>
                                <Stack
                                    alignItems="center"
                                    justifyContent="center"
                                >
                                    {dataPlanDevices.list_month.map((item) =>
                                        buildItemPlanTick(
                                            item,
                                            planSelected != null &&
                                                planSelected.id == item.id,
                                            () => {
                                                setNewPlan(item);
                                            }
                                        )
                                    )}
                                </Stack>
                                <List>
                                    {dataPlanDevices.list_method.map(
                                        (method, index) => {
                                            var urlPayment = method.payment_url
                                                .replace(
                                                    "{{email}}",
                                                    user?.email
                                                )
                                                .replace(
                                                    "{{month}}",
                                                    planSelected?.month
                                                )
                                                .replace(
                                                    "{{device_login_id}}",
                                                    deviceIdCurrent
                                                );

                                            urlPayment =
                                                urlPayment +
                                                "&code_voucher=" +
                                                (props.codeVoucher ?? "");
                                            return (
                                                <List.Item
                                                    key={index}
                                                    style={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                        padding: "10px",
                                                        borderBottom:
                                                            "1px solid #f0f0f0",
                                                    }}
                                                >
                                                    <img
                                                        width={120}
                                                        height={80}
                                                        src={method.image}
                                                        alt={method.name}
                                                        size="xl"
                                                        style={{
                                                            marginRight: "15px",
                                                            objectFit:
                                                                "contain",
                                                        }}
                                                    />
                                                    <div style={{ flex: 1 }}>
                                                        <strong>
                                                            {method.name}
                                                        </strong>
                                                        <p
                                                            style={{
                                                                margin: 0,
                                                                color: "#888",
                                                            }}
                                                        >
                                                            {method.description}
                                                        </p>
                                                    </div>
                                                    <a
                                                        href={urlPayment}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="rs-btn rs-btn-primary"
                                                    >
                                                        Pay
                                                    </a>
                                                </List.Item>
                                            );
                                        }
                                    )}
                                </List>
                            </Modal.Body>
                        )}
                    </>
                )}
                <Modal.Footer>
                    <Button
                        onClick={() => props.setOpen(false)}
                        appearance="subtle"
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default DevicePayModal;
