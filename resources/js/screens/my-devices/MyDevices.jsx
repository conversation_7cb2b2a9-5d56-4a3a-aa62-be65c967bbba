import React, { useEffect, useState } from "react";
import {
    Breadcrumb,
    Placeholder,
    Content,
    Grid,
    Panel,
    Stack,
    Button,
} from "rsuite";
import { RiHome4Line } from "react-icons/ri";
import { devicesStore } from "../../store/devicesStore";
import IOsIcon from "@rsuite/icons/IOs";
import AndroidIcon from "@rsuite/icons/Android";
import PcIcon from "@rsuite/icons/Pc";
import TrashIcon from "@rsuite/icons/Trash";
import EleExpiry from "../../components/EleExpiry";
import { IconButton } from "rsuite";
import { displayTimeAgoFromTime } from "../../utils/time";
import DevicePayModal from "./DevicePayModal";
import { alerts } from "../../utils/alerts";
import { sagaDialogYesNo } from "../../utils/dialog";

const buildIconDevice = (platform) => {
    if (platform === "android") {
        return (
            <div style={circleAvatarStyle("green")}>
                <AndroidIcon style={{ color: "white" }} />
            </div>
        );
    }
    if (platform === "ios") {
        return (
            <div style={circleAvatarStyle("black")}>
                <IOsIcon style={{ color: "white" }} />
            </div>
        );
    }
    return (
        <div style={circleAvatarStyle("lightBlue")}>
            <PcIcon style={{ color: "white" }} />
        </div>
    );
};

const itemDeviceLogin = (model, onRenew, removeDevice) => {
    return (
        <Panel
            bordered
            style={{
                marginBottom: 10,
            }}
        >
            <Stack justifyContent="space-between">
                {model.device_id == null ? (
                    <Stack alignItems="center">
                        <span>No login</span>
                    </Stack>
                ) : (
                    <Stack direction="column" alignItems="flex-start">
                        {buildIconDevice(model.platform ?? "")}

                        <Stack>
                            <div style={textContainerStyle}>
                                <span>{model.platform ?? ""}:</span>
                            </div>
                            <div style={textContainerStyle}>
                                <span
                                    style={{ fontSize: 13, fontWeight: "bold" }}
                                >
                                    {model.model_name ?? ""}
                                </span>
                            </div>
                        </Stack>
                        <div style={{ height: 5 }} />
                    </Stack>
                )}

                <Stack style={{ marginBottom: 10 }}>
                    <IconButton
                        onClick={() => {
                            sagaDialogYesNo("Do you want remove?", () => {
                                removeDevice();
                            });
                        }}
                        icon={<TrashIcon />}
                        appearance="default"
                    />
                </Stack>
            </Stack>

            <Stack justifyContent="space-between" spacing={5}>
                <Stack>
                    {model.last_visit_time && (
                        <span style={lastVisitStyle}>
                            {displayTimeAgoFromTime(model.last_visit_time)}
                        </span>
                    )}
                </Stack>

                <Stack spacing={8}>
                    {" "}
                    {model.is_main ? (
                        <Button size="xs" appearance="primary">
                            main
                        </Button>
                    ) : (
                        <div style={rowStyle}>
                            <span style={expiryStyle}>
                                <Stack spacing={4}>
                                    {model.expiry_use != null
                                        ? "Expiration date: "
                                        : ""}{" "}
                                    {<EleExpiry time={model.expiry_use} />}
                                </Stack>
                            </span>

                            <Button
                                onClick={() => {
                                    onRenew(model.id);
                                }}
                                size="xs"
                                style={{ marginLeft: 5 }}
                            >
                                Renew
                            </Button>
                        </div>
                    )}
                </Stack>
            </Stack>
        </Panel>
    );
};

// CSS-in-JS styles
const circleAvatarStyle = (backgroundColor) => ({
    backgroundColor,
    borderRadius: "50%",
    width: 40,
    height: 40,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
});

const rowStyle = {
    display: "flex",
    alignItems: "center",
};

const textContainerStyle = {
    padding: "2px 12px",
};

const lastVisitStyle = {
    color: "#666",
    fontSize: 12,
};

const expiryStyle = {
    color: "gray",
    fontSize: 11,
};

function MyDevices(props) {
    const loadingDevices = devicesStore((state) => state.loadingDevices);
    const devices = devicesStore((state) => state.devices);
    const getDevices = devicesStore((state) => state.getDevices);
    const removeDevice = devicesStore((state) => state.removeDevice);
    const [openPayDevice, setOpenPayDevice] = useState(false);

    const [deviceIdCurrent, setDeviceIdCurrent] = useState("");

    const onAddorUpdateDevice = (deviceLoginId = null) => {
        if (deviceLoginId != null) {
            setDeviceIdCurrent(deviceLoginId);
            setOpenPayDevice(true);
        } else {
            setDeviceIdCurrent("");
            setOpenPayDevice(true);
        }
    };

    const init = () => {
        getDevices();
    };

    useEffect(() => {
        init();
    }, []);

    return (
        <div>
            <Content
                style={{
                    maxWidth: "1000px",
                    margin: "0 auto",
                    padding: "20px",
                }}
            >
                <DevicePayModal
                    open={openPayDevice}
                    setOpen={(open) => {
                        if (open == false) {
                            getDevices();
                        }
                        setOpenPayDevice(open);
                    }}
                    deviceIdCurrent={deviceIdCurrent}
                />
                <Grid fluid alignitems="center" justifycontent="center">
                    <Panel
                        header={
                            <>
                                <h3 className="title">My devices</h3>{" "}
                                <Breadcrumb>
                                    <Breadcrumb.Item href="/">
                                        <RiHome4Line /> Home
                                    </Breadcrumb.Item>
                                    <Breadcrumb.Item active>
                                        My devices
                                    </Breadcrumb.Item>
                                </Breadcrumb>
                            </>
                        }
                    >
                        <div
                            style={{
                                display: "flex",
                                justifyContent: "flex-end",
                            }}
                        >
                            <Button
                                appearance="primary"
                                color="blue"
                                size="lg"
                                onClick={() => {
                                    onAddorUpdateDevice();
                                }}
                            >
                                Add device
                            </Button>
                        </div>
                        <h4 className="title">THIS DEVICE</h4> <br></br>
                        {loadingDevices ? (
                            <Placeholder.Paragraph rows={4} />
                        ) : (
                            <Panel>
                                {devices
                                    .filter((item) => item.active == true)
                                    .map((ele) =>
                                        itemDeviceLogin(
                                            ele,
                                            () => {
                                                onAddorUpdateDevice(ele.id);
                                            },
                                            () => {
                                                removeDevice(ele.id);
                                            }
                                        )
                                    )}
                            </Panel>
                        )}
                        <br></br>
                        <h4 className="title">OTHER DEVICE</h4>{" "}
                        {loadingDevices ? (
                            <Placeholder.Paragraph rows={4} />
                        ) : (
                            <Panel>
                                {devices
                                    .filter((item) => item.active != true)
                                    .map((ele) =>
                                        itemDeviceLogin(
                                            ele,
                                            () => {
                                                onAddorUpdateDevice(ele.id);
                                            },
                                            () => {
                                                removeDevice(ele.id);
                                            }
                                        )
                                    )}
                            </Panel>
                        )}
                    </Panel>
                </Grid>
            </Content>
        </div>
    );
}

export default MyDevices;
