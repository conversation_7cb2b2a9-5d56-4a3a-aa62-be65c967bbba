import React, { useState, useEffect } from "react";
import { useMainStore } from "../../store/mainStore";
import Profile from "../Profile";
import Home from "./home";
import Topbar from "../../components/Topbar";
import ServicePacks from "../ServicePacks";
import ChangePassword from "../ChangePassword";
import { <PERSON><PERSON>, Loader, Stack } from "rsuite";
import { useBadgesStore } from "../../store/badgesStore";
import ReloadIcon from "@rsuite/icons/Reload";
import Login from "../Login";
import PayScreen from "../pay/PayScreen";
import MyDevices from "../my-devices/MyDevices";

function buildBody(pageType) {
    if (pageType == "profile") {
        return <Profile />;
    }
    if (pageType == "service_packs") {
        return <ServicePacks />;
    }
    if (pageType == "change_password") {
        return <ChangePassword />;
    }
    if (pageType == "my_devices") {
        return <MyDevices />;
    }
    if (pageType == "payment") {
        return <PayScreen />;
    }

    return <Home />;
}
function MainAgent(props) {
    const pageType = useMainStore((state) => state.pageType);
    const getAgentTheme = useMainStore((state) => state.getAgentTheme);
    const loadingBadges = useBadgesStore((state) => state.loading);
    const getAllBadges = useBadgesStore((state) => state.getAllBadges);
    const errorBadges = useBadgesStore((state) => state.errorBadges);
    const user = useBadgesStore((state) => state.user);

    const init = () => {
        getAgentTheme();
        getAllBadges();
    };

    useEffect(() => {
        init();
    }, []);

    if (loadingBadges) {
        return <Loader center size="lg" speed="fast" />;
    }

    if (errorBadges) {
        return (
            <Stack
                style={{ height: "100vh" }}
                justifyContent="center"
                alignItems="center"
            >
                <Button
                    onClick={() => {
                        getAllBadges();
                    }}
                    color="red"
                    appearance="ghost"
                    startIcon={<ReloadIcon />}
                >
                    Try again
                </Button>
            </Stack>
        );
    }

    if (user == null) {
        return <Login />;
    }
    return (
        <div >
            <Topbar appearance="inverse" />
            {buildBody(pageType)}
        </div>
    );
}

export default MainAgent;
