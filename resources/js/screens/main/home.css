.bg-gradient-orange {
    background: linear-gradient(87deg, #fb6340 0, #fbb140 100%);
}

.bg-gradient-red {
    background: linear-gradient(87deg, #f5365c 0, #f56036 100%);
}

.bg-gradient-green {
    background: linear-gradient(87deg, #2dce89 0, #2dcecc 100%);
}

.bg-gradient-blue {
    background: linear-gradient(87deg, #254261 0, #226ac8 100%);
}

.bg-gradient-black {
    background: linear-gradient(87deg, #254261 0, #4b596a 100%);
}

.bg-gradient-stripe {
    background: linear-gradient(to right, #a6ffcb, #12d8fa, #1fa2ff);
}

.bg-gradient-purple {
    background: linear-gradient(87deg, #8965e0 0, #d633fc 100%);
}

.bg-gradient-aubergine {
    background: linear-gradient(to right, #aa076b, #61045f);
}

.bg-gradient-mirage {
    background: linear-gradient(to right, #16222a, #3a6073);
}

.bg-gradient-mojito {
    background: linear-gradient(87deg, #93f9b9, #1d976c);
}

.wrap-button:hover {
    filter: brightness(0.85);
}

a:hover {
    text-decoration: none;
    cursor: pointer;
}

@media (max-width: 576px) {
    .rs-avatar-image {
        width: 40px;
        height: auto;
    }
    .program-name {
        font-size: 12px;
        line-height: 15px;
    }

    .welcome-cc > .rs-heading {
        padding: 0px;
    }
}
