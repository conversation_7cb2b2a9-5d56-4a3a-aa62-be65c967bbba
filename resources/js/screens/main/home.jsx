import React, { useEffect, useRef } from "react";
import {
    Content,
    Divider,
    Loader,
    Container,
    Modal,
    Button,
    ButtonToolbar,
} from "rsuite";
import PcIcon from "@rsuite/icons/Pc";
import EmailIcon from "@rsuite/icons/Email";

import { uniqueId } from "lodash";

import { Grid, Row, Col } from "rsuite";
import RemindIcon from "@rsuite/icons/legacy/Remind";

import "./home.css";
import { useAuthStore } from "../../store/authStore";
import { useMainStore } from "../../store/mainStore";
import { useBadgesStore } from "../../store/badgesStore";
import { alerts } from "../../utils/alerts";
import ItemProductMain1 from "../../components/ItemProductMain1";
import { isSafariOnIphone } from "../../utils/device";
import { boards } from "../../utils/constants/home";
export const creatCard = (progress) => {
    return {
        id: uniqueId(),
        content: "fdasfas",
        image: "asdasd",
        progress: progress ? Math.floor(Math.random() * 100) : null,
        users: [],
    };
};

export const mockCards = (length, progress) =>
    Array.from({ length }, () => creatCard(progress));

const Home = (props) => {
    const changePageType = useMainStore((state) => state.changePageType);
    const loadingCheckSecurity = useAuthStore(
        (state) => state.loadingCheckSecurity
    );
    const checkSecurity = useAuthStore((state) => state.checkSecurity);

    const [openModelExpiry, setOpenExpiry] = React.useState(false);
    const handleOpenExpiry = () => setOpenExpiry(true);
    const handleCloseExpiry = () => setOpenExpiry(false);

    const [openModelLoginOtherDevice, setOpenLoginOtherDevice] =
        React.useState(false);
    const handleOpenLoginOtherDevice = () => setOpenLoginOtherDevice(true);
    const handleCloseLoginOtherDevice = () => setOpenLoginOtherDevice(false);
    const user = useBadgesStore((state) => state.user);

    const resetDevice = useAuthStore((state) => state.resetDevice);
    const loadingResetDevices = useAuthStore(
        (state) => state.loadingResetDevices
    );
    const changeThemeToDark = useMainStore((state) => state.changeThemeToDark);
    const type = useMainStore((state) => state.type);
    useEffect(() => {
        if (type == "main") {
            changeThemeToDark();
        }
    }, []);

    return (
        <div>
            <Modal
                centered
                backdrop="static"
                role="alertdialog"
                open={openModelExpiry}
                onClose={handleCloseExpiry}
                size="xs"
                style={{
                    display: "flex",
                    alignItems: "center",
                }}
            >
                <Modal.Body>
                    <RemindIcon
                        style={{
                            color: "#ffb300",
                            fontSize: 24,
                            marginRight: 15,
                        }}
                    />
                    Please renew to use the service
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        onClick={() => {
                            changePageType("service_packs");
                            handleCloseExpiry();
                        }}
                        appearance="primary"
                    >
                        Ok
                    </Button>
                    <Button onClick={handleCloseExpiry} appearance="subtle">
                        Cancel
                    </Button>
                </Modal.Footer>
            </Modal>
            <Modal
                centered
                backdrop="static"
                role="alertdialog"
                open={openModelLoginOtherDevice}
                onClose={handleCloseLoginOtherDevice}
                size="xs"
                style={{
                    display: "flex",
                    alignItems: "center",
                }}
            >
                <Modal.Body>
                    <RemindIcon
                        style={{
                            color: "#ffb300",
                            fontSize: 24,
                            marginRight: 15,
                        }}
                    />
                    This account is already signed in on another device
                    <ButtonToolbar
                        style={{
                            marginTop: 15,
                        }}
                    >
                        <Button
                            onClick={() => {
                                if (loadingResetDevices == false) {
                                    resetDevice(
                                        {
                                            email: user.email,
                                            type: "DEVICE",
                                        },
                                        () => {
                                            alerts.success(
                                                "Please check your email!"
                                            );
                                        },

                                        (err) => {
                                            alerts.error(err);
                                        }
                                    );
                                }
                            }}
                            appearance="link"
                            block
                        >
                            {loadingResetDevices ? (
                                <Loader
                                    style={{
                                        marginRight: 8,
                                    }}
                                />
                            ) : (
                                <EmailIcon
                                    style={{
                                        marginRight: 8,
                                    }}
                                />
                            )}
                            Send email reset devices
                        </Button>
                        <Divider
                            style={{
                                margin: "1px 0",
                            }}
                        >
                            Or
                        </Divider>
                        <Button
                            onClick={() => {
                                changePageType("my_devices");
                                handleCloseLoginOtherDevice();
                            }}
                            appearance="link"
                            block
                        >
                            <PcIcon
                                style={{
                                    marginRight: 8,
                                }}
                            />{" "}
                            Use on multiple devices
                        </Button>
                    </ButtonToolbar>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        onClick={handleCloseLoginOtherDevice}
                        appearance="subtle"
                    >
                        Cancel
                    </Button>
                </Modal.Footer>
            </Modal>
            <Content
                style={{
                    margin: "0 auto",
                    padding: "0px 15px",
                }}
            >
                <Container style={{ position: "relative", paddingTop: 20 }}>
                    <Grid fluid alignitems="center" justifycontent="center">
                        <Row className="show-grid">
                            {boards.map((board) => (
                                <ItemProductMain1
                                    board={board}
                                    onClick={() => {

                                         if(board.expiry_column == "expiry_toyota") {
                                            changePageType('toyota')
                                            return;
                                        }
                                        if (board.expiry_column == null) {
                                            window.open(board.to, "_blank");
                                            // window.location.href = board.to
                                            return;
                                        }
                                        checkSecurity(
                                            board.expiry_column,
                                            () => {
                                                if (isSafariOnIphone()) {
                                                    window.location.href =
                                                        board.to;
                                                } else {
                                                    window.open(
                                                        board.to,
                                                        "_blank"
                                                    );
                                                }
                                            },
                                            (errorCode) => {
                                                if (errorCode == "EXPIRED") {
                                                    handleOpenExpiry();
                                                } else if (
                                                    errorCode ==
                                                    "ERROR_LOGGED_IN_ON_ANOTHER_DEVICE"
                                                ) {
                                                    handleOpenLoginOtherDevice();
                                                }
                                            }
                                        );
                                    }}
                                ></ItemProductMain1>
                            ))}
                        </Row>
                    </Grid>

                    {loadingCheckSecurity ? (
                        <Loader backdrop center vertical size="md" />
                    ) : (
                        ""
                    )}
                </Container>
            </Content>
        </div>
    );
};

export default Home;
