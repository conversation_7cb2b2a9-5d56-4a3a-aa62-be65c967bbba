import React, { useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Container, V<PERSON><PERSON><PERSON>, But<PERSON> } from "rsuite";
import classNames from "classnames";
import { useMainStore } from "../store/mainStore";
import { useBadgesStore } from "../store/badgesStore";

const ItemProductMain1 = ({ board, onClick }) => {
    const user = useBadgesStore((state) => state.user);
    const type = useMainStore((state) => state.type);
    const status_server = useBadgesStore((state) => state.status_server);

    const hasExpiry =
        (board.expiry_column != null &&
            user[board.expiry_column] != null &&
            new Date() < new Date(user[board.expiry_column])) ||
        board.expiry_column == null;

    return (
        <Col
            key={board.title}
            xs={12}
            sm={8}
            md={8}
            style={{
                marginBottom: 10,
                position: "relative",
            }}
        >
            <Container
                onClick={onClick}
                key={board.id}
                className={classNames("wrap-button", "board-box")}
                style={{
                    cursor: "pointer",
                    background: "white",
                    padding: 4,
                }}
            >
                <VStack
                    spacing={2}
                    justifyContent="flex-start"
                    alignItems="center"
                >
                    <img
                        height={60}
                        width={60}
                        hexColor="#691A1b"
                        src={
                            "/assets/images/" +
                            (type == "main" ? "main/" : "agent/") +
                            board.image
                        }
                    ></img>

                    <div>
                        <Heading
                            className="program-name"
                            style={{
                                textDecoration: "none",
                                color: "#5c3636",
                            }}
                            level={5}
                        >
                            {board.title}
                        </Heading>
                    </div>

                    {hasExpiry ? (
                        <Button
                            style={{
                                backgroundColor: "rgb(46 132 18)",
                                paddingLeft: 35,
                                paddingRight: 35,
                            }}
                            color="red"
                            appearance="primary"
                        >
                            Access
                        </Button>
                    ) : (
                        <Button
                            style={{
                                backgroundColor: "#691A1b",
                                paddingLeft: 35,
                                paddingRight: 35,
                            }}
                            color="red"
                            appearance="primary"
                        >
                            Buy
                        </Button>
                    )}

                    {hasExpiry &&
                    status_server != null &&
                    status_server[board.status_server_column] == false ? (
                        <div>
                            <Badge
                                style={{
                                    background: "red",
                                }}
                            />{" "}
                            <span
                                style={{
                                    fontSize: 12,
                                    color: "grey",
                                }}
                            >
                                Fixing errors
                            </span>
                        </div>
                    ) : (
                        <div>
                            <Badge
                                style={{
                                    background: "white",
                                }}
                            />{" "}
                            <span
                                style={{
                                    fontSize: 12,
                                    color: "grey",
                                }}
                            ></span>
                        </div>
                    )}
                </VStack>
            </Container>
        </Col>
    );
};

export default ItemProductMain1;
