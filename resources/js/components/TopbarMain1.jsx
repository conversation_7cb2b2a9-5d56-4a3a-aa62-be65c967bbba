import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    IconButton,
    <PERSON><PERSON><PERSON>,
    <PERSON>tar,
    Dropdown,
    Navbar,
    Nav,
    Loader,
    ButtonToolbar,
    Button,
} from "rsuite";

import React, { useRef } from "react";
import classNames from "classnames";
import { Icon } from "@rsuite/icons";
import OffIcon from "@rsuite/icons/Off";
import { MdOutlineNightlight, MdOutlineLightMode } from "react-icons/md";
import { useMainStore } from "../store/mainStore";
import { useBadgesStore } from "../store/badgesStore";
import { useAuthStore } from "../store/authStore";
import { getTheme, removeToken } from "../utils/auth";
import { alerts } from "../utils/alerts";

const CustomNavbar = ({ onSelect, activeKey, ...props }) => {
    const changeTheme = useMainStore((state) => state.changeTheme);
    const theme = useMainStore((state) => state.theme);
    const user = useBadgesStore((state) => state.user);
    const agentTheme = useMainStore((state) => state.agentTheme);
    const changePageType = useMainStore((state) => state.changePageType);
    const loadingLogout = useAuthStore((state) => state.loadingLogout);
    const logout = useAuthStore((state) => state.logout);
    const getAllBadges = useBadgesStore((state) => state.getAllBadges);

    return (
        <Navbar {...props}>
            <Navbar.Brand onClick={() => changePageType("home")}>
                {" "}
                {(agentTheme.agent_code ?? "").toUpperCase()}
            </Navbar.Brand>
            <Nav onSelect={onSelect} activeKey={activeKey}>
                <Nav.Item eventKey="2" onClick={() => changePageType("forum")}>
                    Home
                </Nav.Item>

                <Nav.Item
                    eventKey="3"
                    onClick={() => window.open('https://wa.me/+84945238603', '_blank')}
                    >
                    Contact
                </Nav.Item>
            </Nav>

            {user == null && (
                <Nav pullRight>
                    <Nav.Menu title="Login" icon={<OffIcon />}>
                        <Nav.Item
                            eventKey="4"
                            onClick={() => changePageType("login")}
                        >
                            Login
                        </Nav.Item>
                        <Nav.Item
                            eventKey="5"
                            onClick={() => changePageType("sign_up")}
                        >
                            Sign up
                        </Nav.Item>
                    </Nav.Menu>
                </Nav>
            )}

            {user != null && (
                <Nav pullRight>
                    <Nav.Menu
                        title="Profile     "
                        icon={
                            <Avatar
                                size="md"
                                circle
                                src={user.avatar_image}
                                // alt={user.name}
                                style={{ marginLeft: 8 }}
                            />
                        }
                        style={{ paddingRight: 20 }}
                    >
                        <Dropdown.Item
                            panel
                            style={{
                                padding: 10,
                                width: 160,
                                color: theme == "light" ? "black" : "white",
                            }}
                        >
                            <p>Signed in as</p>
                            <strong
                                style={{
                                    padding: 10,
                                    width: 160,
                                }}
                            >
                                {user?.username}
                            </strong>
                        </Dropdown.Item>
                        <Dropdown.Item divider />
                        <Dropdown.Item
                            onClick={() => changePageType("profile")}
                        >
                            Profile & account
                        </Dropdown.Item>
                        <Dropdown.Item
                            onClick={() => changePageType("change_password")}
                        >
                            Change password
                        </Dropdown.Item>

                        <Dropdown.Item divider />

                        <Dropdown.Item
                            onClick={() => changePageType("service_packs")}
                        >
                            Buy service - History
                        </Dropdown.Item>

                        <Dropdown.Item
                            onClick={() => changePageType("my_devices")}
                        >
                            My devices
                        </Dropdown.Item>

                        <Dropdown.Item
                            icon={loadingLogout ? <Loader /> : <OffIcon />}
                            onClick={() => {
                                if (!loadingLogout) {
                                    logout(
                                        () => {
                                            removeToken();
                                            getAllBadges();
                                        },
                                        (err) => {
                                            alerts.error(err);
                                        }
                                    );
                                }
                            }}
                        >
                            Logout{" "}
                        </Dropdown.Item>
                    </Nav.Menu>
                </Nav>
            )}
        </Navbar>
    );
};

const TopbarMain1 = () => {
    const [activeKey, setActiveKey] = React.useState(null);

    return (
        <>
            <CustomNavbar
                style={{ backgroundColor: "#691A1b" }}
                appearance="inverse"
                activeKey={activeKey}
                onSelect={setActiveKey}
            />
        </>
    );
};

export default TopbarMain1;
