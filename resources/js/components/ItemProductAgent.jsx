import React, { useRef } from "react";
import { Ava<PERSON>, <PERSON><PERSON>, Badge, Col, Panel, Stack } from "rsuite";
import classNames from "classnames";
import { useMainStore } from "../store/mainStore";
import { useBadgesStore } from "../store/badgesStore";

const ItemProductAgent = ({ board, onClick }) => {
    const agentTheme = useMainStore((state) => state.agentTheme);
    const user = useBadgesStore((state) => state.user);
    const type = useMainStore((state) => state.type);
    const status_server = useBadgesStore((state) => state.status_server);
    const hasExpiry =
        (board.expiry_column != null &&
            user[board.expiry_column] != null &&
            new Date() < new Date(user[board.expiry_column])) ||
        board.expiry_column == null;


    return (
        <Col
            key={board.title}
            xs={12}
            sm={12}
            md={8}
            style={{
                marginBottom: 10,
                position: "relative",
            }}
        >
         
            <Panel
                key={board.id}
                className={classNames("wrap-button", "board-box")}
                style={{
                    cursor: "pointer",
                    background: agentTheme.color_main_1,
                }}
                onClick={onClick}
            >
                <Stack spacing={6} justifyContent="flex-start">
                    <img
                        height={40}
                        width={40}
                        src={
                            "/assets/images/" +
                            (type == "main" ? "main/" : "agent/") +
                            board.image
                        }
                        style={{
                            padding: 10,
                            background: "#6a6f7617",
                        }}
                    ></img>

                    <div>
                        <Heading
                            className="program-name"
                            style={{
                                color: "white",
                                textDecoration: "none",
                            }}
                            level={5}
                        >
                            {board.title}
                        </Heading>
                    </div>
                </Stack>
            </Panel>

            {hasExpiry ? (
                status_server != null &&
                status_server[board.status_server_column] == false ? (
                    <div
                        style={{
                            position: "absolute",
                            bottom: 10,
                            right: 15,
                        }}
                    >
                        <Badge
                            style={{
                                background: "red",
                            }}
                        />{" "}
                        <span
                            style={{
                                fontSize: 12,
                                color: "white",
                            }}
                        >
                            Fixing errors
                        </span>
                    </div>
                ) : (
                    <div
                        style={{
                            position: "absolute",
                            bottom: 10,
                            right: 15,
                        }}
                    >
                        <Badge
                            style={{
                                background: "#4caf50",
                            }}
                        />{" "}
                        <span
                            style={{
                                fontSize: 12,
                                color: "white",
                            }}
                        >
                            Actived
                        </span>
                    </div>
                )
            ) : (
                ""
            )}
        </Col>
    );
};

export default ItemProductAgent;
