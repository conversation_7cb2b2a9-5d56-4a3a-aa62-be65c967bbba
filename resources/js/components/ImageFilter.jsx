import React from "react";
import PropTypes from "prop-types";

const ImageFilter = ({ src, alt, hexColor }) => {
    const style = {
        filter: `
      sepia(1) 
      saturate(5) 
      hue-rotate(180deg) 
      brightness(0.8)
    `,
        width: "80px",
        height: "80px",
    };

    return <img src={src} alt={alt} style={style} />;
};

ImageFilter.propTypes = {
    src: PropTypes.string.isRequired,
    alt: PropTypes.string,
    hexColor: PropTypes.string, // Chưa sử dụng trực tiếp nhưng giữ để tùy biến thêm nếu cần
};

ImageFilter.defaultProps = {
    alt: "filtered image",
};

export default ImageFilter;
