import {
    <PERSON><PERSON>,
    Con<PERSON>er,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Dropdown,
    Loader,
} from "rsuite";

import React, { useRef } from "react";
import classNames from "classnames";
import { Icon } from "@rsuite/icons";
import OffIcon from "@rsuite/icons/Off";
import { MdOutlineNightlight, MdOutlineLightMode } from "react-icons/md";
import { useMainStore } from "../store/mainStore";
import { useBadgesStore } from "../store/badgesStore";
import { useAuthStore } from "../store/authStore";
import { getTheme, removeToken } from "../utils/auth";
import { alerts } from "../utils/alerts";

const renderAdminSpeaker = ({ onClose, left, top, className }, ref) => {
    const changePageType = useMainStore((state) => state.changePageType);
    const user = useBadgesStore((state) => state.user);
    const logout = useAuthStore((state) => state.logout);
    const loadingLogout = useAuthStore((state) => state.loadingLogout);
    const getAllBadges = useBadgesStore((state) => state.getAllBadges);

    const handleSelect = (eventKey) => {
        onClose();
        console.log(eventKey);
    };
    return (
        <Popover ref={ref} className={className} style={{ left, top }} full>
            <Dropdown.Menu onSelect={handleSelect}>
                <Dropdown.Item panel style={{ padding: 10, width: 160 }}>
                    <p>Signed in as</p>
                    <strong>{user?.username}</strong>
                </Dropdown.Item>
                <Dropdown.Item divider />
              
                  <Dropdown.Item onClick={() => changePageType("profile")}>
                    Profile & account
                </Dropdown.Item>
                <Dropdown.Item
                    onClick={() => changePageType("change_password")}
                >
                    Change password
                </Dropdown.Item>

                <Dropdown.Item divider />
                <Dropdown.Item onClick={() => changePageType("service_packs")}>
                    Buy service - Renewal History
                </Dropdown.Item>

                <Dropdown.Item onClick={() => changePageType("my_devices")}>
                    My devices
                </Dropdown.Item>

                <Dropdown.Item
                    icon={loadingLogout ? <Loader /> : <OffIcon />}
                    onClick={() => {
                        if (!loadingLogout) {
                            logout(
                                () => {
                                    removeToken();
                                    getAllBadges();
                                },
                                (err) => {
                                    alerts.error(err);
                                }
                            );
                        }
                    }}
                >
                    Logout{" "}
                </Dropdown.Item>
            </Dropdown.Menu>
        </Popover>
    );
};

const Topbar = ({ onSelect, activeKey, ...props }) => {
    const containerClasses = classNames("page-container", {
        "container-full": true,
    });

    const changeTheme = useMainStore((state) => state.changeTheme);
    const theme = useMainStore((state) => state.theme);
    const user = useBadgesStore((state) => state.user);
    const agentTheme = useMainStore((state) => state.agentTheme);

    const trigger = useRef();
    return (
        <Container className={containerClasses}>
            <Stack
                style={{
                    padding: 20,
                }}
                justifyContent="space-between"
                className="header"
                spacing={8}
            >
                <div></div>
                <Stack spacing={8}>
                    <IconButton
                        icon={
                            <Icon
                                as={
                                    theme === "light"
                                        ? MdOutlineLightMode
                                        : MdOutlineNightlight
                                }
                                style={{ fontSize: 20 }}
                            />
                        }
                        onClick={() => changeTheme()}
                    />

                    <a href={agentTheme.contact_link} target="_blank">
                        <img
                            src={"/assets/images/whatsapp.png"}
                            className="rs-avatar-image"
                            height={40}
                        ></img>
                    </a>

                    <Whisper
                        placement="bottomEnd"
                        trigger="click"
                        ref={trigger}
                        speaker={renderAdminSpeaker}
                    >
                        <Avatar
                            size="md"
                            circle
                            src={user.avatar_image}
                            // alt={user.name}
                            style={{ marginLeft: 8 }}
                        />
                    </Whisper>
                </Stack>
            </Stack>
        </Container>
    );
};

export default Topbar;
