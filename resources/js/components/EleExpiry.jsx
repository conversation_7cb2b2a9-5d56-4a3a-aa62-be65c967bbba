import React from "react";
import moment from "moment";

export const sagaTimeNowString = (time) => {
    return moment(time).format("YYYY-MM-DD HH:mm:ss");
};

const EleExpiry = (props) => {
    return (
        <div
            style={{
                fontWeight:
                    props.time == null || moment(props.time) < moment()
                        ? "nomal"
                        : "bold",
                color:
                    props.time == null || moment(props.time) < moment()
                        ? "red"
                        : "green",
            }}
        >
            <span>
                {props.time == null
                    ? "Unregistered"
                    : sagaTimeNowString(props.time)}
            </span>
        </div>
    );
};

export default EleExpiry;
