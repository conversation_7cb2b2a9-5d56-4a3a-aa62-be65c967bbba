import "./App.css";

import React from "react";
import { <PERSON>ton, CustomProvider, Container } from "rsuite";
import ReactDOM from "react-dom/client";
import "rsuite/dist/rsuite.min.css";

import { BrowserRouter } from "react-router-dom";
import MainAgent from "./screens/agent/MainAgent";
import Main1 from "./screens/main/Main1.jsx";

import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useMainStore } from "./store/mainStore";

function App() {
    const theme = useMainStore((state) => state.theme);
    const type = useMainStore((state) => state.type);

    return (
        <BrowserRouter>
            {type == "main" ? (
                <Container>
                    <CustomProvider theme={theme}>
                        <Main1 key={1} />
                    </CustomProvider>
                </Container>
            ) : (
                <Container>
                    <CustomProvider theme={theme}>
                        <MainAgent key={1} />
                    </CustomProvider>
                </Container>
            )}

            <ToastContainer />
        </BrowserRouter>
    );
}

ReactDOM.createRoot(document.getElementById("app")).render(<App />);
