import { create } from "zustand";
import { setTheme, getTheme } from "../utils/auth";

export const useMainStore = create((set, get) => ({
    pageType: "home",

    agentTheme: {
        logo_url: "/logo.png",
        background_url: "/logo.png",
        color_main_1: "rgb(31 41 55)",
        agent_code: "FHOST",
        theme_style: "light",
    },
    getAgentTheme: async () => {
        const metaTag = document.querySelector('meta[name="web_theme"]');
        const content = metaTag.getAttribute("content");
        const webTheme = JSON.parse(content);

        set((state) => {
            if (webTheme != null) {
                return {
                    theme:
                        getTheme() ??
                        webTheme.theme_style ??
                        state.agentTheme.theme_style ??
                        "light",
                    type:  window.location.href.includes('localdrcar') ? "main" : webTheme.type, //main agent
                    agentTheme: {
                        ...state.agentTheme,
                        logo_url:
                            webTheme.logo_url ?? null,
                        background_url:
                            webTheme.background_url ??
                            state.agentTheme.background_url,
                        color_main_1:
                            webTheme.color_main_1 ??
                            state.agentTheme.color_main_1,
                        background_color:
                            webTheme.background_color ??
                            state.agentTheme.background_color,
                        theme_style:
                            webTheme.theme_style ??
                            state.agentTheme.theme_style,
                        title: webTheme.title ?? state.agentTheme.title,
                        agent_code:
                            webTheme.agent_code ?? state.agentTheme.agent_code,
                        contact_link:
                            webTheme.contact_link ??
                            state.agentTheme.contact_link,
                    },
                };
            }
        });
    },
    changeTheme: async () => {
        const theme = getTheme() ?? get().agentTheme.theme_style ?? "light";
        if (theme == "light") {
            set({ theme: "dark" });
            setTheme("dark");
        } else {
            set({ theme: "light" });
            setTheme("light");
        }
    },
    changeThemeToDark: async () => {
        set({ theme: "dark" });
    },
    changeThemeToLight: async () => {
        set({ theme: "light" });
    },

    changePageType: (type) => {
      
        set((state) => ({ pageType: type }));

        if (get().type == "main") {
            if (
                get().pageType == "home" ||
                get().pageType == "products" ||
                get().pageType == "forum"
            ) {
                set({ theme: "dark" });
                return "dark";
            } else {
                set({ theme: "light" });
                return "light";
            }
        } else {
            return get().theme;
        }
    },
    backToHome: () => set(() => ({ pageType: "home" })),
}));
