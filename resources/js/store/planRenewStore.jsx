import { create } from "zustand";
import { RepositoryRemote } from "../services";
import { alerts } from "../utils/alerts";

export const planRenewStore = create((set, get) => ({
    planServices: [],
    loadingPlanServices: true,
    dataRenewalHistories: {},
    loadingRenewalHistories: true,
    loadingCheckVoucher: false,
    plansSelected: [],
    paymentMethods: [],
    loadingPaymentMethods: false,


    getRenewalHistories: async () => {
        set({ loadingRenewalHistories: true });
        try {
            const response =
                await RepositoryRemote.planRenew.renewalHistories();
            set({ dataRenewalHistories: response.data.data });
        } catch (error) {
            alerts.error(error?.response?.data?.msg || "ERROR");
        }
        set({ loadingRenewalHistories: false });
    },
    getPaymentMethods: async () => {
        set({ loadingPaymentMethods: true });
        try {
            const response =
                await RepositoryRemote.planRenew.getPaymentMethods();
            set({ paymentMethods: response.data.data });
        } catch (error) {
            alerts.error(error?.response?.data?.msg || "ERROR");
        }
        set({ loadingPaymentMethods: false });
    },

    selectPlan: (plan, name) => {
        var name = name.toUpperCase() + " " + plan.month + " MONTH";
        plan.name = name;
        get().addOrRemovePlan(plan);
    },

    addOrRemovePlan: (plan) => {
        var prevPlans = get().plansSelected;

        const newPlans = () => {
            const exists = prevPlans.some((item) => item.id === plan.id);
            if (exists) {
                return prevPlans.filter((item) => item.id !== plan.id);
            } else {
                return [...prevPlans, plan];
            }
        };
        get().setPlansSelected(newPlans());
    },

    setPlansSelected: (plansSelected) => {
        set({ plansSelected: plansSelected });
    },

    getPlanServices: async (agencyCode, voucherCode, loading = true) => {
        var plansSelected = get().plansSelected;

        if (loading) {
            set({ loadingPlanServices: true });
        }

        try {
            const response = await RepositoryRemote.planRenew.planServices(
                agencyCode,
                voucherCode
            );
            const dataRes = response.data.data;
            for (const ele of dataRes) {
                for (var item of ele.plans ?? []) {
                    const index = plansSelected.findIndex(
                        (plan) => plan.id === item.id
                    );
                    if (index >= 0) {
                        var name =
                            ele.name.toUpperCase() +
                            " " +
                            item.month +
                            " MONTH";
                        item.name = name;

                        plansSelected[index] = item;
                    }
                }
            }

            set({
                planServices: response.data.data,
                plansSelected: plansSelected,
            });
        } catch (error) {
            alerts.error(error?.response?.data?.msg || "ERROR");
        }
        set({ loadingPlanServices: false });
    },
    checkVoucher: async (voucherCode, onOk) => {
        set({ loadingCheckVoucher: true });
        try {
            const response = await RepositoryRemote.planRenew.checkVoucher(
                voucherCode
            );
            onOk();
        } catch (error) {
            alerts.error(error?.response?.data?.msg || "ERROR");
        }
        set({ loadingCheckVoucher: false });
    },

}));
