import { create } from "zustand";
import { RepositoryRemote } from "../services";
import { alerts } from "../utils/alerts";

export const useBadgesStore = create((set) => ({
    badges: {},
    loading: true,
    errorBadges: false,
    user: null,
    status_server: null,
    getAllBadges: async (onSuccess = () => {}, onFail = () => {}) => {
        set({ errorBadges: false });
        set({ loading: true });
        try {
            const response = await RepositoryRemote.badges.getAllBadges();
            if (response.data.code !== 200) Promise.reject(response.data.msg);
            set({
                badges: response.data.data,
                user: response.data?.data?.user,
                status_server: response.data?.data?.status_server,
            });
            onSuccess(response.data.data);
        } catch (error) {
            set({ errorBadges: true });

            alerts.error(error?.response?.data?.msg || "ERROR");
            onFail("ERROR");
        }
        set({ loading: false });
    },
}));
