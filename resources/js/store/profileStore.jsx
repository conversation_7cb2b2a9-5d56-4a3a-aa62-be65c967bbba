import { create } from "zustand";
import { RepositoryRemote } from "../services";

export const useProfileStore = create((set) => ({
    loadingUpdate: false,
    loadingChange: false,
    updateProfile: async (form, onSuccess, onFail) => {
        try {
            set({ loadingUpdate: true });
            const response = await RepositoryRemote.profile.updateProfile(form);
            onSuccess(response.data.data);
        } catch (error) {
            onFail(error?.response?.data?.msg || "ERROR!");
        }
        set({ loadingUpdate: false });
    },

    changePassword: async (form, onSuccess, onFail) => {
        try {
            set({ loadingChange: true });
            const response = await RepositoryRemote.profile.changePassword(form);
            onSuccess(response.data.data);
        } catch (error) {
            onFail(error?.response?.data?.msg || "ERROR!");
        }
        set({ loadingChange: false });
    },
}));


