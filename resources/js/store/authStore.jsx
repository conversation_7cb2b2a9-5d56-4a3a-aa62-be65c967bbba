import { create } from "zustand";
import { RepositoryRemote } from "../services";

export const useAuthStore = create((set) => ({
    tokenInfo: {},
    loading: false,
    loadingLogout: false,
    loadingSignUp: false,
    loadingResetPassword: false,
    loadingCheckSecurity: false,
    loadingResetDevices: false,

    login: async (form, onSuccess, onFail) => {
        try {
            set({ loading: true });
            const response = await RepositoryRemote.auth.login(form);
            set({ tokenInfo: response.data.data });
            onSuccess(response.data.data);
        } catch (error) {
            console.log(error);
            onFail(error?.response?.data?.msg || "ERROR!");
        }
        set({ loading: false });
    },
    signup: async (form, onSuccess, onFail) => {
        try {
            set({ loadingSignUp: true });
            const response = await RepositoryRemote.auth.signup(form);
            set({ tokenInfo: response.data.data });
            onSuccess(response.data.data);
        } catch (error) {
            console.log(error);
            onFail(error?.response?.data?.msg || "ERROR!");
        }
        set({ loadingSignUp: false });
    },
    logout: async (onSuccess, onFail) => {
        try {
            set({ loadingLogout: true });
            await RepositoryRemote.auth.logout();
            onSuccess();
        } catch (error) {
            onFail(error?.response?.data?.msg || "ERROR!");
        }
        set({ loadingLogout: false });
    },
    resetPassword: async (form, onSuccess, onFail) => {
        try {
            set({ loadingResetPassword: true });
            await RepositoryRemote.auth.sendEmailReset(form);
            onSuccess();
        } catch (error) {
            onFail(error?.response?.data?.msg || "ERROR!");
        }
        set({ loadingResetPassword: false });
    },
    checkSecurity: async (columnExpiry, onSuccess, onFail) => {
        try {
            set({ loadingCheckSecurity: true });
            await RepositoryRemote.auth.checkSecurity(columnExpiry);
            onSuccess();
        } catch (error) {
            onFail(error?.response?.data?.msg_code || "ERROR!");
        }
        set({ loadingCheckSecurity: false });
    },

    resetDevice: async (form, onSuccess, onFail) => {
        try {
            set({ loadingResetDevices: true });
            await RepositoryRemote.auth.sendEmailReset(form);
            onSuccess();
        } catch (error) {
            onFail(error?.response?.data?.msg || "ERROR!");
        }
        set({ loadingResetDevices: false });
    },
}));
