import { create } from "zustand";
import { RepositoryRemote } from "../services";
import { alerts } from "../utils/alerts";

export const devicesStore = create((set) => ({
    devices: [],
    loadingDevices: true,

    dataPlanDevices: {},
    loadingPlanDevices: true,
    loadingIdRemoveCurrent: null,
    loadingIdResetCurrent: null,

    removeDevice: async (deviceLoginId,  onOk) => {
        set({ loadingIdRemoveCurrent: deviceLoginId });
        try {
            const response =
                await RepositoryRemote.deviceLogin.removeDevice(deviceLoginId);

            onOk()
        } catch (error) {
            alerts.error(error?.response?.data?.msg || "ERROR");
        }
        set({ loadingIdRemoveCurrent: null });
    },


    resetDevice: async (deviceLoginId,  onOk) => {
        set({ loadingIdResetCurrent: deviceLoginId });
        try {
            const response =
                await RepositoryRemote.deviceLogin.resetDevice(deviceLoginId);

            onOk()
        } catch (error) {
            alerts.error(error?.response?.data?.msg || "ERROR");
        }
        set({ loadingIdResetCurrent: null });
    },

    getDevices: async () => {
        set({ loadingDevices: true });
        try {
            const response =
                await RepositoryRemote.deviceLogin.getDevices();

            set({ devices: response.data.data });
        } catch (error) {
            alerts.error(error?.response?.data?.msg || "ERROR");
        }
        set({ loadingDevices: false });
    },

    getListPlanDevices: async (loading = true) => {
        if (loading) {
            set({ loadingPlanDevices: true });
        }
        try {
            const response = await RepositoryRemote.deviceLogin.getListPlanDevices();
            set({
                dataPlanDevices: response.data.data,
            });
        } catch (error) {
            alerts.error(error?.response?.data?.msg || "ERROR");
        }
        set({ loadingPlanDevices: false });
    },
  
}));
