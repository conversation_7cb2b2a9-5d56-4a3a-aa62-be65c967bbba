export const getDomainWithSubdomain = (subdomain = "main") => {
    const currentOrigin = window.location.origin;
    const protocol = window.location.protocol;
    const currentDomain = window.location.hostname;
    const domainParts = currentDomain.split(".");

    if (domainParts.length > 2) {
        domainParts.shift();
    }
    const baseDomain = domainParts.join(".");
    const newDomain = `https://${subdomain}.${baseDomain}`;

    return newDomain;
};

export const constants = {
    MESSAGE_ERROR: "Có lỗi xảy ra",
    API_URL: window.location.origin + "/api",
        // window.location.origin.includes("localhost") ||
        // window.location.origin.includes("127")
        //     ? getDomainWithSubdomain() + "/api"
        //     : getDomainWithSubdomain() + "/api",
};
