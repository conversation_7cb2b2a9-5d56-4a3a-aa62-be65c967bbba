import { Icon } from "@rsuite/icons";
import BlockIcon from "@rsuite/icons/Block";
import { getDomainWithSubdomain } from ".";

export const boards = [
    {
        id: 1,
        title: "ALL Data EU",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("app-alldata"),
        bgColor: "bg-gradient-black",
        image: "alldata.png",
        expiry_column: "expiry_alldata",
        status_server_column: "status_alldata_eu",
    },
    {
        id: 2,
        title: "ALL Data US",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("my-alldata"),
        bgColor: "bg-gradient-black",
        image: "alldata_us.png",
        expiry_column: "expiry_alldata",
        status_server_column: "status_alldata_us",
    },
    {
        id: 3,
        title: "GDS KDS",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("kdsgds"),
        bgColor: "bg-gradient-black",
        image: "kdsgds.png",
        expiry_column: "expiry_kdsgds",
        status_server_column: "status_kds_gds",
    },

    {
        id: 4,
        title: "Mitchell Prodemand",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("mitchell-prodemand"),
        bgColor: "bg-gradient-black",
        image: "mitchell.png",
        expiry_column: "expiry_mitchell_prodemand",
        status_server_column: "status_mitchell_prodemand",
    },
    {
        id: 5,
        title: "ATSG 2017",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("atsg"),
        bgColor: "bg-gradient-black",
        image: "atsg.png",
        expiry_column: null,
        status_server_column: "status_atsg",
    },
    {
        id: 8,
        title: "Identifix",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("identifix"),
        bgColor: "bg-gradient-black",
        image: "identifix.png",
        expiry_column: "expiry_identifix",
        status_server_column: "status_identifix",
    },
    {
        id: 6,
        title: "Haynes Pro",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("haynespro"),
        bgColor: "bg-gradient-black",
        image: "haynespro.png",
        expiry_column: "expiry_haynespro",
        status_server_column: "status_haynespro",
    },
    {
        id: 7,
        title: "Haynes Pro Truck",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("haynespro-truck"),
        bgColor: "bg-gradient-black",
        image: "haynespro.png",
        expiry_column: "expiry_haynespro_truck",
        status_server_column: "status_haynespro_truck",
    },

    {
        id: 9,
        title: "Partslink24",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("partslink24"),
        bgColor: "bg-gradient-black",
        image: "partslink24.png",
        expiry_column: "expiry_partslink24",
        status_server_column: "status_partslink24",
    },
    {
        id: 10,
        title: "ETKA",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("etka"),
        bgColor: "bg-gradient-black",
        image: "etka.png",
        expiry_column: null,
        status_server_column: "status_etka",
    },
    {
        id: 11,
        title: "TecDoc",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("tecdoc"),
        bgColor: "bg-gradient-black",
        image: "tecdoc.png",
        expiry_column: "expiry_tecdoc",
        status_server_column: "status_tecdoc",
    },
    {
        id: 12,
        title: "Ford PTS",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("ford-pts"),
        bgColor: "bg-gradient-black",
        image: "ford_pts.png",
        expiry_column: "expiry_ford_pts",
        status_server_column: "status_ford_pts",
    },
    {
        id: 13,
        title: "Toyota",
        icon: <Icon as={BlockIcon} />,
        to: getDomainWithSubdomain("toyota"),
        bgColor: "bg-gradient-black",
        image: "toyota.png",
        expiry_column: "expiry_toyota",
        status_server_column: "status_toyota_tis",
    },
];
