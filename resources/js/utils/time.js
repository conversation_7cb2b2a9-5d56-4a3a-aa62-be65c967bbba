import moment from "moment";

export const displayTimeAgoFromTime = (time) => {
    const diffInHours = moment().diff(moment(time), "hours");
    let timeValue = 0;
    let timeUnit = "";

    if (diffInHours < 1) {
        const diffInMinutes = moment().diff(moment(time), "minutes");
        timeValue = diffInMinutes;
        timeUnit = "min";
    } else if (diffInHours < 24) {
        timeValue = diffInHours;
        timeUnit = "hour";
    } else if (diffInHours >= 24 && diffInHours < 24 * 7) {
        timeValue = Math.floor(diffInHours / 24);
        timeUnit = "day";
    } else if (diffInHours >= 24 * 7 && diffInHours < 24 * 30) {
        timeValue = Math.floor(diffInHours / (24 * 7));
        timeUnit = "week";
    } else if (diffInHours >= 24 * 30 && diffInHours < 24 * 12 * 30) {
        timeValue = Math.floor(diffInHours / (24 * 30));
        timeUnit = "month";
    } else {
        timeValue = Math.floor(diffInHours / (24 * 365));
        timeUnit = "year";
    }

    const timeAgo = `${timeValue} ${timeUnit}`;
    return timeAgo === "0 min" ? "now" : `${timeAgo} ago`;
};
