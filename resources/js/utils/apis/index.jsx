import axios from "axios";
import { constants as c, getDomainWithSubdomain } from "../constants";
import { getToken, removeToken } from "../../utils/auth";
import { getDeviceData } from "../device";

const exceptPrefix = ["/login", "/register"];
const checkEndPoint = (endpoint) => {
    for (const prefix of exceptPrefix) {
        if (endpoint.includes(prefix)) {
            return true;
        }
    }
    return false;
};

export function setCookie(name, value, days = 30) {
    let expires = "";
    if (days) {
        const date = new Date();
        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000); // Thời gian hết hạn theo số ngày
        expires = "; expires=" + date.toUTCString();
    }

    const domain = getDomainWithSubdomain()
        .replace(/^www\./, "")
        .replace("main.", "")
        .replace("https://", "")
        .replace("http://", "");
    document.cookie =
        name +
        "=" +
        (value || "") +
        expires +
        "; path=/; domain=." +
        domain +
        "; Secure; SameSite=Lax";
}


export const callApi = (endPoint, method, body, type = "") => {
    if (checkEndPoint(endPoint) === false) {
        axios.interceptors.request.use(
            (config) => {
                const token = getToken();
                if (token) {
                    config.headers["dr-token"] = token;

                    setCookie("dr-token", token);
                }
                var deviceData = getDeviceData();
                config.headers["dr-device-id"] = deviceData["dr-device-id"];
                config.headers["dr-modelx"] = deviceData["dr-modelx"];
                config.headers["dr-platform"] = deviceData["dr-platform"];
                config.headers["dr-time-now-device"] =
                    deviceData["dr-time-now-device"];
                config.headers["platform-app-local"] =
                    deviceData["dr-platform"];

                setCookie("dr-device-id", deviceData["dr-device-id"]);
                setCookie("dr-modelx", deviceData["dr-modelx"]);
                setCookie("dr-platform", deviceData["dr-platform"]);
                setCookie("platform-app-local", deviceData["dr-platform"]);
                setCookie("dr-time-now-device",deviceData["dr-time-now-device"]);

                const protocol = window.location.protocol;  
                const host = window.location.host;         
                const fullDomain = protocol + '//' + host;
                setCookie("dr-home-domain",fullDomain);

                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );
        axios.interceptors.response.use(
            (response) => {
                return response;
            },
            (error) => {
                if (error?.response?.data?.code === 404) {
                    // window.location.replace("/khong-tim-thay-trang");
                } else if (error?.response?.data?.code === 401) {
                    removeToken();

                    if (!checkEndPoint(endPoint)) {
                        //  history.push("/login");
                    }
                }
                return Promise.reject(error);
            }
        );
    }

    return axios({
        method,
        url: `${c.API_URL}${endPoint}`,
        data: body,
        headers: {
            "Content-Type":
                type === "form-data"
                    ? "multipart/form-data"
                    : "application/json",
        },
    });
};
