export const getDeviceData = () => {
    var deviceId = navigator.vendor + navigator.userAgent;
    deviceId = deviceId
        .replace(/;/g, "")
        .replace(/ /g, "")
        .replace(/\./g, "")
        .replace(/,/g, "");

    var drPlatform = "web";
    var drModel = "Web browser";
    var drTimeNowDevice = new Date().toISOString();

    return {
        "dr-platform": drPlatform,
        "dr-device-id": deviceId,
        "dr-modelx": drModel,
        "dr-time-now-device": drTimeNowDevice,
    };
};


export function isSafariOnIphone() {
    const userAgent = navigator.userAgent.toLowerCase();
    return (
      /iphone/.test(userAgent) &&
      /safari/.test(userAgent) &&
      !/chrome/.test(userAgent)
    );
  }