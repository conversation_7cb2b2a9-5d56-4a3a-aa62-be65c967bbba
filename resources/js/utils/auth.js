import { setCookie } from "./apis";

const tokenKey = "dr-token";
const themeKey = "theme";
export function getToken() {
    return localStorage.getItem(tokenKey);
}

export function setToken(token) {
    return localStorage.setItem(tokenKey, token);
}

export function removeToken() {
    setCookie(tokenKey, "");
    return localStorage.removeItem(tokenKey);
}

export function getTheme() {
    return localStorage.getItem(themeKey);
}

export function setTheme(va) {
    return localStorage.setItem(themeKey, va);
}
