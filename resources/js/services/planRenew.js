import { callApi } from "../utils/apis/index";
const planServices = (agency_code = "", code_voucher = "") => {
    return callApi(
        `/user/plan_services?agency_code=${agency_code}&code_voucher=${code_voucher}`,
        "get"
    );
};
const renewalHistories = (page = 1) => {
    return callApi(`/user/renewal_histories?page=${page}`, "get");
};
const checkVoucher = (code_voucher) => {
    return callApi(`/user/voucher/check_voucher`, "post", {
        code_voucher: code_voucher,
    });
};

const getPaymentMethods = () => {
    return callApi(`/user/payment_methods`, "get");
};


export const planRenew = {
    planServices,
    renewalHistories,
    checkVoucher,
    getPaymentMethods,

};
