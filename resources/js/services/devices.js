import { callApi } from "../utils/apis/index";

const getDevices = (page = 1) => {
    return callApi(`/user/device_login`, "get");
};


const getListPlanDevices = () => {
    return callApi(`/user/device_login/list_plan_devices`, "get");
};

const removeDevice = (device_login_id) => {
    return callApi(`/user/device_login/delete_device`, "delete", {device_login_id:device_login_id});
};

const resetDevice = (device_login_id) => {
    return callApi(`/user/device_login/reset_device`, "post", {device_login_id:device_login_id});
};


export const deviceLogin = {
    getDevices,
    getListPlanDevices,
    resetDevice,
    removeDevice
};
