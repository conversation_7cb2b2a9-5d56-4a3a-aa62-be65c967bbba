import { callApi } from "./../utils/apis/index";
const login = (form) => {
    return callApi(`/user/login`, "post", form);
};
const signup = (form) => {
    return callApi(`/user/register`, "post", form);
};
const logout = (form) => {
    return callApi(`/user/logout`, "post", form);
};
const sendEmailReset = (form) => {
    return callApi(`/user/send_email_reset`, "post", form);
};
const checkSecurity = (columnExpiry) => {
    return callApi(`/user/check_security?column_expiry=${columnExpiry}`, "get");
};

export const auth = {
    signup,
    login,
    logout,
    sendEmailReset,
    checkSecurity,
};
