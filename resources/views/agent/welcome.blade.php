<!DOCTYPE html>
<base href="/">
<meta charset="UTF-8">
<meta content="IE=Edge" http-equiv="X-UA-Compatible">
<meta name="apple-mobile-web-app-capable" content="yes" >
<meta name="apple-mobile-web-app-status-bar-style" content="black" >
<meta name="apple-mobile-web-app-title" content="{{$title}}" >
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<link rel="apple-touch-icon" href="{{$favicon_url}}"/>
<link rel="icon" type="image/png" href="{{$favicon_url}}">
<meta property="og:image" content="{{$favicon_url}}">
<title>{{$title}}</title>
<link rel="manifest" href="/manifest.json"/>
<meta name="description" content="{{$description}}">
<meta name=web_theme content="{{$web_theme}}" >
<meta name="robots" content="noindex">
<meta name="googlebot" content="noindex">

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
<script>
  window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());

gtag('config', 'G-KXWTNY3G2J');
</script>
<script>
  document.addEventListener('gesturestart', function (e) {
      e.preventDefault();
  });
  document.addEventListener('touchmove', function (e) {
      if (e.scale !== undefined && e.scale !== 1) {
          e.preventDefault();
      }
  }, { passive: false });
</script>


<head>
    <div id="app"></div>

    @viteReactRefresh
    @vite('resources/js/app.jsx')
</head>