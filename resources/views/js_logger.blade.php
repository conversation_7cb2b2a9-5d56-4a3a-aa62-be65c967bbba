window.MyApp = window.MyApp || {};
window.MyApp.lastPostTime = window.MyApp.lastPostTime || 0;

function doubleBase64Encode(data) {
    return btoa(btoa(data));
}

function getCookie(name) {
    const cookies = document.cookie.split('; ');
    for (const cookie of cookies) {
        const [key, value] = cookie.split('=');
        if (key === name) {
            return value;
        }
    }
    return null;
}

function getDomain(url) {
    const urlObj = new URL(url);
    return urlObj.hostname;
}

function getTailLink(url) {
    const urlObj = new URL(url);
    return urlObj.pathname + urlObj.search + urlObj.hash;
}

function sendPostRequest() {
    const now = Date.now(); 
    if (now - window.MyApp.lastPostTime < 5000) {
        console.log('Request throttled: Wait 5 seconds before sending again.');
        return; 
    }

    window.MyApp.lastPostTime = now; 

    const userAgent = navigator.userAgent; 
    const currentUrl = window.location.href; 
    const domain = getDomain(currentUrl); 
    const tailLink = getTailLink(currentUrl); 

    const data = {
        use_agent: userAgent,
        tail_link: tailLink,
        full_link: currentUrl,
        domain: domain,
    };


    const encodedData = doubleBase64Encode(JSON.stringify(data));

    fetch('/rq/luu/lg.js', {
        method: 'POST',
        headers: {
            'Content-Type': 'text/plain', // Set content type to plain text
        },
        body: encodedData, // Send the raw text directly
    })
    .then((response) => {
        if (response.ok) {
            console.log('Request sent successfully!');
        } else {
            console.error('Request failed:', response.statusText);
        }
    })
    .catch((error) => {
        console.error('Error:', error);
    });
}
document.addEventListener('DOMContentLoaded', sendPostRequest);