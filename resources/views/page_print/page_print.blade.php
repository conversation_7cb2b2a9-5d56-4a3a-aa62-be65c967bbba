<!DOCTYPE html>
<html>

<head>
    <title>Print Image</title>
</head>

<body>
    <!-- HTML content -->

    <script>
        function print() {
            var linkPrint = '{{ $linkPrint }}';
            // <PERSON><PERSON><PERSON> yêu cầu in <PERSON>n khi trang đ<PERSON> tải
            fetch(linkPrint)
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const iframe = document.createElement('iframe');
                    iframe.style.display = 'none';
                    iframe.src = url;
                    document.body.appendChild(iframe);

                    iframe.onload = function() {
                        window.frames[0].focus();
                        window.frames[0].print();
                        window.URL.revokeObjectURL(url);
                    };
                });
        }
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            print()
        });
    </script>

    <button id="printButton" style="   position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);" onclick="print()">Print</button>

</body>

</html>