@php


if (!function_exists('domainWidthoutSub'))
{
function domainWidthoutSub()
{

$host = "$_SERVER[HTTP_HOST]";
$host_names = explode(".", $host);

if (str_contains($host, 'localhost')) {
$bottom_host_name = $host_names[count($host_names) - 1];
} else {
$bottom_host_name = $host_names[count($host_names) - 2] . "." . $host_names[count($host_names) - 1];
}
// $domain = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
return $bottom_host_name;

}
}

if (!function_exists('subAgency'))
{
function subAgency()
{
return app('request')->agency_sub ?? "web";
}
}

if (!function_exists('fullDomainHome'))
{
    function fullDomainHome()
    {
    $cookies = $_COOKIE;
    $drHomeDomain = $cookies['dr-home-domain'] ?? null;
    if($drHomeDomain !=null) {
        return $drHomeDomain;
    } else {
        $subAgency = subAgency();
        $domainWidthoutSub = domainWidthoutSub();
        return "https://$subAgency.$domainWidthoutSub";
    }
    }

}


@endphp

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{$appName}} SECURITY ERROR</title>
    <meta name="robots" content="noindex">
    <meta name="googlebot" content="noindex">
    <link rel="stylesheet" href="/style/main.css">
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-KXWTNY3G2J"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag("js", new Date());

        gtag("config", "G-KXWTNY3G2J");
    </script>
</head>

<body>

    <div class="bg">

        <div class="card">


            <div class="pageWrap">
                <div class="checkmark-circle">
                    <div class="background" style="background:red"></div>
                    <div class="checkmark draw2"></div>
                </div>
            </div>
            <h1 class="msg">{{$msg_code}}</h1>
            <h2 class="submsg">{{$msg}}</h2>
            <h2 class="info">{{$info ?? "..."}}</h2>



            @if(($try_again ?? null) == true)
            <div class="tags">
                <span class="tag" style="font-weight:bold; color:#dd3909"><a href="/">Click try again</a> </span>
            </div>
            @else
            <div class="tags">
                <span class="tag" style="font-weight:bold; color:#dd3909"><a href="{{fullDomainHome()}}">Click to fix!</a> </span>
            </div>
            @endif

        </div>

    </div>


    <script src="/js/main.js"></script>
</body>

</html>