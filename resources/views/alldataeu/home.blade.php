<html lang="en" class="no-js dp_fouc" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title>Vehicle Selection</title>
    <link rel="stylesheet" type="text/css" href="/alldata_override/css/alldata-build-1.0.4535.css?v=1">
    <link rel="stylesheet" href="/css/alldata_us_loading.css?v=65">
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
    <script defer>
        var currentIdManu = ""
        var currentIdModel  = ""
        var currentIdEngine = ""
        var currentListEngine = []
        function clickManus() {
            const yearField = document.getElementById('manufacturerDropdown');
            if(yearField.classList.contains('openDropdown')) {
                yearField.classList.remove('openDropdown');
            } else {
                yearField.classList.add('openDropdown');
            }
        }
        function clickModels() {
            console.log("fff")
            const yearField = document.getElementById('modelDropdown');
            if(yearField.classList.contains('openDropdown')) {
                yearField.classList.remove('openDropdown');
            } else {
                yearField.classList.add('openDropdown');
            }
        }
        function clickYears() {
            const yearField = document.getElementById('yearsDropdown');
            if(yearField.classList.contains('openDropdown')) {
                yearField.classList.remove('openDropdown');
            } else {
                yearField.classList.add('openDropdown');
            }
        }
    </script>



    <script defer>
        function getEventTarget(e) {
                e = e || window.event;
                return e.target || e.srcElement; 
            }

            document.addEventListener("DOMContentLoaded", function(){

                var ul = document.getElementById('manufacturerDropdown');
                ul.onclick = function(event) {
                var target = getEventTarget(event);
                var idManu = target.id
                var textContent = target.textContent
                currentIdManu = idManu

                document.querySelector("#manufacturerName").textContent = textContent
              

            var ul1 = document.getElementById('modelDropdown');
            ul1.innerHTML = '';
            var ul1 = document.getElementById('yearsDropdown');
            ul1.innerHTML = '';
            var ul1 = document.getElementById('listOfEngines');
            ul1.innerHTML = '';

            document.querySelector("#modelName").textContent = "Model"
            document.querySelector("#yearName").textContent = "Year"
                
                $(".loader222").show();
                $(".loader222").hide();
                $(".loader222").show();
                $(".loader222").hide();
                $(".loader222").show();
                
                fetch("/alldata/vehicle/"+idManu)
                    .then(response => response.json())
                    .then(data => {
                        $(".loader222").hide();
                        var modelList = data
                        console.log(modelList)

                        const ul = document.getElementById('modelDropdown');
                        ul.innerHTML = '';
                        modelList.forEach(item => {
                        const li = document.createElement('li');
                        const a = document.createElement('a');

                        a.textContent = item.name;
   
                        if(item.frequency == 0) {
                            a.setAttribute('class', 'zeroFrequency');
                        }
                        a.id = item.id;
                        li.id = item.id;
                     
                        li.appendChild(a);
                        ul.appendChild(li);
                        });

                        const widthPer35 = document.getElementsByClassName('widthPer35')[0];
                        widthPer35.classList.remove('dropDownDisabled');

                        const widthPer35modelDropdown = document.querySelector('.widthPer35 #modelDropdown');
                        widthPer35modelDropdown.classList.add('openDropdown');

                    })
                    .catch(error =>  $(".loader222").hide());
            };
});
            
    </script>

    <script defer>
        function getEventTarget(e) {
            e = e || window.event;
            return e.target || e.srcElement; 
        }

        document.addEventListener("DOMContentLoaded", function(){

            var ul = document.getElementById('modelDropdown');
            ul.onclick = function(event) {
            var target = getEventTarget(event);
            var idModel = target.id
            var textContent = target.textContent
            currentIdModel = idModel

            var ul1 = document.getElementById('yearsDropdown');
            ul1.innerHTML = '';
            var ul1 = document.getElementById('listOfEngines');
            ul1.innerHTML = '';

  
            document.querySelector("#yearName").textContent = "Year"
            document.querySelector("#modelName").textContent = textContent
          
            $(".loader222").show();
            fetch("/alldata/vehicle/"+currentIdManu + "/" + currentIdModel)
                .then(response => response.json())
                .then(data => {
                    $(".loader222").hide()
                    var modelList = data
                    console.log(modelList)
                    currentListEngine = modelList
                    const ul = document.getElementById('yearsDropdown');
                    ul.innerHTML = '';
                    var index = 0;
                    modelList.forEach(item => {
                    const li = document.createElement('li');
                    const a = document.createElement('a');

                    a.textContent = ((item.metadata.Year_from == item.metadata.Year_to) ? item.metadata.Year_to  :  ( item.metadata.Year_from  + " - " + item.metadata.Year_to)) + " | " + item.name;

                    if(item.frequency == 0) {
                        a.setAttribute('class', 'zeroFrequency');
                    }
                    a.id = index;
                    li.id = index;
                    
                    li.appendChild(a);
                    ul.appendChild(li);
                    index++;
                    });

                    const widthPer35 = document.querySelectorAll('#vehicleSelectionContainer #selector')[2];
                    widthPer35.classList.remove('dropDownDisabled');

                    const widthPer35modelDropdown = document.querySelector('#vehicleSelectionContainer #selector #yearsDropdown');
                    widthPer35modelDropdown.classList.add('openDropdown');


                    const myList = document.getElementById('yearsDropdown');
                    const listItems = Array.from(myList.getElementsByTagName('li'));

                    listItems.sort(function(a, b) {
                    if (a.innerText < b.innerText) {
                        return 1;
                    } else if (a.innerText > b.innerText) {
                        return -1;
                    } else {
                        return 0;
                    }
                    });

                    listItems.forEach(function(item) {
                    myList.appendChild(item);
                    });

                })
                .catch(error => $(".loader222").hide());
        };
});
        
    </script>

    <script defer>
        function getEventTarget(e) {
            e = e || window.event;
            return e.target || e.srcElement; 
        }

        document.addEventListener("DOMContentLoaded", function(){

            var ul = document.getElementById('yearsDropdown');
            ul.onclick = function(event) {
            var target = getEventTarget(event);
            var idYear = target.id
            var textContent = target.textContent
            currentIdEngine = idYear
          

            var ul1 = document.getElementById('listOfEngines');
            ul1.innerHTML = '';

            document.querySelector("#yearName").textContent = textContent

            var currentEng = currentListEngine[currentIdEngine]

            const ul = document.getElementById('listOfEngines');
            ul.innerHTML = '';

            var year = currentEng.metadata.Year_from
            var name = currentEng.name


            currentListEngine.forEach(item => {
                if(item.metadata.Year_from == year && item.name == name) {

                    item.associated.forEach(itemSub => {
                                const li = document.createElement('li');
                                const a = document.createElement('a');
                                const span1 = document.createElement('span');
                                const span2 = document.createElement('span');

                                span1.textContent = item.name + " " + item.metadata.Power_kw + " / " + item.metadata.Power_hp + " " + item.metadata.Fuel + " " + item.metadata.Capacity ;
                                span2.textContent = itemSub.name;

                            
                                a.id = item.id;
                        
                                li.setAttribute('class', "vehicleListElement")
                                a.setAttribute('class', "engineElement")
                                a.setAttribute('href', `/alldata/vehicle/home#/vehicle/${item.id}/engine/${itemSub.id}/model/${currentIdModel}/manufacturer/${currentIdManu}`)

                                span1.id ="vehicleDescription"
                                span2.id ="engineCode"

                                span1.setAttribute('class', "floatLeft widthPer68")
                                span2.setAttribute('class', "floatLeft widthPer30")

                                a.appendChild(span1);
                                a.appendChild(span2);

                                li.appendChild(a);
                                ul.appendChild(li);
                    })
                   
                  
                }

            })

         
            console.log(currentEng)
         
        };
});
        
    </script>

</head>

<body>
    <div class="loader222" style="display: none;"></div>
    <div id="header">
        <div id="logo"> <a href="/alldata/">&nbsp;</a> </div>
        <div id="header-right">
            <div id="hr-container" class="menu_container">
                <div id="trialContainer" tabindex="" class="dp_container" style="display: inline-block;"> </div>
                <div id="userAcct" style="display: block" class="dp_container">
                    <div tabindex="" id="welcome_bar" class="dp_container" style="display: inline-block;">
                        <a class="dp_toggle" style="padding-right: 40px; margin-right: 35px">
                            <span class="icon-toolbar icon-profile">&nbsp;</span><span
                                class="dp_label">{{$email}}</span></a>
                        <div class="dp_options" style="top: 34px;">
                            <ul class="dp_options_inner"> </ul>
                            <ul class="dp_options_inner black">
                                <li class=""> <a href="/alldata/user/profile">Profile</a></li>
                                <li class=""> <a href="/alldata/user/settings">Users</a></li>
                                <li class=""><a id="logout" href="/alldata/logout.do">Sign out</a></li>
                            </ul>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <div id="mBar">
        <div>
            <ul class="bMenu">
                <li id="liVehicles">
                    <a href="/alldata/vehicle/" class="selected">Vehicles</a>
                </li>

            </ul>
        </div>
        <div id="search">&nbsp;</div>
    </div>

    <div id="bar">
        <div class="left">
            <div class="sectionTitle"> <span class="left"> <span class="title">Vehicle Selection</span> </span> </div>
        </div>

    </div>

    <div id="application" class="noPath">
        <div id="widgetContainer">
            <div class="vehicleContainer">

                <div id="vehicleSelectionContainer">
                    <div tabindex="0" class="dropDown">
                        <div onclick="clickManus()" id="selector" class="dropDownList widthPer20"><label
                                id="manufacturerName">Manufacturer</label><span class="iconDropDownList"></span>
                            <ul id="manufacturerDropdown" class="openDropdown">
                                <li><a id="Manufacturer_2">Alfa-Romeo</a></li>
                                <li><a id="Manufacturer_5">Audi</a></li>
                                <li><a id="Manufacturer_16">BMW</a></li>
                                <li><a id="Manufacturer_21">Citroen</a></li>
                                <li><a id="Manufacturer_100_CUPRA">Cupra</a></li>
                                <li><a id="Manufacturer_139">Dacia</a></li>
                                <li><a id="Manufacturer_35">Fiat</a></li>
                                <li><a id="Manufacturer_36">Ford</a></li>
                                <li><a id="Manufacturer_45">Honda</a></li>
                                <li><a id="Manufacturer_183">Hyundai</a></li>
                                <li><a id="Manufacturer_55">Jaguar</a></li>
                                <li><a id="Manufacturer_184">Kia</a></li>
                                <li><a id="Manufacturer_64">Lancia</a></li>
                                <li><a id="Manufacturer_65">Land-Rover</a></li>
                                <li><a id="Manufacturer_66" class="zeroFrequency">Lexus</a></li>
                                <li><a id="Manufacturer_70">Mazda</a></li>
                                <li><a id="Manufacturer_74">Mercedes-Benz</a></li>
                                <li><a id="Manufacturer_1523">Mini</a></li>
                                <li><a id="Manufacturer_80">Nissan</a></li>
                                <li><a id="Manufacturer_84">Opel</a></li>
                                <li><a id="Manufacturer_88">Peugeot</a></li>
                                <li><a id="Manufacturer_93">Renault</a></li>
                                <li><a id="Manufacturer_100">Seat</a></li>
                                <li><a id="Manufacturer_106">Skoda</a></li>
                                <li><a id="Manufacturer_1138">Smart</a></li>
                                <li><a id="Manufacturer_115">Toyota</a></li>
                                <li><a id="Manufacturer_117">Vauxhall</a></li>
                                <li><a id="Manufacturer_125">Volvo</a></li>
                                <li><a id="Manufacturer_121">VW</a></li>
                            </ul>
                        </div>
                    </div>
                    <div tabindex="0" class="dropDown">
                        <div onclick="clickModels()" id="selector" class="dropDownList widthPer35 dropDownDisabled">
                            <label id="modelName">Model</label><span></span>
                            <ul id="modelDropdown"> </ul>
                        </div>
                    </div>
                    <div tabindex="0" class="dropDown">
                        <div onclick="clickYears()" id="selector" style="width:37%"
                            class="dropDownList dropDownDisabled"><label id="yearName">Year</label><span></span>
                            <ul id="yearsDropdown"> </ul>
                        </div>
                    </div>

                    <div id="engineListContainer">
                        <div id="engineTitle" class="floatLeft widthPer100 engineTitle">
                            <div class="floatLeft widthPer65">Engine</div>
                            <div class="floatLeft widthPer30">Code</div>
                        </div>
                        <ul id="listOfEngines" class="vehicleSearchList"
                            style="float: left; width: 92%; display: grid;">

                        </ul>
                    </div>

                </div>


            </div>
            {{-- <div id="recentVehiclesContainer">
                <h3 style="font-size:24px; margin-left: 8px; color:#7c7c7c;">Recent Vehicles</h3> <br>
                <ul id="recentVehiclesList">
                    <li><a id="recentVehicleElement" data-vehicleid="Vehicle_137680_F16"
                            data-engineid="NISSEngine_96055" data-modelid="Model_NISSAN_JUKE_F16"
                            data-manufacturerid="Manufacturer_80"><span id="title">Nissan Juke (F16) 2019 -
                                2023</span><br><span>1.0 DIG-T 86 Kw / 117 Hp Petrol 999ccm(HR10DDT)</span></a></li>
                    <li><a id="recentVehicleElement" data-vehicleid="Vehicle_128712" data-engineid="Engine_51994"
                            data-modelid="Model_MINI_F56" data-manufacturerid="Manufacturer_1523"><span id="title">Mini
                                MINI (F56) 3 Door 2015 - 2021</span><br><span>Cooper S 155 Kw / 211 Hp Petrol
                                1998ccm(B48A20A)</span></a></li>
                </ul>
            </div> --}}
        </div>
    </div>
</body>

</html>