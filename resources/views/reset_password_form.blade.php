



<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link href="/gt/Content.css" rel="stylesheet"/>

    <script src="/gt/bundles.js"></script>


    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.1.0/css/all.css"
          integrity="sha384-lKuwvrZot6UHsBSfcMvOkWwlCMgc0TaWr+30HWe3a4ltaBwTZhyTEggF5tJv8tbt"
          crossorigin="anonymous">
</head>

<body>
          <div class="container">
            
            
            <div class="collapse navbar-collapse head" id="navbarCollapse">
                <ul class="navbar-nav mr-auto">
                    <li class="nav-item headtext">
                        <h3></h3>
                    </li>
                    
                </ul>
                
            </div>
        </div>
    <main role="main" class="container">
        

        


<html>
<head>
    <title>Reset Password</title>
    <link href="/gt/css_style2.css" rel="stylesheet" type="text/css" media="all" />
    <!-- Custom Theme files -->
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="keywords" content="Reset Password Form" />
    <!--google fonts-->
    <link href='//fonts.googleapis.com/css?family=Roboto:400,100,300,500,700,900' rel='stylesheet' type='text/css'>
</head>
<body>


<form action="#" method="post" role="form"><div class="validation-summary-valid text-danger" data-valmsg-summary="true"><ul><li style="display:none"></li>
</ul></div>        <div class="elelment">
            <h2>Reset Password</h2>
            <div class="element-main">
                <h1>New Password</h1>
      
                @if($is_error == true)    
                <div class="alert alert-danger" role="alert">
                    {{$mess}}
                  </div>
                  @else
                  @endif

                  @if($is_success == true)    
                  <div class="alert alert-success" role="alert">
                    {{$mess}}
                  </div>
                  @else
                  @endif

                <div>
                    <input data-val="true"   id="password" name="password" placeholder="Create new password" type="password" value="" />
                    <input  data-val="true"  id="password_confirm" name="password_confirm" placeholder="Confirm your password" type="password" value="" />
                    
                    <input type="submit" value="Reset my Password">
                </div>
            </div>
        </div>
</form></body>
</html>

    </main>
    <script src="/gt/bundles_jquery.js"></script>

    <script src="/gt/bundles_bootstrap.js"></script>

    
            <script src="/gt/bundles_val.js"></script>

        
</body>
</html>
