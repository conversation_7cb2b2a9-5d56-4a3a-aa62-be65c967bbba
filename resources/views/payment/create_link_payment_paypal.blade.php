<!DOCTYPE html>
<html>

<head>
  <title>Pay {{$price}}$ with paypal</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script>
    function myFunction() {
      // Get the text field
      var copyText = document.getElementById("myInput");

      // Select the text field
      copyText.select();
      copyText.setSelectionRange(0, 99999); // For mobile devices

      // Copy the text inside the text field
      navigator.clipboard.writeText(copyText.value);

      // Alert the copied text
      alert("Copied the text: " + copyText.value);
    }

    var input = document.getElementById('myInput');
    input.focus();
    input.select();
  </script>

</head>

<body>

  <div style="padding:20px">
    <input style="font-size: 8px;width: 260px; height: 50px;" onFocus="this.select()" type="text" value="{{$link}}" id="myInput">

    <br> <br> <br> <br>
    <a href="/paypal"> <- Create new link </a>
  </div>

</body>

</html>