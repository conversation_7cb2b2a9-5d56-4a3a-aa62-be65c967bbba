npm-index(7) -- Index of all npm documentation
==============================================

## README(1)

node package manager

# Command Line Documentation

## npm(1)

node package manager

## npm-adduser(1)

Add a registry user account

## npm-bin(1)

Display npm bin folder

## npm-bugs(1)

Bugs for a package in a web browser maybe

## npm-build(1)

Build a package

## npm-bundle(1)

REMOVED

## npm-cache(1)

Manipulates packages cache

## npm-completion(1)

Tab Completion for npm

## npm-config(1)

Manage the npm configuration files

## npm-dedupe(1)

Reduce duplication

## npm-deprecate(1)

Deprecate a version of a package

## npm-docs(1)

Docs for a package in a web browser maybe

## npm-edit(1)

Edit an installed package

## npm-explore(1)

Browse an installed package

## npm-help-search(1)

Search npm help documentation

## npm-help(1)

Get help on npm

## npm-init(1)

Interactively create a package.json file

## npm-install(1)

Install a package

## npm-link(1)

Symlink a package folder

## npm-ls(1)

List installed packages

## npm-outdated(1)

Check for outdated packages

## npm-owner(1)

Manage package owners

## npm-pack(1)

Create a tarball from a package

## npm-prefix(1)

Display prefix

## npm-prune(1)

Remove extraneous packages

## npm-publish(1)

Publish a package

## npm-rebuild(1)

Rebuild a package

## npm-repo(1)

Open package repository page in the browser

## npm-restart(1)

Start a package

## npm-rm(1)

Remove a package

## npm-root(1)

Display npm root

## npm-run-script(1)

Run arbitrary package scripts

## npm-search(1)

Search for packages

## npm-shrinkwrap(1)

Lock down dependency versions

## npm-star(1)

Mark your favorite packages

## npm-stars(1)

View packages marked as favorites

## npm-start(1)

Start a package

## npm-stop(1)

Stop a package

## npm-submodule(1)

Add a package as a git submodule

## npm-tag(1)

Tag a published version

## npm-test(1)

Test a package

## npm-uninstall(1)

Remove a package

## npm-unpublish(1)

Remove a package from the registry

## npm-update(1)

Update a package

## npm-version(1)

Bump a package version

## npm-view(1)

View registry info

## npm-whoami(1)

Display npm username

# API Documentation

## npm(3)

node package manager

## npm-bin(3)

Display npm bin folder

## npm-bugs(3)

Bugs for a package in a web browser maybe

## npm-cache(3)

manage the npm cache programmatically

## npm-commands(3)

npm commands

## npm-config(3)

Manage the npm configuration files

## npm-deprecate(3)

Deprecate a version of a package

## npm-docs(3)

Docs for a package in a web browser maybe

## npm-edit(3)

Edit an installed package

## npm-explore(3)

Browse an installed package

## npm-help-search(3)

Search the help pages

## npm-init(3)

Interactively create a package.json file

## npm-install(3)

install a package programmatically

## npm-link(3)

Symlink a package folder

## npm-load(3)

Load config settings

## npm-ls(3)

List installed packages

## npm-outdated(3)

Check for outdated packages

## npm-owner(3)

Manage package owners

## npm-pack(3)

Create a tarball from a package

## npm-prefix(3)

Display prefix

## npm-prune(3)

Remove extraneous packages

## npm-publish(3)

Publish a package

## npm-rebuild(3)

Rebuild a package

## npm-repo(3)

Open package repository page in the browser

## npm-restart(3)

Start a package

## npm-root(3)

Display npm root

## npm-run-script(3)

Run arbitrary package scripts

## npm-search(3)

Search for packages

## npm-shrinkwrap(3)

programmatically generate package shrinkwrap file

## npm-start(3)

Start a package

## npm-stop(3)

Stop a package

## npm-submodule(3)

Add a package as a git submodule

## npm-tag(3)

Tag a published version

## npm-test(3)

Test a package

## npm-uninstall(3)

uninstall a package programmatically

## npm-unpublish(3)

Remove a package from the registry

## npm-update(3)

Update a package

## npm-version(3)

Bump a package version

## npm-view(3)

View registry info

## npm-whoami(3)

Display npm username

# Files

## npm-folders(5)

Folder Structures Used by npm

## npmrc(5)

The npm config files

## package.json(5)

Specifics of npm's package.json handling

# Misc

## npm-coding-style(7)

npm's "funny" coding style

## npm-config(7)

More than you probably want to know about npm configuration

## npm-developers(7)

Developer Guide

## npm-disputes(7)

Handling Module Name Disputes

## npm-faq(7)

Frequently Asked Questions

## npm-index(7)

Index of all npm documentation

## npm-registry(7)

The JavaScript Package Registry

## npm-scripts(7)

How npm handles the "scripts" field

## removing-npm(7)

Cleaning the Slate

## semver(7)

The semantic versioner for npm

