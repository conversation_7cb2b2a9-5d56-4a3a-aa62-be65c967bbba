.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-BUGS" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-bugs\fR \-\- Bugs for a package in a web browser maybe
.
.SH "SYNOPSIS"
.
.nf
npm bugs <pkgname>
npm bugs (with no args in a package dir)
.
.fi
.
.SH "DESCRIPTION"
This command tries to guess at the likely location of a package\'s
bug tracker URL, and then tries to open it using the \fB\-\-browser\fR
config param\. If no package name is provided, it will search for
a \fBpackage\.json\fR in the current folder and use the \fBname\fR property\.
.
.SH "CONFIGURATION"
.
.SS "browser"
.
.IP "\(bu" 4
Default: OS X: \fB"open"\fR, Windows: \fB"start"\fR, Others: \fB"xdg\-open"\fR
.
.IP "\(bu" 4
Type: String
.
.IP "" 0
.
.P
The browser that is called by the \fBnpm bugs\fR command to open websites\.
.
.SS "registry"
.
.IP "\(bu" 4
Default: https://registry\.npmjs\.org/
.
.IP "\(bu" 4
Type: url
.
.IP "" 0
.
.P
The base URL of the npm package registry\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help docs
.
.IP "\(bu" 4
npm help view
.
.IP "\(bu" 4
npm help publish
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help  package\.json
.
.IP "" 0

