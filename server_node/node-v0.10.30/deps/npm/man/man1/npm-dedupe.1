.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-DEDUPE" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-dedupe\fR \-\- Reduce duplication
.
.SH "SYNOPSIS"
.
.nf
npm dedupe [package names\.\.\.]
npm ddp [package names\.\.\.]
.
.fi
.
.SH "DESCRIPTION"
Searches the local package tree and attempts to simplify the overall
structure by moving dependencies further up the tree, where they can
be more effectively shared by multiple dependent packages\.
.
.P
For example, consider this dependency graph:
.
.IP "" 4
.
.nf
a
+\-\- b <\-\- depends on c@1\.0\.x
|   `\-\- c@1\.0\.3
`\-\- d <\-\- depends on c@~1\.0\.9
    `\-\- c@1\.0\.10
.
.fi
.
.IP "" 0
.
.P
npm help In this case, \fBnpm\-dedupe\fR will transform the tree to:
.
.IP "" 4
.
.nf
a
+\-\- b
+\-\- d
`\-\- c@1\.0\.10
.
.fi
.
.IP "" 0
.
.P
Because of the hierarchical nature of node\'s module lookup, b and d
will both get their dependency met by the single c package at the root
level of the tree\.
.
.P
If a suitable version exists at the target location in the tree
already, then it will be left untouched, but the other duplicates will
be deleted\.
.
.P
If no suitable version can be found, then a warning is printed, and
nothing is done\.
.
.P
If any arguments are supplied, then they are filters, and only the
named packages will be touched\.
.
.P
Note that this operation transforms the dependency tree, and may
result in packages getting updated versions, perhaps from the npm
registry\.
.
.P
This feature is experimental, and may change in future versions\.
.
.P
The \fB\-\-tag\fR argument will apply to all of the affected dependencies\. If a
tag with the given name exists, the tagged version is preferred over newer
versions\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help ls
.
.IP "\(bu" 4
npm help update
.
.IP "\(bu" 4
npm help install
.
.IP "" 0

