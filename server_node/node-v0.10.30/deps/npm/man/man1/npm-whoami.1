.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-WHOAMI" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-whoami\fR \-\- Display npm username
.
.SH "SYNOPSIS"
.
.nf
npm whoami
.
.fi
.
.SH "DESCRIPTION"
Print the \fBusername\fR config to standard output\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help adduser
.
.IP "" 0

