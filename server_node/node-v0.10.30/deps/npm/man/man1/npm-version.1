.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-VERSION" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-version\fR \-\- Bump a package version
.
.SH "SYNOPSIS"
.
.nf
npm version [<newversion> | major | minor | patch]
.
.fi
.
.SH "DESCRIPTION"
Run this in a package directory to bump the version and write the new
data back to the package\.json file\.
.
.P
The \fBnewversion\fR argument should be a valid semver string, \fIor\fR a valid
second argument to semver\.inc (one of "patch", "minor", or
"major")\. In the second case, the existing version will be incremented
by 1 in the specified field\.
.
.P
If run in a git repo, it will also create a version commit and tag, and
fail if the repo is not clean\.
.
.P
If supplied with \fB\-\-message\fR (shorthand: \fB\-m\fR) config option, npm will
use it as a commit message when creating a version commit\.  If the \fBmessage\fR config contains \fB%s\fR then that will be replaced with the
resulting version number\.  For example:
.
.IP "" 4
.
.nf
npm version patch \-m "Upgrade to %s for reasons"
.
.fi
.
.IP "" 0
.
.P
If the \fBsign\-git\-tag\fR config is set, then the tag will be signed using
the \fB\-s\fR flag to git\.  Note that you must have a default GPG key set up
in your git config for this to work properly\.  For example:
.
.IP "" 4
.
.nf
$ npm config set sign\-git\-tag true
$ npm version patch
You need a passphrase to unlock the secret key for
user: "isaacs (http://blog\.izs\.me/) <i@izs\.me>"
2048\-bit RSA key, ID 6C481CF6, created 2010\-08\-31
Enter passphrase:
.
.fi
.
.IP "" 0
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help init
.
.IP "\(bu" 4
npm help  package\.json
.
.IP "\(bu" 4
npm help  semver
.
.IP "" 0

