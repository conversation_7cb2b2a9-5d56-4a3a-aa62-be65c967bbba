.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-STAR" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-star\fR \-\- Mark your favorite packages
.
.SH "SYNOPSIS"
.
.nf
npm star <pkgname> [<pkg>, \.\.\.]
npm unstar <pkgname> [<pkg>, \.\.\.]
.
.fi
.
.SH "DESCRIPTION"
"Starring" a package means that you have some interest in it\.  It\'s
a vaguely positive way to show that you care\.
.
.P
"Unstarring" is the same thing, but in reverse\.
.
.P
It\'s a boolean thing\.  Starring repeatedly has no additional effect\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help view
.
.IP "\(bu" 4
npm help whoami
.
.IP "\(bu" 4
npm help adduser
.
.IP "" 0

