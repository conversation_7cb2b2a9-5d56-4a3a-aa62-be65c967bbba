.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-VIEW" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-view\fR \-\- View registry info
.
.SH "SYNOPSIS"
.
.nf
npm view <name>[@<version>] [<field>[\.<subfield>]\.\.\.]
npm v <name>[@<version>] [<field>[\.<subfield>]\.\.\.]
.
.fi
.
.SH "DESCRIPTION"
This command shows data about a package and prints it to the stream
referenced by the \fBoutfd\fR config, which defaults to stdout\.
.
.P
To show the package registry entry for the \fBconnect\fR package, you can do
this:
.
.IP "" 4
.
.nf
npm view connect
.
.fi
.
.IP "" 0
.
.P
The default version is "latest" if unspecified\.
.
.P
Field names can be specified after the package descriptor\.
For example, to show the dependencies of the \fBronn\fR package at version
0\.3\.5, you could do the following:
.
.IP "" 4
.
.nf
npm view ronn@0\.3\.5 dependencies
.
.fi
.
.IP "" 0
.
.P
You can view child field by separating them with a period\.
To view the git repository URL for the latest version of npm, you could
do this:
.
.IP "" 4
.
.nf
npm view npm repository\.url
.
.fi
.
.IP "" 0
.
.P
This makes it easy to view information about a dependency with a bit of
shell scripting\.  For example, to view all the data about the version of
opts that ronn depends on, you can do this:
.
.IP "" 4
.
.nf
npm view opts@$(npm view ronn dependencies\.opts)
.
.fi
.
.IP "" 0
.
.P
For fields that are arrays, requesting a non\-numeric field will return
all of the values from the objects in the list\.  For example, to get all
the contributor names for the "express" project, you can do this:
.
.IP "" 4
.
.nf
npm view express contributors\.email
.
.fi
.
.IP "" 0
.
.P
You may also use numeric indices in square braces to specifically select
an item in an array field\.  To just get the email address of the first
contributor in the list, you can do this:
.
.IP "" 4
.
.nf
npm view express contributors[0]\.email
.
.fi
.
.IP "" 0
.
.P
Multiple fields may be specified, and will be printed one after another\.
For exampls, to get all the contributor names and email addresses, you
can do this:
.
.IP "" 4
.
.nf
npm view express contributors\.name contributors\.email
.
.fi
.
.IP "" 0
.
.P
"Person" fields are shown as a string if they would be shown as an
object\.  So, for example, this will show the list of npm contributors in
the shortened string format\.  (npm help  See \fBpackage\.json\fR for more on this\.)
.
.IP "" 4
.
.nf
npm view npm contributors
.
.fi
.
.IP "" 0
.
.P
If a version range is provided, then data will be printed for every
matching version of the package\.  This will show which version of jsdom
was required by each matching version of yui3:
.
.IP "" 4
.
.nf
npm view yui3@\'>0\.5\.4\' dependencies\.jsdom
.
.fi
.
.IP "" 0
.
.SH "OUTPUT"
If only a single string field for a single version is output, then it
will not be colorized or quoted, so as to enable piping the output to
another command\. If the field is an object, it will be output as a JavaScript object literal\.
.
.P
If the \-\-json flag is given, the outputted fields will be JSON\.
.
.P
If the version range matches multiple versions, than each printed value
will be prefixed with the version it applies to\.
.
.P
If multiple fields are requested, than each of them are prefixed with
the field name\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help search
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help docs
.
.IP "" 0

