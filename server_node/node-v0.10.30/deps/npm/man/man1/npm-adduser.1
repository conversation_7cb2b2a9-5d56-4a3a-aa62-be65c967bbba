.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-ADDUSER" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-adduser\fR \-\- Add a registry user account
.
.SH "SYNOPSIS"
.
.nf
npm adduser
.
.fi
.
.SH "DESCRIPTION"
Create or verify a user named \fB<username>\fR in the npm registry, and
save the credentials to the \fB\|\.npmrc\fR file\.
.
.P
The username, password, and email are read in from prompts\.
.
.P
You may use this command to change your email address, but not username
or password\.
.
.P
To reset your password, go to \fIhttps://npmjs\.org/forgot\fR
.
.P
You may use this command multiple times with the same user account to
authorize on a new machine\.
.
.SH "CONFIGURATION"
.
.SS "registry"
Default: http://registry\.npmjs\.org/
.
.P
The base URL of the npm package registry\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help owner
.
.IP "\(bu" 4
npm help whoami
.
.IP "" 0

