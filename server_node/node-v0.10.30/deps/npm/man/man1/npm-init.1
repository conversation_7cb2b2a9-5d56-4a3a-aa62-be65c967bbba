.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-INIT" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-init\fR \-\- Interactively create a package\.json file
.
.SH "SYNOPSIS"
.
.nf
npm init
.
.fi
.
.SH "DESCRIPTION"
This will ask you a bunch of questions, and then write a package\.json for you\.
.
.P
It attempts to make reasonable guesses about what you want things to be set to,
and then writes a package\.json file with the options you\'ve selected\.
.
.P
If you already have a package\.json file, it\'ll read that first, and default to
the options in there\.
.
.P
It is strictly additive, so it does not delete options from your package\.json
without a really good reason to do so\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
\fIhttps://github\.com/isaacs/init\-package\-json\fR
.
.IP "\(bu" 4
npm help  package\.json
.
.IP "\(bu" 4
npm help version
.
.IP "" 0

