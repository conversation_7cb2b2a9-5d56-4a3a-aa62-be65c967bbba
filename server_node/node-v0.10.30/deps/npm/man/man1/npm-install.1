.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-INSTALL" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-install\fR \-\- Install a package
.
.SH "SYNOPSIS"
.
.nf
npm install (with no args in a package dir)
npm install <tarball file>
npm install <tarball url>
npm install <folder>
npm install <name> [\-\-save|\-\-save\-dev|\-\-save\-optional] [\-\-save\-exact]
npm install <name>@<tag>
npm install <name>@<version>
npm install <name>@<version range>
npm i (with any of the previous argument usage)
.
.fi
.
.SH "DESCRIPTION"
This command installs a package, and any packages that it depends on\. If the
package has a shrinkwrap file, the installation of dependencies will be driven
npm help by that\. See npm\-shrinkwrap\.
.
.P
A \fBpackage\fR is:
.
.IP "\(bu" 4
a) a folder containing a program described by a package\.json file
.
.IP "\(bu" 4
b) a gzipped tarball containing (a)
.
.IP "\(bu" 4
c) a url that resolves to (b)
.
.IP "\(bu" 4
d) a \fB<name>@<version>\fR that is published on the registry (npm help  see \fBnpm\-registry\fR) with (c)
.
.IP "\(bu" 4
e) a \fB<name>@<tag>\fR that points to (d)
.
.IP "\(bu" 4
f) a \fB<name>\fR that has a "latest" tag satisfying (e)
.
.IP "\(bu" 4
g) a \fB<git remote url>\fR that resolves to (b)
.
.IP "" 0
.
.P
Even if you never publish your package, you can still get a lot of
benefits of using npm if you just want to write a node program (a), and
perhaps if you also want to be able to easily install it elsewhere
after packing it up into a tarball (b)\.
.
.IP "\(bu" 4
\fBnpm install\fR (in package directory, no arguments):
.
.IP
Install the dependencies in the local node_modules folder\.
.
.IP
In global mode (ie, with \fB\-g\fR or \fB\-\-global\fR appended to the command),
it installs the current package context (ie, the current working
directory) as a global package\.
.
.IP
By default, \fBnpm install\fR will install all modules listed as
dependencies\. With the \fB\-\-production\fR flag,
npm will not install modules listed in \fBdevDependencies\fR\|\.
.
.IP "\(bu" 4
\fBnpm install <folder>\fR:
.
.IP
Install a package that is sitting in a folder on the filesystem\.
.
.IP "\(bu" 4
\fBnpm install <tarball file>\fR:
.
.IP
Install a package that is sitting on the filesystem\.  Note: if you just want
to link a dev directory into your npm root, you can do this more easily by
using \fBnpm link\fR\|\.
.
.IP
Example:
.
.IP "" 4
.
.nf
  npm install \./package\.tgz
.
.fi
.
.IP "" 0

.
.IP "\(bu" 4
\fBnpm install <tarball url>\fR:
.
.IP
Fetch the tarball url, and then install it\.  In order to distinguish between
this and other options, the argument must start with "http://" or "https://"
.
.IP
Example:
.
.IP "" 4
.
.nf
  npm install https://github\.com/indexzero/forever/tarball/v0\.5\.6
.
.fi
.
.IP "" 0

.
.IP "\(bu" 4
\fBnpm install <name> [\-\-save|\-\-save\-dev|\-\-save\-optional]\fR:
.
.IP
Do a \fB<name>@<tag>\fR install, where \fB<tag>\fR is the "tag" config\. (npm help  See \fBnpm\-config\fR\|\.)
.
.IP
In most cases, this will install the latest version
of the module published on npm\.
.
.IP
Example:
.
.IP
      npm install sax
.
.IP
\fBnpm install\fR takes 3 exclusive, optional flags which save or update
the package version in your main package\.json:
.
.IP "\(bu" 4
\fB\-\-save\fR: Package will appear in your \fBdependencies\fR\|\.
.
.IP "\(bu" 4
\fB\-\-save\-dev\fR: Package will appear in your \fBdevDependencies\fR\|\.
.
.IP "\(bu" 4
\fB\-\-save\-optional\fR: Package will appear in your \fBoptionalDependencies\fR\|\.
.
.IP
When using any of the above options to save dependencies to your
package\.json, there is an additional, optional flag:
.
.IP "\(bu" 4
\fB\-\-save\-exact\fR: Saved dependencies will be configured with an
exact version rather than using npm\'s default semver range
operator\.
.
.IP
Examples:
.
.IP
  npm install sax \-\-save
  npm install node\-tap \-\-save\-dev
  npm install dtrace\-provider \-\-save\-optional
  npm install readable\-stream \-\-save \-\-save\-exact
.
.IP
\fBNote\fR: If there is a file or folder named \fB<name>\fR in the current
working directory, then it will try to install that, and only try to
fetch the package by name if it is not valid\.
.
.IP "" 0

.
.IP "\(bu" 4
\fBnpm install <name>@<tag>\fR:
.
.IP
Install the version of the package that is referenced by the specified tag\.
If the tag does not exist in the registry data for that package, then this
will fail\.
.
.IP
Example:
.
.IP "" 4
.
.nf
  npm install sax@latest
.
.fi
.
.IP "" 0

.
.IP "\(bu" 4
\fBnpm install <name>@<version>\fR:
.
.IP
Install the specified version of the package\.  This will fail if the version
has not been published to the registry\.
.
.IP
Example:
.
.IP "" 4
.
.nf
  npm install sax@0\.1\.1
.
.fi
.
.IP "" 0

.
.IP "\(bu" 4
\fBnpm install <name>@<version range>\fR:
.
.IP
Install a version of the package matching the specified version range\.  This
npm help  will follow the same rules for resolving dependencies described in \fBpackage\.json\fR\|\.
.
.IP
Note that most version ranges must be put in quotes so that your shell will
treat it as a single argument\.
.
.IP
Example:
.
.IP
      npm install sax@">=0\.1\.0 <0\.2\.0"
.
.IP "\(bu" 4
\fBnpm install <git remote url>\fR:
.
.IP
Install a package by cloning a git remote url\.  The format of the git
url is:
.
.IP
      <protocol>://[<user>@]<hostname><separator><path>[#<commit\-ish>]
.
.IP
\fB<protocol>\fR is one of \fBgit\fR, \fBgit+ssh\fR, \fBgit+http\fR, or \fBgit+https\fR\|\.  If no \fB<commit\-ish>\fR is specified, then \fBmaster\fR is
used\.
.
.IP
Examples:
.
.IP "" 4
.
.nf
  git+ssh://git@github\.com:npm/npm\.git#v1\.0\.27
  git+https://isaacs@github\.com/npm/npm\.git
  git://github\.com/npm/npm\.git#v1\.0\.27
.
.fi
.
.IP "" 0

.
.IP "" 0
.
.P
You may combine multiple arguments, and even multiple types of arguments\.
For example:
.
.IP "" 4
.
.nf
npm install sax@">=0\.1\.0 <0\.2\.0" bench supervisor
.
.fi
.
.IP "" 0
.
.P
The \fB\-\-tag\fR argument will apply to all of the specified install targets\. If a
tag with the given name exists, the tagged version is preferred over newer
versions\.
.
.P
The \fB\-\-force\fR argument will force npm to fetch remote resources even if a
local copy exists on disk\.
.
.IP "" 4
.
.nf
npm install sax \-\-force
.
.fi
.
.IP "" 0
.
.P
The \fB\-\-global\fR argument will cause npm to install the package globally
npm help  rather than locally\.  See \fBnpm\-folders\fR\|\.
.
.P
The \fB\-\-link\fR argument will cause npm to link global installs into the
local space in some cases\.
.
.P
The \fB\-\-no\-bin\-links\fR argument will prevent npm from creating symlinks for
any binaries the package might contain\.
.
.P
The \fB\-\-no\-optional\fR argument will prevent optional dependencies from
being installed\.
.
.P
The \fB\-\-no\-shrinkwrap\fR argument, which will ignore an available
shrinkwrap file and use the package\.json instead\.
.
.P
The \fB\-\-nodedir=/path/to/node/source\fR argument will allow npm to find the
node source code so that npm can compile native modules\.
.
.P
npm help  See \fBnpm\-config\fR\|\.  Many of the configuration params have some
effect on installation, since that\'s most of what npm does\.
.
.SH "ALGORITHM"
To install a package, npm uses the following algorithm:
.
.IP "" 4
.
.nf
install(where, what, family, ancestors)
fetch what, unpack to <where>/node_modules/<what>
for each dep in what\.dependencies
  resolve dep to precise version
for each dep@version in what\.dependencies
    not in <where>/node_modules/<what>/node_modules/*
    and not in <family>
  add precise version deps to <family>
  install(<where>/node_modules/<what>, dep, family)
.
.fi
.
.IP "" 0
.
.P
For this \fBpackage{dep}\fR structure: \fBA{B,C}, B{C}, C{D}\fR,
this algorithm produces:
.
.IP "" 4
.
.nf
A
+\-\- B
`\-\- C
    `\-\- D
.
.fi
.
.IP "" 0
.
.P
That is, the dependency from B to C is satisfied by the fact that A
already caused C to be installed at a higher level\.
.
.P
npm help  See npm\-folders for a more detailed description of the specific
folder structures that npm creates\.
.
.SS "Limitations of npm&#39;s Install Algorithm"
There are some very rare and pathological edge\-cases where a cycle can
cause npm to try to install a never\-ending tree of packages\.  Here is
the simplest case:
.
.IP "" 4
.
.nf
A \-> B \-> A\' \-> B\' \-> A \-> B \-> A\' \-> B\' \-> A \-> \.\.\.
.
.fi
.
.IP "" 0
.
.P
where \fBA\fR is some version of a package, and \fBA\'\fR is a different version
of the same package\.  Because \fBB\fR depends on a different version of \fBA\fR
than the one that is already in the tree, it must install a separate
copy\.  The same is true of \fBA\'\fR, which must install \fBB\'\fR\|\.  Because \fBB\'\fR
depends on the original version of \fBA\fR, which has been overridden, the
cycle falls into infinite regress\.
.
.P
To avoid this situation, npm flat\-out refuses to install any \fBname@version\fR that is already present anywhere in the tree of package
folder ancestors\.  A more correct, but more complex, solution would be
to symlink the existing version into the new location\.  If this ever
affects a real use\-case, it will be investigated\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help update
.
.IP "\(bu" 4
npm help link
.
.IP "\(bu" 4
npm help rebuild
.
.IP "\(bu" 4
npm help  scripts
.
.IP "\(bu" 4
npm help build
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help tag
.
.IP "\(bu" 4
npm help rm
.
.IP "\(bu" 4
npm help shrinkwrap
.
.IP "" 0

