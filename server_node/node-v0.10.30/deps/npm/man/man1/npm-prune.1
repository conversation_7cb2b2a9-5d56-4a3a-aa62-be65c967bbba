.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-PRUNE" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-prune\fR \-\- Remove extraneous packages
.
.SH "SYNOPSIS"
.
.nf
npm prune [<name> [<name \.\.\.]]
npm prune [<name> [<name \.\.\.]] [\-\-production]
.
.fi
.
.SH "DESCRIPTION"
This command removes "extraneous" packages\.  If a package name is
provided, then only packages matching one of the supplied names are
removed\.
.
.P
Extraneous packages are packages that are not listed on the parent
package\'s dependencies list\.
.
.P
If the \fB\-\-production\fR flag is specified, this command will remove the
packages specified in your \fBdevDependencies\fR\|\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help rm
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help ls
.
.IP "" 0

