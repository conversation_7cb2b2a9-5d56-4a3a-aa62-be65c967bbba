.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-RUN\-SCRIPT" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-run-script\fR \-\- Run arbitrary package scripts
.
.SH "SYNOPSIS"
.
.nf
npm run\-script [<pkg>] [command]
npm run [<pkg>] [command]
.
.fi
.
.SH "DESCRIPTION"
This runs an arbitrary command from a package\'s \fB"scripts"\fR object\.
If no package name is provided, it will search for a \fBpackage\.json\fR
in the current folder and use its \fB"scripts"\fR object\. If no \fB"command"\fR
is provided, it will list the available top level scripts\.
.
.P
It is used by the test, start, restart, and stop commands, but can be
called directly, as well\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help  scripts
.
.IP "\(bu" 4
npm help test
.
.IP "\(bu" 4
npm help start
.
.IP "\(bu" 4
npm help restart
.
.IP "\(bu" 4
npm help stop
.
.IP "" 0

