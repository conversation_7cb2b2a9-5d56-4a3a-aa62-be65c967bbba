.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-UNPUBLISH" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-unpublish\fR \-\- Remove a package from the registry
.
.SH "SYNOPSIS"
.
.nf
npm unpublish <name>[@<version>]
.
.fi
.
.SH "WARNING"
\fBIt is generally considered bad behavior to remove versions of a library
that others are depending on!\fR
.
.P
Consider using the \fBdeprecate\fR command
instead, if your intent is to encourage users to upgrade\.
.
.P
There is plenty of room on the registry\.
.
.SH "DESCRIPTION"
This removes a package version from the registry, deleting its
entry and removing the tarball\.
.
.P
If no version is specified, or if all versions are removed then
the root package entry is removed from the registry entirely\.
.
.P
Even if a package version is unpublished, that specific name and
version combination can never be reused\.  In order to publish the
package again, a new version number must be used\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help deprecate
.
.IP "\(bu" 4
npm help publish
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help adduser
.
.IP "\(bu" 4
npm help owner
.
.IP "" 0

