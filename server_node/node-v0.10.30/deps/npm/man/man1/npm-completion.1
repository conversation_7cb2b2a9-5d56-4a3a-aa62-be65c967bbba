.\" Generated with <PERSON>njs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-COMPLETION" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-completion\fR \-\- Tab Completion for npm
.
.SH "SYNOPSIS"
.
.nf
\|\. <(npm completion)
.
.fi
.
.SH "DESCRIPTION"
Enables tab\-completion in all npm commands\.
.
.P
The synopsis above
loads the completions into your current shell\.  Adding it to
your ~/\.bashrc or ~/\.zshrc will make the completions available
everywhere\.
.
.P
You may of course also pipe the output of npm completion to a file
such as \fB/usr/local/etc/bash_completion\.d/npm\fR if you have a system
that will read that file for you\.
.
.P
When \fBCOMP_CWORD\fR, \fBCOMP_LINE\fR, and \fBCOMP_POINT\fR are defined in the
environment, \fBnpm completion\fR acts in "plumbing mode", and outputs
completions based on the arguments\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help  developers
.
.IP "\(bu" 4
npm help  faq
.
.IP "\(bu" 4
npm help npm
.
.IP "" 0

