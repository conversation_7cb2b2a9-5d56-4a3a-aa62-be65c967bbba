.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-BUILD" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-build\fR \-\- Build a package
.
.SH "SYNOPSIS"
.
.nf
npm build <package\-folder>
.
.fi
.
.IP "\(bu" 4
\fB<package\-folder>\fR:
A folder containing a \fBpackage\.json\fR file in its root\.
.
.IP "" 0
.
.SH "DESCRIPTION"
This is the plumbing command called by \fBnpm link\fR and \fBnpm install\fR\|\.
.
.P
It should generally not be called directly\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help install
.
.IP "\(bu" 4
npm help link
.
.IP "\(bu" 4
npm help  scripts
.
.IP "\(bu" 4
npm help  package\.json
.
.IP "" 0

