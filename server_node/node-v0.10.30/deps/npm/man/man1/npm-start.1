.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-START" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-start\fR \-\- Start a package
.
.SH "SYNOPSIS"
.
.nf
npm start <name>
.
.fi
.
.SH "DESCRIPTION"
This runs a package\'s "start" script, if one was provided\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help run\-script
.
.IP "\(bu" 4
npm help  scripts
.
.IP "\(bu" 4
npm help test
.
.IP "\(bu" 4
npm help restart
.
.IP "\(bu" 4
npm help stop
.
.IP "" 0

