.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-PREFIX" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-prefix\fR \-\- Display prefix
.
.SH "SYNOPSIS"
.
.nf
npm prefix
.
.fi
.
.SH "DESCRIPTION"
Print the prefix to standard out\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help root
.
.IP "\(bu" 4
npm help bin
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "" 0

