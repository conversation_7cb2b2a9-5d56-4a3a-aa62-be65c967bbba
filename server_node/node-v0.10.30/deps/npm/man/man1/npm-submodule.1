.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-SUBMODULE" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-submodule\fR \-\- Add a package as a git submodule
.
.SH "SYNOPSIS"
.
.nf
npm submodule <pkg>
.
.fi
.
.SH "DESCRIPTION"
If the specified package has a git repository url in its package\.json
description, then this command will add it as a git submodule at \fBnode_modules/<pkg name>\fR\|\.
.
.P
This is a convenience only\.  From then on, it\'s up to you to manage
updates by using the appropriate git commands\.  npm will stubbornly
refuse to update, modify, or remove anything with a \fB\|\.git\fR subfolder
in it\.
.
.P
This command also does not install missing dependencies, if the package
does not include them in its git repository\.  If \fBnpm ls\fR reports that
things are missing, you can either install, link, or submodule them yourself,
or you can do \fBnpm explore <pkgname> \-\- npm install\fR to install the
dependencies into the submodule folder\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help  package\.json
.
.IP "\(bu" 4
git help submodule
.
.IP "" 0

