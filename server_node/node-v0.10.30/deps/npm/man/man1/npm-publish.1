.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-PUBLISH" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-publish\fR \-\- Publish a package
.
.SH "SYNOPSIS"
.
.nf
npm publish <tarball> [\-\-tag <tag>]
npm publish <folder> [\-\-tag <tag>]
.
.fi
.
.SH "DESCRIPTION"
Publishes a package to the registry so that it can be installed by name\.
.
.IP "\(bu" 4
\fB<folder>\fR:
A folder containing a package\.json file
.
.IP "\(bu" 4
\fB<tarball>\fR:
A url or file path to a gzipped tar archive containing a single folder
with a package\.json file inside\.
.
.IP "\(bu" 4
\fB[\-\-tag <tag>]\fR
Registers the published package with the given tag, such that \fBnpm install
<name>@<tag>\fR will install this version\.  By default, \fBnpm publish\fR updates
and \fBnpm install\fR installs the \fBlatest\fR tag\.
.
.IP "" 0
.
.P
Fails if the package name and version combination already exists in
the registry\.
.
.P
Once a package is published with a given name and version, that
specific name and version combination can never be used again, even if
npm help it is removed with npm\-unpublish\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help adduser
.
.IP "\(bu" 4
npm help owner
.
.IP "\(bu" 4
npm help deprecate
.
.IP "\(bu" 4
npm help tag
.
.IP "" 0

