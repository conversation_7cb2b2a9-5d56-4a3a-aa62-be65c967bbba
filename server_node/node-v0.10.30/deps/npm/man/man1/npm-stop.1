.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-STOP" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-stop\fR \-\- Stop a package
.
.SH "SYNOPSIS"
.
.nf
npm stop <name>
.
.fi
.
.SH "DESCRIPTION"
This runs a package\'s "stop" script, if one was provided\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help run\-script
.
.IP "\(bu" 4
npm help  scripts
.
.IP "\(bu" 4
npm help test
.
.IP "\(bu" 4
npm help start
.
.IP "\(bu" 4
npm help restart
.
.IP "" 0

