.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-RM" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-rm\fR \-\- Remove a package
.
.SH "SYNOPSIS"
.
.nf
npm rm <name>
npm r <name>
npm uninstall <name>
npm un <name>
.
.fi
.
.SH "DESCRIPTION"
This uninstalls a package, completely removing everything npm installed
on its behalf\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help prune
.
.IP "\(bu" 4
npm help install
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "" 0

