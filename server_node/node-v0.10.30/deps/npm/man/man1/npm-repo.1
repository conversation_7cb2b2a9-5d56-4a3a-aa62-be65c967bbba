.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-REPO" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-repo\fR \-\- Open package repository page in the browser
.
.SH "SYNOPSIS"
.
.nf
npm repo <pkgname>
npm repo (with no args in a package dir)
.
.fi
.
.SH "DESCRIPTION"
This command tries to guess at the likely location of a package\'s
repository URL, and then tries to open it using the \fB\-\-browser\fR
config param\. If no package name is provided, it will search for
a \fBpackage\.json\fR in the current folder and use the \fBname\fR property\.
.
.SH "CONFIGURATION"
.
.SS "browser"
.
.IP "\(bu" 4
Default: OS X: \fB"open"\fR, Windows: \fB"start"\fR, Others: \fB"xdg\-open"\fR
.
.IP "\(bu" 4
Type: String
.
.IP "" 0
.
.P
The browser that is called by the \fBnpm repo\fR command to open websites\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help docs
.
.IP "\(bu" 4
npm help config
.
.IP "" 0

