.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-SEARCH" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-search\fR \-\- Search for packages
.
.SH "SYNOPSIS"
.
.nf
npm search [\-\-long] [search terms \.\.\.]
npm s [search terms \.\.\.]
npm se [search terms \.\.\.]
.
.fi
.
.SH "DESCRIPTION"
Search the registry for packages matching the search terms\.
.
.P
If a term starts with \fB/\fR, then it\'s interpreted as a regular expression\.
A trailing \fB/\fR will be ignored in this case\.  (Note that many regular
expression characters must be escaped or quoted in most shells\.)
.
.SH "CONFIGURATION"
.
.SS "long"
.
.IP "\(bu" 4
Default: false
.
.IP "\(bu" 4
Type: Boolean
.
.IP "" 0
.
.P
Display full package descriptions and other long text across multiple
lines\. When disabled (default) search results are truncated to fit
neatly on a single line\. Modules with extremely long names will
fall on multiple lines\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help view
.
.IP "" 0

