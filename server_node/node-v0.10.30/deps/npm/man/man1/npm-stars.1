.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-STARS" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-stars\fR \-\- View packages marked as favorites
.
.SH "SYNOPSIS"
.
.nf
npm stars
npm stars [username]
.
.fi
.
.SH "DESCRIPTION"
If you have starred a lot of neat things and want to find them again
quickly this command lets you do just that\.
.
.P
You may also want to see your friend\'s favorite packages, in this case
you will most certainly enjoy this command\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help star
.
.IP "\(bu" 4
npm help view
.
.IP "\(bu" 4
npm help whoami
.
.IP "\(bu" 4
npm help adduser
.
.IP "" 0

