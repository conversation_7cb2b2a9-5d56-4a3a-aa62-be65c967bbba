.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-ROOT" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-root\fR \-\- Display npm root
.
.SH "SYNOPSIS"
.
.nf
npm root
.
.fi
.
.SH "DESCRIPTION"
Print the effective \fBnode_modules\fR folder to standard out\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help prefix
.
.IP "\(bu" 4
npm help bin
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "" 0

