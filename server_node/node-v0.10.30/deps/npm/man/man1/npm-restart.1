.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-RESTART" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-restart\fR \-\- Start a package
.
.SH "SYNOPSIS"
.
.nf
npm restart <name>
.
.fi
.
.SH "DESCRIPTION"
This runs a package\'s "restart" script, if one was provided\.
Otherwise it runs package\'s "stop" script, if one was provided, and then
the "start" script\.
.
.P
If no version is specified, then it restarts the "active" version\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help run\-script
.
.IP "\(bu" 4
npm help  scripts
.
.IP "\(bu" 4
npm help test
.
.IP "\(bu" 4
npm help start
.
.IP "\(bu" 4
npm help stop
.
.IP "" 0

