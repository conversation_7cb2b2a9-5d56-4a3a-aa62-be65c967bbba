.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-LS" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-ls\fR \-\- List installed packages
.
.SH "SYNOPSIS"
.
.nf
npm list [<pkg> \.\.\.]
npm ls [<pkg> \.\.\.]
npm la [<pkg> \.\.\.]
npm ll [<pkg> \.\.\.]
.
.fi
.
.SH "DESCRIPTION"
This command will print to stdout all the versions of packages that are
installed, as well as their dependencies, in a tree\-structure\.
.
.P
Positional arguments are \fBname@version\-range\fR identifiers, which will
limit the results to only the paths to the packages named\.  Note that
nested packages will \fIalso\fR show the paths to the specified packages\.
For example, running \fBnpm ls promzard\fR in npm\'s source tree will show:
.
.IP "" 4
.
.nf
npm@1.4.21 /path/to/npm
└─┬ init\-package\-json@0\.0\.4
  └── promzard@0\.1\.5
.
.fi
.
.IP "" 0
.
.P
It will print out extraneous, missing, and invalid packages\.
.
.P
If a project specifies git urls for dependencies these are shown
in parentheses after the name@version to make it easier for users to
recognize potential forks of a project\.
.
.P
When run as \fBll\fR or \fBla\fR, it shows extended information by default\.
.
.SH "CONFIGURATION"
.
.SS "json"
.
.IP "\(bu" 4
Default: false
.
.IP "\(bu" 4
Type: Boolean
.
.IP "" 0
.
.P
Show information in JSON format\.
.
.SS "long"
.
.IP "\(bu" 4
Default: false
.
.IP "\(bu" 4
Type: Boolean
.
.IP "" 0
.
.P
Show extended information\.
.
.SS "parseable"
.
.IP "\(bu" 4
Default: false
.
.IP "\(bu" 4
Type: Boolean
.
.IP "" 0
.
.P
Show parseable output instead of tree view\.
.
.SS "global"
.
.IP "\(bu" 4
Default: false
.
.IP "\(bu" 4
Type: Boolean
.
.IP "" 0
.
.P
List packages in the global install prefix instead of in the current
project\.
.
.SS "depth"
.
.IP "\(bu" 4
Type: Int
.
.IP "" 0
.
.P
Max display depth of the dependency tree\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help install
.
.IP "\(bu" 4
npm help link
.
.IP "\(bu" 4
npm help prune
.
.IP "\(bu" 4
npm help outdated
.
.IP "\(bu" 4
npm help update
.
.IP "" 0

