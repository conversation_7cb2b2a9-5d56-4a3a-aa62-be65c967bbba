.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-UPDATE" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-update\fR \-\- Update a package
.
.SH "SYNOPSIS"
.
.nf
npm update [\-g] [<name> [<name> \.\.\.]]
.
.fi
.
.SH "DESCRIPTION"
This command will update all the packages listed to the latest version
(specified by the \fBtag\fR config)\.
.
.P
It will also install missing packages\.
.
.P
If the \fB\-g\fR flag is specified, this command will update globally installed packages\.
If no package name is specified, all packages in the specified location (global or local) will be updated\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help install
.
.IP "\(bu" 4
npm help outdated
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help ls
.
.IP "" 0

