.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-BIN" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-bin\fR \-\- Display npm bin folder
.
.SH "SYNOPSIS"
.
.nf
npm bin
.
.fi
.
.SH "DESCRIPTION"
Print the folder where npm will install executables\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help prefix
.
.IP "\(bu" 4
npm help root
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "" 0

