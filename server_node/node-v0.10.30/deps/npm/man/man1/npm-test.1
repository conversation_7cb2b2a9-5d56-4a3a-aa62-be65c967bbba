.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-TEST" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-test\fR \-\- Test a package
.
.SH "SYNOPSIS"
.
.nf
  npm test <name>
  npm tst <name>
.
.fi
.
.SH "DESCRIPTION"
This runs a package\'s "test" script, if one was provided\.
.
.P
To run tests as a condition of installation, set the \fBnpat\fR config to
true\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help run\-script
.
.IP "\(bu" 4
npm help  scripts
.
.IP "\(bu" 4
npm help start
.
.IP "\(bu" 4
npm help restart
.
.IP "\(bu" 4
npm help stop
.
.IP "" 0

