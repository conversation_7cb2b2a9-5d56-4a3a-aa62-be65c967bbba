.\" Generated with <PERSON>njs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-HELP" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-help\fR \-\- Get help on npm
.
.SH "SYNOPSIS"
.
.nf
npm help <topic>
npm help some search terms
.
.fi
.
.SH "DESCRIPTION"
If supplied a topic, then show the appropriate documentation page\.
.
.P
If the topic does not exist, or if multiple terms are provided, then run
the \fBhelp\-search\fR command to find a match\.  Note that, if \fBhelp\-search\fR
finds a single subject, then it will run \fBhelp\fR on that topic, so unique
matches are equivalent to specifying a topic name\.
.
.SH "CONFIGURATION"
.
.SS "viewer"
.
.IP "\(bu" 4
Default: "man" on Posix, "browser" on Windows
.
.IP "\(bu" 4
Type: path
.
.IP "" 0
.
.P
The program to use to view help content\.
.
.P
Set to \fB"browser"\fR to view html help content in the default web browser\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help npm
.
.IP "\(bu" 4
README
.
.IP "\(bu" 4
npm help  faq
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help  package\.json
.
.IP "\(bu" 4
npm help help\-search
.
.IP "\(bu" 4
npm help  index
.
.IP "" 0

