.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-HELP\-SEARCH" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-help-search\fR \-\- Search npm help documentation
.
.SH "SYNOPSIS"
.
.nf
npm help\-search some search terms
.
.fi
.
.SH "DESCRIPTION"
This command will search the npm markdown documentation files for the
terms provided, and then list the results, sorted by relevance\.
.
.P
If only one result is found, then it will show that help topic\.
.
.P
If the argument to \fBnpm help\fR is not a known help topic, then it will
call \fBhelp\-search\fR\|\.  It is rarely if ever necessary to call this
command directly\.
.
.SH "CONFIGURATION"
.
.SS "long"
.
.IP "\(bu" 4
Type: Boolean
.
.IP "\(bu" 4
Default false
.
.IP "" 0
.
.P
If true, the "long" flag will cause help\-search to output context around
where the terms were found in the documentation\.
.
.P
If false, then help\-search will just list out the help topics found\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help npm
.
.IP "\(bu" 4
npm help  faq
.
.IP "\(bu" 4
npm help help
.
.IP "" 0

