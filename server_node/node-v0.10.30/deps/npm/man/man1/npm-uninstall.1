.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-RM" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-rm\fR \-\- Remove a package
.
.SH "SYNOPSIS"
.
.nf
npm uninstall <name> [\-\-save|\-\-save\-dev|\-\-save\-optional]
npm rm (with any of the previous argument usage)
.
.fi
.
.SH "DESCRIPTION"
This uninstalls a package, completely removing everything npm installed
on its behalf\.
.
.P
Example:
.
.IP "" 4
.
.nf
npm uninstall sax
.
.fi
.
.IP "" 0
.
.P
In global mode (ie, with \fB\-g\fR or \fB\-\-global\fR appended to the command),
it uninstalls the current package context as a global package\.
.
.P
\fBnpm uninstall\fR takes 3 exclusive, optional flags which save or update
the package version in your main package\.json:
.
.IP "\(bu" 4
\fB\-\-save\fR: Package will be removed from your \fBdependencies\fR\|\.
.
.IP "\(bu" 4
\fB\-\-save\-dev\fR: Package will be removed from your \fBdevDependencies\fR\|\.
.
.IP "\(bu" 4
\fB\-\-save\-optional\fR: Package will be removed from your \fBoptionalDependencies\fR\|\.
.
.IP "" 0
.
.P
Examples:
.
.IP "" 4
.
.nf
npm uninstall sax \-\-save
npm uninstall node\-tap \-\-save\-dev
npm uninstall dtrace\-provider \-\-save\-optional
.
.fi
.
.IP "" 0
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help prune
.
.IP "\(bu" 4
npm help install
.
.IP "\(bu" 4
npm help  folders
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "" 0

