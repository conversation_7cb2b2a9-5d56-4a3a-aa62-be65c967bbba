.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-OUTDATED" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-outdated\fR \-\- Check for outdated packages
.
.SH "SYNOPSIS"
.
.nf
npm outdated [<name> [<name> \.\.\.]]
.
.fi
.
.SH "DESCRIPTION"
This command will check the registry to see if any (or, specific) installed
packages are currently outdated\.
.
.P
The resulting field \'wanted\' shows the latest version according to the
version specified in the package\.json, the field \'latest\' the very latest
version of the package\.
.
.SH "CONFIGURATION"
.
.SS "json"
.
.IP "\(bu" 4
Default: false
.
.IP "\(bu" 4
Type: Boolean
.
.IP "" 0
.
.P
Show information in JSON format\.
.
.SS "long"
.
.IP "\(bu" 4
Default: false
.
.IP "\(bu" 4
Type: Boolean
.
.IP "" 0
.
.P
Show extended information\.
.
.SS "parseable"
.
.IP "\(bu" 4
Default: false
.
.IP "\(bu" 4
Type: Boolean
.
.IP "" 0
.
.P
Show parseable output instead of tree view\.
.
.SS "global"
.
.IP "\(bu" 4
Default: false
.
.IP "\(bu" 4
Type: Boolean
.
.IP "" 0
.
.P
Check packages in the global install prefix instead of in the current
project\.
.
.SS "depth"
.
.IP "\(bu" 4
Type: Int
.
.IP "" 0
.
.P
Max depth for checking dependency tree\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help update
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help  folders
.
.IP "" 0

