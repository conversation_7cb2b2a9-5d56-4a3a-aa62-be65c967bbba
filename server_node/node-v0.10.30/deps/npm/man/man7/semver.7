.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "SEMVER" "7" "July 2014" "" ""
.
.SH "NAME"
\fBsemver\fR \-\- The semantic versioner for npm
.
.SH "Usage"
.
.nf
$ npm install semver
semver\.valid(\'1\.2\.3\') // \'1\.2\.3\'
semver\.valid(\'a\.b\.c\') // null
semver\.clean(\'  =v1\.2\.3   \') // \'1\.2\.3\'
semver\.satisfies(\'1\.2\.3\', \'1\.x || >=2\.5\.0 || 5\.0\.0 \- 7\.2\.3\') // true
semver\.gt(\'1\.2\.3\', \'9\.8\.7\') // false
semver\.lt(\'1\.2\.3\', \'9\.8\.7\') // true
.
.fi
.
.P
As a command\-line utility:
.
.IP "" 4
.
.nf
$ semver \-h
Usage: semver <version> [<version> [\.\.\.]] [\-r <range> | \-i <inc> | \-d <dec>]
Test if version(s) satisfy the supplied range(s), and sort them\.
Multiple versions or ranges may be supplied, unless increment
or decrement options are specified\.  In that case, only a single
version may be used, and it is incremented by the specified level
Program exits successfully if any valid version satisfies
all supplied ranges, and prints all satisfying versions\.
If no versions are valid, or ranges are not satisfied,
then exits failure\.
Versions are printed in ascending order, so supplying
multiple versions to the utility will just sort them\.
.
.fi
.
.IP "" 0
.
.SH "Versions"
A "version" is described by the \fBv2\.0\.0\fR specification found at \fIhttp://semver\.org/\fR\|\.
.
.P
A leading \fB"="\fR or \fB"v"\fR character is stripped off and ignored\.
.
.SH "Ranges"
The following range styles are supported:
.
.IP "\(bu" 4
\fB1\.2\.3\fR A specific version\.  When nothing else will do\.  Must be a full
version number, with major, minor, and patch versions specified\.
Note that build metadata is still ignored, so \fB1\.2\.3+build2012\fR will
satisfy this range\.
.
.IP "\(bu" 4
\fB>1\.2\.3\fR Greater than a specific version\.
.
.IP "\(bu" 4
\fB<1\.2\.3\fR Less than a specific version\.  If there is no prerelease
tag on the version range, then no prerelease version will be allowed
either, even though these are technically "less than"\.
.
.IP "\(bu" 4
\fB>=1\.2\.3\fR Greater than or equal to\.  Note that prerelease versions
are NOT equal to their "normal" equivalents, so \fB1\.2\.3\-beta\fR will
not satisfy this range, but \fB2\.3\.0\-beta\fR will\.
.
.IP "\(bu" 4
\fB<=1\.2\.3\fR Less than or equal to\.  In this case, prerelease versions
ARE allowed, so \fB1\.2\.3\-beta\fR would satisfy\.
.
.IP "\(bu" 4
\fB1\.2\.3 \- 2\.3\.4\fR := \fB>=1\.2\.3 <=2\.3\.4\fR
.
.IP "\(bu" 4
\fB~1\.2\.3\fR := \fB>=1\.2\.3\-0 <1\.3\.0\-0\fR  "Reasonably close to \fB1\.2\.3\fR"\.  When
using tilde operators, prerelease versions are supported as well,
but a prerelease of the next significant digit will NOT be
satisfactory, so \fB1\.3\.0\-beta\fR will not satisfy \fB~1\.2\.3\fR\|\.
.
.IP "\(bu" 4
\fB^1\.2\.3\fR := \fB>=1\.2\.3\-0 <2\.0\.0\-0\fR  "Compatible with \fB1\.2\.3\fR"\.  When
using caret operators, anything from the specified version (including
prerelease) will be supported up to, but not including, the next
major version (or its prereleases)\. \fB1\.5\.1\fR will satisfy \fB^1\.2\.3\fR,
while \fB1\.2\.2\fR and \fB2\.0\.0\-beta\fR will not\.
.
.IP "\(bu" 4
\fB^0\.1\.3\fR := \fB>=0\.1\.3\-0 <0\.2\.0\-0\fR "Compatible with \fB0\.1\.3\fR"\. \fB0\.x\.x\fR versions are
special: the first non\-zero component indicates potentially breaking changes,
meaning the caret operator matches any version with the same first non\-zero
component starting at the specified version\.
.
.IP "\(bu" 4
\fB^0\.0\.2\fR := \fB=0\.0\.2\fR "Only the version \fB0\.0\.2\fR is considered compatible"
.
.IP "\(bu" 4
\fB~1\.2\fR := \fB>=1\.2\.0\-0 <1\.3\.0\-0\fR "Any version starting with \fB1\.2\fR"
.
.IP "\(bu" 4
\fB^1\.2\fR := \fB>=1\.2\.0\-0 <2\.0\.0\-0\fR "Any version compatible with \fB1\.2\fR"
.
.IP "\(bu" 4
\fB1\.2\.x\fR := \fB>=1\.2\.0\-0 <1\.3\.0\-0\fR "Any version starting with \fB1\.2\fR"
.
.IP "\(bu" 4
\fB1\.2\.*\fR Same as \fB1\.2\.x\fR\|\.
.
.IP "\(bu" 4
\fB1\.2\fR Same as \fB1\.2\.x\fR\|\.
.
.IP "\(bu" 4
\fB~1\fR := \fB>=1\.0\.0\-0 <2\.0\.0\-0\fR "Any version starting with \fB1\fR"
.
.IP "\(bu" 4
\fB^1\fR := \fB>=1\.0\.0\-0 <2\.0\.0\-0\fR "Any version compatible with \fB1\fR"
.
.IP "\(bu" 4
\fB1\.x\fR := \fB>=1\.0\.0\-0 <2\.0\.0\-0\fR "Any version starting with \fB1\fR"
.
.IP "\(bu" 4
\fB1\.*\fR Same as \fB1\.x\fR\|\.
.
.IP "\(bu" 4
\fB1\fR Same as \fB1\.x\fR\|\.
.
.IP "\(bu" 4
\fB*\fR Any version whatsoever\.
.
.IP "\(bu" 4
\fBx\fR Same as \fB*\fR\|\.
.
.IP "\(bu" 4
\fB""\fR (just an empty string) Same as \fB*\fR\|\.
.
.IP "" 0
.
.P
Ranges can be joined with either a space (which implies "and") or a \fB||\fR (which implies "or")\.
.
.SH "Functions"
All methods and classes take a final \fBloose\fR boolean argument that, if
true, will be more forgiving about not\-quite\-valid semver strings\.
The resulting output will always be 100% strict, of course\.
.
.P
Strict\-mode Comparators and Ranges will be strict about the SemVer
strings that they parse\.
.
.IP "\(bu" 4
\fBvalid(v)\fR: Return the parsed version, or null if it\'s not valid\.
.
.IP "\(bu" 4
\fBinc(v, release)\fR\fBmajor\fR\fBpremajor\fR\fBminor\fR\fBpreminor\fR\fBpatch\fR\fBprepatch\fR\fBprerelease\fR
.
.IP "\(bu" 4
\fBpremajor\fR in one call will bump the version up to the next major
version and down to a prerelease of that major version\. \fBpreminor\fR, and \fBprepatch\fR work the same way\.
.
.IP "\(bu" 4
If called from a non\-prerelease version, the \fBprerelease\fR will work the
same as \fBprepatch\fR\|\. It increments the patch version, then makes a
prerelease\. If the input version is already a prerelease it simply
increments it\.
.
.IP "" 0

.
.IP "" 0
.
.SS "Comparison"
.
.IP "\(bu" 4
\fBgt(v1, v2)\fR: \fBv1 > v2\fR
.
.IP "\(bu" 4
\fBgte(v1, v2)\fR: \fBv1 >= v2\fR
.
.IP "\(bu" 4
\fBlt(v1, v2)\fR: \fBv1 < v2\fR
.
.IP "\(bu" 4
\fBlte(v1, v2)\fR: \fBv1 <= v2\fR
.
.IP "\(bu" 4
\fBeq(v1, v2)\fR: \fBv1 == v2\fR This is true if they\'re logically equivalent,
even if they\'re not the exact same string\.  You already know how to
compare strings\.
.
.IP "\(bu" 4
\fBneq(v1, v2)\fR: \fBv1 != v2\fR The opposite of \fBeq\fR\|\.
.
.IP "\(bu" 4
\fBcmp(v1, comparator, v2)\fR: Pass in a comparison string, and it\'ll call
the corresponding function above\.  \fB"==="\fR and \fB"!=="\fR do simple
string comparison, but are included for completeness\.  Throws if an
invalid comparison string is provided\.
.
.IP "\(bu" 4
\fBcompare(v1, v2)\fR: Return \fB0\fR if \fBv1 == v2\fR, or \fB1\fR if \fBv1\fR is greater, or \fB\-1\fR if \fBv2\fR is greater\.  Sorts in ascending order if passed to \fBArray\.sort()\fR\|\.
.
.IP "\(bu" 4
\fBrcompare(v1, v2)\fR: The reverse of compare\.  Sorts an array of versions
in descending order when passed to \fBArray\.sort()\fR\|\.
.
.IP "" 0
.
.SS "Ranges"
.
.IP "\(bu" 4
\fBvalidRange(range)\fR: Return the valid range or null if it\'s not valid
.
.IP "\(bu" 4
\fBsatisfies(version, range)\fR: Return true if the version satisfies the
range\.
.
.IP "\(bu" 4
\fBmaxSatisfying(versions, range)\fR: Return the highest version in the list
that satisfies the range, or \fBnull\fR if none of them do\.
.
.IP "\(bu" 4
\fBgtr(version, range)\fR: Return \fBtrue\fR if version is greater than all the
versions possible in the range\.
.
.IP "\(bu" 4
\fBltr(version, range)\fR: Return \fBtrue\fR if version is less than all the
versions possible in the range\.
.
.IP "\(bu" 4
\fBoutside(version, range, hilo)\fR: Return true if the version is outside
the bounds of the range in either the high or low direction\.  The \fBhilo\fR argument must be either the string \fB\'>\'\fR or \fB\'<\'\fR\|\.  (This is
the function called by \fBgtr\fR and \fBltr\fR\|\.)
.
.IP "" 0
.
.P
Note that, since ranges may be non\-contiguous, a version might not be
greater than a range, less than a range, \fIor\fR satisfy a range!  For
example, the range \fB1\.2 <1\.2\.9 || >2\.0\.0\fR would have a hole from \fB1\.2\.9\fR
until \fB2\.0\.0\fR, so the version \fB1\.2\.10\fR would not be greater than the
range (because \fB2\.0\.1\fR satisfies, which is higher), nor less than the
range (since \fB1\.2\.8\fR satisfies, which is lower), and it also does not
satisfy the range\.
.
.P
If you want to know if a version satisfies or does not satisfy a
range, use the \fBsatisfies(version, range)\fR function\.
