.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-FAQ" "7" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-faq\fR \-\- Frequently Asked Questions
.
.SH "Where can I find these docs in HTML?"
\fIhttps://www\.npmjs\.org/doc/\fR, or run:
.
.IP "" 4
.
.nf
npm config set viewer browser
.
.fi
.
.IP "" 0
.
.P
to open these documents in your default web browser rather than \fBman\fR\|\.
.
.SH "It didn&#39;t work\."
That\'s not really a question\.
.
.SH "Why didn&#39;t it work?"
I don\'t know yet\.
.
.P
Read the error output, and if you can\'t figure out what it means,
do what it says and post a bug with all the information it asks for\.
.
.SH "Where does npm put stuff?"
npm help  See \fBnpm\-folders\fR
.
.P
tl;dr:
.
.IP "\(bu" 4
Use the \fBnpm root\fR command to see where modules go, and the \fBnpm bin\fR
command to see where executables go
.
.IP "\(bu" 4
Global installs are different from local installs\.  If you install
something with the \fB\-g\fR flag, then its executables go in \fBnpm bin \-g\fR
and its modules go in \fBnpm root \-g\fR\|\.
.
.IP "" 0
.
.SH "How do I install something on my computer in a central location?"
Install it globally by tacking \fB\-g\fR or \fB\-\-global\fR to the command\.  (This
is especially important for command line utilities that need to add
their bins to the global system \fBPATH\fR\|\.)
.
.SH "I installed something globally, but I can&#39;t "
Install it locally\.
.
.P
The global install location is a place for command\-line utilities
to put their bins in the system \fBPATH\fR\|\.  It\'s not for use with \fBrequire()\fR\|\.
.
.P
If you \fBrequire()\fR a module in your code, then that means it\'s a
dependency, and a part of your program\.  You need to install it locally
in your program\.
.
.SH "Why can&#39;t npm just put everything in one place, like other package managers?"
Not every change is an improvement, but every improvement is a change\.
This would be like asking git to do network IO for every commit\.  It\'s
not going to happen, because it\'s a terrible idea that causes more
problems than it solves\.
.
.P
It is much harder to avoid dependency conflicts without nesting
dependencies\.  This is fundamental to the way that npm works, and has
npm help  proven to be an extremely successful approach\.  See \fBnpm\-folders\fR for
more details\.
.
.P
If you want a package to be installed in one place, and have all your
programs reference the same copy of it, then use the \fBnpm link\fR command\.
That\'s what it\'s for\.  Install it globally, then link it into each
program that uses it\.
.
.SH "Whatever, I really want the old style &#39;everything global&#39; style\."
Write your own package manager\.  You could probably even wrap up \fBnpm\fR
in a shell script if you really wanted to\.
.
.P
npm will not help you do something that is known to be a bad idea\.
.
.SH "Should I check my "
Mikeal Rogers answered this question very well:
.
.P
\fIhttp://www\.futurealoof\.com/posts/nodemodules\-in\-git\.html\fR
.
.P
tl;dr
.
.IP "\(bu" 4
Check \fBnode_modules\fR into git for things you \fBdeploy\fR, such as
websites and apps\.
.
.IP "\(bu" 4
Do not check \fBnode_modules\fR into git for libraries and modules
intended to be reused\.
.
.IP "\(bu" 4
Use npm to manage dependencies in your dev environment, but not in
your deployment scripts\.
.
.IP "" 0
.
.SH "Is it &#39;npm&#39; or &#39;NPM&#39; or &#39;Npm&#39;?"
npm should never be capitalized unless it is being displayed in a
location that is customarily all\-caps (such as the title of man pages\.)
.
.SH "If &#39;npm&#39; is an acronym, why is it never capitalized?"
Contrary to the belief of many, "npm" is not in fact an abbreviation for
"Node Package Manager"\.  It is a recursive bacronymic abbreviation for
"npm is not an acronym"\.  (If it was "ninaa", then it would be an
acronym, and thus incorrectly named\.)
.
.P
"NPM", however, \fIis\fR an acronym (more precisely, a capitonym) for the
National Association of Pastoral Musicians\.  You can learn more
about them at \fIhttp://npm\.org/\fR\|\.
.
.P
In software, "NPM" is a Non\-Parametric Mapping utility written by
Chris Rorden\.  You can analyze pictures of brains with it\.  Learn more
about the (capitalized) NPM program at \fIhttp://www\.cabiatl\.com/mricro/npm/\fR\|\.
.
.P
The first seed that eventually grew into this flower was a bash utility
named "pm", which was a shortened descendent of "pkgmakeinst", a
bash function that was used to install various different things on different
platforms, most often using Yahoo\'s \fByinst\fR\|\.  If \fBnpm\fR was ever an
acronym for anything, it was \fBnode pm\fR or maybe \fBnew pm\fR\|\.
.
.P
So, in all seriousness, the "npm" project is named after its command\-line
utility, which was organically selected to be easily typed by a right\-handed
programmer using a US QWERTY keyboard layout, ending with the
right\-ring\-finger in a postition to type the \fB\-\fR key for flags and
other command\-line arguments\.  That command\-line utility is always
lower\-case, though it starts most sentences it is a part of\.
.
.SH "How do I list installed packages?"
\fBnpm ls\fR
.
.SH "How do I search for packages?"
\fBnpm search\fR
.
.P
Arguments are greps\.  \fBnpm search jsdom\fR shows jsdom packages\.
.
.SH "How do I update npm?"
.
.nf
npm update npm \-g
.
.fi
.
.P
You can also update all outdated local packages by doing \fBnpm update\fR without
any arguments, or global packages by doing \fBnpm update \-g\fR\|\.
.
.P
Occasionally, the version of npm will progress such that the current
version cannot be properly installed with the version that you have
installed already\.  (Consider, if there is ever a bug in the \fBupdate\fR
command\.)
.
.P
In those cases, you can do this:
.
.IP "" 4
.
.nf
curl https://www\.npmjs\.org/install\.sh | sh
.
.fi
.
.IP "" 0
.
.SH "What is a "
A package is:
.
.IP "\(bu" 4
a) a folder containing a program described by a package\.json file
.
.IP "\(bu" 4
b) a gzipped tarball containing (a)
.
.IP "\(bu" 4
c) a url that resolves to (b)
.
.IP "\(bu" 4
d) a \fB<name>@<version>\fR that is published on the registry with (c)
.
.IP "\(bu" 4
e) a \fB<name>@<tag>\fR that points to (d)
.
.IP "\(bu" 4
f) a \fB<name>\fR that has a "latest" tag satisfying (e)
.
.IP "\(bu" 4
g) a \fBgit\fR url that, when cloned, results in (a)\.
.
.IP "" 0
.
.P
Even if you never publish your package, you can still get a lot of
benefits of using npm if you just want to write a node program (a), and
perhaps if you also want to be able to easily install it elsewhere
after packing it up into a tarball (b)\.
.
.P
Git urls can be of the form:
.
.IP "" 4
.
.nf
git://github\.com/user/project\.git#commit\-ish
git+ssh://user@hostname:project\.git#commit\-ish
git+http://user@hostname/project/blah\.git#commit\-ish
git+https://user@hostname/project/blah\.git#commit\-ish
.
.fi
.
.IP "" 0
.
.P
The \fBcommit\-ish\fR can be any tag, sha, or branch which can be supplied as
an argument to \fBgit checkout\fR\|\.  The default is \fBmaster\fR\|\.
.
.SH "What is a "
A module is anything that can be loaded with \fBrequire()\fR in a Node\.js
program\.  The following things are all examples of things that can be
loaded as modules:
.
.IP "\(bu" 4
A folder with a \fBpackage\.json\fR file containing a \fBmain\fR field\.
.
.IP "\(bu" 4
A folder with an \fBindex\.js\fR file in it\.
.
.IP "\(bu" 4
A JavaScript file\.
.
.IP "" 0
.
.P
Most npm packages are modules, because they are libraries that you
load with \fBrequire\fR\|\.  However, there\'s no requirement that an npm
package be a module!  Some only contain an executable command\-line
interface, and don\'t provide a \fBmain\fR field for use in Node programs\.
.
.P
Almost all npm packages (at least, those that are Node programs) \fIcontain\fR many modules within them (because every file they load with \fBrequire()\fR is a module)\.
.
.P
In the context of a Node program, the \fBmodule\fR is also the thing that
was loaded \fIfrom\fR a file\.  For example, in the following program:
.
.IP "" 4
.
.nf
var req = require(\'request\')
.
.fi
.
.IP "" 0
.
.P
we might say that "The variable \fBreq\fR refers to the \fBrequest\fR module"\.
.
.SH "So, why is it the &quot;"
The \fBpackage\.json\fR file defines the package\.  (See "What is a
package?" above\.)
.
.P
The \fBnode_modules\fR folder is the place Node\.js looks for modules\.
(See "What is a module?" above\.)
.
.P
For example, if you create a file at \fBnode_modules/foo\.js\fR and then
had a program that did \fBvar f = require(\'foo\.js\')\fR then it would load
the module\.  However, \fBfoo\.js\fR is not a "package" in this case,
because it does not have a package\.json\.
.
.P
Alternatively, if you create a package which does not have an \fBindex\.js\fR or a \fB"main"\fR field in the \fBpackage\.json\fR file, then it is
not a module\.  Even if it\'s installed in \fBnode_modules\fR, it can\'t be
an argument to \fBrequire()\fR\|\.
.
.SH "<code>&quot;node_modules&quot;</code>"
No\.  This will never happen\.  This question comes up sometimes,
because it seems silly from the outside that npm couldn\'t just be
configured to put stuff somewhere else, and then npm could load them
from there\.  It\'s an arbitrary spelling choice, right?  What\'s the big
deal?
.
.P
At the time of this writing, the string \fB\'node_modules\'\fR appears 151
times in 53 separate files in npm and node core (excluding tests and
documentation)\.
.
.P
Some of these references are in node\'s built\-in module loader\.  Since
npm is not involved \fBat all\fR at run\-time, node itself would have to
be configured to know where you\'ve decided to stick stuff\.  Complexity
hurdle #1\.  Since the Node module system is locked, this cannot be
changed, and is enough to kill this request\.  But I\'ll continue, in
deference to your deity\'s delicate feelings regarding spelling\.
.
.P
Many of the others are in dependencies that npm uses, which are not
necessarily tightly coupled to npm (in the sense that they do not read
npm\'s configuration files, etc\.)  Each of these would have to be
configured to take the name of the \fBnode_modules\fR folder as a
parameter\.  Complexity hurdle #2\.
.
.P
Furthermore, npm has the ability to "bundle" dependencies by adding
the dep names to the \fB"bundledDependencies"\fR list in package\.json,
which causes the folder to be included in the package tarball\.  What
if the author of a module bundles its dependencies, and they use a
different spelling for \fBnode_modules\fR?  npm would have to rename the
folder at publish time, and then be smart enough to unpack it using
your locally configured name\.  Complexity hurdle #3\.
.
.P
Furthermore, what happens when you \fIchange\fR this name?  Fine, it\'s
easy enough the first time, just rename the \fBnode_modules\fR folders to \fB\|\./blergyblerp/\fR or whatever name you choose\.  But what about when you
change it again?  npm doesn\'t currently track any state about past
configuration settings, so this would be rather difficult to do
properly\.  It would have to track every previous value for this
config, and always accept any of them, or else yesterday\'s install may
be broken tomorrow\.  Complexity hurdle #4\.
.
.P
Never going to happen\.  The folder is named \fBnode_modules\fR\|\.  It is
written indelibly in the Node Way, handed down from the ancient times
of Node 0\.3\.
.
.SH "How do I install node with npm?"
You don\'t\.  Try one of these node version managers:
.
.P
Unix:
.
.IP "\(bu" 4
\fIhttp://github\.com/isaacs/nave\fR
.
.IP "\(bu" 4
\fIhttp://github\.com/visionmedia/n\fR
.
.IP "\(bu" 4
\fIhttp://github\.com/creationix/nvm\fR
.
.IP "" 0
.
.P
Windows:
.
.IP "\(bu" 4
\fIhttp://github\.com/marcelklehr/nodist\fR
.
.IP "\(bu" 4
\fIhttps://github\.com/hakobera/nvmw\fR
.
.IP "\(bu" 4
\fIhttps://github\.com/nanjingboy/nvmw\fR
.
.IP "" 0
.
.SH "How can I use npm for development?"
npm help  See \fBnpm\-developersnpm help  \fR and \fBpackage\.json\fR\|\.
.
.P
You\'ll most likely want to \fBnpm link\fR your development folder\.  That\'s
awesomely handy\.
.
.P
npm help  To set up your own private registry, check out \fBnpm\-registry\fR\|\.
.
.SH "Can I list a url as a dependency?"
Yes\.  It should be a url to a gzipped tarball containing a single folder
that has a package\.json in its root, or a git url\.
(See "what is a package?" above\.)
.
.SH "How do I symlink to a dev folder so I don&#39;t have to keep re\-installing?"
npm help See \fBnpm\-link\fR
.
.SH "The package registry website\.  What is that exactly?"
npm help  See \fBnpm\-registry\fR\|\.
.
.SH "I forgot my password, and can&#39;t publish\.  How do I reset it?"
Go to \fIhttps://npmjs\.org/forgot\fR\|\.
.
.SH "I get ECONNREFUSED a lot\.  What&#39;s up?"
Either the registry is down, or node\'s DNS isn\'t able to reach out\.
.
.P
To check if the registry is down, open up \fIhttps://registry\.npmjs\.org/\fR in a web browser\.  This will also tell
you if you are just unable to access the internet for some reason\.
.
.P
If the registry IS down, let us know by emailing \fIsupport@npmjs\.com\fR
or posting an issue at \fIhttps://github\.com/npm/npm/issues\fR\|\.  If it\'s
down for the world (and not just on your local network) then we\'re
probably already being pinged about it\.
.
.P
You can also often get a faster response by visiting the #npm channel
on Freenode IRC\.
.
.SH "Why no namespaces?"
Please see this discussion: \fIhttps://github\.com/npm/npm/issues/798\fR
.
.P
tl;dr \- It doesn\'t actually make things better, and can make them worse\.
.
.P
If you want to namespace your own packages, you may: simply use the \fB\-\fR character to separate the names\.  npm is a mostly anarchic system\.
There is not sufficient need to impose namespace rules on everyone\.
.
.SH "Who does npm?"
npm was originally written by Isaac Z\. Schlueter, and many others have
contributed to it, some of them quite substantially\.
.
.P
The npm open source project, The npm Registry, and the community
website \fIhttps://www\.npmjs\.org\fR are maintained and operated by the
good folks at npm, Inc\. \fIhttp://www\.npmjs\.com\fR
.
.SH "I have a question or request not addressed here\. Where should I put it?"
Post an issue on the github project:
.
.IP "\(bu" 4
\fIhttps://github\.com/npm/npm/issues\fR
.
.IP "" 0
.
.SH "Why does npm hate me?"
npm is not capable of hatred\.  It loves everyone, especially you\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help npm
.
.IP "\(bu" 4
npm help  developers
.
.IP "\(bu" 4
npm help  package\.json
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  folders
.
.IP "" 0

