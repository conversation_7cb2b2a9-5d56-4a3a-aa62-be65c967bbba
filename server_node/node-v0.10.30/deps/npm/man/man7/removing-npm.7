.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-REMOVAL" "1" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-removal\fR \-\- Cleaning the Slate
.
.SH "SYNOPSIS"
So sad to see you go\.
.
.IP "" 4
.
.nf
sudo npm uninstall npm \-g
.
.fi
.
.IP "" 0
.
.P
Or, if that fails, get the npm source code, and do:
.
.IP "" 4
.
.nf
sudo make uninstall
.
.fi
.
.IP "" 0
.
.SH "More Severe Uninstalling"
Usually, the above instructions are sufficient\.  That will remove
npm, but leave behind anything you\'ve installed\.
.
.P
If that doesn\'t work, or if you require more drastic measures,
continue reading\.
.
.P
Note that this is only necessary for globally\-installed packages\.  Local
installs are completely contained within a project\'s \fBnode_modules\fR
folder\.  Delete that folder, and everything is gone (unless a package\'s
install script is particularly ill\-behaved)\.
.
.P
This assumes that you installed node and npm in the default place\.  If
you configured node with a different \fB\-\-prefix\fR, or installed npm with a
different prefix setting, then adjust the paths accordingly, replacing \fB/usr/local\fR with your install prefix\.
.
.P
To remove everything npm\-related manually:
.
.IP "" 4
.
.nf
rm \-rf /usr/local/{lib/node{,/\.npm,_modules},bin,share/man}/npm*
.
.fi
.
.IP "" 0
.
.P
If you installed things \fIwith\fR npm, then your best bet is to uninstall
them with npm first, and then install them again once you have a
proper install\.  This can help find any symlinks that are lying
around:
.
.IP "" 4
.
.nf
ls \-laF /usr/local/{lib/node{,/\.npm},bin,share/man} | grep npm
.
.fi
.
.IP "" 0
.
.P
Prior to version 0\.3, npm used shim files for executables and node
modules\.  To track those down, you can do the following:
.
.IP "" 4
.
.nf
find /usr/local/{lib/node,bin} \-exec grep \-l npm \\{\\} \\; ;
.
.fi
.
.IP "" 0
.
.P
(This is also in the README file\.)
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
README
.
.IP "\(bu" 4
npm help rm
.
.IP "\(bu" 4
npm help prune
.
.IP "" 0

