.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-REGISTRY" "7" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-registry\fR \-\- The JavaScript Package Registry
.
.SH "DESCRIPTION"
To resolve packages by name and version, npm talks to a registry website
that implements the CommonJS Package Registry specification for reading
package info\.
.
.P
Additionally, npm\'s package registry implementation supports several
write APIs as well, to allow for publishing packages and managing user
account information\.
.
.P
The official public npm registry is at \fIhttp://registry\.npmjs\.org/\fR\|\.  It
is powered by a CouchDB database at \fIhttp://isaacs\.iriscouch\.com/registry\fR\|\.  The code for the couchapp is
available at \fIhttp://github\.com/npm/npmjs\.org\fR\|\.  npm user accounts
are CouchDB users, stored in the \fIhttp://isaacs\.iriscouch\.com/_users\fR
database\.
.
.P
npm help  npm help The registry URL is supplied by the \fBregistry\fR config parameter\.  See \fBnpm\-config\fR, \fBnpmrcnpm help  \fR, and \fBnpm\-config\fR for more on managing
npm\'s configuration\.
.
.SH "Can I run my own private registry?"
Yes!
.
.P
The easiest way is to replicate the couch database, and use the same (or
similar) design doc to implement the APIs\.
.
.P
If you set up continuous replication from the official CouchDB, and then
set your internal CouchDB as the registry config, then you\'ll be able
to read any published packages, in addition to your private ones, and by
default will only publish internally\.  If you then want to publish a
package for the whole world to see, you can simply override the \fB\-\-registry\fR config for that command\.
.
.SH "I don&#39;t want my package published in the official registry\. It&#39;s private\."
Set \fB"private": true\fR in your package\.json to prevent it from being
published at all, or \fB"publishConfig":{"registry":"http://my\-internal\-registry\.local"}\fR
to force it to be published only to your internal registry\.
.
.P
npm help  See \fBpackage\.json\fR for more info on what goes in the package\.json file\.
.
.SH "Will you replicate from my registry into the public one?"
No\.  If you want things to be public, then publish them into the public
registry using npm\.  What little security there is would be for nought
otherwise\.
.
.SH "Do I have to use couchdb to build a registry that npm can talk to?"
No, but it\'s way easier\.  Basically, yes, you do, or you have to
effectively implement the entire CouchDB API anyway\.
.
.SH "Is there a website or something to see package docs and such?"
Yes, head over to \fIhttps://npmjs\.org/\fR
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help config
.
.IP "\(bu" 4
npm help  config
.
.IP "\(bu" 4
npm help  npmrc
.
.IP "\(bu" 4
npm help  developers
.
.IP "\(bu" 4
npm help  disputes
.
.IP "" 0

