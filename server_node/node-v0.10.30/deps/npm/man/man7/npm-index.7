.\" Generated with <PERSON>njs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-INDEX" "7" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-index\fR \-\- Index of all npm documentation
.
npm help .SH "README"
node package manager
.
npm help .SH "npm"
node package manager
.
npm help .SH "npm\-adduser"
Add a registry user account
.
npm help .SH "npm\-bin"
Display npm bin folder
.
npm help .SH "npm\-bugs"
Bugs for a package in a web browser maybe
.
npm help .SH "npm\-build"
Build a package
.
npm help .SH "npm\-bundle"
REMOVED
.
npm help .SH "npm\-cache"
Manipulates packages cache
.
npm help .SH "npm\-completion"
Tab Completion for npm
.
npm help .SH "npm\-config"
Manage the npm configuration files
.
npm help .SH "npm\-dedupe"
Reduce duplication
.
npm help .SH "npm\-deprecate"
Deprecate a version of a package
.
npm help .SH "npm\-docs"
Docs for a package in a web browser maybe
.
npm help .SH "npm\-edit"
Edit an installed package
.
npm help .SH "npm\-explore"
Browse an installed package
.
npm help .SH "npm\-help\-search"
Search npm help documentation
.
npm help .SH "npm\-help"
Get help on npm
.
npm help .SH "npm\-init"
Interactively create a package\.json file
.
npm help .SH "npm\-install"
Install a package
.
npm help .SH "npm\-link"
Symlink a package folder
.
npm help .SH "npm\-ls"
List installed packages
.
npm help .SH "npm\-outdated"
Check for outdated packages
.
npm help .SH "npm\-owner"
Manage package owners
.
npm help .SH "npm\-pack"
Create a tarball from a package
.
npm help .SH "npm\-prefix"
Display prefix
.
npm help .SH "npm\-prune"
Remove extraneous packages
.
npm help .SH "npm\-publish"
Publish a package
.
npm help .SH "npm\-rebuild"
Rebuild a package
.
npm help .SH "npm\-repo"
Open package repository page in the browser
.
npm help .SH "npm\-restart"
Start a package
.
npm help .SH "npm\-rm"
Remove a package
.
npm help .SH "npm\-root"
Display npm root
.
npm help .SH "npm\-run\-script"
Run arbitrary package scripts
.
npm help .SH "npm\-search"
Search for packages
.
npm help .SH "npm\-shrinkwrap"
Lock down dependency versions
.
npm help .SH "npm\-star"
Mark your favorite packages
.
npm help .SH "npm\-stars"
View packages marked as favorites
.
npm help .SH "npm\-start"
Start a package
.
npm help .SH "npm\-stop"
Stop a package
.
npm help .SH "npm\-submodule"
Add a package as a git submodule
.
npm help .SH "npm\-tag"
Tag a published version
.
npm help .SH "npm\-test"
Test a package
.
npm help .SH "npm\-uninstall"
Remove a package
.
npm help .SH "npm\-unpublish"
Remove a package from the registry
.
npm help .SH "npm\-update"
Update a package
.
npm help .SH "npm\-version"
Bump a package version
.
npm help .SH "npm\-view"
View registry info
.
npm help .SH "npm\-whoami"
Display npm username
.
npm apihelp .SH "npm"
node package manager
.
npm apihelp .SH "npm\-bin"
Display npm bin folder
.
npm apihelp .SH "npm\-bugs"
Bugs for a package in a web browser maybe
.
npm apihelp .SH "npm\-cache"
manage the npm cache programmatically
.
npm apihelp .SH "npm\-commands"
npm commands
.
npm apihelp .SH "npm\-config"
Manage the npm configuration files
.
npm apihelp .SH "npm\-deprecate"
Deprecate a version of a package
.
npm apihelp .SH "npm\-docs"
Docs for a package in a web browser maybe
.
npm apihelp .SH "npm\-edit"
Edit an installed package
.
npm apihelp .SH "npm\-explore"
Browse an installed package
.
npm apihelp .SH "npm\-help\-search"
Search the help pages
.
npm apihelp .SH "npm\-init"
Interactively create a package\.json file
.
npm apihelp .SH "npm\-install"
install a package programmatically
.
npm apihelp .SH "npm\-link"
Symlink a package folder
.
npm apihelp .SH "npm\-load"
Load config settings
.
npm apihelp .SH "npm\-ls"
List installed packages
.
npm apihelp .SH "npm\-outdated"
Check for outdated packages
.
npm apihelp .SH "npm\-owner"
Manage package owners
.
npm apihelp .SH "npm\-pack"
Create a tarball from a package
.
npm apihelp .SH "npm\-prefix"
Display prefix
.
npm apihelp .SH "npm\-prune"
Remove extraneous packages
.
npm apihelp .SH "npm\-publish"
Publish a package
.
npm apihelp .SH "npm\-rebuild"
Rebuild a package
.
npm apihelp .SH "npm\-repo"
Open package repository page in the browser
.
npm apihelp .SH "npm\-restart"
Start a package
.
npm apihelp .SH "npm\-root"
Display npm root
.
npm apihelp .SH "npm\-run\-script"
Run arbitrary package scripts
.
npm apihelp .SH "npm\-search"
Search for packages
.
npm apihelp .SH "npm\-shrinkwrap"
programmatically generate package shrinkwrap file
.
npm apihelp .SH "npm\-start"
Start a package
.
npm apihelp .SH "npm\-stop"
Stop a package
.
npm apihelp .SH "npm\-submodule"
Add a package as a git submodule
.
npm apihelp .SH "npm\-tag"
Tag a published version
.
npm apihelp .SH "npm\-test"
Test a package
.
npm apihelp .SH "npm\-uninstall"
uninstall a package programmatically
.
npm apihelp .SH "npm\-unpublish"
Remove a package from the registry
.
npm apihelp .SH "npm\-update"
Update a package
.
npm apihelp .SH "npm\-version"
Bump a package version
.
npm apihelp .SH "npm\-view"
View registry info
.
npm apihelp .SH "npm\-whoami"
Display npm username
.
npm help  .SH "npm\-folders"
Folder Structures Used by npm
.
npm help  .SH "npmrc"
The npm config files
.
npm help  .SH "package\.json"
Specifics of npm\'s package\.json handling
.
npm help  .SH "npm\-coding\-style"
npm\'s "funny" coding style
.
npm help  .SH "npm\-config"
More than you probably want to know about npm configuration
.
npm help  .SH "npm\-developers"
Developer Guide
.
npm help  .SH "npm\-disputes"
Handling Module Name Disputes
.
npm help  .SH "npm\-faq"
Frequently Asked Questions
.
npm help  .SH "npm\-index"
Index of all npm documentation
.
npm help  .SH "npm\-registry"
The JavaScript Package Registry
.
npm help  .SH "npm\-scripts"
How npm handles the "scripts" field
.
npm help  .SH "removing\-npm"
Cleaning the Slate
.
npm help  .SH "semver"
The semantic versioner for npm
