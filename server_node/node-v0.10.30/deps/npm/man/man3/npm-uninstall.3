.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-UNINSTALL" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-uninstall\fR \-\- uninstall a package programmatically
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.uninstall(packages, callback)
.
.fi
.
.SH "DESCRIPTION"
This acts much the same ways as uninstalling on the command\-line\.
.
.P
The \'packages\' parameter is an array of strings\. Each element in the array is
the name of a package to be uninstalled\.
.
.P
Finally, \'callback\' is a function that will be called when all packages have been
uninstalled or when an error has been encountered\.
