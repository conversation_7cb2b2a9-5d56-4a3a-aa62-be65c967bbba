.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-TEST" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-test\fR \-\- Test a package
.
.SH "SYNOPSIS"
.
.nf
  npm\.commands\.test(packages, callback)
.
.fi
.
.SH "DESCRIPTION"
This runs a package\'s "test" script, if one was provided\.
.
.P
To run tests as a condition of installation, set the \fBnpat\fR config to
true\.
.
.P
npm can run tests on multiple packages\. Just specify multiple packages
in the \fBpackages\fR parameter\.
