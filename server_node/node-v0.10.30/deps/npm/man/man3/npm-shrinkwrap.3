.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-SHRINKWRAP" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-shrinkwrap\fR \-\- programmatically generate package shrinkwrap file
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.shrinkwrap(args, [silent,] callback)
.
.fi
.
.SH "DESCRIPTION"
This acts much the same ways as shrinkwrapping on the command\-line\.
.
.P
This command does not take any arguments, but \'args\' must be defined\.
Beyond that, if any arguments are passed in, n<PERSON> will politely warn that it
does not take positional arguments\.
.
.P
If the \'silent\' parameter is set to true, nothing will be output to the screen,
but the shrinkwrap file will still be written\.
.
.P
Finally, \'callback\' is a function that will be called when the shrinkwrap has
been saved\.
