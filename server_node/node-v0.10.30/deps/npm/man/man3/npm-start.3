.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-START" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-start\fR \-\- Start a package
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.start(packages, callback)
.
.fi
.
.SH "DESCRIPTION"
This runs a package\'s "start" script, if one was provided\.
.
.P
npm can run tests on multiple packages\. Just specify multiple packages
in the \fBpackages\fR parameter\.
