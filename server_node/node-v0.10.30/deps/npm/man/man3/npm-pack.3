.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-PACK" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-pack\fR \-\- Create a tarball from a package
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.pack([packages,] callback)
.
.fi
.
.SH "DESCRIPTION"
For anything that\'s installable (that is, a package folder, tarball,
tarball url, name@tag, name@version, or name), this command will fetch
it to the cache, and then copy the tarball to the current working
directory as \fB<name>\-<version>\.tgz\fR, and then write the filenames out to
stdout\.
.
.P
If the same package is specified multiple times, then the file will be
overwritten the second time\.
.
.P
If no arguments are supplied, then npm packs the current package folder\.
