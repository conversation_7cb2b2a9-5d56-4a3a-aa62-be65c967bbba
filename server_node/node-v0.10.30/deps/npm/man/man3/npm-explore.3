.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-EXPLORE" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-explore\fR \-\- Browse an installed package
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.explore(args, callback)
.
.fi
.
.SH "DESCRIPTION"
Spawn a subshell in the directory of the installed package specified\.
.
.P
If a command is specified, then it is run in the subshell, which then
immediately terminates\.
.
.P
Note that the package is \fInot\fR automatically rebuilt afterwards, so be
sure to use \fBnpm rebuild <pkg>\fR if you make any changes\.
.
.P
The first element in the \'args\' parameter must be a package name\.  After that is the optional command, which can be any number of strings\. All of the strings will be combined into one, space\-delimited command\.
