.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-WHOAMI" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-whoami\fR \-\- Display npm username
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.whoami(args, callback)
.
.fi
.
.SH "DESCRIPTION"
Print the \fBusername\fR config to standard output\.
.
.P
\'args\' is never used and callback is never called with data\.
\'args\' must be present or things will break\.
.
.P
This function is not useful programmatically
