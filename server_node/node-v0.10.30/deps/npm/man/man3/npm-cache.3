.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-CACHE" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-cache\fR \-\- manage the npm cache programmatically
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.cache([args], callback)
// helpers
npm\.commands\.cache\.clean([args], callback)
npm\.commands\.cache\.add([args], callback)
npm\.commands\.cache\.read(name, version, forceBypass, callback)
.
.fi
.
.SH "DESCRIPTION"
npm help This acts much the same ways as the npm\-cache command line
functionality\.
.
.P
The callback is called with the package\.json data of the thing that is
eventually added to or read from the cache\.
.
.P
The top level \fBnpm\.commands\.cache(\.\.\.)\fR functionality is a public
interface, and like all commands on the \fBnpm\.commands\fR object, it will
match the command line behavior exactly\.
.
.P
However, the cache folder structure and the cache helper functions are
considered \fBinternal\fR API surface, and as such, may change in future
releases of npm, potentially without warning or significant version
incrementation\.
.
.P
Use at your own risk\.
