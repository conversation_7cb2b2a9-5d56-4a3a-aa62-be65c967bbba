.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-REPO" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-repo\fR \-\- Open package repository page in the browser
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.repo(package, callback)
.
.fi
.
.SH "DESCRIPTION"
This command tries to guess at the likely location of a package\'s
repository URL, and then tries to open it using the \fB\-\-browser\fR
config param\.
.
.P
Like other commands, the first parameter is an array\. This command only
uses the first element, which is expected to be a package name with an
optional version number\.
.
.P
This command will launch a browser, so this command may not be the most
friendly for programmatic use\.
