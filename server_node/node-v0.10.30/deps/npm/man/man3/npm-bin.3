.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-BIN" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-bin\fR \-\- Display npm bin folder
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.bin(args, cb)
.
.fi
.
.SH "DESCRIPTION"
Print the folder where npm will install executables\.
.
.P
This function should not be used programmatically\.  Instead, just refer
to the \fBnpm\.bin\fR member\.
