.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-OUTDATED" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-outdated\fR \-\- Check for outdated packages
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.outdated([packages,] callback)
.
.fi
.
.SH "DESCRIPTION"
This command will check the registry to see if the specified packages are
currently outdated\.
.
.P
If the \'packages\' parameter is left out, npm will check all packages\.
