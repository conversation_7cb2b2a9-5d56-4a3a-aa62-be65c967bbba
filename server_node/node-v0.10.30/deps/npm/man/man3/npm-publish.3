.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-PUBLISH" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-publish\fR \-\- Publish a package
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.publish([packages,] callback)
.
.fi
.
.SH "DESCRIPTION"
Publishes a package to the registry so that it can be installed by name\.
Possible values in the \'packages\' array are:
.
.IP "\(bu" 4
\fB<folder>\fR:
A folder containing a package\.json file
.
.IP "\(bu" 4
\fB<tarball>\fR:
A url or file path to a gzipped tar archive containing a single folder
with a package\.json file inside\.
.
.IP "" 0
.
.P
If the package array is empty, npm will try to publish something in the
current working directory\.
.
.P
This command could fails if one of the packages specified already exists in
the registry\.  Overwrites when the "force" environment variable is set\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help  registry
.
.IP "\(bu" 4
npm help adduser
.
.IP "\(bu" 4
npm apihelp owner
.
.IP "" 0

