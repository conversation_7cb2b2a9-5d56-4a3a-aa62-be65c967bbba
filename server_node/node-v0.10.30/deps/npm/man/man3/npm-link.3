.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-LINK" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-link\fR \-\- Symlink a package folder
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.link(callback)
npm\.commands\.link(packages, callback)
.
.fi
.
.SH "DESCRIPTION"
Package linking is a two\-step process\.
.
.P
Without parameters, link will create a globally\-installed
symbolic link from \fBprefix/package\-name\fR to the current folder\.
.
.P
With a parameters, link will create a symlink from the local \fBnode_modules\fR
folder to the global symlink\.
.
.P
When creating tarballs for \fBnpm publish\fR, the linked packages are
"snapshotted" to their current state by resolving the symbolic links\.
.
.P
This is
handy for installing your own stuff, so that you can work on it and test it
iteratively without having to continually rebuild\.
.
.P
For example:
.
.IP "" 4
.
.nf
npm\.commands\.link(cb)           # creates global link from the cwd
                                # (say redis package)
npm\.commands\.link(\'redis\', cb)  # link\-install the package
.
.fi
.
.IP "" 0
.
.P
Now, any changes to the redis package will be reflected in
the package in the current working directory
