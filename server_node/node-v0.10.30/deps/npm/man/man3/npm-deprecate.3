.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-DEPRECATE" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-deprecate\fR \-\- Deprecate a version of a package
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.deprecate(args, callback)
.
.fi
.
.SH "DESCRIPTION"
This command will update the npm registry entry for a package, providing
a deprecation warning to all who attempt to install it\.
.
.P
The \'args\' parameter must have exactly two elements:
.
.IP "\(bu" 4
\fBpackage[@version]\fR
.
.IP
The \fBversion\fR portion is optional, and may be either a range, or a
specific version, or a tag\.
.
.IP "\(bu" 4
\fBmessage\fR
.
.IP
The warning message that will be printed whenever a user attempts to
install the package\.
.
.IP "" 0
.
.P
Note that you must be the package owner to deprecate something\.  See the \fBowner\fR and \fBadduser\fR help topics\.
.
.P
To un\-deprecate a package, specify an empty string (\fB""\fR) for the \fBmessage\fR argument\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm apihelp publish
.
.IP "\(bu" 4
npm apihelp unpublish
.
.IP "\(bu" 4
npm help  registry
.
.IP "" 0

