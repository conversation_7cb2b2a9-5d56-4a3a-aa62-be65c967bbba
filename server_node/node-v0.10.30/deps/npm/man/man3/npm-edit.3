.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-EDIT" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-edit\fR \-\- Edit an installed package
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.edit(package, callback)
.
.fi
.
.SH "DESCRIPTION"
Opens the package folder in the default editor (or whatever you\'ve
configured as the npm \fBeditor\fR config \-\- see \fBnpm help config\fR\|\.)
.
.P
After it has been edited, the package is rebuilt so as to pick up any
changes in compiled packages\.
.
.P
For instance, you can do \fBnpm install connect\fR to install connect
into your package, and then \fBnpm\.commands\.edit(["connect"], callback)\fR
to make a few changes to your locally installed copy\.
.
.P
The first parameter is a string array with a single element, the package
to open\. The package can optionally have a version number attached\.
.
.P
Since this command opens an editor in a new process, be careful about where
and how this is used\.
