.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-CONFIG" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-config\fR \-\- Manage the npm configuration files
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.config(args, callback)
var val = npm\.config\.get(key)
npm\.config\.set(key, val)
.
.fi
.
.SH "DESCRIPTION"
This function acts much the same way as the command\-line version\.  The first
element in the array tells config what to do\. Possible values are:
.
.IP "\(bu" 4
\fBset\fR
.
.IP
Sets a config parameter\.  The second element in \fBargs\fR is interpreted as the
key, and the third element is interpreted as the value\.
.
.IP "\(bu" 4
\fBget\fR
.
.IP
Gets the value of a config parameter\. The second element in \fBargs\fR is the
key to get the value of\.
.
.IP "\(bu" 4
\fBdelete\fR (\fBrm\fR or \fBdel\fR)
.
.IP
Deletes a parameter from the config\. The second element in \fBargs\fR is the
key to delete\.
.
.IP "\(bu" 4
\fBlist\fR (\fBls\fR)
.
.IP
Show all configs that aren\'t secret\. No parameters necessary\.
.
.IP "\(bu" 4
\fBedit\fR:
.
.IP
Opens the config file in the default editor\. This command isn\'t very useful
programmatically, but it is made available\.
.
.IP "" 0
.
.P
To programmatically access npm configuration settings, or set them for
the duration of a program, use the \fBnpm\.config\.set\fR and \fBnpm\.config\.get\fR
functions instead\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm apihelp npm
.
.IP "" 0

