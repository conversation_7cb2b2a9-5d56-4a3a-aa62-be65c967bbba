.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-UPDATE" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-update\fR \-\- Update a package
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.update(packages, callback)
.
.fi
Updates a package, upgrading it to the latest version\. It also installs any missing packages\.
.
.P
The \'packages\' argument is an array of packages to update\. The \'callback\' parameter will be called when done or when an error occurs\.
