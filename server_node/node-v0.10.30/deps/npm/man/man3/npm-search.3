.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-SEARCH" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-search\fR \-\- Search for packages
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.search(searchTerms, [silent,] [staleness,] callback)
.
.fi
.
.SH "DESCRIPTION"
Search the registry for packages matching the search terms\. The available parameters are:
.
.IP "\(bu" 4
searchTerms:
Array of search terms\. These terms are case\-insensitive\.
.
.IP "\(bu" 4
silent:
If true, npm will not log anything to the console\.
.
.IP "\(bu" 4
staleness:
This is the threshold for stale packages\. "Fresh" packages are not refreshed
from the registry\. This value is measured in seconds\.
.
.IP "\(bu" 4
callback:
Returns an object where each key is the name of a package, and the value
is information about that package along with a \'words\' property, which is
a space\-delimited string of all of the interesting words in that package\.
The only properties included are those that are searched, which generally include:
.
.IP "\(bu" 4
name
.
.IP "\(bu" 4
description
.
.IP "\(bu" 4
maintainers
.
.IP "\(bu" 4
url
.
.IP "\(bu" 4
keywords
.
.IP "" 0

.
.IP "" 0
.
.P
A search on the registry excludes any result that does not match all of the
search terms\. It also removes any items from the results that contain an
excluded term (the "searchexclude" config)\. The search is case insensitive
and doesn\'t try to read your mind (it doesn\'t do any verb tense matching or the
like)\.
