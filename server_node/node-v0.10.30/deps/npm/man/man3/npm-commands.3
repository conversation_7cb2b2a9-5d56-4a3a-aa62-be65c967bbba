.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-COMMANDS" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-commands\fR \-\- npm commands
.
.SH "SYNOPSIS"
.
.nf
npm\.commands[<command>](args, callback)
.
.fi
.
.SH "DESCRIPTION"
npm comes with a full set of commands, and each of the commands takes a
similar set of arguments\.
.
.P
In general, all commands on the command object take an \fBarray\fR of positional
argument \fBstrings\fR\|\. The last argument to any function is a callback\. Some
commands are special and take other optional arguments\.
.
.P
All commands have their own man page\. See \fBman npm\-<command>\fR for command\-line
usage, or \fBman 3 npm\-<command>\fR for programmatic usage\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm help  index
.
.IP "" 0

