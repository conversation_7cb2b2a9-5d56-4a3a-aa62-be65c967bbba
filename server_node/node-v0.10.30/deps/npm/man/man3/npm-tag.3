.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-TAG" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-tag\fR \-\- Tag a published version
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.tag(package@version, tag, callback)
.
.fi
.
.SH "DESCRIPTION"
Tags the specified version of the package with the specified tag, or the \fB\-\-tag\fR config if not specified\.
.
.P
The \'package@version\' is an array of strings, but only the first two elements are
currently used\.
.
.P
The first element must be in the form package@version, where package
is the package name and version is the version number (much like installing a
specific version)\.
.
.P
The second element is the name of the tag to tag this version with\. If this
parameter is missing or falsey (empty), the default froom the config will be
used\. For more information about how to set this config, check \fBman 3 npm\-config\fR for programmatic usage or \fBman npm\-config\fR for cli usage\.
