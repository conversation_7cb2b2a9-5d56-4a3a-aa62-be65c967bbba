.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-STOP" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-stop\fR \-\- Stop a package
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.stop(packages, callback)
.
.fi
.
.SH "DESCRIPTION"
This runs a package\'s "stop" script, if one was provided\.
.
.P
npm can run stop on multiple packages\. Just specify multiple packages
in the \fBpackages\fR parameter\.
