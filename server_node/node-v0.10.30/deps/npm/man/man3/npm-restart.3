.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-RESTART" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-restart\fR \-\- Start a package
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.restart(packages, callback)
.
.fi
.
.SH "DESCRIPTION"
This runs a package\'s "restart" script, if one was provided\.
Otherwise it runs package\'s "stop" script, if one was provided, and then
the "start" script\.
.
.P
If no version is specified, then it restarts the "active" version\.
.
.P
npm can run tests on multiple packages\. Just specify multiple packages
in the \fBpackages\fR parameter\.
.
.SH "SEE ALSO"
.
.IP "\(bu" 4
npm apihelp start
.
.IP "\(bu" 4
npm apihelp stop
.
.IP "" 0

