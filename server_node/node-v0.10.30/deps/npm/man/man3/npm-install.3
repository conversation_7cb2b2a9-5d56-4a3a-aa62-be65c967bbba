.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-INSTALL" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-install\fR \-\- install a package programmatically
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.install([where,] packages, callback)
.
.fi
.
.SH "DESCRIPTION"
This acts much the same ways as installing on the command\-line\.
.
.P
The \'where\' parameter is optional and only used internally, and it specifies
where the packages should be installed to\.
.
.P
The \'packages\' parameter is an array of strings\. Each element in the array is
the name of a package to be installed\.
.
.P
Finally, \'callback\' is a function that will be called when all packages have been
installed or when an error has been encountered\.
