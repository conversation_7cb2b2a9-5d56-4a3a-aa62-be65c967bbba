.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-VIEW" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-view\fR \-\- View registry info
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.view(args, [silent,] callback)
.
.fi
.
.SH "DESCRIPTION"
This command shows data about a package and prints it to the stream
referenced by the \fBoutfd\fR config, which defaults to stdout\.
.
.P
The "args" parameter is an ordered list that closely resembles the command\-line
usage\. The elements should be ordered such that the first element is
the package and version (package@version)\. The version is optional\. After that,
the rest of the parameters are fields with optional subfields ("field\.subfield")
which can be used to get only the information desired from the registry\.
.
.P
The callback will be passed all of the data returned by the query\.
.
.P
For example, to get the package registry entry for the \fBconnect\fR package,
you can do this:
.
.IP "" 4
.
.nf
npm\.commands\.view(["connect"], callback)
.
.fi
.
.IP "" 0
.
.P
If no version is specified, "latest" is assumed\.
.
.P
Field names can be specified after the package descriptor\.
For example, to show the dependencies of the \fBronn\fR package at version
0\.3\.5, you could do the following:
.
.IP "" 4
.
.nf
npm\.commands\.view(["ronn@0\.3\.5", "dependencies"], callback)
.
.fi
.
.IP "" 0
.
.P
You can view child field by separating them with a period\.
To view the git repository URL for the latest version of npm, you could
do this:
.
.IP "" 4
.
.nf
npm\.commands\.view(["npm", "repository\.url"], callback)
.
.fi
.
.IP "" 0
.
.P
For fields that are arrays, requesting a non\-numeric field will return
all of the values from the objects in the list\.  For example, to get all
the contributor names for the "express" project, you can do this:
.
.IP "" 4
.
.nf
npm\.commands\.view(["express", "contributors\.email"], callback)
.
.fi
.
.IP "" 0
.
.P
You may also use numeric indices in square braces to specifically select
an item in an array field\.  To just get the email address of the first
contributor in the list, you can do this:
.
.IP "" 4
.
.nf
npm\.commands\.view(["express", "contributors[0]\.email"], callback)
.
.fi
.
.IP "" 0
.
.P
Multiple fields may be specified, and will be printed one after another\.
For exampls, to get all the contributor names and email addresses, you
can do this:
.
.IP "" 4
.
.nf
npm\.commands\.view(["express", "contributors\.name", "contributors\.email"], callback)
.
.fi
.
.IP "" 0
.
.P
"Person" fields are shown as a string if they would be shown as an
object\.  So, for example, this will show the list of npm contributors in
the shortened string format\.  (See \fBnpm help json\fR for more on this\.)
.
.IP "" 4
.
.nf
npm\.commands\.view(["npm", "contributors"], callback)
.
.fi
.
.IP "" 0
.
.P
If a version range is provided, then data will be printed for every
matching version of the package\.  This will show which version of jsdom
was required by each matching version of yui3:
.
.IP "" 4
.
.nf
npm\.commands\.view(["yui3@\'>0\.5\.4\'", "dependencies\.jsdom"], callback)
.
.fi
.
.IP "" 0
.
.SH "OUTPUT"
If only a single string field for a single version is output, then it
will not be colorized or quoted, so as to enable piping the output to
another command\.
.
.P
If the version range matches multiple versions, than each printed value
will be prefixed with the version it applies to\.
.
.P
If multiple fields are requested, than each of them are prefixed with
the field name\.
.
.P
Console output can be disabled by setting the \'silent\' parameter to true\.
.
.SH "RETURN VALUE"
The data returned will be an object in this formation:
.
.IP "" 4
.
.nf
{ <version>:
  { <field>: <value>
  , \.\.\. }
, \.\.\. }
.
.fi
.
.IP "" 0
.
.P
corresponding to the list of fields selected\.
