.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-VERSION" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-version\fR \-\- Bump a package version
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.version(newversion, callback)
.
.fi
.
.SH "DESCRIPTION"
Run this in a package directory to bump the version and write the new
data back to the package\.json file\.
.
.P
If run in a git repo, it will also create a version commit and tag, and
fail if the repo is not clean\.
.
.P
Like all other commands, this function takes a string array as its first
parameter\. The difference, however, is this function will fail if it does
not have exactly one element\. The only element should be a version number\.
