.\" Generated with Ronnjs 0.3.8
.\" http://github.com/kapouer/ronnjs/
.
.TH "NPM\-PREFIX" "3" "July 2014" "" ""
.
.SH "NAME"
\fBnpm-prefix\fR \-\- Display prefix
.
.SH "SYNOPSIS"
.
.nf
npm\.commands\.prefix(args, callback)
.
.fi
.
.SH "DESCRIPTION"
Print the prefix to standard out\.
.
.P
\'args\' is never used and callback is never called with data\.
\'args\' must be present or things will break\.
.
.P
This function is not useful programmatically
