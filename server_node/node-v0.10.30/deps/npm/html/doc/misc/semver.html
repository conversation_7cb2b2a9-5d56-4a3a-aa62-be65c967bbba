<!doctype html>
<html>
  <title>semver</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/misc/semver.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../misc/semver.html">semver</a></h1> <p>The semantic versioner for npm</p>
<h2 id="usage">Usage</h2>
<pre><code>$ npm install semver

semver.valid(&#39;1.2.3&#39;) // &#39;1.2.3&#39;
semver.valid(&#39;a.b.c&#39;) // null
semver.clean(&#39;  =v1.2.3   &#39;) // &#39;1.2.3&#39;
semver.satisfies(&#39;1.2.3&#39;, &#39;1.x || &gt;=2.5.0 || 5.0.0 - 7.2.3&#39;) // true
semver.gt(&#39;1.2.3&#39;, &#39;9.8.7&#39;) // false
semver.lt(&#39;1.2.3&#39;, &#39;9.8.7&#39;) // true
</code></pre><p>As a command-line utility:</p>
<pre><code>$ semver -h

Usage: semver &lt;version&gt; [&lt;version&gt; [...]] [-r &lt;range&gt; | -i &lt;inc&gt; | -d &lt;dec&gt;]
Test if version(s) satisfy the supplied range(s), and sort them.

Multiple versions or ranges may be supplied, unless increment
or decrement options are specified.  In that case, only a single
version may be used, and it is incremented by the specified level

Program exits successfully if any valid version satisfies
all supplied ranges, and prints all satisfying versions.

If no versions are valid, or ranges are not satisfied,
then exits failure.

Versions are printed in ascending order, so supplying
multiple versions to the utility will just sort them.
</code></pre><h2 id="versions">Versions</h2>
<p>A &quot;version&quot; is described by the <code>v2.0.0</code> specification found at
<a href="http://semver.org/">http://semver.org/</a>.</p>
<p>A leading <code>&quot;=&quot;</code> or <code>&quot;v&quot;</code> character is stripped off and ignored.</p>
<h2 id="ranges">Ranges</h2>
<p>The following range styles are supported:</p>
<ul>
<li><code>1.2.3</code> A specific version.  When nothing else will do.  Must be a full
version number, with major, minor, and patch versions specified.
Note that build metadata is still ignored, so <code>1.2.3+build2012</code> will
satisfy this range.</li>
<li><code>&gt;1.2.3</code> Greater than a specific version.</li>
<li><code>&lt;1.2.3</code> Less than a specific version.  If there is no prerelease
tag on the version range, then no prerelease version will be allowed
either, even though these are technically &quot;less than&quot;.</li>
<li><code>&gt;=1.2.3</code> Greater than or equal to.  Note that prerelease versions
are NOT equal to their &quot;normal&quot; equivalents, so <code>1.2.3-beta</code> will
not satisfy this range, but <code>2.3.0-beta</code> will.</li>
<li><code>&lt;=1.2.3</code> Less than or equal to.  In this case, prerelease versions
ARE allowed, so <code>1.2.3-beta</code> would satisfy.</li>
<li><code>1.2.3 - 2.3.4</code> := <code>&gt;=1.2.3 &lt;=2.3.4</code></li>
<li><code>~1.2.3</code> := <code>&gt;=1.2.3-0 &lt;1.3.0-0</code>  &quot;Reasonably close to <code>1.2.3</code>&quot;.  When
using tilde operators, prerelease versions are supported as well,
but a prerelease of the next significant digit will NOT be
satisfactory, so <code>1.3.0-beta</code> will not satisfy <code>~1.2.3</code>.</li>
<li><code>^1.2.3</code> := <code>&gt;=1.2.3-0 &lt;2.0.0-0</code>  &quot;Compatible with <code>1.2.3</code>&quot;.  When
using caret operators, anything from the specified version (including
prerelease) will be supported up to, but not including, the next
major version (or its prereleases). <code>1.5.1</code> will satisfy <code>^1.2.3</code>,
while <code>1.2.2</code> and <code>2.0.0-beta</code> will not.</li>
<li><code>^0.1.3</code> := <code>&gt;=0.1.3-0 &lt;0.2.0-0</code> &quot;Compatible with <code>0.1.3</code>&quot;. <code>0.x.x</code> versions are
special: the first non-zero component indicates potentially breaking changes,
meaning the caret operator matches any version with the same first non-zero
component starting at the specified version.</li>
<li><code>^0.0.2</code> := <code>=0.0.2</code> &quot;Only the version <code>0.0.2</code> is considered compatible&quot;</li>
<li><code>~1.2</code> := <code>&gt;=1.2.0-0 &lt;1.3.0-0</code> &quot;Any version starting with <code>1.2</code>&quot;</li>
<li><code>^1.2</code> := <code>&gt;=1.2.0-0 &lt;2.0.0-0</code> &quot;Any version compatible with <code>1.2</code>&quot;</li>
<li><code>1.2.x</code> := <code>&gt;=1.2.0-0 &lt;1.3.0-0</code> &quot;Any version starting with <code>1.2</code>&quot;</li>
<li><code>1.2.*</code> Same as <code>1.2.x</code>.</li>
<li><code>1.2</code> Same as <code>1.2.x</code>.</li>
<li><code>~1</code> := <code>&gt;=1.0.0-0 &lt;2.0.0-0</code> &quot;Any version starting with <code>1</code>&quot;</li>
<li><code>^1</code> := <code>&gt;=1.0.0-0 &lt;2.0.0-0</code> &quot;Any version compatible with <code>1</code>&quot;</li>
<li><code>1.x</code> := <code>&gt;=1.0.0-0 &lt;2.0.0-0</code> &quot;Any version starting with <code>1</code>&quot;</li>
<li><code>1.*</code> Same as <code>1.x</code>.</li>
<li><code>1</code> Same as <code>1.x</code>.</li>
<li><code>*</code> Any version whatsoever.</li>
<li><code>x</code> Same as <code>*</code>.</li>
<li><code>&quot;&quot;</code> (just an empty string) Same as <code>*</code>.</li>
</ul>
<p>Ranges can be joined with either a space (which implies &quot;and&quot;) or a
<code>||</code> (which implies &quot;or&quot;).</p>
<h2 id="functions">Functions</h2>
<p>All methods and classes take a final <code>loose</code> boolean argument that, if
true, will be more forgiving about not-quite-valid semver strings.
The resulting output will always be 100% strict, of course.</p>
<p>Strict-mode Comparators and Ranges will be strict about the SemVer
strings that they parse.</p>
<ul>
<li><code>valid(v)</code>: Return the parsed version, or null if it&#39;s not valid.</li>
<li><code>inc(v, release)</code>: Return the version incremented by the release
type (<code>major</code>,   <code>premajor</code>, <code>minor</code>, <code>preminor</code>, <code>patch</code>,
<code>prepatch</code>, or <code>prerelease</code>), or null if it&#39;s not valid<ul>
<li><code>premajor</code> in one call will bump the version up to the next major
version and down to a prerelease of that major version.
<code>preminor</code>, and <code>prepatch</code> work the same way.</li>
<li>If called from a non-prerelease version, the <code>prerelease</code> will work the
same as <code>prepatch</code>. It increments the patch version, then makes a
prerelease. If the input version is already a prerelease it simply
increments it.</li>
</ul>
</li>
</ul>
<h3 id="comparison">Comparison</h3>
<ul>
<li><code>gt(v1, v2)</code>: <code>v1 &gt; v2</code></li>
<li><code>gte(v1, v2)</code>: <code>v1 &gt;= v2</code></li>
<li><code>lt(v1, v2)</code>: <code>v1 &lt; v2</code></li>
<li><code>lte(v1, v2)</code>: <code>v1 &lt;= v2</code></li>
<li><code>eq(v1, v2)</code>: <code>v1 == v2</code> This is true if they&#39;re logically equivalent,
even if they&#39;re not the exact same string.  You already know how to
compare strings.</li>
<li><code>neq(v1, v2)</code>: <code>v1 != v2</code> The opposite of <code>eq</code>.</li>
<li><code>cmp(v1, comparator, v2)</code>: Pass in a comparison string, and it&#39;ll call
the corresponding function above.  <code>&quot;===&quot;</code> and <code>&quot;!==&quot;</code> do simple
string comparison, but are included for completeness.  Throws if an
invalid comparison string is provided.</li>
<li><code>compare(v1, v2)</code>: Return <code>0</code> if <code>v1 == v2</code>, or <code>1</code> if <code>v1</code> is greater, or <code>-1</code> if
<code>v2</code> is greater.  Sorts in ascending order if passed to <code>Array.sort()</code>.</li>
<li><code>rcompare(v1, v2)</code>: The reverse of compare.  Sorts an array of versions
in descending order when passed to <code>Array.sort()</code>.</li>
</ul>
<h3 id="ranges">Ranges</h3>
<ul>
<li><code>validRange(range)</code>: Return the valid range or null if it&#39;s not valid</li>
<li><code>satisfies(version, range)</code>: Return true if the version satisfies the
range.</li>
<li><code>maxSatisfying(versions, range)</code>: Return the highest version in the list
that satisfies the range, or <code>null</code> if none of them do.</li>
<li><code>gtr(version, range)</code>: Return <code>true</code> if version is greater than all the
versions possible in the range.</li>
<li><code>ltr(version, range)</code>: Return <code>true</code> if version is less than all the
versions possible in the range.</li>
<li><code>outside(version, range, hilo)</code>: Return true if the version is outside
the bounds of the range in either the high or low direction.  The
<code>hilo</code> argument must be either the string <code>&#39;&gt;&#39;</code> or <code>&#39;&lt;&#39;</code>.  (This is
the function called by <code>gtr</code> and <code>ltr</code>.)</li>
</ul>
<p>Note that, since ranges may be non-contiguous, a version might not be
greater than a range, less than a range, <em>or</em> satisfy a range!  For
example, the range <code>1.2 &lt;1.2.9 || &gt;2.0.0</code> would have a hole from <code>1.2.9</code>
until <code>2.0.0</code>, so the version <code>1.2.10</code> would not be greater than the
range (because <code>2.0.1</code> satisfies, which is higher), nor less than the
range (since <code>1.2.8</code> satisfies, which is lower), and it also does not
satisfy the range.</p>
<p>If you want to know if a version satisfies or does not satisfy a
range, use the <code>satisfies(version, range)</code> function.</p>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">semver &mdash; npm@1.4.21</p>

