<!doctype html>
<html>
  <title>npm-faq</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/misc/npm-faq.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../misc/npm-faq.html">npm-faq</a></h1> <p>Frequently Asked Questions</p>
<h2 id="where-can-i-find-these-docs-in-html-">Where can I find these docs in HTML?</h2>
<p><a href="https://www.npmjs.org/doc/">https://www.npmjs.org/doc/</a>, or run:</p>
<pre><code>npm config set viewer browser
</code></pre><p>to open these documents in your default web browser rather than <code>man</code>.</p>
<h2 id="it-didn-t-work-">It didn&#39;t work.</h2>
<p>That&#39;s not really a question.</p>
<h2 id="why-didn-t-it-work-">Why didn&#39;t it work?</h2>
<p>I don&#39;t know yet.</p>
<p>Read the error output, and if you can&#39;t figure out what it means,
do what it says and post a bug with all the information it asks for.</p>
<h2 id="where-does-npm-put-stuff-">Where does npm put stuff?</h2>
<p>See <code><a href="../files/npm-folders.html">npm-folders(5)</a></code></p>
<p>tl;dr:</p>
<ul>
<li>Use the <code>npm root</code> command to see where modules go, and the <code>npm bin</code>
command to see where executables go</li>
<li>Global installs are different from local installs.  If you install
something with the <code>-g</code> flag, then its executables go in <code>npm bin -g</code>
and its modules go in <code>npm root -g</code>.</li>
</ul>
<h2 id="how-do-i-install-something-on-my-computer-in-a-central-location-">How do I install something on my computer in a central location?</h2>
<p>Install it globally by tacking <code>-g</code> or <code>--global</code> to the command.  (This
is especially important for command line utilities that need to add
their bins to the global system <code>PATH</code>.)</p>
<h2 id="i-installed-something-globally-but-i-can-t-require-it">I installed something globally, but I can&#39;t <code>require()</code> it</h2>
<p>Install it locally.</p>
<p>The global install location is a place for command-line utilities
to put their bins in the system <code>PATH</code>.  It&#39;s not for use with <code>require()</code>.</p>
<p>If you <code>require()</code> a module in your code, then that means it&#39;s a
dependency, and a part of your program.  You need to install it locally
in your program.</p>
<h2 id="why-can-t-npm-just-put-everything-in-one-place-like-other-package-managers-">Why can&#39;t npm just put everything in one place, like other package managers?</h2>
<p>Not every change is an improvement, but every improvement is a change.
This would be like asking git to do network IO for every commit.  It&#39;s
not going to happen, because it&#39;s a terrible idea that causes more
problems than it solves.</p>
<p>It is much harder to avoid dependency conflicts without nesting
dependencies.  This is fundamental to the way that npm works, and has
proven to be an extremely successful approach.  See <code><a href="../files/npm-folders.html">npm-folders(5)</a></code> for
more details.</p>
<p>If you want a package to be installed in one place, and have all your
programs reference the same copy of it, then use the <code>npm link</code> command.
That&#39;s what it&#39;s for.  Install it globally, then link it into each
program that uses it.</p>
<h2 id="whatever-i-really-want-the-old-style-everything-global-style-">Whatever, I really want the old style &#39;everything global&#39; style.</h2>
<p>Write your own package manager.  You could probably even wrap up <code>npm</code>
in a shell script if you really wanted to.</p>
<p>npm will not help you do something that is known to be a bad idea.</p>
<h2 id="should-i-check-my-node_modules-folder-into-git-">Should I check my <code>node_modules</code> folder into git?</h2>
<p>Mikeal Rogers answered this question very well:</p>
<p><a href="http://www.futurealoof.com/posts/nodemodules-in-git.html">http://www.futurealoof.com/posts/nodemodules-in-git.html</a></p>
<p>tl;dr</p>
<ul>
<li>Check <code>node_modules</code> into git for things you <strong>deploy</strong>, such as
websites and apps.</li>
<li>Do not check <code>node_modules</code> into git for libraries and modules
intended to be reused.</li>
<li>Use npm to manage dependencies in your dev environment, but not in
your deployment scripts.</li>
</ul>
<h2 id="is-it-npm-or-npm-or-npm-">Is it &#39;npm&#39; or &#39;NPM&#39; or &#39;Npm&#39;?</h2>
<p>npm should never be capitalized unless it is being displayed in a
location that is customarily all-caps (such as the title of man pages.)</p>
<h2 id="if-npm-is-an-acronym-why-is-it-never-capitalized-">If &#39;npm&#39; is an acronym, why is it never capitalized?</h2>
<p>Contrary to the belief of many, &quot;npm&quot; is not in fact an abbreviation for
&quot;Node Package Manager&quot;.  It is a recursive bacronymic abbreviation for
&quot;npm is not an acronym&quot;.  (If it was &quot;ninaa&quot;, then it would be an
acronym, and thus incorrectly named.)</p>
<p>&quot;NPM&quot;, however, <em>is</em> an acronym (more precisely, a capitonym) for the
National Association of Pastoral Musicians.  You can learn more
about them at <a href="http://npm.org/">http://npm.org/</a>.</p>
<p>In software, &quot;NPM&quot; is a Non-Parametric Mapping utility written by
Chris Rorden.  You can analyze pictures of brains with it.  Learn more
about the (capitalized) NPM program at <a href="http://www.cabiatl.com/mricro/npm/">http://www.cabiatl.com/mricro/npm/</a>.</p>
<p>The first seed that eventually grew into this flower was a bash utility
named &quot;pm&quot;, which was a shortened descendent of &quot;pkgmakeinst&quot;, a
bash function that was used to install various different things on different
platforms, most often using Yahoo&#39;s <code>yinst</code>.  If <code>npm</code> was ever an
acronym for anything, it was <code>node pm</code> or maybe <code>new pm</code>.</p>
<p>So, in all seriousness, the &quot;npm&quot; project is named after its command-line
utility, which was organically selected to be easily typed by a right-handed
programmer using a US QWERTY keyboard layout, ending with the
right-ring-finger in a postition to type the <code>-</code> key for flags and
other command-line arguments.  That command-line utility is always
lower-case, though it starts most sentences it is a part of.</p>
<h2 id="how-do-i-list-installed-packages-">How do I list installed packages?</h2>
<p><code>npm ls</code></p>
<h2 id="how-do-i-search-for-packages-">How do I search for packages?</h2>
<p><code>npm search</code></p>
<p>Arguments are greps.  <code>npm search jsdom</code> shows jsdom packages.</p>
<h2 id="how-do-i-update-npm-">How do I update npm?</h2>
<pre><code>npm update npm -g
</code></pre><p>You can also update all outdated local packages by doing <code>npm update</code> without
any arguments, or global packages by doing <code>npm update -g</code>.</p>
<p>Occasionally, the version of npm will progress such that the current
version cannot be properly installed with the version that you have
installed already.  (Consider, if there is ever a bug in the <code>update</code>
command.)</p>
<p>In those cases, you can do this:</p>
<pre><code>curl https://www.npmjs.org/install.sh | sh
</code></pre><h2 id="what-is-a-package-">What is a <code>package</code>?</h2>
<p>A package is:</p>
<ul>
<li>a) a folder containing a program described by a package.json file</li>
<li>b) a gzipped tarball containing (a)</li>
<li>c) a url that resolves to (b)</li>
<li>d) a <code>&lt;name&gt;@&lt;version&gt;</code> that is published on the registry with (c)</li>
<li>e) a <code>&lt;name&gt;@&lt;tag&gt;</code> that points to (d)</li>
<li>f) a <code>&lt;name&gt;</code> that has a &quot;latest&quot; tag satisfying (e)</li>
<li>g) a <code>git</code> url that, when cloned, results in (a).</li>
</ul>
<p>Even if you never publish your package, you can still get a lot of
benefits of using npm if you just want to write a node program (a), and
perhaps if you also want to be able to easily install it elsewhere
after packing it up into a tarball (b).</p>
<p>Git urls can be of the form:</p>
<pre><code>git://github.com/user/project.git#commit-ish
git+ssh://user@hostname:project.git#commit-ish
git+http://user@hostname/project/blah.git#commit-ish
git+https://user@hostname/project/blah.git#commit-ish
</code></pre><p>The <code>commit-ish</code> can be any tag, sha, or branch which can be supplied as
an argument to <code>git checkout</code>.  The default is <code>master</code>.</p>
<h2 id="what-is-a-module-">What is a <code>module</code>?</h2>
<p>A module is anything that can be loaded with <code>require()</code> in a Node.js
program.  The following things are all examples of things that can be
loaded as modules:</p>
<ul>
<li>A folder with a <code>package.json</code> file containing a <code>main</code> field.</li>
<li>A folder with an <code>index.js</code> file in it.</li>
<li>A JavaScript file.</li>
</ul>
<p>Most npm packages are modules, because they are libraries that you
load with <code>require</code>.  However, there&#39;s no requirement that an npm
package be a module!  Some only contain an executable command-line
interface, and don&#39;t provide a <code>main</code> field for use in Node programs.</p>
<p>Almost all npm packages (at least, those that are Node programs)
<em>contain</em> many modules within them (because every file they load with
<code>require()</code> is a module).</p>
<p>In the context of a Node program, the <code>module</code> is also the thing that
was loaded <em>from</em> a file.  For example, in the following program:</p>
<pre><code>var req = require(&#39;request&#39;)
</code></pre><p>we might say that &quot;The variable <code>req</code> refers to the <code>request</code> module&quot;.</p>
<h2 id="so-why-is-it-the-node_modules-folder-but-package-json-file-why-not-node_packages-or-module-json-">So, why is it the &quot;<code>node_modules</code>&quot; folder, but &quot;<code>package.json</code>&quot; file?  Why not <code>node_packages</code> or <code>module.json</code>?</h2>
<p>The <code>package.json</code> file defines the package.  (See &quot;What is a
package?&quot; above.)</p>
<p>The <code>node_modules</code> folder is the place Node.js looks for modules.
(See &quot;What is a module?&quot; above.)</p>
<p>For example, if you create a file at <code>node_modules/foo.js</code> and then
had a program that did <code>var f = require(&#39;foo.js&#39;)</code> then it would load
the module.  However, <code>foo.js</code> is not a &quot;package&quot; in this case,
because it does not have a package.json.</p>
<p>Alternatively, if you create a package which does not have an
<code>index.js</code> or a <code>&quot;main&quot;</code> field in the <code>package.json</code> file, then it is
not a module.  Even if it&#39;s installed in <code>node_modules</code>, it can&#39;t be
an argument to <code>require()</code>.</p>
<h2 id="-node_modules-is-the-name-of-my-deity-s-arch-rival-and-a-forbidden-word-in-my-religion-can-i-configure-npm-to-use-a-different-folder-"><code>&quot;node_modules&quot;</code> is the name of my deity&#39;s arch-rival, and a Forbidden Word in my religion.  Can I configure npm to use a different folder?</h2>
<p>No.  This will never happen.  This question comes up sometimes,
because it seems silly from the outside that npm couldn&#39;t just be
configured to put stuff somewhere else, and then npm could load them
from there.  It&#39;s an arbitrary spelling choice, right?  What&#39;s the big
deal?</p>
<p>At the time of this writing, the string <code>&#39;node_modules&#39;</code> appears 151
times in 53 separate files in npm and node core (excluding tests and
documentation).</p>
<p>Some of these references are in node&#39;s built-in module loader.  Since
npm is not involved <strong>at all</strong> at run-time, node itself would have to
be configured to know where you&#39;ve decided to stick stuff.  Complexity
hurdle #1.  Since the Node module system is locked, this cannot be
changed, and is enough to kill this request.  But I&#39;ll continue, in
deference to your deity&#39;s delicate feelings regarding spelling.</p>
<p>Many of the others are in dependencies that npm uses, which are not
necessarily tightly coupled to npm (in the sense that they do not read
npm&#39;s configuration files, etc.)  Each of these would have to be
configured to take the name of the <code>node_modules</code> folder as a
parameter.  Complexity hurdle #2.</p>
<p>Furthermore, npm has the ability to &quot;bundle&quot; dependencies by adding
the dep names to the <code>&quot;bundledDependencies&quot;</code> list in package.json,
which causes the folder to be included in the package tarball.  What
if the author of a module bundles its dependencies, and they use a
different spelling for <code>node_modules</code>?  npm would have to rename the
folder at publish time, and then be smart enough to unpack it using
your locally configured name.  Complexity hurdle #3.</p>
<p>Furthermore, what happens when you <em>change</em> this name?  Fine, it&#39;s
easy enough the first time, just rename the <code>node_modules</code> folders to
<code>./blergyblerp/</code> or whatever name you choose.  But what about when you
change it again?  npm doesn&#39;t currently track any state about past
configuration settings, so this would be rather difficult to do
properly.  It would have to track every previous value for this
config, and always accept any of them, or else yesterday&#39;s install may
be broken tomorrow.  Complexity hurdle #4.</p>
<p>Never going to happen.  The folder is named <code>node_modules</code>.  It is
written indelibly in the Node Way, handed down from the ancient times
of Node 0.3.</p>
<h2 id="how-do-i-install-node-with-npm-">How do I install node with npm?</h2>
<p>You don&#39;t.  Try one of these node version managers:</p>
<p>Unix:</p>
<ul>
<li><a href="http://github.com/isaacs/nave">http://github.com/isaacs/nave</a></li>
<li><a href="http://github.com/visionmedia/n">http://github.com/visionmedia/n</a></li>
<li><a href="http://github.com/creationix/nvm">http://github.com/creationix/nvm</a></li>
</ul>
<p>Windows:</p>
<ul>
<li><a href="http://github.com/marcelklehr/nodist">http://github.com/marcelklehr/nodist</a></li>
<li><a href="https://github.com/hakobera/nvmw">https://github.com/hakobera/nvmw</a></li>
<li><a href="https://github.com/nanjingboy/nvmw">https://github.com/nanjingboy/nvmw</a></li>
</ul>
<h2 id="how-can-i-use-npm-for-development-">How can I use npm for development?</h2>
<p>See <code><a href="../misc/npm-developers.html">npm-developers(7)</a></code> and <code><a href="../files/package.json.html">package.json(5)</a></code>.</p>
<p>You&#39;ll most likely want to <code>npm link</code> your development folder.  That&#39;s
awesomely handy.</p>
<p>To set up your own private registry, check out <code><a href="../misc/npm-registry.html">npm-registry(7)</a></code>.</p>
<h2 id="can-i-list-a-url-as-a-dependency-">Can I list a url as a dependency?</h2>
<p>Yes.  It should be a url to a gzipped tarball containing a single folder
that has a package.json in its root, or a git url.
(See &quot;what is a package?&quot; above.)</p>
<h2 id="how-do-i-symlink-to-a-dev-folder-so-i-don-t-have-to-keep-re-installing-">How do I symlink to a dev folder so I don&#39;t have to keep re-installing?</h2>
<p>See <code><a href="../cli/npm-link.html">npm-link(1)</a></code></p>
<h2 id="the-package-registry-website-what-is-that-exactly-">The package registry website.  What is that exactly?</h2>
<p>See <code><a href="../misc/npm-registry.html">npm-registry(7)</a></code>.</p>
<h2 id="i-forgot-my-password-and-can-t-publish-how-do-i-reset-it-">I forgot my password, and can&#39;t publish.  How do I reset it?</h2>
<p>Go to <a href="https://npmjs.org/forgot">https://npmjs.org/forgot</a>.</p>
<h2 id="i-get-econnrefused-a-lot-what-s-up-">I get ECONNREFUSED a lot.  What&#39;s up?</h2>
<p>Either the registry is down, or node&#39;s DNS isn&#39;t able to reach out.</p>
<p>To check if the registry is down, open up
<a href="https://registry.npmjs.org/">https://registry.npmjs.org/</a> in a web browser.  This will also tell
you if you are just unable to access the internet for some reason.</p>
<p>If the registry IS down, let us know by emailing <a href="&#x6d;&#x61;&#105;&#x6c;&#x74;&#111;&#58;&#x73;&#x75;&#x70;&#x70;&#111;&#114;&#116;&#64;&#x6e;&#112;&#109;&#x6a;&#x73;&#x2e;&#x63;&#111;&#x6d;">&#x73;&#x75;&#x70;&#x70;&#111;&#114;&#116;&#64;&#x6e;&#112;&#109;&#x6a;&#x73;&#x2e;&#x63;&#111;&#x6d;</a>
or posting an issue at <a href="https://github.com/npm/npm/issues">https://github.com/npm/npm/issues</a>.  If it&#39;s
down for the world (and not just on your local network) then we&#39;re
probably already being pinged about it.</p>
<p>You can also often get a faster response by visiting the #npm channel
on Freenode IRC.</p>
<h2 id="why-no-namespaces-">Why no namespaces?</h2>
<p>Please see this discussion: <a href="https://github.com/npm/npm/issues/798">https://github.com/npm/npm/issues/798</a></p>
<p>tl;dr - It doesn&#39;t actually make things better, and can make them worse.</p>
<p>If you want to namespace your own packages, you may: simply use the
<code>-</code> character to separate the names.  npm is a mostly anarchic system.
There is not sufficient need to impose namespace rules on everyone.</p>
<h2 id="who-does-npm-">Who does npm?</h2>
<p>npm was originally written by Isaac Z. Schlueter, and many others have
contributed to it, some of them quite substantially.</p>
<p>The npm open source project, The npm Registry, and <a href="https://www.npmjs.org">the community
website</a> are maintained and operated by the
good folks at <a href="http://www.npmjs.com">npm, Inc.</a></p>
<h2 id="i-have-a-question-or-request-not-addressed-here-where-should-i-put-it-">I have a question or request not addressed here. Where should I put it?</h2>
<p>Post an issue on the github project:</p>
<ul>
<li><a href="https://github.com/npm/npm/issues">https://github.com/npm/npm/issues</a></li>
</ul>
<h2 id="why-does-npm-hate-me-">Why does npm hate me?</h2>
<p>npm is not capable of hatred.  It loves everyone, especially you.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm.html">npm(1)</a></li>
<li><a href="../misc/npm-developers.html">npm-developers(7)</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-faq &mdash; npm@1.4.21</p>

