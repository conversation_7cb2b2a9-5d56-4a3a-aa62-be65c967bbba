<!doctype html>
<html>
  <title>npm-developers</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/misc/npm-developers.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../misc/npm-developers.html">npm-developers</a></h1> <p>Developer Guide</p>
<h2 id="description">DESCRIPTION</h2>
<p>So, you&#39;ve decided to use npm to develop (and maybe publish/deploy)
your project.</p>
<p>Fantastic!</p>
<p>There are a few things that you need to do above the simple steps
that your users will do to install your program.</p>
<h2 id="about-these-documents">About These Documents</h2>
<p>These are man pages.  If you install npm, you should be able to
then do <code>man npm-thing</code> to get the documentation on a particular
topic, or <code>npm help thing</code> to see the same information.</p>
<h2 id="what-is-a-package-">What is a <code>package</code></h2>
<p>A package is:</p>
<ul>
<li>a) a folder containing a program described by a package.json file</li>
<li>b) a gzipped tarball containing (a)</li>
<li>c) a url that resolves to (b)</li>
<li>d) a <code>&lt;name&gt;@&lt;version&gt;</code> that is published on the registry with (c)</li>
<li>e) a <code>&lt;name&gt;@&lt;tag&gt;</code> that points to (d)</li>
<li>f) a <code>&lt;name&gt;</code> that has a &quot;latest&quot; tag satisfying (e)</li>
<li>g) a <code>git</code> url that, when cloned, results in (a).</li>
</ul>
<p>Even if you never publish your package, you can still get a lot of
benefits of using npm if you just want to write a node program (a), and
perhaps if you also want to be able to easily install it elsewhere
after packing it up into a tarball (b).</p>
<p>Git urls can be of the form:</p>
<pre><code>git://github.com/user/project.git#commit-ish
git+ssh://user@hostname:project.git#commit-ish
git+http://user@hostname/project/blah.git#commit-ish
git+https://user@hostname/project/blah.git#commit-ish
</code></pre><p>The <code>commit-ish</code> can be any tag, sha, or branch which can be supplied as
an argument to <code>git checkout</code>.  The default is <code>master</code>.</p>
<h2 id="the-package-json-file">The package.json File</h2>
<p>You need to have a <code>package.json</code> file in the root of your project to do
much of anything with npm.  That is basically the whole interface.</p>
<p>See <code><a href="../files/package.json.html">package.json(5)</a></code> for details about what goes in that file.  At the very
least, you need:</p>
<ul>
<li><p>name:
This should be a string that identifies your project.  Please do not
use the name to specify that it runs on node, or is in JavaScript.
You can use the &quot;engines&quot; field to explicitly state the versions of
node (or whatever else) that your program requires, and it&#39;s pretty
well assumed that it&#39;s javascript.</p>
<p>It does not necessarily need to match your github repository name.</p>
<p>So, <code>node-foo</code> and <code>bar-js</code> are bad names.  <code>foo</code> or <code>bar</code> are better.</p>
</li>
<li><p>version:
A semver-compatible version.</p>
</li>
<li><p>engines:
Specify the versions of node (or whatever else) that your program
runs on.  The node API changes a lot, and there may be bugs or new
functionality that you depend on.  Be explicit.</p>
</li>
<li><p>author:
Take some credit.</p>
</li>
<li><p>scripts:
If you have a special compilation or installation script, then you
should put it in the <code>scripts</code> hash.  You should definitely have at
least a basic smoke-test command as the &quot;scripts.test&quot; field.
See <a href="../misc/npm-scripts.html">npm-scripts(7)</a>.</p>
</li>
<li><p>main:
If you have a single module that serves as the entry point to your
program (like what the &quot;foo&quot; package gives you at require(&quot;foo&quot;)),
then you need to specify that in the &quot;main&quot; field.</p>
</li>
<li><p>directories:
This is a hash of folders.  The best ones to include are &quot;lib&quot; and
&quot;doc&quot;, but if you specify a folder full of man pages in &quot;man&quot;, then
they&#39;ll get installed just like these ones.</p>
</li>
</ul>
<p>You can use <code>npm init</code> in the root of your package in order to get you
started with a pretty basic package.json file.  See <code><a href="../cli/npm-init.html">npm-init(1)</a></code> for
more info.</p>
<h2 id="keeping-files-out-of-your-package">Keeping files <em>out</em> of your package</h2>
<p>Use a <code>.npmignore</code> file to keep stuff out of your package.  If there&#39;s
no <code>.npmignore</code> file, but there <em>is</em> a <code>.gitignore</code> file, then npm will
ignore the stuff matched by the <code>.gitignore</code> file.  If you <em>want</em> to
include something that is excluded by your <code>.gitignore</code> file, you can
create an empty <code>.npmignore</code> file to override it.</p>
<p>By default, the following paths and files are ignored, so there&#39;s no
need to add them to <code>.npmignore</code> explicitly:</p>
<ul>
<li><code>.*.swp</code></li>
<li><code>._*</code></li>
<li><code>.DS_Store</code></li>
<li><code>.git</code></li>
<li><code>.hg</code></li>
<li><code>.lock-wscript</code></li>
<li><code>.svn</code></li>
<li><code>.wafpickle-*</code></li>
<li><code>CVS</code></li>
<li><code>npm-debug.log</code></li>
</ul>
<p>Additionally, everything in <code>node_modules</code> is ignored, except for
bundled dependencies. npm automatically handles this for you, so don&#39;t
bother adding <code>node_modules</code> to <code>.npmignore</code>.</p>
<p>The following paths and files are never ignored, so adding them to
<code>.npmignore</code> is pointless:</p>
<ul>
<li><code>package.json</code></li>
<li><code><a href="../../doc/README.html">README</a>.*</code></li>
</ul>
<h2 id="link-packages">Link Packages</h2>
<p><code>npm link</code> is designed to install a development package and see the
changes in real time without having to keep re-installing it.  (You do
need to either re-link or <code>npm rebuild -g</code> to update compiled packages,
of course.)</p>
<p>More info at <code><a href="../cli/npm-link.html">npm-link(1)</a></code>.</p>
<h2 id="before-publishing-make-sure-your-package-installs-and-works">Before Publishing: Make Sure Your Package Installs and Works</h2>
<p><strong>This is important.</strong></p>
<p>If you can not install it locally, you&#39;ll have
problems trying to publish it.  Or, worse yet, you&#39;ll be able to
publish it, but you&#39;ll be publishing a broken or pointless package.
So don&#39;t do that.</p>
<p>In the root of your package, do this:</p>
<pre><code>npm install . -g
</code></pre><p>That&#39;ll show you that it&#39;s working.  If you&#39;d rather just create a symlink
package that points to your working directory, then do this:</p>
<pre><code>npm link
</code></pre><p>Use <code>npm ls -g</code> to see if it&#39;s there.</p>
<p>To test a local install, go into some other folder, and then do:</p>
<pre><code>cd ../some-other-folder
npm install ../my-package
</code></pre><p>to install it locally into the node_modules folder in that other place.</p>
<p>Then go into the node-repl, and try using require(&quot;my-thing&quot;) to
bring in your module&#39;s main module.</p>
<h2 id="create-a-user-account">Create a User Account</h2>
<p>Create a user with the adduser command.  It works like this:</p>
<pre><code>npm adduser
</code></pre><p>and then follow the prompts.</p>
<p>This is documented better in <a href="../cli/npm-adduser.html">npm-adduser(1)</a>.</p>
<h2 id="publish-your-package">Publish your package</h2>
<p>This part&#39;s easy.  IN the root of your folder, do this:</p>
<pre><code>npm publish
</code></pre><p>You can give publish a url to a tarball, or a filename of a tarball,
or a path to a folder.</p>
<p>Note that pretty much <strong>everything in that folder will be exposed</strong>
by default.  So, if you have secret stuff in there, use a
<code>.npmignore</code> file to list out the globs to ignore, or publish
from a fresh checkout.</p>
<h2 id="brag-about-it">Brag about it</h2>
<p>Send emails, write blogs, blab in IRC.</p>
<p>Tell the world how easy it is to install your program!</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../misc/npm-faq.html">npm-faq(7)</a></li>
<li><a href="../cli/npm.html">npm(1)</a></li>
<li><a href="../cli/npm-init.html">npm-init(1)</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../misc/npm-scripts.html">npm-scripts(7)</a></li>
<li><a href="../cli/npm-publish.html">npm-publish(1)</a></li>
<li><a href="../cli/npm-adduser.html">npm-adduser(1)</a></li>
<li><a href="../misc/npm-registry.html">npm-registry(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-developers &mdash; npm@1.4.21</p>

