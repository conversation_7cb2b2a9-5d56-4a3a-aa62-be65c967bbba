<!doctype html>
<html>
  <title>npm-ls</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/api/npm-ls.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../api/npm-ls.html">npm-ls</a></h1> <p>List installed packages</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm.commands.ls(args, [silent,] callback)
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This command will print to stdout all the versions of packages that are
installed, as well as their dependencies, in a tree-structure. It will also
return that data using the callback.</p>
<p>This command does not take any arguments, but args must be defined.
Beyond that, if any arguments are passed in, npm will politely warn that it
does not take positional arguments, though you may set config flags
like with any other command, such as <code>global</code> to list global packages.</p>
<p>It will print out extraneous, missing, and invalid packages.</p>
<p>If the silent parameter is set to true, nothing will be output to the screen,
but the data will still be returned.</p>
<p>Callback is provided an error if one occurred, the full data about which
packages are installed and which dependencies they will receive, and a
&quot;lite&quot; data object which just shows which versions are installed where.
Note that the full data object is a circular structure, so care must be
taken if it is serialized to JSON.</p>
<h2 id="configuration">CONFIGURATION</h2>
<h3 id="long">long</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show extended information.</p>
<h3 id="parseable">parseable</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show parseable output instead of tree view.</p>
<h3 id="global">global</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>List packages in the global install prefix instead of in the current
project.</p>
<p>Note, if parseable is set or long isn&#39;t set, then duplicates will be trimmed.
This means that if a submodule a same dependency as a parent module, then the
dependency will only be output once.</p>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-ls &mdash; npm@1.4.21</p>

