<!doctype html>
<html>
  <title>npmrc</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/files/npmrc.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../files/npmrc.html">npmrc</a></h1> <p>The npm config files</p>
<h2 id="description">DESCRIPTION</h2>
<p>npm gets its config settings from the command line, environment
variables, and <code>npmrc</code> files.</p>
<p>The <code>npm config</code> command can be used to update and edit the contents
of the user and global npmrc files.</p>
<p>For a list of available configuration options, see <a href="../misc/npm-config.html">npm-config(7)</a>.</p>
<h2 id="files">FILES</h2>
<p>The four relevant files are:</p>
<ul>
<li>per-project config file (/path/to/my/project/.npmrc)</li>
<li>per-user config file (~/.npmrc)</li>
<li>global config file ($PREFIX/npmrc)</li>
<li>npm builtin config file (/path/to/npm/npmrc)</li>
</ul>
<p>All npm config files are an ini-formatted list of <code>key = value</code>
parameters.  Environment variables can be replaced using
<code>${VARIABLE_NAME}</code>. For example:</p>
<pre><code>prefix = ${HOME}/.npm-packages
</code></pre><p>Each of these files is loaded, and config options are resolved in
priority order.  For example, a setting in the userconfig file would
override the setting in the globalconfig file.</p>
<h3 id="per-project-config-file">Per-project config file</h3>
<p>When working locally in a project, a <code>.npmrc</code> file in the root of the
project (ie, a sibling of <code>node_modules</code> and <code>package.json</code>) will set
config values specific to this project.</p>
<p>Note that this only applies to the root of the project that you&#39;re
running npm in.  It has no effect when your module is published.  For
example, you can&#39;t publish a module that forces itself to install
globally, or in a different location.</p>
<h3 id="per-user-config-file">Per-user config file</h3>
<p><code>$HOME/.npmrc</code> (or the <code>userconfig</code> param, if set in the environment
or on the command line)</p>
<h3 id="global-config-file">Global config file</h3>
<p><code>$PREFIX/etc/npmrc</code> (or the <code>globalconfig</code> param, if set above):
This file is an ini-file formatted list of <code>key = value</code> parameters.
Environment variables can be replaced as above.</p>
<h3 id="built-in-config-file">Built-in config file</h3>
<p><code>path/to/npm/itself/npmrc</code></p>
<p>This is an unchangeable &quot;builtin&quot; configuration file that npm keeps
consistent across updates.  Set fields in here using the <code>./configure</code>
script that comes with npm.  This is primarily for distribution
maintainers to override default configs in a standard and consistent
manner.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../cli/npm.html">npm(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npmrc &mdash; npm@1.4.21</p>

