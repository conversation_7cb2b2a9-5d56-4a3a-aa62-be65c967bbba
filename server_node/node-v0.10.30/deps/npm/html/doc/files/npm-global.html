<!doctype html>
<html>
  <title>npm-folders</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/files/npm-folders.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../files/npm-folders.html">npm-folders</a></h1> <p>Folder Structures Used by npm</p>
<h2 id="description">DESCRIPTION</h2>
<p>npm puts various things on your computer.  That&#39;s its job.</p>
<p>This document will tell you what it puts where.</p>
<h3 id="tl-dr">tl;dr</h3>
<ul>
<li>Local install (default): puts stuff in <code>./node_modules</code> of the current
package root.</li>
<li>Global install (with <code>-g</code>): puts stuff in /usr/local or wherever node
is installed.</li>
<li>Install it <strong>locally</strong> if you&#39;re going to <code>require()</code> it.</li>
<li>Install it <strong>globally</strong> if you&#39;re going to run it on the command line.</li>
<li>If you need both, then install it in both places, or use <code>npm link</code>.</li>
</ul>
<h3 id="prefix-configuration">prefix Configuration</h3>
<p>The <code>prefix</code> config defaults to the location where node is installed.
On most systems, this is <code>/usr/local</code>, and most of the time is the same
as node&#39;s <code>process.installPrefix</code>.</p>
<p>On windows, this is the exact location of the node.exe binary.  On Unix
systems, it&#39;s one level up, since node is typically installed at
<code>{prefix}/bin/node</code> rather than <code>{prefix}/node.exe</code>.</p>
<p>When the <code>global</code> flag is set, npm installs things into this prefix.
When it is not set, it uses the root of the current package, or the
current working directory if not in a package already.</p>
<h3 id="node-modules">Node Modules</h3>
<p>Packages are dropped into the <code>node_modules</code> folder under the <code>prefix</code>.
When installing locally, this means that you can
<code>require(&quot;packagename&quot;)</code> to load its main module, or
<code>require(&quot;packagename/lib/path/to/sub/module&quot;)</code> to load other modules.</p>
<p>Global installs on Unix systems go to <code>{prefix}/lib/node_modules</code>.
Global installs on Windows go to <code>{prefix}/node_modules</code> (that is, no
<code>lib</code> folder.)</p>
<p>If you wish to <code>require()</code> a package, then install it locally.</p>
<h3 id="executables">Executables</h3>
<p>When in global mode, executables are linked into <code>{prefix}/bin</code> on Unix,
or directly into <code>{prefix}</code> on Windows.</p>
<p>When in local mode, executables are linked into
<code>./node_modules/.bin</code> so that they can be made available to scripts run
through npm.  (For example, so that a test runner will be in the path
when you run <code>npm test</code>.)</p>
<h3 id="man-pages">Man Pages</h3>
<p>When in global mode, man pages are linked into <code>{prefix}/share/man</code>.</p>
<p>When in local mode, man pages are not installed.</p>
<p>Man pages are not installed on Windows systems.</p>
<h3 id="cache">Cache</h3>
<p>See <code><a href="../cli/npm-cache.html">npm-cache(1)</a></code>.  Cache files are stored in <code>~/.npm</code> on Posix, or
<code>~/npm-cache</code> on Windows.</p>
<p>This is controlled by the <code>cache</code> configuration param.</p>
<h3 id="temp-files">Temp Files</h3>
<p>Temporary files are stored by default in the folder specified by the
<code>tmp</code> config, which defaults to the TMPDIR, TMP, or TEMP environment
variables, or <code>/tmp</code> on Unix and <code>c:\windows\temp</code> on Windows.</p>
<p>Temp files are given a unique folder under this root for each run of the
program, and are deleted upon successful exit.</p>
<h2 id="more-information">More Information</h2>
<p>When installing locally, npm first tries to find an appropriate
<code>prefix</code> folder.  This is so that <code>npm install foo@1.2.3</code> will install
to the sensible root of your package, even if you happen to have <code>cd</code>ed
into some other folder.</p>
<p>Starting at the $PWD, npm will walk up the folder tree checking for a
folder that contains either a <code>package.json</code> file, or a <code>node_modules</code>
folder.  If such a thing is found, then that is treated as the effective
&quot;current directory&quot; for the purpose of running npm commands.  (This
behavior is inspired by and similar to git&#39;s .git-folder seeking
logic when running git commands in a working dir.)</p>
<p>If no package root is found, then the current folder is used.</p>
<p>When you run <code>npm install foo@1.2.3</code>, then the package is loaded into
the cache, and then unpacked into <code>./node_modules/foo</code>.  Then, any of
foo&#39;s dependencies are similarly unpacked into
<code>./node_modules/foo/node_modules/...</code>.</p>
<p>Any bin files are symlinked to <code>./node_modules/.bin/</code>, so that they may
be found by npm scripts when necessary.</p>
<h3 id="global-installation">Global Installation</h3>
<p>If the <code>global</code> configuration is set to true, then npm will
install packages &quot;globally&quot;.</p>
<p>For global installation, packages are installed roughly the same way,
but using the folders described above.</p>
<h3 id="cycles-conflicts-and-folder-parsimony">Cycles, Conflicts, and Folder Parsimony</h3>
<p>Cycles are handled using the property of node&#39;s module system that it
walks up the directories looking for <code>node_modules</code> folders.  So, at every
stage, if a package is already installed in an ancestor <code>node_modules</code>
folder, then it is not installed at the current location.</p>
<p>Consider the case above, where <code>foo -&gt; bar -&gt; baz</code>.  Imagine if, in
addition to that, baz depended on bar, so you&#39;d have:
<code>foo -&gt; bar -&gt; baz -&gt; bar -&gt; baz ...</code>.  However, since the folder
structure is: <code>foo/node_modules/bar/node_modules/baz</code>, there&#39;s no need to
put another copy of bar into <code>.../baz/node_modules</code>, since when it calls
require(&quot;bar&quot;), it will get the copy that is installed in
<code>foo/node_modules/bar</code>.</p>
<p>This shortcut is only used if the exact same
version would be installed in multiple nested <code>node_modules</code> folders.  It
is still possible to have <code>a/node_modules/b/node_modules/a</code> if the two
&quot;a&quot; packages are different versions.  However, without repeating the
exact same package multiple times, an infinite regress will always be
prevented.</p>
<p>Another optimization can be made by installing dependencies at the
highest level possible, below the localized &quot;target&quot; folder.</p>
<h4 id="example">Example</h4>
<p>Consider this dependency graph:</p>
<pre><code>foo
+-- blerg@1.2.5
+-- bar@1.2.3
|   +-- blerg@1.x (latest=1.3.7)
|   +-- baz@2.x
|   |   `-- quux@3.x
|   |       `-- bar@1.2.3 (cycle)
|   `-- asdf@*
`-- baz@1.2.3
    `-- quux@3.x
        `-- bar
</code></pre><p>In this case, we might expect a folder structure like this:</p>
<pre><code>foo
+-- node_modules
    +-- blerg (1.2.5) &lt;---[A]
    +-- bar (1.2.3) &lt;---[B]
    |   `-- node_modules
    |       +-- baz (2.0.2) &lt;---[C]
    |       |   `-- node_modules
    |       |       `-- quux (3.2.0)
    |       `-- asdf (2.3.4)
    `-- baz (1.2.3) &lt;---[D]
        `-- node_modules
            `-- quux (3.2.0) &lt;---[E]
</code></pre><p>Since foo depends directly on <code>bar@1.2.3</code> and <code>baz@1.2.3</code>, those are
installed in foo&#39;s <code>node_modules</code> folder.</p>
<p>Even though the latest copy of blerg is 1.3.7, foo has a specific
dependency on version 1.2.5.  So, that gets installed at [A].  Since the
parent installation of blerg satisfies bar&#39;s dependency on <code>blerg@1.x</code>,
it does not install another copy under [B].</p>
<p>Bar [B] also has dependencies on baz and asdf, so those are installed in
bar&#39;s <code>node_modules</code> folder.  Because it depends on <code>baz@2.x</code>, it cannot
re-use the <code>baz@1.2.3</code> installed in the parent <code>node_modules</code> folder [D],
and must install its own copy [C].</p>
<p>Underneath bar, the <code>baz -&gt; quux -&gt; bar</code> dependency creates a cycle.
However, because bar is already in quux&#39;s ancestry [B], it does not
unpack another copy of bar into that folder.</p>
<p>Underneath <code>foo -&gt; baz</code> [D], quux&#39;s [E] folder tree is empty, because its
dependency on bar is satisfied by the parent folder copy installed at [B].</p>
<p>For a graphical breakdown of what is installed where, use <code>npm ls</code>.</p>
<h3 id="publishing">Publishing</h3>
<p>Upon publishing, npm will look in the <code>node_modules</code> folder.  If any of
the items there are not in the <code>bundledDependencies</code> array, then they will
not be included in the package tarball.</p>
<p>This allows a package maintainer to install all of their dependencies
(and dev dependencies) locally, but only re-publish those items that
cannot be found elsewhere.  See <code><a href="../files/package.json.html">package.json(5)</a></code> for more information.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../misc/npm-faq.html">npm-faq(7)</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../cli/npm-install.html">npm-install(1)</a></li>
<li><a href="../cli/npm-pack.html">npm-pack(1)</a></li>
<li><a href="../cli/npm-cache.html">npm-cache(1)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../cli/npm-publish.html">npm-publish(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-folders &mdash; npm@1.4.21</p>

