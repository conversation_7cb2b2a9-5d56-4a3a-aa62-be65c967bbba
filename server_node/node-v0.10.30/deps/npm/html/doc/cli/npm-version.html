<!doctype html>
<html>
  <title>npm-version</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-version.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-version.html">npm-version</a></h1> <p>Bump a package version</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm version [&lt;newversion&gt; | major | minor | patch]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Run this in a package directory to bump the version and write the new
data back to the package.json file.</p>
<p>The <code>newversion</code> argument should be a valid semver string, <em>or</em> a valid
second argument to semver.inc (one of &quot;patch&quot;, &quot;minor&quot;, or
&quot;major&quot;). In the second case, the existing version will be incremented
by 1 in the specified field.</p>
<p>If run in a git repo, it will also create a version commit and tag, and
fail if the repo is not clean.</p>
<p>If supplied with <code>--message</code> (shorthand: <code>-m</code>) config option, npm will
use it as a commit message when creating a version commit.  If the
<code>message</code> config contains <code>%s</code> then that will be replaced with the
resulting version number.  For example:</p>
<pre><code>npm version patch -m &quot;Upgrade to %s for reasons&quot;
</code></pre><p>If the <code>sign-git-tag</code> config is set, then the tag will be signed using
the <code>-s</code> flag to git.  Note that you must have a default GPG key set up
in your git config for this to work properly.  For example:</p>
<pre><code>$ npm config set sign-git-tag true
$ npm version patch

You need a passphrase to unlock the secret key for
user: &quot;isaacs (http://blog.izs.me/) &lt;<EMAIL>&gt;&quot;
2048-bit RSA key, ID 6C481CF6, created 2010-08-31

Enter passphrase:
</code></pre><h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-init.html">npm-init(1)</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../misc/semver.html">semver(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-version &mdash; npm@1.4.21</p>

