<!doctype html>
<html>
  <title>npm-explore</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-explore.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-explore.html">npm-explore</a></h1> <p>Browse an installed package</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm explore &lt;name&gt; [ -- &lt;cmd&gt;]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Spawn a subshell in the directory of the installed package specified.</p>
<p>If a command is specified, then it is run in the subshell, which then
immediately terminates.</p>
<p>This is particularly handy in the case of git submodules in the
<code>node_modules</code> folder:</p>
<pre><code>npm explore some-dependency -- git pull origin master
</code></pre><p>Note that the package is <em>not</em> automatically rebuilt afterwards, so be
sure to use <code>npm rebuild &lt;pkg&gt;</code> if you make any changes.</p>
<h2 id="configuration">CONFIGURATION</h2>
<h3 id="shell">shell</h3>
<ul>
<li>Default: SHELL environment variable, or &quot;bash&quot; on Posix, or &quot;cmd&quot; on
Windows</li>
<li>Type: path</li>
</ul>
<p>The shell to run for the <code>npm explore</code> command.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-submodule.html">npm-submodule(1)</a></li>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm-edit.html">npm-edit(1)</a></li>
<li><a href="../cli/npm-rebuild.html">npm-rebuild(1)</a></li>
<li><a href="../cli/npm-build.html">npm-build(1)</a></li>
<li><a href="../cli/npm-install.html">npm-install(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-explore &mdash; npm@1.4.21</p>

