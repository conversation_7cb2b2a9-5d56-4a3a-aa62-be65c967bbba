<!doctype html>
<html>
  <title>npm</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm.html">npm</a></h1> <p>node package manager</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm &lt;command&gt; [args]
</code></pre><h2 id="version">VERSION</h2>
<p>1.4.21</p>
<h2 id="description">DESCRIPTION</h2>
<p>npm is the package manager for the Node JavaScript platform.  It puts
modules in place so that node can find them, and manages dependency
conflicts intelligently.</p>
<p>It is extremely configurable to support a wide variety of use cases.
Most commonly, it is used to publish, discover, install, and develop node
programs.</p>
<p>Run <code>npm help</code> to get a list of available commands.</p>
<h2 id="introduction">INTRODUCTION</h2>
<p>You probably got npm because you want to install stuff.</p>
<p>Use <code>npm install blerg</code> to install the latest version of &quot;blerg&quot;.  Check out
<code><a href="../cli/npm-install.html">npm-install(1)</a></code> for more info.  It can do a lot of stuff.</p>
<p>Use the <code>npm search</code> command to show everything that&#39;s available.
Use <code>npm ls</code> to show everything you&#39;ve installed.</p>
<h2 id="dependencies">DEPENDENCIES</h2>
<p>If a package references to another package with a git URL, npm depends
on a preinstalled git.</p>
<p>If one of the packages npm tries to install is a native node module and
requires compiling of C++ Code, npm will use
<a href="https://github.com/TooTallNate/node-gyp">node-gyp</a> for that task.
For a Unix system, <a href="https://github.com/TooTallNate/node-gyp">node-gyp</a>
needs Python, make and a buildchain like GCC. On Windows,
Python and Microsoft Visual Studio C++ is needed. Python 3 is
not supported by <a href="https://github.com/TooTallNate/node-gyp">node-gyp</a>.
For more information visit
<a href="https://github.com/TooTallNate/node-gyp">the node-gyp repository</a> and
the <a href="https://github.com/TooTallNate/node-gyp/wiki">node-gyp Wiki</a>.</p>
<h2 id="directories">DIRECTORIES</h2>
<p>See <code><a href="../files/npm-folders.html">npm-folders(5)</a></code> to learn about where npm puts stuff.</p>
<p>In particular, npm has two modes of operation:</p>
<ul>
<li>global mode:<br>npm installs packages into the install prefix at
<code>prefix/lib/node_modules</code> and bins are installed in <code>prefix/bin</code>.</li>
<li>local mode:<br>npm installs packages into the current project directory, which
defaults to the current working directory.  Packages are installed to
<code>./node_modules</code>, and bins are installed to <code>./node_modules/.bin</code>.</li>
</ul>
<p>Local mode is the default.  Use <code>--global</code> or <code>-g</code> on any command to
operate in global mode instead.</p>
<h2 id="developer-usage">DEVELOPER USAGE</h2>
<p>If you&#39;re using npm to develop and publish your code, check out the
following help topics:</p>
<ul>
<li>json:
Make a package.json file.  See <code><a href="../files/package.json.html">package.json(5)</a></code>.</li>
<li>link:
For linking your current working code into Node&#39;s path, so that you
don&#39;t have to reinstall every time you make a change.  Use
<code>npm link</code> to do this.</li>
<li>install:
It&#39;s a good idea to install things if you don&#39;t need the symbolic link.
Especially, installing other peoples code from the registry is done via
<code>npm install</code></li>
<li>adduser:
Create an account or log in.  Credentials are stored in the
user config file.</li>
<li>publish:
Use the <code>npm publish</code> command to upload your code to the registry.</li>
</ul>
<h2 id="configuration">CONFIGURATION</h2>
<p>npm is extremely configurable.  It reads its configuration options from
5 places.</p>
<ul>
<li>Command line switches:<br>Set a config with <code>--key val</code>.  All keys take a value, even if they
are booleans (the config parser doesn&#39;t know what the options are at
the time of parsing.)  If no value is provided, then the option is set
to boolean <code>true</code>.</li>
<li>Environment Variables:<br>Set any config by prefixing the name in an environment variable with
<code>npm_config_</code>.  For example, <code>export npm_config_key=val</code>.</li>
<li>User Configs:<br>The file at $HOME/.npmrc is an ini-formatted list of configs.  If
present, it is parsed.  If the <code>userconfig</code> option is set in the cli
or env, then that will be used instead.</li>
<li>Global Configs:<br>The file found at ../etc/npmrc (from the node executable, by default
this resolves to /usr/local/etc/npmrc) will be parsed if it is found.
If the <code>globalconfig</code> option is set in the cli, env, or user config,
then that file is parsed instead.</li>
<li>Defaults:<br>npm&#39;s default configuration options are defined in
lib/utils/config-defs.js.  These must not be changed.</li>
</ul>
<p>See <code><a href="../misc/npm-config.html">npm-config(7)</a></code> for much much more information.</p>
<h2 id="contributions">CONTRIBUTIONS</h2>
<p>Patches welcome!</p>
<ul>
<li>code:
Read through <code><a href="../misc/npm-coding-style.html">npm-coding-style(7)</a></code> if you plan to submit code.
You don&#39;t have to agree with it, but you do have to follow it.</li>
<li>docs:
If you find an error in the documentation, edit the appropriate markdown
file in the &quot;doc&quot; folder.  (Don&#39;t worry about generating the man page.)</li>
</ul>
<p>Contributors are listed in npm&#39;s <code>package.json</code> file.  You can view them
easily by doing <code>npm view npm contributors</code>.</p>
<p>If you would like to contribute, but don&#39;t know what to work on, check
the issues list or ask on the mailing list.</p>
<ul>
<li><a href="http://github.com/npm/npm/issues">http://github.com/npm/npm/issues</a></li>
<li><a href="&#109;&#x61;&#105;&#108;&#x74;&#111;&#58;&#x6e;&#x70;&#x6d;&#x2d;&#64;&#x67;&#x6f;&#x6f;&#103;&#108;&#x65;&#103;&#x72;&#x6f;&#117;&#x70;&#115;&#46;&#x63;&#x6f;&#109;">&#x6e;&#x70;&#x6d;&#x2d;&#64;&#x67;&#x6f;&#x6f;&#103;&#108;&#x65;&#103;&#x72;&#x6f;&#117;&#x70;&#115;&#46;&#x63;&#x6f;&#109;</a></li>
</ul>
<h2 id="bugs">BUGS</h2>
<p>When you find issues, please report them:</p>
<ul>
<li>web:
<a href="http://github.com/npm/npm/issues">http://github.com/npm/npm/issues</a></li>
<li>email:
<a href="&#109;&#x61;&#x69;&#108;&#x74;&#111;&#58;&#x6e;&#112;&#x6d;&#45;&#64;&#x67;&#111;&#111;&#x67;&#x6c;&#101;&#x67;&#114;&#x6f;&#x75;&#112;&#x73;&#46;&#99;&#111;&#109;">&#x6e;&#112;&#x6d;&#45;&#64;&#x67;&#111;&#111;&#x67;&#x6c;&#101;&#x67;&#114;&#x6f;&#x75;&#112;&#x73;&#46;&#99;&#111;&#109;</a></li>
</ul>
<p>Be sure to include <em>all</em> of the output from the npm command that didn&#39;t work
as expected.  The <code>npm-debug.log</code> file is also helpful to provide.</p>
<p>You can also look for isaacs in #node.js on irc://irc.freenode.net.  He
will no doubt tell you to put the output in a gist or email.</p>
<h2 id="author">AUTHOR</h2>
<p><a href="http://blog.izs.me/">Isaac Z. Schlueter</a> ::
<a href="https://github.com/isaacs/">isaacs</a> ::
<a href="http://twitter.com/izs">@izs</a> ::
<a href="&#109;&#97;&#105;&#x6c;&#x74;&#x6f;&#x3a;&#105;&#64;&#105;&#x7a;&#115;&#x2e;&#109;&#x65;">&#105;&#64;&#105;&#x7a;&#115;&#x2e;&#109;&#x65;</a></p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-help.html">npm-help(1)</a></li>
<li><a href="../misc/npm-faq.html">npm-faq(7)</a></li>
<li><a href="../../doc/README.html">README</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../cli/npm-install.html">npm-install(1)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../misc/npm-index.html">npm-index(7)</a></li>
<li><a href="../api/npm.html">npm(3)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm &mdash; npm@1.4.21</p>

