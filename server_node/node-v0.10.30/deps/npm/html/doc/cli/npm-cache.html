<!doctype html>
<html>
  <title>npm-cache</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-cache.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-cache.html">npm-cache</a></h1> <p>Manipulates packages cache</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm cache add &lt;tarball file&gt;
npm cache add &lt;folder&gt;
npm cache add &lt;tarball url&gt;
npm cache add &lt;name&gt;@&lt;version&gt;

npm cache ls [&lt;path&gt;]

npm cache clean [&lt;path&gt;]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Used to add, list, or clear the npm cache folder.</p>
<ul>
<li><p>add:
Add the specified package to the local cache.  This command is primarily
intended to be used internally by npm, but it can provide a way to
add data to the local installation cache explicitly.</p>
</li>
<li><p>ls:
Show the data in the cache.  Argument is a path to show in the cache
folder.  Works a bit like the <code>find</code> program, but limited by the
<code>depth</code> config.</p>
</li>
<li><p>clean:
Delete data out of the cache folder.  If an argument is provided, then
it specifies a subpath to delete.  If no argument is provided, then
the entire cache is cleared.</p>
</li>
</ul>
<h2 id="details">DETAILS</h2>
<p>npm stores cache data in the directory specified in <code>npm config get cache</code>.
For each package that is added to the cache, three pieces of information are
stored in <code>{cache}/{name}/{version}</code>:</p>
<ul>
<li>.../package/package.json:
The package.json file, as npm sees it.</li>
<li>.../package.tgz:
The tarball for that version.</li>
</ul>
<p>Additionally, whenever a registry request is made, a <code>.cache.json</code> file
is placed at the corresponding URI, to store the ETag and the requested
data.  This is stored in <code>{cache}/{hostname}/{path}/.cache.json</code>.</p>
<p>Commands that make non-essential registry requests (such as <code>search</code> and
<code>view</code>, or the completion scripts) generally specify a minimum timeout.
If the <code>.cache.json</code> file is younger than the specified timeout, then
they do not make an HTTP request to the registry.</p>
<h2 id="configuration">CONFIGURATION</h2>
<h3 id="cache">cache</h3>
<p>Default: <code>~/.npm</code> on Posix, or <code>%AppData%/npm-cache</code> on Windows.</p>
<p>The root cache folder.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../cli/npm-install.html">npm-install(1)</a></li>
<li><a href="../cli/npm-publish.html">npm-publish(1)</a></li>
<li><a href="../cli/npm-pack.html">npm-pack(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-cache &mdash; npm@1.4.21</p>

