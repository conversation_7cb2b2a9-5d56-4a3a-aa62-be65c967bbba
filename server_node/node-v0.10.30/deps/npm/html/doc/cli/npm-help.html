<!doctype html>
<html>
  <title>npm-help</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-help.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-help.html">npm-help</a></h1> <p>Get help on npm</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm help &lt;topic&gt;
npm help some search terms
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>If supplied a topic, then show the appropriate documentation page.</p>
<p>If the topic does not exist, or if multiple terms are provided, then run
the <code>help-search</code> command to find a match.  Note that, if <code>help-search</code>
finds a single subject, then it will run <code>help</code> on that topic, so unique
matches are equivalent to specifying a topic name.</p>
<h2 id="configuration">CONFIGURATION</h2>
<h3 id="viewer">viewer</h3>
<ul>
<li>Default: &quot;man&quot; on Posix, &quot;browser&quot; on Windows</li>
<li>Type: path</li>
</ul>
<p>The program to use to view help content.</p>
<p>Set to <code>&quot;browser&quot;</code> to view html help content in the default web browser.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm.html">npm(1)</a></li>
<li><a href="../../doc/README.html">README</a></li>
<li><a href="../misc/npm-faq.html">npm-faq(7)</a></li>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../cli/npm-help-search.html">npm-help-search(1)</a></li>
<li><a href="../misc/npm-index.html">npm-index(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-help &mdash; npm@1.4.21</p>

