<!doctype html>
<html>
  <title>npm-uninstall</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-uninstall.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-rm.html">npm-rm</a></h1> <p>Remove a package</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm uninstall &lt;name&gt; [--save|--save-dev|--save-optional]
npm rm (with any of the previous argument usage)
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This uninstalls a package, completely removing everything npm installed
on its behalf.</p>
<p>Example:</p>
<pre><code>npm uninstall sax
</code></pre><p>In global mode (ie, with <code>-g</code> or <code>--global</code> appended to the command),
it uninstalls the current package context as a global package.</p>
<p><code>npm uninstall</code> takes 3 exclusive, optional flags which save or update
the package version in your main package.json:</p>
<ul>
<li><p><code>--save</code>: Package will be removed from your <code>dependencies</code>.</p>
</li>
<li><p><code>--save-dev</code>: Package will be removed from your <code>devDependencies</code>.</p>
</li>
<li><p><code>--save-optional</code>: Package will be removed from your <code>optionalDependencies</code>.</p>
</li>
</ul>
<p>Examples:</p>
<pre><code>npm uninstall sax --save
npm uninstall node-tap --save-dev
npm uninstall dtrace-provider --save-optional
</code></pre><h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-prune.html">npm-prune(1)</a></li>
<li><a href="../cli/npm-install.html">npm-install(1)</a></li>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-uninstall &mdash; npm@1.4.21</p>

