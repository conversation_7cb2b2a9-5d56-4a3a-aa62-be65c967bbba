<!doctype html>
<html>
  <title>npm-view</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-view.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-view.html">npm-view</a></h1> <p>View registry info</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm view &lt;name&gt;[@&lt;version&gt;] [&lt;field&gt;[.&lt;subfield&gt;]...]
npm v &lt;name&gt;[@&lt;version&gt;] [&lt;field&gt;[.&lt;subfield&gt;]...]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This command shows data about a package and prints it to the stream
referenced by the <code>outfd</code> config, which defaults to stdout.</p>
<p>To show the package registry entry for the <code>connect</code> package, you can do
this:</p>
<pre><code>npm view connect
</code></pre><p>The default version is &quot;latest&quot; if unspecified.</p>
<p>Field names can be specified after the package descriptor.
For example, to show the dependencies of the <code>ronn</code> package at version
0.3.5, you could do the following:</p>
<pre><code>npm view ronn@0.3.5 dependencies
</code></pre><p>You can view child field by separating them with a period.
To view the git repository URL for the latest version of npm, you could
do this:</p>
<pre><code>npm view npm repository.url
</code></pre><p>This makes it easy to view information about a dependency with a bit of
shell scripting.  For example, to view all the data about the version of
opts that ronn depends on, you can do this:</p>
<pre><code>npm view opts@$(npm view ronn dependencies.opts)
</code></pre><p>For fields that are arrays, requesting a non-numeric field will return
all of the values from the objects in the list.  For example, to get all
the contributor names for the &quot;express&quot; project, you can do this:</p>
<pre><code>npm view express contributors.email
</code></pre><p>You may also use numeric indices in square braces to specifically select
an item in an array field.  To just get the email address of the first
contributor in the list, you can do this:</p>
<pre><code>npm view express contributors[0].email
</code></pre><p>Multiple fields may be specified, and will be printed one after another.
For exampls, to get all the contributor names and email addresses, you
can do this:</p>
<pre><code>npm view express contributors.name contributors.email
</code></pre><p>&quot;Person&quot; fields are shown as a string if they would be shown as an
object.  So, for example, this will show the list of npm contributors in
the shortened string format.  (See <code><a href="../files/package.json.html">package.json(5)</a></code> for more on this.)</p>
<pre><code>npm view npm contributors
</code></pre><p>If a version range is provided, then data will be printed for every
matching version of the package.  This will show which version of jsdom
was required by each matching version of yui3:</p>
<pre><code>npm view yui3@&#39;&gt;0.5.4&#39; dependencies.jsdom
</code></pre><h2 id="output">OUTPUT</h2>
<p>If only a single string field for a single version is output, then it
will not be colorized or quoted, so as to enable piping the output to
another command. If the field is an object, it will be output as a JavaScript object literal.</p>
<p>If the --json flag is given, the outputted fields will be JSON.</p>
<p>If the version range matches multiple versions, than each printed value
will be prefixed with the version it applies to.</p>
<p>If multiple fields are requested, than each of them are prefixed with
the field name.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-search.html">npm-search(1)</a></li>
<li><a href="../misc/npm-registry.html">npm-registry(7)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../cli/npm-docs.html">npm-docs(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-view &mdash; npm@1.4.21</p>

