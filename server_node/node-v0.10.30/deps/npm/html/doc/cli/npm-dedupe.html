<!doctype html>
<html>
  <title>npm-dedupe</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-dedupe.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-dedupe.html">npm-dedupe</a></h1> <p>Reduce duplication</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm dedupe [package names...]
npm ddp [package names...]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Searches the local package tree and attempts to simplify the overall
structure by moving dependencies further up the tree, where they can
be more effectively shared by multiple dependent packages.</p>
<p>For example, consider this dependency graph:</p>
<pre><code>a
+-- b &lt;-- depends on c@1.0.x
|   `-- c@1.0.3
`-- d &lt;-- depends on c@~1.0.9
    `-- c@1.0.10
</code></pre><p>In this case, <code><a href="../cli/npm-dedupe.html">npm-dedupe(1)</a></code> will transform the tree to:</p>
<pre><code>a
+-- b
+-- d
`-- c@1.0.10
</code></pre><p>Because of the hierarchical nature of node&#39;s module lookup, b and d
will both get their dependency met by the single c package at the root
level of the tree.</p>
<p>If a suitable version exists at the target location in the tree
already, then it will be left untouched, but the other duplicates will
be deleted.</p>
<p>If no suitable version can be found, then a warning is printed, and
nothing is done.</p>
<p>If any arguments are supplied, then they are filters, and only the
named packages will be touched.</p>
<p>Note that this operation transforms the dependency tree, and may
result in packages getting updated versions, perhaps from the npm
registry.</p>
<p>This feature is experimental, and may change in future versions.</p>
<p>The <code>--tag</code> argument will apply to all of the affected dependencies. If a
tag with the given name exists, the tagged version is preferred over newer
versions.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-ls.html">npm-ls(1)</a></li>
<li><a href="../cli/npm-update.html">npm-update(1)</a></li>
<li><a href="../cli/npm-install.html">npm-install(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-dedupe &mdash; npm@1.4.21</p>

