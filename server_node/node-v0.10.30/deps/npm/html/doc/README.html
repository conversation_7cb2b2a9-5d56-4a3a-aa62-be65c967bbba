<!doctype html>
<html>
  <title>README</title>
  <meta http-equiv="content-type" value="text/html;utf-8">
  <link rel="stylesheet" type="text/css" href="../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/README.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="cli/npm.html">npm</a></h1> <p>node package manager</p>
<p><a href="https://travis-ci.org/npm/npm"><img src="https://img.shields.io/travis/npm/npm/master.svg" alt="Build Status"></a></p>
<h2 id="synopsis">SYNOPSIS</h2>
<p>This is just enough info to get you up and running.</p>
<p>Much more info available via <code>npm help</code> once it&#39;s installed.</p>
<h2 id="important">IMPORTANT</h2>
<p><strong>You need node v0.8 or higher to run this program.</strong></p>
<p>To install an old <strong>and unsupported</strong> version of npm that works on node 0.3
and prior, clone the git repo and dig through the old tags and branches.</p>
<h2 id="super-easy-install">Super Easy Install</h2>
<p>npm comes with node now.</p>
<h3 id="windows-computers">Windows Computers</h3>
<p>Get the MSI.  npm is in it.</p>
<h3 id="apple-macintosh-computers">Apple Macintosh Computers</h3>
<p>Get the pkg.  npm is in it.</p>
<h3 id="other-sorts-of-unices">Other Sorts of Unices</h3>
<p>Run <code>make install</code>.  npm will be installed with node.</p>
<p>If you want a more fancy pants install (a different version, customized
paths, etc.) then read on.</p>
<h2 id="fancy-install-unix-">Fancy Install (Unix)</h2>
<p>There&#39;s a pretty robust install script at
<a href="https://www.npmjs.org/install.sh">https://www.npmjs.org/install.sh</a>.  You can download that and run it.</p>
<p>Here&#39;s an example using curl:</p>
<pre><code>curl -L https://npmjs.org/install.sh | sh
</code></pre><h3 id="slightly-fancier">Slightly Fancier</h3>
<p>You can set any npm configuration params with that script:</p>
<pre><code>npm_config_prefix=/some/path sh install.sh
</code></pre><p>Or, you can run it in uber-debuggery mode:</p>
<pre><code>npm_debug=1 sh install.sh
</code></pre><h3 id="even-fancier">Even Fancier</h3>
<p>Get the code with git.  Use <code>make</code> to build the docs and do other stuff.
If you plan on hacking on npm, <code>make link</code> is your friend.</p>
<p>If you&#39;ve got the npm source code, you can also semi-permanently set
arbitrary config keys using the <code>./configure --key=val ...</code>, and then
run npm commands by doing <code>node cli.js &lt;cmd&gt; &lt;args&gt;</code>.  (This is helpful
for testing, or running stuff without actually installing npm itself.)</p>
<h2 id="fancy-windows-install">Fancy Windows Install</h2>
<p>You can download a zip file from <a href="https://npmjs.org/dist/">https://npmjs.org/dist/</a>, and unpack it
in the same folder where node.exe lives.</p>
<p>If that&#39;s not fancy enough for you, then you can fetch the code with
git, and mess with it directly.</p>
<h2 id="installing-on-cygwin">Installing on Cygwin</h2>
<p>No.</p>
<h2 id="permissions-when-using-npm-to-install-other-stuff">Permissions when Using npm to Install Other Stuff</h2>
<p><strong>tl;dr</strong></p>
<ul>
<li>Use <code>sudo</code> for greater safety.  Or don&#39;t, if you prefer not to.</li>
<li>npm will downgrade permissions if it&#39;s root before running any build
scripts that package authors specified.</li>
</ul>
<h3 id="more-details-">More details...</h3>
<p>As of version 0.3, it is recommended to run npm as root.
This allows npm to change the user identifier to the <code>nobody</code> user prior
to running any package build or test commands.</p>
<p>If you are not the root user, or if you are on a platform that does not
support uid switching, then npm will not attempt to change the userid.</p>
<p>If you would like to ensure that npm <strong>always</strong> runs scripts as the
&quot;nobody&quot; user, and have it fail if it cannot downgrade permissions, then
set the following configuration param:</p>
<pre><code>npm config set unsafe-perm false
</code></pre><p>This will prevent running in unsafe mode, even as non-root users.</p>
<h2 id="uninstalling">Uninstalling</h2>
<p>So sad to see you go.</p>
<pre><code>sudo npm uninstall npm -g
</code></pre><p>Or, if that fails,</p>
<pre><code>sudo make uninstall
</code></pre><h2 id="more-severe-uninstalling">More Severe Uninstalling</h2>
<p>Usually, the above instructions are sufficient.  That will remove
npm, but leave behind anything you&#39;ve installed.</p>
<p>If you would like to remove all the packages that you have installed,
then you can use the <code>npm ls</code> command to find them, and then <code>npm rm</code> to
remove them.</p>
<p>To remove cruft left behind by npm 0.x, you can use the included
<code>clean-old.sh</code> script file.  You can run it conveniently like this:</p>
<pre><code>npm explore npm -g -- sh scripts/clean-old.sh
</code></pre><p>npm uses two configuration files, one for per-user configs, and another
for global (every-user) configs.  You can view them by doing:</p>
<pre><code>npm config get userconfig   # defaults to ~/.npmrc
npm config get globalconfig # defaults to /usr/local/etc/npmrc
</code></pre><p>Uninstalling npm does not remove configuration files by default.  You
must remove them yourself manually if you want them gone.  Note that
this means that future npm installs will not remember the settings that
you have chosen.</p>
<h2 id="using-npm-programmatically">Using npm Programmatically</h2>
<p>If you would like to use npm programmatically, you can do that.
It&#39;s not very well documented, but it <em>is</em> rather simple.</p>
<p>Most of the time, unless you actually want to do all the things that
npm does, you should try using one of npm&#39;s dependencies rather than
using npm itself, if possible.</p>
<p>Eventually, npm will be just a thin cli wrapper around the modules
that it depends on, but for now, there are some things that you must
use npm itself to do.</p>
<pre><code>var npm = require(&quot;npm&quot;)
npm.load(myConfigObject, function (er) {
  if (er) return handlError(er)
  npm.commands.install([&quot;some&quot;, &quot;args&quot;], function (er, data) {
    if (er) return commandFailed(er)
    // command succeeded, and data might have some info
  })
  npm.on(&quot;log&quot;, function (message) { .... })
})
</code></pre><p>The <code>load</code> function takes an object hash of the command-line configs.
The various <code>npm.commands.&lt;cmd&gt;</code> functions take an <strong>array</strong> of
positional argument <strong>strings</strong>.  The last argument to any
<code>npm.commands.&lt;cmd&gt;</code> function is a callback.  Some commands take other
optional arguments.  Read the source.</p>
<p>You cannot set configs individually for any single npm function at this
time.  Since <code>npm</code> is a singleton, any call to <code>npm.config.set</code> will
change the value for <em>all</em> npm commands in that process.</p>
<p>See <code>./bin/npm-cli.js</code> for an example of pulling config values off of the
command line arguments using nopt.  You may also want to check out <code>npm
help config</code> to learn about all the options you can set there.</p>
<h2 id="more-docs">More Docs</h2>
<p>Check out the <a href="https://www.npmjs.org/doc/">docs</a>,
especially the <a href="https://www.npmjs.org/doc/faq.html">faq</a>.</p>
<p>You can use the <code>npm help</code> command to read any of them.</p>
<p>If you&#39;re a developer, and you want to use npm to publish your program,
you should <a href="https://www.npmjs.org/doc/developers.html">read this</a></p>
<h2 id="legal-stuff">Legal Stuff</h2>
<p>&quot;npm&quot; and &quot;The npm Registry&quot; are owned by npm, Inc.
All rights reserved.  See the included LICENSE file for more details.</p>
<p>&quot;Node.js&quot; and &quot;node&quot; are trademarks owned by Joyent, Inc.</p>
<p>Modules published on the npm registry are not officially endorsed by
npm, Inc. or the Node.js project.</p>
<p>Data published to the npm registry is not part of npm itself, and is
the sole property of the publisher.  While every effort is made to
ensure accountability, there is absolutely no guarantee, warrantee, or
assertion expressed or implied as to the quality, fitness for a
specific purpose, or lack of malice in any given npm package.</p>
<p>If you have a complaint about a package in the public npm registry,
and cannot <a href="https://www.npmjs.org/doc/misc/npm-disputes.html">resolve it with the package
owner</a>, please email
<a href="&#x6d;&#x61;&#x69;&#x6c;&#116;&#x6f;&#58;&#115;&#117;&#x70;&#112;&#x6f;&#114;&#116;&#64;&#110;&#112;&#109;&#x6a;&#x73;&#46;&#x63;&#x6f;&#x6d;">&#115;&#117;&#x70;&#112;&#x6f;&#114;&#116;&#64;&#110;&#112;&#109;&#x6a;&#x73;&#46;&#x63;&#x6f;&#x6d;</a> and explain the situation.</p>
<p>Any data published to The npm Registry (including user account
information) may be removed or modified at the sole discretion of the
npm server administrators.</p>
<h3 id="in-plainer-english">In plainer english</h3>
<p>npm is the property of npm, Inc.</p>
<p>If you publish something, it&#39;s yours, and you are solely accountable
for it.</p>
<p>If other people publish something, it&#39;s theirs.</p>
<p>Users can publish Bad Stuff.  It will be removed promptly if reported.
But there is no vetting process for published modules, and you use
them at your own risk.  Please inspect the source.</p>
<p>If you publish Bad Stuff, we may delete it from the registry, or even
ban your account in extreme cases.  So don&#39;t do that.</p>
<h2 id="bugs">BUGS</h2>
<p>When you find issues, please report them:</p>
<ul>
<li>web:
<a href="https://github.com/npm/npm/issues">https://github.com/npm/npm/issues</a></li>
<li>email:
<a href="&#x6d;&#97;&#x69;&#108;&#116;&#x6f;&#x3a;&#110;&#x70;&#x6d;&#x2d;&#x40;&#x67;&#x6f;&#111;&#x67;&#x6c;&#x65;&#103;&#x72;&#111;&#x75;&#112;&#x73;&#46;&#99;&#111;&#x6d;">&#110;&#x70;&#x6d;&#x2d;&#x40;&#x67;&#x6f;&#111;&#x67;&#x6c;&#x65;&#103;&#x72;&#111;&#x75;&#112;&#x73;&#46;&#99;&#111;&#x6d;</a></li>
</ul>
<p>Be sure to include <em>all</em> of the output from the npm command that didn&#39;t work
as expected.  The <code>npm-debug.log</code> file is also helpful to provide.</p>
<p>You can also look for isaacs in #node.js on irc://irc.freenode.net.  He
will no doubt tell you to put the output in a gist or email.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="cli/npm.html">npm(1)</a></li>
<li><a href="misc/npm-faq.html">npm-faq(7)</a></li>
<li><a href="cli/npm-help.html">npm-help(1)</a></li>
<li><a href="misc/npm-index.html">npm-index(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer"><a href="../doc/README.html">README</a> &mdash; npm@1.4.21</p>

