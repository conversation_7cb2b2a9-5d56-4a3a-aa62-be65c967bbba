{"name": "sntp", "description": "SNTP Client", "version": "0.2.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://hueniverse.com"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/hueniverse/sntp"}, "main": "index", "keywords": ["sntp", "ntp", "time"], "engines": {"node": ">=0.8.0"}, "dependencies": {"hoek": "0.9.x"}, "devDependencies": {"lab": "0.1.x", "complexity-report": "0.x.x"}, "scripts": {"test": "make test-cov"}, "licenses": [{"type": "BSD", "url": "http://github.com/hueniverse/sntp/raw/master/LICENSE"}], "readme": "# sntp\n\nAn SNTP v4 client (RFC4330) for node. Simpy connects to the NTP or SNTP server requested and returns the server time\nalong with the roundtrip duration and clock offset. To adjust the local time to the NTP time, add the returned `t` offset\nto the local time.\n\n[![Build Status](https://secure.travis-ci.org/hueniverse/sntp.png)](http://travis-ci.org/hueniverse/sntp)\n\n# Usage\n\n```javascript\nvar Sntp = require('sntp');\n\n// All options are optional\n\nvar options = {\n    host: 'nist1-sj.ustiming.org',  // Defaults to pool.ntp.org\n    port: 123,                      // Defaults to 123 (NTP)\n    resolveReference: true,         // Default to false (not resolving)\n    timeout: 1000                   // Defaults to zero (no timeout)\n};\n\n// Request server time\n\nSntp.time(options, function (err, time) {\n\n    if (err) {\n        console.log('Failed: ' + err.message);\n        process.exit(1);\n    }\n\n    console.log('Local clock is off by: ' + time.t + ' milliseconds');\n    process.exit(0);\n});\n```\n\nIf an application needs to maintain continuous time synchronization, the module provides a stateful method for\nquerying the current offset only when the last one is too old (defaults to daily).\n\n```javascript\n// Request offset once\n\nSntp.offset(function (err, offset) {\n\n    console.log(offset);                    // New (served fresh)\n\n    // Request offset again\n\n    Sntp.offset(function (err, offset) {\n\n        console.log(offset);                // Identical (served from cache)\n    });\n});\n```\n\nTo set a background offset refresh, start the interval and use the provided now() method. If for any reason the\nclient fails to obtain an up-to-date offset, the current system clock is used.\n\n```javascript\nvar before = Sntp.now();                    // System time without offset\n\nSntp.start(function () {\n\n    var now = Sntp.now();                   // With offset\n    Sntp.stop();\n});\n```\n\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/hueniverse/sntp/issues"}, "homepage": "https://github.com/hueniverse/sntp", "_id": "sntp@0.2.4", "_from": "sntp@0.2.x"}