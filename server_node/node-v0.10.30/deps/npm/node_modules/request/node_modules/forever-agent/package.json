{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "forever-agent", "description": "HTTP Agent that keeps socket connections alive between keep-alive requests. Formerly part of mikeal/request, now a standalone module.", "version": "0.5.0", "repository": {"url": "https://github.com/mikeal/forever-agent"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "readme": "forever-agent\n=============\n\nHTTP Agent that keeps socket connections alive between keep-alive requests. Formerly part of mikeal/request, now a standalone module.\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/mikeal/forever-agent/issues"}, "homepage": "https://github.com/mikeal/forever-agent", "_id": "forever-agent@0.5.0", "_from": "forever-agent@~0.5.0"}