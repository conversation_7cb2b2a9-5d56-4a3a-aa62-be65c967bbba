{"metadata": {"ctf2json_version": "1.0", "created_at": 1316563573, "derived_from": "/lib/libc.so", "ctf_version": 2, "requested_types": ["psinfo_t"]}, "data": [{"name": "int", "integer": {"length": 4, "signed": true}}, {"name": "char", "integer": {"length": 1, "signed": true}}, {"name": "unsigned short", "integer": {"length": 2, "signed": false}}, {"name": "long", "integer": {"length": 4, "signed": true}}, {"name": "unsigned", "integer": {"length": 4, "signed": false}}, {"name": "size_t", "typedef": "unsigned"}, {"name": "unsigned long", "integer": {"length": 4, "signed": false}}, {"name": "time_t", "typedef": "long"}, {"name": "struct timespec", "struct": [{"name": "tv_sec", "type": "time_t"}, {"name": "tv_nsec", "type": "long"}]}, {"name": "zoneid_t", "typedef": "long"}, {"name": "taskid_t", "typedef": "long"}, {"name": "dev_t", "typedef": "unsigned long"}, {"name": "uid_t", "typedef": "unsigned"}, {"name": "gid_t", "typedef": "unsigned"}, {"name": "timestruc_t", "typedef": "struct timespec"}, {"name": "short", "integer": {"length": 2, "signed": true}}, {"name": "projid_t", "typedef": "long"}, {"name": "ushort_t", "typedef": "unsigned short"}, {"name": "poolid_t", "typedef": "long"}, {"name": "uintptr_t", "typedef": "unsigned"}, {"name": "id_t", "typedef": "long"}, {"name": "pid_t", "typedef": "long"}, {"name": "processorid_t", "typedef": "int"}, {"name": "psetid_t", "typedef": "int"}, {"name": "struct lwpsinfo", "struct": [{"name": "pr_flag", "type": "int"}, {"name": "pr_lwpid", "type": "id_t"}, {"name": "pr_addr", "type": "uintptr_t"}, {"name": "pr_wchan", "type": "uintptr_t"}, {"name": "pr_stype", "type": "char"}, {"name": "pr_state", "type": "char"}, {"name": "pr_sname", "type": "char"}, {"name": "pr_nice", "type": "char"}, {"name": "pr_syscall", "type": "short"}, {"name": "pr_oldpri", "type": "char"}, {"name": "pr_cpu", "type": "char"}, {"name": "pr_pri", "type": "int"}, {"name": "pr_pctcpu", "type": "ushort_t"}, {"name": "pr_pad", "type": "ushort_t"}, {"name": "pr_start", "type": "timestruc_t"}, {"name": "pr_time", "type": "timestruc_t"}, {"name": "pr_clname", "type": "char [8]"}, {"name": "pr_name", "type": "char [16]"}, {"name": "pr_onpro", "type": "processorid_t"}, {"name": "pr_bindpro", "type": "processorid_t"}, {"name": "pr_bindpset", "type": "psetid_t"}, {"name": "pr_lgrp", "type": "int"}, {"name": "pr_filler", "type": "int [4]"}]}, {"name": "lwpsinfo_t", "typedef": "struct lwpsinfo"}, {"name": "struct psinfo", "struct": [{"name": "pr_flag", "type": "int"}, {"name": "pr_nlwp", "type": "int"}, {"name": "pr_pid", "type": "pid_t"}, {"name": "pr_ppid", "type": "pid_t"}, {"name": "pr_pgid", "type": "pid_t"}, {"name": "pr_sid", "type": "pid_t"}, {"name": "pr_uid", "type": "uid_t"}, {"name": "pr_euid", "type": "uid_t"}, {"name": "pr_gid", "type": "gid_t"}, {"name": "pr_egid", "type": "gid_t"}, {"name": "pr_addr", "type": "uintptr_t"}, {"name": "pr_size", "type": "size_t"}, {"name": "pr_rssize", "type": "size_t"}, {"name": "pr_pad1", "type": "size_t"}, {"name": "pr_ttydev", "type": "dev_t"}, {"name": "pr_pctcpu", "type": "ushort_t"}, {"name": "pr_pctmem", "type": "ushort_t"}, {"name": "pr_start", "type": "timestruc_t"}, {"name": "pr_time", "type": "timestruc_t"}, {"name": "pr_ctime", "type": "timestruc_t"}, {"name": "pr_fname", "type": "char [16]"}, {"name": "pr_psargs", "type": "char [80]"}, {"name": "pr_wstat", "type": "int"}, {"name": "pr_argc", "type": "int"}, {"name": "pr_argv", "type": "uintptr_t"}, {"name": "pr_envp", "type": "uintptr_t"}, {"name": "pr_dmodel", "type": "char"}, {"name": "pr_pad2", "type": "char [3]"}, {"name": "pr_taskid", "type": "taskid_t"}, {"name": "pr_projid", "type": "projid_t"}, {"name": "pr_nzomb", "type": "int"}, {"name": "pr_poolid", "type": "poolid_t"}, {"name": "pr_zoneid", "type": "zoneid_t"}, {"name": "pr_contract", "type": "id_t"}, {"name": "pr_filler", "type": "int [1]"}, {"name": "pr_lwp", "type": "lwpsinfo_t"}]}, {"name": "psinfo_t", "typedef": "struct psinfo"}]}