{"author": {"name": "Joyent, Inc"}, "name": "http-signature", "description": "Reference implementation of Joyent's HTTP Signature Scheme", "version": "0.10.0", "repository": {"type": "git", "url": "git://github.com/joyent/node-http-signature.git"}, "engines": {"node": ">=0.8"}, "main": "lib/index.js", "scripts": {"test": "./node_modules/.bin/tap tst/*.js"}, "dependencies": {"assert-plus": "0.1.2", "asn1": "0.1.11", "ctype": "0.5.2"}, "devDependencies": {"node-uuid": "1.4.0", "tap": "0.4.2"}, "readme": "# node-http-signature\n\nnode-http-signature is a node.js library that has client and server components\nfor Joyent's [HTTP Signature Scheme](http_signing.md).\n\n## Usage\n\nNote the example below signs a request with the same key/cert used to start an\nHTTP server. This is almost certainly not what you actaully want, but is just\nused to illustrate the API calls; you will need to provide your own key\nmanagement in addition to this library.\n\n### Client\n\n    var fs = require('fs');\n    var https = require('https');\n    var httpSignature = require('http-signature');\n\n    var key = fs.readFileSync('./key.pem', 'ascii');\n\n    var options = {\n      host: 'localhost',\n      port: 8443,\n      path: '/',\n      method: 'GET',\n      headers: {}\n    };\n\n    // Adds a 'Date' header in, signs it, and adds the\n    // 'Authorization' header in.\n    var req = https.request(options, function(res) {\n      console.log(res.statusCode);\n    });\n\n\n    httpSignature.sign(req, {\n      key: key,\n      keyId: './cert.pem'\n    });\n\n    req.end();\n\n### Server\n\n    var fs = require('fs');\n    var https = require('https');\n    var httpSignature = require('http-signature');\n\n    var options = {\n      key: fs.readFileSync('./key.pem'),\n      cert: fs.readFileSync('./cert.pem')\n    };\n\n    https.createServer(options, function (req, res) {\n      var rc = 200;\n      var parsed = httpSignature.parseRequest(req);\n      var pub = fs.readFileSync(parsed.keyId, 'ascii');\n      if (!httpSignature.verifySignature(parsed, pub))\n        rc = 401;\n\n      res.writeHead(rc);\n      res.end();\n    }).listen(8443);\n\n## Installation\n\n    npm install http-signature\n\n## License\n\nMIT.\n\n## Bugs\n\nSee <https://github.com/joyent/node-http-signature/issues>.\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/joyent/node-http-signature/issues"}, "homepage": "https://github.com/joyent/node-http-signature", "_id": "http-signature@0.10.0", "_from": "http-signature@~0.10.0"}