{"name": "json-stringify-safe", "version": "5.0.0", "description": "Like JSON.stringify, but doesn't blow up on circular refs", "main": "stringify.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe"}, "keywords": ["json", "stringify", "circular", "safe"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "license": "BSD", "readmeFilename": "README.md", "readme": "# json-stringify-safe\n\nLike JSON.stringify, but doesn't throw on circular references.\n\n## Usage\n\nTakes the same arguments as `JSON.stringify`.\n\n```javascript\nvar stringify = require('json-stringify-safe');\nvar circularObj = {};\ncircularObj.circularRef = circularObj;\ncircularObj.list = [ circularObj, circularObj ];\nconsole.log(stringify(circularObj, null, 2));\n```\n\nOutput:\n\n```json\n{\n  \"circularRef\": \"[Circular]\",\n  \"list\": [\n    \"[Circular]\",\n    \"[Circular]\"\n  ]\n}\n```\n\n## Details\n\n```\nstringify(obj, serializer, indent, decycler)\n```\n\nThe first three arguments are the same as to JSON.stringify.  The last\nis an argument that's only used when the object has been seen already.\n\nThe default `decycler` function returns the string `'[Circular]'`.\nIf, for example, you pass in `function(k,v){}` (return nothing) then it\nwill prune cycles.  If you pass in `function(k,v){ return {foo: 'bar'}}`,\nthen cyclical objects will always be represented as `{\"foo\":\"bar\"}` in\nthe result.\n\n```\nstringify.getSerialize(serializer, decycler)\n```\n\nReturns a serializer that can be used elsewhere.  This is the actual\nfunction that's passed to JSON.stringify.\n", "bugs": {"url": "https://github.com/isaacs/json-stringify-safe/issues"}, "homepage": "https://github.com/isaacs/json-stringify-safe", "_id": "json-stringify-safe@5.0.0", "_from": "json-stringify-safe@~5.0.0"}