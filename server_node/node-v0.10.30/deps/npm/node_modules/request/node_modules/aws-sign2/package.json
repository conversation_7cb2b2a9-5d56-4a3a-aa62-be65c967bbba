{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "aws-sign2", "description": "AWS signing. Originally pulled from LearnBoost/knox, maintained as vendor in request, now a standalone module.", "version": "0.5.0", "repository": {"url": "https://github.com/mikeal/aws-sign"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "readme": "aws-sign\n========\n\nAWS signing. Originally pulled from LearnBoost/knox, maintained as vendor in request, now a standalone module.\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/mikeal/aws-sign/issues"}, "homepage": "https://github.com/mikeal/aws-sign", "_id": "aws-sign2@0.5.0", "_from": "aws-sign2@~0.5.0"}