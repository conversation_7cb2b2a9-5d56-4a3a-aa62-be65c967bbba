{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "minimatch", "description": "a glob matcher in javascript", "version": "0.3.0", "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "main": "minimatch.js", "scripts": {"test": "tap test/*.js"}, "engines": {"node": "*"}, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "devDependencies": {"tap": ""}, "license": {"type": "MIT", "url": "http://github.com/isaacs/minimatch/raw/master/LICENSE"}, "bugs": {"url": "https://github.com/isaacs/minimatch/issues"}, "homepage": "https://github.com/isaacs/minimatch", "_id": "minimatch@0.3.0", "_shasum": "275d8edaac4f1bb3326472089e7949c8394699dd", "_from": "minimatch@latest", "_npmVersion": "1.4.10", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "275d8edaac4f1bb3326472089e7949c8394699dd", "tarball": "http://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz"}