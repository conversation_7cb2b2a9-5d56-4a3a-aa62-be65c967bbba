{"name": "editor", "version": "0.1.0", "description": "launch $EDITOR in your program", "main": "index.js", "directories": {"example": "example", "test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.4.4"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/node-editor.git"}, "homepage": "https://github.com/substack/node-editor", "keywords": ["text", "edit", "shell"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "engine": {"node": ">=0.6"}, "bugs": {"url": "https://github.com/substack/node-editor/issues"}, "_id": "editor@0.1.0", "dist": {"shasum": "542f4662c6a8c88e862fc11945e204e51981b9a1", "tarball": "http://registry.npmjs.org/editor/-/editor-0.1.0.tgz"}, "_from": "editor@latest", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "_shasum": "542f4662c6a8c88e862fc11945e204e51981b9a1", "_resolved": "https://registry.npmjs.org/editor/-/editor-0.1.0.tgz"}