{"name": "opener", "description": "Opens stuff, like webpages and files and executables, cross-platform", "version": "1.3.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenicdenicola.com"}, "license": "WTFPL", "repository": {"type": "git", "url": "git://github.com/domenic/opener.git"}, "bugs": {"url": "http://github.com/domenic/opener/issues"}, "main": "opener.js", "bin": {"opener": "opener.js"}, "scripts": {"lint": "jshint opener.js"}, "devDependencies": {"jshint": ">= 0.9.0"}, "readme": "# It Opens Stuff\r\n\r\nThat is, in your desktop environment. This will make *actual windows pop up*, with stuff in them:\r\n\r\n```bash\r\nnpm install opener -g\r\n\r\nopener http://google.com\r\nopener ./my-file.txt\r\nopener firefox\r\nopener npm run lint\r\n```\r\n\r\nAlso if you want to use it programmatically you can do that too:\r\n\r\n```js\r\nvar opener = require(\"opener\");\r\n\r\nopener(\"http://google.com\");\r\nopener(\"./my-file.txt\");\r\nopener(\"firefox\");\r\nopener(\"npm run lint\");\r\n```\r\n\r\n## Use It for Good\r\n\r\nLike opening the user's browser with a test harness in your package's test script:\r\n\r\n```json\r\n{\r\n    \"scripts\": {\r\n        \"test\": \"opener ./test/runner.html\"\r\n    },\r\n    \"devDependencies\": {\r\n        \"opener\": \"*\"\r\n    }\r\n}\r\n```\r\n\r\n## Why\r\n\r\nBecause Windows has `start`, Macs have `open`, and *nix has `xdg-open`. At least\r\n[according to some guy on StackOverflow](http://stackoverflow.com/q/1480971/3191). And I like things that work on all\r\nthree. Like Node.js. And Opener.\r\n", "_id": "opener@1.3.0", "dist": {"shasum": "d72b4b2e61b0a4ca7822a7554070620002fb90d9"}, "_from": "opener@latest"}