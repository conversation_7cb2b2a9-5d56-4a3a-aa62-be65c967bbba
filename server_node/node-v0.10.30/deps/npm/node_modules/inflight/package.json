{"name": "inflight", "version": "1.0.1", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "dependencies": {"once": "^1.3.0"}, "devDependencies": {"tap": "^0.4.10"}, "scripts": {"test": "tap test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/inflight"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "_id": "inflight@1.0.1", "_shasum": "01f6911821535243c790ac0f998f54e9023ffb6f", "_from": "inflight@~1.0.1", "_npmVersion": "1.4.9", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "01f6911821535243c790ac0f998f54e9023ffb6f", "tarball": "http://registry.npmjs.org/inflight/-/inflight-1.0.1.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.1.tgz", "readme": "ERROR: No README data found!"}