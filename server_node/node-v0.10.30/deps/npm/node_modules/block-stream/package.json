{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "block-stream", "description": "a stream of blocks", "version": "0.0.7", "repository": {"type": "git", "url": "git://github.com/isaacs/block-stream.git"}, "engines": {"node": "0.4 || >=0.5.8"}, "main": "block-stream.js", "dependencies": {"inherits": "~2.0.0"}, "devDependencies": {"tap": "0.x"}, "scripts": {"test": "tap test/"}, "license": "BSD", "readme": "# block-stream\n\nA stream of blocks.\n\nWrite data into it, and it'll output data in buffer blocks the size you\nspecify, padding with zeroes if necessary.\n\n```javascript\nvar block = new BlockStream(512)\nfs.createReadStream(\"some-file\").pipe(block)\nblock.pipe(fs.createWriteStream(\"block-file\"))\n```\n\nWhen `.end()` or `.flush()` is called, it'll pad the block with zeroes.\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/block-stream/issues"}, "_id": "block-stream@0.0.7", "_from": "block-stream@latest"}