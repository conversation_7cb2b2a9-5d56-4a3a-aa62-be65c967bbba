How To Build and Install wcwidth.js
===================================

This package does not provide an automated way to build or install the library
except using [`npm`](http://npmjs.org/package/wcwidth.js) because wcwidth.js is
intended to runs on top of [`node.js`](http://nodejs.org) that is a javascript
interpreter. If you have `node.js` installed,

    npm install wcwidth.js

brings the latest version of `wcwidth.js` and installs it with its all
depending packages.
