{"name": "wcwidth.js", "version": "0.0.4", "description": "A JavaScript porting of C's wcwidth() and wcswidth()", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://code.woong.org/"}, "contributors": [], "homepage": "http://code.woong.org/wcwidth.js", "repository": {"type": "git", "url": "https://github.com/mycoboco/wcwidth.js.git"}, "bugs": {"url": "https://github.com/mycoboco/wcwidth.js/issues", "email": "<EMAIL>"}, "main": "wcwidth.js", "dependencies": {"underscore": ">= 1.3.0"}, "devDependencies": {}, "engines": {"node": ">= 0.8.0"}, "licenses": "MIT", "keywords": ["wide character", "wc", "wide character string", "wcs", "terminal", "width", "wcwidth", "wcswidth"], "_id": "wcwidth.js@0.0.4", "dist": {"shasum": "44298a7c899c17501990fdaddd76ef6bd081be75", "tarball": "http://registry.npmjs.org/wcwidth.js/-/wcwidth.js-0.0.4.tgz"}, "_from": "wcwidth.js@~0.0.4", "_npmVersion": "1.3.11", "_npmUser": {"name": "mycoboco", "email": "<EMAIL>"}, "maintainers": [{"name": "mycoboco", "email": "<EMAIL>"}], "directories": {}, "_shasum": "44298a7c899c17501990fdaddd76ef6bd081be75", "_resolved": "https://registry.npmjs.org/wcwidth.js/-/wcwidth.js-0.0.4.tgz", "readme": "ERROR: No README data found!"}