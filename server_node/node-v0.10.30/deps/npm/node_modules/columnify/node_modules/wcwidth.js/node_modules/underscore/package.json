{"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://underscorejs.org", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/jashkenas/underscore.git"}, "main": "underscore.js", "version": "1.6.0", "devDependencies": {"docco": "0.6.x", "phantomjs": "1.9.0-1", "uglify-js": "2.4.x"}, "scripts": {"test": "phantomjs test/vendor/runner.js test/index.html?noglobals=true", "build": "uglifyjs underscore.js -c \"evaluate=false\" --comments \"/    .*/\" -m --source-map underscore-min.map -o underscore-min.js", "doc": "docco underscore.js"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/jashkenas/underscore/master/LICENSE"}], "files": ["underscore.js", "underscore-min.js", "LICENSE"], "bugs": {"url": "https://github.com/jashkenas/underscore/issues"}, "_id": "underscore@1.6.0", "dist": {"shasum": "8b38b10cacdef63337b8b24e4ff86d45aea529a8", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.6.0.tgz"}, "_from": "underscore@>= 1.3.0", "_npmVersion": "1.3.21", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}, "_shasum": "8b38b10cacdef63337b8b24e4ff86d45aea529a8", "_resolved": "https://registry.npmjs.org/underscore/-/underscore-1.6.0.tgz", "readme": "ERROR: No README data found!"}