{"name": "strip-ansi", "version": "0.2.2", "description": "Strip ANSI escape codes", "license": "MIT", "bin": {"strip-ansi": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-regex": "^0.1.0"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/strip-ansi/issues"}, "homepage": "https://github.com/sindresorhus/strip-ansi", "_id": "strip-ansi@0.2.2", "_shasum": "854d290c981525fc8c397a910b025ae2d54ffc08", "_from": "strip-ansi@^0.2.1", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "854d290c981525fc8c397a910b025ae2d54ffc08", "tarball": "http://registry.npmjs.org/strip-ansi/-/strip-ansi-0.2.2.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.2.2.tgz", "readme": "ERROR: No README data found!"}