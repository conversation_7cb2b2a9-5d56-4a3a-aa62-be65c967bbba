{"name": "columnify", "version": "1.1.0", "description": "Render data in text columns, supports in-column text-wrap.", "main": "index.js", "scripts": {"test": "faucet"}, "author": {"name": "<PERSON>"}, "license": "MIT", "devDependencies": {"chalk": "^0.4.0", "faucet": "0.0.1", "tape": "~2.12.3"}, "repository": {"type": "git", "url": "git://github.com/timoxley/columnify.git"}, "keywords": ["column", "text", "ansi", "console", "terminal", "wrap", "table"], "bugs": {"url": "https://github.com/timoxley/columnify/issues"}, "homepage": "https://github.com/timoxley/columnify", "dependencies": {"strip-ansi": "^0.2.1", "wcwidth.js": "~0.0.4"}, "directories": {"test": "test"}, "_id": "columnify@1.1.0", "_shasum": "0b908e6d4f1c80194358a1933aaf9dc49271c679", "_from": "columnify@latest", "_npmVersion": "1.4.10", "_npmUser": {"name": "timo<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "timo<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0b908e6d4f1c80194358a1933aaf9dc49271c679", "tarball": "http://registry.npmjs.org/columnify/-/columnify-1.1.0.tgz"}, "_resolved": "https://registry.npmjs.org/columnify/-/columnify-1.1.0.tgz", "readme": "ERROR: No README data found!"}