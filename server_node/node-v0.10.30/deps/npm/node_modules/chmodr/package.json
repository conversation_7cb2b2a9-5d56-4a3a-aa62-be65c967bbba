{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chmodr", "description": "like `chmod -R`", "version": "0.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/chmodr.git"}, "main": "chmodr.js", "devDependencies": {"tap": "0.2", "mkdirp": "0.3", "rimraf": ""}, "scripts": {"test": "tap test/*.js"}, "license": "BSD", "readme": "Like `chmod -R`.\n\nTakes the same arguments as `fs.chmod()`\n", "readmeFilename": "README.md", "_id": "chmodr@0.1.0", "_from": "chmodr@latest"}