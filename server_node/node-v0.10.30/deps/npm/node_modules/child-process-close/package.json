{"name": "child-process-close", "version": "0.1.1", "description": "Make child_process objects emit 'close' events in node v0.6 like they do in v0.8. This makes it easier to write code that works correctly on both version of node.", "main": "index.js", "scripts": {"test": "node test/test.js"}, "repository": {"type": "git", "url": "git://github.com/piscisaureus/child-process-close.git"}, "keywords": ["child_process", "spawn", "fork", "exec", "execFile", "close", "exit"], "author": {"name": "<PERSON>"}, "license": "MIT", "readme": "\n# child-process-close\n\nThis module makes child process objects, (created with `spawn`, `fork`, `exec`\nor `execFile`) emit the `close` event in node v0.6 like they do in node v0.8.\nThis makes it easier to write code that works correctly on both version of\nnode.\n\n\n## Usage\n\nJust make sure to `require('child-process-close')` anywhere. It will patch the `child_process` module.\n\n```js\nrequire('child-process-close');\nvar spawn = require('child_process').spawn;\n\nvar cp = spawn('foo');\ncp.on('close', function(exitCode, signal) {\n  // This now works on all node versions.\n});\n```\n\n\n## License\n\nCopyright (C) 2012 Bert Belder\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/piscisaureus/child-process-close/issues"}, "_id": "child-process-close@0.1.1", "dist": {"shasum": "c1909c6c3bbcea623e3bd74493ddb1c94c47c500"}, "_from": "child-process-close@", "_resolved": "https://registry.npmjs.org/child-process-close/-/child-process-close-0.1.1.tgz"}