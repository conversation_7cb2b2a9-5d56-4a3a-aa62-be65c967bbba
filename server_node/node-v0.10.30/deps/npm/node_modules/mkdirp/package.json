{"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.3.5", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "main": "./index", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "http://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.4.0"}, "license": "MIT", "readme": "# mkdirp\n\nLike `mkdir -p`, but in node.js!\n\n[![build status](https://secure.travis-ci.org/substack/node-mkdirp.png)](http://travis-ci.org/substack/node-mkdirp)\n\n# example\n\n## pow.js\n\n```js\nvar mkdirp = require('mkdirp');\n    \nmkdirp('/tmp/foo/bar/baz', function (err) {\n    if (err) console.error(err)\n    else console.log('pow!')\n});\n```\n\nOutput\n\n```\npow!\n```\n\nAnd now /tmp/foo/bar/baz exists, huzzah!\n\n# methods\n\n```js\nvar mkdirp = require('mkdirp');\n```\n\n## mkdirp(dir, mode, cb)\n\nCreate a new directory and any necessary subdirectories at `dir` with octal\npermission string `mode`.\n\nIf `mode` isn't specified, it defaults to `0777 & (~process.umask())`.\n\n`cb(err, made)` fires with the error or the first directory `made`\nthat had to be created, if any.\n\n## mkdirp.sync(dir, mode)\n\nSynchronously create a new directory and any necessary subdirectories at `dir`\nwith octal permission string `mode`.\n\nIf `mode` isn't specified, it defaults to `0777 & (~process.umask())`.\n\nReturns the first directory that had to be created, if any.\n\n# install\n\nWith [npm](http://npmjs.org) do:\n\n```\nnpm install mkdirp\n```\n\n# license\n\nMIT\n", "readmeFilename": "readme.markdown", "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "_id": "mkdirp@0.3.5", "_from": "mkdirp@latest"}