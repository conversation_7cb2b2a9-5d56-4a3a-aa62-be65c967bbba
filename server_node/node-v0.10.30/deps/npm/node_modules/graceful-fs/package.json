{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "gitHead": "0caa11544c0c9001db78bf593cf0c5805d149a41", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@3.0.2", "_shasum": "2cb5bf7f742bea8ad47c754caeee032b7e71a577", "_from": "graceful-fs@~3.0.0", "_npmVersion": "1.4.14", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "2cb5bf7f742bea8ad47c754caeee032b7e71a577", "tarball": "http://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.2.tgz"}, "_resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.2.tgz"}