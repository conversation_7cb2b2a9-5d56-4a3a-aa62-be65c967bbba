{"name": "archy", "version": "0.0.2", "description": "render nested hierarchies `npm ls` style with unicode pipes", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "devDependencies": {"tap": "~0.2.3"}, "scripts": {"test": "tap test"}, "repository": {"type": "git", "url": "git://github.com/substack/node-archy.git"}, "keywords": ["hierarchy", "npm ls", "unicode", "pretty", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "archy@0.0.2", "dependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.13", "_nodeVersion": "v0.7.7-pre", "_defaultsLoaded": true, "_from": "archy@0.0.2"}