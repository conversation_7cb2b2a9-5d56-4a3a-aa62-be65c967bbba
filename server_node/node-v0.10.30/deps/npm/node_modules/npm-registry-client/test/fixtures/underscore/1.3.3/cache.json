{"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.3.3", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_id": "underscore@1.3.3", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "47ac53683daf832bfa952e1774417da47817ae42", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.3.3.tgz"}, "readme": "                       __                                                         \n                      /\\ \\                                                         __           \n     __  __    ___    \\_\\ \\     __   _ __   ____    ___    ___   _ __    __       /\\_\\    ____  \n    /\\ \\/\\ \\ /' _ `\\  /'_  \\  /'__`\\/\\  __\\/ ,__\\  / ___\\ / __`\\/\\  __\\/'__`\\     \\/\\ \\  /',__\\ \n    \\ \\ \\_\\ \\/\\ \\/\\ \\/\\ \\ \\ \\/\\  __/\\ \\ \\//\\__, `\\/\\ \\__//\\ \\ \\ \\ \\ \\//\\  __/  __  \\ \\ \\/\\__, `\\\n     \\ \\____/\\ \\_\\ \\_\\ \\___,_\\ \\____\\\\ \\_\\\\/\\____/\\ \\____\\ \\____/\\ \\_\\\\ \\____\\/\\_\\ _\\ \\ \\/\\____/\n      \\/___/  \\/_/\\/_/\\/__,_ /\\/____/ \\/_/ \\/___/  \\/____/\\/___/  \\/_/ \\/____/\\/_//\\ \\_\\ \\/___/ \n                                                                                  \\ \\____/      \n                                                                                   \\/___/\n                                                                               \nUnderscore.js is a utility-belt library for JavaScript that provides \nsupport for the usual functional suspects (each, map, reduce, filter...) \nwithout extending any core JavaScript objects.\n\nFor Docs, License, Tests, and pre-packed downloads, see:\nhttp://documentcloud.github.com/underscore/\n\nMany thanks to our contributors:\nhttps://github.com/documentcloud/underscore/contributors\n", "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}