{"_id": "underscore", "_rev": "72-47f2986bfd8e8b55068b204588bbf484", "name": "underscore", "description": "JavaScript's functional programming helper library.", "dist-tags": {"latest": "1.3.3", "stable": "1.3.3"}, "versions": {"1.0.3": {"name": "underscore", "description": "Functional programming aid for JavaScript. Works well with jQuery.", "url": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "underscore", "version": "1.0.3", "_id": "underscore@1.0.3", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "http://registry.npmjs.org/underscore/-/underscore-1.0.3.tgz"}, "directories": {}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}]}, "1.0.4": {"name": "underscore", "description": "Functional programming aid for JavaScript. Works well with jQuery.", "url": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "underscore", "version": "1.0.4", "_id": "underscore@1.0.4", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "http://registry.npmjs.org/underscore/-/underscore-1.0.4.tgz"}, "directories": {}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}]}, "1.1.0": {"name": "underscore", "description": "Functional programming aid for JavaScript. Works well with jQuery.", "url": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "underscore", "version": "1.1.0", "_id": "underscore@1.1.0", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "http://registry.npmjs.org/underscore/-/underscore-1.1.0.tgz"}, "directories": {}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}]}, "1.1.1": {"name": "underscore", "description": "Functional programming aid for JavaScript. Works well with jQuery.", "url": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "underscore", "version": "1.1.1", "_id": "underscore@1.1.1", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "http://registry.npmjs.org/underscore/-/underscore-1.1.1.tgz"}, "directories": {}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}]}, "1.1.2": {"name": "underscore", "description": "Functional programming aid for JavaScript. Works well with jQuery.", "url": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "underscore", "version": "1.1.2", "_id": "underscore@1.1.2", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "http://registry.npmjs.org/underscore/-/underscore-1.1.2.tgz"}, "directories": {}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}]}, "1.1.3": {"name": "underscore", "description": "Functional programming aid for JavaScript. Works well with jQuery.", "url": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "underscore", "version": "1.1.3", "_id": "underscore@1.1.3", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.8-1", "_nodeVersion": "v0.2.5", "dist": {"tarball": "http://registry.npmjs.org/underscore/-/underscore-1.1.3.tgz"}, "directories": {}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}]}, "1.1.4": {"name": "underscore", "description": "Functional programming aid for JavaScript. Works well with jQuery.", "url": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "lib": ".", "main": "underscore.js", "version": "1.1.4", "_id": "underscore@1.1.4", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.9", "_nodeVersion": "v0.5.0-pre", "dist": {"shasum": "9e82274902865625b3a6d4c315a38ffd80047dae", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.1.4.tgz"}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}, "1.1.5": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.1.5", "_id": "underscore@1.1.5", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.16", "_nodeVersion": "v0.4.2", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "23601d62c75619998b2f0db24938102793336a56", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.1.5.tgz"}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}]}, "1.1.6": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.1.6", "_id": "underscore@1.1.6", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.18", "_nodeVersion": "v0.4.2", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "6868da1bdd72d75285be0b4e50f228e70d001a2c", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.1.6.tgz"}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}]}, "1.1.7": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.1.7", "devDependencies": {}, "_id": "underscore@1.1.7", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.7", "_defaultsLoaded": true, "dist": {"shasum": "40bab84bad19d230096e8d6ef628bff055d83db0", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.1.7.tgz"}, "scripts": {}, "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.2.0", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/underscore/1.2.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "underscore@1.2.0", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "b32ce32c8c118caa8031c10b54c7f65ab3b557fd", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.2.0.tgz"}, "scripts": {}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "directories": {}}, "1.2.1": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.2.1", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/underscore/1.2.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "underscore@1.2.1", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "fc5c6b0765673d92a2d4ac8b4dc0aa88702e2bd4", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.2.1.tgz"}, "scripts": {}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "directories": {}}, "1.2.2": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.2.2", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_id": "underscore@1.2.2", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "74dd40e9face84e724eb2edae945b8aedc233ba3", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.2.2.tgz"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}, "1.2.3": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "dependencies": {}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.2.3", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_id": "underscore@1.2.3", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "11b874da70f4683d7d48bba2b44be1e600d2f6cf", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.2.3.tgz"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}, "1.2.4": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.2.4", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_id": "underscore@1.2.4", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "e8da6241aa06f64df2473bb2590b8c17c84c3c7e", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.2.4.tgz"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}, "1.3.0": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.3.0", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_id": "underscore@1.3.0", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "253b2d79b7bb67943ced0fc744eb18267963ede8", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.3.0.tgz"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}, "1.3.1": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.3.1", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_id": "underscore@1.3.1", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "6cb8aad0e77eb5dbbfb54b22bcd8697309cf9641", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.3.1.tgz"}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}, "1.3.2": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.3.2", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_id": "underscore@1.3.2", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "1b4e455089ab1d1d38ab6794ffe6cf08f764394a", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.3.2.tgz"}, "readme": "                       __                                                         \n                      /\\ \\                                                         __           \n     __  __    ___    \\_\\ \\     __   _ __   ____    ___    ___   _ __    __       /\\_\\    ____  \n    /\\ \\/\\ \\ /' _ `\\  /'_  \\  /'__`\\/\\  __\\/ ,__\\  / ___\\ / __`\\/\\  __\\/'__`\\     \\/\\ \\  /',__\\ \n    \\ \\ \\_\\ \\/\\ \\/\\ \\/\\ \\ \\ \\/\\  __/\\ \\ \\//\\__, `\\/\\ \\__//\\ \\ \\ \\ \\ \\//\\  __/  __  \\ \\ \\/\\__, `\\\n     \\ \\____/\\ \\_\\ \\_\\ \\___,_\\ \\____\\\\ \\_\\\\/\\____/\\ \\____\\ \\____/\\ \\_\\\\ \\____\\/\\_\\ _\\ \\ \\/\\____/\n      \\/___/  \\/_/\\/_/\\/__,_ /\\/____/ \\/_/ \\/___/  \\/____/\\/___/  \\/_/ \\/____/\\/_//\\ \\_\\ \\/___/ \n                                                                                  \\ \\____/      \n                                                                                   \\/___/\n                                                                               \nUnderscore.js is a utility-belt library for JavaScript that provides \nsupport for the usual functional suspects (each, map, reduce, filter...) \nwithout extending any core JavaScript objects.\n\nFor Docs, License, Tests, and pre-packed downloads, see:\nhttp://documentcloud.github.com/underscore/\n\nMany thanks to our contributors:\nhttps://github.com/documentcloud/underscore/contributors\n", "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}, "1.3.3": {"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://documentcloud.github.com/underscore/", "keywords": ["util", "functional", "server", "client", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "main": "underscore.js", "version": "1.3.3", "_npmUser": {"name": "jashkenas", "email": "<EMAIL>"}, "_id": "underscore@1.3.3", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "47ac53683daf832bfa952e1774417da47817ae42", "tarball": "http://registry.npmjs.org/underscore/-/underscore-1.3.3.tgz"}, "readme": "                       __                                                         \n                      /\\ \\                                                         __           \n     __  __    ___    \\_\\ \\     __   _ __   ____    ___    ___   _ __    __       /\\_\\    ____  \n    /\\ \\/\\ \\ /' _ `\\  /'_  \\  /'__`\\/\\  __\\/ ,__\\  / ___\\ / __`\\/\\  __\\/'__`\\     \\/\\ \\  /',__\\ \n    \\ \\ \\_\\ \\/\\ \\/\\ \\/\\ \\ \\ \\/\\  __/\\ \\ \\//\\__, `\\/\\ \\__//\\ \\ \\ \\ \\ \\//\\  __/  __  \\ \\ \\/\\__, `\\\n     \\ \\____/\\ \\_\\ \\_\\ \\___,_\\ \\____\\\\ \\_\\\\/\\____/\\ \\____\\ \\____/\\ \\_\\\\ \\____\\/\\_\\ _\\ \\ \\/\\____/\n      \\/___/  \\/_/\\/_/\\/__,_ /\\/____/ \\/_/ \\/___/  \\/____/\\/___/  \\/_/ \\/____/\\/_//\\ \\_\\ \\/___/ \n                                                                                  \\ \\____/      \n                                                                                   \\/___/\n                                                                               \nUnderscore.js is a utility-belt library for JavaScript that provides \nsupport for the usual functional suspects (each, map, reduce, filter...) \nwithout extending any core JavaScript objects.\n\nFor Docs, License, Tests, and pre-packed downloads, see:\nhttp://documentcloud.github.com/underscore/\n\nMany thanks to our contributors:\nhttps://github.com/documentcloud/underscore/contributors\n", "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "directories": {}}}, "maintainers": [{"name": "documentcloud", "email": "<EMAIL>"}, {"name": "jashkenas", "email": "<EMAIL>"}], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "time": {"1.0.3": "2011-12-07T15:12:18.045Z", "1.0.4": "2011-12-07T15:12:18.045Z", "1.1.0": "2011-12-07T15:12:18.045Z", "1.1.1": "2011-12-07T15:12:18.045Z", "1.1.2": "2011-12-07T15:12:18.045Z", "1.1.3": "2011-12-07T15:12:18.045Z", "1.1.4": "2011-12-07T15:12:18.045Z", "1.1.5": "2011-12-07T15:12:18.045Z", "1.1.6": "2011-12-07T15:12:18.045Z", "1.1.7": "2011-12-07T15:12:18.045Z", "1.2.0": "2011-12-07T15:12:18.045Z", "1.2.1": "2011-12-07T15:12:18.045Z", "1.2.2": "2011-11-14T20:28:47.115Z", "1.2.3": "2011-12-07T15:12:18.045Z", "1.2.4": "2012-01-09T17:23:14.818Z", "1.3.0": "2012-01-11T16:41:38.459Z", "1.3.1": "2012-01-23T22:57:36.474Z", "1.3.2": "2012-04-09T18:38:14.345Z", "1.3.3": "2012-04-10T14:43:48.089Z"}, "repository": {"type": "git", "url": "git://github.com/documentcloud/underscore.git"}, "users": {"vesln": true, "mvolkmann": true, "lancehunt": true, "mikl": true, "linus": true, "vasc": true, "bat": true, "dmalam": true, "mbrevoort": true, "danielr": true, "rsimoes": true, "thlorenz": true}}