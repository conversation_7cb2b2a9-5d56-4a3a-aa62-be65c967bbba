{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "npm-registry-client", "description": "Client for the npm registry", "version": "2.0.3", "repository": {"url": "git://github.com/isaacs/npm-registry-client"}, "main": "index.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"chownr": "0", "graceful-fs": "^3.0.0", "mkdirp": "~0.3.3", "npm-cache-filename": "^1.0.0", "request": "2 >=2.25.0", "retry": "0.6.0", "rimraf": "~2", "semver": "2 >=2.2.1", "slide": "~1.1.3", "npmlog": ""}, "devDependencies": {"tap": ""}, "optionalDependencies": {"npmlog": ""}, "license": "BSD", "readme": "# npm-registry-client\n\nThe code that n<PERSON> uses to talk to the registry.\n\nIt handles all the caching and HTTP calls.\n\n## Usage\n\n```javascript\nvar RegClient = require('npm-registry-client')\nvar client = new RegClient(config)\nvar uri = \"npm://registry.npmjs.org/npm\"\nvar options = {timeout: 1000}\n\nclient.get(uri, options, function (error, data, raw, res) {\n  // error is an error if there was a problem.\n  // data is the parsed data object\n  // raw is the json string\n  // res is the response from couch\n})\n```\n\n# Registry URLs\n\nThe registry calls take either a full URL pointing to a resource in the\nregistry, or a base URL for the registry as a whole (for the base URL, any path\nwill be ignored). In addition to `http` and `https`, `npm` URLs are allowed.\n`npm` URLs are `https` URLs with the additional restrictions that they will\nalways include authorization credentials, and the response is always registry\nmetadata (and not tarballs or other attachments).\n\n# Configuration\n\nThis program is designed to work with\n[npmconf](https://npmjs.org/package/npmconf), but you can also pass in\na plain-jane object with the appropriate configs, and it'll shim it\nfor you.  Any configuration thingie that has get/set/del methods will\nalso be accepted.\n\n* `cache` **Required** {String} Path to the cache folder\n* `always-auth` {Boolean} Auth even for GET requests.\n* `auth` {String} A base64-encoded `username:password`\n* `email` {String} User's email address\n* `tag` {String} The default tag to use when publishing new packages.\n  Default = `\"latest\"`\n* `ca` {String} Cerficate signing authority certificates to trust.\n* `cert` {String} Client certificate (PEM encoded). Enable access\n  to servers that require client certificates\n* `key` {String} Private key (PEM encoded) for client certificate 'cert'\n* `strict-ssl` {Boolean} Whether or not to be strict with SSL\n  certificates.  Default = `true`\n* `user-agent` {String} User agent header to send.  Default =\n  `\"node/{process.version} {process.platform} {process.arch}\"`\n* `log` {Object} The logger to use.  Defaults to `require(\"npmlog\")` if\n  that works, otherwise logs are disabled.\n* `fetch-retries` {Number} Number of times to retry on GET failures.\n  Default=2\n* `fetch-retry-factor` {Number} `factor` setting for `node-retry`. Default=10\n* `fetch-retry-mintimeout` {Number} `minTimeout` setting for `node-retry`.\n  Default=10000 (10 seconds)\n* `fetch-retry-maxtimeout` {Number} `maxTimeout` setting for `node-retry`.\n  Default=60000 (60 seconds)\n* `proxy` {URL} The url to proxy requests through.\n* `https-proxy` {URL} The url to proxy https requests through.\n  Defaults to be the same as `proxy` if unset.\n* `_auth` {String} The base64-encoded authorization header.\n* `username` `_password` {String} Username/password to use to generate\n  `_auth` if not supplied.\n* `_token` {Object} A token for use with\n  [couch-login](https://npmjs.org/package/couch-login)\n\n# client.request(method, uri, options, cb)\n\n* `method` {String} HTTP method\n* `uri` {String} URI pointing to the resource to request\n* `options` {Object} Object containing optional per-request properties.\n  * `what` {Stream | Buffer | String | Object} The request body.  Objects\n    that are not Buffers or Streams are encoded as JSON.\n  * `etag` {String} The cached ETag\n  * `follow` {Boolean} Follow 302/301 responses (defaults to true)\n* `cb` {Function}\n  * `error` {Error | null}\n  * `data` {Object} the parsed data object\n  * `raw` {String} the json\n  * `res` {Response Object} response from couch\n\nMake a request to the registry.  All the other methods are wrappers around\n`request`.\n\n# client.adduser(base, username, password, email, cb)\n\n* `base` {String} Base registry URL\n* `username` {String}\n* `password` {String}\n* `email` {String}\n* `cb` {Function}\n\nAdd a user account to the registry, or verify the credentials.\n\n# client.deprecate(uri, version, message, cb)\n\n* `uri` {String} Full registry URI for the deprecated package\n* `version` {String} Semver version range\n* `message` {String} The message to use as a deprecation warning\n* `cb` {Function}\n\nDeprecate a version of a package in the registry.\n\n# client.bugs(uri, cb)\n\n* `uri` {String} Full registry URI for the package\n* `cb` {Function}\n\nGet the url for bugs of a package\n\n# client.get(uri, options, cb)\n\n* `uri` {String} The complete registry URI to fetch\n* `options` {Object} Object containing optional per-request properties.\n  * `timeout` {Number} Duration before the request times out.\n  * `follow` {Boolean} Follow 302/301 responses (defaults to true)\n  * `staleOk` {Boolean} If there's cached data available, then return that\n    to the callback quickly, and update the cache the background.\n\nFetches data from the registry via a GET request, saving it in the cache folder\nwith the ETag.\n\n# client.publish(uri, data, tarball, cb)\n\n* `uri` {String} The registry URI to publish to\n* `data` {Object} Package data\n* `tarball` {String | Stream} Filename or stream of the package tarball\n* `cb` {Function}\n\nPublish a package to the registry.\n\nNote that this does not create the tarball from a folder.  However, it can\naccept a gzipped tar stream or a filename to a tarball.\n\n# client.star(uri, starred, cb)\n\n* `uri` {String} The complete registry URI to star\n* `starred` {Boolean} True to star the package, false to unstar it.\n* `cb` {Function}\n\nStar or unstar a package.\n\nNote that the user does not have to be the package owner to star or unstar a\npackage, though other writes do require that the user be the package owner.\n\n# client.stars(base, username, cb)\n\n* `base` {String} The base URL for the registry\n* `username` {String} Name of user to fetch starred packages for.\n* `cb` {Function}\n\nView your own or another user's starred packages.\n\n# client.tag(uri, version, tag, cb)\n\n* `uri` {String} The complete registry URI to tag\n* `version` {String} Version to tag\n* `tag` {String} Tag name to apply\n* `cb` {Function}\n\nMark a version in the `dist-tags` hash, so that `pkg@tag` will fetch the\nspecified version.\n\n# client.unpublish(uri, [ver], cb)\n\n* `uri` {String} The complete registry URI to unpublish\n* `ver` {String} version to unpublish. Leave blank to unpublish all\n  versions.\n* `cb` {Function}\n\nRemove a version of a package (or all versions) from the registry.  When the\nlast version us unpublished, the entire document is removed from the database.\n\n# client.upload(uri, file, [etag], [nofollow], cb)\n\n* `uri` {String} The complete registry URI to upload to\n* `file` {String | Stream} Either the filename or a readable stream\n* `etag` {String} Cache ETag\n* `nofollow` {Boolean} Do not follow 301/302 responses\n* `cb` {Function}\n\nUpload an attachment.  Mostly used by `client.publish()`.\n", "readmeFilename": "README.md", "gitHead": "2578fb9a807d77417554ba235ba8fac39405e832", "bugs": {"url": "https://github.com/isaacs/npm-registry-client/issues"}, "homepage": "https://github.com/isaacs/npm-registry-client", "_id": "npm-registry-client@2.0.3", "_shasum": "93dad3d9a162c99404badb71739c622c0f3b9a72", "_from": "npm-registry-client@2.0.3"}