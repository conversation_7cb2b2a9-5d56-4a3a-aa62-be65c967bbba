{"name": "once", "version": "1.3.0", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "readme": "# once\n\nOnly call a function once.\n\n## usage\n\n```javascript\nvar once = require('once')\n\nfunction load (file, cb) {\n  cb = once(cb)\n  loader.load('file')\n  loader.once('load', cb)\n  loader.once('error', cb)\n}\n```\n\nOr add to the Function.prototype in a responsible way:\n\n```javascript\n// only has to be done once\nrequire('once').proto()\n\nfunction load (file, cb) {\n  cb = cb.once()\n  loader.load('file')\n  loader.once('load', cb)\n  loader.once('error', cb)\n}\n```\n\nIronically, the prototype feature makes this module twice as\ncomplicated as necessary.\n\nTo check whether you function has been called, use `fn.called`. Once the\nfunction is called for the first time the return value of the original\nfunction is saved in `fn.value` and subsequent calls will continue to\nreturn this value.\n\n```javascript\nvar once = require('once')\n\nfunction load (cb) {\n  cb = once(cb)\n  var stream = createStream()\n  stream.once('data', cb)\n  stream.once('end', function () {\n    if (!cb.called) cb(new Error('not found'))\n  })\n}\n```\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "_id": "once@1.3.0", "_from": "once@latest"}