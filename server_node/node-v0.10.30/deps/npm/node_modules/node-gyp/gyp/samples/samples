#!/usr/bin/python

# Copyright (c) 2009 Google Inc. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import os.path
import shutil
import sys


gyps = [
    'app/app.gyp',
    'base/base.gyp',
    'build/temp_gyp/googleurl.gyp',
    'build/all.gyp',
    'build/common.gypi',
    'build/external_code.gypi',
    'chrome/test/security_tests/security_tests.gyp',
    'chrome/third_party/hunspell/hunspell.gyp',
    'chrome/chrome.gyp',
    'media/media.gyp',
    'net/net.gyp',
    'printing/printing.gyp',
    'sdch/sdch.gyp',
    'skia/skia.gyp',
    'testing/gmock.gyp',
    'testing/gtest.gyp',
    'third_party/bzip2/bzip2.gyp',
    'third_party/icu38/icu38.gyp',
    'third_party/libevent/libevent.gyp',
    'third_party/libjpeg/libjpeg.gyp',
    'third_party/libpng/libpng.gyp',
    'third_party/libxml/libxml.gyp',
    'third_party/libxslt/libxslt.gyp',
    'third_party/lzma_sdk/lzma_sdk.gyp',
    'third_party/modp_b64/modp_b64.gyp',
    'third_party/npapi/npapi.gyp',
    'third_party/sqlite/sqlite.gyp',
    'third_party/zlib/zlib.gyp',
    'v8/tools/gyp/v8.gyp',
    'webkit/activex_shim/activex_shim.gyp',
    'webkit/activex_shim_dll/activex_shim_dll.gyp',
    'webkit/build/action_csspropertynames.py',
    'webkit/build/action_cssvaluekeywords.py',
    'webkit/build/action_jsconfig.py',
    'webkit/build/action_makenames.py',
    'webkit/build/action_maketokenizer.py',
    'webkit/build/action_useragentstylesheets.py',
    'webkit/build/rule_binding.py',
    'webkit/build/rule_bison.py',
    'webkit/build/rule_gperf.py',
    'webkit/tools/test_shell/test_shell.gyp',
    'webkit/webkit.gyp',
]


def Main(argv):
  if len(argv) != 3 or argv[1] not in ['push', 'pull']:
    print 'Usage: %s push/pull PATH_TO_CHROME' % argv[0]
    return 1

  path_to_chrome = argv[2]

  for g in gyps:
    chrome_file = os.path.join(path_to_chrome, g)
    local_file = os.path.join(os.path.dirname(argv[0]), os.path.split(g)[1])
    if argv[1] == 'push':
      print 'Copying %s to %s' % (local_file, chrome_file)
      shutil.copyfile(local_file, chrome_file)
    elif argv[1] == 'pull':
      print 'Copying %s to %s' % (chrome_file, local_file)
      shutil.copyfile(chrome_file, local_file)
    else:
      assert False

  return 0


if __name__ == '__main__':
  sys.exit(Main(sys.argv))
