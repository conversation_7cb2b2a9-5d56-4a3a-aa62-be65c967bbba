#!/usr/bin/env python

# Copyright (c) 2012 Google Inc. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

"""Unit tests for the MSVSSettings.py file."""

import StringIO
import unittest
import gyp.MSVSSettings as MSVSSettings


class TestSequenceFunctions(unittest.TestCase):

  def setUp(self):
    self.stderr = StringIO.StringIO()

  def _ExpectedWarnings(self, expected):
    """Compares recorded lines to expected warnings."""
    self.stderr.seek(0)
    actual = self.stderr.read().split('\n')
    actual = [line for line in actual if line]
    self.assertEqual(sorted(expected), sorted(actual))

  def testValidateMSVSSettings_tool_names(self):
    """Tests that only MSVS tool names are allowed."""
    MSVSSettings.ValidateMSVSSettings(
        {'VCCLCompilerTool': {},
         'VCLinkerTool': {},
         'VCMIDLTool': {},
         'foo': {},
         'VCResourceCompilerTool': {},
         'VCLibrarianTool': {},
         'VCManifestTool': {},
         'ClCompile': {}},
        self.stderr)
    self._ExpectedWarnings([
        'Warning: unrecognized tool foo',
        'Warning: unrecognized tool ClCompile'])

  def testValidateMSVSSettings_settings(self):
    """Tests that for invalid MSVS settings."""
    MSVSSettings.ValidateMSVSSettings(
        {'VCCLCompilerTool': {
            'AdditionalIncludeDirectories': 'folder1;folder2',
            'AdditionalOptions': ['string1', 'string2'],
            'AdditionalUsingDirectories': 'folder1;folder2',
            'AssemblerListingLocation': 'a_file_name',
            'AssemblerOutput': '0',
            'BasicRuntimeChecks': '5',
            'BrowseInformation': 'fdkslj',
            'BrowseInformationFile': 'a_file_name',
            'BufferSecurityCheck': 'true',
            'CallingConvention': '-1',
            'CompileAs': '1',
            'DebugInformationFormat': '2',
            'DefaultCharIsUnsigned': 'true',
            'Detect64BitPortabilityProblems': 'true',
            'DisableLanguageExtensions': 'true',
            'DisableSpecificWarnings': 'string1;string2',
            'EnableEnhancedInstructionSet': '1',
            'EnableFiberSafeOptimizations': 'true',
            'EnableFunctionLevelLinking': 'true',
            'EnableIntrinsicFunctions': 'true',
            'EnablePREfast': 'true',
            'Enableprefast': 'bogus',
            'ErrorReporting': '1',
            'ExceptionHandling': '1',
            'ExpandAttributedSource': 'true',
            'FavorSizeOrSpeed': '1',
            'FloatingPointExceptions': 'true',
            'FloatingPointModel': '1',
            'ForceConformanceInForLoopScope': 'true',
            'ForcedIncludeFiles': 'file1;file2',
            'ForcedUsingFiles': 'file1;file2',
            'GeneratePreprocessedFile': '1',
            'GenerateXMLDocumentationFiles': 'true',
            'IgnoreStandardIncludePath': 'true',
            'InlineFunctionExpansion': '1',
            'KeepComments': 'true',
            'MinimalRebuild': 'true',
            'ObjectFile': 'a_file_name',
            'OmitDefaultLibName': 'true',
            'OmitFramePointers': 'true',
            'OpenMP': 'true',
            'Optimization': '1',
            'PrecompiledHeaderFile': 'a_file_name',
            'PrecompiledHeaderThrough': 'a_file_name',
            'PreprocessorDefinitions': 'string1;string2',
            'ProgramDataBaseFileName': 'a_file_name',
            'RuntimeLibrary': '1',
            'RuntimeTypeInfo': 'true',
            'ShowIncludes': 'true',
            'SmallerTypeCheck': 'true',
            'StringPooling': 'true',
            'StructMemberAlignment': '1',
            'SuppressStartupBanner': 'true',
            'TreatWChar_tAsBuiltInType': 'true',
            'UndefineAllPreprocessorDefinitions': 'true',
            'UndefinePreprocessorDefinitions': 'string1;string2',
            'UseFullPaths': 'true',
            'UsePrecompiledHeader': '1',
            'UseUnicodeResponseFiles': 'true',
            'WarnAsError': 'true',
            'WarningLevel': '1',
            'WholeProgramOptimization': 'true',
            'XMLDocumentationFileName': 'a_file_name',
            'ZZXYZ': 'bogus'},
         'VCLinkerTool': {
             'AdditionalDependencies': 'file1;file2',
             'AdditionalLibraryDirectories': 'folder1;folder2',
             'AdditionalManifestDependencies': 'file1;file2',
             'AdditionalOptions': 'a string1',
             'AddModuleNamesToAssembly': 'file1;file2',
             'AllowIsolation': 'true',
             'AssemblyDebug': '2',
             'AssemblyLinkResource': 'file1;file2',
             'BaseAddress': 'a string1',
             'CLRImageType': '2',
             'CLRThreadAttribute': '2',
             'CLRUnmanagedCodeCheck': 'true',
             'DataExecutionPrevention': '2',
             'DelayLoadDLLs': 'file1;file2',
             'DelaySign': 'true',
             'Driver': '2',
             'EmbedManagedResourceFile': 'file1;file2',
             'EnableCOMDATFolding': '2',
             'EnableUAC': 'true',
             'EntryPointSymbol': 'a string1',
             'ErrorReporting': '2',
             'FixedBaseAddress': '2',
             'ForceSymbolReferences': 'file1;file2',
             'FunctionOrder': 'a_file_name',
             'GenerateDebugInformation': 'true',
             'GenerateManifest': 'true',
             'GenerateMapFile': 'true',
             'HeapCommitSize': 'a string1',
             'HeapReserveSize': 'a string1',
             'IgnoreAllDefaultLibraries': 'true',
             'IgnoreDefaultLibraryNames': 'file1;file2',
             'IgnoreEmbeddedIDL': 'true',
             'IgnoreImportLibrary': 'true',
             'ImportLibrary': 'a_file_name',
             'KeyContainer': 'a_file_name',
             'KeyFile': 'a_file_name',
             'LargeAddressAware': '2',
             'LinkIncremental': '2',
             'LinkLibraryDependencies': 'true',
             'LinkTimeCodeGeneration': '2',
             'ManifestFile': 'a_file_name',
             'MapExports': 'true',
             'MapFileName': 'a_file_name',
             'MergedIDLBaseFileName': 'a_file_name',
             'MergeSections': 'a string1',
             'MidlCommandFile': 'a_file_name',
             'ModuleDefinitionFile': 'a_file_name',
             'OptimizeForWindows98': '1',
             'OptimizeReferences': '2',
             'OutputFile': 'a_file_name',
             'PerUserRedirection': 'true',
             'Profile': 'true',
             'ProfileGuidedDatabase': 'a_file_name',
             'ProgramDatabaseFile': 'a_file_name',
             'RandomizedBaseAddress': '2',
             'RegisterOutput': 'true',
             'ResourceOnlyDLL': 'true',
             'SetChecksum': 'true',
             'ShowProgress': '2',
             'StackCommitSize': 'a string1',
             'StackReserveSize': 'a string1',
             'StripPrivateSymbols': 'a_file_name',
             'SubSystem': '2',
             'SupportUnloadOfDelayLoadedDLL': 'true',
             'SuppressStartupBanner': 'true',
             'SwapRunFromCD': 'true',
             'SwapRunFromNet': 'true',
             'TargetMachine': '2',
             'TerminalServerAware': '2',
             'TurnOffAssemblyGeneration': 'true',
             'TypeLibraryFile': 'a_file_name',
             'TypeLibraryResourceID': '33',
             'UACExecutionLevel': '2',
             'UACUIAccess': 'true',
             'UseLibraryDependencyInputs': 'true',
             'UseUnicodeResponseFiles': 'true',
             'Version': 'a string1'},
         'VCMIDLTool': {
             'AdditionalIncludeDirectories': 'folder1;folder2',
             'AdditionalOptions': 'a string1',
             'CPreprocessOptions': 'a string1',
             'DefaultCharType': '1',
             'DLLDataFileName': 'a_file_name',
             'EnableErrorChecks': '1',
             'ErrorCheckAllocations': 'true',
             'ErrorCheckBounds': 'true',
             'ErrorCheckEnumRange': 'true',
             'ErrorCheckRefPointers': 'true',
             'ErrorCheckStubData': 'true',
             'GenerateStublessProxies': 'true',
             'GenerateTypeLibrary': 'true',
             'HeaderFileName': 'a_file_name',
             'IgnoreStandardIncludePath': 'true',
             'InterfaceIdentifierFileName': 'a_file_name',
             'MkTypLibCompatible': 'true',
             'notgood': 'bogus',
             'OutputDirectory': 'a string1',
             'PreprocessorDefinitions': 'string1;string2',
             'ProxyFileName': 'a_file_name',
             'RedirectOutputAndErrors': 'a_file_name',
             'StructMemberAlignment': '1',
             'SuppressStartupBanner': 'true',
             'TargetEnvironment': '1',
             'TypeLibraryName': 'a_file_name',
             'UndefinePreprocessorDefinitions': 'string1;string2',
             'ValidateParameters': 'true',
             'WarnAsError': 'true',
             'WarningLevel': '1'},
         'VCResourceCompilerTool': {
             'AdditionalOptions': 'a string1',
             'AdditionalIncludeDirectories': 'folder1;folder2',
             'Culture': '1003',
             'IgnoreStandardIncludePath': 'true',
             'notgood2': 'bogus',
             'PreprocessorDefinitions': 'string1;string2',
             'ResourceOutputFileName': 'a string1',
             'ShowProgress': 'true',
             'SuppressStartupBanner': 'true',
             'UndefinePreprocessorDefinitions': 'string1;string2'},
         'VCLibrarianTool': {
             'AdditionalDependencies': 'file1;file2',
             'AdditionalLibraryDirectories': 'folder1;folder2',
             'AdditionalOptions': 'a string1',
             'ExportNamedFunctions': 'string1;string2',
             'ForceSymbolReferences': 'a string1',
             'IgnoreAllDefaultLibraries': 'true',
             'IgnoreSpecificDefaultLibraries': 'file1;file2',
             'LinkLibraryDependencies': 'true',
             'ModuleDefinitionFile': 'a_file_name',
             'OutputFile': 'a_file_name',
             'SuppressStartupBanner': 'true',
             'UseUnicodeResponseFiles': 'true'},
         'VCManifestTool': {
             'AdditionalManifestFiles': 'file1;file2',
             'AdditionalOptions': 'a string1',
             'AssemblyIdentity': 'a string1',
             'ComponentFileName': 'a_file_name',
             'DependencyInformationFile': 'a_file_name',
             'GenerateCatalogFiles': 'true',
             'InputResourceManifests': 'a string1',
             'ManifestResourceFile': 'a_file_name',
             'OutputManifestFile': 'a_file_name',
             'RegistrarScriptFile': 'a_file_name',
             'ReplacementsFile': 'a_file_name',
             'SuppressStartupBanner': 'true',
             'TypeLibraryFile': 'a_file_name',
             'UpdateFileHashes': 'truel',
             'UpdateFileHashesSearchPath': 'a_file_name',
             'UseFAT32Workaround': 'true',
             'UseUnicodeResponseFiles': 'true',
             'VerboseOutput': 'true'}},
        self.stderr)
    self._ExpectedWarnings([
        'Warning: for VCCLCompilerTool/BasicRuntimeChecks, '
        'index value (5) not in expected range [0, 4)',
        'Warning: for VCCLCompilerTool/BrowseInformation, '
        "invalid literal for int() with base 10: 'fdkslj'",
        'Warning: for VCCLCompilerTool/CallingConvention, '
        'index value (-1) not in expected range [0, 3)',
        'Warning: for VCCLCompilerTool/DebugInformationFormat, '
        'converted value for 2 not specified.',
        'Warning: unrecognized setting VCCLCompilerTool/Enableprefast',
        'Warning: unrecognized setting VCCLCompilerTool/ZZXYZ',
        'Warning: for VCLinkerTool/TargetMachine, '
        'converted value for 2 not specified.',
        'Warning: unrecognized setting VCMIDLTool/notgood',
        'Warning: unrecognized setting VCResourceCompilerTool/notgood2',
        'Warning: for VCManifestTool/UpdateFileHashes, '
        "expected bool; got 'truel'"
        ''])

  def testValidateMSBuildSettings_settings(self):
    """Tests that for invalid MSBuild settings."""
    MSVSSettings.ValidateMSBuildSettings(
        {'ClCompile': {
            'AdditionalIncludeDirectories': 'folder1;folder2',
            'AdditionalOptions': ['string1', 'string2'],
            'AdditionalUsingDirectories': 'folder1;folder2',
            'AssemblerListingLocation': 'a_file_name',
            'AssemblerOutput': 'NoListing',
            'BasicRuntimeChecks': 'StackFrameRuntimeCheck',
            'BrowseInformation': 'false',
            'BrowseInformationFile': 'a_file_name',
            'BufferSecurityCheck': 'true',
            'BuildingInIDE': 'true',
            'CallingConvention': 'Cdecl',
            'CompileAs': 'CompileAsC',
            'CompileAsManaged': 'Pure',
            'CreateHotpatchableImage': 'true',
            'DebugInformationFormat': 'ProgramDatabase',
            'DisableLanguageExtensions': 'true',
            'DisableSpecificWarnings': 'string1;string2',
            'EnableEnhancedInstructionSet': 'StreamingSIMDExtensions',
            'EnableFiberSafeOptimizations': 'true',
            'EnablePREfast': 'true',
            'Enableprefast': 'bogus',
            'ErrorReporting': 'Prompt',
            'ExceptionHandling': 'SyncCThrow',
            'ExpandAttributedSource': 'true',
            'FavorSizeOrSpeed': 'Neither',
            'FloatingPointExceptions': 'true',
            'FloatingPointModel': 'Precise',
            'ForceConformanceInForLoopScope': 'true',
            'ForcedIncludeFiles': 'file1;file2',
            'ForcedUsingFiles': 'file1;file2',
            'FunctionLevelLinking': 'false',
            'GenerateXMLDocumentationFiles': 'true',
            'IgnoreStandardIncludePath': 'true',
            'InlineFunctionExpansion': 'OnlyExplicitInline',
            'IntrinsicFunctions': 'false',
            'MinimalRebuild': 'true',
            'MultiProcessorCompilation': 'true',
            'ObjectFileName': 'a_file_name',
            'OmitDefaultLibName': 'true',
            'OmitFramePointers': 'true',
            'OpenMPSupport': 'true',
            'Optimization': 'Disabled',
            'PrecompiledHeader': 'NotUsing',
            'PrecompiledHeaderFile': 'a_file_name',
            'PrecompiledHeaderOutputFile': 'a_file_name',
            'PreprocessKeepComments': 'true',
            'PreprocessorDefinitions': 'string1;string2',
            'PreprocessOutputPath': 'a string1',
            'PreprocessSuppressLineNumbers': 'false',
            'PreprocessToFile': 'false',
            'ProcessorNumber': '33',
            'ProgramDataBaseFileName': 'a_file_name',
            'RuntimeLibrary': 'MultiThreaded',
            'RuntimeTypeInfo': 'true',
            'ShowIncludes': 'true',
            'SmallerTypeCheck': 'true',
            'StringPooling': 'true',
            'StructMemberAlignment': '1Byte',
            'SuppressStartupBanner': 'true',
            'TrackerLogDirectory': 'a_folder',
            'TreatSpecificWarningsAsErrors': 'string1;string2',
            'TreatWarningAsError': 'true',
            'TreatWChar_tAsBuiltInType': 'true',
            'UndefineAllPreprocessorDefinitions': 'true',
            'UndefinePreprocessorDefinitions': 'string1;string2',
            'UseFullPaths': 'true',
            'UseUnicodeForAssemblerListing': 'true',
            'WarningLevel': 'TurnOffAllWarnings',
            'WholeProgramOptimization': 'true',
            'XMLDocumentationFileName': 'a_file_name',
            'ZZXYZ': 'bogus'},
         'Link': {
             'AdditionalDependencies': 'file1;file2',
             'AdditionalLibraryDirectories': 'folder1;folder2',
             'AdditionalManifestDependencies': 'file1;file2',
             'AdditionalOptions': 'a string1',
             'AddModuleNamesToAssembly': 'file1;file2',
             'AllowIsolation': 'true',
             'AssemblyDebug': '',
             'AssemblyLinkResource': 'file1;file2',
             'BaseAddress': 'a string1',
             'BuildingInIDE': 'true',
             'CLRImageType': 'ForceIJWImage',
             'CLRSupportLastError': 'Enabled',
             'CLRThreadAttribute': 'MTAThreadingAttribute',
             'CLRUnmanagedCodeCheck': 'true',
             'CreateHotPatchableImage': 'X86Image',
             'DataExecutionPrevention': 'false',
             'DelayLoadDLLs': 'file1;file2',
             'DelaySign': 'true',
             'Driver': 'NotSet',
             'EmbedManagedResourceFile': 'file1;file2',
             'EnableCOMDATFolding': 'false',
             'EnableUAC': 'true',
             'EntryPointSymbol': 'a string1',
             'FixedBaseAddress': 'false',
             'ForceFileOutput': 'Enabled',
             'ForceSymbolReferences': 'file1;file2',
             'FunctionOrder': 'a_file_name',
             'GenerateDebugInformation': 'true',
             'GenerateMapFile': 'true',
             'HeapCommitSize': 'a string1',
             'HeapReserveSize': 'a string1',
             'IgnoreAllDefaultLibraries': 'true',
             'IgnoreEmbeddedIDL': 'true',
             'IgnoreSpecificDefaultLibraries': 'a_file_list',
             'ImageHasSafeExceptionHandlers': 'true',
             'ImportLibrary': 'a_file_name',
             'KeyContainer': 'a_file_name',
             'KeyFile': 'a_file_name',
             'LargeAddressAware': 'false',
             'LinkDLL': 'true',
             'LinkErrorReporting': 'SendErrorReport',
             'LinkStatus': 'true',
             'LinkTimeCodeGeneration': 'UseLinkTimeCodeGeneration',
             'ManifestFile': 'a_file_name',
             'MapExports': 'true',
             'MapFileName': 'a_file_name',
             'MergedIDLBaseFileName': 'a_file_name',
             'MergeSections': 'a string1',
             'MidlCommandFile': 'a_file_name',
             'MinimumRequiredVersion': 'a string1',
             'ModuleDefinitionFile': 'a_file_name',
             'MSDOSStubFileName': 'a_file_name',
             'NoEntryPoint': 'true',
             'OptimizeReferences': 'false',
             'OutputFile': 'a_file_name',
             'PerUserRedirection': 'true',
             'PreventDllBinding': 'true',
             'Profile': 'true',
             'ProfileGuidedDatabase': 'a_file_name',
             'ProgramDatabaseFile': 'a_file_name',
             'RandomizedBaseAddress': 'false',
             'RegisterOutput': 'true',
             'SectionAlignment': '33',
             'SetChecksum': 'true',
             'ShowProgress': 'LinkVerboseREF',
             'SpecifySectionAttributes': 'a string1',
             'StackCommitSize': 'a string1',
             'StackReserveSize': 'a string1',
             'StripPrivateSymbols': 'a_file_name',
             'SubSystem': 'Console',
             'SupportNobindOfDelayLoadedDLL': 'true',
             'SupportUnloadOfDelayLoadedDLL': 'true',
             'SuppressStartupBanner': 'true',
             'SwapRunFromCD': 'true',
             'SwapRunFromNET': 'true',
             'TargetMachine': 'MachineX86',
             'TerminalServerAware': 'false',
             'TrackerLogDirectory': 'a_folder',
             'TreatLinkerWarningAsErrors': 'true',
             'TurnOffAssemblyGeneration': 'true',
             'TypeLibraryFile': 'a_file_name',
             'TypeLibraryResourceID': '33',
             'UACExecutionLevel': 'AsInvoker',
             'UACUIAccess': 'true',
             'Version': 'a string1'},
         'ResourceCompile': {
             'AdditionalIncludeDirectories': 'folder1;folder2',
             'AdditionalOptions': 'a string1',
             'Culture': '0x236',
             'IgnoreStandardIncludePath': 'true',
             'NullTerminateStrings': 'true',
             'PreprocessorDefinitions': 'string1;string2',
             'ResourceOutputFileName': 'a string1',
             'ShowProgress': 'true',
             'SuppressStartupBanner': 'true',
             'TrackerLogDirectory': 'a_folder',
             'UndefinePreprocessorDefinitions': 'string1;string2'},
         'Midl': {
             'AdditionalIncludeDirectories': 'folder1;folder2',
             'AdditionalOptions': 'a string1',
             'ApplicationConfigurationMode': 'true',
             'ClientStubFile': 'a_file_name',
             'CPreprocessOptions': 'a string1',
             'DefaultCharType': 'Signed',
             'DllDataFileName': 'a_file_name',
             'EnableErrorChecks': 'EnableCustom',
             'ErrorCheckAllocations': 'true',
             'ErrorCheckBounds': 'true',
             'ErrorCheckEnumRange': 'true',
             'ErrorCheckRefPointers': 'true',
             'ErrorCheckStubData': 'true',
             'GenerateClientFiles': 'Stub',
             'GenerateServerFiles': 'None',
             'GenerateStublessProxies': 'true',
             'GenerateTypeLibrary': 'true',
             'HeaderFileName': 'a_file_name',
             'IgnoreStandardIncludePath': 'true',
             'InterfaceIdentifierFileName': 'a_file_name',
             'LocaleID': '33',
             'MkTypLibCompatible': 'true',
             'OutputDirectory': 'a string1',
             'PreprocessorDefinitions': 'string1;string2',
             'ProxyFileName': 'a_file_name',
             'RedirectOutputAndErrors': 'a_file_name',
             'ServerStubFile': 'a_file_name',
             'StructMemberAlignment': 'NotSet',
             'SuppressCompilerWarnings': 'true',
             'SuppressStartupBanner': 'true',
             'TargetEnvironment': 'Itanium',
             'TrackerLogDirectory': 'a_folder',
             'TypeLibFormat': 'NewFormat',
             'TypeLibraryName': 'a_file_name',
             'UndefinePreprocessorDefinitions': 'string1;string2',
             'ValidateAllParameters': 'true',
             'WarnAsError': 'true',
             'WarningLevel': '1'},
         'Lib': {
             'AdditionalDependencies': 'file1;file2',
             'AdditionalLibraryDirectories': 'folder1;folder2',
             'AdditionalOptions': 'a string1',
             'DisplayLibrary': 'a string1',
             'ErrorReporting': 'PromptImmediately',
             'ExportNamedFunctions': 'string1;string2',
             'ForceSymbolReferences': 'a string1',
             'IgnoreAllDefaultLibraries': 'true',
             'IgnoreSpecificDefaultLibraries': 'file1;file2',
             'LinkTimeCodeGeneration': 'true',
             'MinimumRequiredVersion': 'a string1',
             'ModuleDefinitionFile': 'a_file_name',
             'Name': 'a_file_name',
             'OutputFile': 'a_file_name',
             'RemoveObjects': 'file1;file2',
             'SubSystem': 'Console',
             'SuppressStartupBanner': 'true',
             'TargetMachine': 'MachineX86i',
             'TrackerLogDirectory': 'a_folder',
             'TreatLibWarningAsErrors': 'true',
             'UseUnicodeResponseFiles': 'true',
             'Verbose': 'true'},
         'Manifest': {
             'AdditionalManifestFiles': 'file1;file2',
             'AdditionalOptions': 'a string1',
             'AssemblyIdentity': 'a string1',
             'ComponentFileName': 'a_file_name',
             'EnableDPIAwareness': 'fal',
             'GenerateCatalogFiles': 'truel',
             'GenerateCategoryTags': 'true',
             'InputResourceManifests': 'a string1',
             'ManifestFromManagedAssembly': 'a_file_name',
             'notgood3': 'bogus',
             'OutputManifestFile': 'a_file_name',
             'OutputResourceManifests': 'a string1',
             'RegistrarScriptFile': 'a_file_name',
             'ReplacementsFile': 'a_file_name',
             'SuppressDependencyElement': 'true',
             'SuppressStartupBanner': 'true',
             'TrackerLogDirectory': 'a_folder',
             'TypeLibraryFile': 'a_file_name',
             'UpdateFileHashes': 'true',
             'UpdateFileHashesSearchPath': 'a_file_name',
             'VerboseOutput': 'true'},
         'ProjectReference': {
             'LinkLibraryDependencies': 'true',
             'UseLibraryDependencyInputs': 'true'},
         'ManifestResourceCompile': {
             'ResourceOutputFileName': 'a_file_name'},
         '': {
             'EmbedManifest': 'true',
             'GenerateManifest': 'true',
             'IgnoreImportLibrary': 'true',
             'LinkIncremental': 'false'}},
        self.stderr)
    self._ExpectedWarnings([
        'Warning: unrecognized setting ClCompile/Enableprefast',
        'Warning: unrecognized setting ClCompile/ZZXYZ',
        'Warning: unrecognized setting Manifest/notgood3',
        'Warning: for Manifest/GenerateCatalogFiles, '
        "expected bool; got 'truel'",
        'Warning: for Lib/TargetMachine, unrecognized enumerated value '
        'MachineX86i',
        "Warning: for Manifest/EnableDPIAwareness, expected bool; got 'fal'"])

  def testConvertToMSBuildSettings_empty(self):
    """Tests an empty conversion."""
    msvs_settings = {}
    expected_msbuild_settings = {}
    actual_msbuild_settings = MSVSSettings.ConvertToMSBuildSettings(
        msvs_settings,
        self.stderr)
    self.assertEqual(expected_msbuild_settings, actual_msbuild_settings)
    self._ExpectedWarnings([])

  def testConvertToMSBuildSettings_minimal(self):
    """Tests a minimal conversion."""
    msvs_settings = {
        'VCCLCompilerTool': {
            'AdditionalIncludeDirectories': 'dir1',
            'AdditionalOptions': '/foo',
            'BasicRuntimeChecks': '0',
            },
        'VCLinkerTool': {
            'LinkTimeCodeGeneration': '1',
            'ErrorReporting': '1',
            'DataExecutionPrevention': '2',
            },
        }
    expected_msbuild_settings = {
        'ClCompile': {
            'AdditionalIncludeDirectories': 'dir1',
            'AdditionalOptions': '/foo',
            'BasicRuntimeChecks': 'Default',
            },
        'Link': {
            'LinkTimeCodeGeneration': 'UseLinkTimeCodeGeneration',
            'LinkErrorReporting': 'PromptImmediately',
            'DataExecutionPrevention': 'true',
            },
        }
    actual_msbuild_settings = MSVSSettings.ConvertToMSBuildSettings(
        msvs_settings,
        self.stderr)
    self.assertEqual(expected_msbuild_settings, actual_msbuild_settings)
    self._ExpectedWarnings([])

  def testConvertToMSBuildSettings_warnings(self):
    """Tests conversion that generates warnings."""
    msvs_settings = {
        'VCCLCompilerTool': {
            'AdditionalIncludeDirectories': '1',
            'AdditionalOptions': '2',
            # These are incorrect values:
            'BasicRuntimeChecks': '12',
            'BrowseInformation': '21',
            'UsePrecompiledHeader': '13',
            'GeneratePreprocessedFile': '14'},
        'VCLinkerTool': {
            # These are incorrect values:
            'Driver': '10',
            'LinkTimeCodeGeneration': '31',
            'ErrorReporting': '21',
            'FixedBaseAddress': '6'},
        'VCResourceCompilerTool': {
            # Custom
            'Culture': '1003'}}
    expected_msbuild_settings = {
        'ClCompile': {
            'AdditionalIncludeDirectories': '1',
            'AdditionalOptions': '2'},
        'Link': {},
        'ResourceCompile': {
            # Custom
            'Culture': '0x03eb'}}
    actual_msbuild_settings = MSVSSettings.ConvertToMSBuildSettings(
        msvs_settings,
        self.stderr)
    self.assertEqual(expected_msbuild_settings, actual_msbuild_settings)
    self._ExpectedWarnings([
        'Warning: while converting VCCLCompilerTool/BasicRuntimeChecks to '
        'MSBuild, index value (12) not in expected range [0, 4)',
        'Warning: while converting VCCLCompilerTool/BrowseInformation to '
        'MSBuild, index value (21) not in expected range [0, 3)',
        'Warning: while converting VCCLCompilerTool/UsePrecompiledHeader to '
        'MSBuild, index value (13) not in expected range [0, 3)',
        'Warning: while converting VCCLCompilerTool/GeneratePreprocessedFile to '
        'MSBuild, value must be one of [0, 1, 2]; got 14',

        'Warning: while converting VCLinkerTool/Driver to '
        'MSBuild, index value (10) not in expected range [0, 4)',
        'Warning: while converting VCLinkerTool/LinkTimeCodeGeneration to '
        'MSBuild, index value (31) not in expected range [0, 5)',
        'Warning: while converting VCLinkerTool/ErrorReporting to '
        'MSBuild, index value (21) not in expected range [0, 3)',
        'Warning: while converting VCLinkerTool/FixedBaseAddress to '
        'MSBuild, index value (6) not in expected range [0, 3)',
        ])

  def testConvertToMSBuildSettings_full_synthetic(self):
    """Tests conversion of all the MSBuild settings."""
    msvs_settings = {
        'VCCLCompilerTool': {
            'AdditionalIncludeDirectories': 'folder1;folder2;folder3',
            'AdditionalOptions': 'a_string',
            'AdditionalUsingDirectories': 'folder1;folder2;folder3',
            'AssemblerListingLocation': 'a_file_name',
            'AssemblerOutput': '0',
            'BasicRuntimeChecks': '1',
            'BrowseInformation': '2',
            'BrowseInformationFile': 'a_file_name',
            'BufferSecurityCheck': 'true',
            'CallingConvention': '0',
            'CompileAs': '1',
            'DebugInformationFormat': '4',
            'DefaultCharIsUnsigned': 'true',
            'Detect64BitPortabilityProblems': 'true',
            'DisableLanguageExtensions': 'true',
            'DisableSpecificWarnings': 'd1;d2;d3',
            'EnableEnhancedInstructionSet': '0',
            'EnableFiberSafeOptimizations': 'true',
            'EnableFunctionLevelLinking': 'true',
            'EnableIntrinsicFunctions': 'true',
            'EnablePREfast': 'true',
            'ErrorReporting': '1',
            'ExceptionHandling': '2',
            'ExpandAttributedSource': 'true',
            'FavorSizeOrSpeed': '0',
            'FloatingPointExceptions': 'true',
            'FloatingPointModel': '1',
            'ForceConformanceInForLoopScope': 'true',
            'ForcedIncludeFiles': 'file1;file2;file3',
            'ForcedUsingFiles': 'file1;file2;file3',
            'GeneratePreprocessedFile': '1',
            'GenerateXMLDocumentationFiles': 'true',
            'IgnoreStandardIncludePath': 'true',
            'InlineFunctionExpansion': '2',
            'KeepComments': 'true',
            'MinimalRebuild': 'true',
            'ObjectFile': 'a_file_name',
            'OmitDefaultLibName': 'true',
            'OmitFramePointers': 'true',
            'OpenMP': 'true',
            'Optimization': '3',
            'PrecompiledHeaderFile': 'a_file_name',
            'PrecompiledHeaderThrough': 'a_file_name',
            'PreprocessorDefinitions': 'd1;d2;d3',
            'ProgramDataBaseFileName': 'a_file_name',
            'RuntimeLibrary': '0',
            'RuntimeTypeInfo': 'true',
            'ShowIncludes': 'true',
            'SmallerTypeCheck': 'true',
            'StringPooling': 'true',
            'StructMemberAlignment': '1',
            'SuppressStartupBanner': 'true',
            'TreatWChar_tAsBuiltInType': 'true',
            'UndefineAllPreprocessorDefinitions': 'true',
            'UndefinePreprocessorDefinitions': 'd1;d2;d3',
            'UseFullPaths': 'true',
            'UsePrecompiledHeader': '1',
            'UseUnicodeResponseFiles': 'true',
            'WarnAsError': 'true',
            'WarningLevel': '2',
            'WholeProgramOptimization': 'true',
            'XMLDocumentationFileName': 'a_file_name'},
        'VCLinkerTool': {
            'AdditionalDependencies': 'file1;file2;file3',
            'AdditionalLibraryDirectories': 'folder1;folder2;folder3',
            'AdditionalLibraryDirectories_excluded': 'folder1;folder2;folder3',
            'AdditionalManifestDependencies': 'file1;file2;file3',
            'AdditionalOptions': 'a_string',
            'AddModuleNamesToAssembly': 'file1;file2;file3',
            'AllowIsolation': 'true',
            'AssemblyDebug': '0',
            'AssemblyLinkResource': 'file1;file2;file3',
            'BaseAddress': 'a_string',
            'CLRImageType': '1',
            'CLRThreadAttribute': '2',
            'CLRUnmanagedCodeCheck': 'true',
            'DataExecutionPrevention': '0',
            'DelayLoadDLLs': 'file1;file2;file3',
            'DelaySign': 'true',
            'Driver': '1',
            'EmbedManagedResourceFile': 'file1;file2;file3',
            'EnableCOMDATFolding': '0',
            'EnableUAC': 'true',
            'EntryPointSymbol': 'a_string',
            'ErrorReporting': '0',
            'FixedBaseAddress': '1',
            'ForceSymbolReferences': 'file1;file2;file3',
            'FunctionOrder': 'a_file_name',
            'GenerateDebugInformation': 'true',
            'GenerateManifest': 'true',
            'GenerateMapFile': 'true',
            'HeapCommitSize': 'a_string',
            'HeapReserveSize': 'a_string',
            'IgnoreAllDefaultLibraries': 'true',
            'IgnoreDefaultLibraryNames': 'file1;file2;file3',
            'IgnoreEmbeddedIDL': 'true',
            'IgnoreImportLibrary': 'true',
            'ImportLibrary': 'a_file_name',
            'KeyContainer': 'a_file_name',
            'KeyFile': 'a_file_name',
            'LargeAddressAware': '2',
            'LinkIncremental': '1',
            'LinkLibraryDependencies': 'true',
            'LinkTimeCodeGeneration': '2',
            'ManifestFile': 'a_file_name',
            'MapExports': 'true',
            'MapFileName': 'a_file_name',
            'MergedIDLBaseFileName': 'a_file_name',
            'MergeSections': 'a_string',
            'MidlCommandFile': 'a_file_name',
            'ModuleDefinitionFile': 'a_file_name',
            'OptimizeForWindows98': '1',
            'OptimizeReferences': '0',
            'OutputFile': 'a_file_name',
            'PerUserRedirection': 'true',
            'Profile': 'true',
            'ProfileGuidedDatabase': 'a_file_name',
            'ProgramDatabaseFile': 'a_file_name',
            'RandomizedBaseAddress': '1',
            'RegisterOutput': 'true',
            'ResourceOnlyDLL': 'true',
            'SetChecksum': 'true',
            'ShowProgress': '0',
            'StackCommitSize': 'a_string',
            'StackReserveSize': 'a_string',
            'StripPrivateSymbols': 'a_file_name',
            'SubSystem': '2',
            'SupportUnloadOfDelayLoadedDLL': 'true',
            'SuppressStartupBanner': 'true',
            'SwapRunFromCD': 'true',
            'SwapRunFromNet': 'true',
            'TargetMachine': '3',
            'TerminalServerAware': '2',
            'TurnOffAssemblyGeneration': 'true',
            'TypeLibraryFile': 'a_file_name',
            'TypeLibraryResourceID': '33',
            'UACExecutionLevel': '1',
            'UACUIAccess': 'true',
            'UseLibraryDependencyInputs': 'false',
            'UseUnicodeResponseFiles': 'true',
            'Version': 'a_string'},
        'VCResourceCompilerTool': {
            'AdditionalIncludeDirectories': 'folder1;folder2;folder3',
            'AdditionalOptions': 'a_string',
            'Culture': '1003',
            'IgnoreStandardIncludePath': 'true',
            'PreprocessorDefinitions': 'd1;d2;d3',
            'ResourceOutputFileName': 'a_string',
            'ShowProgress': 'true',
            'SuppressStartupBanner': 'true',
            'UndefinePreprocessorDefinitions': 'd1;d2;d3'},
        'VCMIDLTool': {
            'AdditionalIncludeDirectories': 'folder1;folder2;folder3',
            'AdditionalOptions': 'a_string',
            'CPreprocessOptions': 'a_string',
            'DefaultCharType': '0',
            'DLLDataFileName': 'a_file_name',
            'EnableErrorChecks': '2',
            'ErrorCheckAllocations': 'true',
            'ErrorCheckBounds': 'true',
            'ErrorCheckEnumRange': 'true',
            'ErrorCheckRefPointers': 'true',
            'ErrorCheckStubData': 'true',
            'GenerateStublessProxies': 'true',
            'GenerateTypeLibrary': 'true',
            'HeaderFileName': 'a_file_name',
            'IgnoreStandardIncludePath': 'true',
            'InterfaceIdentifierFileName': 'a_file_name',
            'MkTypLibCompatible': 'true',
            'OutputDirectory': 'a_string',
            'PreprocessorDefinitions': 'd1;d2;d3',
            'ProxyFileName': 'a_file_name',
            'RedirectOutputAndErrors': 'a_file_name',
            'StructMemberAlignment': '3',
            'SuppressStartupBanner': 'true',
            'TargetEnvironment': '1',
            'TypeLibraryName': 'a_file_name',
            'UndefinePreprocessorDefinitions': 'd1;d2;d3',
            'ValidateParameters': 'true',
            'WarnAsError': 'true',
            'WarningLevel': '4'},
        'VCLibrarianTool': {
            'AdditionalDependencies': 'file1;file2;file3',
            'AdditionalLibraryDirectories': 'folder1;folder2;folder3',
            'AdditionalLibraryDirectories_excluded': 'folder1;folder2;folder3',
            'AdditionalOptions': 'a_string',
            'ExportNamedFunctions': 'd1;d2;d3',
            'ForceSymbolReferences': 'a_string',
            'IgnoreAllDefaultLibraries': 'true',
            'IgnoreSpecificDefaultLibraries': 'file1;file2;file3',
            'LinkLibraryDependencies': 'true',
            'ModuleDefinitionFile': 'a_file_name',
            'OutputFile': 'a_file_name',
            'SuppressStartupBanner': 'true',
            'UseUnicodeResponseFiles': 'true'},
        'VCManifestTool': {
            'AdditionalManifestFiles': 'file1;file2;file3',
            'AdditionalOptions': 'a_string',
            'AssemblyIdentity': 'a_string',
            'ComponentFileName': 'a_file_name',
            'DependencyInformationFile': 'a_file_name',
            'EmbedManifest': 'true',
            'GenerateCatalogFiles': 'true',
            'InputResourceManifests': 'a_string',
            'ManifestResourceFile': 'my_name',
            'OutputManifestFile': 'a_file_name',
            'RegistrarScriptFile': 'a_file_name',
            'ReplacementsFile': 'a_file_name',
            'SuppressStartupBanner': 'true',
            'TypeLibraryFile': 'a_file_name',
            'UpdateFileHashes': 'true',
            'UpdateFileHashesSearchPath': 'a_file_name',
            'UseFAT32Workaround': 'true',
            'UseUnicodeResponseFiles': 'true',
            'VerboseOutput': 'true'}}
    expected_msbuild_settings = {
        'ClCompile': {
            'AdditionalIncludeDirectories': 'folder1;folder2;folder3',
            'AdditionalOptions': 'a_string /J',
            'AdditionalUsingDirectories': 'folder1;folder2;folder3',
            'AssemblerListingLocation': 'a_file_name',
            'AssemblerOutput': 'NoListing',
            'BasicRuntimeChecks': 'StackFrameRuntimeCheck',
            'BrowseInformation': 'true',
            'BrowseInformationFile': 'a_file_name',
            'BufferSecurityCheck': 'true',
            'CallingConvention': 'Cdecl',
            'CompileAs': 'CompileAsC',
            'DebugInformationFormat': 'EditAndContinue',
            'DisableLanguageExtensions': 'true',
            'DisableSpecificWarnings': 'd1;d2;d3',
            'EnableEnhancedInstructionSet': 'NotSet',
            'EnableFiberSafeOptimizations': 'true',
            'EnablePREfast': 'true',
            'ErrorReporting': 'Prompt',
            'ExceptionHandling': 'Async',
            'ExpandAttributedSource': 'true',
            'FavorSizeOrSpeed': 'Neither',
            'FloatingPointExceptions': 'true',
            'FloatingPointModel': 'Strict',
            'ForceConformanceInForLoopScope': 'true',
            'ForcedIncludeFiles': 'file1;file2;file3',
            'ForcedUsingFiles': 'file1;file2;file3',
            'FunctionLevelLinking': 'true',
            'GenerateXMLDocumentationFiles': 'true',
            'IgnoreStandardIncludePath': 'true',
            'InlineFunctionExpansion': 'AnySuitable',
            'IntrinsicFunctions': 'true',
            'MinimalRebuild': 'true',
            'ObjectFileName': 'a_file_name',
            'OmitDefaultLibName': 'true',
            'OmitFramePointers': 'true',
            'OpenMPSupport': 'true',
            'Optimization': 'Full',
            'PrecompiledHeader': 'Create',
            'PrecompiledHeaderFile': 'a_file_name',
            'PrecompiledHeaderOutputFile': 'a_file_name',
            'PreprocessKeepComments': 'true',
            'PreprocessorDefinitions': 'd1;d2;d3',
            'PreprocessSuppressLineNumbers': 'false',
            'PreprocessToFile': 'true',
            'ProgramDataBaseFileName': 'a_file_name',
            'RuntimeLibrary': 'MultiThreaded',
            'RuntimeTypeInfo': 'true',
            'ShowIncludes': 'true',
            'SmallerTypeCheck': 'true',
            'StringPooling': 'true',
            'StructMemberAlignment': '1Byte',
            'SuppressStartupBanner': 'true',
            'TreatWarningAsError': 'true',
            'TreatWChar_tAsBuiltInType': 'true',
            'UndefineAllPreprocessorDefinitions': 'true',
            'UndefinePreprocessorDefinitions': 'd1;d2;d3',
            'UseFullPaths': 'true',
            'WarningLevel': 'Level2',
            'WholeProgramOptimization': 'true',
            'XMLDocumentationFileName': 'a_file_name'},
        'Link': {
            'AdditionalDependencies': 'file1;file2;file3',
            'AdditionalLibraryDirectories': 'folder1;folder2;folder3',
            'AdditionalManifestDependencies': 'file1;file2;file3',
            'AdditionalOptions': 'a_string',
            'AddModuleNamesToAssembly': 'file1;file2;file3',
            'AllowIsolation': 'true',
            'AssemblyDebug': '',
            'AssemblyLinkResource': 'file1;file2;file3',
            'BaseAddress': 'a_string',
            'CLRImageType': 'ForceIJWImage',
            'CLRThreadAttribute': 'STAThreadingAttribute',
            'CLRUnmanagedCodeCheck': 'true',
            'DataExecutionPrevention': '',
            'DelayLoadDLLs': 'file1;file2;file3',
            'DelaySign': 'true',
            'Driver': 'Driver',
            'EmbedManagedResourceFile': 'file1;file2;file3',
            'EnableCOMDATFolding': '',
            'EnableUAC': 'true',
            'EntryPointSymbol': 'a_string',
            'FixedBaseAddress': 'false',
            'ForceSymbolReferences': 'file1;file2;file3',
            'FunctionOrder': 'a_file_name',
            'GenerateDebugInformation': 'true',
            'GenerateMapFile': 'true',
            'HeapCommitSize': 'a_string',
            'HeapReserveSize': 'a_string',
            'IgnoreAllDefaultLibraries': 'true',
            'IgnoreEmbeddedIDL': 'true',
            'IgnoreSpecificDefaultLibraries': 'file1;file2;file3',
            'ImportLibrary': 'a_file_name',
            'KeyContainer': 'a_file_name',
            'KeyFile': 'a_file_name',
            'LargeAddressAware': 'true',
            'LinkErrorReporting': 'NoErrorReport',
            'LinkTimeCodeGeneration': 'PGInstrument',
            'ManifestFile': 'a_file_name',
            'MapExports': 'true',
            'MapFileName': 'a_file_name',
            'MergedIDLBaseFileName': 'a_file_name',
            'MergeSections': 'a_string',
            'MidlCommandFile': 'a_file_name',
            'ModuleDefinitionFile': 'a_file_name',
            'NoEntryPoint': 'true',
            'OptimizeReferences': '',
            'OutputFile': 'a_file_name',
            'PerUserRedirection': 'true',
            'Profile': 'true',
            'ProfileGuidedDatabase': 'a_file_name',
            'ProgramDatabaseFile': 'a_file_name',
            'RandomizedBaseAddress': 'false',
            'RegisterOutput': 'true',
            'SetChecksum': 'true',
            'ShowProgress': 'NotSet',
            'StackCommitSize': 'a_string',
            'StackReserveSize': 'a_string',
            'StripPrivateSymbols': 'a_file_name',
            'SubSystem': 'Windows',
            'SupportUnloadOfDelayLoadedDLL': 'true',
            'SuppressStartupBanner': 'true',
            'SwapRunFromCD': 'true',
            'SwapRunFromNET': 'true',
            'TargetMachine': 'MachineARM',
            'TerminalServerAware': 'true',
            'TurnOffAssemblyGeneration': 'true',
            'TypeLibraryFile': 'a_file_name',
            'TypeLibraryResourceID': '33',
            'UACExecutionLevel': 'HighestAvailable',
            'UACUIAccess': 'true',
            'Version': 'a_string'},
        'ResourceCompile': {
            'AdditionalIncludeDirectories': 'folder1;folder2;folder3',
            'AdditionalOptions': 'a_string',
            'Culture': '0x03eb',
            'IgnoreStandardIncludePath': 'true',
            'PreprocessorDefinitions': 'd1;d2;d3',
            'ResourceOutputFileName': 'a_string',
            'ShowProgress': 'true',
            'SuppressStartupBanner': 'true',
            'UndefinePreprocessorDefinitions': 'd1;d2;d3'},
        'Midl': {
            'AdditionalIncludeDirectories': 'folder1;folder2;folder3',
            'AdditionalOptions': 'a_string',
            'CPreprocessOptions': 'a_string',
            'DefaultCharType': 'Unsigned',
            'DllDataFileName': 'a_file_name',
            'EnableErrorChecks': 'All',
            'ErrorCheckAllocations': 'true',
            'ErrorCheckBounds': 'true',
            'ErrorCheckEnumRange': 'true',
            'ErrorCheckRefPointers': 'true',
            'ErrorCheckStubData': 'true',
            'GenerateStublessProxies': 'true',
            'GenerateTypeLibrary': 'true',
            'HeaderFileName': 'a_file_name',
            'IgnoreStandardIncludePath': 'true',
            'InterfaceIdentifierFileName': 'a_file_name',
            'MkTypLibCompatible': 'true',
            'OutputDirectory': 'a_string',
            'PreprocessorDefinitions': 'd1;d2;d3',
            'ProxyFileName': 'a_file_name',
            'RedirectOutputAndErrors': 'a_file_name',
            'StructMemberAlignment': '4',
            'SuppressStartupBanner': 'true',
            'TargetEnvironment': 'Win32',
            'TypeLibraryName': 'a_file_name',
            'UndefinePreprocessorDefinitions': 'd1;d2;d3',
            'ValidateAllParameters': 'true',
            'WarnAsError': 'true',
            'WarningLevel': '4'},
        'Lib': {
            'AdditionalDependencies': 'file1;file2;file3',
            'AdditionalLibraryDirectories': 'folder1;folder2;folder3',
            'AdditionalOptions': 'a_string',
            'ExportNamedFunctions': 'd1;d2;d3',
            'ForceSymbolReferences': 'a_string',
            'IgnoreAllDefaultLibraries': 'true',
            'IgnoreSpecificDefaultLibraries': 'file1;file2;file3',
            'ModuleDefinitionFile': 'a_file_name',
            'OutputFile': 'a_file_name',
            'SuppressStartupBanner': 'true',
            'UseUnicodeResponseFiles': 'true'},
        'Manifest': {
            'AdditionalManifestFiles': 'file1;file2;file3',
            'AdditionalOptions': 'a_string',
            'AssemblyIdentity': 'a_string',
            'ComponentFileName': 'a_file_name',
            'GenerateCatalogFiles': 'true',
            'InputResourceManifests': 'a_string',
            'OutputManifestFile': 'a_file_name',
            'RegistrarScriptFile': 'a_file_name',
            'ReplacementsFile': 'a_file_name',
            'SuppressStartupBanner': 'true',
            'TypeLibraryFile': 'a_file_name',
            'UpdateFileHashes': 'true',
            'UpdateFileHashesSearchPath': 'a_file_name',
            'VerboseOutput': 'true'},
        'ManifestResourceCompile': {
            'ResourceOutputFileName': 'my_name'},
        'ProjectReference': {
            'LinkLibraryDependencies': 'true',
            'UseLibraryDependencyInputs': 'false'},
        '': {
            'EmbedManifest': 'true',
            'GenerateManifest': 'true',
            'IgnoreImportLibrary': 'true',
            'LinkIncremental': 'false'}}
    actual_msbuild_settings = MSVSSettings.ConvertToMSBuildSettings(
        msvs_settings,
        self.stderr)
    self.assertEqual(expected_msbuild_settings, actual_msbuild_settings)
    self._ExpectedWarnings([])

  def testConvertToMSBuildSettings_actual(self):
    """Tests the conversion of an actual project.

    A VS2008 project with most of the options defined was created through the
    VS2008 IDE.  It was then converted to VS2010.  The tool settings found in
    the .vcproj and .vcxproj files were converted to the two dictionaries
    msvs_settings and expected_msbuild_settings.

    Note that for many settings, the VS2010 converter adds macros like
    %(AdditionalIncludeDirectories) to make sure than inherited values are
    included.  Since the Gyp projects we generate do not use inheritance,
    we removed these macros.  They were:
        ClCompile:
            AdditionalIncludeDirectories:  ';%(AdditionalIncludeDirectories)'
            AdditionalOptions:  ' %(AdditionalOptions)'
            AdditionalUsingDirectories:  ';%(AdditionalUsingDirectories)'
            DisableSpecificWarnings: ';%(DisableSpecificWarnings)',
            ForcedIncludeFiles:  ';%(ForcedIncludeFiles)',
            ForcedUsingFiles:  ';%(ForcedUsingFiles)',
            PreprocessorDefinitions:  ';%(PreprocessorDefinitions)',
            UndefinePreprocessorDefinitions:
                ';%(UndefinePreprocessorDefinitions)',
        Link:
            AdditionalDependencies:  ';%(AdditionalDependencies)',
            AdditionalLibraryDirectories:  ';%(AdditionalLibraryDirectories)',
            AdditionalManifestDependencies:
                ';%(AdditionalManifestDependencies)',
            AdditionalOptions:  ' %(AdditionalOptions)',
            AddModuleNamesToAssembly:  ';%(AddModuleNamesToAssembly)',
            AssemblyLinkResource:  ';%(AssemblyLinkResource)',
            DelayLoadDLLs:  ';%(DelayLoadDLLs)',
            EmbedManagedResourceFile:  ';%(EmbedManagedResourceFile)',
            ForceSymbolReferences:  ';%(ForceSymbolReferences)',
            IgnoreSpecificDefaultLibraries:
                ';%(IgnoreSpecificDefaultLibraries)',
        ResourceCompile:
            AdditionalIncludeDirectories:  ';%(AdditionalIncludeDirectories)',
            AdditionalOptions:  ' %(AdditionalOptions)',
            PreprocessorDefinitions:  ';%(PreprocessorDefinitions)',
        Manifest:
            AdditionalManifestFiles:  ';%(AdditionalManifestFiles)',
            AdditionalOptions:  ' %(AdditionalOptions)',
            InputResourceManifests:  ';%(InputResourceManifests)',
    """
    msvs_settings = {
        'VCCLCompilerTool': {
            'AdditionalIncludeDirectories': 'dir1',
            'AdditionalOptions': '/more',
            'AdditionalUsingDirectories': 'test',
            'AssemblerListingLocation': '$(IntDir)\\a',
            'AssemblerOutput': '1',
            'BasicRuntimeChecks': '3',
            'BrowseInformation': '1',
            'BrowseInformationFile': '$(IntDir)\\e',
            'BufferSecurityCheck': 'false',
            'CallingConvention': '1',
            'CompileAs': '1',
            'DebugInformationFormat': '4',
            'DefaultCharIsUnsigned': 'true',
            'Detect64BitPortabilityProblems': 'true',
            'DisableLanguageExtensions': 'true',
            'DisableSpecificWarnings': 'abc',
            'EnableEnhancedInstructionSet': '1',
            'EnableFiberSafeOptimizations': 'true',
            'EnableFunctionLevelLinking': 'true',
            'EnableIntrinsicFunctions': 'true',
            'EnablePREfast': 'true',
            'ErrorReporting': '2',
            'ExceptionHandling': '2',
            'ExpandAttributedSource': 'true',
            'FavorSizeOrSpeed': '2',
            'FloatingPointExceptions': 'true',
            'FloatingPointModel': '1',
            'ForceConformanceInForLoopScope': 'false',
            'ForcedIncludeFiles': 'def',
            'ForcedUsingFiles': 'ge',
            'GeneratePreprocessedFile': '2',
            'GenerateXMLDocumentationFiles': 'true',
            'IgnoreStandardIncludePath': 'true',
            'InlineFunctionExpansion': '1',
            'KeepComments': 'true',
            'MinimalRebuild': 'true',
            'ObjectFile': '$(IntDir)\\b',
            'OmitDefaultLibName': 'true',
            'OmitFramePointers': 'true',
            'OpenMP': 'true',
            'Optimization': '3',
            'PrecompiledHeaderFile': '$(IntDir)\\$(TargetName).pche',
            'PrecompiledHeaderThrough': 'StdAfx.hd',
            'PreprocessorDefinitions': 'WIN32;_DEBUG;_CONSOLE',
            'ProgramDataBaseFileName': '$(IntDir)\\vc90b.pdb',
            'RuntimeLibrary': '3',
            'RuntimeTypeInfo': 'false',
            'ShowIncludes': 'true',
            'SmallerTypeCheck': 'true',
            'StringPooling': 'true',
            'StructMemberAlignment': '3',
            'SuppressStartupBanner': 'false',
            'TreatWChar_tAsBuiltInType': 'false',
            'UndefineAllPreprocessorDefinitions': 'true',
            'UndefinePreprocessorDefinitions': 'wer',
            'UseFullPaths': 'true',
            'UsePrecompiledHeader': '0',
            'UseUnicodeResponseFiles': 'false',
            'WarnAsError': 'true',
            'WarningLevel': '3',
            'WholeProgramOptimization': 'true',
            'XMLDocumentationFileName': '$(IntDir)\\c'},
        'VCLinkerTool': {
            'AdditionalDependencies': 'zx',
            'AdditionalLibraryDirectories': 'asd',
            'AdditionalManifestDependencies': 's2',
            'AdditionalOptions': '/mor2',
            'AddModuleNamesToAssembly': 'd1',
            'AllowIsolation': 'false',
            'AssemblyDebug': '1',
            'AssemblyLinkResource': 'd5',
            'BaseAddress': '23423',
            'CLRImageType': '3',
            'CLRThreadAttribute': '1',
            'CLRUnmanagedCodeCheck': 'true',
            'DataExecutionPrevention': '0',
            'DelayLoadDLLs': 'd4',
            'DelaySign': 'true',
            'Driver': '2',
            'EmbedManagedResourceFile': 'd2',
            'EnableCOMDATFolding': '1',
            'EnableUAC': 'false',
            'EntryPointSymbol': 'f5',
            'ErrorReporting': '2',
            'FixedBaseAddress': '1',
            'ForceSymbolReferences': 'd3',
            'FunctionOrder': 'fssdfsd',
            'GenerateDebugInformation': 'true',
            'GenerateManifest': 'false',
            'GenerateMapFile': 'true',
            'HeapCommitSize': '13',
            'HeapReserveSize': '12',
            'IgnoreAllDefaultLibraries': 'true',
            'IgnoreDefaultLibraryNames': 'flob;flok',
            'IgnoreEmbeddedIDL': 'true',
            'IgnoreImportLibrary': 'true',
            'ImportLibrary': 'f4',
            'KeyContainer': 'f7',
            'KeyFile': 'f6',
            'LargeAddressAware': '2',
            'LinkIncremental': '0',
            'LinkLibraryDependencies': 'false',
            'LinkTimeCodeGeneration': '1',
            'ManifestFile':
            '$(IntDir)\\$(TargetFileName).2intermediate.manifest',
            'MapExports': 'true',
            'MapFileName': 'd5',
            'MergedIDLBaseFileName': 'f2',
            'MergeSections': 'f5',
            'MidlCommandFile': 'f1',
            'ModuleDefinitionFile': 'sdsd',
            'OptimizeForWindows98': '2',
            'OptimizeReferences': '2',
            'OutputFile': '$(OutDir)\\$(ProjectName)2.exe',
            'PerUserRedirection': 'true',
            'Profile': 'true',
            'ProfileGuidedDatabase': '$(TargetDir)$(TargetName).pgdd',
            'ProgramDatabaseFile': 'Flob.pdb',
            'RandomizedBaseAddress': '1',
            'RegisterOutput': 'true',
            'ResourceOnlyDLL': 'true',
            'SetChecksum': 'false',
            'ShowProgress': '1',
            'StackCommitSize': '15',
            'StackReserveSize': '14',
            'StripPrivateSymbols': 'd3',
            'SubSystem': '1',
            'SupportUnloadOfDelayLoadedDLL': 'true',
            'SuppressStartupBanner': 'false',
            'SwapRunFromCD': 'true',
            'SwapRunFromNet': 'true',
            'TargetMachine': '1',
            'TerminalServerAware': '1',
            'TurnOffAssemblyGeneration': 'true',
            'TypeLibraryFile': 'f3',
            'TypeLibraryResourceID': '12',
            'UACExecutionLevel': '2',
            'UACUIAccess': 'true',
            'UseLibraryDependencyInputs': 'true',
            'UseUnicodeResponseFiles': 'false',
            'Version': '333'},
        'VCResourceCompilerTool': {
            'AdditionalIncludeDirectories': 'f3',
            'AdditionalOptions': '/more3',
            'Culture': '3084',
            'IgnoreStandardIncludePath': 'true',
            'PreprocessorDefinitions': '_UNICODE;UNICODE2',
            'ResourceOutputFileName': '$(IntDir)/$(InputName)3.res',
            'ShowProgress': 'true'},
        'VCManifestTool': {
            'AdditionalManifestFiles': 'sfsdfsd',
            'AdditionalOptions': 'afdsdafsd',
            'AssemblyIdentity': 'sddfdsadfsa',
            'ComponentFileName': 'fsdfds',
            'DependencyInformationFile': '$(IntDir)\\mt.depdfd',
            'EmbedManifest': 'false',
            'GenerateCatalogFiles': 'true',
            'InputResourceManifests': 'asfsfdafs',
            'ManifestResourceFile':
            '$(IntDir)\\$(TargetFileName).embed.manifest.resfdsf',
            'OutputManifestFile': '$(TargetPath).manifestdfs',
            'RegistrarScriptFile': 'sdfsfd',
            'ReplacementsFile': 'sdffsd',
            'SuppressStartupBanner': 'false',
            'TypeLibraryFile': 'sfsd',
            'UpdateFileHashes': 'true',
            'UpdateFileHashesSearchPath': 'sfsd',
            'UseFAT32Workaround': 'true',
            'UseUnicodeResponseFiles': 'false',
            'VerboseOutput': 'true'}}
    expected_msbuild_settings = {
        'ClCompile': {
            'AdditionalIncludeDirectories': 'dir1',
            'AdditionalOptions': '/more /J',
            'AdditionalUsingDirectories': 'test',
            'AssemblerListingLocation': '$(IntDir)a',
            'AssemblerOutput': 'AssemblyCode',
            'BasicRuntimeChecks': 'EnableFastChecks',
            'BrowseInformation': 'true',
            'BrowseInformationFile': '$(IntDir)e',
            'BufferSecurityCheck': 'false',
            'CallingConvention': 'FastCall',
            'CompileAs': 'CompileAsC',
            'DebugInformationFormat': 'EditAndContinue',
            'DisableLanguageExtensions': 'true',
            'DisableSpecificWarnings': 'abc',
            'EnableEnhancedInstructionSet': 'StreamingSIMDExtensions',
            'EnableFiberSafeOptimizations': 'true',
            'EnablePREfast': 'true',
            'ErrorReporting': 'Queue',
            'ExceptionHandling': 'Async',
            'ExpandAttributedSource': 'true',
            'FavorSizeOrSpeed': 'Size',
            'FloatingPointExceptions': 'true',
            'FloatingPointModel': 'Strict',
            'ForceConformanceInForLoopScope': 'false',
            'ForcedIncludeFiles': 'def',
            'ForcedUsingFiles': 'ge',
            'FunctionLevelLinking': 'true',
            'GenerateXMLDocumentationFiles': 'true',
            'IgnoreStandardIncludePath': 'true',
            'InlineFunctionExpansion': 'OnlyExplicitInline',
            'IntrinsicFunctions': 'true',
            'MinimalRebuild': 'true',
            'ObjectFileName': '$(IntDir)b',
            'OmitDefaultLibName': 'true',
            'OmitFramePointers': 'true',
            'OpenMPSupport': 'true',
            'Optimization': 'Full',
            'PrecompiledHeader': 'NotUsing',  # Actual conversion gives ''
            'PrecompiledHeaderFile': 'StdAfx.hd',
            'PrecompiledHeaderOutputFile': '$(IntDir)$(TargetName).pche',
            'PreprocessKeepComments': 'true',
            'PreprocessorDefinitions': 'WIN32;_DEBUG;_CONSOLE',
            'PreprocessSuppressLineNumbers': 'true',
            'PreprocessToFile': 'true',
            'ProgramDataBaseFileName': '$(IntDir)vc90b.pdb',
            'RuntimeLibrary': 'MultiThreadedDebugDLL',
            'RuntimeTypeInfo': 'false',
            'ShowIncludes': 'true',
            'SmallerTypeCheck': 'true',
            'StringPooling': 'true',
            'StructMemberAlignment': '4Bytes',
            'SuppressStartupBanner': 'false',
            'TreatWarningAsError': 'true',
            'TreatWChar_tAsBuiltInType': 'false',
            'UndefineAllPreprocessorDefinitions': 'true',
            'UndefinePreprocessorDefinitions': 'wer',
            'UseFullPaths': 'true',
            'WarningLevel': 'Level3',
            'WholeProgramOptimization': 'true',
            'XMLDocumentationFileName': '$(IntDir)c'},
        'Link': {
            'AdditionalDependencies': 'zx',
            'AdditionalLibraryDirectories': 'asd',
            'AdditionalManifestDependencies': 's2',
            'AdditionalOptions': '/mor2',
            'AddModuleNamesToAssembly': 'd1',
            'AllowIsolation': 'false',
            'AssemblyDebug': 'true',
            'AssemblyLinkResource': 'd5',
            'BaseAddress': '23423',
            'CLRImageType': 'ForceSafeILImage',
            'CLRThreadAttribute': 'MTAThreadingAttribute',
            'CLRUnmanagedCodeCheck': 'true',
            'DataExecutionPrevention': '',
            'DelayLoadDLLs': 'd4',
            'DelaySign': 'true',
            'Driver': 'UpOnly',
            'EmbedManagedResourceFile': 'd2',
            'EnableCOMDATFolding': 'false',
            'EnableUAC': 'false',
            'EntryPointSymbol': 'f5',
            'FixedBaseAddress': 'false',
            'ForceSymbolReferences': 'd3',
            'FunctionOrder': 'fssdfsd',
            'GenerateDebugInformation': 'true',
            'GenerateMapFile': 'true',
            'HeapCommitSize': '13',
            'HeapReserveSize': '12',
            'IgnoreAllDefaultLibraries': 'true',
            'IgnoreEmbeddedIDL': 'true',
            'IgnoreSpecificDefaultLibraries': 'flob;flok',
            'ImportLibrary': 'f4',
            'KeyContainer': 'f7',
            'KeyFile': 'f6',
            'LargeAddressAware': 'true',
            'LinkErrorReporting': 'QueueForNextLogin',
            'LinkTimeCodeGeneration': 'UseLinkTimeCodeGeneration',
            'ManifestFile': '$(IntDir)$(TargetFileName).2intermediate.manifest',
            'MapExports': 'true',
            'MapFileName': 'd5',
            'MergedIDLBaseFileName': 'f2',
            'MergeSections': 'f5',
            'MidlCommandFile': 'f1',
            'ModuleDefinitionFile': 'sdsd',
            'NoEntryPoint': 'true',
            'OptimizeReferences': 'true',
            'OutputFile': '$(OutDir)$(ProjectName)2.exe',
            'PerUserRedirection': 'true',
            'Profile': 'true',
            'ProfileGuidedDatabase': '$(TargetDir)$(TargetName).pgdd',
            'ProgramDatabaseFile': 'Flob.pdb',
            'RandomizedBaseAddress': 'false',
            'RegisterOutput': 'true',
            'SetChecksum': 'false',
            'ShowProgress': 'LinkVerbose',
            'StackCommitSize': '15',
            'StackReserveSize': '14',
            'StripPrivateSymbols': 'd3',
            'SubSystem': 'Console',
            'SupportUnloadOfDelayLoadedDLL': 'true',
            'SuppressStartupBanner': 'false',
            'SwapRunFromCD': 'true',
            'SwapRunFromNET': 'true',
            'TargetMachine': 'MachineX86',
            'TerminalServerAware': 'false',
            'TurnOffAssemblyGeneration': 'true',
            'TypeLibraryFile': 'f3',
            'TypeLibraryResourceID': '12',
            'UACExecutionLevel': 'RequireAdministrator',
            'UACUIAccess': 'true',
            'Version': '333'},
        'ResourceCompile': {
            'AdditionalIncludeDirectories': 'f3',
            'AdditionalOptions': '/more3',
            'Culture': '0x0c0c',
            'IgnoreStandardIncludePath': 'true',
            'PreprocessorDefinitions': '_UNICODE;UNICODE2',
            'ResourceOutputFileName': '$(IntDir)%(Filename)3.res',
            'ShowProgress': 'true'},
        'Manifest': {
            'AdditionalManifestFiles': 'sfsdfsd',
            'AdditionalOptions': 'afdsdafsd',
            'AssemblyIdentity': 'sddfdsadfsa',
            'ComponentFileName': 'fsdfds',
            'GenerateCatalogFiles': 'true',
            'InputResourceManifests': 'asfsfdafs',
            'OutputManifestFile': '$(TargetPath).manifestdfs',
            'RegistrarScriptFile': 'sdfsfd',
            'ReplacementsFile': 'sdffsd',
            'SuppressStartupBanner': 'false',
            'TypeLibraryFile': 'sfsd',
            'UpdateFileHashes': 'true',
            'UpdateFileHashesSearchPath': 'sfsd',
            'VerboseOutput': 'true'},
        'ProjectReference': {
            'LinkLibraryDependencies': 'false',
            'UseLibraryDependencyInputs': 'true'},
        '': {
            'EmbedManifest': 'false',
            'GenerateManifest': 'false',
            'IgnoreImportLibrary': 'true',
            'LinkIncremental': ''
            },
        'ManifestResourceCompile': {
            'ResourceOutputFileName':
            '$(IntDir)$(TargetFileName).embed.manifest.resfdsf'}
        }
    actual_msbuild_settings = MSVSSettings.ConvertToMSBuildSettings(
        msvs_settings,
        self.stderr)
    self.assertEqual(expected_msbuild_settings, actual_msbuild_settings)
    self._ExpectedWarnings([])


if __name__ == '__main__':
  unittest.main()
