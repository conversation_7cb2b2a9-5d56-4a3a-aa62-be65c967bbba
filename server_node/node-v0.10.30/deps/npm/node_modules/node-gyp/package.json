{"name": "node-gyp", "description": "Node.js native addon build tool", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "gyp"], "version": "0.13.1", "installVersion": 9, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tootallnate.net"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-gyp.git"}, "preferGlobal": true, "bin": {"node-gyp": "./bin/node-gyp.js"}, "main": "./lib/node-gyp.js", "dependencies": {"glob": "3 || 4", "graceful-fs": "2||3", "fstream": "0", "minimatch": "0", "mkdirp": "0", "nopt": "2 || 3", "npmlog": "0", "osenv": "0", "request": "2", "rimraf": "2", "semver": "2", "tar": "0", "which": "1"}, "engines": {"node": ">= 0.8.0"}, "bugs": {"url": "https://github.com/TooTallNate/node-gyp/issues"}, "homepage": "https://github.com/TooTallNate/node-gyp", "_id": "node-gyp@0.13.1", "_shasum": "5a484dd2dc13d5b894a8fe781a250c07eae7bffa", "_from": "node-gyp@~0.13.0", "_npmVersion": "1.4.9", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "TooTallNate", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "5a484dd2dc13d5b894a8fe781a250c07eae7bffa", "tarball": "http://registry.npmjs.org/node-gyp/-/node-gyp-0.13.1.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/node-gyp/-/node-gyp-0.13.1.tgz", "readme": "ERROR: No README data found!"}