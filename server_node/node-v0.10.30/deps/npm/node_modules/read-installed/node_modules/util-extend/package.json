{"name": "util-extend", "version": "1.0.1", "description": "Node's internal object extension function", "main": "extend.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/util-extend"}, "author": "", "license": "MIT", "readmeFilename": "README.md", "readme": "# util-extend\n\nThe Node object extending function that Node uses for Node!\n\n## Usage\n\n```js\nvar extend = require('util-extend');\nfunction functionThatTakesOptions(options) {\n  var options = extend(defaults, options);\n  // now any unset options are set to the defaults.\n}\n```\n", "bugs": {"url": "https://github.com/isaacs/util-extend/issues"}, "_id": "util-extend@1.0.1", "dist": {"shasum": "bb703b79480293ddcdcfb3c6a9fea20f483415bc", "tarball": "http://registry.npmjs.org/util-extend/-/util-extend-1.0.1.tgz"}, "_from": "util-extend@^1.0.1", "_npmVersion": "1.3.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_shasum": "bb703b79480293ddcdcfb3c6a9fea20f483415bc", "_resolved": "https://registry.npmjs.org/util-extend/-/util-extend-1.0.1.tgz"}