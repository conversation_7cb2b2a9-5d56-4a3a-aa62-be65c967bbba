{"name": "read-installed", "description": "Read all the installed packages in a folder, and return a tree structure with all the data.", "version": "2.0.5", "repository": {"type": "git", "url": "git://github.com/isaacs/read-installed"}, "main": "read-installed.js", "scripts": {"test": "tap ./test/*.js"}, "dependencies": {"read-package-json": "1", "semver": "2", "slide": "~1.1.3", "util-extend": "^1.0.1", "graceful-fs": "2 || 3"}, "optionalDependencies": {"graceful-fs": "2 || 3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "devDependencies": {"tap": "~0.4.8"}, "readme": "# read-installed\n\nRead all the installed packages in a folder, and return a tree\nstructure with all the data.\n\nnpm uses this.\n\n## 2.0.0\n\nBreaking changes in `2.0.0`:\n\nThe second argument is now an `Object` that contains the following keys:\n\n * `depth` optional, defaults to Infinity\n * `log` optional log Function\n * `dev` optional, default false, set to true to include devDependencies\n\n## Usage\n\n```javascript\nvar readInstalled = require(\"read-installed\")\n// optional options\nvar options = { dev: false, log: fn, depth: 2 }\nreadInstalled(folder, options, function (er, data) {\n  ...\n})\n```\n", "readmeFilename": "README.md", "gitHead": "2595631e4d3cbd64b26cee63dc3b5ce9f53e3533", "bugs": {"url": "https://github.com/isaacs/read-installed/issues"}, "homepage": "https://github.com/isaacs/read-installed", "_id": "read-installed@2.0.5", "_shasum": "761eda1fd2dc322f8e77844a8bf1ddedbcfc754b", "_from": "read-installed@latest"}