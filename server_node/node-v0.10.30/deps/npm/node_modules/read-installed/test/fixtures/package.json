{"name": "read-installed", "description": "Read all the installed packages in a folder, and return a tree structure with all the data.", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/read-installed"}, "main": "read-installed.js", "scripts": {"test": "tap ./test/"}, "dependencies": {"semver": "2", "slide": "~1.1.3", "read-package-json": "1", "util-extend": "1"}, "optionalDependencies": {"graceful-fs": "~2"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "devDependencies": {"tap": "~0.4.8"}}