{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "fstream-ignore", "description": "A thing for ignoring files based on globs", "version": "0.0.8", "repository": {"type": "git", "url": "git://github.com/isaacs/fstream-ignore.git"}, "main": "ignore.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"fstream": "~0.1.17", "inherits": "2", "minimatch": "^0.3.0"}, "devDependencies": {"tap": "", "rimraf": "", "mkdirp": ""}, "license": "BSD", "gitHead": "8dfc936e8b2d25c0d82042b73a22c659e6e69a1e", "bugs": {"url": "https://github.com/isaacs/fstream-ignore/issues"}, "homepage": "https://github.com/isaacs/fstream-ignore", "_id": "fstream-ignore@0.0.8", "_shasum": "cc4830fb9963178be5d9eb37569a4a0785cf9e53", "_from": "fstream-ignore@~0.0", "_npmVersion": "1.4.10", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "cc4830fb9963178be5d9eb37569a4a0785cf9e53", "tarball": "http://registry.npmjs.org/fstream-ignore/-/fstream-ignore-0.0.8.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/fstream-ignore/-/fstream-ignore-0.0.8.tgz", "readme": "ERROR: No README data found!"}