{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "fstream-npm", "description": "fstream class for creating npm packages", "version": "0.1.7", "repository": {"type": "git", "url": "git://github.com/isaacs/fstream-npm.git"}, "main": "./fstream-npm.js", "dependencies": {"fstream-ignore": "~0.0", "inherits": "2"}, "license": "BSD", "readme": "# fstream-npm\n\nThis is an fstream DirReader class that will read a directory and filter\nthings according to the semantics of what goes in an npm package.\n\nFor example:\n\n```javascript\n// This will print out all the files that would be included\n// by 'npm publish' or 'npm install' of this directory.\n\nvar FN = require(\"fstream-npm\")\nFN({ path: \"./\" })\n  .on(\"child\", function (e) {\n    console.error(e.path.substr(e.root.path.length + 1))\n  })\n```\n\n", "readmeFilename": "README.md", "gitHead": "13839c9be784f2addc9352d525b35835fba01151", "bugs": {"url": "https://github.com/isaacs/fstream-npm/issues"}, "homepage": "https://github.com/isaacs/fstream-npm", "_id": "fstream-npm@0.1.7", "scripts": {}, "_shasum": "423dc5d1d1fcb7d878501f43c7e11a33292bd55f", "_from": "fstream-npm@latest"}