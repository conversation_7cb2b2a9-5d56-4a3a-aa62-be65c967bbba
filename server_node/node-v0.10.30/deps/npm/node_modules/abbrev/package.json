{"name": "abbrev", "version": "1.0.5", "description": "Like ruby's abbrev module, but in js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "main": "abbrev.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "http://github.com/isaacs/abbrev-js"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/abbrev-js/raw/master/LICENSE"}, "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "homepage": "https://github.com/isaacs/abbrev-js", "_id": "abbrev@1.0.5", "_shasum": "5d8257bd9ebe435e698b2fa431afde4fe7b10b03", "_from": "abbrev@latest", "_npmVersion": "1.4.7", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "5d8257bd9ebe435e698b2fa431afde4fe7b10b03", "tarball": "http://registry.npmjs.org/abbrev/-/abbrev-1.0.5.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.5.tgz"}