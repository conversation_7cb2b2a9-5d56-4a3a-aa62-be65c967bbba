{"name": "npm-install-checks", "version": "1.0.2", "description": "checks that npm runs during the installation of a module", "main": "index.js", "dependencies": {"npmlog": "0.1", "semver": "^2.3.0"}, "devDependencies": {"tap": "~0.4.8", "rimraf": "~2.2.5", "mkdirp": "~0.3.5"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/npm/npm-install-checks.git"}, "homepage": "https://github.com/npm/npm-install-checks", "keywords": ["npm,", "install"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/npm/npm-install-checks/issues"}, "readme": "# npm-install-checks\n\nA package that contains checks that npm runs during the installation.\n\n## API\n\n### .checkEngine(target, npmVer, nodeVer, force, strict, cb)\nCheck if node/npm version is supported by the package.\n\nError type: `ENOTSUP`\n\n### .checkPlatform(target, force, cb)\nCheck if OS/Arch is supported by the package.\n\nError type: `EBA<PERSON><PERSON>TFORM`\n\n### .check<PERSON>ycle(target, ancestors, cb)\nCheck for cyclic dependencies.\n\nError type: `ECYCLE`\n\n### .checkGit(folder, cb)\nCheck if a folder is a .git folder.\n\nError type: `EISGIT`\n", "readmeFilename": "README.md", "gitHead": "056ade7c5e3a6b3c720ca6a743c1b99a0705d29e", "_id": "npm-install-checks@1.0.2", "_shasum": "ebba769753fc8551308333ef411920743a6809f6", "_from": "npm-install-checks@latest"}