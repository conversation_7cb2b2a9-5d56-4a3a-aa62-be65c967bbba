{
  "name": "config-chain",
  "version": "0.3.0",
  "description": "HANDLE CONFIGURATION ONCE AND FOR ALL",
  "homepage": "http://github.com/dominictarr/config-chain",
  "repository": {
    "type": "git",
    "url": "https://github.com/dominictarr/config-chain.git"
  }
  //missing , and then this comment. this json is intensionally invalid
  "dependencies": {
    "proto-list": "1",
    "ini": "~1.0.2"
  },
  "bundleDependencies": ["ini"],
  "REM": "REMEMBER TO REMOVE BUNDLING WHEN/IF ISAACS MERGES ini#7",
  "author": "<PERSON> <<EMAIL>> (http://dominictarr.com)",
  "scripts": {
    "test": "node test/find-file.js && node test/ini.js && node test/env.js"
  }
}
