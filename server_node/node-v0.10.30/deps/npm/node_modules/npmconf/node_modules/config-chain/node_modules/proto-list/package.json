{"name": "proto-list", "version": "1.2.3", "description": "A utility for managing a prototype chain", "main": "./proto-list.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/isaacs/proto-list"}, "license": {"type": "MIT", "url": "https://github.com/isaacs/proto-list/blob/master/LICENSE"}, "devDependencies": {"tap": "0"}, "gitHead": "44d76897176861d176a53ed3f3fc5e05cdee7643", "bugs": {"url": "https://github.com/isaacs/proto-list/issues"}, "homepage": "https://github.com/isaacs/proto-list", "_id": "proto-list@1.2.3", "_shasum": "6235554a1bca1f0d15e3ca12ca7329d5def42bd9", "_from": "proto-list@~1.2.1", "_npmVersion": "1.4.14", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "6235554a1bca1f0d15e3ca12ca7329d5def42bd9", "tarball": "http://registry.npmjs.org/proto-list/-/proto-list-1.2.3.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/proto-list/-/proto-list-1.2.3.tgz", "readme": "ERROR: No README data found!"}