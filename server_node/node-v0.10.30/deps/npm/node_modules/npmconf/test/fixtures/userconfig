email = <EMAIL>
env-thing = ${random_env_var}
init.author.name = <PERSON>
init.author.email = <EMAIL>
init.author.url = http://blog.izs.me/
proprietary-attribs = false
npm:publishtest = true
_npmjs.org:couch = *************************************/registry
_auth = dXNlcm5hbWU6cGFzc3dvcmQ=
npm-www:nocache = 1
nodedir = /Users/<USER>/dev/js/node-v0.8
sign-git-tag = true
message = v%s
strict-ssl = false
tmp = ~/.tmp

[_token]
AuthSession = yabba-dabba-doodle
version = 1
expires = 1345001053415
path = /
httponly = true
