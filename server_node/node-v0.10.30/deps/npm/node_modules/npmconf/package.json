{"name": "npmconf", "version": "1.1.4", "description": "The config thing npm uses", "main": "npmconf.js", "directories": {"test": "test"}, "dependencies": {"config-chain": "~1.1.8", "inherits": "~2.0.0", "ini": "^1.2.0", "mkdirp": "~0.3.3", "nopt": "~3.0.1", "once": "~1.3.0", "osenv": "^0.1.0", "semver": "2", "uid-number": "0.0.5"}, "devDependencies": {"tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/npmconf"}, "keywords": ["npm", "config", "config-chain", "conf", "ini"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "license": "BSD", "readme": "# npmconf\n\nThe config thing npm uses\n\nIf you are interested in interacting with the config settings that npm\nuses, then use this module.\n\nHowever, if you are writing a new Node.js program, and want\nconfiguration functionality similar to what npm has, but for your\nown thing, then I'd recommend using [rc](https://github.com/dominictarr/rc),\nwhich is probably what you want.\n\nIf I were to do it all over again, that's what I'd do for npm.  But,\nalas, there are many systems depending on many of the particulars of\nnpm's configuration setup, so it's not worth the cost of changing.\n\n## USAGE\n\n```javascript\nvar npmconf = require('npmconf')\n\n// pass in the cli options that you read from the cli\n// or whatever top-level configs you want npm to use for now.\nnpmconf.load({some:'configs'}, function (er, conf) {\n  // do stuff with conf\n  conf.get('some', 'cli') // 'configs'\n  conf.get('username') // 'joebobwhatevers'\n  conf.set('foo', 'bar', 'user')\n  conf.save('user', function (er) {\n    // foo = bar is now saved to ~/.npmrc or wherever\n  })\n})\n```\n", "readmeFilename": "README.md", "gitHead": "3662624af0834159c35843fcf850469c09e0873c", "bugs": {"url": "https://github.com/isaacs/npmconf/issues"}, "homepage": "https://github.com/isaacs/npmconf", "_id": "npmconf@1.1.4", "_shasum": "2ac5d080206d4bfb9abc01bd34f083f93ae1603c", "_from": "npmconf@latest"}