{"name": "github-url-from-username-repo", "version": "0.2.0", "description": "Create urls from username/repo", "main": "index.js", "scripts": {"test": "mocha -R spec"}, "devDependencies": {"mocha": "~1.13.0"}, "repository": {"type": "git", "url": "**************:robert<PERSON><PERSON><PERSON>/github-url-from-username-repo.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/robert<PERSON><PERSON>ski/github-url-from-username-repo/issues"}, "keywords": ["git", "github", "repo"], "homepage": "https://github.com/robert<PERSON><PERSON>ski/github-url-from-username-repo", "_id": "github-url-from-username-repo@0.2.0", "_shasum": "7590b4fa605b7a6cbb7e06ffcd9d253210f9dbe1", "_from": "github-url-from-username-repo@latest", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7590b4fa605b7a6cbb7e06ffcd9d253210f9dbe1", "tarball": "http://registry.npmjs.org/github-url-from-username-repo/-/github-url-from-username-repo-0.2.0.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/github-url-from-username-repo/-/github-url-from-username-repo-0.2.0.tgz"}