{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "promzard", "description": "prompting wizardly", "version": "0.2.2", "repository": {"url": "git://github.com/isaacs/promzard"}, "dependencies": {"read": "1"}, "devDependencies": {"tap": "~0.2.5"}, "main": "promzard.js", "scripts": {"test": "tap test/*.js"}, "license": "ISC", "bugs": {"url": "https://github.com/isaacs/promzard/issues"}, "homepage": "https://github.com/isaacs/promzard", "_id": "promzard@0.2.2", "_shasum": "918b9f2b29458cb001781a8856502e4a79b016e0", "_from": "promzard@~0.2.0", "_npmVersion": "1.4.10", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "918b9f2b29458cb001781a8856502e4a79b016e0", "tarball": "http://registry.npmjs.org/promzard/-/promzard-0.2.2.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/promzard/-/promzard-0.2.2.tgz"}