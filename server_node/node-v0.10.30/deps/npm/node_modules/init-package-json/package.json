{"name": "init-package-json", "version": "0.1.0", "main": "init-package-json.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/init-package-json"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "description": "A node module to get your node module started", "dependencies": {"glob": "^4.0.2", "promzard": "~0.2.0", "read": "~1.0.1", "read-package-json": "1", "semver": "2.x"}, "devDependencies": {"tap": "~0.2.5", "rimraf": "~2.0.2"}, "keywords": ["init", "package.json", "package", "helper", "wizard", "wizerd", "prompt", "start"], "readme": "# init-package-json\n\nA node module to get your node module started.\n\n## Usage\n\n```javascript\nvar init = require('init-package-json')\nvar path = require('path')\n\n// a path to a promzard module.  In the event that this file is\n// not found, one will be provided for you.\nvar initFile = path.resolve(process.env.HOME, '.npm-init')\n\n// the dir where we're doin stuff.\nvar dir = process.cwd()\n\n// extra stuff that gets put into the PromZard module's context.\n// In npm, this is the resolved config object.  Exposed as 'config'\n// Optional.\nvar configData = { some: 'extra stuff' }\n\n// Any existing stuff from the package.json file is also exposed in the\n// PromZard module as the `package` object.  There will also be free\n// vars for:\n// * `filename` path to the package.json file\n// * `basename` the tip of the package dir\n// * `dirname` the parent of the package dir\n\ninit(dir, initFile, configData, function (er, data) {\n  // the data's already been written to {dir}/package.json\n  // now you can do stuff with it\n})\n```\n\nOr from the command line:\n\n```\n$ npm-init\n```\n\nSee [PromZard](https://github.com/isaacs/promzard) for details about\nwhat can go in the config file.\n", "readmeFilename": "README.md", "gitHead": "378bf828106a56e340d3017258ae372a12f0efe7", "bugs": {"url": "https://github.com/isaacs/init-package-json/issues"}, "homepage": "https://github.com/isaacs/init-package-json", "_id": "init-package-json@0.1.0", "_shasum": "249c982759a102556f294f2592c14a2dad855f52", "_from": "init-package-json@latest"}