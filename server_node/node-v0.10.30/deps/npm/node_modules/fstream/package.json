{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "fstream", "description": "Advanced file system stream things", "version": "0.1.28", "repository": {"type": "git", "url": "git://github.com/isaacs/fstream.git"}, "main": "fstream.js", "engines": {"node": ">=0.6"}, "dependencies": {"graceful-fs": "~3.0.2", "inherits": "~2.0.0", "mkdirp": "0.3", "rimraf": "2"}, "devDependencies": {"tap": ""}, "scripts": {"test": "tap examples/*.js"}, "license": "BSD", "readme": "Like FS streams, but with stat on them, and supporting directories and\nsymbolic links, as well as normal files.  Also, you can use this to set\nthe stats on a file, even if you don't change its contents, or to create\na symlink, etc.\n\nSo, for example, you can \"write\" a directory, and it'll call `mkdir`.  You\ncan specify a uid and gid, and it'll call `chown`.  You can specify a\n`mtime` and `atime`, and it'll call `utimes`.  You can call it a symlink\nand provide a `linkpath` and it'll call `symlink`.\n\nNote that it won't automatically resolve symbolic links.  So, if you\ncall `fstream.Reader('/some/symlink')` then you'll get an object\nthat stats and then ends immediately (since it has no data).  To follow\nsymbolic links, do this: `fstream.Reader({path:'/some/symlink', follow:\ntrue })`.\n\nThere are various checks to make sure that the bytes emitted are the\nsame as the intended size, if the size is set.\n\n## Examples\n\n```javascript\nfstream\n  .Writer({ path: \"path/to/file\"\n          , mode: 0755\n          , size: 6\n          })\n  .write(\"hello\\n\")\n  .end()\n```\n\nThis will create the directories if they're missing, and then write\n`hello\\n` into the file, chmod it to 0755, and assert that 6 bytes have\nbeen written when it's done.\n\n```javascript\nfstream\n  .Writer({ path: \"path/to/file\"\n          , mode: 0755\n          , size: 6\n          , flags: \"a\"\n          })\n  .write(\"hello\\n\")\n  .end()\n```\n\nYou can pass flags in, if you want to append to a file.\n\n```javascript\nfstream\n  .Writer({ path: \"path/to/symlink\"\n          , linkpath: \"./file\"\n          , SymbolicLink: true\n          , mode: \"0755\" // octal strings supported\n          })\n  .end()\n```\n\nIf isSymbolicLink is a function, it'll be called, and if it returns\ntrue, then it'll treat it as a symlink.  If it's not a function, then\nany truish value will make a symlink, or you can set `type:\n'SymbolicLink'`, which does the same thing.\n\nNote that the linkpath is relative to the symbolic link location, not\nthe parent dir or cwd.\n\n```javascript\nfstream\n  .Reader(\"path/to/dir\")\n  .pipe(fstream.Writer(\"path/to/other/dir\"))\n```\n\nThis will do like `cp -Rp path/to/dir path/to/other/dir`.  If the other\ndir exists and isn't a directory, then it'll emit an error.  It'll also\nset the uid, gid, mode, etc. to be identical.  In this way, it's more\nlike `rsync -a` than simply a copy.\n", "readmeFilename": "README.md", "gitHead": "f757159eb6a5198a9ebc6cde1c4c1b9ae8e0d7b2", "bugs": {"url": "https://github.com/isaacs/fstream/issues"}, "homepage": "https://github.com/isaacs/fstream", "_id": "fstream@0.1.28", "_shasum": "2b9286f3a646e30075efd0354729361c4b762a29", "_from": "fstream@~0.1.27"}