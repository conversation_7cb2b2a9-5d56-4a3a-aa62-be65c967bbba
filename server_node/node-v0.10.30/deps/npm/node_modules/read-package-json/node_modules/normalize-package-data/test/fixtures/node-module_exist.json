{"name": "node-module_exist", "description": "Find if a NodeJS module is available to require or not", "version": "0.0.1", "main": "module_exist.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "*******************:3135914.git"}, "homepage": "https://github.com/FGRibreau", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "http://fgribreau.com.com/"}, "devDependencies": {"nodeunit": "~0.7.4"}, "keywords": ["core", "modules"], "license": "MIT"}