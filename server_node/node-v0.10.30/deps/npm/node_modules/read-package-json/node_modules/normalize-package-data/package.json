{"name": "normalize-package-data", "version": "0.3.0", "author": {"name": "Meryn Stol", "email": "<EMAIL>"}, "description": "Normalizes data that can be found in package.json files.", "repository": {"type": "git", "url": "git://github.com/meryn/normalize-package-data.git"}, "main": "lib/normalize.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"github-url-from-git": "~1.1.1", "github-url-from-username-repo": "^0.2.0", "semver": "2"}, "devDependencies": {"tap": "~0.2.5", "underscore": "~1.4.4", "async": "~0.2.7"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Meryn Stol", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "f4d4268894a23d6f37a335d4ae28bd90b14e4ef9", "bugs": {"url": "https://github.com/meryn/normalize-package-data/issues"}, "homepage": "https://github.com/meryn/normalize-package-data", "_id": "normalize-package-data@0.3.0", "_shasum": "70ea9e5b6caf69faa9d83e42f71489642372d815", "_from": "normalize-package-data@^0.3.0", "_npmVersion": "1.4.14", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "70ea9e5b6caf69faa9d83e42f71489642372d815", "tarball": "http://registry.npmjs.org/normalize-package-data/-/normalize-package-data-0.3.0.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-0.3.0.tgz"}