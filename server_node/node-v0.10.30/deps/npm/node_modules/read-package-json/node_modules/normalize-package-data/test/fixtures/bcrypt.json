{"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "0.7.5", "author": "<PERSON> (http://github.com/ncb000gt)", "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "http://github.com/ncb000gt/node.bcrypt.js.git"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "http://github.com/ncb000gt/node.bcrypt.js/issues"}, "scripts": {"test": "node-gyp configure build && nodeunit test"}, "dependencies": {"bindings": "1.0.0"}, "devDependencies": {"nodeunit": ">=0.6.4"}, "contributors": ["<PERSON> <<EMAIL>> (https://github.com/Shadowfiend)", "<PERSON> <<EMAIL>> (https://github.com/thegoleffect)", "<PERSON> <<EMAIL>> (https://github.com/dtrejo)", "<PERSON> <<EMAIL>> (https://github.com/pixelglow)", "NewITFarmer.com <> (https://github.com/newitfarmer)", "<PERSON> <<EMAIL>> (https://github.com/alfredwesterveld)", "<PERSON>-Roy <<EMAIL>> (https://github.com/vincentcr)", "<PERSON> <<EMAIL>> (https://github.com/lloyd)", "<PERSON>htylman <<EMAIL>> (https://github.com/shtylman)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/vadimg)", "<PERSON> <> (https://github.com/bnoordhuis)", "<PERSON> <<EMAIL>> (https://github.com/tootallnate)", "<PERSON> <<EMAIL>> (https://github.com/seanmonstar)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/weareu)"]}