{"name": "request", "description": "Simplified HTTP request client.", "tags": ["http", "simple", "util", "utility"], "version": "2.16.7", "author": "<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "http://github.com/mikeal/request.git"}, "bugs": {"url": "http://github.com/mikeal/request/issues"}, "engines": ["node >= 0.8.0"], "main": "index.js", "dependencies": {"form-data": "~0.0.3", "mime": "~1.2.7", "hawk": "~0.10.2", "node-uuid": "~1.4.0", "cookie-jar": "~0.2.0", "aws-sign": "~0.2.0", "oauth-sign": "~0.2.0", "forever-agent": "~0.2.0", "tunnel-agent": "~0.2.0", "json-stringify-safe": "~3.0.0", "qs": "~0.5.4"}, "scripts": {"test": "node tests/run.js"}}