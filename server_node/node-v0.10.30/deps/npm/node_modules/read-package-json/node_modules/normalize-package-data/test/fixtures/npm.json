{"version": "1.2.17", "name": "npm", "publishConfig": {"proprietary-attribs": false}, "description": "A package manager for node", "keywords": ["package manager", "modules", "install", "package.json"], "preferGlobal": true, "config": {"publishtest": false}, "homepage": "https://npmjs.org/doc/", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "repository": {"type": "git", "url": "https://github.com/isaacs/npm"}, "bugs": {"email": "<EMAIL>", "url": "http://github.com/isaacs/npm/issues"}, "directories": {"doc": "./doc", "man": "./man", "lib": "./lib", "bin": "./bin"}, "main": "./lib/npm.js", "bin": "./bin/npm-cli.js", "dependencies": {"semver": "~1.1.2", "ini": "~1.1.0", "slide": "1", "abbrev": "~1.0.4", "graceful-fs": "~1.2.0", "minimatch": "~0.2.11", "nopt": "~2.1.1", "rimraf": "2", "request": "~2.9", "which": "1", "tar": "~0.1.17", "fstream": "~0.1.22", "block-stream": "*", "inherits": "1", "mkdirp": "~0.3.3", "read": "~1.0.4", "lru-cache": "~2.3.0", "node-gyp": "~0.9.3", "fstream-npm": "~0.1.3", "uid-number": "0", "archy": "0", "chownr": "0", "npmlog": "0", "ansi": "~0.1.2", "npm-registry-client": "~0.2.18", "read-package-json": "~0.3.0", "read-installed": "0", "glob": "~3.1.21", "init-package-json": "0.0.6", "osenv": "0", "lockfile": "~0.3.0", "retry": "~0.6.0", "once": "~1.1.1", "npmconf": "0", "opener": "~1.3.0", "chmodr": "~0.1.0", "cmd-shim": "~1.1.0"}, "bundleDependencies": ["semver", "ini", "slide", "abbrev", "graceful-fs", "minimatch", "nopt", "<PERSON><PERSON><PERSON>", "request", "which", "tar", "fstream", "block-stream", "inherits", "mkdirp", "read", "lru-cache", "node-gyp", "fstream-npm", "uid-number", "archy", "chownr", "npmlog", "ansi", "npm-registry-client", "read-package-json", "read-installed", "glob", "init-package-json", "osenv", "lockfile", "retry", "once", "npmconf", "opener", "chmodr", "cmd-shim"], "devDependencies": {"ronn": "~0.3.6", "tap": "~0.4.0"}, "engines": {"node": ">=0.6", "npm": "1"}, "scripts": {"test": "node ./test/run.js && tap test/tap/*.js", "tap": "tap test/tap/*.js", "prepublish": "node bin/npm-cli.js prune ; rm -rf test/*/*/node_modules ; make -j4 doc", "dumpconf": "env | grep npm | sort | uniq", "echo": "node bin/npm-cli.js"}, "licenses": [{"type": "MIT +no-false-attribs", "url": "https://github.com/isaacs/npm/raw/master/LICENSE"}]}