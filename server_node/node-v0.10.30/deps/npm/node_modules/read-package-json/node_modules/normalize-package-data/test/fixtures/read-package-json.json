{"name": "read-package-json", "version": "0.1.1", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "description": "The thing npm uses to read package.json files with semantics and defaults and validation", "repository": {"type": "git", "url": "git://github.com/isaacs/read-package-json.git"}, "main": "read-json.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"glob": "~3.1.9", "lru-cache": "~1.1.0", "semver": "~1.0.14", "slide": "~1.1.3"}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {"npmlog": "0", "graceful-fs": "~1.1.8"}}