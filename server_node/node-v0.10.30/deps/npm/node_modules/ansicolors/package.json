{"name": "ansicolors", "version": "0.3.2", "description": "Functions that surround a string with ansicolor codes so it prints in color.", "main": "ansicolors.js", "scripts": {"test": "node test/*.js"}, "repository": {"type": "git", "url": "git://github.com/thlorenz/ansicolors.git"}, "keywords": ["ansi", "colors", "highlight", "string"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, "license": "MIT", "readmeFilename": "README.md", "gitHead": "858847ca28e8b360d9b70eee0592700fa2ab087d", "readme": "# ansicolors [![build status](https://secure.travis-ci.org/thlorenz/ansicolors.png)](http://next.travis-ci.org/thlorenz/ansicolors)\n\nFunctions that surround a string with ansicolor codes so it prints in color.\n\nIn case you need styles, like `bold`, have a look at [ansistyles](https://github.com/thlorenz/ansistyles).\n\n## Installation\n\n    npm install ansicolors\n\n## Usage\n\n```js\nvar colors = require('ansicolors');\n\n// foreground colors\nvar redHerring = colors.red('herring');\nvar blueMoon = colors.blue('moon');\nvar brighBlueMoon = colors.brightBlue('moon');\n\nconsole.log(redHerring);      // this will print 'herring' in red\nconsole.log(blueMoon);        // this 'moon' in blue\nconsole.log(brightBlueMoon);  // I think you got the idea\n\n// background colors\nconsole.log(colors.bgYellow('printed on yellow background'));\nconsole.log(colors.bgBrightBlue('printed on bright blue background'));\n\n// mixing background and foreground colors\n// below two lines have same result (order in which bg and fg are combined doesn't matter)\nconsole.log(colors.bgYellow(colors.blue('printed on yellow background in blue')));\nconsole.log(colors.blue(colors.bgYellow('printed on yellow background in blue')));\n```\n\n## Advanced API\n\n**ansicolors** allows you to access opening and closing escape sequences separately.\n\n```js\nvar colors = require('ansicolors');\n\nfunction inspect(obj, depth) {\n  return require('util').inspect(obj, false, depth || 5, true);\n}\n\nconsole.log('open blue', inspect(colors.open.blue));\nconsole.log('close bgBlack', inspect(colors.close.bgBlack));\n\n// => open blue '\\u001b[34m'\n//    close bgBlack '\\u001b[49m'\n```\n\n## Tests\n\nLook at the [tests](https://github.com/thlorenz/ansicolors/blob/master/test/ansicolors.js) to see more examples and/or run them via: \n\n    npm explore ansicolors && npm test\n\n## Alternatives\n\n**ansicolors** tries to meet simple use cases with a very simple API. However, if you need a more powerful ansi formatting tool, \nI'd suggest to look at the [features](https://github.com/TooTallNate/ansi.js#features) of the [ansi module](https://github.com/TooTallNate/ansi.js).\n", "bugs": {"url": "https://github.com/thlorenz/ansicolors/issues"}, "homepage": "https://github.com/thlorenz/ansicolors", "_id": "ansicolors@0.3.2", "_from": "ansicolors@latest"}