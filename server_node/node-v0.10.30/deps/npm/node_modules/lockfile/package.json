{"name": "lockfile", "version": "0.4.2", "main": "lockfile.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5", "touch": "0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/lockfile"}, "keywords": ["lockfile", "lock", "file", "fs", "O_EXCL"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "description": "A very polite lock file utility, which endeavors to not litter, and to wait patiently for others.", "readme": "# lockfile\n\nA very polite lock file utility, which endeavors to not litter, and to\nwait patiently for others.\n\n## Usage\n\n```javascript\nvar lockFile = require('lockfile')\n\n// opts is optional, and defaults to {}\nlockFile.lock('some-file.lock', opts, function (er) {\n  // if the er happens, then it failed to acquire a lock.\n  // if there was not an error, then the file was created,\n  // and won't be deleted until we unlock it.\n\n  // do my stuff, free of interruptions\n  // then, some time later, do:\n  lockFile.unlock('some-file.lock', function (er) {\n    // er means that an error happened, and is probably bad.\n  })\n})\n```\n\n## Methods\n\nSync methods return the value/throw the error, others don't.  Standard\nnode fs stuff.\n\nAll known locks are removed when the process exits.  Of course, it's\npossible for certain types of failures to cause this to fail, but a best\neffort is made to not be a litterbug.\n\n### lockFile.lock(path, [opts], cb)\n\nAcquire a file lock on the specified path\n\n### lockFile.lockSync(path, [opts])\n\nAcquire a file lock on the specified path\n\n### lockFile.unlock(path, cb)\n\nClose and unlink the lockfile.\n\n### lockFile.unlockSync(path)\n\nClose and unlink the lockfile.\n\n### lockFile.check(path, [opts], cb)\n\nCheck if the lockfile is locked and not stale.\n\nReturns boolean.\n\n### lockFile.checkSync(path, [opts], cb)\n\nCheck if the lockfile is locked and not stale.\n\nCallback is called with `cb(error, isLocked)`.\n\n## Options\n\n### opts.wait\n\nA number of milliseconds to wait for locks to expire before giving up.\nOnly used by lockFile.lock.  Relies on fs.watch.  If the lock is not\ncleared by the time the wait expires, then it returns with the original\nerror.\n\n### opts.stale\n\nA number of milliseconds before locks are considered to have expired.\n\n### opts.retries\n\nUsed by lock and lockSync.  Retry `n` number of times before giving up.\n\n### opts.retryWait\n\nUsed by lock.  Wait `n` milliseconds before retrying.\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/isaacs/lockfile/issues"}, "_id": "lockfile@0.4.2", "dist": {"shasum": "ab91f5d3745bc005ae4fa34d078910d1f2b9612d"}, "_from": "lockfile@0.4.2", "_resolved": "https://registry.npmjs.org/lockfile/-/lockfile-0.4.2.tgz"}