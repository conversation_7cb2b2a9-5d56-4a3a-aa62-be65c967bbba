{"name": "npm-user-validate", "version": "0.1.0", "description": "User validations for npm", "main": "npm-user-validate.js", "devDependencies": {"tap": "0.4.3"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/npm/npm-registry-mock.git"}, "keywords": ["npm", "validation", "registry"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "readme": "[![Build Status](https://travis-ci.org/npm/npm-user-validate.png?branch=master)](https://travis-ci.org/npm/npm-user-validate)\n[![devDependency Status](https://david-dm.org/npm/npm-user-validate/dev-status.png)](https://david-dm.org/npm/npm-user-validate#info=devDependencies)\n\n# npm-user-validate\n\nValidation for the npm client and npm-www (and probably other npm projects)\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/npm/npm-registry-mock/issues"}, "homepage": "https://github.com/npm/npm-registry-mock", "_id": "npm-user-validate@0.1.0", "_shasum": "358a5b5148ed3f79771d980388c6e34c4a61f638", "_from": "npm-user-validate@latest"}