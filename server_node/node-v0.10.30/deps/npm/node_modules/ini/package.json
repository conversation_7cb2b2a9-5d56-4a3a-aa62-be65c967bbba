{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "ini", "description": "An ini encoder/decoder for node", "version": "1.2.1", "repository": {"type": "git", "url": "git://github.com/isaacs/ini.git"}, "main": "ini.js", "scripts": {"test": "tap test/*.js"}, "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"tap": "~0.4.0"}, "gitHead": "13498ce1ba5a6a20cd77ed2b55de0e714786f70c", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "homepage": "https://github.com/isaacs/ini", "_id": "ini@1.2.1", "_shasum": "7f774e2f22752cd1dacbf9c63323df2a164ebca3", "_from": "ini@latest", "_npmVersion": "1.4.11", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "7f774e2f22752cd1dacbf9c63323df2a164ebca3", "tarball": "http://registry.npmjs.org/ini/-/ini-1.2.1.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/ini/-/ini-1.2.1.tgz"}