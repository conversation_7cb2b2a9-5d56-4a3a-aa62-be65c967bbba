/* Copyright Joyent, Inc. and other Node contributors. All rights reserved.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 */

#include "uv.h"
#include "task.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

static void connection_cb(uv_stream_t* server, int status);
static void connect_cb(uv_connect_t* req, int status);
static void write_cb(uv_write_t* req, int status);
static void read_cb(uv_stream_t* stream, ssize_t nread, uv_buf_t buf);
static uv_buf_t alloc_cb(uv_handle_t* handle, size_t suggested_size);

static uv_tcp_t tcp_server;
static uv_tcp_t tcp_client;
static uv_tcp_t tcp_peer; /* client socket as accept()-ed by server */
static uv_connect_t connect_req;
static uv_write_t write_req;

static int write_cb_called;
static int read_cb_called;

static void connection_cb(uv_stream_t* server, int status) {
  int r;
  uv_buf_t buf;

  ASSERT(server == (uv_stream_t*)&tcp_server);
  ASSERT(status == 0);

  r = uv_tcp_init(server->loop, &tcp_peer);
  ASSERT(r == 0);

  r = uv_accept(server, (uv_stream_t*)&tcp_peer);
  ASSERT(r == 0);

  r = uv_read_start((uv_stream_t*)&tcp_peer, alloc_cb, read_cb);
  ASSERT(r == 0);

  buf.base = "hello\n";
  buf.len = 6;

  r = uv_write(&write_req, (uv_stream_t*)&tcp_peer, &buf, 1, write_cb);
  ASSERT(r == 0);
}


static uv_buf_t alloc_cb(uv_handle_t* handle, size_t suggested_size) {
  static char slab[1024];
  return uv_buf_init(slab, sizeof slab);
}


static void read_cb(uv_stream_t* stream, ssize_t nread, uv_buf_t buf) {
  if (nread == -1) {
    fprintf(stderr, "read_cb error: %s\n", uv_err_name(uv_last_error(stream->loop)));
    ASSERT(uv_last_error(stream->loop).code == UV_ECONNRESET ||
      uv_last_error(stream->loop).code == UV_EOF);

    uv_close((uv_handle_t*)&tcp_server, NULL);
    uv_close((uv_handle_t*)&tcp_peer, NULL);
  }

  read_cb_called++;
}


static void connect_cb(uv_connect_t* req, int status) {
  ASSERT(req == &connect_req);
  ASSERT(status == 0);

  /* Close the client. */
  uv_close((uv_handle_t*)&tcp_client, NULL);
}


static void write_cb(uv_write_t* req, int status) {
  ASSERT(status == 0);
  write_cb_called++;
}


TEST_IMPL(tcp_write_to_half_open_connection) {
  uv_loop_t* loop;
  int r;

  loop = uv_default_loop();
  ASSERT(loop != NULL);

  r = uv_tcp_init(loop, &tcp_server);
  ASSERT(r == 0);

  r = uv_tcp_bind(&tcp_server, uv_ip4_addr("127.0.0.1", TEST_PORT));
  ASSERT(r == 0);

  r = uv_listen((uv_stream_t*)&tcp_server, 1, connection_cb);
  ASSERT(r == 0);

  r = uv_tcp_init(loop, &tcp_client);
  ASSERT(r == 0);

  r = uv_tcp_connect(&connect_req,
                     &tcp_client,
                     uv_ip4_addr("127.0.0.1", TEST_PORT),
                     connect_cb);
  ASSERT(r == 0);

  r = uv_run(loop, UV_RUN_DEFAULT);
  ASSERT(r == 0);

  ASSERT(write_cb_called > 0);
  ASSERT(read_cb_called > 0);

  MAKE_VALGRIND_HAPPY();
  return 0;
}
