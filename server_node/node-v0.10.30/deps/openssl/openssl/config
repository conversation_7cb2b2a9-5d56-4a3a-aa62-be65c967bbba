#!/bin/sh
#
# OpenSSL config: determine the operating system and run ./Configure
#
# "config -h" for usage information.
#
#          this is a merge of minarch and Guess<PERSON> from the Apache Group.
#          Originally written by <PERSON> <<EMAIL>>.

# Original Apache Group comments on GuessOS

# Simple OS/Platform guesser. Similar to config.guess but
# much, much smaller. Since it was developed for use with
# Apache, it follows under Apache's regular licensing
# with one specific addition: Any changes or additions
# to this script should be Emailed to the Apache
# group (<EMAIL>) in general and to
# <PERSON> (<EMAIL>) in specific.
#
# Be as similar to the output of config.guess/config.sub
# as possible.

PREFIX=""
SUFFIX=""
TEST="false"
EXE=""

# pick up any command line args to config
for i
do
case "$i" in 
-d*) PREFIX="debug-";;
-t*) TEST="true";;
-h*) TEST="true"; cat <<EOF
Usage: config [options]
 -d	Add a debug- prefix to machine choice.
 -t	Test mode, do not run the Configure perl script.
 -h	This help.

Any other text will be passed to the Configure perl script.
See INSTALL for instructions.

EOF
;;
*) options=$options" $i" ;;
esac
done

# First get uname entries that we use below

[ "$MACHINE" ] || MACHINE=`(uname -m) 2>/dev/null` || MACHINE="unknown"
[ "$RELEASE" ] || RELEASE=`(uname -r) 2>/dev/null` || RELEASE="unknown"
[ "$SYSTEM" ] || SYSTEM=`(uname -s) 2>/dev/null`  || SYSTEM="unknown"
[ "$BUILD" ] || VERSION=`(uname -v) 2>/dev/null` || VERSION="unknown"


# Now test for ISC and SCO, since it is has a braindamaged uname.
#
# We need to work around FreeBSD ******* 
(
XREL=`uname -X 2>/dev/null | grep "^Release" | awk '{print $3}'`
if [ "x$XREL" != "x" ]; then
    if [ -f /etc/kconfig ]; then
	case "$XREL" in
	    4.0|4.1)
		    echo "${MACHINE}-whatever-isc4"; exit 0
		;;
	esac
    else
	case "$XREL" in
	    3.2v4.2)
		echo "whatever-whatever-sco3"; exit 0
		;;
	    3.2v5.0*)
		echo "whatever-whatever-sco5"; exit 0
		;;
	    4.2MP)
		case "x${VERSION}" in
		    x2.0*) echo "whatever-whatever-unixware20"; exit 0 ;;
		    x2.1*) echo "whatever-whatever-unixware21"; exit 0 ;;
		    x2*)   echo "whatever-whatever-unixware2";  exit 0 ;;
		esac
		;;
	    4.2)
		echo "whatever-whatever-unixware1"; exit 0
		;;
	    5*)
		case "x${VERSION}" in
		    # We hardcode i586 in place of ${MACHINE} for the
		    # following reason. The catch is that even though Pentium
		    # is minimum requirement for platforms in question,
		    # ${MACHINE} gets always assigned to i386. Now, problem
		    # with i386 is that it makes ./config pass 386 to
		    # ./Configure, which in turn makes make generate
		    # inefficient SHA-1 (for this moment) code.
		    x[678]*)  echo "i586-sco-unixware7"; exit 0 ;;
		esac
		;;
	esac
    fi
fi
# Now we simply scan though... In most cases, the SYSTEM info is enough
#
case "${SYSTEM}:${RELEASE}:${VERSION}:${MACHINE}" in
    MPE/iX:*)
	MACHINE=`echo "$MACHINE" | sed -e 's/-/_/g'`
	echo "parisc-hp-MPE/iX"; exit 0
	;;
    A/UX:*)
	echo "m68k-apple-aux3"; exit 0
	;;

    AIX:[3-9]:4:*)
	echo "${MACHINE}-ibm-aix"; exit 0
	;;

    AIX:*:[5-9]:*)
	echo "${MACHINE}-ibm-aix"; exit 0
	;;

    AIX:*)
	echo "${MACHINE}-ibm-aix3"; exit 0
	;;

    BeOS:*:BePC)
    if [ -e /boot/develop/headers/be/bone ]; then
		echo "beos-x86-bone"; exit 0
	else
		echo "beos-x86-r5"; exit 0
	fi
	;;

    dgux:*)
	echo "${MACHINE}-dg-dgux"; exit 0
	;;

    HI-UX:*)
	echo "${MACHINE}-hi-hiux"; exit 0
	;;

    HP-UX:*)
	HPUXVER=`echo ${RELEASE}|sed -e 's/[^.]*.[0B]*//'`
	case "$HPUXVER" in
	    1[0-9].*)	# HPUX 10 and 11 targets are unified
		echo "${MACHINE}-hp-hpux1x"; exit 0
		;;
	    *)
		echo "${MACHINE}-hp-hpux"; exit 0
		;;
	esac
	;;

    IRIX:5.*)
	echo "mips2-sgi-irix"; exit 0
	;;

    IRIX:6.*)
	echo "mips3-sgi-irix"; exit 0
	;;

    IRIX64:*)
	echo "mips4-sgi-irix64"; exit 0
	;;

    Linux:[2-9].*)
	echo "${MACHINE}-whatever-linux2"; exit 0
	;;

    Linux:1.*)
	echo "${MACHINE}-whatever-linux1"; exit 0
	;;

    GNU*)
	echo "hurd-x86"; exit 0;
	;;

    LynxOS:*)
	echo "${MACHINE}-lynx-lynxos"; exit 0
	;;

    BSD/OS:4.*)  # BSD/OS always says 386
	echo "i486-whatever-bsdi4"; exit 0
	;;

    BSD/386:*:*:*486*|BSD/OS:*:*:*:*486*)
        case `/sbin/sysctl -n hw.model` in
	    Pentium*)
                echo "i586-whatever-bsdi"; exit 0
                ;;
            *)
                echo "i386-whatever-bsdi"; exit 0
                ;;
            esac;
	;;

    BSD/386:*|BSD/OS:*)
	echo "${MACHINE}-whatever-bsdi"; exit 0
	;;

    FreeBSD:*:*:*386*)
        VERS=`echo ${RELEASE} | sed -e 's/[-(].*//'`
        MACH=`sysctl -n hw.model`
        ARCH='whatever'
        case ${MACH} in
           *386*       ) MACH="i386"     ;;
           *486*       ) MACH="i486"     ;;
           Pentium\ II*) MACH="i686"     ;;
           Pentium*    ) MACH="i586"     ;;
           *           ) MACH="$MACHINE" ;;
        esac
        case ${MACH} in
           i[0-9]86 ) ARCH="pc" ;;
        esac
        echo "${MACH}-${ARCH}-freebsd${VERS}"; exit 0
        ;;

    FreeBSD:*)
	echo "${MACHINE}-whatever-freebsd"; exit 0
	;;

    NetBSD:*:*:*386*)
        echo "`(/usr/sbin/sysctl -n hw.model || /sbin/sysctl -n hw.model) | sed 's,.*\(.\)86-class.*,i\186,'`-whatever-netbsd"; exit 0
	;;

    NetBSD:*)
	echo "${MACHINE}-whatever-netbsd"; exit 0
	;;

    OpenBSD:*)
	echo "${MACHINE}-whatever-openbsd"; exit 0
	;;

    OpenUNIX:*)
	echo "${MACHINE}-unknown-OpenUNIX${VERSION}"; exit 0
	;;

    OSF1:*:*:*alpha*)
	OSFMAJOR=`echo ${RELEASE}| sed -e 's/^V\([0-9]*\)\..*$/\1/'`
	case "$OSFMAJOR" in
	    4|5)
		echo "${MACHINE}-dec-tru64"; exit 0
		;;
	    1|2|3)
		echo "${MACHINE}-dec-osf"; exit 0
		;;
	    *)
		echo "${MACHINE}-dec-osf"; exit 0
		;;
	esac
	;;

    QNX:*)
	case "$RELEASE" in
	    4*)
		echo "${MACHINE}-whatever-qnx4"
		;;
	    6*)
		echo "${MACHINE}-whatever-qnx6"
		;;
	    *)
		echo "${MACHINE}-whatever-qnx"
		;;
	esac
	exit 0
	;;

    Paragon*:*:*:*)
	echo "i860-intel-osf1"; exit 0
	;;

    Rhapsody:*)
	echo "ppc-apple-rhapsody"; exit 0
	;;

    Darwin:*)
	case "$MACHINE" in
	    Power*)
		echo "ppc-apple-darwin${VERSION}"
		;;
	    *)
		echo "i686-apple-darwin${VERSION}"
		;;
	esac
	exit 0
	;;

    SunOS:5.*)
	echo "${MACHINE}-whatever-solaris2"; exit 0
	;;

    SunOS:*)
	echo "${MACHINE}-sun-sunos4"; exit 0
	;;

    UNIX_System_V:4.*:*)
	echo "${MACHINE}-whatever-sysv4"; exit 0
	;;

    VOS:*:*:i786)
     echo "i386-stratus-vos"; exit 0
     ;;

    VOS:*:*:*)
     echo "hppa1.1-stratus-vos"; exit 0
     ;;

    *:4*:R4*:m88k)
	echo "${MACHINE}-whatever-sysv4"; exit 0
	;;

    DYNIX/ptx:4*:*)
	echo "${MACHINE}-whatever-sysv4"; exit 0
	;;

    *:4.0:3.0:3[34]?? | *:4.0:3.0:3[34]??,*)
	echo "i486-ncr-sysv4"; exit 0
	;;

    ULTRIX:*)
	echo "${MACHINE}-unknown-ultrix"; exit 0
	;;

    SINIX*|ReliantUNIX*)
	echo "${MACHINE}-siemens-sysv4"; exit 0
	;;

    POSIX-BC*)
	echo "${MACHINE}-siemens-sysv4"; exit 0   # Here, $MACHINE == "BS2000"
	;;

    machten:*)
       echo "${MACHINE}-tenon-${SYSTEM}"; exit 0;
       ;;

    library:*)
	echo "${MACHINE}-ncr-sysv4"; exit 0
	;;

    ConvexOS:*:11.0:*)
	echo "${MACHINE}-v11-${SYSTEM}"; exit 0;
	;;

    NEWS-OS:4.*)
	echo "mips-sony-newsos4"; exit 0;
	;;

    MINGW*)
	echo "${MACHINE}-whatever-mingw"; exit 0;
	;;
    CYGWIN*)
	case "$RELEASE" in
	    [bB]*|1.0|1.[12].*)
		echo "${MACHINE}-whatever-cygwin_pre1.3"
		;;
	    *)
		echo "${MACHINE}-whatever-cygwin"
		;;
	esac
	exit 0
	;;

    *"CRAY T3E")
       echo "t3e-cray-unicosmk"; exit 0;
       ;;

    *CRAY*)
       echo "j90-cray-unicos"; exit 0;
       ;;

    NONSTOP_KERNEL*)
       echo "nsr-tandem-nsk"; exit 0;
       ;;

    vxworks*)
       echo "${MACHINE}-whatever-vxworks"; exit 0;
       ;;
esac

#
# Ugg. These are all we can determine by what we know about
# the output of uname. Be more creative:
#

# Do the Apollo stuff first. Here, we just simply assume
# that the existance of the /usr/apollo directory is proof
# enough
if [ -d /usr/apollo ]; then
    echo "whatever-apollo-whatever"
    exit 0
fi

# Now NeXT
ISNEXT=`hostinfo 2>/dev/null`
case "$ISNEXT" in
    *'NeXT Mach 3.3'*)
	echo "whatever-next-nextstep3.3"; exit 0
	;;
    *NeXT*)
	echo "whatever-next-nextstep"; exit 0
	;;
esac

# At this point we gone through all the one's
# we know of: Punt

echo "${MACHINE}-whatever-${SYSTEM}" 
exit 0
) 2>/dev/null | (

# ---------------------------------------------------------------------------
# this is where the translation occurs into SSLeay terms
# ---------------------------------------------------------------------------

# Only set CC if not supplied already
if [ -z "$CROSS_COMPILE$CC" ]; then
  GCCVER=`sh -c "gcc -dumpversion" 2>/dev/null`
  if [ "$GCCVER" != "" ]; then
    # then strip off whatever prefix egcs prepends the number with...
    # Hopefully, this will work for any future prefixes as well.
    GCCVER=`echo $GCCVER | LC_ALL=C sed 's/^[a-zA-Z]*\-//'`
    # Since gcc 3.1 gcc --version behaviour has changed.  gcc -dumpversion
    # does give us what we want though, so we use that.  We just just the
    # major and minor version numbers.
    # peak single digit before and after first dot, e.g. 2.95.1 gives 29
    GCCVER=`echo $GCCVER | sed 's/\([0-9]\)\.\([0-9]\).*/\1\2/'`
    CC=gcc
  else
    CC=cc
  fi
fi
GCCVER=${GCCVER:-0}
if [ "$SYSTEM" = "HP-UX" ];then
  # By default gcc is a ILP32 compiler (with long long == 64).
  GCC_BITS="32"
  if [ $GCCVER -ge 30 ]; then
    # PA64 support only came in with gcc 3.0.x.
    # We check if the preprocessor symbol __LP64__ is defined...
    if echo "__LP64__" | gcc -v -E -x c - 2>/dev/null | grep "^__LP64__" 2>&1 > /dev/null; then
      : # __LP64__ has slipped through, it therefore is not defined
    else
      GCC_BITS="64"
    fi
  fi
fi
if [ "$SYSTEM" = "SunOS" ]; then
  if [ $GCCVER -ge 30 ]; then
    # 64-bit ABI isn't officially supported in gcc 3.0, but it appears
    # to be working, at the very least 'make test' passes...
    if gcc -v -E -x c /dev/null 2>&1 | grep __arch64__ > /dev/null; then
      GCC_ARCH="-m64"
    else
      GCC_ARCH="-m32"
    fi
  fi
  # check for WorkShop C, expected output is "cc: blah-blah C x.x"
  CCVER=`(cc -V 2>&1) 2>/dev/null | \
  	egrep -e '^cc: .* C [0-9]\.[0-9]' | \
	sed 's/.* C \([0-9]\)\.\([0-9]\).*/\1\2/'`
  CCVER=${CCVER:-0}
  if [ $MACHINE != i86pc -a $CCVER -gt 40 ]; then
    CC=cc	# overrides gcc!!!
    if [ $CCVER -eq 50 ]; then
      echo "WARNING! Detected WorkShop C 5.0. Do make sure you have"
      echo "         patch #107357-01 or later applied."
      sleep 5
    fi
  fi
fi

if [ "${SYSTEM}-${MACHINE}" = "Linux-alpha" ]; then
  # check for Compaq C, expected output is "blah-blah C Vx.x"
  CCCVER=`(ccc -V 2>&1) 2>/dev/null | \
	egrep -e '.* C V[0-9]\.[0-9]' | \
	sed 's/.* C V\([0-9]\)\.\([0-9]\).*/\1\2/'`
  CCCVER=${CCCVER:-0}
  if [ $CCCVER -gt 60 ]; then
    CC=ccc	# overrides gcc!!! well, ccc outperforms inoticeably
		# only on hash routines and des, otherwise gcc (2.95)
		# keeps along rather tight...
  fi
fi

if [ "${SYSTEM}" = "AIX" ]; then	# favor vendor cc over gcc
    (cc) 2>&1 | grep -iv "not found" > /dev/null && CC=cc
fi

CCVER=${CCVER:-0}

# read the output of the embedded GuessOS 
read GUESSOS

echo Operating system: $GUESSOS

# now map the output into SSLeay terms ... really should hack into the
# script above so we end up with values in vars but that would take
# more time that I want to waste at the moment
case "$GUESSOS" in
  uClinux*64*)
    OUT=uClinux-dist64
	;;
  uClinux*)
    OUT=uClinux-dist
	;;
  mips2-sgi-irix)
	CPU=`(hinv -t cpu) 2>/dev/null | head -1 | sed 's/^CPU:[^R]*R\([0-9]*\).*/\1/'`
	CPU=${CPU:-0}
	if [ $CPU -ge 4000 ]; then
		options="$options -mips2"
	fi
	OUT="irix-$CC"
	;;
  mips3-sgi-irix)
	#CPU=`(hinv -t cpu) 2>/dev/null | head -1 | sed 's/^CPU:[^R]*R\([0-9]*\).*/\1/'`
	#CPU=${CPU:-0}
	#if [ $CPU -ge 5000 ]; then
	#	options="$options -mips4"
	#else
	#	options="$options -mips3"
	#fi
	OUT="irix-mips3-$CC"
	;;
  mips4-sgi-irix64)
	echo "WARNING! If you wish to build 64-bit library, then you have to"
	echo "         invoke './Configure irix64-mips4-$CC' *manually*."
	if [ "$TEST" = "false" -a -t 1 ]; then
	  echo "         You have about 5 seconds to press Ctrl-C to abort."
	  (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
	fi
        #CPU=`(hinv -t cpu) 2>/dev/null | head -1 | sed 's/^CPU:[^R]*R\([0-9]*\).*/\1/'`
        #CPU=${CPU:-0}
        #if [ $CPU -ge 5000 ]; then
        #        options="$options -mips4"
        #else
        #        options="$options -mips3"
        #fi
	OUT="irix-mips3-$CC"
	;;
  ppc-apple-rhapsody) OUT="rhapsody-ppc-cc" ;;
  ppc-apple-darwin*)
	ISA64=`(sysctl -n hw.optional.64bitops) 2>/dev/null`
	if [ "$ISA64" = "1" -a -z "$KERNEL_BITS" ]; then
	    echo "WARNING! If you wish to build 64-bit library, then you have to"
	    echo "         invoke './Configure darwin64-ppc-cc' *manually*."
	    if [ "$TEST" = "false" -a -t 1 ]; then
	      echo "         You have about 5 seconds to press Ctrl-C to abort."
	      (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
	    fi
	fi
	if [ "$ISA64" = "1" -a "$KERNEL_BITS" = "64" ]; then
	    OUT="darwin64-ppc-cc"
	else
	    OUT="darwin-ppc-cc"
	fi ;;
  i?86-apple-darwin*)
	ISA64=`(sysctl -n hw.optional.x86_64) 2>/dev/null`
	if [ "$ISA64" = "1" -a -z "$KERNEL_BITS" ]; then
	    echo "WARNING! If you wish to build 64-bit library, then you have to"
	    echo "         invoke './Configure darwin64-x86_64-cc' *manually*."
	    if [ "$TEST" = "false" -a -t 1 ]; then
	      echo "         You have about 5 seconds to press Ctrl-C to abort."
	      (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
	    fi
	fi
	if [ "$ISA64" = "1" -a "$KERNEL_BITS" = "64" ]; then
	    OUT="darwin64-x86_64-cc"
	else
	    OUT="darwin-i386-cc"
	fi ;;
  armv6+7-*-iphoneos)
	options="$options -arch%20armv6 -arch%20armv7"
	OUT="iphoneos-cross" ;;
  *-*-iphoneos)
	options="$options -arch%20${MACHINE}"
	OUT="iphoneos-cross" ;;
  alpha-*-linux2)
        ISA=`awk '/cpu model/{print$4;exit(0);}' /proc/cpuinfo`
	case ${ISA:-generic} in
	*[678])	OUT="linux-alpha+bwx-$CC" ;;
	*)	OUT="linux-alpha-$CC" ;;
	esac
	if [ "$CC" = "gcc" ]; then
	    case ${ISA:-generic} in
	    EV5|EV45)		options="$options -mcpu=ev5";;
	    EV56|PCA56)		options="$options -mcpu=ev56";;
	    *)			options="$options -mcpu=ev6";;
	    esac
	fi
	;;
  ppc64-*-linux2)
	echo "WARNING! If you wish to build 64-bit library, then you have to"
	echo "         invoke './Configure linux-ppc64' *manually*."
	if [ "$TEST" = "false" -a -t 1 ]; then
	    echo "         You have about 5 seconds to press Ctrl-C to abort."
	    (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
	fi
	OUT="linux-ppc"
	;;
  ppc-*-linux2) OUT="linux-ppc" ;;
  ppc60x-*-vxworks*) OUT="vxworks-ppc60x" ;;
  ppcgen-*-vxworks*) OUT="vxworks-ppcgen" ;;
  pentium-*-vxworks*) OUT="vxworks-pentium" ;;
  simlinux-*-vxworks*) OUT="vxworks-simlinux" ;;
  mips-*-vxworks*) OUT="vxworks-mips";;
  ia64-*-linux?) OUT="linux-ia64" ;;
  sparc64-*-linux2)
	echo "WARNING! If you *know* that your GNU C supports 64-bit/V9 ABI"
	echo "         and wish to build 64-bit library, then you have to"
	echo "         invoke './Configure linux64-sparcv9' *manually*."
	if [ "$TEST" = "false" -a -t 1 ]; then
	  echo "          You have about 5 seconds to press Ctrl-C to abort."
	  (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
	fi
	OUT="linux-sparcv9" ;;
  sparc-*-linux2)
	KARCH=`awk '/^type/{print$3;exit(0);}' /proc/cpuinfo`
	case ${KARCH:-sun4} in
	sun4u*)	OUT="linux-sparcv9" ;;
	sun4m)	OUT="linux-sparcv8" ;;
	sun4d)	OUT="linux-sparcv8" ;;
	*)	OUT="linux-generic32"; options="$options -DB_ENDIAN" ;;
	esac ;;
  parisc*-*-linux2)
	# 64-bit builds under parisc64 linux are not supported and
	# compiler is expected to generate 32-bit objects...
	CPUARCH=`awk '/cpu family/{print substr($5,1,3); exit(0);}' /proc/cpuinfo`
	CPUSCHEDULE=`awk '/^cpu.[ 	]*: PA/{print substr($3,3); exit(0);}' /proc/cpuinfo`

	# ??TODO ??  Model transformations
	# 0. CPU Architecture for the 1.1 processor has letter suffixes. We strip that off
	#    assuming no further arch. identification will ever be used by GCC.
	# 1. I'm most concerned about whether is a 7300LC is closer to a 7100 versus a 7100LC.
	# 2. The variant 64-bit processors cause concern should GCC support explicit schedulers
	#    for these chips in the future.
	#         PA7300LC -> 7100LC (1.1)
	#         PA8200   -> 8000   (2.0)
	#         PA8500   -> 8000   (2.0)
	#         PA8600   -> 8000   (2.0)

	CPUSCHEDULE=`echo $CPUSCHEDULE|sed -e 's/7300LC/7100LC/' -e 's/8.00/8000/'`
	# Finish Model transformations

	options="$options -DB_ENDIAN -mschedule=$CPUSCHEDULE -march=$CPUARCH"
	OUT="linux-generic32" ;;
  armv[1-3]*-*-linux2) OUT="linux-generic32" ;;
  armv[7-9]*-*-linux2) OUT="linux-armv4"; options="$options -march=armv7-a" ;;
  arm*-*-linux2) OUT="linux-armv4" ;;
  sh*b-*-linux2) OUT="linux-generic32"; options="$options -DB_ENDIAN" ;;
  sh*-*-linux2)  OUT="linux-generic32"; options="$options -DL_ENDIAN" ;;
  m68k*-*-linux2) OUT="linux-generic32"; options="$options -DB_ENDIAN" ;;
  s390-*-linux2) OUT="linux-generic32"; options="$options -DB_ENDIAN" ;;
  s390x-*-linux2)
	# To be uncommented when glibc bug is fixed, see Configure...
	#if egrep -e '^features.* highgprs' /proc/cpuinfo >/dev/null ; then
	#  echo "WARNING! If you wish to build \"highgprs\" 32-bit library, then you"
	#  echo "         have to invoke './Configure linux32-s390x' *manually*."
	#  if [ "$TEST" = "false" -a -t -1 ]; then
	#    echo "         You have about 5 seconds to press Ctrl-C to abort."
	#    (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
	#  fi
	#fi
	OUT="linux64-s390x"
	;;
  x86_64-*-linux?) OUT="linux-x86_64" ;;
  *86-*-linux2) OUT="linux-elf"
	if [ "$GCCVER" -gt 28 ]; then
          if grep '^model.*Pentium' /proc/cpuinfo >/dev/null ; then
	    options="$options -march=pentium"
          fi
          if grep '^model.*Pentium Pro' /proc/cpuinfo >/dev/null ; then
	    options="$options -march=pentiumpro"
          fi
          if grep '^model.*K6' /proc/cpuinfo >/dev/null ; then
	    options="$options -march=k6"
          fi
        fi ;;
  *-*-linux1) OUT="linux-aout" ;;
  *-*-linux2) OUT="linux-generic32" ;;
  sun4[uv]*-*-solaris2)
	OUT="solaris-sparcv9-$CC"
	ISA64=`(isalist) 2>/dev/null | grep sparcv9`
	if [ "$ISA64" != "" -a "$KERNEL_BITS" = "" ]; then
	    if [ "$CC" = "cc" -a $CCVER -ge 50 ]; then
		echo "WARNING! If you wish to build 64-bit library, then you have to"
		echo "         invoke './Configure solaris64-sparcv9-cc' *manually*."
		if [ "$TEST" = "false" -a -t 1 ]; then
		  echo "         You have about 5 seconds to press Ctrl-C to abort."
		  (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
		fi
	    elif [ "$CC" = "gcc" -a "$GCC_ARCH" = "-m64" ]; then
		# $GCC_ARCH denotes default ABI chosen by compiler driver
		# (first one found on the $PATH). I assume that user
		# expects certain consistency with the rest of his builds
		# and therefore switch over to 64-bit. <appro>
		OUT="solaris64-sparcv9-gcc"
		echo "WARNING! If you wish to build 32-bit library, then you have to"
		echo "         invoke './Configure solaris-sparcv9-gcc' *manually*."
		if [ "$TEST" = "false" -a -t 1 ]; then
		  echo "         You have about 5 seconds to press Ctrl-C to abort."
		  (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
		fi
	    elif [ "$GCC_ARCH" = "-m32" ]; then
		echo "NOTICE! If you *know* that your GNU C supports 64-bit/V9 ABI"
		echo "        and wish to build 64-bit library, then you have to"
		echo "        invoke './Configure solaris64-sparcv9-gcc' *manually*."
		if [ "$TEST" = "false" -a -t 1 ]; then
		  echo "         You have about 5 seconds to press Ctrl-C to abort."
		  (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
		fi
	    fi
	fi
	if [ "$ISA64" != "" -a "$KERNEL_BITS" = "64" ]; then
	    OUT="solaris64-sparcv9-$CC"
	fi
	;;
  sun4m-*-solaris2)	OUT="solaris-sparcv8-$CC" ;;
  sun4d-*-solaris2)	OUT="solaris-sparcv8-$CC" ;;
  sun4*-*-solaris2)	OUT="solaris-sparcv7-$CC" ;;
  *86*-*-solaris2)
	ISA64=`(isalist) 2>/dev/null | grep amd64`
	if [ "$ISA64" != "" -a ${KERNEL_BITS:-64} -eq 64 ]; then
	    OUT="solaris64-x86_64-$CC"
	else
	    OUT="solaris-x86-$CC"
	    if [ `uname -r | sed -e 's/5\.//'` -lt 10 ]; then
		options="$options no-sse2"
	    fi
	fi
	;;
  *-*-sunos4)		OUT="sunos-$CC" ;;

  *86*-*-bsdi4)		OUT="BSD-x86-elf"; options="$options no-sse2 -ldl" ;;
  alpha*-*-*bsd*)	OUT="BSD-generic64"; options="$options -DL_ENDIAN" ;;
  powerpc64-*-*bsd*)	OUT="BSD-generic64"; options="$options -DB_ENDIAN" ;;
  sparc64-*-*bsd*)	OUT="BSD-sparc64" ;;
  ia64-*-*bsd*)		OUT="BSD-ia64" ;;
  amd64-*-*bsd*)	OUT="BSD-x86_64" ;;
  *86*-*-*bsd*)		# mimic ld behaviour when it's looking for libc...
			if [ -L /usr/lib/libc.so ]; then	# [Free|Net]BSD
			    libc=/usr/lib/libc.so
			else					# OpenBSD
			    # ld searches for highest libc.so.* and so do we
			    libc=`(ls /usr/lib/libc.so.* /lib/libc.so.* | tail -1) 2>/dev/null`
			fi
			case "`(file -L $libc) 2>/dev/null`" in
			*ELF*)	OUT="BSD-x86-elf" ;;
			*)	OUT="BSD-x86"; options="$options no-sse2" ;;
			esac ;;
  *-*-*bsd*)		OUT="BSD-generic32" ;;

  *-*-osf)		OUT="osf1-alpha-cc" ;;
  *-*-tru64)		OUT="tru64-alpha-cc" ;;
  *-*-[Uu]nix[Ww]are7)
	if [ "$CC" = "gcc" ]; then
	  OUT="unixware-7-gcc" ; options="$options no-sse2"
	else    
	  OUT="unixware-7" ; options="$options no-sse2 -D__i386__"
	fi
	;;
  *-*-[Uu]nix[Ww]are20*) OUT="unixware-2.0"; options="$options no-sse2 no-sha512" ;;
  *-*-[Uu]nix[Ww]are21*) OUT="unixware-2.1"; options="$options no-sse2 no-sha512" ;;
  *-*-vos)
	options="$options no-threads no-shared no-asm no-dso"
	EXE=".pm"
	OUT="vos-$CC" ;;
  BS2000-siemens-sysv4) OUT="BS2000-OSD" ;;
  RM*-siemens-sysv4) OUT="ReliantUNIX" ;;
  *-siemens-sysv4) OUT="SINIX" ;;
  *-hpux1*)
	if [ $CC = "gcc" -a $GCC_BITS = "64" ]; then
	    OUT="hpux64-parisc2-gcc"
	fi
	[ "$KERNEL_BITS" ] || KERNEL_BITS=`(getconf KERNEL_BITS) 2>/dev/null`
	KERNEL_BITS=${KERNEL_BITS:-32}
	CPU_VERSION=`(getconf CPU_VERSION) 2>/dev/null`
	CPU_VERSION=${CPU_VERSION:-0}
	# See <sys/unistd.h> for further info on CPU_VERSION.
	if   [ $CPU_VERSION -ge 768 ]; then	# IA-64 CPU
	     if [ $KERNEL_BITS -eq 64 -a "$CC" = "cc" ]; then
	        OUT="hpux64-ia64-cc"
             else
	        OUT="hpux-ia64-cc"
             fi
	elif [ $CPU_VERSION -ge 532 ]; then	# PA-RISC 2.x CPU
	     OUT=${OUT:-"hpux-parisc2-${CC}"}
	     if [ $KERNEL_BITS -eq 64 -a "$CC" = "cc" ]; then
		echo "WARNING! If you wish to build 64-bit library then you have to"
		echo "         invoke './Configure hpux64-parisc2-cc' *manually*."
		if [ "$TEST" = "false" -a -t 1 ]; then
		  echo "         You have about 5 seconds to press Ctrl-C to abort."
		  (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
		fi
	     fi
	elif [ $CPU_VERSION -ge 528 ]; then	# PA-RISC 1.1+ CPU
	     OUT="hpux-parisc-${CC}"
	elif [ $CPU_VERSION -ge 523 ]; then	# PA-RISC 1.0 CPU
	     OUT="hpux-parisc-${CC}"
	else					# Motorola(?) CPU
	     OUT="hpux-$CC"
	fi
	options="$options -D_REENTRANT" ;;
  *-hpux)	OUT="hpux-parisc-$CC" ;;
  *-aix)
	[ "$KERNEL_BITS" ] || KERNEL_BITS=`(getconf KERNEL_BITMODE) 2>/dev/null`
	KERNEL_BITS=${KERNEL_BITS:-32}
	OBJECT_MODE=${OBJECT_MODE:-32}
	if [ "$CC" = "gcc" ]; then
	    OUT="aix-gcc"
          if [ $OBJECT_MODE -eq 64 ]; then
            echo 'Your $OBJECT_MODE was found to be set to 64'
            OUT="aix64-gcc"
          fi
	elif [ $OBJECT_MODE -eq 64 ]; then
	    echo 'Your $OBJECT_MODE was found to be set to 64' 
	    OUT="aix64-cc"
	else
	    OUT="aix-cc"
	    if [ $KERNEL_BITS -eq 64 ]; then
		echo "WARNING! If you wish to build 64-bit kit, then you have to"
		echo "         invoke './Configure aix64-cc' *manually*."
		if [ "$TEST" = "false" -a -t 1 ]; then
		    echo "         You have ~5 seconds to press Ctrl-C to abort."
		    (trap "stty `stty -g`" 2 0; stty -icanon min 0 time 50; read waste) <&1
		fi
	    fi
	fi
	if (lsattr -E -O -l `lsdev -c processor|awk '{print$1;exit}'` | grep -i powerpc) >/dev/null 2>&1; then
	    :	# this applies even to Power3 and later, as they return PowerPC_POWER[345]
	else
	    options="$options no-asm"
	fi
	;;
  # these are all covered by the catchall below
  # *-dgux) OUT="dgux" ;;
  mips-sony-newsos4) OUT="newsos4-gcc" ;;
  *-*-cygwin_pre1.3) OUT="Cygwin-pre1.3" ;;
  *-*-cygwin) OUT="Cygwin" ;;
  t3e-cray-unicosmk) OUT="cray-t3e" ;;
  j90-cray-unicos) OUT="cray-j90" ;;
  nsr-tandem-nsk) OUT="tandem-c89" ;;
  beos-*) OUT="$GUESSOS" ;;
  x86pc-*-qnx6) OUT="QNX6-i386" ;;
  *-*-qnx6) OUT="QNX6" ;;
  x86-*-android|i?86-*-android) OUT="android-x86" ;;
  armv[7-9]*-*-android) OUT="android-armv7" ;;
  *) OUT=`echo $GUESSOS | awk -F- '{print $3}'`;;
esac

# NB: This atalla support has been superceded by the ENGINE support
# That contains its own header and definitions anyway. Support can
# be enabled or disabled on any supported platform without external
# headers, eg. by adding the "hw-atalla" switch to ./config or
# perl Configure
#
# See whether we can compile Atalla support
#if [ -f /usr/include/atasi.h ]
#then
#  options="$options -DATALLA"
#fi

if expr "$options" : '.*no\-asm' > /dev/null; then :; else
  sh -c "$CROSS_COMPILE${CC:-gcc} -Wa,--help -c -o /tmp/null.$$.o -x assembler /dev/null && rm /tmp/null.$$.o" 2>&1 | \
  grep \\--noexecstack >/dev/null && \
  options="$options -Wa,--noexecstack"
fi

# gcc < 2.8 does not support -march=ultrasparc
if [ "$OUT" = solaris-sparcv9-gcc -a $GCCVER -lt 28 ]
then
  echo "WARNING! Falling down to 'solaris-sparcv8-gcc'."
  echo "         Upgrade to gcc-2.8 or later."
  sleep 5
  OUT=solaris-sparcv8-gcc
fi
if [ "$OUT" = "linux-sparcv9" -a $GCCVER -lt 28 ]
then
  echo "WARNING! Falling down to 'linux-sparcv8'."
  echo "         Upgrade to gcc-2.8 or later."
  sleep 5
  OUT=linux-sparcv8
fi

case "$GUESSOS" in
  i386-*) options="$options 386" ;;
esac

for i in aes bf camellia cast des dh dsa ec hmac idea md2 md5 mdc2 rc2 rc4 rc5 ripemd rsa seed sha
do
  if [ ! -d crypto/$i ]
  then
    options="$options no-$i"
  fi
done

# Discover Kerberos 5 (since it's still a prototype, we don't
# do any guesses yet, that's why this section is commented away.
#if [ -d /usr/kerberos ]; then
#    krb5_dir=/usr/kerberos
#    if [ \( -f $krb5_dir/lib/libgssapi_krb5.a -o -f $krb5_dir/lib/libgssapi_krb5.so* \)\
#	-a \( -f $krb5_dir/lib/libkrb5.a -o -f $krb5_dir/lib/libkrb5.so* \)\
#	-a \( -f $krb5_dir/lib/libcom_err.a -o -f $krb5_dir/lib/libcom_err.so* \)\
#	-a \( -f $krb5_dir/lib/libk5crypto.a -o -f $krb5_dir/lib/libk5crypto.so* \)\
#	-a \( -f $krb5_dir/include/krb5.h \) ]; then
#	options="$options --with-krb5-flavor=MIT"
#    fi
#elif [ -d /usr/heimdal ]; then
#    krb5_dir=/usr/heimdal
#    if [ \( -f $krb5_dir/lib/libgssapi.a -o -f $krb5_dir/lib/libgssapi.so* \)\
#	-a \( -f $krb5_dir/lib/libkrb5.a -o -f $krb5_dir/lib/libkrb5.so* \)\
#	-a \( -f $krb5_dir/lib/libcom_err.a -o -f $krb5_dir/lib/libcom_err.so* \)\
#	-a \( -f $krb5_dir/include/krb5.h \) ]; then
#	options="$options --with-krb5-flavor=Heimdal"
#    fi
#fi

if [ -z "$OUT" ]; then
  OUT="$CC"
fi

if [ ".$PERL" = . ] ; then
	for i in . `echo $PATH | sed 's/:/ /g'`; do
		if [ -f "$i/perl5$EXE" ] ; then
			PERL="$i/perl5$EXE"
			break;
		fi;
	done
fi

if [ ".$PERL" = . ] ; then
	for i in . `echo $PATH | sed 's/:/ /g'`; do
		if [ -f "$i/perl$EXE" ] ; then
			if "$i/perl$EXE" -e 'exit($]<5.0)'; then
				PERL="$i/perl$EXE"
				break;
			fi;
		fi;
	done
fi

if [ ".$PERL" = . ] ; then
	echo "You need Perl 5."
	exit 1
fi

# run Configure to check to see if we need to specify the 
# compiler for the platform ... in which case we add it on
# the end ... otherwise we leave it off

$PERL ./Configure LIST | grep "$OUT-$CC" > /dev/null
if [ $? = "0" ]; then
  OUT="$OUT-$CC"
fi

OUT="$PREFIX$OUT"

$PERL ./Configure LIST | grep "$OUT" > /dev/null
if [ $? = "0" ]; then
  echo Configuring for $OUT

  if [ "$TEST" = "true" ]; then
    echo $PERL ./Configure $OUT $options
  else
    $PERL ./Configure $OUT $options
  fi
else
  echo "This system ($OUT) is not supported. See file INSTALL for details."
fi
)
