
INSTALLATION ON THE NETWARE PLATFORM
------------------------------------

Notes about building OpenSSL for NetWare.


BUILD PLATFORM:
---------------
The build scripts (batch files, perl scripts, etc) have been developed and
tested on W2K.  The scripts should run fine on other Windows platforms
(NT, Win9x, WinXP) but they have not been tested.  They may require some
modifications.


Supported NetWare Platforms - NetWare 5.x, NetWare 6.x:
-------------------------------------------------------
OpenSSL can either use the WinSock interfaces introduced in NetWare 5,
or the BSD socket interface.  Previous versions of NetWare, 4.x and 3.x,
are only supported if OpenSSL is build for CLIB and BSD sockets;
WinSock builds only support NetWare 5 and up.

On NetWare there are two c-runtime libraries.  There is the legacy CLIB 
interfaces and the newer LIBC interfaces.  Being ANSI-C libraries, the 
functionality in CLIB and LIBC is similar but the LIBC interfaces are built 
using Novell Kernal Services (NKS) which is designed to leverage 
multi-processor environments.

The NetWare port of OpenSSL can be configured to build using CLIB or LIBC.
The CLIB build was developed and tested using NetWare 5.0 sp6.0a.  The LIBC 
build was developed and tested using the NetWare 6.0 FCS.  

The necessary LIBC functionality ships with NetWare 6.  However, earlier 
NetWare 5.x versions will require updates in order to run the OpenSSL LIBC
build (NetWare 5.1 SP8 is known to work).

As of June 2005, the LIBC build can be configured to use BSD sockets instead
of WinSock sockets. Call Configure (usually through netware\build.bat) using
a target of "netware-libc-bsdsock" instead of "netware-libc".

As of June 2007, support for CLIB and BSD sockets is also now available
using a target of "netware-clib-bsdsock" instead of "netware-clib";
also gcc builds are now supported on both Linux and Win32 (post 0.9.8e).

REQUIRED TOOLS:
---------------
Based upon the configuration and build options used, some or all of the
following tools may be required:

* Perl for Win32 - required (http://www.activestate.com/ActivePerl)
   Used to run the various perl scripts on the build platform.

* Perl 5.8.0 for NetWare v3.20 (or later) - required 
   (http://developer.novell.com) Used to run the test script on NetWare 
   after building.

* Compiler / Linker - required:
   Metrowerks CodeWarrior PDK 2.1 (or later) for NetWare (commercial):
      Provides command line tools used for building.
      Tools:
      mwccnlm.exe  - C/C++ Compiler for NetWare
      mwldnlm.exe  - Linker for NetWare
      mwasmnlm.exe - x86 assembler for NetWare (if using assembly option)

   gcc / nlmconv Cross-Compiler, available from Novell Forge (free):
         http://forge.novell.com/modules/xfmod/project/?aunixnw

* Assemblers - optional:
   If you intend to build using the assembly options you will need an
   assembler.  Work has been completed to support two assemblers, Metrowerks
   and NASM.  However, during development, a bug was found in the Metrowerks
   assembler which generates incorrect code.  Until this problem is fixed,
   the Metrowerks assembler cannot be used.

   mwasmnlm.exe - Metrowerks x86 assembler - part of CodeWarrior tools.
         (version 2.2 Built Aug 23, 1999 - not useable due to code
          generation bug)

   nasmw.exe - Netwide Assembler NASM
         version 0.98 was used in development and testing

* Make Tool - required:
   In order to build you will need a make tool.  Two make tools are
   supported, GNU make (gmake.exe) or Microsoft nmake.exe.

   make.exe - GNU make for Windows (version 3.75 used for development)
         http://gnuwin32.sourceforge.net/packages/make.htm

   nmake.exe - Microsoft make (Version 6.00.8168.0 used for development)
         http://support.microsoft.com/kb/132084/EN-US/

* Novell Developer Kit (NDK) - required: (http://developer.novell.com)

   CLIB - BUILDS:

      WinSock2 Developer Components for NetWare:
         For initial development, the October 27, 2000 version was used.
         However, future versions should also work.

         NOTE:  The WinSock2 components include headers & import files for
         NetWare, but you will also need the winsock2.h and supporting
         headers (pshpack4.h, poppack.h, qos.h) delivered in the
         Microsoft SDK.  Note: The winsock2.h support headers may change
         with various versions of winsock2.h.  Check the dependencies
         section on the NDK WinSock2 download page for the latest
         information on dependencies. These components are unsupported by
         Novell. They are provided as a courtesy, but it is strongly
         suggested that all development be done using LIBC, not CLIB.

         As of June 2005, the WinSock2 components are available at:
         http://forgeftp.novell.com//ws2comp/


      NLM and NetWare libraries for C (including CLIB and XPlat):
         If you are going to build a CLIB version of OpenSSL, you will
         need the CLIB headers and imports.  The March, 2001 NDK release or 
         later is recommended.

         Earlier versions should work but haven't been tested.  In recent
         versions the import files have been consolidated and function
         names moved.  This means you may run into link problems
         (undefined symbols) when using earlier versions.   The functions
         are available in earlier versions, but you will have to modifiy
         the make files to include additional import files (see
         openssl\util\pl\netware.pl).


   LIBC - BUILDS:
   
      Libraries for C (LIBC) - LIBC headers and import files
         If you are going to build a LIBC version of OpenSSL, you will
         need the LIBC headers and imports.  The March 14, 2002 NDK release or
         later is required.  
         
         NOTE: The LIBC SDK includes the necessary WinSock2 support.
         It is not necessary to download the WinSock2 NDK when building for
         LIBC. The LIBC SDK also includes the appropriate BSD socket support
         if configuring to use BSD sockets.


BUILDING:
---------
Before building, you will need to set a few environment variables.  You can
set them manually or you can modify the "netware\set_env.bat" file.

The set_env.bat file is a template you can use to set up the path
and environment variables you will need to build.  Modify the
various lines to point to YOUR tools and run set_env.bat.

   netware\set_env.bat <target> [compiler]

      target        - "netware-clib" - CLIB NetWare build
                    - "netware-libc" - LIBC NetWare build

      compiler      - "gnuc"         - GNU GCC Compiler
                    - "codewarrior"  - MetroWerks CodeWarrior (default)

If you don't use set_env.bat, you will need to set up the following
environment variables:

   PATH - Set PATH to point to the tools you will use.

   INCLUDE - The location of the NDK include files.
         
            CLIB ex: set INCLUDE=c:\ndk\nwsdk\include\nlm
            LIBC ex: set INCLUDE=c:\ndk\libc\include

   PRELUDE - The absolute path of the prelude object to link with.  For
            a CLIB build it is recommended you use the "clibpre.o" files shipped
            with the Metrowerks PDK for NetWare.  For a LIBC build you should 
            use the "libcpre.o" file delivered with the LIBC NDK components.

            CLIB ex: set PRELUDE=c:\ndk\nwsdk\imports\clibpre.o
            LIBC ex: set PRELUDE=c:\ndk\libc\imports\libcpre.o

   IMPORTS - The locaton of the NDK import files.

            CLIB ex: set IMPORTS=c:\ndk\nwsdk\imports
            LIBC ex: set IMPORTS=c:\ndk\libc\imports


In order to build, you need to run the Perl scripts to configure the build
process and generate a make file.  There is a batch file,
"netware\build.bat", to automate the process.

Build.bat runs the build configuration scripts and generates a make file.
If an assembly option is specified, it also runs the scripts to generate 
the assembly code.  Always run build.bat from the "openssl" directory.

   netware\build [target] [debug opts] [assembly opts] [configure opts]

      target        - "netware-clib" - CLIB NetWare build (WinSock Sockets)
                    - "netware-clib-bsdsock" - CLIB NetWare build (BSD Sockets)
                    - "netware-libc" - LIBC NetWare build (WinSock Sockets)
                    - "netware-libc-bsdsock" - LIBC NetWare build (BSD Sockets)
 
      debug opts    - "debug"  - build debug

      assembly opts - "nw-mwasm" - use Metrowerks assembler
                      "nw-nasm"  - use NASM assembler
                      "no-asm"   - don't use assembly

      configure opts- all unrecognized arguments are passed to the
                      perl 'configure' script. See that script for
                      internal documentation regarding options that
                      are available.

   examples:

      CLIB build, debug, without assembly:
         netware\build.bat netware-clib debug no-asm

      LIBC build, non-debug, using NASM assembly, add mdc2 support:
         netware\build.bat netware-libc nw-nasm enable-mdc2

      LIBC build, BSD sockets, non-debug, without assembly:
         netware\build.bat netware-libc-bsdsock no-asm

Running build.bat generates a make file to be processed by your make 
tool (gmake or nmake):

   CLIB ex: gmake -f netware\nlm_clib_dbg.mak 
   LIBC ex: gmake -f netware\nlm_libc.mak 
   LIBC ex: gmake -f netware\nlm_libc_bsdsock.mak 


You can also run the build scripts manually if you do not want to use the
build.bat file.  Run the following scripts in the "\openssl"
subdirectory (in the order listed below):

   perl configure no-asm [other config opts] [netware-clib|netware-libc|netware-libc-bsdsock]
      configures no assembly build for specified netware environment
      (CLIB or LIBC).

   perl util\mkfiles.pl >MINFO
      generates a listing of source files (used by mk1mf)

   perl util\mk1mf.pl no-asm [other config opts] [netware-clib|netware-libc|netware-libc-bsdsock >netware\nlm.mak
      generates the makefile for NetWare

   gmake -f netware\nlm.mak
      build with the make tool (nmake.exe also works)

NOTE:  If you are building using the assembly option, you must also run the
various Perl scripts to generate the assembly files.  See build.bat
for an example of running the various assembly scripts.  You must use the
"no-asm" option to build without assembly.  The configure and mk1mf scripts
also have various other options.  See the scripts for more information.


The output from the build is placed in the following directories:

   CLIB Debug build:
      out_nw_clib.dbg     - static libs & test nlm(s)
      tmp_nw_clib.dbg     - temporary build files
      outinc_nw_clib      - necessary include files

   CLIB Non-debug build:
      out_nw_clib         - static libs & test nlm(s)
      tmp_nw_clib         - temporary build files
      outinc_nw_clib      - necesary include files

   LIBC Debug build:
      out_nw_libc.dbg     - static libs & test nlm(s)
      tmp_nw_libc.dbg     - temporary build files
      outinc_nw_libc      - necessary include files

   LIBC Non-debug build:
      out_nw_libc         - static libs & test nlm(s)
      tmp_nw_libc         - temporary build files
      outinc_nw_libc      - necesary include files


TESTING:
--------
The build process creates the OpenSSL static libs ( crypto.lib, ssl.lib,
rsaglue.lib ) and several test programs.  You should copy the test programs
to your NetWare server and run the tests.

The batch file "netware\cpy_tests.bat" will copy all the necessary files
to your server for testing.  In order to run the batch file, you need a
drive mapped to your target server.  It will create an "OpenSSL" directory
on the drive and copy the test files to it.  CAUTION: If a directory with the
name of "OpenSSL" already exists, it will be deleted.

To run cpy_tests.bat:

   netware\cpy_tests [output directory] [NetWare drive]

      output directory - "out_nw_clib.dbg", "out_nw_libc", etc.
      NetWare drive    - drive letter of mapped drive

      CLIB ex: netware\cpy_tests out_nw_clib m:
      LIBC ex: netware\cpy_tests out_nw_libc m:


The Perl script, "do_tests.pl", in the "OpenSSL" directory on the server
should be used to execute the tests.  Before running the script, make sure
your SEARCH PATH includes the "OpenSSL" directory.  For example, if you
copied the files to the "sys:" volume you use the command:

   SEARCH ADD SYS:\OPENSSL


To run do_tests.pl type (at the console prompt):

   perl \openssl\do_tests.pl [options]

      options:
         -p    - pause after executing each test

The do_tests.pl script generates a log file "\openssl\test_out\tests.log"
which should be reviewed for errors.  Any errors will be denoted by the word
"ERROR" in the log.

DEVELOPING WITH THE OPENSSL SDK:
--------------------------------
Now that everything is built and tested, you are ready to use the OpenSSL
libraries in your development.

There is no real installation procedure, just copy the static libs and
headers to your build location.  The libs (crypto.lib & ssl.lib) are
located in the appropriate "out_nw_XXXX" directory 
(out_nw_clib, out_nw_libc, etc).  

The headers are located in the appropriate "outinc_nw_XXX" directory 
(outinc_nw_clib, outinc_nw_libc).  

One suggestion is to create the following directory 
structure for the OpenSSL SDK:

   \openssl
      |- bin
      |   |- openssl.nlm
      |   |- (other tests you want)
      |
      |- lib
      |   | - crypto.lib
      |   | - ssl.lib
      |
      |- include
      |   | - openssl
      |   |    | - (all the headers in "outinc_nw\openssl")


The program "openssl.nlm" can be very useful.  It has dozens of
options and you may want to keep it handy for debugging, testing, etc.

When building your apps using OpenSSL, define "NETWARE".  It is needed by
some of the OpenSSL headers.  One way to do this is with a compile option,
for example "-DNETWARE".



NOTES:
------

Resource leaks in Tests
------------------------
Some OpenSSL tests do not clean up resources and NetWare reports
the resource leaks when the tests unload.  If this really bugs you,
you can stop the messages by setting the developer option off at the console
prompt (set developer option = off).  Or better yet, fix the tests to
clean up the resources!


Multi-threaded Development
---------------------------
The NetWare version of OpenSSL is thread-safe, however multi-threaded
applications must provide the necessary locking function callbacks.  This
is described in doc\threads.doc.  The file "openssl-x.x.x\crypto\threads\mttest.c"
is a multi-threaded test program and demonstrates the locking functions.


What is openssl2.nlm?
---------------------
The openssl program has numerous options and can be used for many different
things.  Many of the options operate in an interactive mode requiring the
user to enter data.  Because of this, a default screen is created for the
program.  However, when running the test script it is not desirable to
have a seperate screen.  Therefore, the build also creates openssl2.nlm.
Openssl2.nlm is functionally identical but uses the console screen.
Openssl2 can be used when a non-interactive mode is desired.

NOTE:  There are may other possibilities (command line options, etc)
which could have been used to address the screen issue.  The openssl2.nlm
option was chosen because it impacted only the build not the code.


Why only static libraries?
--------------------------
Globals, globals, and more globals.  The OpenSSL code uses many global
variables that are allocated and initialized when used for the first time.

On NetWare, most applications (at least historically) run in the kernel.
When running in the kernel, there is one instance of global variables.
For regular application type NLM(s) this isn't a problem because they are
the only ones using the globals.  However, for a library NLM (an NLM which
exposes functions and has no threads of execution), the globals cause
problems.  Applications could inadvertently step on each other if they
change some globals.  Even worse, the first application that triggers a
global to be allocated and initialized has the allocated memory charged to
itself.  Now when that application unloads, NetWare will clean up all the
applicaton's memory.  The global pointer variables inside OpenSSL now
point to freed memory.  An abend waiting to happen!

To work correctly in the kernel, library NLM(s) that use globals need to
provide a set of globals (instance data) for each application.  Another
option is to require the library only be loaded in a protected address
space along with the application using it.

Modifying the OpenSSL code to provide a set of globals (instance data) for
each application isn't technically difficult, but due to the large number
globals it would require substantial code changes and it wasn't done.  Hence,
the build currently only builds static libraries which are then linked
into each application.

NOTE:  If you are building a library NLM that uses the OpenSSL static
libraries, you will still have to deal with the global variable issue.
This is because when you link in the OpenSSL code you bring in all the
globals.  One possible solution for the global pointer variables is to
register memory functions with OpenSSL which allocate memory and charge it
to your library NLM (see the function CRYPTO_set_mem_functions).  However,
be aware that now all memory allocated by OpenSSL is charged to your NLM.


CodeWarrior Tools and W2K
---------------------------
There have been problems reported with the CodeWarrior Linker
(mwldnlm.exe) in the PDK 2.1 for NetWare when running on Windows 2000.  The
problems cause the link step to fail.  The only work around is to obtain an
updated linker from Metrowerks.  It is expected Metrowerks will release
PDK 3.0 (in beta testing at this time - May, 2001) in the near future which
will fix these problems.


Makefile "vclean"
------------------
The generated makefile has a "vclean" target which cleans up the build
directories.  If you have been building successfully and suddenly
experience problems, use "vclean" (gmake -f netware\nlm_xxxx.mak vclean) and retry.


"Undefined Symbol" Linker errors
--------------------------------
There have been linker errors reported when doing a CLIB build.  The problems
occur because some versions of the CLIB SDK import files inadvertently 
left out some symbols.  One symbol in particular is "_lrotl".  The missing
functions are actually delivered in the binaries, but they were left out of
the import files.  The issues should be fixed in the September 2001 release 
of the NDK.  If you experience the problems you can temporarily
work around it by manually adding the missing symbols to your version of 
"clib.imp".

