 
 INSTALLATION FOR THE WINDOWS CE PLATFORM
 ----------------------------------------

 Building OpenSSL for Windows CE requires the following external tools:

  * Microsoft eMbedded Visual C++ 3.0 or later
  * Appropriate SDK might be required
  * Perl for Win32 [commonly recommended ActiveState Perl is available
    from http://www.activestate.com/Products/ActivePerl/]

  * wcecompat compatibility library available at
    http://www.essemer.com.au/windowsce/
  * Optionally ceutils for running automated tests (same location)

  _or_

  * PocketConsole driver and PortSDK available at
    http://www.symbolictools.de/public/pocketconsole/
  * CMD command interpreter (same location)

 As Windows CE support in OpenSSL relies on 3rd party compatibility
 library, it's appropriate to check corresponding URL for updates. For
 example if you choose wcecompat, note that as for the moment of this
 writing version 1.2 is available and actually required for WCE 4.2
 and newer platforms. All wcecompat issues should be directed to
 www.essemer.com.au.

 Why compatibility library at all? The C Runtime Library implementation
 for Windows CE that is included with Microsoft eMbedded Visual C++ is
 incomplete and in some places incorrect.  Compatibility library plugs
 the holes and tries to bring the Windows CE CRT to [more] usable level.
 Most gaping hole in CRT is support for stdin/stdout/stderr IO, which
 proposed compatibility libraries solve in two different ways: wcecompat
 redirects IO to active sync link, while PortSDK - to NT-like console
 driver on the handheld itself.

 Building
 --------

 Setup the eMbedded Visual C++ environment.  There are batch files for doing
 this installed with eVC++.  For an ARM processor, for example, execute:

 > "C:\Program Files\Microsoft eMbedded Tools\EVC\WCE300\BIN\WCEARM.BAT"

 Next pick compatibility library according to your preferences.

 1. To choose wcecompat set up WCECOMPAT environment variable pointing
    at the location of wcecompat tree "root":

    > set WCECOMPAT=C:\wcecompat
    > set PORTSDK_LIBPATH=

 2. To choose PortSDK set up PORTSDK_LIBPATH to point at hardware-
    specific location where your portlib.lib is installed:

    > set PORTSDK_LIBPATH=C:\PortSDK\lib\ARM
    > set WCECOMPAT=

 Note that you may not set both variables.

 Next you should run Configure:

 > perl Configure VC-CE

 Next you need to build the Makefiles:

 > ms\do_ms

 If you get errors about things not having numbers assigned then check the
 troubleshooting section in INSTALL.W32: you probably won't be able to compile
 it as it stands.

 Then from the VC++ environment at a prompt do:

   > nmake -f ms\cedll.mak

 [note that static builds are not supported under CE]

 If all is well it should compile and you will have some DLLs and executables
 in out32dll*. 

 <<< everyting below needs revision in respect to wcecompat vs. PortSDK >>>

 If you want
 to try the tests then make sure the ceutils are in the path and do:
 
 > cd out32
 > ..\ms\testce

 This will copy each of the test programs to the Windows CE device and execute
 them, displaying the output of the tests on this computer.  The output should
 look similar to the output produced by running the tests for a regular Windows
 build.

