SSLeay 0.8.3e 30-Sep-1997
built on Tue Sep 30 14:52:58 EST 1997
options:bn(64,32) md2(int) rc4(idx,int) des(idx,cisc,4,long) idea(int) blowfish(ptr2)
C flags:cl /W3 /WX /G5 /Ox /O2 /Ob2 /Gs0 /GF /Gy /nologo -DWIN32 -DL_ENDIAN -DX86_ASM
The 'numbers' are in 1000s of bytes per second processed.
type              8 bytes     64 bytes    256 bytes   1024 bytes   8192 bytes
md2                92.99k      257.59k      348.16k      381.47k      392.14k
mdc2              223.77k      235.30k      237.15k      236.77k      237.29k
md5               862.53k     4222.17k     7842.75k     9925.00k    10392.23k
sha               491.34k     2338.61k     4062.28k     4986.10k     5307.90k
sha1              494.38k     2234.94k     3838.83k     4679.58k     4980.18k
rc4              6338.10k     7489.83k     7676.25k     7698.80k     7631.56k
des cbc          1654.17k     1917.66k     1961.05k     1968.05k     1960.69k
des ede3          691.17k      739.42k      744.13k      745.82k      741.40k
idea cbc          788.46k      870.33k      879.16k      881.38k      879.90k
rc2 cbc           794.44k      859.63k      868.24k      869.68k      867.45k
blowfish cbc     2379.88k     3017.48k     3116.12k     3134.76k     3070.50k
                  sign    verify
rsa  512 bits   0.0204s   0.0027s
rsa 1024 bits   0.1074s   0.0032s
rsa 2048 bits   0.6890s   0.0246s
rsa 4096 bits   5.0180s   0.0911s
                  sign    verify
dsa  512 bits   0.0201s   0.0376s
dsa 1024 bits   0.0608s   0.1193s
dsa 2048 bits   0.2133s   0.4294s
