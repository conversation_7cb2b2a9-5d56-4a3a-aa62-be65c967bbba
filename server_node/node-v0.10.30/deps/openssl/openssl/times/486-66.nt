SSLeay 0.6.1 02-Jul-1996
built on Fri Jul 10 09:53:15 EST 1996
options:bn(64,32) md2(int) rc4(idx,int) des(idx,long) idea(int)
C flags:cl /W3 /WX /G5 /Ox /O2 /Ob2 /Gs0 /nologo -DWIN32 -DL_ENDIAN /MD
The 'numbers' are in 1000s of bytes per second processed.
type           8 bytes     64 bytes    256 bytes   1024 bytes   8192 bytes
md2             38.27k      107.28k      145.43k      159.60k      164.15k
md5            399.00k     1946.13k     3610.80k     4511.94k     4477.27k
sha            182.04k      851.26k     1470.65k     1799.20k     1876.48k
sha1           151.83k      756.55k     1289.76k     1567.38k     1625.70k
rc4           1853.92k     2196.25k     2232.91k     2241.31k     2152.96k
des cfb        360.58k      382.69k      384.94k      386.07k      377.19k
des cbc        376.10k      431.87k      436.32k      437.78k      430.45k
des ede3       152.55k      160.38k      161.51k      161.33k      159.98k
idea cfb       245.59k      255.60k      256.65k      257.16k      254.61k
idea cbc       257.16k      276.12k      279.05k      279.11k      276.70k
rc2 cfb        280.25k      293.49k      294.74k      294.15k      291.47k
rc2 cbc        295.47k      321.57k      324.76k      324.76k      320.00k
rsa  512 bits   0.084s
rsa 1024 bits   0.495s
rsa 2048 bits   3.435s

