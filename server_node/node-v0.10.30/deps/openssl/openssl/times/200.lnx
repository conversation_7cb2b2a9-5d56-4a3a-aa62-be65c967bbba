This machine was slightly loaded :-(

SSLeay 0.8.4c 03-Aug-1999
built on Tue Nov  4 02:52:29 EST 1997
options:bn(64,32) md2(int) rc4(ptr,int) des(ptr,risc1,16,long) idea(int) blowfish(ptr2) 
C flags:gcc -DL_ENDIAN -DTERMIO -DBN_ASM -O3 -fomit-frame-pointer -m486 -Wall -Wuninitialized -DMD5_ASM -DSHA1_ASM
The 'numbers' are in 1000s of bytes per second processed.
type              8 bytes     64 bytes    256 bytes   1024 bytes   8192 bytes
md2               130.86k      365.31k      499.60k      547.75k      561.41k
mdc2              526.03k      581.38k      587.12k      586.31k      589.60k
md5              1919.49k    11173.23k    22387.60k    29553.47k    32587.21k
hmac(md5)         747.09k     5248.35k    14275.44k    24713.26k    31737.13k
sha1             1336.63k     6400.50k    11668.67k    14648.83k    15700.85k
rc4             15002.32k    21327.21k    22301.63k    22503.78k    22549.26k
des cbc          4115.16k     4521.08k     4632.37k     4607.28k     4570.57k
des ede3         1540.29k     1609.76k     1623.64k     1620.76k     1624.18k
idea cbc         2405.08k     2664.78k     2704.22k     2713.95k     2716.29k
rc2 cbc          1634.07k     1764.30k     1780.23k     1790.27k     1788.12k
blowfish cbc     5993.98k     6927.27k     7083.61k     7088.40k     7123.72k
cast cbc         5981.52k     6900.44k     7079.70k     7110.40k     7057.72k
                  sign    verify
rsa  512 bits   0.0085s   0.0007s
rsa 1024 bits   0.0377s   0.0020s
rsa 2048 bits   0.2176s   0.0067s
rsa 4096 bits   1.4800s   0.0242s
sign    verify
dsa  512 bits   0.0071s   0.0132s
dsa 1024 bits   0.0192s   0.0376s
dsa 2048 bits   0.0638s   0.1280s

