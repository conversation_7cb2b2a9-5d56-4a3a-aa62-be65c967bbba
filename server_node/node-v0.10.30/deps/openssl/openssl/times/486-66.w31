Windows 3.1 DLL's, 16 bit C with 32bit assember

SSLeay 0.6.1 02-Jul-1996
built on Wed Jul 10 09:53:15 EST 1996
options:bn(32,32) md2(char) rc4(idx,int) des(ptr,long) idea(short)
C flags:cl /ALw /Gx- /Gf /G2 /f- /Ocgnotb2 /W3 /WX -DL_ENDIAN /nologo -DWIN16
The 'numbers' are in 1000s of bytes per second processed.
type           8 bytes     64 bytes    256 bytes   1024 bytes   8192 bytes
md2             18.94k       54.27k       73.43k       80.91k       83.75k
md5             78.96k      391.26k      734.30k      919.80k      992.97k
sha             39.01k      168.04k      280.67k      336.08k      359.10k
sha1            35.20k      150.14k      247.31k      294.54k      313.94k
rc4            509.61k      655.36k      678.43k      677.02k      670.10k
des cfb         97.09k      104.69k      106.56k      105.70k      106.56k
des cbc        116.82k      129.77k      131.07k      131.07k      131.07k
des ede3        44.22k       47.90k       48.53k       48.47k       47.86k
idea cfb        83.49k       87.03k       87.03k       87.15k       87.73k
idea cbc        89.04k       96.23k       96.95k       97.81k       97.09k
rc2 cfb        108.32k      113.58k      113.78k      114.57k      114.77k
rc2 cbc        118.08k      131.07k      134.02k      134.02k      132.66k
rsa  512 bits   0.181s
rsa 1024 bits   0.846s

