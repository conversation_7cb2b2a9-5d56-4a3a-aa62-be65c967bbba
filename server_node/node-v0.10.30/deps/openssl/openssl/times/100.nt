SSLeay 0.8.4c 03-Aug-1999
built on Tue Aug  3 09:49:58 EST 1999
options:bn(64,32) md2(int) rc4(ptr,int) des(idx,cisc,4,long) idea(int) blowfish(
ptr2)
C flags:cl /W3 /WX /G5 /Ox /O2 /Ob2 /Gs0 /GF /Gy /nologo -DWIN32 -DL_ENDIAN -DBN
_ASM -DMD5_ASM -DSHA1_ASM
The 'numbers' are in 1000s of bytes per second processed.
type              8 bytes     64 bytes    256 bytes   1024 bytes   8192 bytes
md2                93.07k      258.38k      349.03k      382.83k      392.87k
mdc2              245.80k      259.02k      259.34k      259.16k      260.14k
md5              1103.42k     6017.65k    12210.49k    16552.11k    18291.77k
hmac(md5)         520.15k     3394.00k     8761.86k    14593.96k    17742.40k
sha1              538.06k     2726.76k     5242.22k     6821.12k     7426.18k
rc4              8283.90k    10513.09k    10886.38k    10929.50k    10816.75k
des cbc          2073.10k     2232.91k     2251.61k     2256.46k     2232.44k
des ede3          758.85k      782.46k      786.14k      786.08k      781.24k
idea cbc          831.02k      892.63k      901.07k      903.48k      901.85k
rc2 cbc           799.89k      866.09k      873.96k      876.22k      874.03k
blowfish cbc     3835.32k     4418.78k     4511.94k     4494.54k     4416.92k
cast cbc         2974.68k     3272.71k     3313.04k     3335.17k     3261.51k
                  sign    verify
rsa  512 bits   0.0202s   0.0019s
rsa 1024 bits   0.1029s   0.0062s
rsa 2048 bits   0.6770s   0.0220s
rsa 4096 bits   4.8770s   0.0838s
                  sign    verify
dsa  512 bits   0.0191s   0.0364s
dsa 1024 bits   0.0590s   0.1141s
dsa 2048 bits   0.2088s   0.4171s
