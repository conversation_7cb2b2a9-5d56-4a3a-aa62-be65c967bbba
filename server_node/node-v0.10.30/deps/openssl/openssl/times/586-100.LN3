SSLeay 0.8.3v 15-Oct-1997
built on Wed Oct 15 10:05:00 EST 1997
options:bn(64,32) md2(int) rc4(idx,int) des(ptr,risc1,16,long) idea(int) blowfish(ptr2) 
C flags:gcc -DL_ENDIAN -DTERMIO -DX86_ASM -O3 -fomit-frame-pointer -m486 -<PERSON> -Wuninitialized
The 'numbers' are in 1000s of bytes per second processed.
type              8 bytes     64 bytes    256 bytes   1024 bytes   8192 bytes
md2                56.27k      156.76k      211.46k      231.77k      238.71k
mdc2              188.74k      206.12k      207.70k      207.87k      208.18k
md5               991.56k     5718.31k    11748.61k    16090.79k    17850.37k
hmac(md5)         387.56k     2636.01k     7327.83k    13340.33k    17091.24k
sha1              463.55k     2274.18k     4071.17k     5072.90k     5447.68k
rc4              3673.94k     4314.52k     4402.26k     4427.09k     4407.30k
des cbc          2023.79k     2209.77k     2233.34k     2220.71k     2222.76k
des ede3          747.17k      778.54k      781.57k      778.24k      778.24k
idea cbc          614.64k      678.04k      683.52k      685.06k      685.40k
rc2 cbc           536.83k      574.10k      578.05k      579.24k      578.90k
blowfish cbc     3673.39k     4354.58k     4450.22k     4429.48k     4377.26k
                  sign    verify
rsa  512 bits   0.0217s   0.0021s
rsa 1024 bits   0.1083s   0.0064s
rsa 2048 bits   0.6867s   0.0223s
rsa 4096 bits   4.9400s   0.0846s
                  sign    verify
dsa  512 bits   0.0203s   0.0387s
dsa 1024 bits   0.0599s   0.1170s
dsa 2048 bits   0.2115s   0.4242s
