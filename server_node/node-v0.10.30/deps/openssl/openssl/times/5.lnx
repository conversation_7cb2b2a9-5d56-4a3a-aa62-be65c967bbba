SSLeay 0.8.5g 24-Jan-1998
built on Tu<PERSON> 27 08:11:42 EST 1998
options:bn(64,32) md2(int) rc4(idx,int) des(ptr,risc1,16,long) idea(int) blowfish(ptr2) 
C flags:gcc -DL_ENDIAN -DTERMIO -DBN_ASM -O3 -fomit-frame-pointer -m486 -Wall -Wuninitialized -DSHA1_ASM -DMD5_ASM -DRMD160_ASM
The 'numbers' are in 1000s of bytes per second processed.
type              8 bytes     64 bytes    256 bytes   1024 bytes   8192 bytes
md2                 56.55k      156.69k      211.63k      231.77k      238.71k
mdc2               192.26k      208.09k      210.09k      209.58k      210.26k
md5                991.04k     5745.51k    11932.67k    16465.24k    18306.39k
hmac(md5)          333.99k     2383.89k     6890.67k    13133.82k    17397.08k
sha1               571.68k     2883.88k     5379.07k     6880.26k     7443.80k
rmd160             409.41k     2212.91k     4225.45k     5456.55k     5928.28k
rc4               6847.57k     8596.22k     8901.80k     8912.90k     8850.09k
des cbc           2046.29k     2229.78k     2254.76k     2259.97k     2233.69k
des ede3           751.11k      779.95k      783.96k      784.38k      780.97k
idea cbc           653.40k      708.29k      718.42k      720.21k      720.90k
rc2 cbc            647.19k      702.46k      709.21k      710.66k      709.97k
rc5-32/12 cbc     3498.18k     4054.12k     4133.46k     4151.64k     4139.69k
blowfish cbc      3763.95k     4437.74k     4532.74k     4515.50k     4448.26k
cast cbc          2754.22k     3020.67k     3079.08k     3069.95k     3036.50k
                  sign    verify    sign/s verify/s
rsa  512 bits   0.0207s   0.0020s     48.3    511.3
rsa 1024 bits   0.1018s   0.0059s      9.8    169.6
rsa 2048 bits   0.6438s   0.0208s      1.6     48.0
rsa 4096 bits   4.6033s   0.0793s      0.2     12.6
                  sign    verify    sign/s verify/s
dsa  512 bits   0.0190s   0.0359s     52.6     27.8
dsa 1024 bits   0.0566s   0.1109s     17.7      9.0
dsa 2048 bits   0.1988s   0.3915s      5.0      2.6
