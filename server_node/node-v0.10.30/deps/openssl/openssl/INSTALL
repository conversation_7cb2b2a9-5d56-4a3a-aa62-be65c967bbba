
 INSTALLATION ON THE UNIX PLATFORM
 ---------------------------------

 [Installation on DOS (with djgpp), Windows, OpenVMS, MacOS (before MacOS X)
  and NetWare is described in INSTALL.DJGPP, INSTALL.W32, INSTALL.VMS,
  INSTALL.MacOS and INSTALL.NW.
  
  This document describes installation on operating systems in the Unix
  family.]

 To install OpenSSL, you will need:

  * make
  * Perl 5
  * an ANSI C compiler
  * a development environment in form of development libraries and C
    header files
  * a supported Unix operating system

 Quick Start
 -----------

 If you want to just get on with it, do:

  $ ./config
  $ make
  $ make test
  $ make install

 [If any of these steps fails, see section Installation in Detail below.]

 This will build and install OpenSSL in the default location, which is (for
 historical reasons) /usr/local/ssl. If you want to install it anywhere else,
 run config like this:

  $ ./config --prefix=/usr/local --openssldir=/usr/local/openssl


 Configuration Options
 ---------------------

 There are several options to ./config (or ./Configure) to customize
 the build:

  --prefix=DIR  Install in DIR/bin, DIR/lib, DIR/include/openssl.
	        Configuration files used by OpenSSL will be in DIR/ssl
                or the directory specified by --openssldir.

  --openssldir=DIR Directory for OpenSSL files. If no prefix is specified,
                the library files and binaries are also installed there.

  no-threads    Don't try to build with support for multi-threaded
                applications.

  threads       Build with support for multi-threaded applications.
                This will usually require additional system-dependent options!
                See "Note on multi-threading" below.

  no-zlib       Don't try to build with support for zlib compression and
                decompression.

  zlib          Build with support for zlib compression/decompression.

  zlib-dynamic  Like "zlib", but has OpenSSL load the zlib library dynamically
                when needed.  This is only supported on systems where loading
                of shared libraries is supported.  This is the default choice.

  no-shared     Don't try to create shared libraries.

  shared        In addition to the usual static libraries, create shared
                libraries on platforms where it's supported.  See "Note on
                shared libraries" below.

  no-asm        Do not use assembler code.

  386           Use the 80386 instruction set only (the default x86 code is
                more efficient, but requires at least a 486). Note: Use
                compiler flags for any other CPU specific configuration,
                e.g. "-m32" to build x86 code on an x64 system.

  no-sse2	Exclude SSE2 code pathes. Normally SSE2 extention is
		detected at run-time, but the decision whether or not the
		machine code will be executed is taken solely on CPU
		capability vector. This means that if you happen to run OS
		kernel which does not support SSE2 extension on Intel P4
		processor, then your application might be exposed to
		"illegal instruction" exception. There might be a way
		to enable support in kernel, e.g. FreeBSD kernel can be
		compiled with CPU_ENABLE_SSE, and there is a way to
		disengage SSE2 code pathes upon application start-up,
		but if you aim for wider "audience" running such kernel,
		consider no-sse2. Both 386 and no-asm options above imply
		no-sse2.

  no-<cipher>   Build without the specified cipher (bf, cast, des, dh, dsa,
                hmac, md2, md5, mdc2, rc2, rc4, rc5, rsa, sha).
                The crypto/<cipher> directory can be removed after running
                "make depend".

  -Dxxx, -lxxx, -Lxxx, -fxxx, -mXXX, -Kxxx These system specific options will
                be passed through to the compiler to allow you to
                define preprocessor symbols, specify additional libraries,
                library directories or other compiler options.

  -DHAVE_CRYPTODEV Enable the BSD cryptodev engine even if we are not using
		BSD. Useful if you are running ocf-linux or something
		similar. Once enabled you can also enable the use of
		cryptodev digests, which is usually slower unless you have
		large amounts data. Use -DUSE_CRYPTODEV_DIGESTS to force
		it.

 Installation in Detail
 ----------------------

 1a. Configure OpenSSL for your operation system automatically:

       $ ./config [options]

     This guesses at your operating system (and compiler, if necessary) and
     configures OpenSSL based on this guess. Run ./config -t to see
     if it guessed correctly. If you want to use a different compiler, you
     are cross-compiling for another platform, or the ./config guess was
     wrong for other reasons, go to step 1b. Otherwise go to step 2.

     On some systems, you can include debugging information as follows:

       $ ./config -d [options]

 1b. Configure OpenSSL for your operating system manually

     OpenSSL knows about a range of different operating system, hardware and
     compiler combinations. To see the ones it knows about, run

       $ ./Configure

     Pick a suitable name from the list that matches your system. For most
     operating systems there is a choice between using "cc" or "gcc".  When
     you have identified your system (and if necessary compiler) use this name
     as the argument to ./Configure. For example, a "linux-elf" user would
     run:

       $ ./Configure linux-elf [options]

     If your system is not available, you will have to edit the Configure
     program and add the correct configuration for your system. The
     generic configurations "cc" or "gcc" should usually work on 32 bit
     systems.

     Configure creates the file Makefile.ssl from Makefile.org and
     defines various macros in crypto/opensslconf.h (generated from
     crypto/opensslconf.h.in).

  2. Build OpenSSL by running:

       $ make

     This will build the OpenSSL libraries (libcrypto.a and libssl.a) and the
     OpenSSL binary ("openssl"). The libraries will be built in the top-level
     directory, and the binary will be in the "apps" directory.

     If "make" fails, look at the output.  There may be reasons for
     the failure that aren't problems in OpenSSL itself (like missing
     standard headers).  If it is a problem with OpenSSL itself, please
     report the problem to <<EMAIL>> (note that your
     message will be recorded in the request tracker publicly readable
     via http://www.openssl.org/support/rt.html and will be forwarded to a
     public mailing list). Include the output of "make report" in your message.
     Please check out the request tracker. Maybe the bug was already
     reported or has already been fixed.

     [If you encounter assembler error messages, try the "no-asm"
     configuration option as an immediate fix.]

     Compiling parts of OpenSSL with gcc and others with the system
     compiler will result in unresolved symbols on some systems.

  3. After a successful build, the libraries should be tested. Run:

       $ make test

     If a test fails, look at the output.  There may be reasons for
     the failure that isn't a problem in OpenSSL itself (like a missing
     or malfunctioning bc).  If it is a problem with OpenSSL itself,
     try removing any compiler optimization flags from the CFLAG line
     in Makefile.ssl and run "make clean; make". Please send a bug
     report to <<EMAIL>>, including the output of
     "make report" in order to be added to the request tracker at
     http://www.openssl.org/support/rt.html.

  4. If everything tests ok, install OpenSSL with

       $ make install

     This will create the installation directory (if it does not exist) and
     then the following subdirectories:

       certs           Initially empty, this is the default location
                       for certificate files.
       man/man1        Manual pages for the 'openssl' command line tool
       man/man3        Manual pages for the libraries (very incomplete)
       misc            Various scripts.
       private         Initially empty, this is the default location
                       for private key files.

     If you didn't choose a different installation prefix, the
     following additional subdirectories will be created:

       bin             Contains the openssl binary and a few other 
                       utility programs. 
       include/openssl Contains the header files needed if you want to
                       compile programs with libcrypto or libssl.
       lib             Contains the OpenSSL library files themselves.

     Use "make install_sw" to install the software without documentation,
     and "install_docs_html" to install HTML renditions of the manual
     pages.

     Package builders who want to configure the library for standard
     locations, but have the package installed somewhere else so that
     it can easily be packaged, can use

       $ make INSTALL_PREFIX=/tmp/package-root install

     (or specify "--install_prefix=/tmp/package-root" as a configure
     option).  The specified prefix will be prepended to all
     installation target filenames.


  NOTE: The header files used to reside directly in the include
  directory, but have now been moved to include/openssl so that
  OpenSSL can co-exist with other libraries which use some of the
  same filenames.  This means that applications that use OpenSSL
  should now use C preprocessor directives of the form

       #include <openssl/ssl.h>

  instead of "#include <ssl.h>", which was used with library versions
  up to OpenSSL 0.9.2b.

  If you install a new version of OpenSSL over an old library version,
  you should delete the old header files in the include directory.

  Compatibility issues:

  *  COMPILING existing applications

     To compile an application that uses old filenames -- e.g.
     "#include <ssl.h>" --, it will usually be enough to find
     the CFLAGS definition in the application's Makefile and
     add a C option such as

          -I/usr/local/ssl/include/openssl

     to it.

     But don't delete the existing -I option that points to
     the ..../include directory!  Otherwise, OpenSSL header files
     could not #include each other.

  *  WRITING applications

     To write an application that is able to handle both the new
     and the old directory layout, so that it can still be compiled
     with library versions up to OpenSSL 0.9.2b without bothering
     the user, you can proceed as follows:

     -  Always use the new filename of OpenSSL header files,
        e.g. #include <openssl/ssl.h>.

     -  Create a directory "incl" that contains only a symbolic
        link named "openssl", which points to the "include" directory
        of OpenSSL.
        For example, your application's Makefile might contain the
        following rule, if OPENSSLDIR is a pathname (absolute or
        relative) of the directory where OpenSSL resides:

        incl/openssl:
        	-mkdir incl
        	cd $(OPENSSLDIR) # Check whether the directory really exists
        	-ln -s `cd $(OPENSSLDIR); pwd`/include incl/openssl

        You will have to add "incl/openssl" to the dependencies
        of those C files that include some OpenSSL header file.

     -  Add "-Iincl" to your CFLAGS.

     With these additions, the OpenSSL header files will be available
     under both name variants if an old library version is used:
     Your application can reach them under names like <openssl/foo.h>,
     while the header files still are able to #include each other
     with names of the form <foo.h>.


 Note on multi-threading
 -----------------------

 For some systems, the OpenSSL Configure script knows what compiler options
 are needed to generate a library that is suitable for multi-threaded
 applications.  On these systems, support for multi-threading is enabled
 by default; use the "no-threads" option to disable (this should never be
 necessary).

 On other systems, to enable support for multi-threading, you will have
 to specify at least two options: "threads", and a system-dependent option.
 (The latter is "-D_REENTRANT" on various systems.)  The default in this
 case, obviously, is not to include support for multi-threading (but
 you can still use "no-threads" to suppress an annoying warning message
 from the Configure script.)


 Note on shared libraries
 ------------------------

 Shared libraries have certain caveats.  Binary backward compatibility
 can't be guaranteed before OpenSSL version 1.0.  The only reason to
 use them would be to conserve memory on systems where several programs
 are using OpenSSL.

 For some systems, the OpenSSL Configure script knows what is needed to
 build shared libraries for libcrypto and libssl.  On these systems,
 the shared libraries are currently not created by default, but giving
 the option "shared" will get them created.  This method supports Makefile
 targets for shared library creation, like linux-shared.  Those targets
 can currently be used on their own just as well, but this is expected
 to change in future versions of OpenSSL.

 Note on random number generation
 --------------------------------

 Availability of cryptographically secure random numbers is required for
 secret key generation. OpenSSL provides several options to seed the
 internal PRNG. If not properly seeded, the internal PRNG will refuse
 to deliver random bytes and a "PRNG not seeded error" will occur.
 On systems without /dev/urandom (or similar) device, it may be necessary
 to install additional support software to obtain random seed.
 Please check out the manual pages for RAND_add(), RAND_bytes(), RAND_egd(),
 and the FAQ for more information.

 Note on support for multiple builds
 -----------------------------------

 OpenSSL is usually built in its source tree.  Unfortunately, this doesn't
 support building for multiple platforms from the same source tree very well.
 It is however possible to build in a separate tree through the use of lots
 of symbolic links, which should be prepared like this:

	mkdir -p objtree/"`uname -s`-`uname -r`-`uname -m`"
	cd objtree/"`uname -s`-`uname -r`-`uname -m`"
	(cd $OPENSSL_SOURCE; find . -type f) | while read F; do
		mkdir -p `dirname $F`
		rm -f $F; ln -s $OPENSSL_SOURCE/$F $F
		echo $F '->' $OPENSSL_SOURCE/$F
	done
	make -f Makefile.org clean

 OPENSSL_SOURCE is an environment variable that contains the absolute (this
 is important!) path to the OpenSSL source tree.

 Also, operations like 'make update' should still be made in the source tree.
