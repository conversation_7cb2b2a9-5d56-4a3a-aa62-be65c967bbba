OpenSSL  -  Frequently Asked Questions
--------------------------------------

[MISC] Miscellaneous questions

* Which is the current version of OpenSSL?
* Where is the documentation?
* How can I contact the OpenSSL developers?
* Where can I get a compiled version of OpenSSL?
* Why aren't tools like 'autoconf' and 'libtool' used?
* What is an 'engine' version?
* How do I check the authenticity of the OpenSSL distribution?
* How does the versioning scheme work?

[LEGAL] Legal questions

* Do I need patent licenses to use OpenSSL?
* Can I use OpenSSL with GPL software? 

[USER] Questions on using the OpenSSL applications

* Why do I get a "PRNG not seeded" error message?
* Why do I get an "unable to write 'random state'" error message?
* How do I create certificates or certificate requests?
* Why can't I create certificate requests?
* Why does <SSL program> fail with a certificate verify error?
* Why can I only use weak ciphers when I connect to a server using OpenSSL?
* How can I create DSA certificates?
* Why can't I make an SSL connection using a DSA certificate?
* How can I remove the passphrase on a private key?
* Why can't I use OpenSSL certificates with SSL client authentication?
* Why does my browser give a warning about a mismatched hostname?
* How do I install a CA certificate into a browser?
* Why is OpenSSL x509 DN output not conformant to RFC2253?
* What is a "128 bit certificate"? Can I create one with OpenSSL?
* Why does OpenSSL set the authority key identifier extension incorrectly?
* How can I set up a bundle of commercial root CA certificates?

[BUILD] Questions about building and testing OpenSSL

* Why does the linker complain about undefined symbols?
* Why does the OpenSSL test fail with "bc: command not found"?
* Why does the OpenSSL test fail with "bc: 1 no implemented"?
* Why does the OpenSSL test fail with "bc: stack empty"?
* Why does the OpenSSL compilation fail on Alpha Tru64 Unix?
* Why does the OpenSSL compilation fail with "ar: command not found"?
* Why does the OpenSSL compilation fail on Win32 with VC++?
* What is special about OpenSSL on Redhat?
* Why does the OpenSSL compilation fail on MacOS X?
* Why does the OpenSSL test suite fail on MacOS X?
* Why does the OpenSSL test suite fail in BN_sqr test [on a 64-bit platform]?
* Why does OpenBSD-i386 build fail on des-586.s with "Unimplemented segment type"?
* Why does the OpenSSL test suite fail in sha512t on x86 CPU?
* Why does compiler fail to compile sha512.c?
* Test suite still fails, what to do?
* I think I've found a bug, what should I do?
* I'm SURE I've found a bug, how do I report it?
* I've found a security issue, how do I report it?

[PROG] Questions about programming with OpenSSL

* Is OpenSSL thread-safe?
* I've compiled a program under Windows and it crashes: why?
* How do I read or write a DER encoded buffer using the ASN1 functions?
* OpenSSL uses DER but I need BER format: does OpenSSL support BER?
* I've tried using <M_some_evil_pkcs12_macro> and I get errors why?
* I've called <some function> and it fails, why?
* I just get a load of numbers for the error output, what do they mean?
* Why do I get errors about unknown algorithms?
* Why can't the OpenSSH configure script detect OpenSSL?
* Can I use OpenSSL's SSL library with non-blocking I/O?
* Why doesn't my server application receive a client certificate?
* Why does compilation fail due to an undefined symbol NID_uniqueIdentifier?
* I think I've detected a memory leak, is this a bug?
* Why does Valgrind complain about the use of uninitialized data?
* Why doesn't a memory BIO work when a file does?
* Where are the declarations and implementations of d2i_X509() etc?

===============================================================================

[MISC] ========================================================================

* Which is the current version of OpenSSL?

The current version is available from <URL: http://www.openssl.org>.
OpenSSL 1.0.1e was released on Feb 11th, 2013.

In addition to the current stable release, you can also access daily
snapshots of the OpenSSL development version at <URL:
ftp://ftp.openssl.org/snapshot/>, or get it by anonymous Git access.


* Where is the documentation?

OpenSSL is a library that provides cryptographic functionality to
applications such as secure web servers.  Be sure to read the
documentation of the application you want to use.  The INSTALL file
explains how to install this library.

OpenSSL includes a command line utility that can be used to perform a
variety of cryptographic functions.  It is described in the openssl(1)
manpage.  Documentation for developers is currently being written. Many
manual pages are available; overviews over libcrypto and
libssl are given in the crypto(3) and ssl(3) manpages.

The OpenSSL manpages are installed in /usr/local/ssl/man/ (or a
different directory if you specified one as described in INSTALL).
In addition, you can read the most current versions at
<URL: http://www.openssl.org/docs/>. Note that the online documents refer
to the very latest development versions of OpenSSL and may include features
not present in released versions. If in doubt refer to the documentation
that came with the version of OpenSSL you are using. The pod format
documentation is included in each OpenSSL distribution under the docs
directory.

For information on parts of libcrypto that are not yet documented, you
might want to read Ariel Glenn's documentation on SSLeay 0.9, OpenSSL's
predecessor, at <URL: http://www.columbia.edu/~ariel/ssleay/>.  Much
of this still applies to OpenSSL.

There is some documentation about certificate extensions and PKCS#12
in doc/openssl.txt

The original SSLeay documentation is included in OpenSSL as
doc/ssleay.txt.  It may be useful when none of the other resources
help, but please note that it reflects the obsolete version SSLeay
0.6.6.


* How can I contact the OpenSSL developers?

The README file describes how to submit bug reports and patches to
OpenSSL.  Information on the OpenSSL mailing lists is available from
<URL: http://www.openssl.org>.


* Where can I get a compiled version of OpenSSL?

You can finder pointers to binary distributions in
<URL: http://www.openssl.org/related/binaries.html> .

Some applications that use OpenSSL are distributed in binary form.
When using such an application, you don't need to install OpenSSL
yourself; the application will include the required parts (e.g. DLLs).

If you want to build OpenSSL on a Windows system and you don't have
a C compiler, read the "Mingw32" section of INSTALL.W32 for information
on how to obtain and install the free GNU C compiler.

A number of Linux and *BSD distributions include OpenSSL.


* Why aren't tools like 'autoconf' and 'libtool' used?

autoconf will probably be used in future OpenSSL versions. If it was
less Unix-centric, it might have been used much earlier.

* What is an 'engine' version?

With version 0.9.6 OpenSSL was extended to interface to external crypto
hardware. This was realized in a special release '0.9.6-engine'. With
version 0.9.7 the changes were merged into the main development line,
so that the special release is no longer necessary.

* How do I check the authenticity of the OpenSSL distribution?

We provide MD5 digests and ASC signatures of each tarball.
Use MD5 to check that a tarball from a mirror site is identical:

   md5sum TARBALL | awk '{print $1;}' | cmp - TARBALL.md5

You can check authenticity using pgp or gpg. You need the OpenSSL team
member public key used to sign it (download it from a key server, see a
list of keys at <URL: http://www.openssl.org/about/>). Then
just do:

   pgp TARBALL.asc

* How does the versioning scheme work?

After the release of OpenSSL 1.0.0 the versioning scheme changed. Letter 
releases (e.g. 1.0.1a) can only contain bug and security fixes and no
new features. Minor releases change the last number (e.g. 1.0.2) and 
can contain new features that retain binary compatibility. Changes to
the middle number are considered major releases and neither source nor
binary compatibility is guaranteed.

Therefore the answer to the common question "when will feature X be
backported to OpenSSL 1.0.0/0.9.8?" is "never" but it could appear
in the next minor release.

[LEGAL] =======================================================================

* Do I need patent licenses to use OpenSSL?

The patents section of the README file lists patents that may apply to
you if you want to use OpenSSL.  For information on intellectual
property rights, please consult a lawyer.  The OpenSSL team does not
offer legal advice.

You can configure OpenSSL so as not to use IDEA, MDC2 and RC5 by using
 ./config no-idea no-mdc2 no-rc5


* Can I use OpenSSL with GPL software?

On many systems including the major Linux and BSD distributions, yes (the
GPL does not place restrictions on using libraries that are part of the
normal operating system distribution).

On other systems, the situation is less clear. Some GPL software copyright
holders claim that you infringe on their rights if you use OpenSSL with
their software on operating systems that don't normally include OpenSSL.

If you develop open source software that uses OpenSSL, you may find it
useful to choose an other license than the GPL, or state explicitly that
"This program is released under the GPL with the additional exemption that
compiling, linking, and/or using OpenSSL is allowed."  If you are using
GPL software developed by others, you may want to ask the copyright holder
for permission to use their software with OpenSSL.


[USER] ========================================================================

* Why do I get a "PRNG not seeded" error message?

Cryptographic software needs a source of unpredictable data to work
correctly.  Many open source operating systems provide a "randomness
device" (/dev/urandom or /dev/random) that serves this purpose.
All OpenSSL versions try to use /dev/urandom by default; starting with
version 0.9.7, OpenSSL also tries /dev/random if /dev/urandom is not
available.

On other systems, applications have to call the RAND_add() or
RAND_seed() function with appropriate data before generating keys or
performing public key encryption. (These functions initialize the
pseudo-random number generator, PRNG.)  Some broken applications do
not do this.  As of version 0.9.5, the OpenSSL functions that need
randomness report an error if the random number generator has not been
seeded with at least 128 bits of randomness.  If this error occurs and
is not discussed in the documentation of the application you are
using, please contact the author of that application; it is likely
that it never worked correctly.  OpenSSL 0.9.5 and later make the
error visible by refusing to perform potentially insecure encryption.

If you are using Solaris 8, you can add /dev/urandom and /dev/random
devices by installing patch 112438 (Sparc) or 112439 (x86), which are
available via the Patchfinder at <URL: http://sunsolve.sun.com>
(Solaris 9 includes these devices by default). For /dev/random support
for earlier Solaris versions, see Sun's statement at
<URL: http://sunsolve.sun.com/pub-cgi/retrieve.pl?doc=fsrdb/27606&zone_32=SUNWski>
(the SUNWski package is available in patch 105710).

On systems without /dev/urandom and /dev/random, it is a good idea to
use the Entropy Gathering Demon (EGD); see the RAND_egd() manpage for
details.  Starting with version 0.9.7, OpenSSL will automatically look
for an EGD socket at /var/run/egd-pool, /dev/egd-pool, /etc/egd-pool and
/etc/entropy.

Most components of the openssl command line utility automatically try
to seed the random number generator from a file.  The name of the
default seeding file is determined as follows: If environment variable
RANDFILE is set, then it names the seeding file.  Otherwise if
environment variable HOME is set, then the seeding file is $HOME/.rnd.
If neither RANDFILE nor HOME is set, versions up to OpenSSL 0.9.6 will
use file .rnd in the current directory while OpenSSL 0.9.6a uses no
default seeding file at all.  OpenSSL 0.9.6b and later will behave
similarly to 0.9.6a, but will use a default of "C:\" for HOME on
Windows systems if the environment variable has not been set.

If the default seeding file does not exist or is too short, the "PRNG
not seeded" error message may occur.

The openssl command line utility will write back a new state to the
default seeding file (and create this file if necessary) unless
there was no sufficient seeding.

Pointing $RANDFILE to an Entropy Gathering Daemon socket does not work.
Use the "-rand" option of the OpenSSL command line tools instead.
The $RANDFILE environment variable and $HOME/.rnd are only used by the
OpenSSL command line tools. Applications using the OpenSSL library
provide their own configuration options to specify the entropy source,
please check out the documentation coming the with application.


* Why do I get an "unable to write 'random state'" error message?


Sometimes the openssl command line utility does not abort with
a "PRNG not seeded" error message, but complains that it is
"unable to write 'random state'".  This message refers to the
default seeding file (see previous answer).  A possible reason
is that no default filename is known because neither RANDFILE
nor HOME is set.  (Versions up to 0.9.6 used file ".rnd" in the
current directory in this case, but this has changed with 0.9.6a.)


* How do I create certificates or certificate requests?

Check out the CA.pl(1) manual page. This provides a simple wrapper round
the 'req', 'verify', 'ca' and 'pkcs12' utilities. For finer control check
out the manual pages for the individual utilities and the certificate
extensions documentation (in ca(1), req(1), x509v3_config(5) )


* Why can't I create certificate requests?

You typically get the error:

	unable to find 'distinguished_name' in config
	problems making Certificate Request

This is because it can't find the configuration file. Check out the
DIAGNOSTICS section of req(1) for more information.


* Why does <SSL program> fail with a certificate verify error?

This problem is usually indicated by log messages saying something like
"unable to get local issuer certificate" or "self signed certificate".
When a certificate is verified its root CA must be "trusted" by OpenSSL
this typically means that the CA certificate must be placed in a directory
or file and the relevant program configured to read it. The OpenSSL program
'verify' behaves in a similar way and issues similar error messages: check
the verify(1) program manual page for more information.


* Why can I only use weak ciphers when I connect to a server using OpenSSL?

This is almost certainly because you are using an old "export grade" browser
which only supports weak encryption. Upgrade your browser to support 128 bit
ciphers.


* How can I create DSA certificates?

Check the CA.pl(1) manual page for a DSA certificate example.


* Why can't I make an SSL connection to a server using a DSA certificate?

Typically you'll see a message saying there are no shared ciphers when
the same setup works fine with an RSA certificate. There are two possible
causes. The client may not support connections to DSA servers most web
browsers (including Netscape and MSIE) only support connections to servers
supporting RSA cipher suites. The other cause is that a set of DH parameters
has not been supplied to the server. DH parameters can be created with the
dhparam(1) command and loaded using the SSL_CTX_set_tmp_dh() for example:
check the source to s_server in apps/s_server.c for an example.


* How can I remove the passphrase on a private key?

Firstly you should be really *really* sure you want to do this. Leaving
a private key unencrypted is a major security risk. If you decide that
you do have to do this check the EXAMPLES sections of the rsa(1) and
dsa(1) manual pages.


* Why can't I use OpenSSL certificates with SSL client authentication?

What will typically happen is that when a server requests authentication
it will either not include your certificate or tell you that you have
no client certificates (Netscape) or present you with an empty list box
(MSIE). The reason for this is that when a server requests a client
certificate it includes a list of CAs names which it will accept. Browsers
will only let you select certificates from the list on the grounds that
there is little point presenting a certificate which the server will
reject.

The solution is to add the relevant CA certificate to your servers "trusted
CA list". How you do this depends on the server software in uses. You can
print out the servers list of acceptable CAs using the OpenSSL s_client tool:

openssl s_client -connect www.some.host:443 -prexit

If your server only requests certificates on certain URLs then you may need
to manually issue an HTTP GET command to get the list when s_client connects:

GET /some/page/needing/a/certificate.html

If your CA does not appear in the list then this confirms the problem.


* Why does my browser give a warning about a mismatched hostname?

Browsers expect the server's hostname to match the value in the commonName
(CN) field of the certificate. If it does not then you get a warning.


* How do I install a CA certificate into a browser?

The usual way is to send the DER encoded certificate to the browser as
MIME type application/x-x509-ca-cert, for example by clicking on an appropriate
link. On MSIE certain extensions such as .der or .cacert may also work, or you
can import the certificate using the certificate import wizard.

You can convert a certificate to DER form using the command:

openssl x509 -in ca.pem -outform DER -out ca.der

Occasionally someone suggests using a command such as:

openssl pkcs12 -export -out cacert.p12 -in cacert.pem -inkey cakey.pem

DO NOT DO THIS! This command will give away your CAs private key and
reduces its security to zero: allowing anyone to forge certificates in
whatever name they choose.

* Why is OpenSSL x509 DN output not conformant to RFC2253?

The ways to print out the oneline format of the DN (Distinguished Name) have
been extended in version 0.9.7 of OpenSSL. Using the new X509_NAME_print_ex()
interface, the "-nameopt" option could be introduded. See the manual
page of the "openssl x509" commandline tool for details. The old behaviour
has however been left as default for the sake of compatibility.

* What is a "128 bit certificate"? Can I create one with OpenSSL?

The term "128 bit certificate" is a highly misleading marketing term. It does
*not* refer to the size of the public key in the certificate! A certificate
containing a 128 bit RSA key would have negligible security.

There were various other names such as "magic certificates", "SGC
certificates", "step up certificates" etc.

You can't generally create such a certificate using OpenSSL but there is no
need to any more. Nowadays web browsers using unrestricted strong encryption
are generally available.

When there were tight restrictions on the export of strong encryption
software from the US only weak encryption algorithms could be freely exported
(initially 40 bit and then 56 bit). It was widely recognised that this was
inadequate. A relaxation of the rules allowed the use of strong encryption but
only to an authorised server.

Two slighly different techniques were developed to support this, one used by
Netscape was called "step up", the other used by MSIE was called "Server Gated
Cryptography" (SGC). When a browser initially connected to a server it would
check to see if the certificate contained certain extensions and was issued by
an authorised authority. If these test succeeded it would reconnect using
strong encryption.

Only certain (initially one) certificate authorities could issue the
certificates and they generally cost more than ordinary certificates.

Although OpenSSL can create certificates containing the appropriate extensions
the certificate would not come from a permitted authority and so would not
be recognized.

The export laws were later changed to allow almost unrestricted use of strong
encryption so these certificates are now obsolete.


* Why does OpenSSL set the authority key identifier (AKID) extension incorrectly?

It doesn't: this extension is often the cause of confusion.

Consider a certificate chain A->B->C so that A signs B and B signs C. Suppose
certificate C contains AKID.

The purpose of this extension is to identify the authority certificate B. This
can be done either by including the subject key identifier of B or its issuer
name and serial number.

In this latter case because it is identifying certifcate B it must contain the
issuer name and serial number of B.

It is often wrongly assumed that it should contain the subject name of B. If it
did this would be redundant information because it would duplicate the issuer
name of C.


* How can I set up a bundle of commercial root CA certificates?

The OpenSSL software is shipped without any root CA certificate as the
OpenSSL project does not have any policy on including or excluding
any specific CA and does not intend to set up such a policy. Deciding
about which CAs to support is up to application developers or
administrators.

Other projects do have other policies so you can for example extract the CA
bundle used by Mozilla and/or modssl as described in this article:

  <URL: http://www.mail-archive.com/<EMAIL>/msg16980.html>


[BUILD] =======================================================================

* Why does the linker complain about undefined symbols?

Maybe the compilation was interrupted, and make doesn't notice that
something is missing.  Run "make clean; make".

If you used ./Configure instead of ./config, make sure that you
selected the right target.  File formats may differ slightly between
OS versions (for example sparcv8/sparcv9, or a.out/elf).

In case you get errors about the following symbols, use the config
option "no-asm", as described in INSTALL:

 BF_cbc_encrypt, BF_decrypt, BF_encrypt, CAST_cbc_encrypt,
 CAST_decrypt, CAST_encrypt, RC4, RC5_32_cbc_encrypt, RC5_32_decrypt,
 RC5_32_encrypt, bn_add_words, bn_div_words, bn_mul_add_words,
 bn_mul_comba4, bn_mul_comba8, bn_mul_words, bn_sqr_comba4,
 bn_sqr_comba8, bn_sqr_words, bn_sub_words, des_decrypt3,
 des_ede3_cbc_encrypt, des_encrypt, des_encrypt2, des_encrypt3,
 des_ncbc_encrypt, md5_block_asm_host_order, sha1_block_asm_data_order

If none of these helps, you may want to try using the current snapshot.
If the problem persists, please submit a bug report.


* Why does the OpenSSL test fail with "bc: command not found"?

You didn't install "bc", the Unix calculator.  If you want to run the
tests, get GNU bc from ftp://ftp.gnu.org or from your OS distributor.


* Why does the OpenSSL test fail with "bc: 1 no implemented"?

On some SCO installations or versions, bc has a bug that gets triggered
when you run the test suite (using "make test").  The message returned is
"bc: 1 not implemented".

The best way to deal with this is to find another implementation of bc
and compile/install it.  GNU bc (see <URL: http://www.gnu.org/software/software.html>
for download instructions) can be safely used, for example.


* Why does the OpenSSL test fail with "bc: stack empty"?

On some DG/ux versions, bc seems to have a too small stack for calculations
that the OpenSSL bntest throws at it.  This gets triggered when you run the
test suite (using "make test").  The message returned is "bc: stack empty".

The best way to deal with this is to find another implementation of bc
and compile/install it.  GNU bc (see <URL: http://www.gnu.org/software/software.html>
for download instructions) can be safely used, for example.


* Why does the OpenSSL compilation fail on Alpha Tru64 Unix?

On some Alpha installations running Tru64 Unix and Compaq C, the compilation
of crypto/sha/sha_dgst.c fails with the message 'Fatal:  Insufficient virtual
memory to continue compilation.'  As far as the tests have shown, this may be
a compiler bug.  What happens is that it eats up a lot of resident memory
to build something, probably a table.  The problem is clearly in the
optimization code, because if one eliminates optimization completely (-O0),
the compilation goes through (and the compiler consumes about 2MB of resident
memory instead of 240MB or whatever one's limit is currently).

There are three options to solve this problem:

1. set your current data segment size soft limit higher.  Experience shows
that about 241000 kbytes seems to be enough on an AlphaServer DS10.  You do
this with the command 'ulimit -Sd nnnnnn', where 'nnnnnn' is the number of
kbytes to set the limit to.

2. If you have a hard limit that is lower than what you need and you can't
get it changed, you can compile all of OpenSSL with -O0 as optimization
level.  This is however not a very nice thing to do for those who expect to
get the best result from OpenSSL.  A bit more complicated solution is the
following:

----- snip:start -----
  make DIRS=crypto SDIRS=sha "`grep '^CFLAG=' Makefile.ssl | \
       sed -e 's/ -O[0-9] / -O0 /'`"
  rm `ls crypto/*.o crypto/sha/*.o | grep -v 'sha_dgst\.o'`
  make
----- snip:end -----

This will only compile sha_dgst.c with -O0, the rest with the optimization
level chosen by the configuration process.  When the above is done, do the
test and installation and you're set.

3. Reconfigure the toolkit with no-sha0 option to leave out SHA0. It 
should not be used and is not used in SSL/TLS nor any other recognized
protocol in either case.


* Why does the OpenSSL compilation fail with "ar: command not found"?

Getting this message is quite usual on Solaris 2, because Sun has hidden
away 'ar' and other development commands in directories that aren't in
$PATH by default.  One of those directories is '/usr/ccs/bin'.  The
quickest way to fix this is to do the following (it assumes you use sh
or any sh-compatible shell):

----- snip:start -----
  PATH=${PATH}:/usr/ccs/bin; export PATH
----- snip:end -----

and then redo the compilation.  What you should really do is make sure
'/usr/ccs/bin' is permanently in your $PATH, for example through your
'.profile' (again, assuming you use a sh-compatible shell).


* Why does the OpenSSL compilation fail on Win32 with VC++?

Sometimes, you may get reports from VC++ command line (cl) that it
can't find standard include files like stdio.h and other weirdnesses.
One possible cause is that the environment isn't correctly set up.
To solve that problem for VC++ versions up to 6, one should run
VCVARS32.BAT which is found in the 'bin' subdirectory of the VC++
installation directory (somewhere under 'Program Files').  For VC++
version 7 (and up?), which is also called VS.NET, the file is called
VSVARS32.BAT instead.
This needs to be done prior to running NMAKE, and the changes are only
valid for the current DOS session.


* What is special about OpenSSL on Redhat?

Red Hat Linux (release 7.0 and later) include a preinstalled limited
version of OpenSSL. For patent reasons, support for IDEA, RC5 and MDC2
is disabled in this version. The same may apply to other Linux distributions.
Users may therefore wish to install more or all of the features left out.

To do this you MUST ensure that you do not overwrite the openssl that is in
/usr/bin on your Red Hat machine. Several packages depend on this file,
including sendmail and ssh. /usr/local/bin is a good alternative choice. The
libraries that come with Red Hat 7.0 onwards have different names and so are
not affected. (eg For Red Hat 7.2 they are /lib/libssl.so.0.9.6b and
/lib/libcrypto.so.0.9.6b with symlinks /lib/libssl.so.2 and
/lib/libcrypto.so.2 respectively).

Please note that we have been advised by Red Hat attempting to recompile the
openssl rpm with all the cryptography enabled will not work. All other
packages depend on the original Red Hat supplied openssl package. It is also
worth noting that due to the way Red Hat supplies its packages, updates to
openssl on each distribution never change the package version, only the
build number. For example, on Red Hat 7.1, the latest openssl package has
version number 0.9.6 and build number 9 even though it contains all the
relevant updates in packages up to and including 0.9.6b.

A possible way around this is to persuade Red Hat to produce a non-US
version of Red Hat Linux.

FYI: Patent numbers and expiry dates of US patents:
MDC-2: 4,908,861 13/03/2007
IDEA:  5,214,703 25/05/2010
RC5:   5,724,428 03/03/2015


* Why does the OpenSSL compilation fail on MacOS X?

If the failure happens when trying to build the "openssl" binary, with
a large number of undefined symbols, it's very probable that you have
OpenSSL 0.9.6b delivered with the operating system (you can find out by
running '/usr/bin/openssl version') and that you were trying to build
OpenSSL 0.9.7 or newer.  The problem is that the loader ('ld') in
MacOS X has a misfeature that's quite difficult to go around.
Look in the file PROBLEMS for a more detailed explanation and for possible
solutions.


* Why does the OpenSSL test suite fail on MacOS X?

If the failure happens when running 'make test' and the RC4 test fails,
it's very probable that you have OpenSSL 0.9.6b delivered with the
operating system (you can find out by running '/usr/bin/openssl version')
and that you were trying to build OpenSSL 0.9.6d.  The problem is that
the loader ('ld') in MacOS X has a misfeature that's quite difficult to
go around and has linked the programs "openssl" and the test programs
with /usr/lib/libcrypto.dylib and /usr/lib/libssl.dylib instead of the
libraries you just built.
Look in the file PROBLEMS for a more detailed explanation and for possible
solutions.

* Why does the OpenSSL test suite fail in BN_sqr test [on a 64-bit platform]?

Failure in BN_sqr test is most likely caused by a failure to configure the
toolkit for current platform or lack of support for the platform in question.
Run './config -t' and './apps/openssl version -p'. Do these platform
identifiers match? If they don't, then you most likely failed to run
./config and you're hereby advised to do so before filing a bug report.
If ./config itself fails to run, then it's most likely problem with your
local environment and you should turn to your system administrator (or
similar). If identifiers match (and/or no alternative identifier is
suggested by ./config script), then the platform is unsupported. There might
or might not be a workaround. Most notably on SPARC64 platforms with GNU
C compiler you should be able to produce a working build by running
'./config -m32'. I understand that -m32 might not be what you want/need,
but the build should be operational. For further details turn to
<<EMAIL>>.

* Why does OpenBSD-i386 build fail on des-586.s with "Unimplemented segment type"?

As of 0.9.7 assembler routines were overhauled for position independence
of the machine code, which is essential for shared library support. For
some reason OpenBSD is equipped with an out-of-date GNU assembler which
finds the new code offensive. To work around the problem, configure with
no-asm (and sacrifice a great deal of performance) or patch your assembler
according to <URL: http://www.openssl.org/~appro/gas-1.92.3.OpenBSD.patch>.
For your convenience a pre-compiled replacement binary is provided at
<URL: http://www.openssl.org/~appro/gas-1.92.3.static.aout.bin>.
Reportedly elder *BSD a.out platforms also suffer from this problem and
remedy should be same. Provided binary is statically linked and should be
working across wider range of *BSD branches, not just OpenBSD.

* Why does the OpenSSL test suite fail in sha512t on x86 CPU?

If the test program in question fails withs SIGILL, Illegal Instruction
exception, then you more than likely to run SSE2-capable CPU, such as
Intel P4, under control of kernel which does not support SSE2
instruction extentions. See accompanying INSTALL file and
OPENSSL_ia32cap(3) documentation page for further information.

* Why does compiler fail to compile sha512.c?

OpenSSL SHA-512 implementation depends on compiler support for 64-bit
integer type. Few elder compilers [ULTRIX cc, SCO compiler to mention a
couple] lack support for this and therefore are incapable of compiling
the module in question. The recommendation is to disable SHA-512 by
adding no-sha512 to ./config [or ./Configure] command line. Another
possible alternative might be to switch to GCC.

* Test suite still fails, what to do?

Another common reason for failure to complete some particular test is
simply bad code generated by a buggy component in toolchain or deficiency
in run-time environment. There are few cases documented in PROBLEMS file,
consult it for possible workaround before you beat the drum. Even if you
don't find solution or even mention there, do reserve for possibility of
a compiler bug. Compiler bugs might appear in rather bizarre ways, they
never make sense, and tend to emerge when you least expect them. In order
to identify one, drop optimization level, e.g. by editing CFLAG line in
top-level Makefile, recompile and re-run the test.

* I think I've found a bug, what should I do?

If you are a new user then it is quite likely you haven't found a bug and
something is happening you aren't familiar with. Check this FAQ, the associated
documentation and the mailing lists for similar queries. If you are still
unsure whether it is a bug or not submit a query to the openssl-users mailing
list.


* I'm SURE I've found a bug, how do I report it?

Bug reports with no security implications should be sent to the request
tracker. This can be done by mailing the report to <<EMAIL>> (or its
alias <<EMAIL>>), please note that messages sent to the
request tracker also appear in the public openssl-dev mailing list.

The report should be in plain text. Any patches should be sent as
plain text attachments because some mailers corrupt patches sent inline.
If your issue affects multiple versions of OpenSSL check any patches apply
cleanly and, if possible include patches to each affected version.

The report should be given a meaningful subject line briefly summarising the
issue. Just "bug in OpenSSL" or "bug in OpenSSL 0.9.8n" is not very helpful.

By sending reports to the request tracker the bug can then be given a priority
and assigned to the appropriate maintainer. The history of discussions can be
accessed and if the issue has been addressed or a reason why not. If patches
are only sent to openssl-dev they can be mislaid if a team member has to
wade through months of old messages to review the discussion.

See also <URL: http://www.openssl.org/support/rt.html>


* I've found a security issue, how do I report it?

If you think your bug has security implications then please send it to
<EMAIL> if you don't get a prompt reply at least 
acknowledging receipt then resend or mail it directly to one of the
more active team members (e.g. Steve).

Note that bugs only present in the openssl utility are not in general
considered to be security issues. 

[PROG] ========================================================================

* Is OpenSSL thread-safe?

Yes (with limitations: an SSL connection may not concurrently be used
by multiple threads).  On Windows and many Unix systems, OpenSSL
automatically uses the multi-threaded versions of the standard
libraries.  If your platform is not one of these, consult the INSTALL
file.

Multi-threaded applications must provide two callback functions to
OpenSSL by calling CRYPTO_set_locking_callback() and
CRYPTO_set_id_callback(), for all versions of OpenSSL up to and
including 0.9.8[abc...]. As of version 1.0.0, CRYPTO_set_id_callback()
and associated APIs are deprecated by CRYPTO_THREADID_set_callback()
and friends. This is described in the threads(3) manpage.

* I've compiled a program under Windows and it crashes: why?

This is usually because you've missed the comment in INSTALL.W32.
Your application must link against the same version of the Win32
C-Runtime against which your openssl libraries were linked.  The
default version for OpenSSL is /MD - "Multithreaded DLL".

If you are using Microsoft Visual C++'s IDE (Visual Studio), in
many cases, your new project most likely defaulted to "Debug
Singlethreaded" - /ML.  This is NOT interchangeable with /MD and your
program will crash, typically on the first BIO related read or write
operation.

For each of the six possible link stage configurations within Win32,
your application must link  against the same by which OpenSSL was
built.  If you are using MS Visual C++ (Studio) this can be changed
by:

 1. Select Settings... from the Project Menu.
 2. Select the C/C++ Tab.
 3. Select "Code Generation from the "Category" drop down list box
 4. Select the Appropriate library (see table below) from the "Use
    run-time library" drop down list box.  Perform this step for both
    your debug and release versions of your application (look at the
    top left of the settings panel to change between the two)

    Single Threaded           /ML        -  MS VC++ often defaults to
                                            this for the release
                                            version of a new project.
    Debug Single Threaded     /MLd       -  MS VC++ often defaults to
                                            this for the debug version
                                            of a new project.
    Multithreaded             /MT
    Debug Multithreaded       /MTd
    Multithreaded DLL         /MD        -  OpenSSL defaults to this.
    Debug Multithreaded DLL   /MDd

Note that debug and release libraries are NOT interchangeable.  If you
built OpenSSL with /MD your application must use /MD and cannot use /MDd.

As per 0.9.8 the above limitation is eliminated for .DLLs. OpenSSL
.DLLs compiled with some specific run-time option [we insist on the
default /MD] can be deployed with application compiled with different
option or even different compiler. But there is a catch! Instead of
re-compiling OpenSSL toolkit, as you would have to with prior versions,
you have to compile small C snippet with compiler and/or options of
your choice. The snippet gets installed as
<install-root>/include/openssl/applink.c and should be either added to
your application project or simply #include-d in one [and only one]
of your application source files. Failure to link this shim module
into your application manifests itself as fatal "no OPENSSL_Applink"
run-time error. An explicit reminder is due that in this situation
[mixing compiler options] it is as important to add CRYPTO_malloc_init
prior first call to OpenSSL.

* How do I read or write a DER encoded buffer using the ASN1 functions?

You have two options. You can either use a memory BIO in conjunction
with the i2d_*_bio() or d2i_*_bio() functions or you can use the
i2d_*(), d2i_*() functions directly. Since these are often the
cause of grief here are some code fragments using PKCS7 as an example:

 unsigned char *buf, *p;
 int len;

 len = i2d_PKCS7(p7, NULL);
 buf = OPENSSL_malloc(len); /* or Malloc, error checking omitted */
 p = buf;
 i2d_PKCS7(p7, &p);

At this point buf contains the len bytes of the DER encoding of
p7.

The opposite assumes we already have len bytes in buf:

 unsigned char *p;
 p = buf;
 p7 = d2i_PKCS7(NULL, &p, len);

At this point p7 contains a valid PKCS7 structure of NULL if an error
occurred. If an error occurred ERR_print_errors(bio) should give more
information.

The reason for the temporary variable 'p' is that the ASN1 functions
increment the passed pointer so it is ready to read or write the next
structure. This is often a cause of problems: without the temporary
variable the buffer pointer is changed to point just after the data
that has been read or written. This may well be uninitialized data
and attempts to free the buffer will have unpredictable results
because it no longer points to the same address.


* OpenSSL uses DER but I need BER format: does OpenSSL support BER?

The short answer is yes, because DER is a special case of BER and OpenSSL
ASN1 decoders can process BER.

The longer answer is that ASN1 structures can be encoded in a number of
different ways. One set of ways is the Basic Encoding Rules (BER) with various
permissible encodings. A restriction of BER is the Distinguished Encoding
Rules (DER): these uniquely specify how a given structure is encoded.

Therefore, because DER is a special case of BER, DER is an acceptable encoding
for BER.


* I've tried using <M_some_evil_pkcs12_macro> and I get errors why?

This usually happens when you try compiling something using the PKCS#12
macros with a C++ compiler. There is hardly ever any need to use the
PKCS#12 macros in a program, it is much easier to parse and create
PKCS#12 files using the PKCS12_parse() and PKCS12_create() functions
documented in doc/openssl.txt and with examples in demos/pkcs12. The
'pkcs12' application has to use the macros because it prints out 
debugging information.


* I've called <some function> and it fails, why?

Before submitting a report or asking in one of the mailing lists, you
should try to determine the cause. In particular, you should call
ERR_print_errors() or ERR_print_errors_fp() after the failed call
and see if the message helps. Note that the problem may occur earlier
than you think -- you should check for errors after every call where
it is possible, otherwise the actual problem may be hidden because
some OpenSSL functions clear the error state.


* I just get a load of numbers for the error output, what do they mean?

The actual format is described in the ERR_print_errors() manual page.
You should call the function ERR_load_crypto_strings() before hand and
the message will be output in text form. If you can't do this (for example
it is a pre-compiled binary) you can use the errstr utility on the error
code itself (the hex digits after the second colon).


* Why do I get errors about unknown algorithms?

The cause is forgetting to load OpenSSL's table of algorithms with
OpenSSL_add_all_algorithms(). See the manual page for more information. This
can cause several problems such as being unable to read in an encrypted
PEM file, unable to decrypt a PKCS#12 file or signature failure when
verifying certificates.

* Why can't the OpenSSH configure script detect OpenSSL?

Several reasons for problems with the automatic detection exist.
OpenSSH requires at least version 0.9.5a of the OpenSSL libraries.
Sometimes the distribution has installed an older version in the system
locations that is detected instead of a new one installed. The OpenSSL
library might have been compiled for another CPU or another mode (32/64 bits).
Permissions might be wrong.

The general answer is to check the config.log file generated when running
the OpenSSH configure script. It should contain the detailed information
on why the OpenSSL library was not detected or considered incompatible.


* Can I use OpenSSL's SSL library with non-blocking I/O?

Yes; make sure to read the SSL_get_error(3) manual page!

A pitfall to avoid: Don't assume that SSL_read() will just read from
the underlying transport or that SSL_write() will just write to it --
it is also possible that SSL_write() cannot do any useful work until
there is data to read, or that SSL_read() cannot do anything until it
is possible to send data.  One reason for this is that the peer may
request a new TLS/SSL handshake at any time during the protocol,
requiring a bi-directional message exchange; both SSL_read() and
SSL_write() will try to continue any pending handshake.


* Why doesn't my server application receive a client certificate?

Due to the TLS protocol definition, a client will only send a certificate,
if explicitly asked by the server. Use the SSL_VERIFY_PEER flag of the
SSL_CTX_set_verify() function to enable the use of client certificates.


* Why does compilation fail due to an undefined symbol NID_uniqueIdentifier?

For OpenSSL 0.9.7 the OID table was extended and corrected. In earlier
versions, uniqueIdentifier was incorrectly used for X.509 certificates.
The correct name according to RFC2256 (LDAP) is x500UniqueIdentifier.
Change your code to use the new name when compiling against OpenSSL 0.9.7.


* I think I've detected a memory leak, is this a bug?

In most cases the cause of an apparent memory leak is an OpenSSL internal table
that is allocated when an application starts up. Since such tables do not grow
in size over time they are harmless.

These internal tables can be freed up when an application closes using various
functions.  Currently these include following:

Thread-local cleanup functions:

  ERR_remove_state()

Application-global cleanup functions that are aware of usage (and therefore
thread-safe):

  ENGINE_cleanup() and CONF_modules_unload()

"Brutal" (thread-unsafe) Application-global cleanup functions:

  ERR_free_strings(), EVP_cleanup() and CRYPTO_cleanup_all_ex_data().


* Why does Valgrind complain about the use of uninitialized data?

When OpenSSL's PRNG routines are called to generate random numbers the supplied
buffer contents are mixed into the entropy pool: so it technically does not
matter whether the buffer is initialized at this point or not.  Valgrind (and
other test tools) will complain about this. When using Valgrind, make sure the
OpenSSL library has been compiled with the PURIFY macro defined (-DPURIFY)
to get rid of these warnings.


* Why doesn't a memory BIO work when a file does?

This can occur in several cases for example reading an S/MIME email message.
The reason is that a memory BIO can do one of two things when all the data
has been read from it.

The default behaviour is to indicate that no more data is available and that
the call should be retried, this is to allow the application to fill up the BIO
again if necessary.

Alternatively it can indicate that no more data is available and that EOF has
been reached.

If a memory BIO is to behave in the same way as a file this second behaviour
is needed. This must be done by calling:

   BIO_set_mem_eof_return(bio, 0);

See the manual pages for more details.


* Where are the declarations and implementations of d2i_X509() etc?

These are defined and implemented by macros of the form:


 DECLARE_ASN1_FUNCTIONS(X509) and IMPLEMENT_ASN1_FUNCTIONS(X509)

The implementation passes an ASN1 "template" defining the structure into an
ASN1 interpreter using generalised functions such as ASN1_item_d2i().


===============================================================================
