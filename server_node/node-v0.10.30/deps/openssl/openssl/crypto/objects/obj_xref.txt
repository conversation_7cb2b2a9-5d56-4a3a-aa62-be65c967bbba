# OID cross reference table.
# Links signatures OIDs to their corresponding public key algorithms
# and digests.

md2WithRSAEncryption	md2	rsaEncryption
md5WithRSAEncryption	md5	rsaEncryption
shaWithRSAEncryption	sha	rsaEncryption
sha1WithRSAEncryption	sha1	rsaEncryption
md4WithRSAEncryption	md4	rsaEncryption
sha256WithRSAEncryption sha256	rsaEncryption
sha384WithRSAEncryption	sha384	rsaEncryption
sha512WithRSAEncryption	sha512	rsaEncryption
sha224WithRSAEncryption	sha224	rsaEncryption
mdc2WithRSA		mdc2	rsaEncryption
ripemd160WithRSA	ripemd160 rsaEncryption
# For PSS the digest algorithm can vary and depends on the included
# AlgorithmIdentifier. The digest "undef" indicates the public key
# method should handle this explicitly.
rsassaPss		undef	rsaEncryption

# Alternative deprecated OIDs. By using the older "rsa" OID this
# type will be recognized by not normally used.

md5WithRSA		md5	rsa
sha1WithRSA		sha1	rsa

dsaWithSHA		sha	dsa
dsaWithSHA1		sha1	dsa

dsaWithSHA1_2		sha1	dsa_2

ecdsa_with_SHA1		sha1	X9_62_id_ecPublicKey
ecdsa_with_SHA224	sha224	X9_62_id_ecPublicKey
ecdsa_with_SHA256	sha256	X9_62_id_ecPublicKey
ecdsa_with_SHA384	sha384	X9_62_id_ecPublicKey
ecdsa_with_SHA512	sha512	X9_62_id_ecPublicKey
ecdsa_with_Recommended	undef	X9_62_id_ecPublicKey
ecdsa_with_Specified	undef	X9_62_id_ecPublicKey

dsa_with_SHA224		sha224	dsa
dsa_with_SHA256		sha256	dsa

id_GostR3411_94_with_GostR3410_2001	id_GostR3411_94 id_GostR3410_2001
id_GostR3411_94_with_GostR3410_94	id_GostR3411_94 id_GostR3410_94
id_GostR3411_94_with_GostR3410_94_cc	id_GostR3411_94 id_GostR3410_94_cc
id_GostR3411_94_with_GostR3410_2001_cc	id_GostR3411_94 id_GostR3410_2001_cc
