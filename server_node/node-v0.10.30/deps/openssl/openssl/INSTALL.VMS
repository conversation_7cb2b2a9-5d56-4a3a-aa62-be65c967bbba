			VMS Installation instructions
			written by <PERSON>
			<<EMAIL>>


Intro:
======

This file is divided in the following parts:

  Requirements			- Mandatory reading.
  Checking the distribution	- Mandatory reading.
  Compilation			- Mandatory reading.
  Logical names			- Mandatory reading.
  Test				- Mandatory reading.
  Installation			- Mandatory reading.
  Backward portability		- Read if it's an issue.
  Possible bugs or quirks	- A few warnings on things that
				  may go wrong or may surprise you.
  TODO				- Things that are to come.


Requirements:
=============

To build and install OpenSSL, you will need:

 * DEC C or some other ANSI C compiler.  VAX C is *not* supported.
   [Note: OpenSSL has only been tested with DEC C.  Compiling with 
    a different ANSI C compiler may require some work]

Checking the distribution:
==========================

There have been reports of places where the distribution didn't quite get
through, for example if you've copied the tree from a NFS-mounted Unix
mount point.

The easiest way to check if everything got through as it should is to check
for one of the following files:

	[.CRYPTO]OPENSSLCONF.H_IN
	[.CRYPTO]OPENSSLCONF_H.IN

They should never exist both at once, but one of them should (preferably
the first variant).  If you can't find any of those two, something went
wrong.

The best way to get a correct distribution is to download the gzipped tar
file from ftp://ftp.openssl.org/source/, use GUNZIP to uncompress it and
use VMSTAR to unpack the resulting tar file.

GUNZIP is available in many places on the net.  One of the distribution
points is the WKU software archive, ftp://ftp.wku.edu/vms/fileserv/ .

VMSTAR is also available in many places on the net.  The recommended place
to find information about it is http://www.free.lp.se/vmstar/ .


Compilation:
============

I've used the very good command procedures written by Robert Byer
<<EMAIL>>, and just slightly modified them, making
them slightly more general and easier to maintain.

You can actually compile in almost any directory separately.  Look
for a command procedure name xxx-LIB.COM (in the library directories)
or MAKExxx.COM (in the program directories) and read the comments at
the top to understand how to use them.  However, if you want to
compile all you can get, the simplest is to use MAKEVMS.COM in the top
directory.  The syntax is the following:

  @MAKEVMS <option> <bits> <debug-p> [<compiler>]

<option> must be one of the following:

      ALL       Just build "everything".
      CONFIG    Just build the "[.CRYPTO]OPENSSLCONF.H" file.
      BUILDINF  Just build the "[.INCLUDE]BUILDINF.H" file.
      SOFTLINKS Just copies some files, to simulate Unix soft links.
      BUILDALL  Same as ALL, except CONFIG, BUILDINF and SOFTLINKS aren't done.
      RSAREF    Just build the "[.xxx.EXE.RSAREF]LIBRSAGLUE.OLB" library.
      CRYPTO    Just build the "[.xxx.EXE.CRYPTO]LIBCRYPTO.OLB" library.
      SSL       Just build the "[.xxx.EXE.SSL]LIBSSL.OLB" library.
      SSL_TASK  Just build the "[.xxx.EXE.SSL]SSL_TASK.EXE" program.
      TEST      Just build the "[.xxx.EXE.TEST]" test programs for OpenSSL.
      APPS      Just build the "[.xxx.EXE.APPS]" application programs for OpenSSL.

<bits> must be one of the following:

      ""        compile using default pointer size
      32        compile using 32 bit pointer size
      64        compile using 64 bit pointer size

<debug-p> must be one of the following:

      DEBUG     compile with debugging info (will not optimize)
      NODEBUG   compile without debugging info (will optimize)

<compiler> must be one of the following:

      DECC      For DEC C.
      GNUC      For GNU C.


You will find the crypto library in [.xxx.EXE.CRYPTO] (where xxx is VAX,
ALPHA or IA64), called SSL_LIBCRYPTO32.OLB or SSL_LIBCRYPTO.OLB depending
on how it was built.  You will find the SSL library in [.xxx.EXE.SSL],
named SSL_LIBSSL32.OLB or SSL_LIBSSL.OLB, and you will find a bunch of
useful programs in [.xxx.EXE.APPS].  However, these shouldn't be used
right off unless it's just to test them.  For production use, make sure
you install first, see Installation below.

Note 1: Some programs in this package require a TCP/IP library.

Note 2: if you want to compile the crypto library only, please make sure
        you have at least done a @MAKEVMS CONFIG, a @MAKEVMS BUILDINF and
        a @MAKEVMS SOFTLINKS.  A lot of things will break if you don't.


Logical names:
==============

There are a few things that can't currently be given through the command
line.  Instead, logical names are used.

Currently, the logical names supported are:

      OPENSSL_NO_ASM    with value YES, the assembler parts of OpenSSL will
                        not be used.  Instead, plain C implementations are
                        used.  This is good to try if something doesn't work.
      OPENSSL_NO_'alg'  with value YES, the corresponding crypto algorithm
                        will not be implemented.  Supported algorithms to
                        do this with are: RSA, DSA, DH, MD2, MD4, MD5, RIPEMD,
                        SHA, DES, MDC2, CR2, RC4, RC5, IDEA, BF, CAST, HMAC,
                        SSL2.  So, for example, having the logical name
                        OPENSSL_NO_RSA with the value YES means that the
                        LIBCRYPTO.OLB library will not contain an RSA
                        implementation.


Test:
=====

Testing is very simple, just do the following:

  @[.TEST]TESTS

If a test fails, try with defining the logical name OPENSSL_NO_ASM (yes,
it's an ugly hack!) and rebuild. Please send a bug report to
<<EMAIL>>, including the output of "openssl version -a"
and of the failed test.


Installation:
=============

Installation is easy, just do the following:

  @INSTALL <root> <bits>

<root> is the directory in which everything will be installed,
subdirectories, libraries, header files, programs and startup command
procedures.

<bits> works the same way as for MAKEVMS.COM

N.B.: INSTALL.COM builds a new directory structure, different from
the directory tree where you have now build OpenSSL.

In the [.VMS] subdirectory of the installation, you will find the
following command procedures:

  OPENSSL_STARTUP.COM

        defines all needed logical names.  Takes one argument that
        tells it in what logical name table to insert the logical
        names.  If you insert if it SYS$MANAGER:SYSTARTUP_VMS.COM, the
        call should look like this: 

          @openssldev:[openssldir.VMS]OPENSSL_STARTUP "/SYSTEM"

  OPENSSL_UTILS.COM

        sets up the symbols to the applications.  Should be called
        from for example SYS$MANAGER:SYLOGIN.COM 

  OPENSSL_UNDO.COM

	deassigns the logical names created with OPENSSL_STARTUP.COM.

The logical names that are set up are the following:

  SSLROOT       a dotted concealed logical name pointing at the
                root directory.

  SSLCERTS      Initially an empty directory, this is the default
		location for certificate files.
  SSLPRIVATE	Initially an empty directory, this is the default
		location for private key files.

  SSLEXE        Contains the openssl binary and a few other utility
		programs.
  SSLINCLUDE    Contains the header files needed if you want to
		compile programs with libcrypto or libssl.
  SSLLIB        Contains the OpenSSL library files themselves:
  		- SSL_LIBCRYPTO32.OLB and SSL_LIBSSL32.OLB or
		- SSL_LIBCRYPTO.OLB and SSL_LIBSSL.OLB

  OPENSSL	Same as SSLINCLUDE.  This is because the standard
		way to include OpenSSL header files from version
		0.9.3 and on is:

			#include <openssl/header.h>

		For more info on this issue, see the INSTALL. file
		(the NOTE in section 4 of "Installation in Detail").
		You don't need to "deleting old header files"!!!


Backward portability:
=====================

One great problem when you build a library is making sure it will work
on as many versions of VMS as possible.  Especially, code compiled on
OpenVMS version 7.x and above tend to be unusable in version 6.x or
lower, because some C library routines have changed names internally
(the C programmer won't usually see it, because the old name is
maintained through C macros).  One obvious solution is to make sure
you have a development machine with an old enough version of OpenVMS.
However, if you are stuck with a bunch of Alphas running OpenVMS version
7.1, you seem to be out of luck.  Fortunately, the DEC C header files
are cluttered with conditionals that make some declarations and definitions
dependent on the OpenVMS version or the C library version, *and* you
can use those macros to simulate older OpenVMS or C library versions,
by defining the macros _VMS_V6_SOURCE, __VMS_VER and __CTRL_VER with
correct values.  In the compilation scripts, I've provided the possibility
for the user to influence the creation of such macros, through a bunch of
symbols, all having names starting with USER_.  Here's the list of them:

  USER_CCFLAGS		 - Used to give additional qualifiers to the
			   compiler.  It can't be used to define macros
			   since the scripts will do such things as well.
			   To do such things, use USER_CCDEFS.
  USER_CCDEFS		 - Used to define macros on the command line.  The
			   value of this symbol will be inserted inside a
			   /DEFINE=(...).
  USER_CCDISABLEWARNINGS - Used to disable some warnings.  The value is
			   inserted inside a /DISABLE=WARNING=(...).

So, to maintain backward compatibility with older VMS versions, do the
following before you start compiling:

  $ USER_CCDEFS := _VMS_V6_SOURCE=1,__VMS_VER=60000000,__CRTL_VER=60000000
  $ USER_CCDISABLEWARNINGS := PREOPTW

The USER_CCDISABLEWARNINGS is there because otherwise, DEC C will complain
that those macros have been changed.

Note: Currently, this is only useful for library compilation.  The
      programs will still be linked with the current version of the
      C library shareable image, and will thus complain if they are
      faced with an older version of the same C library shareable image.
      This will probably be fixed in a future revision of OpenSSL.


Possible bugs or quirks:
========================

I'm not perfectly sure all the programs will use the SSLCERTS:
directory by default, it may very well be that you have to give them
extra arguments.  Please experiment.


TODO:
=====

There are a few things that need to be worked out in the VMS version of
OpenSSL, still:

- Description files. ("Makefile's" :-))
- Script code to link an already compiled build tree.
- A VMSINSTALlable version (way in the future, unless someone else hacks).
- shareable images (DLL for you Windows folks).

There may be other things that I have missed and that may be desirable.
Please send mail to <<EMAIL>> or to me directly if you
have any ideas.

--
Richard Levitte <<EMAIL>>
2000-02-27, 2011-03-18
