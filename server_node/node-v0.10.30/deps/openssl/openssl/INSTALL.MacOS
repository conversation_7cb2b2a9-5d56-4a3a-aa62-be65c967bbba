OpenSSL - Port To The Macintosh OS 9 or Earlier
===============================================

Thanks to <PERSON> <<EMAIL>> initial support for Mac OS (pre
X) is now provided. "Initial" means that unlike other platforms where you
get an SDK and a "swiss army" openssl application, on Macintosh you only
get one sample application which fetches a page over HTTPS(*) and dumps it
in a window. We don't even build the test applications so that we can't
guarantee that all algorithms are operational.

Required software:

- StuffIt Expander 5.5 or later, alternatively MacGzip and SUNtar;
- Scriptable Finder;
- CodeWarrior Pro 5;

Installation procedure:

- fetch the source at ftp://ftp.openssl.org/ (well, you probably already
  did, huh?)
- unpack the .tar.gz file:
	- if you have StuffIt Expander then just drag it over it;
	- otherwise uncompress it with MacGzip and then unpack with SUNtar;
- locate MacOS folder in OpenSSL source tree and open it;
- unbinhex mklinks.as.hqx and OpenSSL.mcp.hqx if present (**), do it
  "in-place", i.e. unpacked files should end-up in the very same folder;
- execute mklinks.as;
- open OpenSSL.mcp(***) and build 'GetHTTPS PPC' target(****);
- that's it for now;

(*)	URL is hardcoded into ./MacOS/GetHTTPS.src/GetHTTPS.cpp, lines 40
        to 42, change appropriately.
(**)	If you use SUNtar, then it might have already unbinhexed the files
	in question.
(***)	The project file was saved with CW Pro 5.3. If you have an earlier
	version and it refuses to open it, then download
	http://www.openssl.org/~appro/OpenSSL.mcp.xml and import it
	overwriting the original OpenSSL.mcp.
(****)	Other targets are works in progress. If you feel like giving 'em a
	shot, then you should know that OpenSSL* and Lib* targets are
	supposed to be built with the GUSI, MacOS library which mimics
	BSD sockets and some other POSIX APIs. The GUSI distribution is
	expected to be found in the same directory as the openssl source tree,
	i.e., in the parent directory to the one where this very file,
	namely INSTALL.MacOS, resides. For more information about GUSI, see
	http://www.iis.ee.ethz.ch/~neeri/macintosh/gusi-qa.html

Finally some essential comments from our generous contributor:-)

"I've gotten OpenSSL working on the Macintosh. It's probably a bit of a
hack, but it works for what I'm doing. If you don't like the way I've done
it, then feel free to change what I've done. I freely admit that I've done
some less-than-ideal things in my port, and if you don't like the way I've
done something, then feel free to change it-- I won't be offended!

... I've tweaked "bss_sock.c" a little to call routines in a "MacSocket"
library I wrote. My MacSocket library is a wrapper around OpenTransport,
handling stuff like endpoint creation, reading, writing, etc. It is not
designed as a high-performance package such as you'd use in a webserver,
but is fine for lots of other applications. MacSocket also uses some other
code libraries I've written to deal with string manipulations and error
handling. Feel free to use these things in your own code, but give me
credit and/or send me free stuff in appreciation! :-)

...

If you have any questions, feel free to email me as the following:

<EMAIL>

-Roy Wood"

