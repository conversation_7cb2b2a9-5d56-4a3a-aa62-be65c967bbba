
 INSTALLATION ON THE WIN64 PLATFORM
 ----------------------------------

 Caveat lector
 -------------

 As of moment of this writing Win64 support is classified "initial"
 for the following reasons.

 - No assembler modules are engaged upon initial 0.9.8 release.
 - API might change within 0.9.8 life-span, *but* in a manner which
   doesn't break backward binary compatibility. Or in other words,
   application programs compiled with initial 0.9.8 headers will
   be expected to work with future minor release .DLL without need
   to re-compile, even if future minor release features modified API.
 - Above mentioned API modifications have everything to do with
   elimination of a number of limitations, which are normally
   considered inherent to 32-bit platforms. Which in turn is why they
   are treated as limitations on 64-bit platform such as Win64:-)
   The current list comprises [but not necessarily limited to]:

   - null-terminated strings may not be longer than 2G-1 bytes,
     longer strings are treated as zero-length;
   - dynamically and *internally* allocated chunks can't be larger
     than 2G-1 bytes;
   - inability to encrypt/decrypt chunks of data larger than 4GB
     [it's possibly to *hash* chunks of arbitrary size through];

   Neither of these is actually big deal and hardly encountered
   in real-life applications.

 Compiling procedure
 -------------------

 You will need Perl. You can run under Cygwin or you can download
 ActiveState Perl from http://www.activestate.com/ActivePerl.

 You will need Microsoft Platform SDK, available for download at
 http://www.microsoft.com/msdownload/platformsdk/sdkupdate/. As per
 April 2005 Platform SDK is equipped with Win64 compilers, as well
 as assemblers, but it might change in the future.

 To build for Win64/x64:

 > perl Configure VC-WIN64A
 > ms\do_win64a
 > nmake -f ms\ntdll.mak
 > cd out32dll
 > ..\ms\test

 To build for Win64/IA64:

 > perl Configure VC-WIN64I
 > ms\do_win64i
 > nmake -f ms\ntdll.mak
 > cd out32dll
 > ..\ms\test

 Naturally test-suite itself has to be executed on the target platform.

 Installation
 ------------

 TBD, for now see INSTALL.W32.

