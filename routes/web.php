<?php

use App\Http\Controllers\Api\TecDoc\TecDocFileController;
use App\Http\Controllers\Api\User\Payment\BuyMeACoffeeController;
use App\Http\Controllers\Api\User\Payment\CassoController;
use App\Http\Controllers\Api\User\Payment\NganLuongController;
use App\Http\Controllers\Api\User\Payment\NowpaymentsController;
use App\Http\Controllers\Api\User\Payment\PayeerController;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Api\User\Payment\PaymentController;
use App\Http\Controllers\Api\User\Payment\PayOnController;
use App\Http\Controllers\Api\User\Payment\PayPalController;
use App\Http\Controllers\Api\User\Payment\PayPalManualController;
use App\Http\Controllers\Api\User\Payment\StripeController;
use App\Http\Controllers\Api\User\Payment\TetherController;
use App\Http\Controllers\Api\User\Payment\WesternunionController;
use App\Http\Controllers\Api\User\PaymentDevice\NowpaymentsDeviceController;
use App\Http\Controllers\Api\User\PaymentDevice\PaymentDeviceController;
use App\Http\Controllers\Api\User\PaymentDevice\PayPalDeviceController;
use App\Http\Controllers\SelectEngineController;
use App\Http\Controllers\Web\LoginController;
use App\Http\Controllers\Web\LogoutController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('/', function () {
//     return response()->view('welcome');
// });


//lưu lại request chống hack
Route::post('/rq/luu/lg.js', 'App\Http\Controllers\Api\User\JsLogController@saveRequest')->middleware('get_user');
Route::get('/jssav/{slug?}.js', 'App\Http\Controllers\Api\User\JsLogController@getFileJs')->where('slug', '.*')->name('getFileJs');

Route::group(['middleware' => ['get_user_web']], function () {
    // Route::get('/rhome', [HomeController::class, 'index'])->name('home.index');
    Route::get('/p/login', [LoginController::class, 'show'])->name('login.show');
    Route::post('/p/login', [LoginController::class, 'perform'])->name('login.perform');
    Route::get('/p/logout', [LogoutController::class, 'perform'])->name('logout.perform');
});
//
Route::get('payment_drcar', [PaymentController::class, 'pay'])->name('payment_drcar')->middleware('voucher_check', 'payment_check_user_service');

//paypal thủ công
Route::get('paypal', [PayPalController::class, 'inputPayPalCreate'])->name('inputPayPalCreate');
Route::post('paypal', [PayPalController::class, 'inputPayPalCreateLink'])->name('inputPayPalCreateLink');


//Thanh toán dịch vụ
//paypal tự động
Route::get('payment_drcar/paypal-create', [PayPalController::class, 'paypalCreate'])->name('paypalCreate')->middleware('voucher_check', 'payment_check_user_service');
Route::get('payment_drcar/paypal-success', [PayPalController::class, 'paypalSuccess'])->name('paypalSuccess');
Route::get('payment_drcar/paypal-cancel', [PayPalController::class, 'paypalCancel'])->name('paypalCancel');

Route::get('payment_drcar/paypal-manual-create', [PayPalManualController::class, 'paypalCreate'])->name('paypalCreate2')->middleware('voucher_check', 'payment_check_user_service');

Route::get('payment_drcar/westernunion-create', [WesternunionController::class, 'westernunionCreate'])->name('westernunionCreate2')->middleware('voucher_check', 'payment_check_user_service');

Route::get('payment_drcar/tether-create', [TetherController::class, 'tetherCreate'])->name('tetherCreate2')->middleware('voucher_check', 'payment_check_user_service');


Route::get('payment_drcar/stripe-create', [StripeController::class, 'stripeCreate'])->name('stripeCreate')->middleware('voucher_check', 'payment_check_user_service');
Route::get('payment_drcar/stripe-success', [StripeController::class, 'stripeSuccess'])->name('stripeSuccess');
Route::get('payment_drcar/stripe-cancel', [StripeController::class, 'stripeCancel'])->name('stripeCancel');


Route::get('payment_drcar/payeer-create', [PayeerController::class, 'payeerCreate'])->name('payeerCreate')->middleware('voucher_check', 'payment_check_user_service');
Route::get('payment_drcar/payeer-success', [PayeerController::class, 'payeerSuccess'])->name('payeerSuccess');
Route::get('payment_drcar/payeer-cancel', [PayeerController::class, 'payeerCancel'])->name('payeerCancel');

Route::get('payment_drcar/nowpayments-create', [NowpaymentsController::class, 'nowpaymentsCreate'])->name('nowpaymentsCreate')->middleware('voucher_check', 'payment_check_user_service');
Route::get('payment_drcar/nowpayments-success', [NowpaymentsController::class, 'nowpaymentsSuccess'])->name('nowpaymentsSuccess');
Route::get('payment_drcar/nowpayments-cancel', [NowpaymentsController::class, 'nowpaymentsCancel'])->name('nowpaymentsCancel');
Route::post('payment_drcar/nowpayments-success', [NowpaymentsController::class, 'nowpaymentsSuccess'])->name('nowpaymentsSuccess2');

Route::get('payment_drcar/payon-create', [PayOnController::class, 'payonCreate'])->name('payonCreate')->middleware('voucher_check', 'payment_check_user_service');
Route::get('payment_drcar/payon-success', [PayOnController::class, 'payonSuccess'])->name('payonSuccess');
Route::get('payment_drcar/payon-cancel', [PayOnController::class, 'payonCancel'])->name('payonCancel');
Route::post('payment_drcar/payon-success', [PayOnController::class, 'payonSuccess'])->name('payonSuccess2');


Route::get('payment_drcar/buymeacoffee-create', [BuyMeACoffeeController::class, 'buymeacoffeeCreate'])->name('buymeacoffeeCreate')->middleware('voucher_check', 'payment_check_user_service');
Route::get('payment_drcar/buymeacoffee-success', [BuyMeACoffeeController::class, 'buymeacoffeeSuccess'])->name('buymeacoffeeSuccess');
Route::get('payment_drcar/buymeacoffee-cancel', [BuyMeACoffeeController::class, 'buymeacoffeeCancel'])->name('buymeacoffeeCancel');
Route::post('payment_drcar/buymeacoffee-success', [BuyMeACoffeeController::class, 'buymeacoffeeSuccess'])->name('buymeacoffeeSuccess2');

Route::get('payment_drcar/nganluong-create', [NganLuongController::class, 'nganLuongCreate'])->name('nganLuongCreate')->middleware('voucher_check', 'payment_check_user_service');
Route::get('payment_drcar/nganluong-success', [NganLuongController::class, 'nganLuongReturn'])->name('nganLuongSuccess');
Route::get('payment_drcar/nganluong-cancel', [NganLuongController::class, 'nganLuongReturn'])->name('nganLuongCancel');

Route::get('/payment_webhook/casso-sync', [CassoController::class, 'sync'])->name('cassoSuccessSync');
Route::get('payment_drcar/casso-create', [CassoController::class, 'cassoCreate'])->name('cassoCreate')->middleware('voucher_check', 'payment_check_user_service');
Route::post('payment_drcar/casso-success', [CassoController::class, 'cassoSuccess'])->name('cassoSuccess');

//Thanh toán device
Route::get('payment_device_drcar', [PaymentDeviceController::class, 'pay'])->name('payment_device_drcar')->middleware('voucher_check', 'payment_check_plan_device_user');

//paypal tự động mua them thiet bi
Route::get('payment_device_drcar/paypal-create', [PayPalDeviceController::class, 'paypalDeviceCreate'])->name('paypalDeviceCreate')->middleware('voucher_check', 'payment_check_plan_device_user');
Route::get('payment_device_drcar/paypal-success', [PayPalDeviceController::class, 'paypalDeviceSuccess'])->name('paypalDeviceSuccess');
Route::get('payment_device_drcar/paypal-cancel', [PayPalDeviceController::class, 'paypalDeviceCancel'])->name('paypalDeviceCancel');


Route::get('payment_device_drcar/nowpayments-create', [NowpaymentsDeviceController::class, 'nowpaymentsDeviceCreate'])->name('nowpaymentsDeviceCreate')->middleware('voucher_check', 'payment_check_plan_device_user');
Route::get('payment_device_drcar/nowpayments-success', [NowpaymentsDeviceController::class, 'nowpaymentsDeviceSuccess'])->name('nowpaymentsDeviceSuccess');
Route::get('payment_device_drcar/nowpayments-cancel', [NowpaymentsDeviceController::class, 'nowpaymentsDeviceCancel'])->name('nowpaymentsDeviceCancel');
Route::post('payment_device_drcar/nowpayments-success', [NowpaymentsDeviceController::class, 'nowpaymentsDeviceSuccess'])->name('nowpaymentsDeviceSuccess2');



Route::get('/download_page_for_pc', function () {
    return response()->view('download_page_for_pc');
});

Route::get('/redirect-to-link', 'App\Http\Controllers\Api\User\LinkController@redirectToLink');
Route::get('/print_page', 'App\Http\Controllers\Web\PrintPageController@print');

// // //alldata eu v1 (de check token)
// Route::middleware(['cors'])->prefix('alldata')->group(function () {
//     Route::get('/vehicle/ymme/years', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');
//     Route::get('/vehicle/ymme/year/{yearId}/makes', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');
//     Route::get('/vehicle/ymme/year/{yearId}/make/{makeId}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');
// });


Route::get('/w1/manufacturers/{car}/{model}/engines', [SelectEngineController::class, 'indexSelectEngine'])->name('home2');

Route::middleware(['cors', 'blockip', 'check_security_web'])->group(function () { // alldata eu v1 vs v2 and autodata
    //Lấy url param autodata
    Route::get('/app_redirect/data', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');

    Route::get('/w1/vehicles/{manufacturer_id}/{model_id}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');
    Route::get('/w1/manufacturers/{manufacturer_id}/{model_id}/engines', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');
    Route::get('/w1/{route_name}/{vehicle_id}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');
    Route::get('/w1/vehicles/variants/{route_name}/{vehicle_id}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');
    Route::get('/w1/diagram/{route_name}/{vehicle_id}/{num1}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');
    Route::get('/w1/{route_name}/{vehicle_id}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');
    Route::get('/w1/{route_name}/{vehicle_id}/{num1}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet');

    Route::get('/cookie-redirect', 'App\Http\Controllers\Api\User\RedirectCookieController@redirect');

    Route::get('/{slug?}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexGet')->where('slug', '.*')->name('home3');
    Route::post('/{slug?}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexPost')->where('slug', '.*')->name('home4');
    Route::patch('/{slug?}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexPost')->where('slug', '.*')->name('home5');
    Route::put('/{slug?}', 'App\Http\Controllers\Api\DataLeechControllerForm3Controller@indexPost')->where('slug', '.*')->name('home6');
});


