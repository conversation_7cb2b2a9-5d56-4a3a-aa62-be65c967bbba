<?php

use App\Http\Controllers\Api\TecDoc\TecDocFileController;
use App\Http\Controllers\Api\TecDoc\TecDocInformationController;
use App\Http\Controllers\Api\TecDoc\TecDocMenuController;
use App\Http\Controllers\Api\TecDoc\TecDocSearchController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

///api/check_and_fix_server //Auto check and fix server 3 min
///api/auto_check_preventive //Kiểm tra server dự phòng 10 min
///api/run_every_minute //Chạy 1 phút 1 lần


Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

//chuyen doi disable app
Route::get('switching/app/status', 'App\Http\Controllers\Api\Admin\AdminTestApiControllerController@switchingAppStatus');
Route::get('/info_server/mac_address', 'App\Http\Controllers\Api\InfoServer\InfoServerController@getMAcAddressExec');

Route::middleware(['cors', 'blockip'])
    ->prefix('/')->group(function () {

        Route::prefix('payment')->group(function () {
            Route::get('nganluong/post/create', 'App\Http\Controllers\Api\Payment\NganLuongController@create');
            Route::get('nganluong/post/return', 'App\Http\Controllers\Api\Payment\NganLuongController@return');
        });

        //Place
        Route::prefix('place')->group(function () {
            //App-Theme
            Route::get('/vn/{type}/{parent_id}', 'App\Http\Controllers\Api\PlaceController@getWithType');
            Route::get('/vn/{type}', 'App\Http\Controllers\Api\PlaceController@getWithType');
        });


        //Up 1 ảnh
        Route::post('images', 'App\Http\Controllers\Api\UploadImageController@upload');
        Route::post('images_v2', 'App\Http\Controllers\Api\UploadImageController@uploadV2');

        //Ghi log lỗi
        Route::post('logger_fail', 'App\Http\Controllers\Api\LoggerFailController@log');

        //
        // Khu vực chạy Cron Job
        //

        //check_and_fix_server chạy mỗi phút
        Route::get('check_and_fix_server', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server');

        Route::get('check_and_fix_server_alldata_eu', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_alldata_eu');
        Route::get('check_and_fix_server_alldata_us', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_alldata_us');
        Route::get('check_and_fix_server_autodata', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_autodata');
        Route::get('check_and_fix_server_autodata_italy', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_autodata_italy');
        Route::get('check_and_fix_server_haynespro', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_haynespro');
        Route::get('check_and_fix_server_haynespro_truck', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_haynespro_truck');
        Route::get('check_and_fix_server_identifix', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_identifix');
        Route::get('check_and_fix_server_tecdoc', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_tecdoc');
        Route::get('check_and_fix_server_mitchell_repair_center', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_mitchell_repair_center');
        Route::get('check_and_fix_server_mitchell_prodemand', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_mitchell_prodemand');
        Route::get('check_and_fix_server_partslink24', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_partslink24');
        Route::get('check_and_fix_server_partslink24_mobile', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_partslink24_mobile');
        Route::get('check_and_fix_server_ford_pts', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@check_and_fix_server_ford_pts');


        //Check server dự phòng
        Route::get('auto_check_preventive', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@auto_check_preventive');
        //Chạy 1 phút 1 lần
        Route::get('run_every_minute', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@run_every_minute');
        //
        //


        //
        Route::get('info_client', 'App\Http\Controllers\Api\InfoClientController@index');

        Route::get('cookies/get_detail', 'App\Http\Controllers\Api\CookiesController@index');

        Route::get('/admin/test_api', 'App\Http\Controllers\Api\Admin\AdminTestServerCarController@test_api');

        Route::prefix('admin')->middleware('log_access_admin')->group(function () {


            //Đăng nhập
            Route::post('/login', 'App\Http\Controllers\Api\Admin\AdminLoginController@login');

            //update profile
            Route::put('/info_admin', 'App\Http\Controllers\Api\Admin\AdminInfoController@update')->middleware('admin_auth');

            //Up 1 ảnh
            Route::post('images', 'App\Http\Controllers\Api\UploadImageController@upload')->middleware('admin_auth');
            Route::post('images_v2', 'App\Http\Controllers\Api\UploadImageController@uploadV2')->middleware('admin_auth');


            //update one field config remote, update cookie remote
            Route::post('/update_one_field_config_remote', 'App\Http\Controllers\Api\Admin\AdminConfigController@update_one_field_config_remote');
            Route::post('/update_account_items_remote', 'App\Http\Controllers\Api\Admin\AdminConfigController@update_account_items_remote');
            //updateConfig
            Route::put('/update_config', 'App\Http\Controllers\Api\Admin\AdminConfigController@updateConfig')->middleware('admin_auth');
            //getConfig
            Route::get('/get_config', 'App\Http\Controllers\Api\Admin\AdminConfigController@getConfig')->middleware('admin_auth');

            //updateSetting
            Route::put('/setting', 'App\Http\Controllers\Api\Admin\AdminConfigController@updateSetting')->middleware('admin_auth');
            //getSetting
            Route::get('/setting', 'App\Http\Controllers\Api\Admin\AdminConfigController@getSetting')->middleware('admin_auth');



            //Badges
            Route::get('/badges', 'App\Http\Controllers\Api\Admin\AdminBadgesController@getBadges')->middleware('get_admin');

            //Thêm user
            Route::post('/users', 'App\Http\Controllers\Api\Admin\AdminUserController@create')->middleware('admin_auth');
            //Danh sách user
            Route::get('/users', 'App\Http\Controllers\Api\Admin\AdminUserController@getAll')->middleware('get_admin');
            //Sửa user
            Route::put('/users/{user_id}', 'App\Http\Controllers\Api\Admin\AdminUserController@update')->middleware('admin_auth');
            //Get 1 user
            Route::get('/users/{user_id}', 'App\Http\Controllers\Api\Admin\AdminUserController@getOne')->middleware('admin_auth');
            //Get 1 user by email
            Route::get('/users/emails/{email}', 'App\Http\Controllers\Api\Admin\AdminUserController@getOneByEmail')->middleware('get_admin');
            //Get 1 user by email
            Route::get('/users_renew/emails/{email}', 'App\Http\Controllers\Api\Admin\AdminUserController@getRenewInfoByEmail');
            //Export excel
            Route::get('/get_link_export_excel_users', 'App\Http\Controllers\Api\Admin\AdminUserController@getLinkExportExcelUser')->middleware('get_admin');
            //download excel
            Route::get('/download_export_excel_users', 'App\Http\Controllers\Api\Admin\AdminUserController@downloadExportExcelUser');


            //Get 1 user by username
            Route::get('/users/usernames/{username}', 'App\Http\Controllers\Api\Admin\AdminUserController@getOneByUsername')->middleware('get_admin');
            //Edit 1 user by username
            Route::put('/users/usernames/{username}/edit/note', 'App\Http\Controllers\Api\Admin\AdminUserController@editNoteByUsername')->middleware('get_admin');
            //Sửa user status
            Route::put('/users/{user_id}/status', 'App\Http\Controllers\Api\Admin\AdminUserController@updateStatus')->middleware('admin_auth');
            //Sửa user type
            Route::put('/users/{user_id}/type', 'App\Http\Controllers\Api\Admin\AdminUserController@updateType')->middleware('admin_auth');
            //Sửa user is vip
            Route::put('/users/{user_id}/is_admin', 'App\Http\Controllers\Api\Admin\AdminUserController@updateIsAdmin')->middleware('admin_auth');
            //Sửa user is block
            Route::put('/users/{user_id}/is_block', 'App\Http\Controllers\Api\Admin\AdminUserController@updateIsBlock')->middleware('admin_auth');
            //Sửa user is user ios
            Route::put('/users/{user_id}/is_user_ios', 'App\Http\Controllers\Api\Admin\AdminUserController@updateIsUserIos')->middleware('admin_auth');
            //Sửa giá trị trường user
            Route::put('/users/{user_id}/update_with_field', 'App\Http\Controllers\Api\Admin\AdminUserController@updateFieldStatus')->middleware('agency_auth');


            //Thêm email supports
            Route::post('/email_supports', 'App\Http\Controllers\Api\Admin\AdminEmailSupportController@create')->middleware('admin_auth');
            //Danh sách email_supports
            Route::get('/email_supports', 'App\Http\Controllers\Api\Admin\AdminEmailSupportController@getAll')->middleware('admin_auth');
            //Sửa email_supports
            Route::put('/email_supports/{email_id}', 'App\Http\Controllers\Api\Admin\AdminEmailSupportController@update')->middleware('admin_auth');
            //Sửa email_supports running
            Route::put('/email_supports/{email_id}/running', 'App\Http\Controllers\Api\Admin\AdminEmailSupportController@updateRunning')->middleware('admin_auth');
            //Get 1 email
            Route::get('/email_supports/{email_id}', 'App\Http\Controllers\Api\Admin\AdminEmailSupportController@getOne')->middleware('admin_auth');
            //delete email
            Route::delete('/email_supports/{email_id}', 'App\Http\Controllers\Api\Admin\AdminEmailSupportController@delete')->middleware('admin_auth');
            //send email
            Route::post('/email_supports/{email_id}/send', 'App\Http\Controllers\Api\Admin\AdminEmailSupportController@send')->middleware('admin_auth');
            //Danh sách lịch sử gửi email_supports
            Route::get('/history_send_email_supports', 'App\Http\Controllers\Api\Admin\AdminEmailSupportController@getAllHistorySendEmail')->middleware('admin_auth');




            //Thêm staff
            Route::post('/staff', 'App\Http\Controllers\Api\Admin\AdminStaffController@create')->middleware('admin_auth');
            //Danh sách staff
            Route::get('/staff', 'App\Http\Controllers\Api\Admin\AdminStaffController@getAll')->middleware('admin_auth');
            //Sửa staff
            Route::put('/staff/{staff_id}', 'App\Http\Controllers\Api\Admin\AdminStaffController@update')->middleware('admin_auth');
            //Get 1 staff
            Route::get('/staff/{staff_id}', 'App\Http\Controllers\Api\Admin\AdminStaffController@getOne')->middleware('admin_auth');
            //Get 1 staff by phone number
            Route::get('/staff/numbers/{phone_number}', 'App\Http\Controllers\Api\Admin\AdminStaffController@getOneByEmail')->middleware('admin_auth');
            //Sửa staff status
            Route::put('/staff/{staff_id}/status', 'App\Http\Controllers\Api\Admin\AdminStaffController@updateStatus')->middleware('admin_auth');
            //delete staff
            Route::delete('/staff/{staff_id}', 'App\Http\Controllers\Api\Admin\AdminStaffController@delete')->middleware('admin_auth');


            //Get 1 staff by phone number
            Route::get('/referral_user', 'App\Http\Controllers\Api\Admin\AdminReferralUserController@getAll')->middleware('admin_auth');


            //hyêu cầu nạp tiền
            Route::get('/recharge', 'App\Http\Controllers\Api\Admin\AdminRechargeController@histories')->middleware('admin_auth');
            //yêu cầu nạp tiền
            Route::put('/recharge/{recharge_id}/status', 'App\Http\Controllers\Api\Admin\AdminRechargeController@updateStatus')->middleware('admin_auth');
            //xem  1 yêu cầu nạp tiền
            Route::get('/recharge/{recharge_id}', 'App\Http\Controllers\Api\Admin\AdminRechargeController@getOne')->middleware('admin_auth');



            //Xóa user
            Route::delete('/users/{user_id}', 'App\Http\Controllers\Api\Admin\AdminUserController@delete')->middleware('admin_auth');
            //XCộng trừ
            Route::post('/users/{user_id}/add_sub_money', 'App\Http\Controllers\Api\Admin\AdminUserController@addSubMoney')->middleware('admin_auth');
            //reset user
            Route::put('/users/{user_id}/reset_password', 'App\Http\Controllers\Api\Admin\AdminUserController@resetPassword')->middleware('get_admin');
            //reset device
            Route::put('/users/{user_id}/reset_device', 'App\Http\Controllers\Api\Admin\AdminUserController@resetDevice')->middleware('get_admin');

            //report
            Route::get('/report', 'App\Http\Controllers\Api\Admin\AdminReportController@overview')->middleware('admin_auth');

            //Báo cáo lịch sử trạng thái server
            Route::get('/history_change_status_3', 'App\Http\Controllers\Api\Admin\AdminHistoryStatusServer3Controller@getAll')->middleware('admin_auth');

            //Tạo plans
            Route::get('/plan_services', 'App\Http\Controllers\Api\Admin\AdminPlanController@create')->middleware('admin_auth');

            //Thêm hoặc bớt time
            Route::post('/sub_add_expiry', 'App\Http\Controllers\Api\Admin\AdminRenewalController@sub_add_expiry')->middleware('agency_auth');
            //Lịch sử gia hạn
            Route::get('/renewal_history', 'App\Http\Controllers\Api\Admin\AdminRenewalController@getAll')->middleware('agency_auth');
            //Cho dùng thử 2 tiếng
            Route::post('/try_demo', 'App\Http\Controllers\Api\Admin\AdminRenewalController@try_demo')->middleware('agency_auth');
            //Export excel
            Route::get('/get_link_export_excel', 'App\Http\Controllers\Api\Admin\AdminRenewalController@getLinkExportExcel')->middleware('agency_auth');
            //download excel
            Route::get('/download_export_excel', 'App\Http\Controllers\Api\Admin\AdminRenewalController@downloadExportExcel');

            //Thêm hoặc bớt device
            Route::post('/sub_add_device', 'App\Http\Controllers\Api\Admin\AdminRenewalController@sub_add_device')->middleware('agency_auth');
            Route::post('/cancel_device', 'App\Http\Controllers\Api\Admin\AdminRenewalController@cancelDevice')->middleware('agency_auth');


            //Giao diện web
            Route::get('/agency_web_theme', 'App\Http\Controllers\Api\Admin\AgencyWebThemeController@getOne')->middleware('agency_auth');
            //Giao diện web
            Route::put('/agency_web_theme', 'App\Http\Controllers\Api\Admin\AgencyWebThemeController@update')->middleware('agency_auth');


            //Danh sách người chat với admin
            Route::get('/chat/persons', 'App\Http\Controllers\Api\Admin\AdminChatController@getAllPerson')->middleware('admin_auth');
            //Danh sách tin nhắn 1 người
            Route::get('/chat/messages', 'App\Http\Controllers\Api\Admin\AdminChatController@getAllMessage')->middleware('admin_auth');
            //Chat cho 1 người
            Route::post('/chat/messages', 'App\Http\Controllers\Api\Admin\AdminChatController@sendMessage')->middleware('admin_auth');
            //Thu hồi
            Route::get('/chat/messages/recall/{mess_id}', 'App\Http\Controllers\Api\Admin\AdminChatController@recallMessage')->middleware('admin_auth');

            //Lấy tất cả token từ Nhà cung cấp token
            Route::get('/provider_server_3', 'App\Http\Controllers\Api\User\CheckServerProviderServer3Controller@getAll');

            //Danh sách proxy
            Route::get('/proxy_servers', 'App\Http\Controllers\Api\Admin\AdminProxyServerController@getAll')->middleware('admin_auth');
            //Xóa 1 proxy
            Route::delete('/proxy_servers/{proxy_server_id}', 'App\Http\Controllers\Api\Admin\AdminProxyServerController@delete')->middleware('admin_auth');
            //Tạo 1 proxy
            Route::post('/proxy_servers', 'App\Http\Controllers\Api\Admin\AdminProxyServerController@create')->middleware('admin_auth');
            //Cập nhật 1 server
            Route::put('/proxy_servers/{proxy_server_id}', 'App\Http\Controllers\Api\Admin\AdminProxyServerController@update')->middleware('admin_auth');
            //kiểm tra 1 server
            Route::get('/proxy_servers/{proxy_server_id}/check', 'App\Http\Controllers\Api\Admin\AdminProxyServerController@checkServerProxy')->middleware('admin_auth');


            //Danh sách cookie
            Route::get('/cookie_rotates', 'App\Http\Controllers\Api\Admin\AdminCookieRotateController@getAll')->middleware('admin_auth');
            //Xóa 1 cookie_rotate
            Route::delete('/cookie_rotates/{cookie_rotate_id}', 'App\Http\Controllers\Api\Admin\AdminCookieRotateController@delete')->middleware('admin_auth');
            //Tạo 1 cookie_rotate
            Route::post('/cookie_rotates', 'App\Http\Controllers\Api\Admin\AdminCookieRotateController@create')->middleware('admin_auth');
            //Cập nhật 1 server
            Route::put('/cookie_rotates/{cookie_rotate_id}', 'App\Http\Controllers\Api\Admin\AdminCookieRotateController@update')->middleware('admin_auth');
            //kiểm tra 1 server
            Route::get('/cookie_rotates/{cookie_rotate_id}/check', 'App\Http\Controllers\Api\Admin\AdminCookieRotateController@checkCookie')->middleware('admin_auth');
            //Cookie sort
            Route::put('/cookie_rotates_sort', 'App\Http\Controllers\Api\Admin\AdminCookieRotateController@updateSortCookie');


            //Danh sách block ip
            Route::get('/blocked_ips', 'App\Http\Controllers\Api\Admin\AdminBlockedIPController@getAll')->middleware('admin_auth');
            //Xóa 1 proxy
            Route::delete('/blocked_ips/{id}', 'App\Http\Controllers\Api\Admin\AdminBlockedIPController@delete')->middleware('admin_auth');
            //Tạo 1 proxy
            Route::post('/blocked_ips', 'App\Http\Controllers\Api\Admin\AdminBlockedIPController@create')->middleware('admin_auth');
            //Danh sách history access
            Route::get('/history_access', 'App\Http\Controllers\Api\Admin\AdminBlockedIPController@getAllHistoryAccess')->middleware('admin_auth');
            //Danh sách lịch sử block
            Route::get('/history_block_ips', 'App\Http\Controllers\Api\Admin\AdminBlockedIPController@getAllHistoryBlockIp')->middleware('admin_auth');

            //Giá
            Route::get('/plan_services', 'App\Http\Controllers\Api\Admin\AdminPlanServiceController@getAll')->middleware('admin_auth');
            //Xóa 1 proxy
            Route::delete('/plan_services/{plan_service_id}', 'App\Http\Controllers\Api\Admin\AdminPlanServiceController@delete')->middleware('admin_auth');
            //Tạo 1 proxy
            Route::post('/plan_services', 'App\Http\Controllers\Api\Admin\AdminPlanServiceController@create')->middleware('admin_auth');
            //Cập nhật 1 server
            Route::put('/plan_services/{plan_service_id}', 'App\Http\Controllers\Api\Admin\AdminPlanServiceController@update')->middleware('admin_auth');

            //check_server_car check từng dịch vụ trong phần Cookies admin
            Route::get('/check_server_car', 'App\Http\Controllers\Api\Admin\AdminTestServerCarController@check')->middleware('admin_auth');
            //web_check
            Route::get('/web_check', 'App\Http\Controllers\Api\Admin\AdminTestServerCarController@web_check');
            //check_autodata
            Route::get('/check_autodata', 'App\Http\Controllers\Api\Admin\AdminTestServerCarController@check_autodata');
            //test_api o day
            Route::get('/check_autodata_proxy', 'App\Http\Controllers\Api\Admin\AdminTestServerCarController@check_autodata_proxy');



            //Get one
            Route::get('/agencies/{agency_id}', 'App\Http\Controllers\Api\Admin\AdminAgencyController@getOne')->middleware('admin_auth');
            //Danh sách ddai ly
            Route::get('/agencies', 'App\Http\Controllers\Api\Admin\AdminAgencyController@getAll')->middleware('admin_auth');
            //Xóa 1 dai y
            Route::delete('/agencies/{agency_id}', 'App\Http\Controllers\Api\Admin\AdminAgencyController@delete')->middleware('admin_auth');
            //Tạo 1 dai ly
            Route::post('/agencies', 'App\Http\Controllers\Api\Admin\AdminAgencyController@create')->middleware('admin_auth');
            //Cập nhật 1 dai ly
            Route::put('/agencies/{agency_id}', 'App\Http\Controllers\Api\Admin\AdminAgencyController@update')->middleware('admin_auth');
            //Thanh toán 1 dịch vụ
            Route::post('/paidForAgency', 'App\Http\Controllers\Api\Admin\AdminAgencyController@paidForAgency')->middleware('admin_auth');
            //Thanh toán tất cả dịch vụ
            Route::post('/paidForAgencyAllService', 'App\Http\Controllers\Api\Admin\AdminAgencyController@paidForAgencyAllService')->middleware('admin_auth');





            //Thêm 1 khách hàng
            Route::post('/agency_users', 'App\Http\Controllers\Api\Admin\AdminAgencyController@create_user')->middleware('get_admin');
            //Xóa 1 khách hàng
            Route::delete('/agency_users/{user_id}', 'App\Http\Controllers\Api\Admin\AdminAgencyController@delete_user')->middleware('get_admin');


            //Proxy
            Route::post('/proxy', 'App\Http\Controllers\Api\Admin\AdminProxyController@create');
            //Danh sách Proxy
            Route::get('/proxy', 'App\Http\Controllers\Api\Admin\AdminProxyController@getAll');
            //Sửa Proxy
            Route::put('/proxy/{proxy_id}', 'App\Http\Controllers\Api\Admin\AdminProxyController@update');
            //Xóa Proxy
            Route::delete('/proxy/{proxy_id}', 'App\Http\Controllers\Api\Admin\AdminProxyController@delete');
            //Proxy sort
            Route::put('/proxy_sort', 'App\Http\Controllers\Api\Admin\AdminProxyController@updateSortProxy');

            //Account
            Route::post('/accounts', 'App\Http\Controllers\Api\Admin\AdminAccountController@create');
            //Danh sách Account
            Route::get('/accounts', 'App\Http\Controllers\Api\Admin\AdminAccountController@getAll');
            //Sửa Account
            Route::put('/accounts/{account_id}', 'App\Http\Controllers\Api\Admin\AdminAccountController@update');
            //Xóa Account
            Route::delete('/accounts/{account_id}', 'App\Http\Controllers\Api\Admin\AdminAccountController@delete');
            //Account sort
            Route::put('/account_sort', 'App\Http\Controllers\Api\Admin\AdminAccountController@updateSortAccount');

            //Nhóm khách hàng
            Route::get('/group_users', 'App\Http\Controllers\Api\Admin\AdminGroupControllerController@getAll');
            Route::post('/group_users', 'App\Http\Controllers\Api\Admin\AdminGroupControllerController@create')->middleware('admin_auth');
            Route::put('/group_users/{group_user_id}', 'App\Http\Controllers\Api\Admin\AdminGroupControllerController@update')->middleware('admin_auth');
            Route::delete('/group_users/{group_user_id}', 'App\Http\Controllers\Api\Admin\AdminGroupControllerController@delete')->middleware('admin_auth');
            Route::get('/group_users/{group_user_id}', 'App\Http\Controllers\Api\Admin\AdminGroupControllerController@getOne')->middleware('admin_auth');

            //Voucher
            Route::get('/vouchers', 'App\Http\Controllers\Api\Admin\AdminVoucherController@getAll');
            Route::post('/vouchers', 'App\Http\Controllers\Api\Admin\AdminVoucherController@create')->middleware('admin_auth');
            Route::put('/vouchers/{voucher_id}', 'App\Http\Controllers\Api\Admin\AdminVoucherController@update')->middleware('admin_auth');
            Route::delete('/vouchers/{voucher_id}', 'App\Http\Controllers\Api\Admin\AdminVoucherController@delete')->middleware('admin_auth');
            Route::get('/vouchers/{voucher_id}', 'App\Http\Controllers\Api\Admin\AdminVoucherController@getOne')->middleware('admin_auth');

            //Top truy cập
            Route::get('/top/history_accesses', 'App\Http\Controllers\Api\Admin\AdminHistoryAccessController@getAll')->middleware('admin_auth');
            Route::get('/top/detail_access', 'App\Http\Controllers\Api\Admin\AdminHistoryAccessController@detailAccess')->middleware('admin_auth');


            Route::get('/test', 'App\Http\Controllers\Api\Admin\AdminTestApiControllerController@testApi');

            Route::get('/statistic', 'App\Http\Controllers\Api\Admin\AdminHistoryAccessController@statistic')->middleware('admin_auth');

            //Service model update
            Route::post('/service-models/toyota', 'App\Http\Controllers\Api\Admin\AdminServiceModelSetController@addOrUpdateToyotaModel')->middleware('admin_auth');
        });


        Route::prefix('user')->group(function () {

            //Đăng ký
            Route::post('/register', 'App\Http\Controllers\Api\User\RegisterController@register')->middleware(['throttle:24,1']);
            //Đăng nhập
            Route::post('/login', 'App\Http\Controllers\Api\User\LoginController@login');
            //Logout
            Route::post('/logout', 'App\Http\Controllers\Api\User\LoginController@logout')->middleware('user_auth');
            //profile
            Route::get('/profile', 'App\Http\Controllers\Api\User\ProfileController@getProfile')->middleware('user_auth');
            //profile
            Route::put('/profile', 'App\Http\Controllers\Api\User\ProfileController@updateProfile')->middleware('user_auth');
            Route::post('/change_password', 'App\Http\Controllers\Api\User\LoginController@change_password')->middleware('user_auth');


            //Up 1 ảnh
            Route::post('images', 'App\Http\Controllers\Api\UploadImageController@upload')->middleware('user_auth');

            //Badges
            Route::get('/badges', 'App\Http\Controllers\Api\User\BadgesController@getBadges')->middleware('get_user');


            //lịch sử cầu nạp tiền
            Route::get('/recharge', 'App\Http\Controllers\Api\User\RechargeController@histories')->middleware('user_auth');
            //yêu cầu nạp tiền
            Route::post('/recharge', 'App\Http\Controllers\Api\User\RechargeController@recharge')->middleware('user_auth');


            //Danh sách người chat với user
            Route::get('/person_chat', 'App\Http\Controllers\Api\User\UserMessageController@getAllPerson')->middleware('user_auth');

            //Xóa chat với 1 người
            Route::delete('/person_chat/{vs_user_id}', 'App\Http\Controllers\Api\User\UserMessageController@removeChat')->middleware('user_auth');


            //Danh sách tin nhắn chat với admin
            Route::get('/chat_with_admin/messages', 'App\Http\Controllers\Api\User\AdminMessageController@getAllMessage')->middleware('get_user');
            //Chat cho admin
            Route::post('/chat_with_admin/messages', 'App\Http\Controllers\Api\User\AdminMessageController@sendMessage')->middleware('get_user');
            //Thu hồi
            Route::get('/chat_with_admin/messages/recall/{mess_id}', 'App\Http\Controllers\Api\User\AdminMessageController@recallMessage')->middleware('get_user');


            //Danh sách tin nhắn chat
            Route::get('/person_chat/{vs_user_id}/messages', 'App\Http\Controllers\Api\User\UserMessageController@getAllMessage')->middleware('user_auth');
            //Chat cho user
            Route::post('/person_chat/{vs_user_id}/messages', 'App\Http\Controllers\Api\User\UserMessageController@sendMessage')->middleware('user_auth');
            //Thu hồi
            Route::get('/person_chat/{vs_user_id}/messages/recall/{mess_id}', 'App\Http\Controllers\Api\User\UserMessageController@recallMessage')->middleware('user_auth');

            //Tạo group
            Route::post('/groups', 'App\Http\Controllers\Api\User\GroupController@createGroup')->middleware('user_auth');
            //Rời nhóm
            Route::post('/groups/{group_id}/leave', 'App\Http\Controllers\Api\User\GroupController@leaveGroup')->middleware('user_auth');
            //remove nhóm
            Route::delete('/groups/{group_id}', 'App\Http\Controllers\Api\User\GroupController@removeGroup')->middleware('user_auth');
            //get 1 nhóm
            Route::get('/groups/{group_id}', 'App\Http\Controllers\Api\User\GroupController@getOne')->middleware('user_auth');
            //update 1 nhóm
            Route::put('/groups/{group_id}', 'App\Http\Controllers\Api\User\GroupController@update')->middleware('user_auth');
            //Thêm thành viên
            Route::post('/groups/{group_id}/add_member', 'App\Http\Controllers\Api\User\GroupController@addMember')->middleware('user_auth');
            //Đuổi thành viên
            Route::post('/groups/{group_id}/kick_member', 'App\Http\Controllers\Api\User\GroupController@kickMember')->middleware('user_auth');
            //onoff chat 
            Route::post('/groups/{group_id}/on_off_chat', 'App\Http\Controllers\Api\User\GroupController@onOffChatMember')->middleware('user_auth');



            //Thành viên bị ban
            Route::get('/groups/{group_id}/ban_members', 'App\Http\Controllers\Api\User\GroupBanMemberController@getAll')->middleware('user_auth');
            //Thêm thành viên ban
            Route::post('/groups/{group_id}/ban_members', 'App\Http\Controllers\Api\User\GroupBanMemberController@addMember')->middleware('user_auth');
            //Xóa thành viên ban
            Route::delete('/groups/{group_id}/ban_members', 'App\Http\Controllers\Api\User\GroupBanMemberController@removeMember')->middleware('user_auth');


            //Danh sách group chat với group
            Route::get('/group_chat', 'App\Http\Controllers\Api\User\GroupMessageController@getAllGroup')->middleware('user_auth');
            //Thu hồi
            Route::get('/group_chat/{to_group_id}/messages/recall/{mess_id}', 'App\Http\Controllers\Api\User\GroupMessageController@recallMessage')->middleware('user_auth');
            //Danh sách tin nhắn chat
            Route::get('/group_chat/{to_group_id}/messages', 'App\Http\Controllers\Api\User\GroupMessageController@getAllMessage')->middleware('user_auth');
            //Chat cho user
            Route::post('/group_chat/{to_group_id}/messages', 'App\Http\Controllers\Api\User\GroupMessageController@sendMessage')->middleware('user_auth');

            //tìm bạns
            Route::post('/search_user/one', 'App\Http\Controllers\Api\User\SearchUserController@searchOne')->middleware('user_auth');

            //Danh sách bạn bè
            Route::get('/friends', 'App\Http\Controllers\Api\User\FriendController@getAll')->middleware('user_auth');
            //Tìm Danh sách bạn bè
            Route::get('/search_users', 'App\Http\Controllers\Api\User\FriendController@searchAll')->middleware('user_auth');


            //Danh sách bạn bè của 1 user
            Route::get('/friends/all/{user_id}', 'App\Http\Controllers\Api\User\FriendController@getAllFriendOfUser')->middleware('user_auth');
            //Hủy kết bạn
            Route::delete('/friends/{user_id}', 'App\Http\Controllers\Api\User\FriendController@cancelFriend')->middleware('user_auth');
            //thêm bạn
            Route::post('/friends/{user_id}/add', 'App\Http\Controllers\Api\User\FriendController@addFriend')->middleware('user_auth');

            //device_token_user
            Route::prefix('device_token_user')->group(function () {
                Route::post('/', 'App\Http\Controllers\Api\User\UserDeviceTokenController@updateDeviceTokenUser')->middleware('user_auth');
            });

            //Danh sách phương thức thanh toán
            Route::get('/payment_methods', 'App\Http\Controllers\Api\User\Payment\PaymentController@paymentMethodList')->middleware('get_user');

            //Danh sách gói gia hạn
            Route::get('/plan_services', 'App\Http\Controllers\Api\User\PlanController@allPlans')->middleware('get_user', 'voucher_check');
            //Gia hạn thêm
            Route::get('/pay/renewal_plan', 'App\Http\Controllers\Api\User\PlanController@renewal_plan')->middleware('payment_check_user_service');
            //Danh sách lịch sử gia hạn
            Route::get('/renewal_histories', 'App\Http\Controllers\Api\User\RenewalHistoriesController@getAll')->middleware('user_auth');

            //Gửi mail reset
            Route::post('/send_email_reset', 'App\Http\Controllers\Api\User\SendEmailController@sendEmailReset');
            //Nhận mail reset
            Route::get('/email_reset', 'App\Http\Controllers\Api\User\SendEmailController@clickEmailLink');
            //Nhận mail reset
            Route::post('/email_reset', 'App\Http\Controllers\Api\User\SendEmailController@clickEmailLink');

            //thanh toán
            Route::post('/payment/google/create', 'App\Http\Controllers\Api\Payment\GooglePayController@create')->middleware('user_auth');

            //Đăng bài
            Route::post('/community_posts', 'App\Http\Controllers\Api\User\CommunityPostController@create')->middleware('user_auth');
            //Danh sách bài viết
            Route::get('/community_posts', 'App\Http\Controllers\Api\User\CommunityPostController@getAll')->middleware('user_auth');
            //Danh sách bài viết home
            Route::get('/community_posts/home', 'App\Http\Controllers\Api\User\CommunityPostController@getAllHome')->middleware('get_user');
            //Danh sách bài viết home
            Route::get('/community_posts/user/{user_id}', 'App\Http\Controllers\Api\User\CommunityPostController@getAllOfUserOther')->middleware('get_user');


            //Lên lại bài đăng
            Route::put('/community_posts/{community_post_id}/reup', 'App\Http\Controllers\Api\User\CommunityPostController@reup')->middleware('user_auth');
            //Sửa bài đăng
            Route::put('/community_posts/{community_post_id}', 'App\Http\Controllers\Api\User\CommunityPostController@update')->middleware('user_auth');
            //Tăng lượt share
            Route::post('/community_posts/{community_post_id}/add_share', 'App\Http\Controllers\Api\User\CommunityPostController@updateTotalShare');

            //1 bài đăng
            Route::get('/community_posts/{community_post_id}', 'App\Http\Controllers\Api\User\CommunityPostController@getOne')->middleware('user_auth');
            //Xóa bài đăng
            Route::delete('/community_posts/{community_post_id}', 'App\Http\Controllers\Api\User\CommunityPostController@delete')->middleware('user_auth');
            //Like
            Route::post('/community_post_like', 'App\Http\Controllers\Api\User\CommunityLikeController@create')->middleware('get_user');


            //Bình luận
            Route::post('/community_comments', 'App\Http\Controllers\Api\User\CommunityCommentController@create')->middleware('user_auth');
            //Danh sách Bình luận
            Route::get('/community_comments', 'App\Http\Controllers\Api\User\CommunityCommentController@getAll')->middleware('get_user');
            //Sửa Bình luận
            Route::put('/community_comments/{community_comment_id}', 'App\Http\Controllers\Api\User\CommunityCommentController@update')->middleware('user_auth');
            //Xóa Bình luận
            Route::delete('/community_comments/{community_comment_id}', 'App\Http\Controllers\Api\User\CommunityCommentController@delete')->middleware('user_auth');

            //Bình luận con
            Route::post('/community_comment_child1', 'App\Http\Controllers\Api\User\CommunityCommentChild1Controller@create')->middleware('user_auth');
            //Danh sách Bình luận con
            Route::get('/community_comment_child1', 'App\Http\Controllers\Api\User\CommunityCommentChild1Controller@getAll')->middleware('get_user');
            //Sửa Bình luận con
            Route::put('/community_comment_child1/{community_comment_id}', 'App\Http\Controllers\Api\User\CommunityCommentChild1Controller@update')->middleware('user_auth');
            //Xóa Bình luận con
            Route::delete('/community_comment_child1/{community_comment_id}', 'App\Http\Controllers\Api\User\CommunityCommentChild1Controller@delete')->middleware('user_auth');
            //Danh sách bạn bè
            Route::get('/community_user_profile/{user_id}', 'App\Http\Controllers\Api\User\CommunityProfileController@getInfoOverview')->middleware('get_user');


            //Danh sách bạn bè
            Route::get('/friends', 'App\Http\Controllers\Api\User\FriendController@getAll')->middleware('user_auth');
            //Hủy kết bạn
            Route::delete('/friends/{user_id}', 'App\Http\Controllers\Api\User\FriendController@cancelFriend')->middleware('user_auth');
            //danh sach cầu kết bạn
            Route::get('/friend_requests', 'App\Http\Controllers\Api\User\FriendController@getAllRequestFriend')->middleware('user_auth');
            //Gửi yêu cầu kết bạn
            Route::post('/friend_requests', 'App\Http\Controllers\Api\User\FriendController@requestFriend')->middleware('user_auth');
            //Huy yêu cầu kết bạn
            Route::delete('/friend_requests/{user_id}', 'App\Http\Controllers\Api\User\FriendController@deleteRequestFriend')->middleware('user_auth');

            //Xử lý yêu cầu kết bạn
            Route::post('/friend_requests/{request_id}/handle', 'App\Http\Controllers\Api\User\FriendController@handleRequest')->middleware('user_auth');

            //Danh sách Sản phẩm
            Route::get('/products', 'App\Http\Controllers\Api\User\ProductController@getAll');
            Route::get('/home_products', 'App\Http\Controllers\Api\User\ProductController@getAllHome');

            Route::get('/categories', 'App\Http\Controllers\Api\User\CategoryController@getAll');
            Route::get('/banner_ads', 'App\Http\Controllers\Api\User\BannerAdsController@getAll');

            Route::get('/agency_base/{agency_code}/theme_web', 'App\Http\Controllers\Api\User\WebThemeController@getThemeWeb')->middleware('has_agency_code');

            Route::post('/web_hook_data_buy_me_coffee', 'App\Http\Controllers\Api\User\WebHookController@saveWebHookBuymecoffee');
            Route::post('/web_hook_data_casso', 'App\Http\Controllers\Api\User\WebHookController@saveWebHookCasso');

            //Danh sách gói gia hạn thiết bị
            Route::get('/device_login/list_plan_devices', 'App\Http\Controllers\Api\User\DeviceLoginController@list_plan_devices')->middleware('get_user');


            //thiết bị đăng nhập
            Route::get('/device_login', 'App\Http\Controllers\Api\User\DeviceLoginController@getAll')->middleware('user_auth');
            //Kiểm tra bảo mật
            Route::get('/device_check_security', 'App\Http\Controllers\Api\User\DeviceLoginController@deviceCheckSecurity')->middleware('user_auth');
            //Kiểm tra bảo mật gia hạn hoặc thiết bị
            Route::get('/check_security', 'App\Http\Controllers\Api\User\DeviceLoginController@checkSecurity')->middleware('user_auth');
            //Xóa thiết bị
            Route::delete('/device_login/delete_device', 'App\Http\Controllers\Api\User\DeviceLoginController@deleteDevice')->middleware('user_auth');
            //Reset thiết bị
            Route::post('/device_login/reset_device', 'App\Http\Controllers\Api\User\DeviceLoginController@resetDevice')->middleware('user_auth');

            //Check voucher
            Route::post('/voucher/check_voucher', 'App\Http\Controllers\Api\User\VoucherController@checkVoucher')->middleware('user_auth');

            //Decoder
            Route::post('/decoder/vin_decode', 'App\Http\Controllers\Api\User\DecoderController@vinDecode')->middleware('user_auth');

            Route::get('/kdsgds/{carprovidertype}/gdsM2/vehicles', 'App\Http\Controllers\Api\User\KdsGds\VehicleController@getVehicle')->middleware('user_auth', 'check_security_kdsgds');
            Route::post('/kdsgds/{carprovidertype}/gdsM2/vehicles/searchVin', 'App\Http\Controllers\Api\User\KdsGds\VehicleController@searchVin')->middleware('user_auth', 'check_security_kdsgds');
            Route::get('/kdsgds/{carprovidertype}/gdsM2/manualV2Data', 'App\Http\Controllers\Api\User\KdsGds\ManualV2DataController@getManualV2Data')->middleware('user_auth', 'check_security_kdsgds');
            Route::post('/kdsgds/{carprovidertype}/gdsM2/getMenuDocument', 'App\Http\Controllers\Api\User\KdsGds\ManualV2DataController@getMenuDocument')->middleware('user_auth', 'check_security_kdsgds');
            Route::post('/kdsgds/{carprovidertype}/gdsM2/getMenuDocumentOfDTC', 'App\Http\Controllers\Api\User\KdsGds\ManualV2DataController@getMenuDocumentOfDTC')->middleware('user_auth', 'check_security_kdsgds');
            Route::post('/kdsgds/{carprovidertype}/gdsM2/getMenuDocumentOfShop', 'App\Http\Controllers\Api\User\KdsGds\ManualV2DataController@getMenuDocument')->middleware('user_auth', 'check_security_kdsgds');

            Route::get('/kdsgds/{carprovidertype}/gdsM2/getTabsDocument', 'App\Http\Controllers\Api\User\KdsGds\ManualV2DataController@getTabsDocument')->middleware('user_auth', 'check_security_kdsgds');
            Route::post('/kdsgds/{carprovidertype}/gdsM2/getTabsDocument', 'App\Http\Controllers\Api\User\KdsGds\ManualV2DataController@getTabsDocument')->middleware('user_auth', 'check_security_kdsgds');

            //lấy file từ path của kds
            Route::get('/kdsgds/{carprovidertype}/gdsM2/getFileFromPath', 'App\Http\Controllers\Api\User\KdsGds\DataFileController@getFileFromPath');
            ///lấy danh sách id file cửa 1 sơ đồ
            Route::post('/kdsgds/{carprovidertype}/gdsM2/getInfoFilePart', 'App\Http\Controllers\Api\User\KdsGds\DataFileController@getInfoFilePart')->middleware('user_auth', 'check_security_kdsgds');
            Route::post('/kdsgds/{carprovidertype}/gdsM2/getInfoFilePart', 'App\Http\Controllers\Api\User\KdsGds\DataFileController@getInfoFilePart')->middleware('user_auth', 'check_security_kdsgds');
        });


        Route::prefix('autodata')->group(function () {
            //Engine codes
            Route::post('/w1/manufacturers/{manufacturerId}/{modelId}/engines/codes', 'App\Http\Controllers\Api\AutoData\AutoDataController@engine_codes')->middleware('user_auth');

            //Menu
            Route::get('/w1/vehicles/{manufacturerId}/{modelId}', 'App\Http\Controllers\Api\AutoData\AutoDataController@menu_main')->middleware('user_auth');
            //Engines
            Route::get('/w1/manufacturers/{manufacturerId}/{modelId}/engines', 'App\Http\Controllers\Api\AutoData\AutoDataController@engines')->middleware('user_auth');

            //Mid end
            Route::post('/w1/vehicle-selection/mid/{vehicleId}', 'App\Http\Controllers\Api\AutoData\AutoDataController@mid_end')->middleware('user_auth');



            //Data web
            Route::get('/data_web_view/data', 'App\Http\Controllers\Api\AutoData\AutoDataController@data_web_view');
        });

        Route::prefix('haynespro')->group(function () {
            //Makes
            Route::get('/makes', 'App\Http\Controllers\Api\HaynesPro\HaynesProController@makes')->middleware('user_auth');
            Route::get('/makes/{makesId}/models', 'App\Http\Controllers\Api\HaynesPro\HaynesProController@models')->middleware('user_auth');
        });


        Route::prefix('tecdoc')->group(function () {
            Route::prefix('/menu')->group(function () {
                Route::get('/makes', [TecDocMenuController::class, 'getMakes']);
                Route::get('/models', [TecDocMenuController::class, 'getModels']);
                Route::get('/model-types', [TecDocMenuController::class, 'getModelTypes']);
                Route::get('/vehicle-information', [TecDocMenuController::class, 'getVehicleInformation']);
                Route::get('/product_categories', [TecDocMenuController::class, 'getProductCategories']);
                Route::get('/brands', [TecDocMenuController::class, 'getBrands']);
            });
            Route::prefix('/details')->group(function () {
                Route::get('/part-info', [TecDocInformationController::class, 'getPartInfo']);
                Route::get('/article-references', [TecDocInformationController::class, 'getTradeAndReplacements']);
                Route::get('/manufacter-address', [TecDocInformationController::class, 'getManufacterAddress']);
                Route::get('/file', [TecDocInformationController::class, 'getFileByArticleId']);
            });
            Route::prefix('/search')->group(function () {
                Route::get('/oem-number', [TecDocSearchController::class, 'searchByOEMNumber']);
                Route::get('/vin', [TecDocSearchController::class, 'getVehiclesByVin']);
            });
            Route::get('access-data/{slug?}', [TecDocFileController::class, 'getFile'])
                ->where('slug', '.*')
                ->name('access-file');
        });
    });
